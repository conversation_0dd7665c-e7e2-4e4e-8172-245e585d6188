/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  JPG_API_H
#define  JPG_API_H

#include "jpg_typedef.h"
typedef struct JPG_ENC_ARG_S{
	int fd;
	u16 dst_width;
	u16 dst_height;
	u8  img_Q;
	u8  timestamp;
	u8  *buf;
	u32 size;
}JPG_ENC_ARG;
typedef struct JPG_DEC_ARG_S{
	u8  type;	// JPG OR BMP ,but now only support jpg
	u8  wait;    // 0: do not wait decode end,1:wait decode end
	u8  fit;   // fit screen,or fit picture 
	u8  step; //HIGH RES TO LOW RES DECODE, use 2 step decode	
	int fd;
	u32 src_type;
	u16 src_width;
	u16 src_height;
	u16 dst_width;
	u16 dst_height;
	u16 step_dst_width;
	u16 step_dst_height;
	u8* jpgbuf;
	u32 jpgsize;
	u8  *yout;
	u8  *uvout;
	u8  *step_yout;
	u8  *step_uvout;
	lcdshow_frame_t *p_lcd_buffer;
}JPG_DEC_ARG;

/*******************************************************************************
* Function Name  : jpg_encode
* Description    : jpg encode write
* Input          : JPG_ENC_ARG *arg
* Output         : NONE
* Return         : 0: success, <0:fail
*******************************************************************************/
int jpg_encode(JPG_ENC_ARG *arg);


#endif
