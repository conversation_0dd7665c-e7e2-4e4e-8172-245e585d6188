/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_USB_H
    #define HX330X_USB_H
#include "hx330x_usb_sfr.h"
//----------------------------------------------------------------------------------------------------

#define USB_FULL_SPEED			0
#define USB_HIGH_SPEED			1

#define EP0_MAX_SIZE			64
#define BULK_MAX_SIZE_HS		512
#define BULK_MAX_SIZE_FS		64		
#define ISO_MAX_SIZE_HS			3000
#define ISO_MAX_SIZE_FS			1000		


#define USB_TIMEOUT 			80000

#define USB20_MAX_EPX			6
#define USB20_EP0				0
#define USB20_EP1     			1
#define USB20_EP2				2
#define USB20_EP3				3
#define USB20_EP4				4
#define USB20_EP5				5
#define USB20_EP6				6

#define USB11_MAX_EPX			3
#define USB11_EP0				0
#define USB11_EP1     			1
#define USB11_EP2				2
#define USB11_EP3				3



#define USB20_EP0_FIFO			64
#define USB20_EP1_FIFO    		4096
#define USB20_EP2_FIFO    		4096
#define USB20_EP3_FIFO    		4096
#define USB20_EP4_FIFO    		4096
#define USB20_EP5_FIFO    		4096
#define USB20_EP6_FIFO    		4096

#define USB11_EP0_FIFO			64
#define USB11_EP1_FIFO    		256
#define USB11_EP2_FIFO    		256
#define USB11_EP3_FIFO    		1024




//XSFR_USB20_SIE_POWER
#define USB_SUSPENDM_EN			(1<<0)
#define USB_SUSPEND_MODE		(1<<1)//in host send suspend signal to target or be a device to into suspend mode
#define USB_RESUME_MODE			(1<<2)//the same to suspend mode
#define USB_RESET_MODE			(1<<3)//read/write in host but read only in peripheral
#define USB_HS_MODE				(1<<4)
#define USB_HS_ENAB				(1<<5)//1:HIGH speed 0:full speed
#define USB_SOFT_CONN			(1<<6)
#define USB_ISO_UPDATE			(1<<7)

//XSFR_USB20_SIE_COM_INTFLG (read only)
#define USB_SUSPEND				(1<<0)
#define USB_RESUME				(1<<1)
#define USB_RESET				(1<<2)
#define USB_BABBLE				(1<<2)
#define USB_SOF					(0<<3)//(1<<3)
#define USB_CONN				(1<<4)//device connection is detected,only valid in host mode
#define USB_DISCON				(1<<5)
#define USB_SESS_REQ			(1<<6)
#define USB_VBUS_ERR			(1<<7)

//定义端点传输类型（同步/批量/中断）
#define TT_ILLEGAL              (0 << 4)
#define TT_ISOCHRONOUS          (1 << 4)
#define TT_BULK                 (2 << 4)
#define TT_INTERRUPT            (3 << 4)

////////////////////////////////////////////////////////////////////////////////
// XSFR_USB20_SIE_EP0_CTRL0 IN DEV
#define DUSB_EP0_RXPKTRDY      	(1<<0)
#define DUSB_EP0_TXPKTRDY       (1<<1)
#define DUSB_EP0_SENTSTALL     	(1<<2)
#define DUSB_EP0_DATAEND        (1<<3)
#define DUSB_EP0_SETUPEND       (1<<4)
#define DUSB_EP0_SENDSTALL      (1<<5)
#define DUSB_EP0_SVDOUTPKTRDY   (1<<6)
#define DUSB_EP0_SVDSETUPEND   	(1<<7)

// XSFR_USB20_SIE_EP0_CTRL0 IN HOST
#define HUSB_EP0_RXPKTRDY      	(1<<0)
#define HUSB_EP0_TXPKTRDY       (1<<1)
#define HUSB_EP0_RXSTALL     	(1<<2)
#define HUSB_EP0_SETUPPKT       (1<<3)
#define HUSB_EP0_ERROR       	(1<<4)
#define HUSB_EP0_REQPKT      	(1<<5)
#define HUSB_EP0_STATUSPKT   	(1<<6)
#define HUSB_EP0_NAKTOUT   		(1<<7)

////////////////////////////////////////////////////////////////////////////////
// XSFR_USB20_SIE_EPTX_CTRL0 IN DEV for EPX
#define DUSB_EPTX_TXPKTRDY      (1<<0)
#define DUSB_EPTX_FIFONOTEMPTY  (1<<1)
#define DUSB_EPTX_UNDERRUN     	(1<<2)
#define DUSB_EPTX_FLUSHFIFO     (1<<3)
#define DUSB_EPTX_SENDSTALL     (1<<4)
#define DUSB_EPTX_SENTSTALL     (1<<5)
#define DUSB_EPTX_CLRDATATOG    (1<<6)
#define DUSB_EPTX_INCOMPTX   	(1<<7)



// XSFR_USB20_SIE_EPTX_CTRL0 IN HOST for EPX
#define HUSB_EPTX_TXPKTRDY      (1<<0)
#define HUSB_EPTX_FIFONOTEMPTY  (1<<1)
#define HUSB_EPTX_TXERROR     	(1<<2)
#define HUSB_EPTX_FLUSHFIFO     (1<<3)
//#define HUSB_EPX_RXSTALL      (1<<4)
#define HUSB_EPTX_RXSTALL       (1<<5)
#define HUSB_EPTX_CLRDATATOG   	(1<<6)
#define HUSB_EPTX_NAKTOUT   	(1<<7)

// XSFR_USB20_SIE_EPTX_CTRL1 IN DEV/HOST for EPX
#define DUSB_EPTX_FRCDATATOG    (1<<3)
#define DUSB_EPTX_DMAEN     	(1<<4)
#define DUSB_EPTX_SETASTX     	(1<<5)
#define DUSB_EPTX_ISOMODE    	(1<<6)
#define DUSB_EPTX_AUTOSET   	(1<<7)
////////////////////////////////////////////////////////////////////////////////
// XSFR_USB20_SIE_EPRX_CTRL0 IN DEV for EPX
#define DUSB_EPRX_RXPKTRDY      (1<<0)
#define DUSB_EPRX_FIFOFULL      (1<<1)
#define DUSB_EPRX_OVERRUN     	(1<<2)
#define DUSB_EPRX_DATAERROR     (1<<3)
#define DUSB_EPRX_FLUSHFIFO     (1<<4)
#define DUSB_EPRX_SENDSTALL     (1<<5)
#define DUSB_EPRX_SENTSTALL     (1<<6)
#define DUSB_EPRX_CLRDATATOG   	(1<<7)

// XSFR_USB20_SIE_EPRX_CTRL0 IN HOST for EPX
#define HUSB_EPRX_RXPKTRDY      (1<<0)
#define HUSB_EPRX_FIFOFULL  	(1<<1)
#define HUSB_EPRX_ERROR     	(1<<2)
#define HUSB_EPRX_NAKTOUT     	(1<<3)
#define HUSB_EPRX_FLUSHFIFO     (1<<4)
#define HUSB_EPRX_REQPKT 	    (1<<5)
#define HUSB_EPRX_SENTSTALL   	(1<<6)
#define HUSB_EPRX_CLRDATATOG   	(1<<7)
// XSFR_USB20_SIE_EPRX_CTRL1 IN DEV/HOST for EPX
#define DUSB_EPRX_DMAMODE       (1<<4)
#define DUSB_EPRX_DMAEN     	(1<<5)
#define DUSB_EPRX_ISOAUTOREQ    (1<<6)
#define DUSB_EPRX_AUTOCLR   	(1<<7)

//Video Interface Subclass Codes
#define SC_VIDEOCONTROL				      	0x01
#define SC_VIDEOSTREAMING                 	0x02
#define SC_VIDEO_INTERFACE_COLLECTION     	0x03

#define SC_AUDIOCONTROL				      	0x01
#define SC_AUDIOSTREAMING                 	0x02
#define SC_AUDIO_INTERFACE_COLLECTION     	0x03
//Video Class-Specific Descriptor Types
#define CS_DEVICE				          	0x21
#define CS_CONFIGURATION                  	0x22
#define CS_STRING                         	0x23
#define CS_INTERFACE                      	0x24
#define CS_ENDPOINT                       	0x25
//Video Class-Specific VC Interface Descriptor Subtypes
#define VC_HEADER                         	0x01
#define VC_INPUT_TERMINAL                 	0x02
#define VC_OUTPUT_TERMINAL                	0x03
#define VC_SELECTOR_UNIT                  	0x04
#define VC_PROCESSING_UNIT                	0x05
#define VC_EXTENSION_UNIT                 	0x06
//Video Class-Specific VS Interface Descriptor Subtypes
#define VS_INPUT_HEADER                   	0x01
#define VS_OUTPUT_HEADER                  	0x02
#define VS_STILL_IMAGE_FRAME              	0x03
#define VS_FORMAT_UNCOMPRESSED            	0x04
#define VS_FRAME_UNCOMPRESSED             	0x05
#define VS_FORMAT_MJPEG                   	0x06
#define VS_FRAME_MJPEG                    	0x07
#define VS_FORMAT_MPEG2TS                 	0x0A
#define VS_FORMAT_DV                      	0x0C
#define VS_COLORFORMAT                    	0x0D
#define VS_FORMAT_FRAME_BASED             	0x10
#define VS_FRAME_FRAME_BASED              	0x11
#define VS_FORMAT_STREAM_BASED            	0x12
//Video Class-Specific Endpoint Descriptor Subtypes
#define EP_GENERAL                        	0x01
#define EP_ENDPOINT                       	0x02
#define EP_INTERRUPT                      	0x03
//Video Class-Specific Request Codes
#define SET_CUR                           	0x01
#define GET_CUR                           	0x81
#define SET_MIN							  	0x02
#define GET_MIN                           	0x82
#define SET_MAX							  	0x03
#define GET_MAX                           	0x83
#define SET_RES							  	0x04
#define GET_RES                           	0x84
#define SET_LEN							  	0x05
#define GET_LEN                           	0x85
#define SET_INFO						  	0x06
#define GET_INFO                          	0x86
#define SET_DEF							  	0x07
#define GET_DEF                           	0x87
//VideoControl Interface Control Selectors
#define VC_VIDEO_POWER_MODE_CONTROL       	0x01
#define VC_REQUEST_ERROR_CODE_CONTROL     	0x02
//Selector Unit Control Selectors	
#define SU_INPUT_SELECT_CONTROL           	0x01
//Camera Terminal Control Selectors	
#define CT_SCANNING_MODE_CONTROL          	0x01
#define CT_AE_MODE_CONTROL                	0x02
#define CT_AE_PRIORITY_CONTROL            	0x03
#define CT_EXPOSURE_TIME_ABSOLUTE_CONTROL 	0x04
#define CT_EXPOSURE_TIME_RELATIVE_CONTROL 	0x05
#define CT_FOCUS_ABSOLUTE_CONTROL         	0x06
#define CT_FOCUS_RELATIVE_CONTROL         	0x07
#define CT_FOCUS_AUTO_CONTROL             	0x08
#define CT_IRIS_ABSOLUTE_CONTROL          	0x09
#define CT_IRIS_RELATIVE_CONTROL          	0x0A
#define CT_ZOOM_ABSOLUTE_CONTROL          	0x0B
#define CT_ZOOM_RELATIVE_CONTROL          	0x0C
#define CT_PANTILT_ABSOLUTE_CONTROL       	0x0D
#define CT_PANTILT_RELATIVE_CONTROL       	0x0E
#define CT_ROLL_ABSOLUTE_CONTROL          	0x0F
#define CT_ROLL_RELATIVE_CONTROL          	0x10
#define CT_PRIVACY_CONTROL                	0x11
//Processing Unit Control Selectors
#define PU_CONTROL_UNDEFINED						0x00
#define PU_BACKLIGHT_COMPENSATION_CONTROL			0x01
#define PU_BRIGHTNESS_CONTROL						0x02
#define PU_CONTRAST_CONTROL							0x03
#define PU_GAIN_CONTROL								0x04
#define PU_POWER_LINE_FREQUENCY_CONTROL				0x05
#define PU_HUE_CONTROL								0x06
#define PU_SATURATION_CONTROL						0x07
#define PU_SHARPNESS_CONTROL						0x08
#define PU_GAMMA_CONTROL							0x09
#define PU_WHITE_BALANCE_TEMPERATURE_CONTROL		0x0a
#define PU_WHITE_BALANCE_TEMPERATURE_AUTO_CONTROL 	0x0b
#define PU_WHITE_BALANCE_COMPONENT_CONTROL			0x0c
#define PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL		0x0d
#define PU_DIGITAL_MULTIPLIER_CONTROL				0x0e
#define PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL			0x0f
#define PU_HUE_AUTO_CONTROL							0x10
#define PU_ANALOG_VIDEO_STANDARD_CONTROL			0x11
#define PU_ANALOG_LOCK_STATUS_CONTROL				0x12
#define PU_CONTRAST_AUTO_CONTROL					0x13
//VideoStreaming Interface Control Selectors
#define VS_PROBE_CONTROL                   0x01
#define VS_COMMIT_CONTROL                  0x02
#define VS_STILL_PROBE_CONTROL             0x03
#define VS_STILL_COMMIT_CONTROL            0x04
#define VS_STILL_IMAGE_TRIGGER_CONTROL     0x05
#define VS_STREAM_ERROR_CODE_CONTROL       0x06
#define VS_GENERATE_KEY_FRAME_CONTROL      0x07
#define VS_UPDATE_FRAME_SEGMENT_CONTROL    0x08
#define VS_SYNCH_DELAY_CONTROL             0x09

//USB Terminal Types
#define TT_VENDOR_SPECIFIC                 0x0100
#define TT_STREAMING                       0x0101
//Input Terminal Types
#define ITT_VENDOR_SPECIFIC                0x0200
#define ITT_CAMERA                         0x0201
#define ITT_MEDIA_TRANSPORT_INPUT          0x0202
//Output Terminal Types
#define OTT_VENDOR_SPECIFIC                0x0300
#define OTT_DISPLAY                        0x0301
#define OTT_MEDIA_TRANSPORT_OUTPUT         0x0302
//External Terminal Types
#define EXTERNAL_VENDOR_SPECIFIC           0x0400
#define COMPOSITE_CONNECTOR                0x0401
#define SVIDEO_CONNECTOR                   0x0402
#define COMPONENT_CONNECTOR                0x0403

//[AUDIO相关CODES]

#define AC_HEADER                          	0x01
#define AC_INPUT_TERMINAL                 	0x02
#define AC_OUTPUT_TERMINAL                	0x03
#define AC_SELECTOR_UNIT                  	0x04
#define AC_PROCESSING_UNIT                	0x05
#define AC_FEATURE_UNIT                 	0x06

#define AS_GENERAL							0x01
#define AS_FORMATTYPE						0x02
//USB Terminal Type
#define USB_UNDEFINED				       	0x0100
#define USB_STREAMING				       	0x0101
#define USB_VENDOR_SPECIFIC			       	0x01FF
//Audio Input Terminal Types	
#define UAC_INPUT_UNDEFINED                	0x0200
#define UAC_MICROPHONE                     	0x0201
#define UAC_DESKTOP_MICROPHONE             	0x0202
#define UAC_PERSONAL_MICROPHONE            	0x0203
#define UAC_OMNI_DIRECTIONAL_MICROPHONE    	0x0204
#define UAC_MICROPHONE_ARRAY               	0x0205
#define UAC_PROCESSING_MICROPHONE_ARRAY    	0x0206 
//Audio Output Terminal Type	
#define UAC_OUTPUT_UNDEFINED               	0x0300
#define UAC_SPEAKER                        	0x0301
#define UAC_HEADPHONES                     	0x0302
#define UAC_HEAD_MOUNTED_DISPLAY_AUDIO     	0x0303
#define UAC_DESKTOP_SPEAKER                	0x0304
#define UAC_ROOM_SPEAKER                   	0x0305
#define UAC_COMMUNICATION_SPEAKER          	0x0306
#define UAC_LOW_FREQUENCY_EFFECTS_SPEAKER  	0x0307
//Audio Data Format Type I Codes	
#define TYPE_I_UNDEFINED                   	0x0000
#define TYPE_I_PCM                         	0x0001
#define TYPE_I_PCM8                        	0x0002
#define TYPE_I_IEEE_FLOAT                  	0x0003
#define TYPE_I_ALAW                        	0x0004
#define TYPE_I_MULAW                       	0x0005
//Audio Data Format Type II Codes	
#define TYPE_II_UNDEFINED                  	0x1000
#define TYPE_II_MPEG                       	0x1001
#define TYPE_II_AC3                        	0x1002
//Audio Data Format Type III Codes	
#define TYPE_III_UNDEFINED                 	0x2000
#define TYPE_III_IEC1937_AC3               	0x2001
#define TYPE_III_IEC1937_MPEG1_Layer1      	0x2002
#define TYPE_III_IEC1937_MPEG1_Layer23     	0x2003
#define TYPE_III_IEC1937_MPEG2_NOEXT       	0x2003
#define TYPE_III_IEC1937_MPEG2_EXT         	0x2004
#define TYPE_III_IEC1937_MPEG2_Layer1_LS   	0x2005
#define TYPE_III_IEC1937_MPEG2_Layer23_LS  	0x2006
//Format Type Codes	
#define FORMAT_TYPE_UNDEFINED              	0x00
#define FORMAT_TYPE_I                      	0x01
#define FORMAT_TYPE_II                     	0x02
#define FORMAT_TYPE_III                    	0x03
//uac feature unit	
#define UAC_FU_CONTROL_UNDEFINED           	0x00
#define UAC_FU_MUTE_CONTROL                	0x01
#define UAC_FU_VOLUME_CONTROL              	0x02
#define UAC_FU_BASS_CONTROL                	0x03
#define UAC_FU_MID_CONTROL                 	0x04
#define UAC_FU_TREBLE_CONTROL              	0x05
#define UAC_FU_GRAPHIC_EQUALIZER_CONTROL   	0x06
#define UAC_FU_AUTOMATIC_GAIN_CONTROL      	0x07
#define UAC_FU_DELAY_CONTROL               	0x08
#define UAC_FU_BASS_BOOST_CONTROL          	0x09
#define UAC_FU_LOUDNESS_CONTROL            	0x0A

#define UAC_EP_CONTROL_UNDEFINED            0x00
#define UAC_EP_SAMPLING_FREQ_CONTROL        0x01
#define UAC_EP_PITCH_CONTROL                0x02


////////////////////////////////////////////////////////////////////////////////
/* 描述符类型定义 */
#define DEVICE_DESCRIPTOR               	1
#define CONFIGURATION_DESCRIPTOR          	2
#define STRING_DESCRIPTOR                 	3
#define INTERFACE_DESCRIPTOR              	4
#define ENDPOINT_DESCRIPTOR               	5
#define DEVICE_QUALIFIER_DESCRIPTOR       	6
#define OTHER_SPEED_CONFIG_DESCRIPTOR     	7

/* Setup Command */
#define SC_GET_STATUS                   	0x00
#define SC_CLEAR_FEATURE                	0x01
#define SC_SET_FEATURE                  	0x03
#define SC_SET_ADDRESS                  	0x05
#define SC_GET_DESCRIPTOR               	0x06
#define SC_GET_CONFIGURATION	        	0x08
#define SC_SET_CONFIGURATION	        	0x09
#define SC_GET_INTERFACE                	0x0A
#define SC_SET_INTERFACE                	0x0B
#define SC_SYNCH_FRAME     					0x0C

#define SC_BULK_ONLY_RESET              	0x21
#define SC_GET_MAX_LUN                  	0xA1

////////////////////////////////////////////////////////////////////////////////
//bmAttributes: D1...0
#define EP_TYPE_ISO                     	0x01
#define EP_TYPE_BULK                    	0x02
#define EP_TYPE_INTERRUPT               	0x03
//bmAttributes: D3...2
#define EP_SYNC_ASYNC                   	(0x01<<2)
#define EP_SYNC_ADAPTIVE                	(0x02<<2)
#define EP_SYNC_SYNC                    	(0x03<<2)
////////////////////////////////////////////////////////////////////////////////
#define CLASS_MASSSTORAGE               	0x08
#define SUBCLASS_SCSI                   	0x06
#define PROTOCOL_BULKONLY               	0x50

#define IAD_DESC 							0x0B
#define CC_VIDEO                         	0x0E
#define CC_AUDIO                         	0x01
#define USB_DEV_UVC_CLASS 	  	         	0xEF 
#define USB_DEV_HUB_CLASS		 			0x09

#define REQUEST_TO_DEVICE               	0x00
#define REQUEST_TO_INTERFACE            	0x01
#define REQUEST_TO_ENDPOINT             	0x02

//USB_REQUEST_FEATURE_SELECTOR
#define ENDPOINT_STALL                 	 	0x00
#define REMOTE_WAKEUP_S                 	0x01
#define TEST_MODE1                      	0x02
typedef enum
{
	USB_IRQ_HANDLER	= 0,  //USB中断
	USB_IRQ_RESET,		  
	USB_IRQ_EP0,
	USB_IRQ_EP1TX,
	USB_IRQ_EP2TX,
	USB_IRQ_EP3TX,
	USB_IRQ_EP4TX,
	USB_IRQ_EP5TX,
	USB_IRQ_EP6TX,	
	USB_IRQ_EP1RX,
	USB_IRQ_EP2RX,
	USB_IRQ_EP3RX,
	USB_IRQ_EP4RX,
	USB_IRQ_EP5RX,
	USB_IRQ_EP6RX,
	USB_IRQ_INSERT,
	USB_IRQ_REMOVE,
	USB_IRQ_SUSPEND,
	USB_IRQ_RESUME,
	USB_IRQ_MAX
}USB20_IRQ_E;
typedef enum
{
	USB11_IRQ_HANDLER	= 0,  //USB中断
	USB11_IRQ_RESET,		  
	USB11_IRQ_EP0,
	USB11_IRQ_EP1TX,
	USB11_IRQ_EP2TX,
	USB11_IRQ_EP3TX,
	USB11_IRQ_EP1RX,
	USB11_IRQ_EP2RX,
	USB11_IRQ_EP3RX,
	USB11_IRQ_INSERT,
	USB11_IRQ_REMOVE,
	USB11_IRQ_MAX
}USB11_IRQ_E;
/*******************************************************************************
* Function Name  : hx330x_usbCallbackRegister
* Description    : USB CALLBACK REGISTER
* Input          : u8 type : USB_IRQ_E
                      void (*callback)(void) : callback
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb20_CallbackRegister(u8 type,void (*callback)(void));
/*******************************************************************************
* Function Name  : hx330x_usb20_Func_Call
* Description    : USB CALLBACK func call
* Input          : u8 type : USB_IRQ_E  
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb20_Func_Call(u8 type);
/*******************************************************************************
* Function Name  : hx330x_usb20_eptx_register
* Description    : hx330x_usb20_eptx_register
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb20_eptx_register(u8 en, u8 epx,void (*callback)(void));
/*******************************************************************************
* Function Name  : hx330x_usb20_eptx_register
* Description    : hx330x_usb20_eptx_register
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb20_eprx_register(u8 en, u8 epx,void (*callback)(void));
/*******************************************************************************
* Function Name  : hx330x_usb11_CallbackRegister
* Description    : USB CALLBACK REGISTER
* Input          : u8 type : USB_IRQ_E
                      void (*callback)(void) : callback
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb11_CallbackRegister(u8 type,void (*callback)(void));
/*******************************************************************************
* Function Name  : hx330x_usb11_Func_Call
* Description    : USB CALLBACK func call
* Input          : u8 type : USB_IRQ_E  
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb11_Func_Call(u8 type);
/*******************************************************************************
* Function Name  : hx330x_usb20_CallbackInit
* Description    : hx330x_usb20_CallbackInit
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb20_CallbackInit(void);
/*******************************************************************************
* Function Name  : hx330x_usb11_CallbackInit
* Description    : hx330x_usb11_CallbackInit
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb11_CallbackInit(void);
/*******************************************************************************
* Function Name  : hx330x_USB20_EPTX_Flush
* Description    : usb20 eptx flush fifo
* Input          : 
* Output         : None
* Return         : true: success
*******************************************************************************/
void hx330x_USB20_EPTX_Flush(u32 index);
/*******************************************************************************
* Function Name  : hx330x_usb20_speed
* Description    : hx330x_usb20_speed
* Input          : 
* Output         : None
* Return         : 
*******************************************************************************/
bool hx330x_usb20_HighSpeed(void);
/*******************************************************************************
* Function Name  : hx330x_bulk20_tx
* Description    : 统一不用USB20_DRAMEN
* Input          : 
* Output         : None
* Return         : true: success
*******************************************************************************/
bool hx330x_bulk20_tx(u32 epx, u32 adr, u32 len);
/*******************************************************************************
* Function Name  : hx330x_bulk20_rx
* Description    : 统一不用USB20_DRAMEN
* Input          : 
* Output         : None
* Return         : true: success
*******************************************************************************/
bool hx330x_bulk20_rx(u32 epx, u32 adr, u32 len);
/*******************************************************************************
* Function Name  : hx330x_iso20_tx
* Description    : 统一不用USB20_DRAMEN,操作的adr需要自己做好cache的flush和write back
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
bool hx330x_iso20_tx(u8 epx, u32 adr, u16 len);
/*******************************************************************************
* Function Name  : hx330x_iso20_tx
* Description    : 统一不用USB20_DRAMEN,操作的adr需要自己做好cache的flush和write back
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iso20_tx_kick(u8 epx, u32 adr, u16 len);
/*******************************************************************************
* Function Name  : 
* Description    : 
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_usb20_dev_reset(void);
/*******************************************************************************
* Function Name  : 
* Description    : 
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_usb20_host_speed_connect(void);
/*******************************************************************************
* Function Name  : hx330x_usb20_host_reset
* Description    : 
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_usb20_host_reset(void);
/*******************************************************************************
* Function Name  : hx330x_usb20_dev_init
* Description    : 
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/	
void hx330x_usb20_dev_init(void);
/*******************************************************************************
* Function Name  : hx330x_usb20_host_init
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/	
void hx330x_usb20_host_init(void);
/*******************************************************************************
* Function Name  : hx330x_usb20_uinit
* Description    : 
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/	
void hx330x_usb20_uinit(void);
/*******************************************************************************
* Function Name  : get_u16softcnt
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/	
u16 get_u16softcnt(void);
/*******************************************************************************
* Function Name  : hx330x_usb20DevIRQHanlder
* Description    : USB DEV irq handler
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb20DevIRQHanlder(void);
/*******************************************************************************
* Function Name  : hx330x_usb20_hostIRQHanlder
* Description    : USB20 HOST irq handler
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb20_hostIRQHanlder(void);
/*******************************************************************************
* Function Name  : hx330x_usb11_host_init
* Description    : hx330x_usb11_host_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb11_host_init(void);
/*******************************************************************************
* Function Name  : hx330x_usb11_host_eprx
* Description    : hx330x_usb11_host_eprx
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb11_host_eprx_register(u8 en, u8 epx,void (*callback)(void));
/*******************************************************************************
* Function Name  : 
* Description    : 
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_usb11_host_speed_connect(void);
/*******************************************************************************
* Function Name  : hx330x_usb11_uinit
* Description    : 
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/	
void hx330x_usb11_uinit(void);
/*******************************************************************************
* Function Name  : hx330x_usb11_hostIRQHanlder
* Description    : hx330x_usb11_hostIRQHanlder
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb11_hostIRQHanlder(void);
/*******************************************************************************
* Function Name  : hx330x_usb11_host_reset
* Description    : hx330x_usb11_host_reset
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_usb11_host_reset(void);
/*******************************************************************************
* Function Name  : hx330x_usb20_dev_check_init
* Description    :
* Input          : none
* Output         : None
* Return         :
*******************************************************************************/
void hx330x_usb20_dev_check_init(void);
/*******************************************************************************
* Function Name  : hx330x_usb20_host_check_init
* Description    :
* Input          : none
* Output         : None
* Return         :
*******************************************************************************/
void hx330x_usb20_host_check_init(void);
/*******************************************************************************
* Function Name  : hx330x_usb11_host_check_init
* Description    :
* Input          : none
* Output         : None
* Return         :
*******************************************************************************/
void hx330x_usb11_host_check_init(void);
/*******************************************************************************
* Function Name  : hx330x_usb20_device_check
* Description    : check if usb20 dev 
* Input          : none
* Output         : None
* Return         : true: check ok, false: check fail
*******************************************************************************/
bool hx330x_usb20_device_check(void);
/*******************************************************************************
* Function Name  : hx330x_usb20_host_check
* Description    : check if usb20 host 
* Input          : none
* Output         : None
* Return         : true: check ok, false: check fail
*******************************************************************************/
bool hx330x_usb20_host_check(void);
/*******************************************************************************
* Function Name  : hx330x_usb11_host_check
* Description    : check if usb11 host 
* Input          : none
* Output         : None
* Return         : true: check ok, false: check fail
*******************************************************************************/
bool hx330x_usb11_host_check(void);





#endif /* USB_H_ */
