
#ifndef USB_DEVICE_VIDEO_H_
#define USB_DEVICE_VIDEO_H_


/*******************************************************************************
* Function Name  : uvc_epx_cfg
* Description    : uvc_epx_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_epx_cfg(void);

/*******************************************************************************
* Function Name  : uac_stop
* Description    : uac_stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool unitsel_set(u8* rxbuf);
/*******************************************************************************
* Function Name  : uvc_unit_ctl_hal
* Description    : uvc_unit_ctl_hal
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uvc_unit_ctl_hal(u8 val, u8 rqu, u8 len);

/*******************************************************************************
* Function Name  : uvc_probe_ctl_hal
* Description    : uvc_probe_ctl_hal
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uvc_probe_ctl_hal(u8 val, u8 rqu, u8 len);

/*******************************************************************************
* Function Name  : uvc_start
* Description    : uvc_start
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_start(void);

/*******************************************************************************
* Function Name  : uvc_stop
* Description    : uvc_stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_stop(void);
/*******************************************************************************
* Function Name  : uvc_is_start
* Description    : uvc_is_start
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u8 uvc_is_start(void);
/*******************************************************************************
* Function Name  : uvc_pic_sanp
* Description    : uvc_pic_sanp
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_pic_sanp(void);

/*******************************************************************************
* Function Name  : uvc_header_fill
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_header_fill(u8 *buf, u8 flg);

/*******************************************************************************
* Function Name  : uvc_process
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_process(void);

/*******************************************************************************
* Function Name  : uvc_process
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_isr_process(void);

#endif
