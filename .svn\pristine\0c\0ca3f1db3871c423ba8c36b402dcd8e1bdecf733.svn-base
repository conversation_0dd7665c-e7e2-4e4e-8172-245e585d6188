/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef TOUCHPANEL_API_H
#define TOUCHPANEL_API_H

#include "touchpanel_iic.h"
typedef enum{
    TP_TYPE_NONE = 0,
    TP_TYPE_PRESS,
    TP_TYPE_MOVE,
    TP_TYPE_ERROR,
}TP_TYPE_S;

typedef enum{
    TP_DIR_NONE = 0,
    TP_DIR_UP,
    TP_DIR_DOWN,
    TP_DIR_LEFT,
    TP_DIR_RIGHT,
}TP_DIR_S;

typedef struct TP_POINT_S
{
	int x;
	int y;
}TP_POINT_T;
typedef struct TOUCHPANEL_OP_S
{
	char name[12];
    u16  tp_width;
    u16  tp_height;
	void (*init)(void);
	int  (*match)(void);
	int  (*getPoint)(TP_POINT_T*);
}TOUCHPANEL_OP_T;
typedef struct TOUCHPANEL_INFO_S
{
    u16  tp_type;
    u16  tp_dir;
    u32  tp_speed;
  	int  x;
	int  y;  
}TOUCHPANEL_INFO_T;
typedef struct TOUCHPANEL_API_S
{
	TOUCHPANEL_OP_T   *tp_op;
    TOUCHPANEL_INFO_T  tp_info;
    u16  lcdui_sx, lcdui_ex, lcdui_sy, lcdui_ey;
    u16  lcdSreen_w, lcdSreen_h;
    
}TOUCHPANEL_API_T;
extern const TOUCHPANEL_OP_T tp_icnt81;
extern const TOUCHPANEL_OP_T tp_ns2009;
/*******************************************************************************
* Function Name  : dev_touchpanel_Init
* Description    : initial touchpanel
* Input          : none
* Output         : none                                            
* Return         : wake up state
*******************************************************************************/
int dev_touchpanel_Init(void);
/*******************************************************************************
* Function Name  : dev_touchpanel_ioctrl
* Description    : dev_touchpanel_ioctrl
* Input          : none
* Output         : none                                            
* Return         : wake up state
*******************************************************************************/
int dev_touchpanel_ioctrl(INT32U op,INT32U para);
#endif
