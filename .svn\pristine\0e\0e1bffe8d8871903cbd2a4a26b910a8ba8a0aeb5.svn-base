Archive member included to satisfy reference by file (symbol)

..\lib\libboot.a(boot.o)      obj\Debug\dev\battery\src\battery_api.o (boot_vddrtcCalculate)
..\lib\libboot.a(boot_loader.o)
                              obj\Debug\mcu\boot\spi_boot_cfg.o (.bootsect)
..\lib\libboot.a(reset.o)     obj\Debug\mcu\boot\spi_boot_cfg.o (_start)
..\lib\libboot.a(boot_lib.o)  ..\lib\libboot.a(boot_loader.o) (boot_sdram_init)
..\lib\libmcu.a(hx330x_adc.o)
                              obj\Debug\hal\src\hal_adc.o (hx330x_adcEnable)
..\lib\libmcu.a(hx330x_auadc.o)
                              obj\Debug\hal\src\hal_auadc.o (hx330x_auadcHalfIRQRegister)
..\lib\libmcu.a(hx330x_csi.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_csiInit)
..\lib\libmcu.a(hx330x_dac.o)
                              obj\Debug\hal\src\hal_dac.o (hx330x_dacTypeCfg)
..\lib\libmcu.a(hx330x_dma.o)
                              obj\Debug\app\app_common\src\app_lcdshow.o (hx330x_dmaNocWinA)
..\lib\libmcu.a(hx330x_dmauart.o)
                              obj\Debug\hal\src\hal_dmauart.o (hx330x_DmaUart_con_cfg)
..\lib\libmcu.a(hx330x_gpio.o)
                              ..\lib\libmcu.a(hx330x_csi.o) (hx330x_gpioSFRSet)
..\lib\libmcu.a(hx330x_iic.o)
                              obj\Debug\hal\src\hal_iic.o (hx330x_iic0Init)
..\lib\libmcu.a(hx330x_int.o)
                              ..\lib\libboot.a(reset.o) (fast_isr)
..\lib\libmcu.a(hx330x_isp.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_isp_mask_tab_cfg)
..\lib\libmcu.a(hx330x_isp_tab.o)
                              ..\lib\libmcu.a(hx330x_isp.o) (GAOS3X3_TAB)
..\lib\libmcu.a(hx330x_jpg.o)
                              obj\Debug\hal\src\hal_lcdshow.o (hx330x_mjpA_EncodeISRRegister)
..\lib\libmcu.a(hx330x_jpg_tab.o)
                              ..\lib\libmcu.a(hx330x_jpg.o) (hx330x_mjpA_table_init)
..\lib\libmcu.a(hx330x_lcd.o)
                              obj\Debug\dev\lcd\src\lcd_api.o (hx330x_lcdReset)
..\lib\libmcu.a(hx330x_lcdrotate.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_rotateIRQHandler)
..\lib\libmcu.a(hx330x_lcdui.o)
                              obj\Debug\dev\usb\dusb\src\dusb_msc.o (hx330x_lcdShowWaitDone)
..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_uiLzoIRQHandler)
..\lib\libmcu.a(hx330x_lcdwin.o)
                              obj\Debug\hal\src\hal_lcdshow.o (hx330x_lcdWinABConfig)
..\lib\libmcu.a(hx330x_md.o)  obj\Debug\hal\src\hal_md.o (hx330x_mdEnable)
..\lib\libmcu.a(hx330x_mipi.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_MipiCSIUinit)
..\lib\libmcu.a(hx330x_misc.o)
                              obj\Debug\dev\touchpanel\src\touchpanel_api.o (hx330x_abs)
..\lib\libmcu.a(hx330x_rtc.o)
                              obj\Debug\hal\src\hal_rtc.o (hx330x_rtcRamRead)
..\lib\libmcu.a(hx330x_sd.o)  obj\Debug\dev\sd\src\sd_api.o (hx330x_sd0Init)
..\lib\libmcu.a(hx330x_spi0.o)
                              obj\Debug\hal\src\hal_spi.o (hx330x_spi0ManualInit)
..\lib\libmcu.a(hx330x_spi1.o)
                              obj\Debug\hal\src\hal_spi1.o (hx330x_spi1_CS_Config)
..\lib\libmcu.a(hx330x_sys.o)
                              obj\Debug\dev\gsensor\src\gsensor_da380.o (hx330x_sysCpuMsDelay)
..\lib\libmcu.a(hx330x_timer.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_timer0IRQHandler)
..\lib\libmcu.a(hx330x_tminf.o)
                              obj\Debug\hal\src\hal_watermark.o (hx330x_mjpA_TimeinfoEnable)
..\lib\libmcu.a(hx330x_uart.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_uart0IRQHandler)
..\lib\libmcu.a(hx330x_usb.o)
                              obj\Debug\dev\usb\dusb\src\dusb_api.o (hx330x_usb20_CallbackRegister)
..\lib\libmcu.a(hx330x_wdt.o)
                              obj\Debug\dev\usb\dusb\src\dusb_msc.o (hx330x_wdtEnable)
..\lib\libmcu.a(hx330x_emi.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_emiIRQHandler)
..\lib\libisp.a(hal_isp.o)    obj\Debug\dev\sensor\src\sensor_api.o (hal_sensor_fps_adpt)
..\lib\libjpg.a(hal_jpg.o)    obj\Debug\hal\src\hal_mjpAEncode.o (hal_mjp_enle_init)
..\lib\liblcd.a(hal_lcd.o)    obj\Debug\hal\src\hal_lcdshow.o (lcd_show_ctrl)
..\lib\liblcd.a(hal_lcdMem.o)
                              ..\lib\liblcd.a(hal_lcd.o) (hal_lcdAddrCalculate)
..\lib\liblcd.a(hal_lcdrotate.o)
                              ..\lib\liblcd.a(hal_lcd.o) (hal_rotateInit)
..\lib\liblcd.a(hal_lcdUi.o)  obj\Debug\app\app_common\src\app_lcdshow.o (hal_uiDrawBufMalloc)
..\lib\liblcd.a(hal_lcdUiLzo.o)
                              ..\lib\liblcd.a(hal_lcdUi.o) (hal_uiLzokick)
..\lib\liblcd.a(lcd_tab.o)    obj\Debug\dev\lcd\src\lcd_api.o (hal_lcdParaLoad)
..\lib\libmultimedia.a(api_multimedia.o)
                              obj\Debug\multimedia\audio\audio_playback.o (api_multimedia_init)
..\lib\libmultimedia.a(avi_dec.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_dec_func)
..\lib\libmultimedia.a(avi_odml_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_odml_enc_func)
..\lib\libmultimedia.a(avi_std_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_std_enc_func)
..\lib\libmultimedia.a(wav_dec.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (wav_dec_func)
..\lib\libmultimedia.a(wav_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (wav_enc_func)
..\lib\libmultimedia.a(wav_pcm.o)
                              ..\lib\libmultimedia.a(wav_enc.o) (pcm_encode)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                              obj\Debug\app\user_config\src\mbedtls_md5.o (memcmp)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                              obj\Debug\dev\sd\src\sd_api.o (memcpy)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                              obj\Debug\dev\fs\src\fs_api.o (memset)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                              obj\Debug\dev\gsensor\src\gsensor_api.o (strcpy)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                              obj\Debug\dev\fs\src\ff.o (__udivdi3)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                              obj\Debug\dev\fs\src\ff.o (__umoddi3)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
                              D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__udivsi3)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
                              D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__umodsi3)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                              D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__clz_tab)

Allocating common symbols
Common symbol       size              file

WAV_TYPE_E          0x4               obj\Debug\dev\battery\src\battery_api.o
SysCtrl             0x130             obj\Debug\app\app_common\src\app_init.o
gsensor_ctl         0x8               obj\Debug\dev\gsensor\src\gsensor_api.o
RGB_GMMA_Tab        0x300             ..\lib\libmcu.a(hx330x_isp.o)
ui_draw_ctrl        0x10              obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
usb_dev_ctl         0x29c             obj\Debug\dev\usb\dusb\src\dusb_api.o
rc128k_div          0x4               ..\lib\libmcu.a(hx330x_rtc.o)
husb_ctl            0x1484            obj\Debug\dev\usb\husb\src\husb_api.o
rtcSecondISR        0x4               ..\lib\libmcu.a(hx330x_rtc.o)
task_play_audio_stat
                    0x4               obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
lcdshow_frame_op    0x1e8             ..\lib\liblcd.a(hal_lcdMem.o)
sd_update_op        0x64              obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
mediaVideoCtl       0x74              obj\Debug\multimedia\video\video_record.o
Y_GMA_Tab           0x200             ..\lib\libmcu.a(hx330x_isp.o)
rtcAlamISR          0x4               ..\lib\libmcu.a(hx330x_rtc.o)
usensor_handle      0x4               obj\Debug\dev\usb\husb\src\husb_usensor.o
USB_CH              0x4               obj\Debug\dev\battery\src\battery_api.o
UVC_CACHE_STA       0x4               obj\Debug\dev\battery\src\battery_api.o
fs_exfunc           0x14              obj\Debug\dev\fs\src\fs_api.o
SDCON0_T            0x4               obj\Debug\dev\battery\src\battery_api.o
usbDeviceOp         0xc               obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
XOSNesting          0x4               obj\Debug\mcu\xos\xos.o
smph_dmacopy        0x4               ..\lib\libmcu.a(hx330x_sys.o)
SDCON1_T            0x4               obj\Debug\dev\battery\src\battery_api.o
tp_api_t            0x20              obj\Debug\dev\touchpanel\src\touchpanel_api.o
hx330x_lcdISR       0x14              ..\lib\libmcu.a(hx330x_lcd.o)
dev_key_tab         0x78              obj\Debug\dev\key\src\key_api.o
hx330x_timerISR     0x10              ..\lib\libmcu.a(hx330x_timer.o)
uhub_handle         0x4               obj\Debug\dev\usb\husb\src\husb_hub.o
UVC_FSTACK_STA      0x4               obj\Debug\dev\battery\src\battery_api.o
rc128k_rtc_cnt      0x4               ..\lib\libmcu.a(hx330x_rtc.o)
lcd_show_ctrl       0x84              ..\lib\liblcd.a(hal_lcd.o)
recordPhotoOp       0x20              obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
rc128k_timer_cnt    0x4               ..\lib\libmcu.a(hx330x_rtc.o)
playVideoOp         0x44              obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
CHANNEL_EXCHANGE_E  0x4               obj\Debug\dev\battery\src\battery_api.o

Memory Configuration

Name             Origin             Length             Attributes
boot             0x01fffc00         0x00000200
ram_boot         0x00000000         0x00006c00
ram_user         0x00000000         0x00007000
usb_ram          0x00008000         0x00008000
line_ram         0x00200000         0x00010000
mp3_text         0x00008000         0x00008000
mp3_ram          0x00200000         0x00010000
nes_text         0x00008000         0x00008000
nes_ram          0x00200000         0x00010000
sdram            0x02000000         0x00800000
flash            0x06000000         0x00800000
exsdram          0x00000000         0x00800000
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD obj\Debug\dev\battery\src\battery_api.o
LOAD obj\Debug\dev\dev_api.o
LOAD obj\Debug\dev\fs\src\diskio.o
LOAD obj\Debug\dev\fs\src\ff.o
LOAD obj\Debug\dev\fs\src\ffunicode.o
LOAD obj\Debug\dev\fs\src\fs_api.o
LOAD obj\Debug\dev\gsensor\src\gsensor_api.o
LOAD obj\Debug\dev\gsensor\src\gsensor_da380.o
LOAD obj\Debug\dev\gsensor\src\gsensor_gma301.o
LOAD obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
LOAD obj\Debug\dev\ir\src\ir_api.o
LOAD obj\Debug\dev\key\src\key_api.o
LOAD obj\Debug\dev\lcd\src\lcd_api.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
LOAD obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
LOAD obj\Debug\dev\led\src\led_api.o
LOAD obj\Debug\dev\led_pwm\src\led_pwm_api.o
LOAD obj\Debug\dev\nvfs\src\nvfs_api.o
LOAD obj\Debug\dev\nvfs\src\nvfs_jpg.o
LOAD obj\Debug\dev\sd\src\sd_api.o
LOAD obj\Debug\dev\sensor\src\sensor_api.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
LOAD obj\Debug\dev\sensor\src\sensor_tab.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_api.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_iic.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_api.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_enum.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_msc.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_uac.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_uvc.o
LOAD obj\Debug\dev\usb\husb\src\husb_api.o
LOAD obj\Debug\dev\usb\husb\src\husb_enum.o
LOAD obj\Debug\dev\usb\husb\src\husb_hub.o
LOAD obj\Debug\dev\usb\husb\src\husb_tpbulk.o
LOAD obj\Debug\dev\usb\husb\src\husb_usensor.o
LOAD obj\Debug\dev\usb\husb\src\husb_uvc.o
LOAD obj\Debug\hal\src\hal_adc.o
LOAD obj\Debug\hal\src\hal_auadc.o
LOAD obj\Debug\hal\src\hal_csi.o
LOAD obj\Debug\hal\src\hal_dac.o
LOAD obj\Debug\hal\src\hal_dmauart.o
LOAD obj\Debug\hal\src\hal_eeprom.o
LOAD obj\Debug\hal\src\hal_gpio.o
LOAD obj\Debug\hal\src\hal_iic.o
LOAD obj\Debug\hal\src\hal_int.o
LOAD obj\Debug\hal\src\hal_lcdshow.o
LOAD obj\Debug\hal\src\hal_md.o
LOAD obj\Debug\hal\src\hal_mjpAEncode.o
LOAD obj\Debug\hal\src\hal_mjpBEncode.o
LOAD obj\Debug\hal\src\hal_mjpDecode.o
LOAD obj\Debug\hal\src\hal_rtc.o
LOAD obj\Debug\hal\src\hal_spi.o
LOAD obj\Debug\hal\src\hal_spi1.o
LOAD obj\Debug\hal\src\hal_stream.o
LOAD obj\Debug\hal\src\hal_sys.o
LOAD obj\Debug\hal\src\hal_timer.o
LOAD obj\Debug\hal\src\hal_uart.o
LOAD obj\Debug\hal\src\hal_watermark.o
LOAD obj\Debug\hal\src\hal_wdt.o
LOAD obj\Debug\mcu\boot\spi_boot_cfg.o
LOAD obj\Debug\mcu\xos\xmbox.o
LOAD obj\Debug\mcu\xos\xmsgq.o
LOAD obj\Debug\mcu\xos\xos.o
LOAD obj\Debug\mcu\xos\xwork.o
LOAD obj\Debug\multimedia\audio\audio_playback.o
LOAD obj\Debug\multimedia\audio\audio_record.o
LOAD obj\Debug\multimedia\image\image_decode.o
LOAD obj\Debug\multimedia\image\image_encode.o
LOAD obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
LOAD obj\Debug\multimedia\video\video_playback.o
LOAD obj\Debug\multimedia\video\video_record.o
LOAD obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
LOAD obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
LOAD obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
LOAD obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
LOAD obj\Debug\sys_manage\res_manage\res_manage_api.o
LOAD obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
LOAD obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
LOAD obj\Debug\app\app_common\src\app_init.o
LOAD obj\Debug\app\app_common\src\app_lcdshow.o
LOAD obj\Debug\app\app_common\src\main.o
LOAD obj\Debug\app\resource\user_res.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
LOAD obj\Debug\app\task_windows\msg_api.o
LOAD obj\Debug\app\task_windows\task_api.o
LOAD obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
LOAD obj\Debug\app\task_windows\task_common\src\task_common.o
LOAD obj\Debug\app\task_windows\task_common\src\task_common_msg.o
LOAD obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
LOAD obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
LOAD obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
LOAD obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
LOAD obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
LOAD obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
LOAD obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
LOAD obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
LOAD obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
LOAD obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
LOAD obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
LOAD obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
LOAD obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
LOAD obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
LOAD obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
LOAD obj\Debug\app\task_windows\windows_api.o
LOAD obj\Debug\app\user_config\src\mbedtls_md5.o
LOAD obj\Debug\app\user_config\src\user_config_api.o
LOAD obj\Debug\app\user_config\src\user_config_tab.o
                0x00200000                __sdram_size = (boot_sdram_size == 0x1)?0x800000:0x200000

.bootsec        0x01fffc00      0x200 load address 0x00000000
 *(.bootsec)
 .bootsec       0x01fffc00      0x200 ..\lib\libboot.a(boot_loader.o)
                0x01fffc10                hex
                0x01fffc10                .hex
                0x01fffd1c                .bootsect

.boot_code      0x00000000     0x29a8 load address 0x00000200
                0x00000000                _boot_vma = .
 *(.vector)
 .vector        0x00000000      0x380 ..\lib\libboot.a(reset.o)
                0x000002a0                _start
                0x000002ec                _step_in
                0x00000330                _step_out
 *(.vector.kepttext)
 .vector.kepttext
                0x00000380       0x6c obj\Debug\dev\dev_api.o
                0x00000380                exception_lowpower_io_cfg
 .vector.kepttext
                0x000003ec      0x44c ..\lib\libboot.a(boot.o)
                0x000004d8                boot_vddrtcCalculate
                0x00000504                boot_putchar
                0x000005b0                boot_uart_puts
                0x00000604                exception
                0x0000081c                exception_trigger
 .vector.kepttext
                0x00000838       0x20 ..\lib\libboot.a(boot_lib.o)
                0x00000838                boot_getChipSN
 .vector.kepttext
                0x00000858      0x210 ..\lib\libmcu.a(hx330x_gpio.o)
                0x00000858                exception_gpioDataSet
                0x000008e4                hx330x_gpioDataGet
                0x00000968                hx330x_gpioCommonDataGet
                0x000009b0                exception_io1d1_softstart_clr
 .vector.kepttext
                0x00000a68      0x304 ..\lib\libmcu.a(hx330x_rtc.o)
                0x00000a68                hx330x_rtcWriteByte
                0x00000ab8                hx330x_rtcReadByte
                0x00000b1c                hx330x_rtcDataRead
                0x00000ba4                hx330x_rtcDataWrite
                0x00000c2c                hx330x_WKI1WakeupEnable
 .vector.kepttext
                0x00000d6c      0x1f0 ..\lib\libmcu.a(hx330x_sys.o)
                0x00000d6c                hx330x_sysCpuMsDelay
                0x00000dc0                hx330x_sysCpuNopDelay
                0x00000dfc                hx330x_bytes_memcpy
                0x00000e6c                hx330x_bytes_memset
                0x00000ec4                hx330x_bytes_cmp
 .vector.kepttext
                0x00000f5c       0x74 ..\lib\libmcu.a(hx330x_uart.o)
                0x00000f5c                hx330x_uart0SendByte
 .vector.kepttext
                0x00000fd0       0x84 ..\lib\libmcu.a(hx330x_wdt.o)
                0x00000fd0                hx330x_wdtEnable
                0x00001004                hx330x_wdtClear
                0x00001038                hx330x_wdtReset
 *(.vector.keptdata)
 .vector.keptdata
                0x00001054      0x148 obj\Debug\app\user_config\src\user_config_tab.o
                0x00001054                hardware_setup
 .vector.keptdata
                0x0000119c       0x3c ..\lib\libboot.a(boot.o)
                0x0000119c                vbg_param
 .vector.keptdata
                0x000011d8      0x100 ..\lib\libboot.a(reset.o)
                0x000011d8                _step_data
 .vector.keptdata
                0x000012d8        0x4 ..\lib\libboot.a(boot_lib.o)
 .vector.keptdata
                0x000012dc        0x4 ..\lib\libmcu.a(hx330x_gpio.o)
 .vector.keptdata
                0x000012e0        0x4 ..\lib\libmcu.a(hx330x_spi0.o)
                0x000012e0                spi_auto_mode
                0x000012e4                . = ALIGN (0x4)
                0x000012e4                _boot_kept_vma = .
 *(.vector.text)
 .vector.text   0x000012e4      0x8f4 ..\lib\libboot.a(boot.o)
                0x00001554                boot_vbg_pre_init
                0x000015ec                boot_vbg_back_init
                0x00001634                exception_init
                0x0000166c                bool_pll_init
                0x00001ae4                boot_clktun_check
                0x00001b70                boot_clktun_save
 .vector.text   0x00001bd8      0xbbc ..\lib\libboot.a(boot_lib.o)
                0x00001dc0                boot_sdram_init
 *(.vector.data)
 .vector.data   0x00002794        0x4 ..\lib\libboot.a(boot.o)
                0x00002794                vbg_adc
 .vector.data   0x00002798      0x210 ..\lib\libboot.a(boot_lib.o)
                0x00002798                tune_values
                0x00002918                tune_by
                0x00002924                tune_tab_2_clk
                0x00002944                tune_tab_1_clk
                0x00002964                tuning_test_addr
                0x00002984                tuning_test_data
                0x000029a4                SDRTUN2_CON

.ram            0x00000000     0x36d0
                0x000012e4                . = _boot_kept_vma
 *fill*         0x00000000     0x12e4 
                0x000012e4                __sram_start = .
 *(.sram_usb11fifo)
 .sram_usb11fifo
                0x000012e4      0x96c obj\Debug\hal\src\hal_sys.o
                0x000012e4                usb11_fifo
 *(.sram_comm)
 .sram_comm     0x00001c50      0xd60 obj\Debug\dev\fs\src\fs_api.o
 .sram_comm     0x000029b0      0x200 obj\Debug\dev\sd\src\sd_api.o
                0x000029b0                sd0RamBuffer
 .sram_comm     0x00002bb0       0x40 obj\Debug\hal\src\hal_dmauart.o
                0x00002bb0                dmauart_fifo
 .sram_comm     0x00002bf0      0x400 obj\Debug\hal\src\hal_mjpDecode.o
 .sram_comm     0x00002ff0       0x60 obj\Debug\mcu\xos\xmsgq.o
 .sram_comm     0x00003050      0x400 ..\lib\libmcu.a(hx330x_jpg.o)
                0x00003050                jpg_dri_tab
 .sram_comm     0x00003450      0x280 ..\lib\libisp.a(hal_isp.o)
                0x000036d0                __sram_end = .

.usb_ram        0x00008000     0x3630
                0x00008000                __ufifo_start = .
 *(.uram_usb20fifo)
 .uram_usb20fifo
                0x00008000     0x1d30 obj\Debug\hal\src\hal_sys.o
                0x00008000                usb20_fifo
 *(.uram_comm)
 .uram_comm     0x00009d30     0x1900 obj\Debug\hal\src\hal_watermark.o
                0x00009d30                tminf_font
                0x0000b630                __ufifo_end = .

.line_ram       0x00200000        0x0
                0x00200000                __line_start = .
 *(.lram_comm)
                0x00200000                __line_end = .

.on_sdram       0x02000000     0x9b9c load address 0x00002c00
                0x02000000                _onsdram_start = .
 *(.sdram_text)
 .sdram_text    0x02000000       0x3c obj\Debug\dev\fs\src\ff.o
                0x02000000                clst2sect
 .sdram_text    0x0200003c       0x58 obj\Debug\dev\fs\src\fs_api.o
                0x0200003c                fs_getClustStartSector
 .sdram_text    0x02000094      0x174 obj\Debug\dev\nvfs\src\nvfs_jpg.o
                0x02000094                nv_jpg_write_by_linkmap
 .sdram_text    0x02000208      0x4f8 obj\Debug\dev\sd\src\sd_api.o
                0x020002a8                sd_api_Stop
                0x02000538                sd_api_Exist
                0x02000560                sd_api_Write
                0x02000640                sd_api_Read
 .sdram_text    0x02000700       0x3c obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .sdram_text    0x0200073c      0x260 obj\Debug\dev\usb\husb\src\husb_hub.o
 .sdram_text    0x0200099c     0x110c obj\Debug\dev\usb\husb\src\husb_uvc.o
                0x02001890                huvc_cache_dcd_down
                0x0200194c                husb_uvc_frame_read
 .sdram_text    0x02001aa8       0x3c obj\Debug\hal\src\hal_adc.o
                0x02001aa8                hal_adcGetChannel
 .sdram_text    0x02001ae4       0x48 obj\Debug\hal\src\hal_dac.o
 .sdram_text    0x02001b2c      0x104 obj\Debug\hal\src\hal_gpio.o
                0x02001b2c                hal_gpioInit
                0x02001bd4                hal_gpioEPullSet
 .sdram_text    0x02001c30      0x12c obj\Debug\hal\src\hal_lcdshow.o
                0x02001c30                hal_lcd_enc_kick
 .sdram_text    0x02001d5c      0x9f8 obj\Debug\hal\src\hal_spi.o
                0x02001d8c                hal_spiUpdata_led_show
                0x02001e44                hal_spiUpdata_led_show_init
                0x02001ec4                hal_spiUpdata_led_show_uinit
                0x02001f1c                hal_spiManualInit
                0x02001f48                hal_spiAutoModeInit
                0x02001f78                hal_spiModeSwitch
                0x02001ff4                hal_spiFlashReadID
                0x02002064                hal_spiFlashWriteEnable
                0x02002098                hal_spiFlashWait
                0x02002128                hal_spiFlashReadPage
                0x02002180                hal_spiFlashRead
                0x0200220c                hal_spiFlashWritePage
                0x020022a0                hal_spiFlashWrite
                0x02002380                hal_spiFlashWriteInManual
                0x02002414                hal_spiFlashEraseSector
                0x020024b8                hal_spiFlashEraseBlock
                0x02002538                hal_spiFlashEraseChip
                0x02002574                hal_spiFlashReadUniqueID
                0x020025fc                hal_spiFlashReadOTP
                0x020026a8                hal_spiFlashReadManual
 .sdram_text    0x02002754      0x460 obj\Debug\hal\src\hal_stream.o
                0x02002754                hal_streamMalloc
                0x0200286c                hal_streamIn
                0x0200294c                hal_streamOut
                0x02002a48                hal_streamOutNext
                0x02002b10                hal_streamfree
 .sdram_text    0x02002bb4       0x24 obj\Debug\hal\src\hal_timer.o
                0x02002bb4                hal_timerPWMStop
 .sdram_text    0x02002bd8      0x158 obj\Debug\hal\src\hal_uart.o
                0x02002bd8                uart_Printf
 .sdram_text    0x02002d30      0xc34 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02002d30                taskSdGetClst
                0x02002da4                taskSdReadBuf
                0x02002ee0                taskSdUpdateProcess
 .sdram_text    0x02003964      0x330 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                0x02003964                taskSdUpdateDrawStartAddrCal
                0x02003a3c                taskSdUpdateDrawAddrReCal
                0x02003ba8                taskSdUpdate_uiProgress
 .sdram_text    0x02003c94      0x11c ..\lib\libmcu.a(hx330x_adc.o)
                0x02003c94                hx330x_adcEnable
                0x02003cf0                hx330x_adcSetBaudrate
                0x02003d24                hx330x_adcConverStart
                0x02003d48                hx330x_adcRead
 .sdram_text    0x02003db0       0x90 ..\lib\libmcu.a(hx330x_auadc.o)
                0x02003db0                hx330x_auadcIRQHandler
 .sdram_text    0x02003e40      0x170 ..\lib\libmcu.a(hx330x_csi.o)
                0x02003e40                hx330x_csiIRQHandler
 .sdram_text    0x02003fb0      0x1e8 ..\lib\libmcu.a(hx330x_dac.o)
                0x02003fb0                hx330x_dacIRQHandler
                0x02004084                hx330x_dacBufferSet
                0x020040a4                hx330x_dacBufferFlush
                0x020040c4                hx330x_dacStart
                0x02004140                hx330x_check_dacstop
 .sdram_text    0x02004198       0xa4 ..\lib\libmcu.a(hx330x_dmauart.o)
                0x02004198                hx330x_uart1IRQHandler
 .sdram_text    0x0200423c      0x584 ..\lib\libmcu.a(hx330x_gpio.o)
                0x0200423c                hx330x_gpioDirSet
                0x020042e0                hx330x_gpioPullSet
                0x020043a0                hx330x_gpioPinPollSet
                0x020043ec                hx330x_gpioDrvSet
                0x02004478                hx330x_gpioDataSet
                0x02004504                hx330x_gpioPinDataSet
                0x02004550                hx330x_gpioMapSet
                0x020045dc                hx330x_gpioDigitalSet
                0x02004668                hx330x_gpioLedPull
                0x02004728                hx330x_gpioIRQHandler
 .sdram_text    0x020047c0      0x230 ..\lib\libmcu.a(hx330x_int.o)
                0x020047c0                hx330x_intHandler
                0x020047d4                fast_isr
                0x020048ac                slow_isr
                0x020049a8                hx330x_intEnable
 .sdram_text    0x020049f0      0x1a4 ..\lib\libmcu.a(hx330x_jpg.o)
                0x020049f0                hx330x_mjpA_EncodeLoadAddrGet
                0x02004a0c                hx330x_mjpA_EncodeStartAddrGet
                0x02004a28                hx330x_mjpA_EncodeDriTabGet
                0x02004a44                hx330x_mjpA_IRQHandler
                0x02004a90                hx330x_mjpB_IRQHandler
 .sdram_text    0x02004b94      0x17c ..\lib\libmcu.a(hx330x_lcd.o)
                0x02004b94                hx330x_lcdKick
                0x02004bb8                hx330x_lcdIRQHandler
 .sdram_text    0x02004d10       0x50 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                0x02004d10                hx330x_rotateIRQHandler
 .sdram_text    0x02004d60      0x470 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x02004d60                hx330x_lcdShowWaitDone
                0x02004dac                hx330x_lcdShowKick
                0x02004dd0                hx330x_lcdShowIRQHandler
                0x02004e30                hx330x_lcdVideoSetScanMode
                0x02004ec4                hx330x_lcdVideoSetAddr
                0x02004ee0                hx330x_lcdVideoSetStride
                0x02004f04                hx330x_lcdvideoSetPosition
                0x02004f28                hx330x_lcdUiEnable
                0x02004f7c                hx330x_lcdUiSetAddr
                0x02005030                hx330x_lcdVideoUpScaler_cfg
                0x02005128                hx330x_lcdVideoUpScalerSoftRotate_cfg
 .sdram_text    0x020051d0       0x4c ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                0x020051d0                hx330x_uiLzoIRQHandler
 .sdram_text    0x0200521c       0x60 ..\lib\libmcu.a(hx330x_misc.o)
                0x0200521c                hx330x_min
                0x02005240                hx330x_data_check
 .sdram_text    0x0200527c      0x224 ..\lib\libmcu.a(hx330x_rtc.o)
                0x0200527c                hx330x_rtcIRQHandler
                0x02005348                hx330x_WKOEnable
                0x020053c4                hx330x_WKI1WakeupTriger
                0x02005414                hx330x_WKI0WakeupTriger
                0x02005464                hx330x_WakeUpCleanPending
 .sdram_text    0x020054a0      0x554 ..\lib\libmcu.a(hx330x_sd.o)
                0x020054a0                hx330x_sd0SendCmd
                0x0200556c                hx330x_sd0Recv
                0x020055c8                hx330x_sd0Send
                0x02005600                hx330x_sd0WaitDAT0
                0x020056a0                hx330x_sd0WaitPend
                0x02005728                hx330x_sd0GetRsp
                0x02005744                hx330x_sd0CRCCheck
                0x020057a8                hx330x_sd1SendCmd
                0x02005874                hx330x_sd1Recv
                0x020058d0                hx330x_sd1Send
                0x02005908                hx330x_sd1WaitPend
                0x02005990                hx330x_sd1CRCCheck
 .sdram_text    0x020059f4      0x678 ..\lib\libmcu.a(hx330x_spi0.o)
                0x020059f4                hx330x_spi0PinConfig
                0x02005a74                hx330x_spi0ManualInit
                0x02005ba0                hx330x_spi0SendByte
                0x02005bf4                hx330x_spi0RecvByte
                0x02005c48                hx330x_spi0Send
                0x02005d00                hx330x_spi0Recv
                0x02005dc4                hx330x_spi0CS0Config
                0x02005e08                hx330x_spi0AutoModeInit
                0x02005ff4                hx330x_spi0ExitAutoMode
 .sdram_text    0x0200606c       0x64 ..\lib\libmcu.a(hx330x_spi1.o)
                0x0200606c                hx330x_spi1DMAIRQHandler
 .sdram_text    0x020060d0      0x63c ..\lib\libmcu.a(hx330x_sys.o)
                0x020060d0                table_init_sfr
                0x02006150                hx330x_sysDcacheWback
                0x020061ec                hx330x_sysDcacheFlush
                0x02006288                hx330x_sysClkSet
                0x020062e4                hx330x_mcpy0_sdram2gram
                0x020063c0                hx330x_mcpy0_sdram2gram_nocache
                0x02006468                hx330x_mcpy1_sdram2gram_nocache_waitdone
                0x020064dc                hx330x_mcpy1_sdram2gram_nocache_kick
                0x0200658c                hx330x_mcpy1_sdram2gram
                0x02006658                hx330x_mcpy1_sdram2gram_nocache
 .sdram_text    0x0200670c      0x1fc ..\lib\libmcu.a(hx330x_timer.o)
                0x0200670c                hx330x_timer0IRQHandler
                0x02006750                hx330x_timer1IRQHandler
                0x02006794                hx330x_timer2IRQHandler
                0x020067d8                hx330x_timer3IRQHandler
                0x0200681c                hx330x_timerTickStart
                0x02006850                hx330x_timerTickStop
                0x0200686c                hx330x_timerTickCount
                0x02006888                hx330x_timerPWMStop
 .sdram_text    0x02006908       0x64 ..\lib\libmcu.a(hx330x_uart.o)
                0x02006908                hx330x_uart0IRQHandler
 .sdram_text    0x0200696c      0x76c ..\lib\libmcu.a(hx330x_usb.o)
                0x020069f4                hx330x_usb20_Func_Call
                0x02006a20                hx330x_bulk20_tx
                0x02006be4                hx330x_bulk20_rx
                0x02006d64                hx330x_usb20DevIRQHanlder
                0x02006e9c                hx330x_usb20_hostIRQHanlder
                0x02006fa4                hx330x_usb11_Func_Call
                0x02006fd0                hx330x_usb11_hostIRQHanlder
 .sdram_text    0x020070d8       0x5c ..\lib\libmcu.a(hx330x_emi.o)
                0x020070d8                hx330x_emiIRQHandler
 .sdram_text    0x02007134       0x68 ..\lib\libjpg.a(hal_jpg.o)
                0x02007134                hal_mjp_enle_tab_get
 .sdram_text    0x0200719c      0x358 ..\lib\liblcd.a(hal_lcd.o)
 .sdram_text    0x020074f4      0x228 ..\lib\liblcd.a(hal_lcdMem.o)
                0x020074f4                hal_lcdAddrCalculate
                0x020076b8                hal_dispframeFree
 .sdram_text    0x0200771c      0x18c ..\lib\liblcd.a(hal_lcdUi.o)
                0x0200771c                hal_uiBuffFree
                0x02007744                hal_lcdUiEnable
                0x0200779c                hal_lcdUiBuffFlush
 *(.sdram_code)
 .sdram_code    0x020078a8      0x110 ..\lib\libmcu.a(hx330x_spi0.o)
                0x020078a8                spi0PinCfg_tab
                0x020078b8                SPI0_4_LINE_tab
                0x020078f8                SPI0_2_LINE1_tab
                0x02007938                SPI0_2_LINE0_tab
                0x02007978                SPI0_1_LINE_tab
 *(.sdram_data)
 *(.data*)
 .data          0x020079b8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .data          0x020079b8      0x120 obj\Debug\dev\dev_api.o
                0x020079b8                dev_node
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\diskio.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\ff.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\ffunicode.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\fs_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .data          0x02007ad8        0x0 obj\Debug\dev\ir\src\ir_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\key\src\key_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .data          0x02007ad8        0x0 obj\Debug\dev\led\src\led_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\nvfs\src\nvfs_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sd\src\sd_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .data          0x02007ad8        0x4 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .data          0x02007adc        0x0 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .data          0x02007adc      0x208 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x02007adc                dusb_com_cfgdsc
                0x02007cc4                dusb_msc_cfgdsc
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_api.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_enum.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_hub.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_adc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_auadc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_csi.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_dac.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_dmauart.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_eeprom.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_gpio.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_iic.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_int.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_lcdshow.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_md.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpAEncode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpBEncode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpDecode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_rtc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_spi.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_spi1.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_stream.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_sys.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_timer.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_uart.o
 .data          0x02007ce4       0x48 obj\Debug\hal\src\hal_watermark.o
 .data          0x02007d2c        0x0 obj\Debug\hal\src\hal_wdt.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xmbox.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xmsgq.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xos.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xwork.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\audio\audio_playback.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\audio\audio_record.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\image\image_decode.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\image\image_encode.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\video\video_playback.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\video\video_record.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .data          0x02007d2c        0x0 obj\Debug\app\app_common\src\app_init.o
 .data          0x02007d2c        0x8 obj\Debug\app\app_common\src\app_lcdshow.o
 .data          0x02007d34        0x0 obj\Debug\app\app_common\src\main.o
 .data          0x02007d34      0xb7c obj\Debug\app\resource\user_res.o
                0x02007d34                User_Icon_Table
                0x02008154                User_String_Table
 .data          0x020088b0        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .data          0x020088b0       0x48 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
                0x020088b0                menuplayBack
                0x020088bc                menuPageplayBack
                0x020088d0                menuItemplayBack
 .data          0x020088f8        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .data          0x020088f8      0x2a0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                0x020088f8                menurecord
                0x02008904                menuPagerecord
                0x02008918                menuItemrecord
                0x020089b8                menuOptionversion
                0x020089c0                menuOptionscreenSave
                0x020089e0                menuOptionirLed
                0x020089f8                menuOptionfrequency
                0x02008a08                menuOptionlanguage
                0x02008a78                menuOptionautoPowerOff
                0x02008a90                menuOptionkeySound
                0x02008aa0                menuOptiongsensor
                0x02008ac0                menuOptiontimeStamp
                0x02008ad0                menuOptionparking
                0x02008ae0                menuOptionaudio
                0x02008af0                menuOptionmd
                0x02008b00                menuOptionev
                0x02008b28                menuOptionawb
                0x02008b50                menuOptionloopRecord
                0x02008b70                menuOptionphotoResolution
                0x02008b88                menuOptionvideoResolution
 .data          0x02008b98       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                0x02008b98                dateTimeWindow
                0x02008ba8                dateTimeMsgDeal
 .data          0x02008c08       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                0x02008c08                defaultWindow
                0x02008c18                defaultMsgDeal
 .data          0x02008c70       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                0x02008c70                delAllWindow
                0x02008c80                delAllMsgDeal
 .data          0x02008cd8       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                0x02008cd8                delCurWindow
                0x02008ce8                delCurMsgDeal
 .data          0x02008d40       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                0x02008d40                formatWindow
                0x02008d50                formatMsgDeal
 .data          0x02008da8       0x98 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x02008db0                menuItemWindow
                0x02008dc0                menuItemMsgDeal
 .data          0x02008e40       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                0x02008e40                lockCurWindow
                0x02008e50                lockCurMsgDeal
 .data          0x02008ea8       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                0x02008ea8                menuOptionWindow
                0x02008eb8                menuOptionMsgDeal
 .data          0x02008f18       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                0x02008f18                unlockAllWindow
                0x02008f28                unlockAllMsgDeal
 .data          0x02008f80       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                0x02008f80                unlockCurWindow
                0x02008f90                unlockCurMsgDeal
 .data          0x02008fe8        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .data          0x02008fe8       0x38 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                0x02008fe8                asternWindow
                0x02008ff8                asternMsgDeal
 .data          0x02009020       0x68 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                0x02009020                noFileWindow
                0x02009030                noFileMsgDeal
 .data          0x02009088       0x98 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                0x02009088                selfTestWindow
                0x02009098                selfTestMsgDeal
 .data          0x02009120       0xcc obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                0x02009120                tips1Window
                0x02009130                tips1MsgDeal
 .data          0x020091ec       0xcc obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                0x020091ec                tipsWindow
                0x020091fc                tipsMsgDeal
 .data          0x020092b8       0x5c obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                0x020092b8                takephotoiconWindow
                0x020092c8                TpIconMsgDeal
 .data          0x02009314        0x0 obj\Debug\app\task_windows\msg_api.o
 .data          0x02009314        0x0 obj\Debug\app\task_windows\task_api.o
 .data          0x02009314        0x0 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .data          0x02009314       0x4c obj\Debug\app\task_windows\task_common\src\task_common.o
 .data          0x02009360        0x0 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .data          0x02009360       0x14 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x02009360                taskPlayAudio
 .data          0x02009374       0x80 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                0x02009374                playAudioWindow
                0x02009384                playAudioMsgDeal
 .data          0x020093f4       0x14 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x020093f4                taskPlayVideo
 .data          0x02009408       0xf8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                0x02009430                playVideoMainWindow
                0x02009440                playVideoMainMsgDeal
 .data          0x02009500       0x9c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                0x0200950c                playVideoSlideWindow
                0x0200951c                playVideoSlideMsgDeal
 .data          0x0200959c       0x88 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                0x0200959c                playVideoThumbnallWindow
                0x020095ac                playVideoThumbnallMsgDeal
 .data          0x02009624       0x14 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
                0x02009624                taskPowerOff
 .data          0x02009638       0x14 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
                0x02009638                taskRecordAudio
 .data          0x0200964c       0x50 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                0x0200964c                RecordAudioWindow
                0x0200965c                recordAudioMsgDeal
 .data          0x0200969c       0x14 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x0200969c                taskRecordPhoto
 .data          0x020096b0      0x270 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                0x020096b0                recordPhotoWindow
                0x020096c0                photoEncodeMsgDeal
                0x02009790                recordPhotoWin
 .data          0x02009920       0x14 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                0x02009920                taskRecordVideo
 .data          0x02009934       0xe8 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                0x02009934                recordVideoWindow
                0x02009944                recordVideoMsgDeal
 .data          0x02009a1c       0x14 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02009a1c                taskSDUpdate
 .data          0x02009a30        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .data          0x02009a30       0x14 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
                0x02009a30                taskShowLogo
 .data          0x02009a44       0x30 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                0x02009a44                ShowLogoWindow
                0x02009a54                ShowLogoMsgDeal
 .data          0x02009a74       0x14 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                0x02009a74                taskUSBDevice
 .data          0x02009a88       0xb8 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                0x02009a88                usbDeviceWindow
                0x02009a98                usbDeviceMsgDeal
 .data          0x02009b40        0x0 obj\Debug\app\task_windows\windows_api.o
 .data          0x02009b40        0x8 obj\Debug\app\user_config\src\mbedtls_md5.o
                0x02009b40                MY_KEY
 .data          0x02009b48        0x0 obj\Debug\app\user_config\src\user_config_api.o
 .data          0x02009b48        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .data          0x02009b48        0x0 ..\lib\libboot.a(boot.o)
 .data          0x02009b48        0x0 ..\lib\libboot.a(boot_loader.o)
 .data          0x02009b48        0x0 ..\lib\libboot.a(reset.o)
 .data          0x02009b48        0x0 ..\lib\libboot.a(boot_lib.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_auadc.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_csi.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_dac.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_dma.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_dmauart.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_gpio.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_iic.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_int.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_isp.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_jpg.o)
 .data          0x02009b48        0x1 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 *fill*         0x02009b49        0x3 
 .data          0x02009b4c        0x4 ..\lib\libmcu.a(hx330x_lcd.o)
 .data          0x02009b50        0x0 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .data          0x02009b50        0x8 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x02009b50                video_scanmode_tab
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_md.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_mipi.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_misc.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_rtc.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_sd.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_spi1.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_sys.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_timer.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_tminf.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_uart.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_usb.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_wdt.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_emi.o)
 .data          0x02009b58        0x4 ..\lib\libisp.a(hal_isp.o)
 .data          0x02009b5c        0x0 ..\lib\libjpg.a(hal_jpg.o)
 .data          0x02009b5c       0x40 ..\lib\liblcd.a(hal_lcd.o)
 .data          0x02009b9c        0x0 ..\lib\liblcd.a(hal_lcdMem.o)
 .data          0x02009b9c        0x0 ..\lib\liblcd.a(hal_lcdrotate.o)
 .data          0x02009b9c        0x0 ..\lib\liblcd.a(hal_lcdUi.o)
 .data          0x02009b9c        0x0 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .data          0x02009b9c        0x0 ..\lib\liblcd.a(lcd_tab.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(api_multimedia.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(avi_dec.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(avi_std_enc.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(wav_dec.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(wav_enc.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(wav_pcm.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.bss            0x02009b9c    0x12b2c load address 0x0000c79c
                0x02009b9c                __bss_start = .
 *(.bss*)
 .bss           0x02009b9c        0x8 obj\Debug\dev\battery\src\battery_api.o
 .bss           0x02009ba4        0x8 obj\Debug\dev\dev_api.o
 .bss           0x02009bac        0x0 obj\Debug\dev\fs\src\diskio.o
 .bss           0x02009bac      0x468 obj\Debug\dev\fs\src\ff.o
 .bss           0x0200a014        0x0 obj\Debug\dev\fs\src\ffunicode.o
 .bss           0x0200a014        0x0 obj\Debug\dev\fs\src\fs_api.o
 .bss           0x0200a014        0x4 obj\Debug\dev\gsensor\src\gsensor_api.o
 .bss           0x0200a018        0x0 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .bss           0x0200a018        0x0 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .bss           0x0200a018        0x0 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .bss           0x0200a018        0x4 obj\Debug\dev\ir\src\ir_api.o
 .bss           0x0200a01c       0x1c obj\Debug\dev\key\src\key_api.o
 .bss           0x0200a038       0xbc obj\Debug\dev\lcd\src\lcd_api.o
                0x0200a038                lcd_saj_nocolor
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .bss           0x0200a0f4        0x4 obj\Debug\dev\led\src\led_api.o
 .bss           0x0200a0f8        0x4 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .bss           0x0200a0fc       0x18 obj\Debug\dev\nvfs\src\nvfs_api.o
 .bss           0x0200a114       0x10 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .bss           0x0200a124       0x2c obj\Debug\dev\sd\src\sd_api.o
                0x0200a124                hal_sdc_speed
 .bss           0x0200a150     0x1218 obj\Debug\dev\sensor\src\sensor_api.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .bss           0x0200b368        0x8 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .bss           0x0200b370        0x0 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .bss           0x0200b370        0x4 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .bss           0x0200b374       0x1c obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .bss           0x0200b390        0x4 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .bss           0x0200b394        0x0 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .bss           0x0200b394        0x0 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .bss           0x0200b394        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .bss           0x0200b394        0x0 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .bss           0x0200b394        0x2 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 *fill*         0x0200b396        0x2 
 .bss           0x0200b398        0xc obj\Debug\dev\usb\husb\src\husb_api.o
 .bss           0x0200b3a4        0x0 obj\Debug\dev\usb\husb\src\husb_enum.o
 .bss           0x0200b3a4        0x0 obj\Debug\dev\usb\husb\src\husb_hub.o
 .bss           0x0200b3a4        0x0 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .bss           0x0200b3a4        0x4 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .bss           0x0200b3a8        0x0 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .bss           0x0200b3a8        0x0 obj\Debug\hal\src\hal_adc.o
 .bss           0x0200b3a8       0xc8 obj\Debug\hal\src\hal_auadc.o
                0x0200b3a8                auadccnt
 .bss           0x0200b470        0x4 obj\Debug\hal\src\hal_csi.o
 .bss           0x0200b474        0x8 obj\Debug\hal\src\hal_dac.o
 .bss           0x0200b47c       0x50 obj\Debug\hal\src\hal_dmauart.o
 .bss           0x0200b4cc        0x0 obj\Debug\hal\src\hal_eeprom.o
 .bss           0x0200b4cc        0x0 obj\Debug\hal\src\hal_gpio.o
 .bss           0x0200b4cc        0x1 obj\Debug\hal\src\hal_iic.o
 .bss           0x0200b4cd        0x0 obj\Debug\hal\src\hal_int.o
 *fill*         0x0200b4cd        0x3 
 .bss           0x0200b4d0        0x8 obj\Debug\hal\src\hal_lcdshow.o
 .bss           0x0200b4d8        0x4 obj\Debug\hal\src\hal_md.o
 .bss           0x0200b4dc      0x3b4 obj\Debug\hal\src\hal_mjpAEncode.o
 .bss           0x0200b890      0x3a0 obj\Debug\hal\src\hal_mjpBEncode.o
 .bss           0x0200bc30       0x74 obj\Debug\hal\src\hal_mjpDecode.o
 .bss           0x0200bca4       0x34 obj\Debug\hal\src\hal_rtc.o
 .bss           0x0200bcd8        0x8 obj\Debug\hal\src\hal_spi.o
                0x0200bcd8                spi_updata_led
 .bss           0x0200bce0       0x10 obj\Debug\hal\src\hal_spi1.o
 .bss           0x0200bcf0        0x0 obj\Debug\hal\src\hal_stream.o
 .bss           0x0200bcf0      0x50c obj\Debug\hal\src\hal_sys.o
 .bss           0x0200c1fc        0x0 obj\Debug\hal\src\hal_timer.o
 .bss           0x0200c1fc        0x8 obj\Debug\hal\src\hal_uart.o
 .bss           0x0200c204        0x8 obj\Debug\hal\src\hal_watermark.o
 .bss           0x0200c20c        0x0 obj\Debug\hal\src\hal_wdt.o
 .bss           0x0200c20c        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .bss           0x0200c20c        0x0 obj\Debug\mcu\xos\xmbox.o
 .bss           0x0200c20c        0x0 obj\Debug\mcu\xos\xmsgq.o
 .bss           0x0200c20c        0x8 obj\Debug\mcu\xos\xos.o
 .bss           0x0200c214       0x80 obj\Debug\mcu\xos\xwork.o
 .bss           0x0200c294       0x60 obj\Debug\multimedia\audio\audio_playback.o
 .bss           0x0200c2f4       0x48 obj\Debug\multimedia\audio\audio_record.o
 .bss           0x0200c33c        0x0 obj\Debug\multimedia\image\image_decode.o
 .bss           0x0200c33c        0x0 obj\Debug\multimedia\image\image_encode.o
 .bss           0x0200c33c        0x0 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .bss           0x0200c33c      0x1c8 obj\Debug\multimedia\video\video_playback.o
 .bss           0x0200c504        0x0 obj\Debug\multimedia\video\video_record.o
 .bss           0x0200c504        0xc obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .bss           0x0200c510      0x530 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .bss           0x0200ca40       0x94 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .bss           0x0200cad4        0x0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .bss           0x0200cad4        0x0 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .bss           0x0200cad4       0x60 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .bss           0x0200cb34     0x5068 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .bss           0x02011b9c        0x4 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .bss           0x02011ba0       0x50 obj\Debug\app\app_common\src\app_init.o
 .bss           0x02011bf0       0x10 obj\Debug\app\app_common\src\app_lcdshow.o
 .bss           0x02011c00        0x0 obj\Debug\app\app_common\src\main.o
 .bss           0x02011c00        0x0 obj\Debug\app\resource\user_res.o
 .bss           0x02011c00        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .bss           0x02011c00        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .bss           0x02011c00        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .bss           0x02011c00        0x8 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                0x02011c00                menuOptionmemory
 .bss           0x02011c08       0x48 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .bss           0x02011c50        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .bss           0x02011c50        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .bss           0x02011c50        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .bss           0x02011c50        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .bss           0x02011c50        0x8 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
 .bss           0x02011c58        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .bss           0x02011c58        0x4 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .bss           0x02011c5c        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .bss           0x02011c5c        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .bss           0x02011c5c        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .bss           0x02011c5c        0x0 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .bss           0x02011c5c        0x0 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .bss           0x02011c5c        0xc obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .bss           0x02011c68        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .bss           0x02011c6c        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .bss           0x02011c70        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .bss           0x02011c74      0x128 obj\Debug\app\task_windows\msg_api.o
 .bss           0x02011d9c       0x3c obj\Debug\app\task_windows\task_api.o
 .bss           0x02011dd8        0x1 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 *fill*         0x02011dd9        0x3 
 .bss           0x02011ddc       0x3c obj\Debug\app\task_windows\task_common\src\task_common.o
 .bss           0x02011e18        0x0 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .bss           0x02011e18        0x0 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .bss           0x02011e18        0x4 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .bss           0x02011e1c        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .bss           0x02011e1c        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .bss           0x02011e1c        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .bss           0x02011e1c        0xc obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .bss           0x02011e28        0x0 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .bss           0x02011e28        0x0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .bss           0x02011e28        0x0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .bss           0x02011e28        0x0 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .bss           0x02011e28       0x18 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .bss           0x02011e40        0x0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
 .bss           0x02011e40       0x18 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .bss           0x02011e58        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .bss           0x02011e58        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .bss           0x02011e58        0x0 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .bss           0x02011e58        0x1 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .bss           0x02011e59        0x0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .bss           0x02011e59        0x0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .bss           0x02011e59        0x0 obj\Debug\app\task_windows\windows_api.o
 .bss           0x02011e59        0x0 obj\Debug\app\user_config\src\mbedtls_md5.o
 *fill*         0x02011e59        0x3 
 .bss           0x02011e5c      0x20c obj\Debug\app\user_config\src\user_config_api.o
 .bss           0x02012068        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .bss           0x02012068        0x0 ..\lib\libboot.a(boot.o)
 .bss           0x02012068        0x0 ..\lib\libboot.a(boot_loader.o)
 .bss           0x02012068        0x0 ..\lib\libboot.a(reset.o)
 .bss           0x02012068        0x0 ..\lib\libboot.a(boot_lib.o)
 .bss           0x02012068        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .bss           0x02012068        0x8 ..\lib\libmcu.a(hx330x_auadc.o)
 .bss           0x02012070       0x3c ..\lib\libmcu.a(hx330x_csi.o)
 .bss           0x020120ac        0x4 ..\lib\libmcu.a(hx330x_dac.o)
 .bss           0x020120b0        0x0 ..\lib\libmcu.a(hx330x_dma.o)
 .bss           0x020120b0        0x4 ..\lib\libmcu.a(hx330x_dmauart.o)
 .bss           0x020120b4       0x6c ..\lib\libmcu.a(hx330x_gpio.o)
 .bss           0x02012120        0x0 ..\lib\libmcu.a(hx330x_iic.o)
 .bss           0x02012120        0x0 ..\lib\libmcu.a(hx330x_int.o)
 .bss           0x02012120        0xc ..\lib\libmcu.a(hx330x_isp.o)
                0x02012120                isp_ccf_dn_tab
                0x02012124                isp_ee_dn_tab
                0x02012128                isp_ee_sharp_tab
 .bss           0x0201212c        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .bss           0x0201212c       0x54 ..\lib\libmcu.a(hx330x_jpg.o)
                0x0201212c                mjpBEncAvgSize
                0x02012130                mjpAEncAvgSize
 .bss           0x02012180        0x0 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 .bss           0x02012180        0x0 ..\lib\libmcu.a(hx330x_lcd.o)
 .bss           0x02012180        0x4 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .bss           0x02012184        0x8 ..\lib\libmcu.a(hx330x_lcdui.o)
 .bss           0x0201218c        0x4 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .bss           0x02012190        0x0 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .bss           0x02012190        0x0 ..\lib\libmcu.a(hx330x_md.o)
 .bss           0x02012190        0x0 ..\lib\libmcu.a(hx330x_mipi.o)
 .bss           0x02012190        0x0 ..\lib\libmcu.a(hx330x_misc.o)
 .bss           0x02012190        0x4 ..\lib\libmcu.a(hx330x_rtc.o)
                0x02012190                rtcAlarmFlag
 .bss           0x02012194       0x10 ..\lib\libmcu.a(hx330x_sd.o)
 .bss           0x020121a4        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .bss           0x020121a4        0x4 ..\lib\libmcu.a(hx330x_spi1.o)
 .bss           0x020121a8        0x4 ..\lib\libmcu.a(hx330x_sys.o)
                0x020121a8                mcp1_lock
 .bss           0x020121ac        0x0 ..\lib\libmcu.a(hx330x_timer.o)
 .bss           0x020121ac       0x28 ..\lib\libmcu.a(hx330x_tminf.o)
 .bss           0x020121d4        0x4 ..\lib\libmcu.a(hx330x_uart.o)
 .bss           0x020121d8       0x7c ..\lib\libmcu.a(hx330x_usb.o)
 .bss           0x02012254        0x0 ..\lib\libmcu.a(hx330x_wdt.o)
 .bss           0x02012254        0x4 ..\lib\libmcu.a(hx330x_emi.o)
 .bss           0x02012258       0xe0 ..\lib\libisp.a(hal_isp.o)
 .bss           0x02012338       0x4c ..\lib\libjpg.a(hal_jpg.o)
 .bss           0x02012384       0x16 ..\lib\liblcd.a(hal_lcd.o)
 .bss           0x0201239a        0x0 ..\lib\liblcd.a(hal_lcdMem.o)
 *fill*         0x0201239a        0x2 
 .bss           0x0201239c       0x28 ..\lib\liblcd.a(hal_lcdrotate.o)
 .bss           0x020123c4       0x28 ..\lib\liblcd.a(hal_lcdUi.o)
 .bss           0x020123ec        0x8 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .bss           0x020123f4        0x0 ..\lib\liblcd.a(lcd_tab.o)
 .bss           0x020123f4       0x60 ..\lib\libmultimedia.a(api_multimedia.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(avi_dec.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(avi_std_enc.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(wav_dec.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(wav_enc.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(wav_pcm.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
 *(COMMON)
 COMMON         0x02012454       0x1c obj\Debug\dev\battery\src\battery_api.o
                0x02012454                WAV_TYPE_E
                0x02012458                USB_CH
                0x0201245c                UVC_CACHE_STA
                0x02012460                SDCON0_T
                0x02012464                SDCON1_T
                0x02012468                UVC_FSTACK_STA
                0x0201246c                CHANNEL_EXCHANGE_E
 COMMON         0x02012470       0x14 obj\Debug\dev\fs\src\fs_api.o
                0x02012470                fs_exfunc
 COMMON         0x02012484        0x8 obj\Debug\dev\gsensor\src\gsensor_api.o
                0x02012484                gsensor_ctl
 COMMON         0x0201248c       0x78 obj\Debug\dev\key\src\key_api.o
                0x0201248c                dev_key_tab
 COMMON         0x02012504       0x20 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                0x02012504                tp_api_t
 COMMON         0x02012524      0x29c obj\Debug\dev\usb\dusb\src\dusb_api.o
                0x02012524                usb_dev_ctl
 COMMON         0x020127c0     0x1484 obj\Debug\dev\usb\husb\src\husb_api.o
                0x020127c0                husb_ctl
 COMMON         0x02013c44        0x4 obj\Debug\dev\usb\husb\src\husb_hub.o
                0x02013c44                uhub_handle
 COMMON         0x02013c48        0x4 obj\Debug\dev\usb\husb\src\husb_usensor.o
                0x02013c48                usensor_handle
 COMMON         0x02013c4c        0x4 obj\Debug\mcu\xos\xos.o
                0x02013c4c                XOSNesting
 COMMON         0x02013c50       0x74 obj\Debug\multimedia\video\video_record.o
                0x02013c50                mediaVideoCtl
 COMMON         0x02013cc4       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                0x02013cc4                ui_draw_ctrl
 COMMON         0x02013cd4      0x130 obj\Debug\app\app_common\src\app_init.o
                0x02013cd4                SysCtrl
 COMMON         0x02013e04        0x4 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x02013e04                task_play_audio_stat
 COMMON         0x02013e08       0x44 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x02013e08                playVideoOp
 COMMON         0x02013e4c       0x20 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x02013e4c                recordPhotoOp
 COMMON         0x02013e6c       0x64 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02013e6c                sd_update_op
 COMMON         0x02013ed0        0xc obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                0x02013ed0                usbDeviceOp
 COMMON         0x02013edc      0x500 ..\lib\libmcu.a(hx330x_isp.o)
                0x02013edc                RGB_GMMA_Tab
                0x020141dc                Y_GMA_Tab
 COMMON         0x020143dc       0x14 ..\lib\libmcu.a(hx330x_lcd.o)
                0x020143dc                hx330x_lcdISR
 COMMON         0x020143f0       0x14 ..\lib\libmcu.a(hx330x_rtc.o)
                0x020143f0                rc128k_div
                0x020143f4                rtcSecondISR
                0x020143f8                rtcAlamISR
                0x020143fc                rc128k_rtc_cnt
                0x02014400                rc128k_timer_cnt
 COMMON         0x02014404        0x4 ..\lib\libmcu.a(hx330x_sys.o)
                0x02014404                smph_dmacopy
 COMMON         0x02014408       0x10 ..\lib\libmcu.a(hx330x_timer.o)
                0x02014408                hx330x_timerISR
 COMMON         0x02014418       0x84 ..\lib\liblcd.a(hal_lcd.o)
                0x02014418                lcd_show_ctrl
 COMMON         0x0201449c      0x1e8 ..\lib\liblcd.a(hal_lcdMem.o)
                0x0201449c                lcdshow_frame_op
 *(.big_buffer*)
 *(._sdram_buf_)
 ._sdram_buf_   0x02014684     0x8044 obj\Debug\dev\fs\src\fs_api.o
                0x02014684                work_fatfs
                0x0201c700                _sdram_remian_addr = (ALIGN (0x40) + 0x0)
                0x0000c800                _text_lma = (((LOADADDR (.on_sdram) + SIZEOF (.on_sdram)) + 0x1ff) & 0xfffffe00)
                0x0600c800                _text_vma = (ORIGIN (flash) + _text_lma)

.text           0x0600c800    0x89450 load address 0x0000c800
 *(.text*)
 .text          0x0600c800      0x264 obj\Debug\dev\battery\src\battery_api.o
                0x0600c800                dev_battery_ioctrl
                0x0600c9a4                dev_battery_init
 .text          0x0600ca64      0x254 obj\Debug\dev\dev_api.o
                0x0600ca64                dev_api_node_init
                0x0600cb88                dev_open
                0x0600cc48                dev_ioctrl
 .text          0x0600ccb8      0x244 obj\Debug\dev\fs\src\diskio.o
                0x0600ccb8                get_fattime
                0x0600cd38                disk_status
                0x0600cd88                disk_initialize
                0x0600cddc                disk_read
                0x0600ce38                disk_write
                0x0600ce94                disk_ioctl
 .text          0x0600cefc     0x8084 obj\Debug\dev\fs\src\ff.o
                0x06010ce4                f_mount
                0x06010d94                f_open
                0x060112e4                f_read
                0x0601161c                f_write
                0x060119ac                f_sync
                0x06011c10                f_close
                0x06011c5c                f_ftime
                0x06011cb8                f_lseek
                0x0601243c                f_opendir
                0x06012594                f_closedir
                0x060125d0                f_readdir
                0x06012674                f_findnext
                0x06012718                f_findfirst
                0x0601276c                f_stat
                0x06012800                f_getfree
                0x06012b7c                f_truncate
                0x06012cd4                f_unlink
                0x06012e88                f_mkdir
                0x06013178                f_rename
                0x0601345c                f_chmod
                0x06013544                f_utime
                0x06013628                f_expand
                0x0601390c                f_mkfs
                0x06014c04                FEX_getlink_clust
                0x06014c3c                f_merge
                0x06014dec                _f_bound
 .text.unlikely
                0x06014f80       0x30 obj\Debug\dev\fs\src\ff.o
 .text          0x06014fb0      0x204 obj\Debug\dev\fs\src\ffunicode.o
                0x06014fb0                ff_uni2oem
                0x06015028                ff_oem2uni
                0x060150a0                ff_wtoupper
 .text          0x060151b4      0xa2c obj\Debug\dev\fs\src\fs_api.o
                0x060151b4                fs_exfunc_init
                0x0601520c                fs_nodeinit
                0x0601523c                fs_mount
                0x06015338                fs_open
                0x06015408                fs_close
                0x06015480                fs_read
                0x060154f8                fs_write
                0x060155e8                fs_seek
                0x060156b4                fs_getcltbl
                0x06015704                fs_getclusize
                0x06015754                fs_mkdir
                0x06015780                fs_alloc
                0x060157f4                fs_sync
                0x0601585c                fs_merge
                0x060158e8                fs_bound
                0x06015974                fs_getclustersize
                0x06015998                fs_size
                0x060159e4                fs_pre_size
                0x06015a30                fs_tell
                0x06015a7c                fs_free_size
                0x06015b00                fs_check
                0x06015b28                fs_getStartSector
                0x06015b84                fs_ftime
 .text          0x06015be0      0x36c obj\Debug\dev\gsensor\src\gsensor_api.o
                0x06015be0                gsensor_iic_enable
                0x06015c04                gsensor_iic_disable
                0x06015c24                gSensorGetName
                0x06015c58                dev_gSensor_Init
                0x06015d78                dev_gSensor_ioctrl
 .text          0x06015f4c      0x550 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .text          0x0601649c      0x80c obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .text          0x06016ca8      0x4fc obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .text          0x060171a4      0x12c obj\Debug\dev\ir\src\ir_api.o
                0x060171a4                dev_ir_init
                0x060171ec                dev_ir_ioctrl
 .text          0x060172d0      0x624 obj\Debug\dev\key\src\key_api.o
                0x060172d0                dev_key_init
                0x06017424                dev_key_ioctrl
                0x06017890                getKeyADCvalue
                0x060178b0                getKeyCurEvent
                0x060178d0                keyLongTypeScanModeSet
 .text          0x060178f4      0x318 obj\Debug\dev\lcd\src\lcd_api.o
                0x060178f4                lcd_initTab_config
                0x06017a04                LcdGetName
                0x06017a20                dev_lcd_init
                0x06017ad0                dev_lcd_ioctrl
                0x06017bec                dev_lcd_nocolor_status
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .text          0x06017c0c        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .text          0x06017c0c      0x104 obj\Debug\dev\led\src\led_api.o
                0x06017c0c                dev_led_init
                0x06017c24                dev_led_ioctrl
 .text          0x06017d10       0xf0 obj\Debug\dev\led_pwm\src\led_pwm_api.o
                0x06017d10                dev_led_pwm_init
                0x06017d28                dev_led_pwm_ioctrl
 .text          0x06017e00      0x30c obj\Debug\dev\nvfs\src\nvfs_api.o
                0x06017e00                nv_port_read
                0x06017e20                nv_init
                0x06017f50                nv_uninit
                0x06017f68                nv_configAddr
                0x06017fe0                nv_open
                0x0601805c                nv_size
                0x060180dc                nv_read
 .text          0x0601810c     0x1f0c obj\Debug\dev\nvfs\src\nvfs_jpg.o
                0x06018994                nv_dir_read
                0x06018ad8                nv_dir_readfirst
                0x06018b48                nv_dir_readnext
                0x06018b9c                nv_jpg_ex_force_init
                0x06018bbc                nv_jpg_ex_init
                0x06018c98                nv_jpg_formart_init
                0x06018e68                nv_jpg_init
                0x060190a8                nv_jpg_uinit
                0x06019110                nv_jpg_format
                0x060191a0                nv_jpg_open
                0x060194d8                nv_jpg_change_lock
                0x060195b4                nv_jpg_close
                0x0601968c                nv_jpgfile_read
                0x0601988c                nv_jpgfile_seek
                0x06019aa8                nv_jpgfile_delete
                0x06019ba0                nv_jpgfile_size
                0x06019be0                nvjpg_free_size
                0x06019c14                nv_jpgfile_write
                0x06019fe8                nvjpg_free_dir
 .text          0x0601a018      0xc28 obj\Debug\dev\sd\src\sd_api.o
                0x0601a7ac                sd_api_init
                0x0601a9f8                sd_api_getNextLBA
                0x0601aa18                sd_api_Uninit
                0x0601aa38                sd_api_lock
                0x0601aa78                sd_api_unlock
                0x0601aaa4                sd_api_CardState_Set
                0x0601aac4                sd_api_CardState_Get
                0x0601aae4                sd_api_GetBusWidth
                0x0601ab04                sd_api_Capacity
                0x0601ab24                sd_api_speed_debg
                0x0601ab70                dev_sdc_init
                0x0601aba0                dev_sdc_ioctrl
 .text          0x0601ac40      0x708 obj\Debug\dev\sensor\src\sensor_api.o
                0x0601ac40                sensor_iic_write
                0x0601acac                sensor_iic_read
                0x0601ad2c                sensor_rgbgamma_tab_load
                0x0601adf4                sensor_ygamma_tab_load
                0x0601aebc                sensor_lsc_tab_load
                0x0601af0c                SensorGetName
                0x0601af28                dev_sensor_init
                0x0601af40                dev_sensor_ioctrl
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .text          0x0601b348        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .text          0x0601b348      0x504 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                0x0601b3a8                dev_touchpanel_Init
                0x0601b544                dev_touchpanel_ioctrl
 .text          0x0601b84c      0x1ec obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                0x0601b84c                tp_icnt81_getPoint
 .text          0x0601ba38      0x74c obj\Debug\dev\touchpanel\src\touchpanel_iic.o
                0x0601be7c                tp_iic_init
                0x0601bef0                tp_iic_config
                0x0601bf14                tp_iic_write
                0x0601c024                tp_iic_read
 .text          0x0601c184      0x374 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                0x0601c184                tp_ns2009_Match
                0x0601c228                tp_ns2009_getPoint
 .text          0x0601c4f8      0x3f4 obj\Debug\dev\usb\dusb\src\dusb_api.o
                0x0601c550                dusb_api_online
                0x0601c570                dusb_api_Init
                0x0601c66c                dusb_api_Uninit
                0x0601c6ac                dusb_api_offline
                0x0601c6e4                dusb_api_Process
                0x0601c754                dev_dusb_init
                0x0601c790                dev_dusb_ioctrl
 .text          0x0601c8ec      0xb50 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x0601ca94                dusb_stall_ep
                0x0601cb20                dusb_ep0_tx
                0x0601cb9c                dusb_ep0_recieve_set
                0x0601cbd4                dusb_ep0_process
                0x0601d328                dusb_ep0_cfg
                0x0601d37c                dusb_cfg_reg
 .text          0x0601d43c     0x1150 obj\Debug\dev\usb\dusb\src\dusb_msc.o
                0x0601d5b4                msc_epx_cfg
                0x0601d650                dusb_WriteToMem
                0x0601d690                dusb_ReadFromMem
                0x0601d6b4                rbc_mem_read
                0x0601d724                rbc_mem_write
                0x0601d79c                rbc_mem_rxfunc
                0x0601d7d8                sdk_returnmask
                0x0601d914                cbw_updatartc
                0x0601d95c                mscCmd_ufmod
                0x0601d9a8                sent_csw
                0x0601da84                mscCmd_Read
                0x0601db84                mscCmd_Write
                0x0601dc84                scsi_cmd_analysis
                0x0601e1e0                get_cbw
                0x0601e42c                rbc_rec_pkg
                0x0601e4cc                rbc_process
 .text          0x0601e58c        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .text          0x0601e58c      0x5a4 obj\Debug\dev\usb\dusb\src\dusb_uac.o
                0x0601e58c                uac_set_volume
                0x0601e5d8                uac_set_mute
                0x0601e620                uac_isr_process
                0x0601e740                uac_epx_cfg
                0x0601e7b4                uac_get_volume
                0x0601e844                uac_unit_ctl_hal
                0x0601e918                UacHandleToStreaming
                0x0601e9b4                uac_start
                0x0601ea78                UacReceiveSetSamplingFreqCallback
                0x0601ead4                uac_stop
 .text          0x0601eb30      0x8f0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                0x0601eb30                uvc_pic_callback
                0x0601eb58                unitsel_set
                0x0601eb94                uvc_video_probe_control_callback
                0x0601ebc8                uvc_still_probe_control_callback
                0x0601ebfc                uvc_epx_cfg
                0x0601ed50                uvc_unit_ctl_hal
                0x0601edf8                uvc_video_probe_control
                0x0601ee40                uvc_still_probe_control
                0x0601ee88                uvc_still_trigger_control
                0x0601eebc                uvc_probe_ctl_hal
                0x0601ef38                uvc_start
                0x0601efdc                uvc_stop
                0x0601f040                uvc_is_start
                0x0601f060                uvc_pic_sanp
                0x0601f080                uvc_header_fill
                0x0601f168                uvc_isr_process
                0x0601f314                uvc_process
 .text          0x0601f420      0x8f0 obj\Debug\dev\usb\husb\src\husb_api.o
                0x0601f420                husb_api_u20_remove
                0x0601f4ac                husb_api_u11_remove
                0x0601f748                husb_api_handle_get
                0x0601f778                husb_api_init
                0x0601f8d4                husb_api_devicesta
                0x0601f908                husb_api_devicesta_set
                0x0601f95c                husb_api_msc_try_tran
                0x0601f998                dev_husb_io_power_set
                0x0601fa80                dev_husb_init
                0x0601fb44                dev_husb_ioctrl
 .text          0x0601fd10     0x2970 obj\Debug\dev\usb\husb\src\husb_enum.o
                0x060201d4                husb_hub_clrport_feature_ack
                0x06020234                husb_hub_set_port_devaddr_ack
                0x060203e0                husb_get_pcommit_cs100_ack
                0x060204c4                husb_uvcunit_ack
                0x06020bd8                husb_setport_feature_ack
                0x06020c00                husb_hub_port_reset_ack
                0x06020c28                husb_get_max_lun_ack
                0x06020c5c                husb_set_intfs_uvc_1_ack
                0x06020c80                husb_hub_inf_ack
                0x06020e34                husb_hub_getport_status_ack
                0x06020ed4                husb_set_pcommit_cs200_ack
                0x06020f10                husb20_ep0_cfg
                0x06020f30                husb11_ep0_cfg
                0x06020f50                usensor_resolution_select
                0x0602127c                husb_all_get_cfg_desc_ack
                0x06021e0c                husb_astern_check
                0x06021e48                husb_astern_ack
                0x06021ecc                husb_api_ep0_kick
                0x06021fac                husb_api_ep0_process
                0x060220f4                husb_api_ep0_uvc_switch_kick
                0x06022130                husb_api_ep0_asterncheck_kick
                0x0602217c                husb_api_uvcunit_get_kick
                0x06022310                husb_api_uvcunit_get_done
                0x060223c4                husb_api_uvcunit_set_kick
                0x06022560                husb_api_uvcunit_set_done
                0x06022608                husb_api_hub_check_kick
 .text          0x06022680      0x230 obj\Debug\dev\usb\husb\src\husb_hub.o
                0x06022680                husb_hub_init
                0x06022824                husb_hub_uinit
 .text          0x060228b0     0x1138 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
                0x060228b0                udisk_cap
                0x060228d4                udisk_online
                0x06022904                husb_msc_init
                0x06022a90                epbulk20_send_dat
                0x06022c54                epbulk20_recieve_dat
                0x06022dc4                epbulk11_send_dat
                0x06022f64                epbulk11_recieve_dat
                0x060230c8                epbulk_send_dat
                0x06023118                epbulk_receive_dat
                0x06023168                cbw_init
                0x060231b4                rbc_read_lba
                0x060232b8                rbc_write_lba
                0x060233ac                spc_inquiry
                0x06023460                spc_test_unit_rdy
                0x06023500                spc_request_sense
                0x060235b4                rbc_read_capacity
                0x06023744                spc_StartStopUnit
                0x060237cc                enum_mass_dev
 .text          0x060239e8      0x7c4 obj\Debug\dev\usb\husb\src\husb_usensor.o
                0x060239e8                husb_api_handle_reg
                0x06023a08                husb_api_usensor_tran_sta
                0x06023a40                husb_api_usensor_atech_sta
                0x06023a78                husb_api_usensor_res_get
                0x06023ac0                husb_api_usensor_res_type_is_mjp
                0x06023afc                husb_api_usensor_res_type_is_yuv
                0x06023b38                husb_api_astern_set
                0x06023b98                husb_api_astern_get
                0x06023bd0                husb_api_detech_check
                0x06023c08                husb_api_usensor_linkingLcd
                0x06023c34                husb_api_usensor_relinkLcd_reg
                0x06023c60                husb_api_usensor_dcdown
                0x06023c98                husb_api_usensor_detech
                0x06023d0c                husb_api_usensor_asterncheck
                0x06023d40                husb_api_usensor_asternset
                0x06023d9c                husb_api_usensor_frame_read
                0x06023e08                husb_api_usensor_csi_kick
                0x06023e78                husb_api_usensor_switch_res_kick
                0x06024004                husb_api_usensor_notran_check
                0x060240d0                husb_api_usensor_stop_fill
                0x06024100                husb_api_usensor_uvcunit_get
                0x0602415c                husb_api_usensor_uvcunit_set
 .text          0x060241ac      0x738 obj\Debug\dev\usb\husb\src\husb_uvc.o
                0x060243c8                husb_uvc_atech_codec
                0x06024498                husb_uvc_dcd_cache_get
                0x060244bc                husb_uvc_init
                0x060246dc                husb_uvc_linking
                0x060247a0                husb_uvc_relink_register
                0x060247d0                husb_uvc_detech
                0x06024818                husb_uvc_stop_fill
 .text          0x060248e4       0xcc obj\Debug\hal\src\hal_adc.o
                0x060248e4                hal_adcInit
                0x06024924                hal_adcRead
                0x06024958                hal_adcSetChannel
 .text          0x060249b0      0x784 obj\Debug\hal\src\hal_auadc.o
                0x06024c68                hal_auadc_stamp_out
                0x06024c88                hal_auadc_stamp_next
                0x06024ca8                hal_auadcInit
                0x06024ce8                hal_auadcMemInit
                0x06024d98                hal_auadc_pcmsize_get
                0x06024db8                hal_auadcMemUninit
                0x06024e14                hal_auadc_cnt
                0x06024e40                hal_auadcmutebuf_get
                0x06024e60                hal_auadcStart
                0x06025000                hal_auadcBufferGet
                0x0602506c                hal_auadcBufferRelease
                0x06025094                hal_auadcStop
                0x060250d4                hal_adcBuffer_prefull
                0x06025114                hal_adc_volume_set
 .text          0x06025134      0x4fc obj\Debug\hal\src\hal_csi.o
                0x06025434                hal_csi_init
                0x06025458                hal_csi_input_switch
                0x0602558c                hal_csi_test_fps_adj
 .text          0x06025630      0x1f0 obj\Debug\hal\src\hal_dac.o
                0x06025630                hal_dacInit
                0x06025694                hal_dacPlayInit
                0x0602571c                hal_dacPlayStart
                0x06025778                hal_dacPlayStop
                0x060257a0                hal_dacSetVolume
                0x06025800                hal_dacCallBackRegister
 .text          0x06025820      0x42c obj\Debug\hal\src\hal_dmauart.o
                0x06025820                hal_dmaUartRxOverWait
                0x060258b4                hal_dmaUartRxDataOut
                0x06025950                hal_dmaUartIRQHandler
                0x060259d8                hal_dmauartInit
                0x06025a80                hal_dmauartTxDma
                0x06025ae0                hal_dmauartTxDmaKick
                0x06025b38                hal_dmauartTest
 .text          0x06025c4c        0x0 obj\Debug\hal\src\hal_eeprom.o
 .text          0x06025c4c       0xa8 obj\Debug\hal\src\hal_gpio.o
                0x06025c4c                hal_gpioInit_io1d1
 .text          0x06025cf4      0xa50 obj\Debug\hal\src\hal_iic.o
                0x06025cf4                hal_iic0Init
                0x06025d24                hal_iic0Uninit
                0x06025d44                hal_iic08bitAddrWriteData
                0x06025db0                hal_iic08bitAddrReadData
                0x06025e38                hal_iic08bitAddrWrite
                0x06025ec8                hal_iic08bitAddrRead
                0x06025f68                hal_iic016bitAddrWriteData
                0x06025fe0                hal_iic016bitAddrReadData
                0x06026074                hal_iic016bitAddrWrite
                0x06026110                hal_iic016bitAddrRead
                0x060261bc                hal_iic1IOShare
                0x060261ec                hal_iic1IOShareCheck
                0x06026234                hal_iic1Init
                0x06026274                hal_iic1Uninit
                0x060262ac                hal_iic18bitAddrWriteData
                0x0602631c                hal_iic18bitAddrReadData
                0x060263a8                hal_iic18bitAddrWrite
                0x0602643c                hal_iic18bitAddrRead
                0x060264e0                hal_iic116bitAddrWriteData
                0x0602655c                hal_iic116bitAddrReadData
                0x060265f4                hal_iic116bitAddrWrite
                0x06026694                hal_iic116bitAddrRead
 .text          0x06026744       0x34 obj\Debug\hal\src\hal_int.o
                0x06026744                hal_intInit
 .text          0x06026778     0x18cc obj\Debug\hal\src\hal_lcdshow.o
                0x0602680c                hal_lcdSetCsiScaler
                0x060268c0                hal_lcdSetVideoScaler
                0x06026930                hal_lcd_scaler_done_check
                0x06026988                hal_lcdVideoSetRotate
                0x06026a80                hal_lcdUiSetRotate
                0x06026ae0                hal_lcdSetRatio
                0x06026d1c                hal_lcdSetBufYUV
                0x06026db0                hal_lcdSetBufYUV_2
                0x06026e34                hal_lcdSetWINAB
                0x06026f08                hal_lcdWinEnablePreSet
                0x06026f28                hal_lcdSetWinEnable
                0x06026f60                lcd_struct_get
                0x06026f80                hal_lcdLCMPowerOff
                0x06027004                hal_lcd_decwin_done
                0x06027310                hal_lcd_encwin_done
                0x060273c0                hal_lcd_frame_enc_func_register
                0x06027410                hal_CSI_lcdFrameEndCallback
                0x06027858                hal_lcd_fps_debg
                0x060278a0                hal_lcdGetSreenResolution
                0x06027928                hal_lcdGetUiResolution
                0x06027984                hal_lcdGetUiPosition
                0x060279e0                hal_lcdUiScanModeGet
                0x06027a10                hal_lcdGetVideoRatioResolution
                0x06027a6c                hal_lcdSetVideoRatioResolution
                0x06027aa4                hal_lcdGetVideoRatioPos
                0x06027b00                hal_lcdGetVideoResolution
                0x06027b5c                hal_lcdGetVideoPos
                0x06027bb8                hal_lcdVideoScanModeGet
                0x06027be8                hal_lcdVideoScalerTypeAdj
                0x06027ca0                hal_lcd_enc_start
                0x06027d34                hal_lcd_enc_stop
                0x06027da0                hal_lcd_enc_checkdone
                0x06027dcc                hal_lcd_enc_frame_get
                0x06027e0c                hal_lcd_enc_frame_res_get
                0x06027e50                hal_lcd_pause_set
                0x06027ea4                hal_lcd_pause_sta_get
                0x06027ef4                hal_lcdSetGamma
 .text          0x06028044      0x154 obj\Debug\hal\src\hal_md.o
                0x0602806c                hal_mdInit
                0x06028118                hal_mdEnable
                0x06028160                hal_mdCheck
 .text          0x06028198     0x1dc0 obj\Debug\hal\src\hal_mjpAEncode.o
                0x0602825c                hal_mjpA_Start
                0x0602828c                hal_mjpA_Restart
                0x060284f4                hal_mjpA_Sizecalculate
                0x06028520                hal_mjpA_EncState
                0x06028b58                hal_jA_fcnt_mnt
                0x06028bac                hal_mjpA_Linebuf_nocolor_change
                0x06028e2c                hal_mjpAEncodePhotoResumePKG
                0x06028eec                hal_mjpAEncodePhotoResumeLLPKG
                0x06028f9c                hal_mjpAEncodePhotoResumeRam
                0x06029098                hal_mjpA_EncodeInit
                0x060290f4                hal_mjpA_LineBuf_get
                0x06029120                hal_mjpA_src_res_get
                0x06029154                hal_mjpA_buf_MenInit
                0x06029204                hal_mjpA_linebufUninit
                0x0602924c                hal_mjpA_MemUninit
                0x0602929c                hal_mjpA_EncodeUninit
                0x06029328                hal_mjpA_EncVideo_Start
                0x060296b4                hal_mjpA_EncPhoto_Start
                0x06029a48                hal_mjpA_EncPhotoLcd_Start
                0x06029cf8                hal_mjpA_photo_encode_mode
                0x06029d18                hal_mjpA_RawBufferfree
                0x06029d40                hal_mjpA_RawBufferGet
                0x06029e3c                hal_mjpA_RkgBufferGet
                0x06029ee0                hal_mjpA_Buffer_prefull
                0x06029f1c                hal_mjpA_Buffer_halffull
 .text          0x06029f58      0x85c obj\Debug\hal\src\hal_mjpBEncode.o
                0x06029f58                hal_mjpBEncodeKickManual
                0x0602a050                hal_mjpB_Sizecalculate
                0x0602a078                hal_jB_fcnt_mnt
                0x0602a0cc                hal_mjpBEnc_state
                0x0602a0f8                hal_mjpBEncodeDoneCfg
                0x0602a11c                hal_mjpBEncodeDoneManual
                0x0602a22c                hal_mjpB_LineBuf_MenInit
                0x0602a2c8                hal_mjpB_LineBuf_cfg
                0x0602a2ec                hal_mjpB_buf_MenInit
                0x0602a3a4                hal_mjpB_MemUninit
                0x0602a404                hal_mjpB_usb_resolution_set
                0x0602a428                hal_mjpB_DecodeODMA1En
                0x0602a46c                hal_mjpB_Enc_Start
                0x0602a5e4                hal_mjpB_Enc_Stop
                0x0602a618                hal_mjpB_RawBufferfree
                0x0602a640                hal_mjpB_RawBufferGet
                0x0602a73c                hal_mjpB_Buffer_prefull
                0x0602a778                hal_mjpB_Buffer_halffull
 .text          0x0602a7b4      0xe50 obj\Debug\hal\src\hal_mjpDecode.o
                0x0602a7b4                hal_mjpHeaderParse
                0x0602ae64                hal_mjpDecodeIsYUV422
                0x0602ae84                hal_mjpDecodeGetResolution
                0x0602aec8                hal_mjpDecodeSetResolution
                0x0602aeec                hal_mjpDecodeBusyCheck
                0x0602af0c                hal_mjpDecodeErrorCheck
                0x0602af2c                hal_mjpDecodeStop
                0x0602af4c                hal_mjpDecodeReset
                0x0602af78                hal_mjpDecodePicture
                0x0602b008                hal_mjpegDecodePicture_noisr
                0x0602b094                hal_mjpegDecodePicture_packet
                0x0602b144                hal_mjpDecodeParse
                0x0602b174                hal_mjpDecodeOneFrame
                0x0602b1ec                hal_mjpDecodeOneFrame_Ext
                0x0602b284                hal_mjpDecodeRestart_Ext
                0x0602b2f0                hal_mjpDecodeOneFrame_Fast
                0x0602b3ec                hal_mjpDecodeMiniSize
                0x0602b570                hal_mjpDecodeODma1Cfg
                0x0602b594                hal_BackRecDecodeStatusCheck
 .text          0x0602b604      0xc80 obj\Debug\hal\src\hal_rtc.o
                0x0602b630                hal_rtcCallBackRegister
                0x0602b690                hal_rtcCallBackRelease
                0x0602b6cc                hal_rtcUninit
                0x0602b708                hal_rtcTimeGet
                0x0602b724                hal_rtcTimeGetExt
                0x0602b758                hal_rtcSecondGet
                0x0602b778                hal_rtcTime2String
                0x0602ba2c                hal_rtcTime2StringExt
                0x0602ba90                hal_rtcLeapYear
                0x0602bcb0                hal_rtcTime
                0x0602bdc4                hal_rtcInit
                0x0602bf04                hal_rtcSecondSet
                0x0602bf40                hal_rtcValue
                0x0602c050                hal_rtcTimeSet
                0x0602c0f4                hal_rtcAlarmSet
                0x0602c184                hal_rtcAlarmSetExt
                0x0602c228                hal_rtcAlarmStatusGet
                0x0602c264                hal_rtcTrimCallBack
 .text          0x0602c284        0x0 obj\Debug\hal\src\hal_spi.o
 .text          0x0602c284      0x504 obj\Debug\hal\src\hal_spi1.o
                0x0602c284                hal_spi1DmaCallback
                0x0602c318                hal_spi1DmaDoneCheck
                0x0602c398                hal_spi1Init
                0x0602c3f0                hal_spi1SendByte
                0x0602c414                hal_spi1RecvByte
                0x0602c434                hal_spi1SendDmaKick
                0x0602c4cc                hal_spi1SendDma
                0x0602c524                hal_spi1RecvDmaKick
                0x0602c5c0                hal_spi1RecvDma
                0x0602c618                hal_spi1_test
 .text          0x0602c788      0x21c obj\Debug\hal\src\hal_stream.o
                0x0602c788                hal_streamInit
                0x0602c8e4                hal_streamMallocDrop
                0x0602c98c                hal_stream_size
 .text          0x0602c9a4      0x9ac obj\Debug\hal\src\hal_sys.o
                0x0602cca0                hal_sysMemPrint
                0x0602cdac                hal_sysMemMalloc
                0x0602cec4                hal_sysMemMallocLast
                0x0602cff0                hal_sysMemFree
                0x0602d1cc                hal_sysMemRemain
                0x0602d224                hal_sysInit
 .text          0x0602d350       0xe0 obj\Debug\hal\src\hal_timer.o
                0x0602d350                hal_timerEnable
                0x0602d388                hal_timerTickEnable
                0x0602d3bc                hal_timerPWMStart
 .text          0x0602d430      0x540 obj\Debug\hal\src\hal_uart.o
                0x0602d494                hal_uartIOShare
                0x0602d4d0                hal_uartIOShareCheck
                0x0602d51c                hal_uartInit
                0x0602d574                hal_uartSendData
                0x0602d598                hal_uartRXIsrRegister
                0x0602d5b8                uart_PutChar_n
                0x0602d5ec                uart_PutStr
                0x0602d62c                uart_Put_hex
                0x0602d758                uart_Put_udec
                0x0602d7ec                uart_Put_dec
                0x0602d894                uart_PrintfBuf
                0x0602d92c                hal_uartPrintString
 .text          0x0602d970      0xa04 obj\Debug\hal\src\hal_watermark.o
                0x0602d9d8                hal_watermarkInit
                0x0602da88                hal_watermarkClose
                0x0602db1c                hal_watermarkClear
                0x0602db9c                hal_watermarkOpen
                0x0602dc3c                hal_watermarkColor
                0x0602dc9c                hal_watermarkAddr
                0x0602dcb4                hal_watermarkSize
                0x0602dd00                hal_watermarkPosition
                0x0602dd4c                hal_watermarkCallbackRegister
                0x0602dd94                hal_watermarkRam
                0x0602df8c                hal_watermarkEnable
                0x0602e0dc                hal_jpg_watermark_init
                0x0602e16c                hal_jpg_watermark_uinit
                0x0602e1b0                hal_jpg_watermarkStart
                0x0602e304                hal_jpgB_watermarkPos_Adjust
 .text          0x0602e374        0x0 obj\Debug\hal\src\hal_wdt.o
 .text          0x0602e374        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .text          0x0602e374        0x0 obj\Debug\mcu\xos\xmbox.o
 .text          0x0602e374      0x2e4 obj\Debug\mcu\xos\xmsgq.o
                0x0602e374                XMsgQInit
                0x0602e3b4                XMsgQCreate
                0x0602e444                XMsgQDestory
                0x0602e47c                XMsgQFlush
                0x0602e4bc                XMsgQPost
                0x0602e534                XMsgQPostFront
                0x0602e5b0                XMsgQPend
                0x0602e640                XMsgQCheck
 .text          0x0602e658      0x14c obj\Debug\mcu\xos\xos.o
                0x0602e658                XOSInit
                0x0602e694                XOSTickService
                0x0602e704                XOSTimeGet
                0x0602e724                XOSTimeDly
                0x0602e784                XOSRandom
 .text          0x0602e7a4      0x168 obj\Debug\mcu\xos\xwork.o
                0x0602e7a4                XWorkInit
                0x0602e7e4                XWorkCreate
                0x0602e870                XWorkDestory
                0x0602e8a0                XWorkService
 .text          0x0602e90c      0x998 obj\Debug\multimedia\audio\audio_playback.o
                0x0602e90c                audioPlaybackInit
                0x0602e958                audioPlaybackParse
                0x0602ea48                audioPlaybackStop
                0x0602eb50                audioPlaybackUninit
                0x0602eb88                audioPlaybackStart
                0x0602ef58                audioPlaybackPause
                0x0602ef94                audioPlaybackFirstPause
                0x0602eff0                audioPlaybackResume
                0x0602f058                audioPlaybackGetStatus
                0x0602f078                audioPlaybackGetTime
                0x0602f0f4                audioPlaybackSetVolume
                0x0602f148                audioPlaybackGetVolume
                0x0602f168                audioPlaybackService
 .text          0x0602f2a4      0x528 obj\Debug\multimedia\audio\audio_record.o
                0x0602f2d8                audioRecordInit
                0x0602f3a0                audioRecordUninit
                0x0602f3f4                audioRecordStop
                0x0602f4a0                audioRecordStart
                0x0602f5dc                audioRecordPuase
                0x0602f60c                audioRecordResume
                0x0602f63c                audioRecordGetStatus
                0x0602f65c                audioRecordSetStatus
                0x0602f67c                audioRecordGetTime
                0x0602f69c                audioRecordService
 .text          0x0602f7cc      0xbf4 obj\Debug\multimedia\image\image_decode.o
                0x0602f7cc                imageDecodeSubCheck
                0x0602f948                imageDecodeStart
                0x0602ff10                imageDecodeSpiStart
                0x06030120                imageDecodeGetResolution
                0x06030144                imageDecodeDirect
 .text          0x060303c0      0x9c8 obj\Debug\multimedia\image\image_encode.o
                0x060303c0                imageEncodeInit
                0x060303ec                imageEncodeUninit
                0x06030410                imageEncodeStart
                0x06030a80                imageLcdEncodeStart
                0x06030b6c                imageEncodeToSpi
 .text          0x06030d88      0x158 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                0x06030d88                jpg_encode
 .text          0x06030ee0     0x1998 obj\Debug\multimedia\video\video_playback.o
                0x06031014                videoPlaybackClear
                0x06031078                videoPlaybackInit
                0x06031154                videoPlaybackDecodeWait
                0x060311a0                videoPlaybackStart
                0x060315b8                videoPlaybackStop
                0x060316f0                videoPlaybackUninit
                0x06031964                videoPlaybackPause
                0x060319e0                videoPlaybackResume
                0x06031a44                videoPlaybackGetStatus
                0x06031a64                videoPlaybackGetSpeed
                0x06031a84                videoPlaybackSetVolume
                0x06031ad4                videoPlaybackGetVolume
                0x06031af4                videoPlaybackGetTime
                0x06031b2c                videoPlaybabkGetArg
                0x06031b48                videoGetFirstFrame
                0x06031d24                videoDecodeFirstFrame
                0x06031e38                videoPlaybackService
                0x060326ec                videoPlaybackFastForward
                0x060327b4                videoPlaybackFastBackward
 .text          0x06032878      0xed8 obj\Debug\multimedia\video\video_record.o
                0x06032878                videoRecordInit
                0x06032938                videoRecordUninit
                0x06032984                videoRecordFileStart
                0x06032af4                videoRecordFileStop
                0x06032bbc                videoRecordFileError
                0x06032c24                videoRecordError
                0x06032c74                videoRecordStart
                0x06032d90                videoRecordGetTimeSec
                0x06032dd4                videoRecordGetStatus
                0x06032df4                videoRecordJunkSync
                0x06032e30                videoRecordStop
                0x06032ea8                videoRecordRestart
                0x06032fa4                videoRecordFrameProcess
                0x06033480                videoRecordService
                0x060335b4                videoRecordCmdSet
                0x06033660                videoRecordSizePreSec
                0x060336cc                videoRecordTakePhotoCfg
                0x060336f0                videoRecordTakePhotoStatus
                0x06033710                videoRecordSetPhotoStatus
                0x06033730                videoRecordTakePhotoFd
 .text          0x06033750     0x19f0 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
                0x06033844                filelist_listFlush
                0x06033948                filelist_api_Init
                0x06033974                filelist_api_nodecreate
                0x06033cdc                filelist_api_nodedestory
                0x06033df8                filelist_api_scan
                0x06033fc8                filenode_api_CountGet
                0x06033ff8                filelist_api_CountGet
                0x06034038                filelist_api_MaxCountGet
                0x06034068                filelist_GetFileNameByIndex
                0x060340e4                filelist_GetFileFullNameByIndex
                0x06034248                filelist_GetFileShortNameByIndex
                0x06034368                filelist_GetFileIndexByIndex
                0x06034414                filelist_findFirstFileName
                0x06034494                filelist_findFileNameByFname
                0x06034568                filelist_delFileByIndex
                0x06034680                filelist_delFileByFname
                0x06034738                filelist_listDelAll
                0x060348ac                filelist_createNewFileFullName
                0x06034914                filelist_createNewFileFullNameByFname
                0x06034954                filenode_addFileByFname
                0x0603499c                filenode_filefullnameLock
                0x06034a70                filenode_filefullnameUnlock
                0x06034b44                filenode_fnameLockByIndex
                0x06034bbc                filenode_fnameUnlockByIndex
                0x06034c38                filelist_fnameChecklockByIndex
                0x06034cc4                filenode_parentdir_get
                0x06034d6c                filelist_GetLrcFileFullNameByIndex
                0x06034e14                filelist_SpaceCheck
 .text          0x06035140      0xedc obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                0x06035140                filelist_NameChangeSufType
                0x060351a0                filenode_fname_check
                0x060351dc                filenode_fname_createNew
                0x06035340                filenode_filename_CreateByFname
                0x06035690                filenode_filefullname_CreateByFname
                0x0603570c                filenode_AddFileByFname
                0x060358c8                filenode_Scan
                0x06035e7c                filenode_api_findfirst
                0x06035f5c                filenode_api_findByFname
 .text          0x0603601c      0x200 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
                0x0603601c                res_ascii_get
                0x06036104                res_getAsciiCharSize
                0x0603613c                res_getAsciiStringSize
 .text          0x0603621c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .text          0x0603621c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .text          0x0603621c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .text          0x0603621c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .text          0x0603621c      0x65c obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
                0x0603621c                res_font_Init
                0x0603634c                res_font_SetLanguage
                0x0603641c                res_font_GetString
                0x06036534                res_font_GetChar
                0x060366fc                res_font_StringTableInit
                0x06036788                res_font_GetAddrAndSize
 .text          0x06036878      0x738 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
                0x06036910                res_iconInit
                0x06036a10                res_iconBuffInit
                0x06036a7c                res_iconGetSizeByIndex
                0x06036b30                res_iconGetAddrByIndex
                0x06036bb0                res_icon_GetAddrAndSize
                0x06036c7c                res_icon_GetTColor
                0x06036cb0                res_iconBuffTimeUpdate
                0x06036cf4                res_iconGetData
                0x06036f2c                res_iconGetPalette
 .text          0x06036fb0      0x1f0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
                0x06036fb0                res_image_show
                0x060370c8                res_image_decode
 .text          0x060371a0      0x118 obj\Debug\sys_manage\res_manage\res_manage_api.o
                0x060371a0                res_GetStringInfor
                0x06037208                res_GetCharInfor
 .text          0x060372b8      0x368 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
                0x060372b8                res_music_end
                0x06037310                res_music_start
                0x06037458                res_keysound_init
                0x0603758c                res_keysound_play
                0x060375c0                res_keysound_stop
 .text          0x06037620        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .text          0x06037620      0x1c8 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
                0x060376ec                uiButtonCreate
 .text          0x060377e8      0x2f8 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
                0x060378e4                uiCycleCreateDirect
                0x06037970                uiCycleCreate
 .text          0x06037ae0      0x1ec obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
                0x06037ae0                uiDialogCreate
                0x06037c14                uiDialogItem
 .text          0x06037ccc     0x19cc obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                0x0603825c                uiWinDrawInit
                0x060382b4                uiWinDrawUpdate
                0x06038310                uiWinDrawLine
                0x06038610                uiWinDrawRect
                0x06038854                uiWinDrawRoundRectWithRim
                0x06038bf8                uiWinDrawPoint
                0x06038c98                uiWinDrawCircle
                0x06038e2c                uiWinDrawIcon
                0x06039118                uiWinDrawString
 .text          0x06039698      0x29c obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
                0x0603981c                uiFrameWinCreate
 .text          0x06039934      0x3b0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
                0x06039b28                uiImageIconCreateDirect
                0x06039c08                uiImageIconCreate
 .text          0x06039ce4     0x18c0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
                0x06039ce4                uiItemManageCreate
                0x06039dd4                uiItemManageSetItemHeight
                0x06039f74                uiItemManageSetHeightAvgGap
                0x0603a0f4                uiItemManageSetHeightNotGap
                0x0603a230                uiItemManageSetRowSum
                0x0603a398                uiItemManageSetColumnSumWithGap
                0x0603a518                uiItemManageCreateItem
                0x0603a6c4                uiItemManageSetResInforFuncEx
                0x0603a724                uiItemManageSetCurItem
                0x0603aa04                uiItemManageUpdateRes
                0x0603aabc                uiItemManageUpdateAllItem
                0x0603ab20                uiItemManageUpdateCurItem
                0x0603acc4                uiItemManageNextItem
                0x0603ad34                uiItemManagePreItem
                0x0603ada4                uiItemManageNextPage
                0x0603ae28                uiItemManagePrePage
                0x0603aeb0                uiItemManageGetCurrentItem
                0x0603af14                uiItemManageSetCharInfor
                0x0603afcc                uiItemManageSetSelectColor
                0x0603b064                uiItemManageSetSelectImage
                0x0603b0fc                uiItemManageSetUnselectColor
                0x0603b194                uiItemManageSetUnselectImage
                0x0603b22c                uiItemManageGetTouchInfor
                0x0603b424                uiItemManageSetSelectColorEx
                0x0603b4e4                uiItemManageSetUnselectColorEx
 .text          0x0603b5a4      0x444 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
                0x0603b83c                uiItemCreateItemMenu
 .text          0x0603b9e8      0x5c4 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
                0x0603bd98                uiItemCreateMenuItemEx
 .text          0x0603bfac      0x390 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
                0x0603c1c4                uiItemCreateMenuOption
 .text          0x0603c33c      0x238 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
                0x0603c3d4                uiLineCreate
 .text          0x0603c574     0x1ccc obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
                0x0603c574                uiWinSendMsg
                0x0603c5b4                uiWinSendMsgId
                0x0603c5dc                uiWinSendMsgToParent
                0x0603c60c                uiWinSendMsgIdToParent
                0x0603c634                uiWinOverlapCmp
                0x0603c698                uiWinInsideCmp
                0x0603c6fc                uiWinStringExRowCal
                0x0603c764                uiWinStringExGetByRow
                0x0603c7c8                uiWinStringExGetNext
                0x0603c82c                uiWinInterSection
                0x0603c8ac                uiWinHasInvalidRect
                0x0603c948                uiWinFreeInvalidRect
                0x0603cc04                uiWinSetbgColor
                0x0603cc38                uiWinSetCycleRadius
                0x0603cc70                uiWinSetRoundRectRadius
                0x0603cca8                uiWinSetfgColor
                0x0603ccdc                uiWinSetVisible
                0x0603cd60                uiWinIsVisible
                0x0603cd9c                uiWinSetResid
                0x0603cdcc                uiWinSetItemSelResid
                0x0603cdfc                uiWinUpdateResId
                0x0603ce20                uiWinUpdateAllResId
                0x0603ce84                uiWinSetStrInfor
                0x0603ced0                uiResInforInit
                0x0603cf1c                uiWinSetSelectInfor
                0x0603cf4c                uiWinSetUnselectInfor
                0x0603cf7c                uiWinGetResSum
                0x0603cfbc                uiWinSetResSum
                0x0603cfec                uiWinSetResidByNum
                0x0603d03c                uiWinSetPorgressRate
                0x0603d06c                uiWinParentRedraw
                0x0603d0dc                uiWinGetRelativePos
                0x0603d16c                uiWinGetPos
                0x0603d1b0                uiWinUpdateInvalid
                0x0603d228                uiWinSetProgressRate
                0x0603d258                uiWinSetName
                0x0603d278                uiWinGetCurrent
                0x0603d298                uiWinDefaultProc
                0x0603d640                uiWinCreate
                0x0603db10                uiWinDestroy
                0x0603dcbc                uiWinGetTouchInfor
                0x0603dcec                uiWinSetTouchInfor
                0x0603dd1c                uiWinTouchProcess
                0x0603df24                uiWinDrawProcess
                0x0603e038                uiWinDestroyDeskTopChildWin
                0x0603e0ac                uiWinInit
                0x0603e1c4                uiWinUninit
 .text          0x0603e240      0x380 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
                0x0603e240                uiWinHeapInit
                0x0603e280                uiWinHeapMemInfo
                0x0603e2fc                uiWinHeapMalloc
                0x0603e3cc                uiWinHeapFree
                0x0603e47c                uiMemPoolCreate
                0x0603e4fc                uiMemPoolGet
                0x0603e544                uiMemPoolPut
                0x0603e580                uiMemPoolInfo
 .text          0x0603e5c0      0x2e4 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
                0x0603e760                uiProgressBarCreateDirect
                0x0603e7fc                uiProgressBarCreate
 .text          0x0603e8a4      0x2dc obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
                0x0603ea08                uiRectCreateDirect
                0x0603ea94                uiRectCreate
 .text          0x0603eb80      0x678 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
                0x0603ef0c                uiStringExCreateDirect
                0x0603f008                uiStringExCreate
 .text          0x0603f1f8      0x3fc obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
                0x0603f3ec                uiStringIconCreateDirect
                0x0603f4f0                uiStringIconCreate
 .text          0x0603f5f4      0x250 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
                0x0603f778                uiTipsCreate
 .text          0x0603f844      0x120 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
                0x0603f844                uiWidgetProc
                0x0603f890                uiWidgetSetType
                0x0603f8c4                uiWidgetGetType
                0x0603f8f8                uiWidgetGetId
                0x0603f930                uiWidgetSetId
 .text          0x0603f964      0x310 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
                0x0603fbd4                uiWidgetManageCreate
 .text          0x0603fc74      0x8d8 obj\Debug\app\app_common\src\app_init.o
                0x0603fc74                app_logo_show
                0x0603fcf8                app_version_get
                0x0603fd2c                app_draw_init
                0x0603fd5c                app_uninit
                0x0603ff2c                app_init
                0x0604049c                app_sendDrawUIMsg
                0x060404d0                app_draw_Service
                0x06040518                app_systemService
 .text          0x0604054c     0x107c obj\Debug\app\app_common\src\app_lcdshow.o
                0x0604054c                app_lcdVideoShowScaler_cfg
                0x06040cb8                app_lcdVideoShowRotate_cfg
                0x06040e90                app_lcdVideoShowMirro_cfg
                0x06041064                app_lcdCsiVideoShowStart
                0x060410e4                app_lcdCsiVideoLayerEnGet
                0x06041104                app_lcdCsiVideoShowStop
                0x06041178                app_lcdVideoIdleFrameGet
                0x06041198                app_lcdUiShowInit
                0x060412c4                app_lcdUiShowUinit
                0x06041314                app_lcdUiDrawIdleFrameGet
                0x06041338                app_lcdShowWinModeCfg
 .text          0x060415c8        0x0 obj\Debug\app\app_common\src\main.o
 .text.startup  0x060415c8       0x50 obj\Debug\app\app_common\src\main.o
                0x060415c8                main
 .text          0x06041618        0x0 obj\Debug\app\resource\user_res.o
 .text          0x06041618       0xf0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
                0x06041618                menuProcDelCur
                0x06041648                menuProcDelAll
                0x06041678                menuProcLockCur
                0x060416a8                menuProcUnlockCur
                0x060416d8                menuProcUnlockAll
 .text          0x06041708        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .text          0x06041708       0x90 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
                0x06041708                menuProcDateTime
                0x06041738                menuProcFormat
                0x06041768                menuProcDefault
 .text          0x06041798        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
 .text          0x06041798      0x970 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .text          0x06042108      0x450 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .text          0x06042558      0x518 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .text          0x06042a70      0x58c obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .text          0x06042ffc      0x548 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .text          0x06043544      0xef0 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x06044414                menuWinIsOpen
 .text          0x06044434      0x554 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .text          0x06044988      0x4f8 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .text          0x06044e80      0x588 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .text          0x06045408      0x564 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .text          0x0604596c        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .text          0x0604596c      0x118 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .text          0x06045a84      0x1f8 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .text          0x06045c7c      0xad4 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .text          0x06046750      0x434 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .text          0x06046b84      0x44c obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .text          0x06046fd0      0x1a8 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .text          0x06047178      0x368 obj\Debug\app\task_windows\msg_api.o
                0x06047178                taskmsgFuncRegister
                0x06047230                sysMsgFuncRegister
                0x060472ac                app_msgDeal
                0x060473e8                app_msgDealByType
                0x06047484                app_msgDealByInfor
 .text          0x060474e0      0x4bc obj\Debug\app\task_windows\task_api.o
                0x06047588                app_taskInit
                0x06047658                app_taskCurId
                0x06047678                app_taskStart
                0x06047744                app_taskChange
                0x060477c0                app_task_rec_Change
                0x0604783c                app_taskService
 .text          0x0604799c      0x550 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .text          0x06047eec     0x2118 obj\Debug\app\task_windows\task_common\src\task_common.o
                0x06047eec                task_com_para_init
                0x06047f5c                task_com_usbhost_set
                0x060481c4                task_com_usb_dev_out
                0x06048208                task_com_spijpg_Init
                0x0604821c                task_com_sdlist_scan
                0x06048230                task_com_sdc_stat_set
                0x06048328                task_com_lcdbk_set
                0x0604839c                task_com_sreen_check
                0x06048458                task_com_auto_poweroff
                0x0604850c                task_com_keysound_play
                0x06048558                task_com_sound_wait_end
                0x0604858c                task_com_sdc_freesize_check
                0x06048644                task_com_fs_scan
                0x06048780                task_com_sdc_freesize_modify
                0x06048814                task_com_ir_set
                0x06048878                task_com_powerOnTime_str
                0x0604890c                task_com_sensor_res_str
                0x060489e0                task_com_rec_show_time_str
                0x06048aa0                task_com_rec_remain_time_str
                0x06048b64                task_com_play_time_str
                0x06048d44                task_com_tips_show
                0x06048f34                task_com_LedPwm_ctrl
                0x0604906c                task_com_LedOnOff_ctrl
                0x060491bc                task_com_scaler_str
                0x060491f8                task_com_USB_CS_DM_DP_status_select
                0x06049508                task_com_service
 .text          0x0604a004       0x44 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .text          0x0604a048      0x2c0 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x0604a1ac                app_taskPlayAudio_start
 .text          0x0604a308      0xa34 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .text          0x0604ad3c     0x1464 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x0604af28                taskPlayVideoThumbnallDrawImage
                0x0604b130                taskPlayVideoSlideOpen
                0x0604b280                taskPlayVideoSlideClose
                0x0604b2d8                taskPlayVideoSlidePause
                0x0604b31c                taskPlayVideoSlideStart
                0x0604b46c                taskPlayVideoMainStart
                0x0604c144                taskPlayVideoMainScalerCfg
 .text          0x0604c1a0     0x1514 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .text          0x0604d6b4      0x3c8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .text          0x0604da7c      0xb10 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .text          0x0604e58c       0xdc obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .text          0x0604e668      0x364 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .text          0x0604e9cc      0x20c obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .text          0x0604ebd8      0x748 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x0604ecc0                taskRecordPhotoRemainCal
                0x0604ee18                app_taskRecordPhoto_callback
                0x0604f150                taskRecordPhotoProcess
 .text          0x0604f320      0xd8c obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .text          0x060500ac      0xcf0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                0x06050894                taskRecordvideoTimeCount1S
                0x06050964                app_taskRecordVideo_caltime
                0x0605099c                app_taskRecordVideo_Capture
                0x06050b50                app_taskRecordVideo_start
                0x06050c14                app_taskRecordVideo_stop
 .text          0x06050d9c     0x10fc obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .text          0x06051e98       0x20 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .text          0x06051eb8       0xd8 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                0x06051eb8                taskSdUpdate_uiInit
 .text          0x06051f90       0x68 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .text          0x06051ff8       0xe4 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .text          0x060520dc      0x1bc obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .text          0x06052298      0x1a0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .text          0x06052438      0x3a0 obj\Debug\app\task_windows\windows_api.o
                0x06052630                uiParentDealMsg
                0x06052660                uiOpenWindow
                0x060527a8                windowIsOpen
 .text          0x060527d8     0x1488 obj\Debug\app\user_config\src\mbedtls_md5.o
                0x060527d8                mbedtls_md5_init
                0x06052800                mbedtls_md5_free
                0x06052840                mbedtls_md5_clone
                0x06052864                mbedtls_md5_starts
                0x060528b4                mbedtls_md5_process
                0x06053894                mbedtls_md5_update
                0x060538c0                mbedtls_md5_finish
                0x060539fc                mbedtls_md5
                0x06053a70                check_uid_entryption
                0x06053c40                spi_flash_check_md5
 .text          0x06053c60      0x4e8 obj\Debug\app\user_config\src\user_config_api.o
                0x06053c60                user_config_set
                0x06053c98                user_config_get
                0x06053cd4                user_config_save
                0x06053d7c                userConfig_Reset
                0x06053de4                userConfig_Init
                0x06053ecc                userConfigInitial
                0x06053eec                user_configValue2Int
                0x06053f54                user_config_Language
                0x06053fbc                user_config_cfgSys
                0x0605410c                user_config_cfgSysAll
 .text          0x06054148        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .text          0x06054148        0x0 ..\lib\libboot.a(boot.o)
 .text          0x06054148        0x0 ..\lib\libboot.a(boot_loader.o)
 .text          0x06054148        0x0 ..\lib\libboot.a(reset.o)
 .text          0x06054148        0x0 ..\lib\libboot.a(boot_lib.o)
 .text          0x06054148        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .text          0x06054148      0x420 ..\lib\libmcu.a(hx330x_auadc.o)
                0x06054148                hx330x_auadcHalfIRQRegister
                0x06054168                hx330x_auadcEndIRQRegister
                0x06054188                hx330x_auadcEnable
                0x06054240                hx330x_auadcAGCEnable
                0x060542d0                hx330x_auadcGainSet
                0x06054368                hx330x_auadcBufferSet
                0x060543c8                hx330x_auadcInit
                0x060544b8                hx330x_auadcSetSampleSet
                0x06054548                hx330x_agc_pwr_get
 .text          0x06054568     0x125c ..\lib\libmcu.a(hx330x_csi.o)
                0x06054568                hx330x_CSI_LBFDMAERR_callback
                0x060545ec                hx330x_CSI_SenSizeErr_callback
                0x06054654                hx330x_csiIOConfig
                0x060546f0                hx330x_csi_reset
                0x06054734                hx330x_csiInit
                0x06054780                hx330x_csi_fcnt_mnt
                0x060547ac                hx330x_csiISRRegiser
                0x060547e8                hx330x_csiOutputSet
                0x06054824                hx330x_csiMclkSet
                0x06054900                hx330x_csiSyncSet
                0x06054964                hx330x_csiPrioritySet
                0x060549a0                hx330x_csiTypeSet
                0x06054a24                hx330x_csiModeSet
                0x06054a60                hx330x_csiModeGet
                0x06054a7c                hx330x_pclk_digital_fir_Set
                0x06054ae0                hx330x_pclk_analog_Set
                0x06054b30                hx330x_pclk_inv_Set
                0x06054b74                hx330x_csi_clk_tun_Set
                0x06054bcc                hx330x_csiSizeSet
                0x06054c04                hx330x_sen_Image_Size_Set
                0x06054c3c                hx330x_csi_in_CropSet
                0x06054cc0                hx330x_csiInputAddrSet
                0x06054cd8                hx330x_csiTestModeSet
                0x06054d38                hx330x_csiDvpClkDivSet
                0x06054d78                hx330x_csiINTSet
                0x06054db4                hx330x_csiEnable
                0x06054e44                hx330x_csiLCDScalerDoneCheck
                0x06054e6c                hx330x_csiToMjpAddrCfg
                0x06054e88                hx330x_csiMJPEGFrameSet
                0x06054f0c                hx330x_csiWifiFrameSet
                0x06054f64                hx330x_csiLCDFrameSet
                0x06054f90                hx330x_csi_YUVFrameSet
                0x06054ff0                hx330x_csiMJPEGScaler
                0x06055174                hx330x_csiMJPEGCrop
                0x06055274                hx330x_csiWifiScaler
                0x06055400                hx330x_csiSetLsawbtooth
                0x06055434                hx330x_csiLCDScaler
                0x06055548                hx330x_csiMJPEGDmaEnable
                0x060555e8                hx330x_csiWifiDmaEnable
                0x06055688                hx330x_csiLCDDmaEnable
                0x0605573c                hx330x_csiLCDDmaKick
                0x06055780                hx330x_csi_common_int_set
 .text          0x060557c4      0x4b0 ..\lib\libmcu.a(hx330x_dac.o)
                0x06055804                hx330x_dacTypeCfg
                0x06055848                hx330x_dacSampleRateSet
                0x060558b8                hx330x_dacEnable
                0x0605593c                hx330x_dacVolumeSet
                0x060559b4                hx330x_dacReset
                0x060559d8                hx330x_dacStop
                0x06055a08                hx330x_dacISRRegister
                0x06055a28                hx330x_dacHPSet
                0x06055abc                eq_coeff_init
                0x06055b2c                eq_gain_init
                0x06055b9c                hx330x_dacInit
 .text          0x06055c74      0x13c ..\lib\libmcu.a(hx330x_dma.o)
                0x06055c74                hx330x_dmaNocCfg
                0x06055ce8                hx330x_dmaNocWinA
                0x06055d10                hx330x_dmaNocWinB
                0x06055d38                hx330x_dmaNocWinDis
                0x06055d60                hx330x_dmaChannelEnable
 .text          0x06055db0      0x41c ..\lib\libmcu.a(hx330x_dmauart.o)
                0x06055db0                hx330x_DmaUart_sta_cfg
                0x06055df0                hx330x_DmaUart_con_cfg
                0x06055e30                hx330x_DmaUartIOCfg
                0x06055f88                hx330x_DmaUartInit
                0x06055ff8                hx330x_DmaUart_CallbackRegister
                0x06056018                hx330x_dmauart_sendbyte
                0x06056048                hx330x_dmauart_sendDmakick
                0x0605607c                hx330x_dmauart_sendDma
                0x060560f8                hx330x_dmauart_recvAutoDmakick
                0x06056144                hx330x_dmauart_recvBytekick
                0x06056178                hx330x_dmauart_rxOutAdrGet
                0x06056194                hx330x_dmauart_rxCntGet
                0x060561b0                hx330x_dmauart_rxFifoOut
 .text          0x060561cc      0x8e8 ..\lib\libmcu.a(hx330x_gpio.o)
                0x060561cc                hx330x_gpioSFRSet
                0x06056410                hx330x_gpioSFRGet
                0x060565cc                hx330x_gpioHystersisSet
                0x06056658                hx330x_GPIO_FUNC
                0x060566c4                hx330x_gpioCommonConfig
                0x06056758                hx330x_gpioLedInit
                0x060567e8                hx330x_gpioINTCheck
                0x06056820                hx330x_gpioINTClear
                0x06056844                hx330x_gpioINTInit
                0x06056960                hx330x_io1d1_softstart
                0x06056a1c                hx330x_io1d1_pd_enable
 .text          0x06056ab4      0xbd0 ..\lib\libmcu.a(hx330x_iic.o)
                0x06056b84                soft_iic0_sdaout
                0x06056bd0                soft_iic0_sdain
                0x06056c1c                soft_iic0_sda_set
                0x06056c50                soft_iic0_scl_set
                0x06056d04                soft_iic0_sda_get
                0x06056d34                soft_iic0_init
                0x06056d64                soft_iic0_start
                0x06056de4                soft_iic0_stop
                0x06056e64                soft_iic0_wait_ack
                0x06056f2c                hx330x_iic0Init
                0x06056fd4                hx330x_iic0Uninit
                0x06057018                hx330x_iic0Start
                0x06057058                hx330x_iic0Stop
                0x060570dc                hx330x_iic0RecvACK
                0x06057130                hx330x_iic0SendACK
                0x06057178                hx330x_iic0SendByte
                0x06057298                hx330x_iic0RecvByte
                0x060573c8                hx330x_iic1Init
                0x06057460                hx330x_iic1Uninit
                0x060574d8                hx330x_iic1Start
                0x060574f4                hx330x_iic1Stop
                0x06057554                hx330x_iic1RecvACK
                0x0605757c                hx330x_iic1SendACK
                0x060575a0                hx330x_iic1SendByte
                0x06057610                hx330x_iic1RecvByte
 .text          0x06057684      0x18c ..\lib\libmcu.a(hx330x_int.o)
                0x06057684                hx330x_int_priority
                0x060576cc                hx330x_intInit
 .text          0x06057810     0x15d4 ..\lib\libmcu.a(hx330x_isp.o)
                0x06057810                hx330x_isp_mask_tab_cfg
                0x06057848                hx330x_ispModeSet
                0x06057884                hx330x_isp_BLC_cfg
                0x060578d0                hx330x_isp_LSC_cfg
                0x06057908                hx330x_isp_DDC_cfg
                0x06057a80                hx330x_isp_AWB_GAIN_adj
                0x06057ab4                hx330x_isp_whtpnt_stat_cfg
                0x06057d20                hx330x_isp_CCM_cfg
                0x06057de4                hx330x_isp_RGB_DGAIN_adj
                0x06057e74                hx330x_isp_hist_stat_cfg
                0x06057efc                hx330x_isp_YGAMMA_cfg
                0x0605802c                hx330x_isp_ylog_ygamma_cal
                0x06058070                hx330x_isp_RGBGAMMA_cfg
                0x06058200                hx330x_isp_CH_cfg
                0x06058558                hx330x_isp_VDE_cfg
                0x0605862c                hx330x_isp_EE_cfg
                0x060589f8                hx330x_isp_CCF_cfg
                0x06058b44                hx330x_isp_SAJ_cfg
                0x06058c5c                hx330x_isp_kick_stat
                0x06058c8c                hx330x_isp_stat_en
                0x06058cd0                hx330x_isp_stat_cp_kick_st
                0x06058cf4                hx330x_isp_stat_cp_done
                0x06058d24                hx330x_isp_model_cfg
 .text          0x06058de4        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .text          0x06058de4     0x19b8 ..\lib\libmcu.a(hx330x_jpg.o)
                0x06058de4                hx330x_mjpA_EncodeISRRegister
                0x06058e04                hx330x_MJPA_EncodeLcdPreRegister
                0x06058e24                hx330x_MJPA_EncodeLcdPre_Func_call
                0x06058e58                hx330x_MJPA_EncodeLcdPre_Func_Check
                0x06058e84                hx330x_MJPA_EncodeLcdBackRegister
                0x06058ea4                hx330x_MJPA_EncodeLcdBack_Func_call
                0x06058ed8                hx330x_MJPA_EncodeLcdBack_Func_Check
                0x06058f04                hx330x_mjpB_EncodeISRRegister
                0x06058f24                hx330x_mjpA_isr_check
                0x06058f50                hx330x_mjpB_DecodeISRRegister
                0x06058f70                hx330x_mjpB_Encode_StartFunc_Check
                0x06058f9c                hx330x_mjpB_Encode_StartFunc_Reg
                0x06058fbc                hx330x_mjpB_Encode_StartFunc_call
                0x06058ff0                hx330x_mjpA_Encode_StartFunc_Check
                0x0605901c                hx330x_mjpA_Encode_StartFunc_Reg
                0x0605903c                hx330x_mjpA_Encode_StartFunc_call
                0x06059070                hx330x_mjpA_reset
                0x060590b4                hx330x_mjpB_reset
                0x06059104                hx330x_mjpA_EncodeSizeSet
                0x060591ac                hx330x_mjpA_EncodeSizeSet2
                0x06059334                hx330x_mjpA_EncodeQuilitySet
                0x06059374                hx330x_mjpA_EncodeInfoSet
                0x060593b4                hx330x_mjpA_EncodeBufferSet
                0x06059400                hx330x_mjpA_Encode_inlinebuf_init
                0x06059458                hx330x_mjpA_Encode_manual_on
                0x060594b8                hx330x_mjpA_Encode_manual_stop
                0x060594e4                hx330x_mjpA_EncodeEnable
                0x06059560                hx330x_mjpA_EncodeQadj
                0x06059628                hx330x_mjpA_EncodeInit
                0x060596d0                hx330x_mjpA_EncodeDriModeSet
                0x06059738                hx330x_cal_jASize
                0x06059768                hx330x_mjpA_Encode_check
                0x06059904                hx330x_mjpA_Flag_Clr
                0x0605991c                hx330x_mjpB_EncodeQuilitySet
                0x0605995c                hx330x_mjpB_EncodeQadj
                0x06059a24                hx330x_mjpB_Encodeinit
                0x06059ba8                hx330x_cal_jBSize
                0x06059bd8                hx330x_mjpB_Encode_inlinebuf_init
                0x06059c04                hx330x_mjpB_Encode_output_init
                0x06059c30                hx330x_mjpB_Encode_manual_stop
                0x06059c98                hx330x_mjpB_Encode_manual_start
                0x06059ce4                hx330x_mjpB_EncodeLoadAddrGet
                0x06059d00                hx330x_mjpB_as_Encode
                0x06059d2c                hx330x_mjpB_DecodeScalerCal
                0x06059dfc                hx330x_mjpB_DecodeSetSize
                0x0605a030                hx330x_mjpB_DecodeOutputSet
                0x0605a04c                hx330x_mjpB_DecodeInputSet
                0x0605a090                hx330x_mjpB_DecodeInputResume
                0x0605a0c8                hx330x_mjpB_DecodeDriSet
                0x0605a0e0                hx330x_mjpB_DecodeCompressSet
                0x0605a0f8                hx330x_mjpB_DecodeInitTable
                0x0605a128                hx330x_mjpB_yuvfmt_set
                0x0605a174                hx330x_mjpB_DecodeInit
                0x0605a2cc                hx330x_mjpB_DecodeDCTimeSet
                0x0605a37c                hx330x_mjpB_DecodeEnable
                0x0605a3ec                hx330x_mjpB_DecodeKick
                0x0605a434                hx330x_mjpB_DecodeStop
                0x0605a470                hx330x_mjpB_DecodeQDTCfg
                0x0605a4c4                hx330x_mjpB_DecodeBusyCheck
                0x0605a4f8                hx330x_mjpB_DecodeCheck
                0x0605a528                hx330x_mjpB_DecodeODma1Cfg
                0x0605a5d8                hx330x_mjpB_DecodePacket_check
                0x0605a69c                hx330x_mjpB_Decode_InResume
                0x0605a6c0                hx330x_mjpB_Decode_check
 .text          0x0605a79c      0x1c8 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                0x0605a79c                hx330x_mjpA_table_init
                0x0605a874                hx330x_mjpB_table_init
 .text          0x0605a964     0x10a0 ..\lib\libmcu.a(hx330x_lcd.o)
                0x0605a964                hx330x_lcdReset
                0x0605a978                hx330x_lcdSPIMode
                0x0605a9a4                hx330x_lcdSPIInit
                0x0605aac4                hx330x_lcdSPIUninit
                0x0605ab20                hx330x_lcdSPISendData
                0x0605ac94                hx330x_lcdMcuSendCmd
                0x0605ad08                hx330x_lcdMcuSendData
                0x0605ad64                hx330x_lcdMcuSendCmd16
                0x0605adf8                hx330x_lcdMcuSendData16
                0x0605ae8c                hx330x_lcdInit
                0x0605aed8                hx330x_lcdIRQEnable
                0x0605af20                hx330x_lcdPreLineSet
                0x0605af3c                hx330x_lcdSignalSet
                0x0605afd4                hx330x_lcdBusWidth
                0x0605b028                hx330x_lcdBusEnable
                0x0605b078                hx330x_lcdClkSet
                0x0605b094                hx330x_lcdSyncSet
                0x0605b0b8                hx330x_lcdDESignalSet
                0x0605b0dc                hx330x_lcdPositionSet
                0x0605b0f8                hx330x_lcdResolutionSet
                0x0605b114                hx330x_lcdWindowSizeSet
                0x0605b130                hx330x_lcdDataModeSet
                0x0605b174                hx330x_lcdClkNumberSet
                0x0605b18c                hx330x_lcdEndLineSet
                0x0605b1a8                hx330x_lcdPanelMode
                0x0605b1f0                hx330x_lcdEnable
                0x0605b254                hx330x_lcdTeMode
                0x0605b29c                hx330x_lcdTeCheck
                0x0605b318                hx330x_lcdISRRegister
                0x0605b35c                hx330x_lcdRGBTimimgInit
                0x0605b5ac                hx330x_lcdMCUTimimgInit
                0x0605b780                hx330x_lcdRGBIOConfig
                0x0605b8e0                hx330x_lcdMCUIOConfig
 .text          0x0605ba04      0x238 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                0x0605ba04                hx330x_rotateISRRegiser
                0x0605ba24                hx330x_rotateCheckBusy
                0x0605ba44                hx330x_rotateReset
                0x0605ba88                hx330x_rotateStart
                0x0605bba8                hx330x_rotateWaitFrameDone
                0x0605bc0c                hx330x_rotateGetSrcYAddr
 .text          0x0605bc3c      0x874 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x0605bc3c                hx330x_checkLcdShowStatus
                0x0605bc60                hx330x_lcdShowISRRegister
                0x0605bc80                hx330x_lcdshowInit
                0x0605bcfc                hx330x_lcdshowSetCritical
                0x0605bd40                hx330x_lcdSetVideoBgColor
                0x0605bd74                hx330x_lcdSetVideoBSC
                0x0605bdc0                hx330x_lcdSetVideoBrightness
                0x0605be18                hx330x_lcdVideoSetRgbWidth
                0x0605be84                hx330x_lcdVideoSetScalePara
                0x0605bed0                hx330x_lcdVideoSetScaleLine
                0x0605bf38                hx330x_lcdVideoGetYAddr
                0x0605bf68                hx330x_lcdVideoGetUVAddr
                0x0605bf98                hx330x_lcdVideoSetSize
                0x0605bfd0                hx330x_lcdvideoMemcpy
                0x0605c030                hx330x_lcdvideoEnable
                0x0605c074                hx330x_lcdVideoSetGAMA
                0x0605c120                hx330x_lcdVideo_CCM_cfg
                0x0605c208                hx330x_lcdVideo_SAJ_cfg
                0x0605c274                hx330x_lcdvideoGammaEnable
                0x0605c2b8                hx330x_lcdUiSetSize
                0x0605c304                hx330x_lcdUiSetPosition
                0x0605c348                hx330x_lcdUiSetPalette
                0x0605c3ac                hx330x_UiGetAddr
                0x0605c3fc                hx330x_lcdUiSetAlpha
                0x0605c460                hx330x_lcdUiLzoSoftCreate
 .text          0x0605c4b0      0x32c ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                0x0605c4b0                hx330x_uiLzoISRRegiser
                0x0605c4d0                hx330x_uiLzoCheckBusy
                0x0605c4f4                hx330x_uiLzoReset
                0x0605c538                hx330x_uiLzoGetOutSize
                0x0605c558                hx330x_uiLzoWaiDone
                0x0605c600                hx330x_uiLzoStart
                0x0605c700                hx330x_uiLzoKick
 .text          0x0605c7dc      0x14c ..\lib\libmcu.a(hx330x_lcdwin.o)
                0x0605c7dc                hx330x_lcdWinABConfig
                0x0605c858                hx330x_lcdWinABEnable
                0x0605c89c                hx330x_lcdWinReset
                0x0605c8dc                hx330x_lcdWinGetTopLayer
                0x0605c900                hx330x_lcdWinGetBotLayer
 .text          0x0605c928      0x118 ..\lib\libmcu.a(hx330x_md.o)
                0x0605c928                hx330x_mdEnable
                0x0605c990                hx330x_mdEnable_check
                0x0605c9b0                hx330x_mdInit
                0x0605c9f8                hx330x_mdXPos
                0x0605ca1c                hx330x_mdYPos
 .text          0x0605ca40      0x304 ..\lib\libmcu.a(hx330x_mipi.o)
                0x0605ca40                hx330x_mipiClkCfg
                0x0605ca94                hx330x_MipiCSIUinit
                0x0605caf4                hx330x_MipiCSIInit
 .text          0x0605cd44      0x860 ..\lib\libmcu.a(hx330x_misc.o)
                0x0605cd44                hx330x_sin
                0x0605cd8c                hx330x_cos
                0x0605cdb0                hx330x_abs
                0x0605cdd0                hx330x_dif_abs
                0x0605cdf4                hx330x_max
                0x0605ce18                hx330x_clip
                0x0605ce48                hx330x_str_cpy
                0x0605ceac                hx330x_str_ncpy
                0x0605cf24                hx330x_str_char
                0x0605cf6c                hx330x_str_cmp
                0x0605cff0                hx330x_str_ncmp
                0x0605d098                hx330x_str_len
                0x0605d0d4                hx330x_str_cat
                0x0605d144                hx330x_str_seek
                0x0605d1b0                hx330x_strTransform
                0x0605d204                hx330x_dec_num2str
                0x0605d244                hx330x_char2hex
                0x0605d298                hx330x_str2num
                0x0605d2f0                hx330x_hex2str
                0x0605d368                hx330x_num2str
                0x0605d3cc                hx330x_num2str_cnt
                0x0605d458                hx330x_CountToString
                0x0605d544                hx330x_str_noftcpy
                0x0605d56c                hx330x_greatest_divisor
 .text          0x0605d5a4      0xd00 ..\lib\libmcu.a(hx330x_rtc.o)
                0x0605d5a4                hx330x_rtcRamRead
                0x0605d640                hx330x_rtcRamWrite
                0x0605d6dc                hx330x_rtcSecondTrim
                0x0605d870                hx330x_rtcInit
                0x0605dac0                hx330x_rtc128K_div_cfg
                0x0605db4c                hx330x_rtc128K_trim
                0x0605dbfc                hx330x_rtcSencodEnable
                0x0605dc78                hx330x_rtcAlamEnable
                0x0605dd60                hx330x_rtcAlamSet
                0x0605dda8                hx330x_rtcGet
                0x0605ddfc                hx330x_rtc_alarm_weakup_reset
                0x0605de84                hx330x_rtcSet
                0x0605decc                hx330x_VDDGSENEnable
                0x0605df3c                hx330x_WKI0InputEnable
                0x0605dfac                hx330x_WKI1InputEnable
                0x0605e01c                hx330x_WKI1Read
                0x0605e040                hx330x_WKI0Read
                0x0605e064                hx330x_WKI0WakeupEnable
                0x0605e1a4                hx330x_rtcBatDecEnable
                0x0605e214                hx330x_rtcSenHVEnable
                0x0605e284                hx330x_rtcAlarmWakeUpFlag
 .text          0x0605e2a4      0x788 ..\lib\libmcu.a(hx330x_sd.o)
                0x0605e2a4                hx330x_sd0Init
                0x0605e3f8                hx330x_sd0Uninit
                0x0605e4cc                hx330x_sd0BusSet
                0x0605e510                hx330x_sd0Buffer
                0x0605e530                hx330x_sd0ClkSet
                0x0605e5c8                hx330x_sd1Init
                0x0605e770                hx330x_sd1Uninit
                0x0605e874                hx330x_sd1BusSet
                0x0605e8b8                hx330x_sd1WaitDAT0
                0x0605e958                hx330x_sd1GetRsp
                0x0605e974                hx330x_sd1Buffer
                0x0605e994                hx330x_sd1ClkSet
 .text          0x0605ea2c        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .text          0x0605ea2c      0x4ac ..\lib\libmcu.a(hx330x_spi1.o)
                0x0605ea2c                hx330x_spi1_pin_config
                0x0605eaac                hx330x_spi1_CS_Config
                0x0605eb08                hx330x_spi1Init
                0x0605ebb8                hx330x_spi1DMAIRQ_CallbackRegister
                0x0605ebd8                hx330x_spi1SendByte
                0x0605ec58                hx330x_spi1RecvByte
                0x0605eca0                hx330x_sp1RecvDmaKick
                0x0605ecf8                hx330x_spi1DmaDoneCheck
                0x0605ed1c                hx330x_sp1SendDmaKick
                0x0605ed78                hx330x_sp1RecvDma
                0x0605ee14                hx330x_sp1SendDma
                0x0605ee94                hx330x_sp1Enable
 .text          0x0605eed8      0x8bc ..\lib\libmcu.a(hx330x_sys.o)
                0x0605eed8                hx330x_word_memcpy
                0x0605ef48                hx330x_halfword_memcpy
                0x0605efb8                hx330x_word_memset
                0x0605f014                hx330x_mtsfr_memcpy
                0x0605f078                hx330x_mfsfr_memcpy
                0x0605f0dc                table_init_data
                0x0605f15c                hx330x_sysDcacheInit
                0x0605f1a0                hx330x_sysIcacheInit
                0x0605f1e4                hx330x_sysDcacheInvalid
                0x0605f27c                hx330x_sysSRAMClear
                0x0605f2d0                hx330x_sysBSSClear
                0x0605f334                hx330x_sysLDOSet
                0x0605f4e4                hx330x_sysReset
                0x0605f524                hx330x_mcpy0_llp
                0x0605f5b4                hx330x_sysInit
                0x0605f6e0                hx330x_sysUninit
 .text          0x0605f794      0x658 ..\lib\libmcu.a(hx330x_timer.o)
                0x0605f794                hx330x_timerISRRegister
                0x0605f7d8                hx330x_timerStart
                0x0605f924                hx330x_timerStop
                0x0605f9f4                hx330x_timerEnable
                0x0605fa80                hx330x_timerDisable
                0x0605fb24                hx330x_timerPWMStart
 .text          0x0605fdec      0x444 ..\lib\libmcu.a(hx330x_tminf.o)
                0x0605fdec                hx330x_mjpA_TimeinfoEnable
                0x0605fe44                hx330x_mjpB_TimeinfoEnable
                0x0605feb0                hx330x_mjpA_TimeinfoColor
                0x0605fef4                hx330x_mjpB_TimeinfoColor
                0x0605ff48                hx330x_mjpA_TimeinfoSize
                0x0605ffa4                hx330x_mjpB_TimeinfoSize
                0x0606002c                hx330x_mjpA_TimeinfoPos
                0x06060088                hx330x_mjpB_TimeinfoPos
                0x06060110                hx330x_mjpA_TimeinfoAddr
                0x06060154                hx330x_mjpB_TimeinfoAddr
                0x060601c4                hx330x_recfg_mjpb_tminf
 .text          0x06060230      0x224 ..\lib\libmcu.a(hx330x_uart.o)
                0x06060230                hx330x_uart0IOCfg
                0x060603b4                hx330x_uart0Init
 .text          0x06060454      0xe10 ..\lib\libmcu.a(hx330x_usb.o)
                0x06060454                hx330x_usb20_CallbackInit
                0x06060484                hx330x_usb20_CallbackRegister
                0x060604c0                hx330x_usb20_eptx_register
                0x06060558                hx330x_usb20_eprx_register
                0x060605e8                hx330x_USB20_EPTX_Flush
                0x06060614                hx330x_usb20_HighSpeed
                0x06060640                hx330x_iso20_tx
                0x06060798                hx330x_iso20_tx_kick
                0x06060854                hx330x_usb20_dev_reset
                0x060608c8                hx330x_usb20_host_speed_connect
                0x06060968                hx330x_usb20_host_reset
                0x060609d0                hx330x_usb20_dev_init
                0x06060b2c                hx330x_usb20_host_init
                0x06060d10                hx330x_usb20_uinit
                0x06060d88                get_u16softcnt
                0x06060da8                hx330x_usb11_CallbackInit
                0x06060dd8                hx330x_usb11_CallbackRegister
                0x06060e14                hx330x_usb11_host_init
                0x06060f08                hx330x_usb11_host_eprx_register
                0x06060fb4                hx330x_usb11_host_reset
                0x0606100c                hx330x_usb11_host_speed_connect
                0x06061054                hx330x_usb11_uinit
                0x0606111c                hx330x_usb20_dev_check_init
                0x06061138                hx330x_usb20_host_check_init
                0x06061154                hx330x_usb11_host_check_init
                0x06061170                hx330x_usb20_device_check
                0x060611a4                hx330x_usb20_host_check
                0x06061238                hx330x_usb11_host_check
 .text          0x06061264       0x14 ..\lib\libmcu.a(hx330x_wdt.o)
                0x06061264                hx330x_wdtTimeSet
 .text          0x06061278      0x184 ..\lib\libmcu.a(hx330x_emi.o)
                0x06061278                hx330x_emiInit
                0x06061328                hx330x_emiISRRegister
                0x0606135c                hx330x_emiKick
                0x060613b8                hx330x_emiCheckBusy
                0x060613d8                hx330x_emiCheckRXError
 .text          0x060613fc     0x1c34 ..\lib\libisp.a(hal_isp.o)
                0x06061724                hal_sensor_fps_adpt
                0x06061804                hal_isp_process
                0x06062768                hal_sensor_awb_scene_set
                0x06062858                hal_sensor_EV_set
                0x06062970                hal_isp_br_get
                0x06062990                hal_ispService
                0x06062a70                hal_isp_init
                0x06062e10                hal_SensorRegister
                0x06062ea8                hal_SensorApiGet
                0x06062ec8                hal_SensorResolutionGet
                0x06062f24                hal_SensorRotate
                0x06062f90                hal_isplog_cnt
                0x06062fe0                hal_isp_cur_yloga
 .text          0x06063030      0xd64 ..\lib\libjpg.a(hal_jpg.o)
                0x06063160                hal_mjp_enle_init
                0x06063190                hal_mjp_enle_unit
                0x06063234                hal_mjp_enle_check
                0x060637e4                hal_mjp_enle_manual_kickstart
                0x06063994                hal_mjp_enle_buf_mdf
                0x06063b08                hal_mjp_enle_manual_done
 .text          0x06063d94      0xe58 ..\lib\liblcd.a(hal_lcd.o)
                0x06063de8                hal_lcdWinUpdata
                0x06063ee4                hal_lcdVideoRotateUpdata
                0x0606402c                hal_lcdCsiShowStop
                0x060640ac                hal_lcdVideoShowFrameGet
                0x0606411c                hal_lcdVideoIdleFrameMalloc
                0x06064160                hal_lcdCsiShowStart
                0x060642c8                hal_lcdVideoSetFrameWait
                0x0606430c                hal_lcdVideoSetFrame
                0x060643f0                hal_lcdBrightnessGet
                0x06064420                hal_lcdRegister
                0x06064ae8                hal_lcdFrameEndCallBackRegister
                0x06064b30                hal_lcd_send_cmd
 .text          0x06064bec      0xe88 ..\lib\liblcd.a(hal_lcdMem.o)
                0x06064cbc                hal_lcdframes_vr_init
                0x06064e3c                hal_lcdframes_vd_init
                0x06064fcc                hal_dispframePrintf
                0x0606501c                hal_dispframeInit
                0x060657ac                hal_dispframeUinit
                0x06065838                hal_dispframeMalloc
                0x06065900                hal_lcdVideoFrameFlush
 .text          0x06065a74      0x384 ..\lib\liblcd.a(hal_lcdrotate.o)
                0x06065bfc                hal_rotateInit
                0x06065c50                hal_rotateAdd
 .text          0x06065df8      0x718 ..\lib\liblcd.a(hal_lcdUi.o)
                0x06065df8                hal_uiRotateBufMalloc
                0x06065e1c                hal_uiLzoBufMalloc
                0x06065e40                hal_uiDrawBufMalloc
                0x06065eb0                hal_lcdUiKickWait
                0x06065f0c                hal_lcdUiKick
                0x06065f98                hal_lcdUiSetAddr
                0x06066008                hal_lcdUiSetBuffer
                0x0606609c                hal_lcdUiSetBufferWaitDone
                0x060660f0                hal_lcdUiInit
                0x0606628c                hal_lcdUiSetPosition
                0x06066308                hal_lcdUiSetPalette
                0x0606635c                hal_lcdUiSetSize
                0x060663d8                hal_lcdUiSetAlpha
                0x06066454                hal_lcdUiResolutionGet
                0x060664c0                hal_lcdVideo_CCM_cfg
                0x060664e8                hal_lcdVideo_SAJ_cfg
 .text          0x06066510      0x1a8 ..\lib\liblcd.a(hal_lcdUiLzo.o)
                0x060665ac                hal_uiLzokick
                0x06066690                hal_uiLzoInit
 .text          0x060666b8      0x1ec ..\lib\liblcd.a(lcd_tab.o)
                0x060666b8                hal_lcdParaLoad
                0x0606677c                hal_lcdSetLsawbtooth
                0x06066830                hal_lcdPQToolGetInfo
 .text          0x060668a4      0xa70 ..\lib\libmultimedia.a(api_multimedia.o)
                0x06066a5c                api_multimedia_cache_init
                0x06066b58                api_multimedia_cachePreRead
                0x06066be8                api_multimedia_cacheRead
                0x06066c64                api_multimedia_init
                0x06066db4                api_multimedia_uninit
                0x06066e38                api_multimedia_start
                0x06066eb4                api_multimedia_end
                0x06066f30                api_multimedia_service
                0x06066fac                api_multimedia_addjunk
                0x06067028                api_multimedia_encodeframe
                0x060670a8                api_multimedia_decodeframe
                0x06067124                api_multimedia_gettime
                0x060671a0                api_multimedia_getsta
                0x0606721c                api_multimedia_decodefast
                0x06067298                api_multimedia_getArg
 .text          0x06067314     0x1440 ..\lib\libmultimedia.a(avi_dec.o)
 .text          0x06068754      0xed4 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .text          0x06069628      0xaa4 ..\lib\libmultimedia.a(avi_std_enc.o)
                0x06069c80                avi_stdEncidxEnd
                0x06069d74                avi_stdEncEnd
 .text          0x0606a0cc      0x958 ..\lib\libmultimedia.a(wav_dec.o)
 .text          0x0606aa24      0x414 ..\lib\libmultimedia.a(wav_enc.o)
                0x0606aa24                wav_EncEnd
 .text          0x0606ae38      0x1c8 ..\lib\libmultimedia.a(wav_pcm.o)
                0x0606ae38                pcm_encode
                0x0606aee8                pcm_decode
 .text          0x0606b000       0xb0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                0x0606b000                memcmp
 .text          0x0606b0b0      0x158 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                0x0606b0b0                memcpy
 .text          0x0606b208      0x15c D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                0x0606b208                memset
 .text          0x0606b364       0xc0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                0x0606b364                strcpy
 .text          0x0606b424      0x7c8 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                0x0606b424                __udivdi3
 .text          0x0606bbec      0x7c8 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                0x0606bbec                __umoddi3
 .text          0x0606c3b4       0xf4 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
                0x0606c3b4                __udivsi3
                0x0606c3b4                __udivsi3_internal
 .text          0x0606c4a8       0x1c D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
                0x0606c4a8                __umodsi3
 .text          0x0606c4c4        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
 *(.rodata*)
 .rodata.str1.1
                0x0606c4c4       0x1b obj\Debug\dev\battery\src\battery_api.o
 *fill*         0x0606c4df        0x1 
 .rodata        0x0606c4e0        0xc obj\Debug\dev\battery\src\battery_api.o
 .rodata.str1.1
                0x0606c4ec       0x49 obj\Debug\dev\dev_api.o
 .rodata.str1.1
                0x0606c535       0x65 obj\Debug\dev\fs\src\ff.o
                                 0x69 (size before relaxing)
 *fill*         0x0606c59a        0x2 
 .rodata.cst4   0x0606c59c        0x4 obj\Debug\dev\fs\src\ff.o
 .rodata        0x0606c5a0       0xb0 obj\Debug\dev\fs\src\ff.o
 .rodata        0x0606c650      0x3d4 obj\Debug\dev\fs\src\ffunicode.o
 .rodata.str1.1
                0x0606ca24       0xb7 obj\Debug\dev\fs\src\fs_api.o
                                 0xb8 (size before relaxing)
 .rodata.str1.1
                0x0606cadb       0x7a obj\Debug\dev\gsensor\src\gsensor_api.o
 *fill*         0x0606cb55        0x3 
 .rodata        0x0606cb58        0xc obj\Debug\dev\gsensor\src\gsensor_api.o
 .rodata        0x0606cb64       0x38 obj\Debug\dev\gsensor\src\gsensor_da380.o
                0x0606cb7c                da380
 .rodata        0x0606cb9c       0x38 obj\Debug\dev\gsensor\src\gsensor_gma301.o
                0x0606cbb4                gma301
 .rodata        0x0606cbd4       0x38 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
                0x0606cbec                sc7a30e
 .rodata.str1.1
                0x0606cc0c       0x16 obj\Debug\dev\lcd\src\lcd_api.o
 .rodata.str1.1
                0x0606cc22       0x1d obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .rodata.str1.1
                0x0606cc3f       0x6a obj\Debug\dev\nvfs\src\nvfs_api.o
 .rodata.str1.1
                0x0606cca9       0x8b obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .rodata.str1.1
                0x0606cd34      0x143 obj\Debug\dev\sd\src\sd_api.o
 *fill*         0x0606ce77        0x1 
 .rodata        0x0606ce78      0x160 obj\Debug\dev\sd\src\sd_api.o
                0x0606ce78                sd_ident_tab
 .rodata.str1.1
                0x0606cfd8       0xc3 obj\Debug\dev\sensor\src\sensor_api.o
 *fill*         0x0606d09b        0x1 
 .rodata        0x0606d09c       0x44 obj\Debug\dev\sensor\src\sensor_api.o
                0x0606d09c                user_ccf_dn_tab
                0x0606d0a8                user_ee_dn_tab
                0x0606d0c4                user_ee_sharp_tab
 .rodata        0x0606d0e0      0x758 obj\Debug\dev\sensor\src\sensor_tab.o
                0x0606d0e0                test_img_init
                0x0606d124                test_img_adpt
 .rodata.str1.1
                0x0606d838       0x57 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .rodata.str1.1
                0x0606d88f       0x47 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 *fill*         0x0606d8d6        0x2 
 .rodata        0x0606d8d8       0x1c obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                0x0606d8d8                tp_icnt81
 .rodata.str1.1
                0x0606d8f4       0xa8 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .rodata.str1.1
                0x0606d99c       0x13 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                                 0x17 (size before relaxing)
 *fill*         0x0606d9af        0x1 
 .rodata        0x0606d9b0       0x2c obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                0x0606d9c0                tp_ns2009
 .rodata.str1.1
                0x0606d9dc       0x50 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .rodata        0x0606da2c      0x100 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x0606da78                StringDescTbl
                0x0606da90                DEV_QAULIFIER_DESC_DATA
                0x0606da9c                UsbStrDescSerialNumber
                0x0606dabc                UsbStrDescProduct_1
                0x0606dad4                UsbStrDescProduct_0
                0x0606daec                UsbStrDescManufacturer
                0x0606dafc                UsbLanguageID
                0x0606db00                dusb_com_devdsc
                0x0606db14                dusb_msc_devdsc
                0x0606db28                SDK_CHIP_INF
 .rodata.str1.1
                0x0606db2c       0x4d obj\Debug\dev\usb\dusb\src\dusb_msc.o
 *fill*         0x0606db79        0x3 
 .rodata        0x0606db7c       0x40 obj\Debug\dev\usb\dusb\src\dusb_msc.o
                0x0606db7c                MscSenseCode
                0x0606db88                device_inquiry_data
 .rodata.str1.1
                0x0606dbbc        0xc obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .rodata        0x0606dbc8        0x4 obj\Debug\dev\usb\dusb\src\dusb_uac.o
                0x0606dbc8                uac_vol_tbl
 .rodata        0x0606dbcc      0x1e0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                0x0606dbe0                uvc_ctl_tab
                0x0606dd20                unit_callback
                0x0606dd70                vc_still_probe_commit_desc
                0x0606dd7c                vc_probe_commit_desc
                0x0606dd98                uvc_res_tab
 .rodata.str1.1
                0x0606ddac       0x69 obj\Debug\dev\usb\husb\src\husb_api.o
 .rodata.str1.1
                0x0606de15      0x5e4 obj\Debug\dev\usb\husb\src\husb_enum.o
                                0x5e6 (size before relaxing)
 *fill*         0x0606e3f9        0x3 
 .rodata        0x0606e3fc      0x2c0 obj\Debug\dev\usb\husb\src\husb_enum.o
                0x0606e42c                husb_uvcunit_set_hcdtrb
                0x0606e44c                husb_uvcunit_get_hcdtrb
                0x0606e46c                husb_astern_hcdtrb
                0x0606e48c                husb_uvc_switch_hcdtrb
                0x0606e50c                husb_enum_hcdtrb
 .rodata.str1.1
                0x0606e6bc       0xa8 obj\Debug\dev\usb\husb\src\husb_hub.o
 .rodata.str1.1
                0x0606e764      0x271 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .rodata.str1.1
                0x0606e9d5       0x17 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .rodata.str1.1
                0x0606e9ec      0x17b obj\Debug\dev\usb\husb\src\husb_uvc.o
                                0x198 (size before relaxing)
 .rodata.str1.1
                0x0606eb67       0xc1 obj\Debug\hal\src\hal_auadc.o
 .rodata.str1.1
                0x0606ec28       0x8c obj\Debug\hal\src\hal_csi.o
 .rodata        0x0606ecb4       0x2c obj\Debug\hal\src\hal_dac.o
 .rodata.str1.1
                0x0606ece0       0x6b obj\Debug\hal\src\hal_dmauart.o
 .rodata.str1.1
                0x0606ed4b       0x8d obj\Debug\hal\src\hal_lcdshow.o
                                 0x93 (size before relaxing)
 .rodata        0x0606edd8       0x14 obj\Debug\hal\src\hal_lcdshow.o
 .rodata.str1.1
                0x0606edec      0x193 obj\Debug\hal\src\hal_mjpAEncode.o
                                0x195 (size before relaxing)
 *fill*         0x0606ef7f        0x1 
 .rodata        0x0606ef80        0xc obj\Debug\hal\src\hal_mjpAEncode.o
                0x0606ef80                mjpegQualityTable
 .rodata.str1.1
                0x0606ef8c       0xbb obj\Debug\hal\src\hal_mjpBEncode.o
 .rodata.str1.1
                0x0606f047       0x47 obj\Debug\hal\src\hal_mjpDecode.o
 *fill*         0x0606f08e        0x2 
 .rodata        0x0606f090        0x8 obj\Debug\hal\src\hal_mjpDecode.o
 .rodata.str1.1
                0x0606f098        0xa obj\Debug\hal\src\hal_rtc.o
 *fill*         0x0606f0a2        0x2 
 .rodata        0x0606f0a4        0xc obj\Debug\hal\src\hal_rtc.o
                0x0606f0a4                monDaysTable
 .rodata.str1.1
                0x0606f0b0       0xdf obj\Debug\hal\src\hal_spi1.o
 .rodata.str1.1
                0x0606f18f       0xc7 obj\Debug\hal\src\hal_sys.o
 .rodata.str1.1
                0x0606f256       0x14 obj\Debug\hal\src\hal_timer.o
 .rodata.str1.1
                0x0606f26a       0x3a obj\Debug\hal\src\hal_watermark.o
 .rodata.str1.1
                0x0606f2a4      0x116 obj\Debug\multimedia\audio\audio_playback.o
 .rodata.str1.1
                0x0606f3ba       0x98 obj\Debug\multimedia\audio\audio_record.o
 .rodata.str1.1
                0x0606f452       0xc5 obj\Debug\multimedia\image\image_decode.o
 .rodata.str1.1
                0x0606f517      0x1a4 obj\Debug\multimedia\image\image_encode.o
 *fill*         0x0606f6bb        0x1 
 .rodata        0x0606f6bc      0x2dc obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                0x0606f6bc                exif_head
 .rodata.str1.1
                0x0606f998      0x2fe obj\Debug\multimedia\video\video_playback.o
                                0x302 (size before relaxing)
 *fill*         0x0606fc96        0x2 
 .rodata        0x0606fc98        0x8 obj\Debug\multimedia\video\video_playback.o
 .rodata.str1.1
                0x0606fca0      0x31e obj\Debug\multimedia\video\video_record.o
 .rodata.str1.1
                0x0606ffbe      0x1a6 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .rodata.str1.1
                0x06070164       0x95 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                                 0xb1 (size before relaxing)
 *fill*         0x060701f9        0x3 
 .rodata        0x060701fc      0xeb0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
                0x060701fc                ascii_num1_table
                0x06070374                ascii_num1_126
                0x06070398                ascii_num1_125
                0x060703bc                ascii_num1_124
                0x060703e0                ascii_num1_123
                0x06070404                ascii_num1_122
                0x06070428                ascii_num1_121
                0x0607044c                ascii_num1_120
                0x06070470                ascii_num1_119
                0x06070494                ascii_num1_118
                0x060704b8                ascii_num1_117
                0x060704dc                ascii_num1_116
                0x06070500                ascii_num1_115
                0x06070524                ascii_num1_114
                0x06070548                ascii_num1_113
                0x0607056c                ascii_num1_112
                0x06070590                ascii_num1_111
                0x060705b4                ascii_num1_110
                0x060705d8                ascii_num1_109
                0x060705fc                ascii_num1_108
                0x06070620                ascii_num1_107
                0x06070644                ascii_num1_106
                0x06070668                ascii_num1_105
                0x0607068c                ascii_num1_104
                0x060706b0                ascii_num1_103
                0x060706d4                ascii_num1_102
                0x060706f8                ascii_num1_101
                0x0607071c                ascii_num1_100
                0x06070740                ascii_num1_99
                0x06070764                ascii_num1_98
                0x06070788                ascii_num1_97
                0x060707ac                ascii_num1_96
                0x060707d0                ascii_num1_95
                0x060707f4                ascii_num1_94
                0x06070818                ascii_num1_93
                0x0607083c                ascii_num1_92
                0x06070860                ascii_num1_91
                0x06070884                ascii_num1_90
                0x060708a8                ascii_num1_89
                0x060708cc                ascii_num1_88
                0x060708f0                ascii_num1_87
                0x06070914                ascii_num1_86
                0x06070938                ascii_num1_85
                0x0607095c                ascii_num1_84
                0x06070980                ascii_num1_83
                0x060709a4                ascii_num1_82
                0x060709c8                ascii_num1_81
                0x060709ec                ascii_num1_80
                0x06070a10                ascii_num1_79
                0x06070a34                ascii_num1_78
                0x06070a58                ascii_num1_77
                0x06070a7c                ascii_num1_76
                0x06070aa0                ascii_num1_75
                0x06070ac4                ascii_num1_74
                0x06070ae8                ascii_num1_73
                0x06070b0c                ascii_num1_72
                0x06070b30                ascii_num1_71
                0x06070b54                ascii_num1_70
                0x06070b78                ascii_num1_69
                0x06070b9c                ascii_num1_68
                0x06070bc0                ascii_num1_67
                0x06070be4                ascii_num1_66
                0x06070c08                ascii_num1_65
                0x06070c2c                ascii_num1_64
                0x06070c50                ascii_num1_63
                0x06070c74                ascii_num1_62
                0x06070c98                ascii_num1_61
                0x06070cbc                ascii_num1_60
                0x06070ce0                ascii_num1_59
                0x06070d04                ascii_num1_58
                0x06070d28                ascii_num1_57
                0x06070d4c                ascii_num1_56
                0x06070d70                ascii_num1_55
                0x06070d94                ascii_num1_54
                0x06070db8                ascii_num1_53
                0x06070ddc                ascii_num1_52
                0x06070e00                ascii_num1_51
                0x06070e24                ascii_num1_50
                0x06070e48                ascii_num1_49
                0x06070e6c                ascii_num1_48
                0x06070e90                ascii_num1_47
                0x06070eb4                ascii_num1_46
                0x06070ed8                ascii_num1_45
                0x06070efc                ascii_num1_44
                0x06070f20                ascii_num1_43
                0x06070f44                ascii_num1_42
                0x06070f68                ascii_num1_41
                0x06070f8c                ascii_num1_40
                0x06070fb0                ascii_num1_39
                0x06070fd4                ascii_num1_38
                0x06070ff8                ascii_num1_37
                0x0607101c                ascii_num1_36
                0x06071040                ascii_num1_35
                0x06071064                ascii_num1_33
                0x06071088                ascii_num1_32
 .rodata        0x060710ac     0x1a70 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
                0x060710ac                ascii_num2_table
                0x06071224                ascii_num2_126
                0x06071268                ascii_num2_125
                0x060712ac                ascii_num2_124
                0x060712f0                ascii_num2_123
                0x06071334                ascii_num2_122
                0x06071378                ascii_num2_121
                0x060713bc                ascii_num2_120
                0x06071400                ascii_num2_119
                0x06071444                ascii_num2_118
                0x06071488                ascii_num2_117
                0x060714cc                ascii_num2_116
                0x06071510                ascii_num2_115
                0x06071554                ascii_num2_114
                0x06071598                ascii_num2_113
                0x060715dc                ascii_num2_112
                0x06071620                ascii_num2_111
                0x06071664                ascii_num2_110
                0x060716a8                ascii_num2_109
                0x060716ec                ascii_num2_108
                0x06071730                ascii_num2_107
                0x06071774                ascii_num2_106
                0x060717b8                ascii_num2_105
                0x060717fc                ascii_num2_104
                0x06071840                ascii_num2_103
                0x06071884                ascii_num2_102
                0x060718c8                ascii_num2_101
                0x0607190c                ascii_num2_100
                0x06071950                ascii_num2_99
                0x06071994                ascii_num2_98
                0x060719d8                ascii_num2_97
                0x06071a1c                ascii_num2_96
                0x06071a60                ascii_num2_95
                0x06071aa4                ascii_num2_94
                0x06071ae8                ascii_num2_93
                0x06071b2c                ascii_num2_92
                0x06071b70                ascii_num2_91
                0x06071bb4                ascii_num2_90
                0x06071bf8                ascii_num2_89
                0x06071c3c                ascii_num2_88
                0x06071c80                ascii_num2_87
                0x06071cc4                ascii_num2_86
                0x06071d08                ascii_num2_85
                0x06071d4c                ascii_num2_84
                0x06071d90                ascii_num2_83
                0x06071dd4                ascii_num2_82
                0x06071e18                ascii_num2_81
                0x06071e5c                ascii_num2_80
                0x06071ea0                ascii_num2_79
                0x06071ee4                ascii_num2_78
                0x06071f28                ascii_num2_77
                0x06071f6c                ascii_num2_76
                0x06071fb0                ascii_num2_75
                0x06071ff4                ascii_num2_74
                0x06072038                ascii_num2_73
                0x0607207c                ascii_num2_72
                0x060720c0                ascii_num2_71
                0x06072104                ascii_num2_70
                0x06072148                ascii_num2_69
                0x0607218c                ascii_num2_68
                0x060721d0                ascii_num2_67
                0x06072214                ascii_num2_66
                0x06072258                ascii_num2_65
                0x0607229c                ascii_num2_64
                0x060722e0                ascii_num2_63
                0x06072324                ascii_num2_62
                0x06072368                ascii_num2_61
                0x060723ac                ascii_num2_60
                0x060723f0                ascii_num2_59
                0x06072434                ascii_num2_58
                0x06072478                ascii_num2_57
                0x060724bc                ascii_num2_56
                0x06072500                ascii_num2_55
                0x06072544                ascii_num2_54
                0x06072588                ascii_num2_53
                0x060725cc                ascii_num2_52
                0x06072610                ascii_num2_51
                0x06072654                ascii_num2_50
                0x06072698                ascii_num2_49
                0x060726dc                ascii_num2_48
                0x06072720                ascii_num2_47
                0x06072764                ascii_num2_46
                0x060727a8                ascii_num2_45
                0x060727ec                ascii_num2_44
                0x06072830                ascii_num2_43
                0x06072874                ascii_num2_42
                0x060728b8                ascii_num2_41
                0x060728fc                ascii_num2_40
                0x06072940                ascii_num2_39
                0x06072984                ascii_num2_38
                0x060729c8                ascii_num2_37
                0x06072a0c                ascii_num2_36
                0x06072a50                ascii_num2_35
                0x06072a94                ascii_num2_33
                0x06072ad8                ascii_num2_32
 .rodata        0x06072b1c     0x5430 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
                0x06072b1c                ascii_num3_table
                0x06072c94                ascii_num3_126
                0x06072d98                ascii_num3_125
                0x06072e1c                ascii_num3_124
                0x06072ea0                ascii_num3_123
                0x06072f24                ascii_num3_122
                0x06072fe8                ascii_num3_121
                0x060730ac                ascii_num3_120
                0x06073170                ascii_num3_119
                0x06073274                ascii_num3_118
                0x06073338                ascii_num3_117
                0x0607343c                ascii_num3_116
                0x060734c0                ascii_num3_115
                0x06073584                ascii_num3_114
                0x06073608                ascii_num3_113
                0x0607370c                ascii_num3_112
                0x06073810                ascii_num3_111
                0x06073914                ascii_num3_110
                0x06073a18                ascii_num3_109
                0x06073b5c                ascii_num3_108
                0x06073be0                ascii_num3_107
                0x06073ca4                ascii_num3_106
                0x06073d28                ascii_num3_105
                0x06073dac                ascii_num3_104
                0x06073eb0                ascii_num3_103
                0x06073fb4                ascii_num3_102
                0x06074038                ascii_num3_101
                0x0607413c                ascii_num3_100
                0x06074240                ascii_num3_99
                0x06074304                ascii_num3_98
                0x06074408                ascii_num3_97
                0x0607450c                ascii_num3_96
                0x06074590                ascii_num3_95
                0x06074654                ascii_num3_94
                0x06074718                ascii_num3_93
                0x0607479c                ascii_num3_92
                0x06074820                ascii_num3_91
                0x060748a4                ascii_num3_90
                0x060749a8                ascii_num3_89
                0x06074aac                ascii_num3_88
                0x06074bb0                ascii_num3_87
                0x06074d34                ascii_num3_86
                0x06074e38                ascii_num3_85
                0x06074f3c                ascii_num3_84
                0x06075040                ascii_num3_83
                0x06075144                ascii_num3_82
                0x06075248                ascii_num3_81
                0x0607538c                ascii_num3_80
                0x06075490                ascii_num3_79
                0x060755d4                ascii_num3_78
                0x060756d8                ascii_num3_77
                0x0607581c                ascii_num3_76
                0x06075920                ascii_num3_75
                0x06075a24                ascii_num3_74
                0x06075ae8                ascii_num3_73
                0x06075b6c                ascii_num3_72
                0x06075c70                ascii_num3_71
                0x06075db4                ascii_num3_70
                0x06075eb8                ascii_num3_69
                0x06075fbc                ascii_num3_68
                0x060760c0                ascii_num3_67
                0x060761c4                ascii_num3_66
                0x060762c8                ascii_num3_65
                0x060763cc                ascii_num3_64
                0x06076550                ascii_num3_63
                0x06076654                ascii_num3_62
                0x06076758                ascii_num3_61
                0x0607685c                ascii_num3_60
                0x06076960                ascii_num3_59
                0x060769e4                ascii_num3_58
                0x06076a68                ascii_num3_57
                0x06076b6c                ascii_num3_56
                0x06076c70                ascii_num3_55
                0x06076d74                ascii_num3_54
                0x06076e78                ascii_num3_53
                0x06076f7c                ascii_num3_52
                0x06077080                ascii_num3_51
                0x06077184                ascii_num3_50
                0x06077288                ascii_num3_49
                0x0607738c                ascii_num3_48
                0x06077490                ascii_num3_47
                0x06077514                ascii_num3_46
                0x06077598                ascii_num3_45
                0x0607761c                ascii_num3_44
                0x060776a0                ascii_num3_43
                0x060777a4                ascii_num3_42
                0x06077868                ascii_num3_41
                0x060778ec                ascii_num3_40
                0x06077970                ascii_num3_39
                0x060779f4                ascii_num3_38
                0x06077af8                ascii_num3_37
                0x06077c3c                ascii_num3_36
                0x06077d40                ascii_num3_35
                0x06077e44                ascii_num3_33
                0x06077ec8                ascii_num3_32
 .rodata        0x06077f4c     0x7870 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
                0x06077f4c                ascii_num4_table
                0x060780c4                ascii_num4_126
                0x06078208                ascii_num4_125
                0x0607834c                ascii_num4_124
                0x06078490                ascii_num4_123
                0x060785d4                ascii_num4_122
                0x06078718                ascii_num4_121
                0x0607885c                ascii_num4_120
                0x060789a0                ascii_num4_119
                0x06078ae4                ascii_num4_118
                0x06078c28                ascii_num4_117
                0x06078d6c                ascii_num4_116
                0x06078eb0                ascii_num4_115
                0x06078ff4                ascii_num4_114
                0x06079138                ascii_num4_113
                0x0607927c                ascii_num4_112
                0x060793c0                ascii_num4_111
                0x06079504                ascii_num4_110
                0x06079648                ascii_num4_109
                0x0607978c                ascii_num4_108
                0x060798d0                ascii_num4_107
                0x06079a14                ascii_num4_106
                0x06079b58                ascii_num4_105
                0x06079c9c                ascii_num4_104
                0x06079de0                ascii_num4_103
                0x06079f24                ascii_num4_102
                0x0607a068                ascii_num4_101
                0x0607a1ac                ascii_num4_100
                0x0607a2f0                ascii_num4_99
                0x0607a434                ascii_num4_98
                0x0607a578                ascii_num4_97
                0x0607a6bc                ascii_num4_96
                0x0607a800                ascii_num4_95
                0x0607a944                ascii_num4_94
                0x0607aa88                ascii_num4_93
                0x0607abcc                ascii_num4_92
                0x0607ad10                ascii_num4_91
                0x0607ae54                ascii_num4_90
                0x0607af98                ascii_num4_89
                0x0607b0dc                ascii_num4_88
                0x0607b220                ascii_num4_87
                0x0607b364                ascii_num4_86
                0x0607b4a8                ascii_num4_85
                0x0607b5ec                ascii_num4_84
                0x0607b730                ascii_num4_83
                0x0607b874                ascii_num4_82
                0x0607b9b8                ascii_num4_81
                0x0607bafc                ascii_num4_80
                0x0607bc40                ascii_num4_79
                0x0607bd84                ascii_num4_78
                0x0607bec8                ascii_num4_77
                0x0607c00c                ascii_num4_76
                0x0607c150                ascii_num4_75
                0x0607c294                ascii_num4_74
                0x0607c3d8                ascii_num4_73
                0x0607c51c                ascii_num4_72
                0x0607c660                ascii_num4_71
                0x0607c7a4                ascii_num4_70
                0x0607c8e8                ascii_num4_69
                0x0607ca2c                ascii_num4_68
                0x0607cb70                ascii_num4_67
                0x0607ccb4                ascii_num4_66
                0x0607cdf8                ascii_num4_65
                0x0607cf3c                ascii_num4_64
                0x0607d080                ascii_num4_63
                0x0607d1c4                ascii_num4_62
                0x0607d308                ascii_num4_61
                0x0607d44c                ascii_num4_60
                0x0607d590                ascii_num4_59
                0x0607d6d4                ascii_num4_58
                0x0607d818                ascii_num4_57
                0x0607d95c                ascii_num4_56
                0x0607daa0                ascii_num4_55
                0x0607dbe4                ascii_num4_54
                0x0607dd28                ascii_num4_53
                0x0607de6c                ascii_num4_52
                0x0607dfb0                ascii_num4_51
                0x0607e0f4                ascii_num4_50
                0x0607e238                ascii_num4_49
                0x0607e37c                ascii_num4_48
                0x0607e4c0                ascii_num4_47
                0x0607e604                ascii_num4_46
                0x0607e748                ascii_num4_45
                0x0607e88c                ascii_num4_44
                0x0607e9d0                ascii_num4_43
                0x0607eb14                ascii_num4_42
                0x0607ec58                ascii_num4_41
                0x0607ed9c                ascii_num4_40
                0x0607eee0                ascii_num4_39
                0x0607f024                ascii_num4_38
                0x0607f168                ascii_num4_37
                0x0607f2ac                ascii_num4_36
                0x0607f3f0                ascii_num4_35
                0x0607f534                ascii_num4_33
                0x0607f678                ascii_num4_32
 .rodata.str1.1
                0x0607f7bc       0xae obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .rodata.str1.1
                0x0607f86a       0x5c obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .rodata.str1.1
                0x0607f8c6       0x1b obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .rodata.str1.1
                0x0607f8e1       0x27 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .rodata        0x0607f908      0x71c obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
                0x0607f908                res_key_music
 .rodata.str1.1
                0x06080024       0x13 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .rodata.str1.1
                0x06080037       0x2e obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 *fill*         0x06080065        0x3 
 .rodata        0x06080068       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .rodata.str1.1
                0x06080078       0x2f obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 *fill*         0x060800a7        0x1 
 .rodata        0x060800a8       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .rodata        0x060800b8       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .rodata.str1.1
                0x06080124       0x17 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 *fill*         0x0608013b        0x1 
 .rodata        0x0608013c       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .rodata        0x060801a8       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .rodata        0x06080214       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .rodata.str1.1
                0x06080280      0x198 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .rodata        0x06080418       0x1c obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .rodata.str1.1
                0x06080434       0x5b obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 *fill*         0x0608048f        0x1 
 .rodata        0x06080490       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .rodata        0x060804fc       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .rodata        0x06080568       0x64 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .rodata.str1.1
                0x060805cc      0x11c obj\Debug\app\app_common\src\app_init.o
                                0x123 (size before relaxing)
 .rodata.str1.1
                0x060806e8       0x58 obj\Debug\app\app_common\src\app_lcdshow.o
 .rodata        0x06080740       0x14 obj\Debug\app\app_common\src\app_lcdshow.o
 .rodata.str1.1
                0x06080754       0x8a obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                                 0x8e (size before relaxing)
 *fill*         0x060807de        0x2 
 .rodata        0x060807e0      0x140 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                0x06080808                dateTimeWin
 .rodata.str1.1
                0x06080920       0x46 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 *fill*         0x06080966        0x2 
 .rodata        0x06080968      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                0x06080968                defaultWin
 .rodata.str1.1
                0x06080a80       0x43 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 *fill*         0x06080ac3        0x1 
 .rodata        0x06080ac4      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                0x06080ac4                delAllWin
 .rodata.str1.1
                0x06080bdc       0x5e obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 *fill*         0x06080c3a        0x2 
 .rodata        0x06080c3c      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                0x06080c3c                delCurWin
 .rodata.str1.1
                0x06080d54       0x6c obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                                 0x6d (size before relaxing)
 .rodata        0x06080dc0      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                0x06080dc0                formatWin
 .rodata.str1.1
                0x06080ed8       0x56 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                                 0x5d (size before relaxing)
 *fill*         0x06080f2e        0x2 
 .rodata        0x06080f30       0xd8 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x06080f40                menuItemWin
 .rodata.str1.1
                0x06081008       0x46 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                                 0x65 (size before relaxing)
 *fill*         0x0608104e        0x2 
 .rodata        0x06081050       0x78 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                0x06081050                lockCurWin
 .rodata.str1.1
                0x060810c8       0x4f obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                                 0x51 (size before relaxing)
 *fill*         0x06081117        0x1 
 .rodata        0x06081118       0xc8 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                0x06081118                menuOptionWin
 .rodata.str1.1
                0x060811e0       0x5f obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                                 0x6d (size before relaxing)
 *fill*         0x0608123f        0x1 
 .rodata        0x06081240       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                0x06081240                unlockAllWin
 .rodata.str1.1
                0x060812e0       0x4c obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                                 0x6d (size before relaxing)
 .rodata        0x0608132c       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                0x0608132c                unlockCurWin
 .rodata.str1.1
                0x060813cc       0x5d obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 *fill*         0x06081429        0x3 
 .rodata        0x0608142c      0x348 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                0x0608142c                asternWin
 .rodata.str1.1
                0x06081774       0x43 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 *fill*         0x060817b7        0x1 
 .rodata        0x060817b8       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                0x060817b8                noFileWin
 .rodata.str1.1
                0x06081830      0x11a obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                                0x17b (size before relaxing)
 *fill*         0x0608194a        0x2 
 .rodata        0x0608194c      0x4e8 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                0x060819d4                selfTestWin
 .rodata.str1.1
                0x06081e34       0x40 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                                 0x42 (size before relaxing)
 .rodata        0x06081e74       0xa0 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                0x06081e74                tips1Win
 .rodata.str1.1
                0x06081f14       0x46 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                                 0x48 (size before relaxing)
 *fill*         0x06081f5a        0x2 
 .rodata        0x06081f5c       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                0x06081f5c                tipsWin
 .rodata.str1.1
                0x06081fd4       0x43 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 *fill*         0x06082017        0x1 
 .rodata        0x06082018       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                0x06082018                TpIconWin
 .rodata.str1.1
                0x06082090      0x148 obj\Debug\app\task_windows\task_api.o
 .rodata        0x060821d8       0x28 obj\Debug\app\task_windows\task_api.o
 .rodata.str1.1
                0x06082200       0x73 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 *fill*         0x06082273        0x1 
 .rodata        0x06082274       0x68 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
                0x06082274                sysComMsgDeal
 .rodata.str1.1
                0x060822dc      0x25a obj\Debug\app\task_windows\task_common\src\task_common.o
                                0x266 (size before relaxing)
 *fill*         0x06082536        0x2 
 .rodata        0x06082538       0x90 obj\Debug\app\task_windows\task_common\src\task_common.o
 .rodata.str1.1
                0x060825c8       0x15 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 *fill*         0x060825dd        0x3 
 .rodata        0x060825e0       0x10 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
                0x060825e0                taskComMsgDeal
 .rodata.str1.1
                0x060825f0       0x72 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .rodata.str1.1
                0x06082662       0x63 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                                 0x65 (size before relaxing)
 *fill*         0x060826c5        0x3 
 .rodata        0x060826c8      0x128 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                0x060826d8                playAudioWin
 .rodata.str1.1
                0x060827f0      0x211 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .rodata.str1.1
                0x06082a01      0x124 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                                0x156 (size before relaxing)
 *fill*         0x06082b25        0x3 
 .rodata        0x06082b28      0x218 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                0x06082b38                playVideoMainWin
 .rodata.str1.1
                0x06082d40       0x5b obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                                 0x5d (size before relaxing)
 *fill*         0x06082d9b        0x1 
 .rodata        0x06082d9c       0xc8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                0x06082d9c                playVideoSlideWin
 .rodata.str1.1
                0x06082e64       0x8d obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                                 0x97 (size before relaxing)
 *fill*         0x06082ef1        0x3 
 .rodata        0x06082ef4      0x4d4 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                0x06082ef4                playVideoThumbnallWin
 .rodata.str1.1
                0x060833c8        0xa obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .rodata.str1.1
                0x060833d2       0xb1 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .rodata.str1.1
                0x06083483        0x9 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                                 0x16 (size before relaxing)
 .rodata        0x0608348c       0xa0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                0x0608348c                RecordAudioWin
 .rodata.str1.1
                0x0608352c      0x17d obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .rodata.str1.1
                0x060836a9       0xe6 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                                0x11f (size before relaxing)
 *fill*         0x0608378f        0x1 
 .rodata        0x06083790       0x10 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .rodata.str1.1
                0x060837a0      0x315 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                                0x321 (size before relaxing)
 .rodata.str1.1
                0x06083ab5       0xaf obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                                 0xfc (size before relaxing)
 .rodata        0x06083b64      0x1f0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                0x06083b74                recordVideoWin
 .rodata.str1.1
                0x06083d54      0x147 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                                0x159 (size before relaxing)
 .rodata.str1.1
                0x06083e9b        0xa obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .rodata.str1.1
                0x06083ea5       0x17 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .rodata        0x06083ebc       0x50 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                0x06083ebc                ShowLogoWin
 .rodata.str1.1
                0x06083f0c       0x45 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                                 0x51 (size before relaxing)
 .rodata.str1.1
                0x06083f51       0x8d obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 *fill*         0x06083fde        0x2 
 .rodata        0x06083fe0      0x230 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                0x06083fe0                usbDeviceWin
 .rodata.str1.1
                0x06084210      0x13b obj\Debug\app\task_windows\windows_api.o
                                0x155 (size before relaxing)
 *fill*         0x0608434b        0x1 
 .rodata        0x0608434c       0x1c obj\Debug\app\task_windows\windows_api.o
 .rodata.str1.1
                0x06084368       0x72 obj\Debug\app\user_config\src\mbedtls_md5.o
                                 0x75 (size before relaxing)
 *fill*         0x060843da        0x2 
 .rodata        0x060843dc       0x40 obj\Debug\app\user_config\src\mbedtls_md5.o
 .rodata.str1.1
                0x0608441c       0x81 obj\Debug\app\user_config\src\user_config_api.o
 *fill*         0x0608449d        0x3 
 .rodata        0x060844a0      0x28c obj\Debug\app\user_config\src\user_config_api.o
 .rodata        0x0608472c      0x100 obj\Debug\app\user_config\src\user_config_tab.o
                0x0608472c                user_cfg_tab
 .rodata        0x0608482c       0xd0 ..\lib\libmcu.a(hx330x_auadc.o)
                0x0608482c                tbl_micvol
                0x0608486c                auadc_samplerate_tab
 .rodata.str1.1
                0x060848fc        0xe ..\lib\libmcu.a(hx330x_csi.o)
 *fill*         0x0608490a        0x2 
 .rodata        0x0608490c      0x154 ..\lib\libmcu.a(hx330x_csi.o)
                0x0608490c                csi_dvp_map_tab
                0x06084920                csi_dvp_map4
                0x06084960                csi_dvp_map3
                0x060849a0                csi_dvp_map2
                0x060849e0                csi_dvp_map1
                0x06084a20                csi_dvp_map0
 .rodata        0x06084a60       0xac ..\lib\libmcu.a(hx330x_dac.o)
                0x06084a60                gain
                0x06084a74                eq_coeff
                0x06084aac                hx330x_dacInit_table
 .rodata        0x06084b0c      0x210 ..\lib\libmcu.a(hx330x_dma.o)
                0x06084b0c                winDis_cfg
                0x06084bbc                winB_cfg
                0x06084c6c                winA_cfg
 .rodata        0x06084d1c       0xd0 ..\lib\libmcu.a(hx330x_dmauart.o)
                0x06084d1c                uart1_IO_MAP_tab
 .rodata        0x06084dec       0xb8 ..\lib\libmcu.a(hx330x_gpio.o)
 .rodata        0x06084ea4      0x4a4 ..\lib\libmcu.a(hx330x_iic.o)
                0x06084ea4                iic1_uinit_map_tab
                0x06084ec0                iic1_init_map_tab
                0x06084edc                iic1_uinit_map6
                0x06084f04                iic1_init_map6
                0x06084f50                iic1_uinit_map5
                0x06084f78                iic1_init_map5
                0x06084fc4                iic1_uinit_map4
                0x06084fec                iic1_init_map4
                0x06085038                iic1_uinit_map3
                0x06085060                iic1_init_map3
                0x060850ac                iic1_uinit_map2
                0x060850d4                iic1_init_map2
                0x06085120                iic1_uinit_map1
                0x06085148                iic1_init_map1
                0x06085194                iic1_uinit_map0
                0x060851bc                iic1_init_map0
                0x06085208                iic0_init_map_tab
                0x06085218                iic0_init_map3
                0x06085264                iic0_init_map2
                0x060852b0                iic0_init_map1
                0x060852fc                iic0_init_map0
 .rodata        0x06085348       0xf0 ..\lib\libmcu.a(hx330x_int.o)
 .rodata        0x06085438      0x14c ..\lib\libmcu.a(hx330x_isp_tab.o)
                0x06085438                Ratio_of_Evstep
                0x06085540                GAOS3X3_TAB
                0x0608554c                GAOS5X5_TAB
                0x06085568                LOG_TAB
 .rodata        0x06085584       0x14 ..\lib\libmcu.a(hx330x_jpg.o)
 .rodata.str1.1
                0x06085598       0x16 ..\lib\libmcu.a(hx330x_jpg.o)
 *fill*         0x060855ae        0x2 
 .rodata        0x060855b0      0xda0 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                0x06085ff0                c_table_chroma
                0x06086030                belta_table_chroma
                0x06086070                alpha_table_chroma
                0x060860b0                c_table_luma
                0x060860f0                belta_table_luma
                0x06086130                alpha_table_luma
                0x06086170                bic_coef_tabR
                0x06086210                bic_coef_tabL
                0x060862b0                bic_coef_tab
 .rodata        0x06086350      0x11c ..\lib\libmcu.a(hx330x_lcd.o)
 .rodata        0x0608646c       0xa0 ..\lib\libmcu.a(hx330x_lcdui.o)
 .rodata        0x0608650c      0x168 ..\lib\libmcu.a(hx330x_misc.o)
 .rodata        0x06086674      0x248 ..\lib\libmcu.a(hx330x_spi1.o)
                0x06086674                SPI1_CS_MAP_tab
                0x0608669c                spi1PinCfg_tab
                0x060866bc                SPI1_2LINE_Pos3_tab
                0x060866fc                SPI1_3LINE_Pos3_tab
                0x0608673c                SPI1_2LINE_Pos2_tab
                0x0608677c                SPI1_3LINE_Pos2_tab
                0x060867bc                SPI1_2LINE_Pos1_tab
                0x060867fc                SPI1_3LINE_Pos1_tab
                0x0608683c                SPI1_2LINE_Pos0_tab
                0x0608687c                SPI1_3LINE_Pos0_tab
 .rodata        0x060868bc       0x18 ..\lib\libmcu.a(hx330x_sys.o)
 .rodata        0x060868d4      0x100 ..\lib\libmcu.a(hx330x_timer.o)
                0x060868d4                timer3_PWM_IO_MAP_tab
                0x06086904                timer2_PWM_IO_MAP_tab
                0x0608693c                timer1_PWM_IO_MAP_tab
                0x0608697c                timer0_PWM_IO_MAP_tab
 .rodata        0x060869d4       0xd8 ..\lib\libmcu.a(hx330x_uart.o)
                0x060869d4                uart0_IO_MAP_tab
 .rodata.cst4   0x06086aac        0x8 ..\lib\libmcu.a(hx330x_usb.o)
 .rodata        0x06086ab4       0x68 ..\lib\libmcu.a(hx330x_emi.o)
                0x06086ab4                hx330x_emiPinConfigSlave_table
                0x06086adc                hx330x_emiPinConfigMaster_table
 .rodata        0x06086b1c       0x44 ..\lib\libisp.a(hal_isp.o)
 .rodata.str1.1
                0x06086b60       0x12 ..\lib\libisp.a(hal_isp.o)
 .rodata.str1.1
                0x06086b72      0x12f ..\lib\libjpg.a(hal_jpg.o)
 .rodata.str1.1
                0x06086ca1       0x36 ..\lib\liblcd.a(hal_lcd.o)
 .rodata.str1.1
                0x06086cd7      0x174 ..\lib\liblcd.a(hal_lcdMem.o)
 .rodata.str1.1
                0x06086e4b       0x11 ..\lib\liblcd.a(hal_lcdrotate.o)
 .rodata.str1.1
                0x06086e5c       0x12 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 *fill*         0x06086e6e        0x2 
 .rodata        0x06086e70       0x20 ..\lib\libmultimedia.a(api_multimedia.o)
 .rodata.str1.1
                0x06086e90       0x81 ..\lib\libmultimedia.a(avi_dec.o)
 *fill*         0x06086f11        0x3 
 .rodata        0x06086f14       0x30 ..\lib\libmultimedia.a(avi_dec.o)
                0x06086f14                avi_dec_func
 .rodata.str1.1
                0x06086f44       0x78 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .rodata        0x06086fbc     0xe830 ..\lib\libmultimedia.a(avi_odml_enc.o)
                0x06086fbc                avi_odml_enc_func
                0x06086fec                avi_odml_header
 .rodata.str1.1
                0x060957ec       0x5f ..\lib\libmultimedia.a(avi_std_enc.o)
 *fill*         0x0609584b        0x1 
 .rodata        0x0609584c      0x218 ..\lib\libmultimedia.a(avi_std_enc.o)
                0x0609584c                avi_std_enc_func
 .rodata.str1.1
                0x06095a64       0x3c ..\lib\libmultimedia.a(wav_dec.o)
 .rodata        0x06095aa0       0x30 ..\lib\libmultimedia.a(wav_dec.o)
                0x06095aa0                wav_dec_func
 .rodata.str1.1
                0x06095ad0       0x24 ..\lib\libmultimedia.a(wav_enc.o)
 .rodata        0x06095af4       0x30 ..\lib\libmultimedia.a(wav_enc.o)
                0x06095af4                wav_enc_func
 .rodata        0x06095b24       0x2c ..\lib\libmultimedia.a(wav_pcm.o)
                0x06095b24                wav_pcm_head
 .rodata        0x06095b50      0x100 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                0x06095b50                __clz_tab

.lcd_resource   0x00000000     0x29bc load address 0x00095e00
 *(.lcd_res.struct)
 .lcd_res.struct
                0x00000000       0xac obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
                0x00000000                __lcd_desc
 *(.lcd_res*)
 .lcd_res       0x000000ac      0x110 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .lcd_res       0x000001bc     0x2800 ..\lib\liblcd.a(lcd_tab.o)
                0x000004bc                lcd_contra_tab
                0x000011bc                lcd_gamma

.sensor_resource
                0x00000000     0x3640 load address 0x000987bc
 *(.sensor_res.header)
 .sensor_res.header
                0x00000000       0x40 obj\Debug\dev\sensor\src\sensor_tab.o
                0x00000000                RES_SensorHeader
                0x00000040                _res_sensor_header_item_start = .
 *(.sensor_res.header.items)
                0x00000040                _res_sensor_header_item_end = .
 *(.sensor_res.isp_tab)
 .sensor_res.isp_tab
                0x00000040     0x3600 obj\Debug\dev\sensor\src\sensor_tab.o
                0x00000040                sensor_rgb_gamma
                0x00001240                sensor_ygamma_tab
 *(.sensor_res.struct)
 *(.sensor_res.init_tab)

.nes_resource   0x00000000        0x0 load address 0x0009be00
 *(.nes_games_tab)

.eh_frame       0x00003640    0x104e8 load address 0x0009bdfc
 *(.eh_frame)
 .eh_frame      0x00003640       0x54 obj\Debug\dev\battery\src\battery_api.o
 .eh_frame      0x00003694       0x80 obj\Debug\dev\dev_api.o
                                 0x94 (size before relaxing)
 .eh_frame      0x00003714       0xa8 obj\Debug\dev\fs\src\diskio.o
                                 0xbc (size before relaxing)
 .eh_frame      0x000037bc      0x914 obj\Debug\dev\fs\src\ff.o
                                0x928 (size before relaxing)
 .eh_frame      0x000040d0       0x54 obj\Debug\dev\fs\src\ffunicode.o
                                 0x68 (size before relaxing)
 .eh_frame      0x00004124      0x2ac obj\Debug\dev\fs\src\fs_api.o
                                0x2c0 (size before relaxing)
 .eh_frame      0x000043d0       0x94 obj\Debug\dev\gsensor\src\gsensor_api.o
                                 0xa8 (size before relaxing)
 .eh_frame      0x00004464       0x80 obj\Debug\dev\gsensor\src\gsensor_da380.o
                                 0x94 (size before relaxing)
 .eh_frame      0x000044e4       0x80 obj\Debug\dev\gsensor\src\gsensor_gma301.o
                                 0x94 (size before relaxing)
 .eh_frame      0x00004564       0x7c obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
                                 0x90 (size before relaxing)
 .eh_frame      0x000045e0       0x3c obj\Debug\dev\ir\src\ir_api.o
                                 0x50 (size before relaxing)
 .eh_frame      0x0000461c       0x8c obj\Debug\dev\key\src\key_api.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x000046a8       0x8c obj\Debug\dev\lcd\src\lcd_api.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x00004734       0x38 obj\Debug\dev\led\src\led_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000476c       0x38 obj\Debug\dev\led_pwm\src\led_pwm_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x000047a4       0xc8 obj\Debug\dev\nvfs\src\nvfs_api.o
                                 0xdc (size before relaxing)
 .eh_frame      0x0000486c      0x430 obj\Debug\dev\nvfs\src\nvfs_jpg.o
                                0x444 (size before relaxing)
 .eh_frame      0x00004c9c      0x3f8 obj\Debug\dev\sd\src\sd_api.o
                                0x40c (size before relaxing)
 .eh_frame      0x00005094       0xe0 obj\Debug\dev\sensor\src\sensor_api.o
                                 0xf4 (size before relaxing)
 .eh_frame      0x00005174       0x64 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                                 0x78 (size before relaxing)
 .eh_frame      0x000051d8       0x58 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                                 0x6c (size before relaxing)
 .eh_frame      0x00005230      0x138 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
                                0x14c (size before relaxing)
 .eh_frame      0x00005368       0x78 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                                 0x8c (size before relaxing)
 .eh_frame      0x000053e0      0x110 obj\Debug\dev\usb\dusb\src\dusb_api.o
                                0x124 (size before relaxing)
 .eh_frame      0x000054f0      0x12c obj\Debug\dev\usb\dusb\src\dusb_enum.o
                                0x140 (size before relaxing)
 .eh_frame      0x0000561c      0x27c obj\Debug\dev\usb\dusb\src\dusb_msc.o
                                0x290 (size before relaxing)
 .eh_frame      0x00005898      0x12c obj\Debug\dev\usb\dusb\src\dusb_uac.o
                                0x140 (size before relaxing)
 .eh_frame      0x000059c4      0x1ec obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                                0x200 (size before relaxing)
 .eh_frame      0x00005bb0      0x1c0 obj\Debug\dev\usb\husb\src\husb_api.o
                                0x1d4 (size before relaxing)
 .eh_frame      0x00005d70      0x668 obj\Debug\dev\usb\husb\src\husb_enum.o
                                0x67c (size before relaxing)
 .eh_frame      0x000063d8       0x60 obj\Debug\dev\usb\husb\src\husb_hub.o
                                 0x74 (size before relaxing)
 .eh_frame      0x00006438      0x264 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
                                0x278 (size before relaxing)
 .eh_frame      0x0000669c      0x248 obj\Debug\dev\usb\husb\src\husb_usensor.o
                                0x25c (size before relaxing)
 .eh_frame      0x000068e4      0x284 obj\Debug\dev\usb\husb\src\husb_uvc.o
                                0x298 (size before relaxing)
 .eh_frame      0x00006b68       0x74 obj\Debug\hal\src\hal_adc.o
                                 0x88 (size before relaxing)
 .eh_frame      0x00006bdc      0x1ec obj\Debug\hal\src\hal_auadc.o
                                0x200 (size before relaxing)
 .eh_frame      0x00006dc8       0xa4 obj\Debug\hal\src\hal_csi.o
                                 0xb8 (size before relaxing)
 .eh_frame      0x00006e6c       0xc4 obj\Debug\hal\src\hal_dac.o
                                 0xd8 (size before relaxing)
 .eh_frame      0x00006f30       0xd8 obj\Debug\hal\src\hal_dmauart.o
                                 0xec (size before relaxing)
 .eh_frame      0x00007008       0x6c obj\Debug\hal\src\hal_gpio.o
                                 0x80 (size before relaxing)
 .eh_frame      0x00007074      0x2c8 obj\Debug\hal\src\hal_iic.o
                                0x2dc (size before relaxing)
 .eh_frame      0x0000733c       0x1c obj\Debug\hal\src\hal_int.o
                                 0x30 (size before relaxing)
 .eh_frame      0x00007358      0x474 obj\Debug\hal\src\hal_lcdshow.o
                                0x488 (size before relaxing)
 .eh_frame      0x000077cc       0x68 obj\Debug\hal\src\hal_md.o
                                 0x7c (size before relaxing)
 .eh_frame      0x00007834      0x40c obj\Debug\hal\src\hal_mjpAEncode.o
                                0x420 (size before relaxing)
 .eh_frame      0x00007c40      0x208 obj\Debug\hal\src\hal_mjpBEncode.o
                                0x21c (size before relaxing)
 .eh_frame      0x00007e48      0x274 obj\Debug\hal\src\hal_mjpDecode.o
                                0x288 (size before relaxing)
 .eh_frame      0x000080bc      0x248 obj\Debug\hal\src\hal_rtc.o
                                0x25c (size before relaxing)
 .eh_frame      0x00008304      0x27c obj\Debug\hal\src\hal_spi.o
                                0x290 (size before relaxing)
 .eh_frame      0x00008580      0x13c obj\Debug\hal\src\hal_spi1.o
                                0x150 (size before relaxing)
 .eh_frame      0x000086bc       0xe8 obj\Debug\hal\src\hal_stream.o
                                 0xfc (size before relaxing)
 .eh_frame      0x000087a4      0x178 obj\Debug\hal\src\hal_sys.o
                                0x18c (size before relaxing)
 .eh_frame      0x0000891c       0x78 obj\Debug\hal\src\hal_timer.o
                                 0x8c (size before relaxing)
 .eh_frame      0x00008994      0x1b0 obj\Debug\hal\src\hal_uart.o
                                0x1c4 (size before relaxing)
 .eh_frame      0x00008b44      0x1fc obj\Debug\hal\src\hal_watermark.o
                                0x210 (size before relaxing)
 .eh_frame      0x00008d40       0xd4 obj\Debug\mcu\xos\xmsgq.o
                                 0xe8 (size before relaxing)
 .eh_frame      0x00008e14       0x88 obj\Debug\mcu\xos\xos.o
                                 0x9c (size before relaxing)
 .eh_frame      0x00008e9c       0x70 obj\Debug\mcu\xos\xwork.o
                                 0x84 (size before relaxing)
 .eh_frame      0x00008f0c      0x19c obj\Debug\multimedia\audio\audio_playback.o
                                0x1b0 (size before relaxing)
 .eh_frame      0x000090a8      0x138 obj\Debug\multimedia\audio\audio_record.o
                                0x14c (size before relaxing)
 .eh_frame      0x000091e0       0xb4 obj\Debug\multimedia\image\image_decode.o
                                 0xc8 (size before relaxing)
 .eh_frame      0x00009294       0xa4 obj\Debug\multimedia\image\image_encode.o
                                 0xb8 (size before relaxing)
 .eh_frame      0x00009338       0x20 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                                 0x34 (size before relaxing)
 .eh_frame      0x00009358      0x2b8 obj\Debug\multimedia\video\video_playback.o
                                0x2cc (size before relaxing)
 .eh_frame      0x00009610      0x230 obj\Debug\multimedia\video\video_record.o
                                0x244 (size before relaxing)
 .eh_frame      0x00009840      0x3c4 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
                                0x3d8 (size before relaxing)
 .eh_frame      0x00009c04      0x134 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                                0x148 (size before relaxing)
 .eh_frame      0x00009d38       0x60 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
                                 0x74 (size before relaxing)
 .eh_frame      0x00009d98       0xc4 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
                                 0xd8 (size before relaxing)
 .eh_frame      0x00009e5c      0x144 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
                                0x158 (size before relaxing)
 .eh_frame      0x00009fa0       0x3c obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
                                 0x50 (size before relaxing)
 .eh_frame      0x00009fdc       0x38 obj\Debug\sys_manage\res_manage\res_manage_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000a014       0x9c obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000a0b0       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a0f0       0x5c obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
                                 0x70 (size before relaxing)
 .eh_frame      0x0000a14c       0x44 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
                                 0x58 (size before relaxing)
 .eh_frame      0x0000a190      0x1c8 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                                0x1dc (size before relaxing)
 .eh_frame      0x0000a358       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a398       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000a3f8      0x358 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
                                0x36c (size before relaxing)
 .eh_frame      0x0000a750       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a79c       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a7e8       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a834       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a874      0x588 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
                                0x59c (size before relaxing)
 .eh_frame      0x0000adfc       0xd8 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000aed4       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000af34       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000af94       0x70 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
                                 0x84 (size before relaxing)
 .eh_frame      0x0000b004       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000b064       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000b0a4       0x80 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
                                 0x94 (size before relaxing)
 .eh_frame      0x0000b124       0x44 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
                                 0x58 (size before relaxing)
 .eh_frame      0x0000b168       0xec obj\Debug\app\app_common\src\app_init.o
                                0x100 (size before relaxing)
 .eh_frame      0x0000b254      0x158 obj\Debug\app\app_common\src\app_lcdshow.o
                                0x16c (size before relaxing)
 .eh_frame      0x0000b3ac       0x1c obj\Debug\app\app_common\src\main.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000b3c8       0x8c obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x0000b454       0x54 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
                                 0x68 (size before relaxing)
 .eh_frame      0x0000b4a8      0x17c obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                                0x190 (size before relaxing)
 .eh_frame      0x0000b624      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000b758      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000b88c      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000b9c4      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000baf8      0x2dc obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                                0x2f0 (size before relaxing)
 .eh_frame      0x0000bdd4      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000bf0c      0x150 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                                0x164 (size before relaxing)
 .eh_frame      0x0000c05c      0x13c obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                                0x150 (size before relaxing)
 .eh_frame      0x0000c198      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000c2d0       0x70 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                                 0x84 (size before relaxing)
 .eh_frame      0x0000c340       0xc8 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                                 0xdc (size before relaxing)
 .eh_frame      0x0000c408      0x194 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                                0x1a8 (size before relaxing)
 .eh_frame      0x0000c59c      0x170 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                                0x184 (size before relaxing)
 .eh_frame      0x0000c70c      0x170 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                                0x184 (size before relaxing)
 .eh_frame      0x0000c87c       0xe0 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                                 0xf4 (size before relaxing)
 .eh_frame      0x0000c95c       0x98 obj\Debug\app\task_windows\msg_api.o
                                 0xac (size before relaxing)
 .eh_frame      0x0000c9f4       0xcc obj\Debug\app\task_windows\task_api.o
                                 0xe0 (size before relaxing)
 .eh_frame      0x0000cac0       0xf4 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
                                0x108 (size before relaxing)
 .eh_frame      0x0000cbb4      0x34c obj\Debug\app\task_windows\task_common\src\task_common.o
                                0x360 (size before relaxing)
 .eh_frame      0x0000cf00       0x1c obj\Debug\app\task_windows\task_common\src\task_common_msg.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000cf1c       0x74 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                                 0x88 (size before relaxing)
 .eh_frame      0x0000cf90      0x200 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                                0x214 (size before relaxing)
 .eh_frame      0x0000d190      0x180 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                                0x194 (size before relaxing)
 .eh_frame      0x0000d310      0x36c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                                0x380 (size before relaxing)
 .eh_frame      0x0000d67c      0x194 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                                0x1a8 (size before relaxing)
 .eh_frame      0x0000d810      0x20c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                                0x220 (size before relaxing)
 .eh_frame      0x0000da1c       0x20 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
                                 0x34 (size before relaxing)
 .eh_frame      0x0000da3c       0x7c obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
                                 0x90 (size before relaxing)
 .eh_frame      0x0000dab8       0xd8 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000db90       0x9c obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000dc2c      0x318 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                                0x32c (size before relaxing)
 .eh_frame      0x0000df44      0x134 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000e078      0x45c obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                                0x470 (size before relaxing)
 .eh_frame      0x0000e4d4       0x84 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                                 0x98 (size before relaxing)
 .eh_frame      0x0000e558       0x9c obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000e5f4       0x38 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000e62c       0x54 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                                 0x68 (size before relaxing)
 .eh_frame      0x0000e680       0x58 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                                 0x6c (size before relaxing)
 .eh_frame      0x0000e6d8       0xd8 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000e7b0       0x78 obj\Debug\app\task_windows\windows_api.o
                                 0x8c (size before relaxing)
 .eh_frame      0x0000e828      0x154 obj\Debug\app\user_config\src\mbedtls_md5.o
                                0x168 (size before relaxing)
 .eh_frame      0x0000e97c      0x120 obj\Debug\app\user_config\src\user_config_api.o
                                0x134 (size before relaxing)
 .eh_frame      0x0000ea9c      0x1d0 ..\lib\libboot.a(boot.o)
                                0x1e4 (size before relaxing)
 .eh_frame      0x0000ec6c       0x78 ..\lib\libboot.a(boot_lib.o)
                                 0x8c (size before relaxing)
 .eh_frame      0x0000ece4       0x68 ..\lib\libmcu.a(hx330x_adc.o)
                                 0x7c (size before relaxing)
 .eh_frame      0x0000ed4c      0x110 ..\lib\libmcu.a(hx330x_auadc.o)
                                0x124 (size before relaxing)
 .eh_frame      0x0000ee5c      0x4e4 ..\lib\libmcu.a(hx330x_csi.o)
                                0x4f8 (size before relaxing)
 .eh_frame      0x0000f340      0x1d4 ..\lib\libmcu.a(hx330x_dac.o)
                                0x1e8 (size before relaxing)
 .eh_frame      0x0000f514       0x88 ..\lib\libmcu.a(hx330x_dma.o)
                                 0x9c (size before relaxing)
 .eh_frame      0x0000f59c      0x170 ..\lib\libmcu.a(hx330x_dmauart.o)
                                0x184 (size before relaxing)
 .eh_frame      0x0000f70c      0x2c8 ..\lib\libmcu.a(hx330x_gpio.o)
                                0x2dc (size before relaxing)
 .eh_frame      0x0000f9d4      0x31c ..\lib\libmcu.a(hx330x_iic.o)
                                0x330 (size before relaxing)
 .eh_frame      0x0000fcf0       0x9c ..\lib\libmcu.a(hx330x_int.o)
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000fd8c      0x264 ..\lib\libmcu.a(hx330x_isp.o)
                                0x278 (size before relaxing)
 .eh_frame      0x0000fff0      0x778 ..\lib\libmcu.a(hx330x_jpg.o)
                                0x78c (size before relaxing)
 .eh_frame      0x00010768       0x38 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                                 0x4c (size before relaxing)
 .eh_frame      0x000107a0      0x3d4 ..\lib\libmcu.a(hx330x_lcd.o)
                                0x3e8 (size before relaxing)
 .eh_frame      0x00010b74       0xc4 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                                 0xd8 (size before relaxing)
 .eh_frame      0x00010c38      0x3c8 ..\lib\libmcu.a(hx330x_lcdui.o)
                                0x3dc (size before relaxing)
 .eh_frame      0x00011000       0xe8 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                                 0xfc (size before relaxing)
 .eh_frame      0x000110e8       0x80 ..\lib\libmcu.a(hx330x_lcdwin.o)
                                 0x94 (size before relaxing)
 .eh_frame      0x00011168       0x80 ..\lib\libmcu.a(hx330x_md.o)
                                 0x94 (size before relaxing)
 .eh_frame      0x000111e8       0x5c ..\lib\libmcu.a(hx330x_mipi.o)
                                 0x70 (size before relaxing)
 .eh_frame      0x00011244      0x29c ..\lib\libmcu.a(hx330x_misc.o)
                                0x2b0 (size before relaxing)
 .eh_frame      0x000114e0      0x374 ..\lib\libmcu.a(hx330x_rtc.o)
                                0x388 (size before relaxing)
 .eh_frame      0x00011854      0x2b4 ..\lib\libmcu.a(hx330x_sd.o)
                                0x2c8 (size before relaxing)
 .eh_frame      0x00011b08      0x108 ..\lib\libmcu.a(hx330x_spi0.o)
                                0x11c (size before relaxing)
 .eh_frame      0x00011c10      0x184 ..\lib\libmcu.a(hx330x_spi1.o)
                                0x198 (size before relaxing)
 .eh_frame      0x00011d94      0x3d4 ..\lib\libmcu.a(hx330x_sys.o)
                                0x3e8 (size before relaxing)
 .eh_frame      0x00012168      0x188 ..\lib\libmcu.a(hx330x_timer.o)
                                0x19c (size before relaxing)
 .eh_frame      0x000122f0      0x110 ..\lib\libmcu.a(hx330x_tminf.o)
                                0x124 (size before relaxing)
 .eh_frame      0x00012400       0x78 ..\lib\libmcu.a(hx330x_uart.o)
                                 0x8c (size before relaxing)
 .eh_frame      0x00012478      0x444 ..\lib\libmcu.a(hx330x_usb.o)
                                0x458 (size before relaxing)
 .eh_frame      0x000128bc       0x60 ..\lib\libmcu.a(hx330x_wdt.o)
                                 0x74 (size before relaxing)
 .eh_frame      0x0001291c       0xa4 ..\lib\libmcu.a(hx330x_emi.o)
                                 0xb8 (size before relaxing)
 .eh_frame      0x000129c0      0x220 ..\lib\libisp.a(hal_isp.o)
                                0x234 (size before relaxing)
 .eh_frame      0x00012be0      0x118 ..\lib\libjpg.a(hal_jpg.o)
                                0x12c (size before relaxing)
 .eh_frame      0x00012cf8      0x1c0 ..\lib\liblcd.a(hal_lcd.o)
                                0x1d4 (size before relaxing)
 .eh_frame      0x00012eb8      0x148 ..\lib\liblcd.a(hal_lcdMem.o)
                                0x15c (size before relaxing)
 .eh_frame      0x00013000       0x84 ..\lib\liblcd.a(hal_lcdrotate.o)
                                 0x98 (size before relaxing)
 .eh_frame      0x00013084      0x238 ..\lib\liblcd.a(hal_lcdUi.o)
                                0x24c (size before relaxing)
 .eh_frame      0x000132bc       0x60 ..\lib\liblcd.a(hal_lcdUiLzo.o)
                                 0x74 (size before relaxing)
 .eh_frame      0x0001331c       0x58 ..\lib\liblcd.a(lcd_tab.o)
                                 0x6c (size before relaxing)
 .eh_frame      0x00013374      0x204 ..\lib\libmultimedia.a(api_multimedia.o)
                                0x218 (size before relaxing)
 .eh_frame      0x00013578      0x14c ..\lib\libmultimedia.a(avi_dec.o)
                                0x160 (size before relaxing)
 .eh_frame      0x000136c4      0x130 ..\lib\libmultimedia.a(avi_odml_enc.o)
                                0x144 (size before relaxing)
 .eh_frame      0x000137f4      0x144 ..\lib\libmultimedia.a(avi_std_enc.o)
                                0x158 (size before relaxing)
 .eh_frame      0x00013938       0xb8 ..\lib\libmultimedia.a(wav_dec.o)
                                 0xcc (size before relaxing)
 .eh_frame      0x000139f0       0xa8 ..\lib\libmultimedia.a(wav_enc.o)
                                 0xbc (size before relaxing)
 .eh_frame      0x00013a98       0x38 ..\lib\libmultimedia.a(wav_pcm.o)
                                 0x4c (size before relaxing)
 .eh_frame      0x00013ad0       0x2c D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                                 0x40 (size before relaxing)
 .eh_frame      0x00013afc       0x2c D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                                 0x40 (size before relaxing)

.rela.dyn       0x00013b28        0x0 load address 0x000ac2e4
 .rela.text     0x00013b28        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.data     0x00013b28        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector.kepttext
                0x00013b28        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.rodata   0x00013b28        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.sdram_text
                0x00013b28        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.sensor_res.header
                0x00013b28        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.text.startup
                0x00013b28        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector.text
                0x00013b28        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.bootsec  0x00013b28        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector   0x00013b28        0x0 obj\Debug\dev\battery\src\battery_api.o

.mp3_text       0x00008000        0x0 load address 0x0009be00
                0x00008000                __mp3_text_start = .
 *(.mp3_text)
                0x00008000                . = ALIGN (0x4)

.mp3_code       0x00200000        0x0 load address 0x0009be00
                0x00200000                __mp3_code_start = .
 *(.mp3_code)
                0x00200000                . = ALIGN (0x4)

.mp3_data
 *(.mp3_data)

.nes_code       0x00200000        0x0 load address 0x0009be00
                0x00200000                __nes_code_start = .
 *(.nes_code)
                0x00200000                . = ALIGN (0x4)

.nes_data       0x00200000        0x0
                0x00200000                __nes_data_start = .
 *(.nes_data)
                0x00200000                __nes_data_end = .

.nes_com_text   0x00008000        0x0 load address 0x0009be00
                0x00008000                __nes_text_start = .
 *(.nes_com_text)
                0x00008000                . = ALIGN (0x4)
                0x00000015                _boot_code_len = ((SIZEOF (.boot_code) + 0x1ff) / 0x200)
                0x00000001                _boot_code_sec = (LOADADDR (.boot_code) / 0x200)
                0x02000000                _text_start = _onsdram_start
                0x00000016                _text_sec = (LOADADDR (.on_sdram) / 0x200)
                0x0000004e                _text_len = ((SIZEOF (.on_sdram) + 0x1ff) / 0x200)
                0x00000001                ASSERT (((_sdram_remian_addr - ORIGIN (sdram)) < __sdram_size), No memroy for sdram)
                0x001e3900                __sdram_remain_size = ((ORIGIN (sdram) + __sdram_size) - _sdram_remian_addr)
                0x00001400                __stack_size = 0x1400
                0x00000001                ASSERT ((((ORIGIN (ram_user) + 0x7000) - __sram_end) >= __stack_size), No memroy for stack)
                0x02200000                __bss_end = (_sdram_remian_addr + __sdram_remain_size)
                0x00000000                __mp3_text_len = SIZEOF (.mp3_text)
                0x0009be00                __mp3_text_addr = LOADADDR (.mp3_text)
                0x00000000                __mp3_code_len = SIZEOF (.mp3_code)
                0x0009be00                __mp3_code_addr = LOADADDR (.mp3_code)
                0x00000000                __nes_text_len = SIZEOF (.nes_com_text)
                0x0009be00                __nes_text_addr = LOADADDR (.nes_com_text)
                0x00000000                __nes_code_len = SIZEOF (.nes_code)
                0x0009be00                __nes_code_addr = LOADADDR (.nes_code)
                0x00095e00                _lcd_res_lma = LOADADDR (.lcd_resource)
                0x000029bc                _lcd_res_size = SIZEOF (.lcd_resource)
                0x000987bc                _sensor_resource_start_addr = LOADADDR (.sensor_resource)
                0x00000000                _res_sensor_header_len = (_res_sensor_header_item_end - _res_sensor_header_item_start)
                0x0009be00                _nes_res_lma = LOADADDR (.nes_resource)
                0x00006ffc                PROVIDE (__stack, ((ORIGIN (ram_user) + 0x7000) - 0x4))
LOAD ..\lib\libboot.a
LOAD ..\lib\libmcu.a
LOAD ..\lib\libisp.a
LOAD ..\lib\libjpg.a
LOAD ..\lib\liblcd.a
LOAD ..\lib\libmultimedia.a
LOAD D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a
LOAD D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a
OUTPUT(bin\Debug\hx330x_sdk.exe elf32-or1k)

.comment        0x00000000       0x11
 .comment       0x00000000       0x11 obj\Debug\dev\battery\src\battery_api.o
                                 0x12 (size before relaxing)
 .comment       0x00000011       0x12 obj\Debug\dev\dev_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\diskio.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\ff.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\ffunicode.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\fs_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .comment       0x00000011       0x12 obj\Debug\dev\ir\src\ir_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\key\src\key_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .comment       0x00000011       0x12 obj\Debug\dev\led\src\led_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\nvfs\src\nvfs_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .comment       0x00000011       0x12 obj\Debug\dev\sd\src\sd_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_tab.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_enum.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_hub.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_adc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_auadc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_csi.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_dac.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_dmauart.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_eeprom.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_gpio.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_iic.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_int.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_lcdshow.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_md.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpAEncode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpBEncode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpDecode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_rtc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_spi.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_spi1.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_stream.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_sys.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_timer.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_uart.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_watermark.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_wdt.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xmbox.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xmsgq.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xos.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xwork.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\audio\audio_playback.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\audio\audio_record.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\image\image_decode.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\image\image_encode.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\video\video_playback.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\video\video_record.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\app_init.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\app_lcdshow.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\main.o
 .comment       0x00000011       0x12 obj\Debug\app\resource\user_res.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\msg_api.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_api.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\task_common.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\windows_api.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\mbedtls_md5.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\user_config_api.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\user_config_tab.o
 .comment       0x00000011       0x12 ..\lib\libboot.a(boot.o)
 .comment       0x00000011       0x12 ..\lib\libboot.a(boot_lib.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_adc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_auadc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_csi.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dac.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dma.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dmauart.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_gpio.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_iic.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_int.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_isp.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_jpg.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcd.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdui.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_md.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_mipi.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_misc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_rtc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_sd.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_spi0.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_spi1.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_sys.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_timer.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_tminf.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_uart.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_usb.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_wdt.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_emi.o)
 .comment       0x00000011       0x12 ..\lib\libisp.a(hal_isp.o)
 .comment       0x00000011       0x12 ..\lib\libjpg.a(hal_jpg.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcd.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdMem.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdrotate.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdUi.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(lcd_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(api_multimedia.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_std_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_pcm.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_info     0x00000000     0x13ab
 .debug_info    0x00000000      0x113 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_info    0x00000113      0x131 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_info    0x00000244      0x117 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_info    0x0000035b      0x10e D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_info    0x00000469      0x71e D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_info    0x00000b87      0x76b D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_info    0x000012f2       0xb9 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_abbrev   0x00000000      0x5c4
 .debug_abbrev  0x00000000       0x7f D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_abbrev  0x0000007f       0xab D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_abbrev  0x0000012a       0x9f D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_abbrev  0x000001c9       0x92 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_abbrev  0x0000025b      0x170 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_abbrev  0x000003cb      0x19c D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_abbrev  0x00000567       0x5d D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_loc      0x00000000     0x2bbe
 .debug_loc     0x00000000      0x102 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_loc     0x00000102      0x2ed D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_loc     0x000003ef      0x1ba D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_loc     0x000005a9       0xc7 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_loc     0x00000670     0x1537 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_loc     0x00001ba7     0x1017 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)

.debug_aranges  0x00000000       0xd8
 .debug_aranges
                0x00000000       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_aranges
                0x00000020       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_aranges
                0x00000040       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_aranges
                0x00000060       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_aranges
                0x00000080       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_aranges
                0x000000a0       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_aranges
                0x000000c0       0x18 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_line     0x00000000      0x849
 .debug_line    0x00000000      0x15d D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_line    0x0000015d      0x164 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_line    0x000002c1      0x16d D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_line    0x0000042e       0xf5 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_line    0x00000523      0x15d D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_line    0x00000680      0x163 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_line    0x000007e3       0x66 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_str      0x00000000      0x4dc
 .debug_str     0x00000000      0x14f D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                                0x188 (size before relaxing)
 .debug_str     0x0000014f       0x7b D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                                0x1af (size before relaxing)
 .debug_str     0x000001ca       0x68 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                                0x19c (size before relaxing)
 .debug_str     0x00000232       0x54 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                                0x1a3 (size before relaxing)
 .debug_str     0x00000286      0x1dc D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                                0x284 (size before relaxing)
 .debug_str     0x00000462        0xa D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                                0x284 (size before relaxing)
 .debug_str     0x0000046c       0x70 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                                0x1cb (size before relaxing)

.debug_frame    0x00000000       0xa0
 .debug_frame   0x00000000       0x28 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_frame   0x00000028       0x28 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_frame   0x00000050       0x28 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_frame   0x00000078       0x28 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)

.debug_ranges   0x00000000      0x320
 .debug_ranges  0x00000000      0x190 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_ranges  0x00000190      0x190 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
