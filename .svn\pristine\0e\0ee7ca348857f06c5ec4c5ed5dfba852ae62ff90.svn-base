/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  VIDEO_PLAYBACK_H
    #define  VIDEO_PLAYBACK_H

#if DBG_VIDEO_PLY_EN
    #define  VIDEOPLY_DBG     		deg_Printf
#else
    #define  VIDEOPLY_DBG(...)
#endif

typedef struct VIDEO_PARG_S
{
	AVI_DEC_ARG avi_arg;
	INT16U	tar_width;
	INT16U	tar_height;
	INT16U	dest_width;
	INT16U	dest_height;
	INT16U	pos_x;
	INT16U	pos_y;
	INT16U  rotate;
    INT16U  firstframe;
	INT8U  	*yout;
   	INT8U  	*uvout;
}VIDEO_PARG_T;  








/*******************************************************************************
* Function Name  : videoPlaybackClear
* Description    : clear screen
* Input          : INT8U *ybuffer : y buffer
				   INT8U *uvbuffer: uv buffer
				   INT16U width   : width
				   INT16U height : height
				   INT8U y : y-value
				   INT8U uv : uv-value
* Output         : none
* Return         : int 
*******************************************************************************/
void videoPlaybackClear(INT8U *ybuffer,INT8U *uvbuffer,INT16U width,INT16U height,INT8U y,INT8U uv);
/*******************************************************************************
* Function Name  : videoPlaybackInit
* Description    : initial video Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoPlaybackInit(void);
/*******************************************************************************
* Function Name  : avideoPlaybackUninit
* Description    : uninitial video Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoPlaybackUninit(void);
/*******************************************************************************
* Function Name  : videoPlaybackDecodeWait
* Description    : videoPlaybackDecodeWait, wait jpg decode until timeout 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoPlaybackDecodeWait(int timeout);
/*******************************************************************************
* Function Name  : videoPlaybackStart
* Description    : Start video Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoPlaybackStart(VIDEO_PARG_T *arg);
/*******************************************************************************
* Function Name  : videoPlaybackStop
* Description    : Stop video Playback 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void videoPlaybackStop(void);
/*******************************************************************************
* Function Name  : videoPlaybackPause
* Description    : Pause video Playback 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void videoPlaybackPause(void);
/*******************************************************************************
* Function Name  : videoPlaybackResume
* Description    : Resume video Playback 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void videoPlaybackResume(void);
/*******************************************************************************
* Function Name  : videoPlaybackGetStatus
* Description    : get video Playback 
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int videoPlaybackGetStatus(void);
/*******************************************************************************
* Function Name  : audioPlaybackSetVolume
* Description    : set audio Playback volume
* Input          : INT8U volume : 0-100
* Return         : none
*******************************************************************************/
void videoPlaybackSetVolume(INT8U volume);
/*******************************************************************************
* Function Name  : audioPlaybackGetVolume
* Description    : get audio Playback volume
* Input          : 
* Return         : INT8U volume : 0-100 
                      
*******************************************************************************/
INT8U videoPlaybackGetVolume(void);
/*******************************************************************************
* Function Name  : videoPlaybackGetTime
* Description    : get video Playback time ms
* Input          : INT32U *total : total time
				   INT32U *curr : current time
* Output         : INT32U *total : total time
				   INT32U *curr : current time
* Return         : int 
*******************************************************************************/
int videoPlaybackGetTime(INT32U *total,INT32U *curr);
/*******************************************************************************
* Function Name  : videoPlaybackGetResolution
* Description    : get video Playback resolution
* Input          : none
* Output         : none
* Return         : Media_Res_T *                       
*******************************************************************************/
AVI_DEC_ARG* videoPlaybabkGetArg(void);
/*******************************************************************************
* Function Name  : videoDecodeFirstFrame
* Description    : video decode first frame
* Input          : VIDEO_PARG_T *arg
* Output         : none
* Return         : int: <0 fail, 0 success                      
*******************************************************************************/
int videoDecodeFirstFrame(VIDEO_PARG_T *arg);
/*******************************************************************************
* Function Name  : videoGetFirstFrame
* Description    : video decode first frame
* Input          : VIDEO_PARG_T *arg
* Output         : none
* Return         : int: <0 fail, 0 success                      
*******************************************************************************/
int videoGetFirstFrame(VIDEO_PARG_T *arg, u32 * offset, u32* length);
/*******************************************************************************
* Function Name  : videoPlaybackGetStatus
* Description    : get video Playback 
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int videoPlaybackService(void);
/*******************************************************************************
* Function Name  : videoPlaybackFastForward
* Description    : ast forward  
* Input          : none
* Output         : none
* Return         : int  : current play speed setp
                            : backward : 0,1,2,3, -> -16,-8,-4,-1
                              normal     : 4 -0
                              forward   : 5,6,7,8, ->1,4,8,16
*******************************************************************************/
int videoPlaybackFastForward(void);
/*******************************************************************************
* Function Name  : videoPlaybackFastBackward
* Description    : fast backward
* Input          : none
* Output         : none
* Return         : int  : current play speed setp
                            : backward : 0,1,2,3, -> -16,-8,-4,-1
                              normal     : 4 -0
                              forward   : 5,6,7,8, ->1,4,8,16                      
*******************************************************************************/
int videoPlaybackFastBackward(void);


int videoPlaybackGetSpeed(void);





















#endif
