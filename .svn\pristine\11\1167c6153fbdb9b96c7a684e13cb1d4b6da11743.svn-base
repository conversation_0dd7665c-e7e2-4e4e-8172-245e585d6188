<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_layout_file>
	<FileVersion major="1" minor="0" />
	<ActiveTarget name="Debug" />
	<File name="..\sys_manage\ui_manage\src\uiWinImageIcon.c" open="1" top="0" tabpos="1" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_SP140A.c" open="1" top="0" tabpos="2" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\led\src\led_api.c" open="1" top="0" tabpos="3" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\Library\JPG\inc\jpg_typedef.h" open="1" top="0" tabpos="4" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_audio\inc\taskRecordAudio.h" open="1" top="0" tabpos="5" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinWidget.h" open="1" top="0" tabpos="6" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_GC0307.c" open="1" top="0" tabpos="7" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_st7701FW1601.c" open="1" top="0" tabpos="8" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\video\video_playback.h" open="1" top="0" tabpos="9" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_video\src\taskPlayVideo.c" open="1" top="0" tabpos="10" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_GC0328.c" open="1" top="0" tabpos="11" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_hx8357b.c" open="1" top="0" tabpos="12" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuUnlockAllMsg.c" open="1" top="0" tabpos="13" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_OV7736.c" open="1" top="0" tabpos="14" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\fs\inc\diskio.h" open="1" top="0" tabpos="15" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowTpIconWin.c" open="1" top="0" tabpos="16" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_iic.c" open="1" top="0" tabpos="17" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_rtc.c" open="1" top="0" tabpos="18" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuFormatMsg.c" open="1" top="0" tabpos="19" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_watermark.h" open="1" top="0" tabpos="20" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_cfg.h" open="1" top="0" tabpos="21" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\fs\src\ff.c" open="1" top="0" tabpos="22" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowNoFileMsg.c" open="1" top="0" tabpos="23" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_dac.h" open="1" top="0" tabpos="24" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_emi.h" open="1" top="0" tabpos="25" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_st7789.c" open="1" top="0" tabpos="26" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_audio\src\taskPlayAudioWin.c" open="1" top="0" tabpos="27" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\inc\husb_api.h" open="1" top="0" tabpos="28" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_mjpDecode.c" open="1" top="0" tabpos="29" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\fs\inc\fs_api.h" open="1" top="0" tabpos="30" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuDefaultMsg.c" open="1" top="0" tabpos="31" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\src\husb_api.c" open="1" top="0" tabpos="32" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.c" open="1" top="0" tabpos="33" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_show_logo\src\taskShowLogoMsg.c" open="1" top="0" tabpos="34" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="resource\user_res.c" open="1" top="0" tabpos="35" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\inc\dusb_msc.h" open="1" top="0" tabpos="36" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\Library\JPG\src\jpg_enc.c" open="1" top="0" tabpos="37" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\dev_api.c" open="1" top="0" tabpos="38" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinItemManage.c" open="1" top="0" tabpos="39" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\touchpanel\src\touchpanel_api.c" open="1" top="0" tabpos="40" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_uart.h" open="1" top="0" tabpos="41" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_usb_device\src\taskUsbDeviceWin.c" open="1" top="0" tabpos="42" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinDialog.h" open="1" top="0" tabpos="43" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\touchpanel\src\touchpanel_iic.c" open="1" top="0" tabpos="44" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\inc\sensor_api.h" open="1" top="0" tabpos="45" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_rtc.h" open="1" top="0" tabpos="46" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_show_logo\src\taskShowLogoWin.c" open="1" top="0" tabpos="47" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="app_common\src\app_lcdshow.c" open="1" top="0" tabpos="48" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_timer.h" open="1" top="0" tabpos="49" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_st7282.c" open="1" top="0" tabpos="50" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_sd_update\src\taskSdUpdateWin.c" open="1" top="0" tabpos="51" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinMemManage.h" open="1" top="0" tabpos="52" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\nvfs\inc\nvfs_api.h" open="1" top="0" tabpos="53" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_sd.h" open="1" top="0" tabpos="54" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_video\src\taskRecordVideo.c" open="1" top="0" tabpos="55" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuDateTimeWin.c" open="1" top="0" tabpos="56" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_SC1345.c" open="1" top="0" tabpos="57" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_spi_ili9341.c" open="1" top="0" tabpos="58" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_ascii\src\ascii_api.c" open="1" top="0" tabpos="59" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_poweroff\src\taskPowerOff.c" open="1" top="0" tabpos="60" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="app_common\src\main.c" open="1" top="0" tabpos="61" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_OV9710.c" open="1" top="0" tabpos="62" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_r61509v.c" open="1" top="0" tabpos="63" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\xos\xcfg.h" open="1" top="0" tabpos="64" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuVersionWin.c" open="1" top="0" tabpos="65" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_SC1243.c" open="1" top="0" tabpos="66" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_hx8352b.c" open="1" top="0" tabpos="67" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuUnlockCurWin.c" open="1" top="0" tabpos="68" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_H65.c" open="1" top="0" tabpos="69" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_isp.h" open="1" top="0" tabpos="70" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowSelfTestMsg.c" open="1" top="0" tabpos="71" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_gpio.c" open="1" top="0" tabpos="72" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_watermark.c" open="1" top="0" tabpos="73" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuDelCurMsg.c" open="1" top="0" tabpos="74" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_uart.h" open="1" top="0" tabpos="75" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_mjpBEncode.c" open="1" top="0" tabpos="76" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuOptionWin.c" open="1" top="0" tabpos="77" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_csi.h" open="1" top="0" tabpos="78" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_md.h" open="1" top="0" tabpos="79" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowTipsMsg.c" open="1" top="0" tabpos="80" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_mjpEncode.h" open="1" top="0" tabpos="81" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_spi.c" open="1" top="0" tabpos="82" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_show_logo\src\taskShowLogo.c" open="1" top="0" tabpos="83" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\gsensor\src\gsensor_gma301.c" open="1" top="0" tabpos="84" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuDateTimeMsg.c" open="1" top="0" tabpos="85" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\inc\dusb_uac.h" open="1" top="0" tabpos="86" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\filelist_manage\src\file_list_manage.c" open="1" top="0" tabpos="87" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\ir\src\ir_api.c" open="1" top="0" tabpos="88" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="app_common\inc\app_typedef.h" open="1" top="0" tabpos="89" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\touchpanel\src\touchpanel_ns2009.c" open="1" top="0" tabpos="90" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_sys.h" open="1" top="0" tabpos="91" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\windows_api.c" open="1" top="0" tabpos="92" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinDraw.h" open="1" top="0" tabpos="93" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\src\husb_hub.c" open="1" top="0" tabpos="94" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\Library\WAV\inc\wav_api.h" open="1" top="0" tabpos="95" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinApi.h" open="1" top="0" tabpos="96" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\inc\dusb_uvc.h" open="1" top="0" tabpos="97" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_otm8019a.c" open="1" top="0" tabpos="98" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\image\image_encode.c" open="1" top="0" tabpos="99" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinRect.c" open="1" top="0" tabpos="100" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_ili8961.c" open="1" top="0" tabpos="101" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\xos\xos.c" open="1" top="0" tabpos="102" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinImageIcon.h" open="1" top="0" tabpos="103" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sd\inc\sd_api.h" open="1" top="0" tabpos="104" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_image\src\res_image_api.c" open="1" top="0" tabpos="105" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_video\inc\taskRecordVideo.h" open="1" top="0" tabpos="106" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinStringIcon.c" open="1" top="0" tabpos="107" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_NT99142.c" open="1" top="0" tabpos="108" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_rm68172.c" open="1" top="0" tabpos="109" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\audio\audio_record.c" open="1" top="0" tabpos="110" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_video\src\taskPlayVideoMainWin.c" open="1" top="0" tabpos="111" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_manage_api.h" open="1" top="0" tabpos="112" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_SC1045.c" open="1" top="0" tabpos="113" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_jd9851.c" open="1" top="0" tabpos="114" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuVersionMsg.c" open="1" top="0" tabpos="115" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\touchpanel\inc\touchpanel_iic.h" open="1" top="0" tabpos="116" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_api.c" open="1" top="0" tabpos="117" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_api.c" open="1" top="0" tabpos="118" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_H62.c" open="1" top="0" tabpos="119" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_dma.h" open="1" top="0" tabpos="120" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuUnlockAllWin.c" open="1" top="0" tabpos="121" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_eeprom.c" open="1" top="0" tabpos="122" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_uart.c" open="1" top="0" tabpos="123" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuOptionMsg.c" open="1" top="0" tabpos="124" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_timer.h" open="1" top="0" tabpos="125" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\boot\spi_boot_cfg.h" open="1" top="0" tabpos="126" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_common\src\task_common_msg.c" open="1" top="0" tabpos="127" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\src\husb_usensor.c" open="1" top="0" tabpos="128" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_sys.c" open="1" top="0" tabpos="129" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_lgdp4532.c" open="1" top="0" tabpos="130" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_common\src\task_common.c" open="1" top="0" tabpos="131" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="19677" topLine="581" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_mjpDecode.h" open="1" top="0" tabpos="132" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_image\inc\res_image_api.h" open="1" top="0" tabpos="133" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\gsensor\src\gsensor_sc7a30e.c" open="1" top="0" tabpos="134" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\mMenuRecordWin.c" open="1" top="0" tabpos="135" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="6170" topLine="147" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\src\dusb_tool_api.c" open="1" top="0" tabpos="136" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_ascii\inc\res_ascii_api.h" open="1" top="0" tabpos="137" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="user_config\src\user_config_api.c" open="1" top="0" tabpos="138" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\inc\lcd_typedef.h" open="1" top="0" tabpos="139" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="app_common\inc\app_api.h" open="1" top="0" tabpos="140" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\touchpanel\src\touchpanel_icnt81.c" open="1" top="0" tabpos="141" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinStringIcon.h" open="1" top="0" tabpos="142" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\inc\dusb_api.h" open="1" top="0" tabpos="143" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\Library\AVI\inc\avi_api.h" open="1" top="0" tabpos="144" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_show_logo\inc\taskShowLogo.h" open="1" top="0" tabpos="145" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinDraw.c" open="1" top="0" tabpos="146" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_sys.h" open="1" top="0" tabpos="147" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\nvfs\src\nvfs_jpg.c" open="1" top="0" tabpos="148" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\image\image_decode.h" open="1" top="0" tabpos="149" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinProgressBar.h" open="1" top="0" tabpos="150" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_ILI9806e_4522.c" open="1" top="0" tabpos="151" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\typedef.h" open="1" top="0" tabpos="152" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="user_config\inc\user_hardware_cfg_hx3308_demo.h" open="1" top="0" tabpos="153" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinItemMenu.h" open="1" top="0" tabpos="154" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\led_pwm\src\led_pwm_api.c" open="1" top="0" tabpos="155" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\audio\audio_record.h" open="1" top="0" tabpos="156" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_photo\src\taskRecordPhotoWin.c" open="1" top="0" tabpos="157" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="11002" topLine="261" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_music\src\res_music_api.c" open="1" top="0" tabpos="158" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_GC0308.c" open="1" top="0" tabpos="159" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_st7701FW1604.c" open="1" top="0" tabpos="160" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\audio\audio_playback.h" open="1" top="0" tabpos="161" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_poweroff\inc\taskPoweroff.h" open="1" top="0" tabpos="162" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinStringEx.c" open="1" top="0" tabpos="163" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_HM1055.c" open="1" top="0" tabpos="164" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_hx8352c.c" open="1" top="0" tabpos="165" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowTips1Win.c" open="1" top="0" tabpos="166" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_IT03A1.c" open="1" top="0" tabpos="167" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\ir\inc\ir_api.h" open="1" top="0" tabpos="168" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuDelAllWin.c" open="1" top="0" tabpos="169" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_mipi_720P_OV9714.c" open="1" top="0" tabpos="170" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_int.h" open="1" top="0" tabpos="171" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_audio\src\taskPlayAudioMsg.c" open="1" top="0" tabpos="172" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_csi.c" open="1" top="0" tabpos="173" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_dac.h" open="1" top="0" tabpos="174" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_photo\src\taskRecordPhotoMsg.c" open="1" top="0" tabpos="175" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="12842" topLine="418" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_stream.h" open="1" top="0" tabpos="176" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_lcdshow.c" open="1" top="0" tabpos="177" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\battery\src\battery_api.c" open="1" top="0" tabpos="178" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="733" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\msg_api.c" open="1" top="0" tabpos="179" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\inc\husb_usensor.h" open="1" top="0" tabpos="180" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_lcd.h" open="1" top="0" tabpos="181" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_SPFD5420.c" open="1" top="0" tabpos="182" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowNoFileWin.c" open="1" top="0" tabpos="183" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_md.h" open="1" top="0" tabpos="184" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_icon\src\res_icon_api.c" open="1" top="0" tabpos="185" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_usb_device\src\taskUsbDevice.c" open="1" top="0" tabpos="186" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\fs\src\diskio.c" open="1" top="0" tabpos="187" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\mMenuRecordMsg.c" open="1" top="0" tabpos="188" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\inc\dusb_tool_api.h" open="1" top="0" tabpos="189" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\filelist_manage\src\file_list_api.c" open="1" top="0" tabpos="190" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\gsensor\src\gsensor_da380.c" open="1" top="0" tabpos="191" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinWidgetManage.c" open="1" top="0" tabpos="192" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_isp.h" open="1" top="0" tabpos="193" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_misc.h" open="1" top="0" tabpos="194" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinLine.h" open="1" top="0" tabpos="195" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_cfg.h" open="1" top="0" tabpos="196" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinTips.h" open="1" top="0" tabpos="197" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_spi.h" open="1" top="0" tabpos="198" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\led\inc\led_api.h" open="1" top="0" tabpos="199" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\image\image_decode.c" open="1" top="0" tabpos="200" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinItemMenuEx.c" open="1" top="0" tabpos="201" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sd\src\sd_api.c" open="1" top="0" tabpos="202" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_usb.h" open="1" top="0" tabpos="203" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_usb_device\inc\taskUsbDevice.h" open="1" top="0" tabpos="204" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinMemManage.c" open="1" top="0" tabpos="205" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_icon\inc\res_icon_api.h" open="1" top="0" tabpos="206" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\led_pwm\inc\led_pwm_api.h" open="1" top="0" tabpos="207" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_photo\src\taskRecordPhoto.c" open="1" top="0" tabpos="208" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\mMenuPlayWin.c" open="1" top="0" tabpos="209" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_GC0309.c" open="1" top="0" tabpos="210" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.c" open="1" top="0" tabpos="211" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\xos\xwork.c" open="1" top="0" tabpos="212" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.c" open="1" top="0" tabpos="213" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinWidget.c" open="1" top="0" tabpos="214" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_BF3703.c" open="1" top="0" tabpos="215" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.c" open="1" top="0" tabpos="216" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowTpIconMsg.c" open="1" top="0" tabpos="217" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_mipi_720P_GC1054.c" open="1" top="0" tabpos="218" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\gsensor\src\gsensor_api.c" open="1" top="0" tabpos="219" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuFormatWin.c" open="1" top="0" tabpos="220" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_mipi_720P_GC1084.c" open="1" top="0" tabpos="221" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_iic.h" open="1" top="0" tabpos="222" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_api.h" open="1" top="0" tabpos="223" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_H42.c" open="1" top="0" tabpos="224" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_dmauart.c" open="1" top="0" tabpos="225" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_video\src\taskPlayVideoThumbnallWin.c" open="1" top="0" tabpos="226" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_rtc.h" open="1" top="0" tabpos="227" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_wdt.c" open="1" top="0" tabpos="228" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_ili9328.c" open="1" top="0" tabpos="229" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowTipsWin.c" open="1" top="0" tabpos="230" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\inc\husb_tpbulk.h" open="1" top="0" tabpos="231" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_spi1.c" open="1" top="0" tabpos="232" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\inc\lcd_api.h" open="1" top="0" tabpos="233" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowAsternMsg.c" open="1" top="0" tabpos="234" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_font\src\res_font_api.c" open="1" top="0" tabpos="235" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="user_config\inc\user_hardware_cfg_hx3303_demo.h" open="1" top="0" tabpos="236" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\fs\inc\ff.h" open="1" top="0" tabpos="237" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\mMenuPlayMsg.c" open="1" top="0" tabpos="238" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\src\dusb_msc.c" open="1" top="0" tabpos="239" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\filelist_manage\inc\file_list_typedef.h" open="1" top="0" tabpos="240" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="user_config\inc\user_hardware_cfg_fgpa.h" open="1" top="0" tabpos="241" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\battery\inc\battery_api.h" open="1" top="0" tabpos="242" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinProgressBar.c" open="1" top="0" tabpos="243" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\inc\dusb_enum.h" open="1" top="0" tabpos="244" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\audio\audio_playback.c" open="1" top="0" tabpos="245" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="9419" topLine="299" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinButton.h" open="1" top="0" tabpos="246" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\src\dusb_uvc.c" open="1" top="0" tabpos="247" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\xos\xwork.h" open="1" top="0" tabpos="248" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="user_config\inc\user_config_api.h" open="1" top="0" tabpos="249" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="app_common\src\app_init.c" open="1" top="0" tabpos="250" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.c" open="1" top="0" tabpos="251" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\Library\WAV\inc\wav_typedef.h" open="1" top="0" tabpos="252" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinItemManage.h" open="1" top="0" tabpos="253" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_GC1004.c" open="1" top="0" tabpos="254" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_pmu.h" open="1" top="0" tabpos="255" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="user_config\src\mbedtls_md5.c" open="1" top="0" tabpos="256" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinCycle.c" open="1" top="0" tabpos="257" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_BF2013.c" open="1" top="0" tabpos="258" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\nvfs\src\nvfs_api.c" open="1" top="0" tabpos="259" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_font\inc\res_font_api.h" open="1" top="0" tabpos="260" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_photo\inc\taskRecordPhoto.h" open="1" top="0" tabpos="261" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinFrame.h" open="1" top="0" tabpos="262" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_SIV121DS.c" open="1" top="0" tabpos="263" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_ILI9806e.c" open="1" top="0" tabpos="264" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\xos\xmsgq.h" open="1" top="0" tabpos="265" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_video\src\taskPlayVideoSlideWin.c" open="1" top="0" tabpos="266" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinManage.c" open="1" top="0" tabpos="267" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_SP1409.c" open="1" top="0" tabpos="268" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_ili9335.c" open="1" top="0" tabpos="269" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_audio\src\taskPlayAudio.c" open="1" top="0" tabpos="270" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\touchpanel\inc\touchpanel_api.h" open="1" top="0" tabpos="271" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\gsensor\inc\gsensor_api.h" open="1" top="0" tabpos="272" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuItemWin.c" open="1" top="0" tabpos="273" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_OV7670.c" open="1" top="0" tabpos="274" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_gpio.h" open="1" top="0" tabpos="275" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuLockCurWin.c" open="1" top="0" tabpos="276" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_GC1064.c" open="1" top="0" tabpos="277" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_dac.c" open="1" top="0" tabpos="278" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_csi.h" open="1" top="0" tabpos="279" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_common\inc\task_common.h" open="1" top="0" tabpos="280" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_pmu.h" open="1" top="0" tabpos="281" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_auadc.h" open="1" top="0" tabpos="282" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\dev_api.h" open="1" top="0" tabpos="283" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1043" topLine="27" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuLockCurMsg.c" open="1" top="0" tabpos="284" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\src\dusb_uac.c" open="1" top="0" tabpos="285" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\boot\spi_boot_cfg.S" open="1" top="0" tabpos="286" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\key\src\key_api.c" open="1" top="0" tabpos="287" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_common\src\sys_common_msg.c" open="1" top="0" tabpos="288" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_eeprom.h" open="1" top="0" tabpos="289" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.c" open="1" top="0" tabpos="290" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_usb_device\src\taskUsbDeviceMsg.c" open="1" top="0" tabpos="291" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\inc\menu_typedef.h" open="1" top="0" tabpos="292" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="1147" topLine="32" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\src\dusb_api.c" open="1" top="0" tabpos="293" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\filelist_manage\inc\file_list_api.h" open="1" top="0" tabpos="294" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinLine.c" open="1" top="0" tabpos="295" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_iic.h" open="1" top="0" tabpos="296" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_manage_api.c" open="1" top="0" tabpos="297" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_auadc.h" open="1" top="0" tabpos="298" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\xos\xdef.h" open="1" top="0" tabpos="299" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_sd_update\src\taskSdUpdate.c" open="1" top="0" tabpos="300" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinDialog.c" open="1" top="0" tabpos="301" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_JLT28060B.c" open="1" top="0" tabpos="302" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\Library\JPG\inc\jpg_api.h" open="1" top="0" tabpos="303" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\sys_manage_api.h" open="1" top="0" tabpos="304" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_FPX1002.c" open="1" top="0" tabpos="305" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\video\video_playback.c" open="1" top="0" tabpos="306" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_sd_update\inc\taskSdUpdate.h" open="1" top="0" tabpos="307" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinRect.h" open="1" top="0" tabpos="308" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_SIV100B.c" open="1" top="0" tabpos="309" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\nvfs\inc\nvfs_jpg.h" open="1" top="0" tabpos="310" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\multimedia_api.h" open="1" top="0" tabpos="311" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_audio\src\taskRecordAudioWin.c" open="1" top="0" tabpos="312" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinTips.c" open="1" top="0" tabpos="313" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_SIV120B.c" open="1" top="0" tabpos="314" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_BF3016.c" open="1" top="0" tabpos="315" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_wdt.h" open="1" top="0" tabpos="316" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_video\src\taskPlayVideoMainMsg.c" open="1" top="0" tabpos="317" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="16046" topLine="479" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinItemMenuEx.h" open="1" top="0" tabpos="318" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_OV9732.c" open="1" top="0" tabpos="319" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_NT35510HSD.c" open="1" top="0" tabpos="320" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\msg_api.h" open="1" top="0" tabpos="321" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_NT99141.c" open="1" top="0" tabpos="322" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\fs\src\fs_api.c" open="1" top="0" tabpos="323" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_video\inc\taskPlayVideo.h" open="1" top="0" tabpos="324" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_auadc.c" open="1" top="0" tabpos="325" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_adc.h" open="1" top="0" tabpos="326" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowTips1Msg.c" open="1" top="0" tabpos="327" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_adc.c" open="1" top="0" tabpos="328" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_mjpAEncode.c" open="1" top="0" tabpos="329" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowSelfTestWin.c" open="1" top="0" tabpos="330" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_timer.c" open="1" top="0" tabpos="331" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuDelCurWin.c" open="1" top="0" tabpos="332" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\dusb\src\dusb_enum.c" open="1" top="0" tabpos="333" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\boot\hx330x.ld" open="1" top="0" tabpos="334" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuDelAllMsg.c" open="1" top="0" tabpos="335" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_lcd.h" open="1" top="0" tabpos="336" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.c" open="1" top="0" tabpos="337" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\inc\menu_api.h" open="1" top="0" tabpos="338" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_adc.h" open="1" top="0" tabpos="339" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\video\video_record.h" open="1" top="0" tabpos="340" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\fs\src\ffunicode.c" open="1" top="0" tabpos="341" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinItemMenuOption.c" open="1" top="0" tabpos="342" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_gpio.h" open="1" top="0" tabpos="343" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="user_config\inc\user_config_typedef.h" open="1" top="0" tabpos="344" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinButton.c" open="1" top="0" tabpos="345" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal.h" open="1" top="0" tabpos="346" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_tminf.h" open="1" top="0" tabpos="347" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\windows_api.h" open="1" top="0" tabpos="348" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinWidgetManage.h" open="1" top="0" tabpos="349" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_GC1054.c" open="1" top="0" tabpos="350" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_spr_defs.h" open="1" top="0" tabpos="351" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinStringEx.h" open="1" top="0" tabpos="352" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_api.c" open="1" top="0" tabpos="353" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\Library\AVI\inc\avi_typedef.h" open="1" top="0" tabpos="354" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_video\src\taskRecordVideoWin.c" open="1" top="0" tabpos="355" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="7865" topLine="163" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_music\src\res_music_tab.c" open="1" top="0" tabpos="356" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_BF3a03.c" open="1" top="0" tabpos="357" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_ili9342c.c" open="1" top="0" tabpos="358" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\xos\xmbox.h" open="1" top="0" tabpos="359" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_audio\src\taskRecordAudioMsg.c" open="1" top="0" tabpos="360" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_music\inc\res_music_api.h" open="1" top="0" tabpos="361" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_VGA_OV7725.c" open="1" top="0" tabpos="362" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\xos\xos.h" open="1" top="0" tabpos="363" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\inc\sensor_typedef.h" open="1" top="0" tabpos="364" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_video\src\taskPlayVideoSlideMsg.c" open="1" top="0" tabpos="365" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_H7640.c" open="1" top="0" tabpos="366" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_ili9225G.c" open="1" top="0" tabpos="367" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_play_audio\inc\taskPlayAudio.h" open="1" top="0" tabpos="368" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_tab.c" open="1" top="0" tabpos="369" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\fs\inc\fs_typedef.h" open="1" top="0" tabpos="370" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sWindowAsternWin.c" open="1" top="0" tabpos="371" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_int.c" open="1" top="0" tabpos="372" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_md.c" open="1" top="0" tabpos="373" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuUnlockCurMsg.c" open="1" top="0" tabpos="374" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_wdt.h" open="1" top="0" tabpos="375" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\hal\src\hal_stream.c" open="1" top="0" tabpos="376" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuItemMsg.c" open="1" top="0" tabpos="377" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="3314" topLine="91" />
		</Cursor>
	</File>
	<File name="..\hal\inc\hal_int.h" open="1" top="0" tabpos="378" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_mipi.h" open="1" top="0" tabpos="379" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_mcu_st7789v.c" open="1" top="0" tabpos="380" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_audio\src\taskRecordAudio.c" open="1" top="0" tabpos="381" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\inc\husb_enum.h" open="1" top="0" tabpos="382" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\inc\hx330x_jpg.h" open="1" top="0" tabpos="383" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\key\inc\key_api.h" open="1" top="0" tabpos="384" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\menu_windows\src\sMenuDefaultWin.c" open="1" top="0" tabpos="385" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\src\husb_uvc.c" open="1" top="0" tabpos="386" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.c" open="1" top="0" tabpos="387" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="resource\user_res.h" open="1" top="0" tabpos="388" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\inc\husb_uvc.h" open="1" top="0" tabpos="389" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\video\video_record.c" open="1" top="0" tabpos="390" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\fs\inc\ffconf.h" open="1" top="0" tabpos="391" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinItemMenu.c" open="1" top="0" tabpos="392" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\src\husb_tpbulk.c" open="1" top="0" tabpos="393" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\Library\api_multimedia.h" open="1" top="0" tabpos="394" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="user_config\inc\mbedtls_md5.h" open="1" top="0" tabpos="395" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\src\uiWinFrame.c" open="1" top="0" tabpos="396" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\usb\husb\src\husb_enum.c" open="1" top="0" tabpos="397" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="25125" topLine="705" />
		</Cursor>
	</File>
	<File name="..\dev\lcd\src\lcd_rgb_ota5182.c" open="1" top="0" tabpos="398" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\multimedia\image\image_encode.h" open="1" top="0" tabpos="399" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinItemMenuOption.h" open="1" top="0" tabpos="400" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sensor\src\sensor_dvp_720P_GC1034.c" open="1" top="0" tabpos="401" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\xos\xmsgq.c" open="1" top="0" tabpos="402" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="user_config\src\user_config_tab.c" open="1" top="0" tabpos="403" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\sys_manage\ui_manage\inc\uiWinManage.h" open="1" top="0" tabpos="404" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\dev\sd\inc\sd_typedef.h" open="1" top="0" tabpos="405" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="..\mcu\xos\xmbox.c" open="1" top="0" tabpos="406" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="0" topLine="0" />
		</Cursor>
	</File>
	<File name="task_windows\task_record_video\src\taskRecordVideoMsg.c" open="1" top="0" tabpos="407" split="0" active="1" splitpos="0" zoom_1="0" zoom_2="0">
		<Cursor>
			<Cursor1 position="553" topLine="0" />
		</Cursor>
	</File>
</CodeBlocks_layout_file>
