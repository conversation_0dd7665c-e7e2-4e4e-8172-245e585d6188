/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../hal/inc/hal.h"


#if  MEDIA_VIDEO_DECODE_EN >0


typedef struct JPEG_CACHE_S
{
	u32 mem;
	u32 offset;
	u32 count;
	u32 need_show;
	u32 max_size;
}JPEG_CACHE_T;
  
typedef struct AUDIO_CACHE_S
{
	u32 mem;
	u32 size;
	u32 max_size;
}AUDIO_CACHE_T;
typedef struct VIDEO_PCTRL_S
{
	INT8U 	*jpegcache;
	INT8U 	*audiobuffer;
	
	MSG_T	jpegIStack[JPEG_CFG_CACHE_NUM];
	MSG_T	jpegBStack[JPEG_CFG_CACHE_NUM];
	MSG_T	vidsIStack[VIDEO_CFG_BUFFER_NUM];
	MSG_T	vidsBStack[VIDEO_CFG_BUFFER_NUM];
	MSG_T 	audsIStack[AUDIO_CFG_BUFFER_NUM];
	MSG_T	audsBStack[AUDIO_CFG_BUFFER_NUM];
	
	
	XMsg 	*jpegIdleQ;
	XMsg 	*jpegBusyQ;
	XMsg 	*vidsIdleQ;
	XMsg 	*vidsBusyQ;
	XMsg 	*audsIdleQ;
	XMsg 	*audsBusyQ;
	
	XWork_T *videoSync;
	
	VIDEO_PARG_T 	arg;
	INT16U			src_width;
	INT16U			src_height;
	
	
	INT8U  	stat;
	INT8U  	dacstat;   // 0 :stop,1:start,2:dac auto stop,3:decode auto stop,4:video first frame
	INT8U	play_endsta; //VIDEO_PLAY_END_T
	INT8U  	volume;
	

	INT8U  	vflag;
	INT8S  	playSpeed;
	//INT8U  	prepause;
	INT8U  	syncValue;
	INT8U  	syncCnt;
	
	
	INT32U 	audiolen;
	
	INT32U 	audsoffset;
	
	AUDIO_CACHE_T* 	auds_curframe;
	AUDIO_CACHE_T* 	auds_nexframe;
	INT32U 	vids_lastoffset;
	INT32U 	vids_curframe;   


	INT32U 	PlayFrames;
	

	MSG_T 	*videoframe;
	MSG_T 	*jpegframe;
	MSG_T 	*lcdframe;

#if VIDEO_DBG_PLY_EN
    INT32U dbg_time;
	INT32U dbg_time_cnt;
    INT32U dbg_frame_cnt;
	INT32U dbg_play_time;
	INT32U dbg_ptime;
#endif
}VIDEO_PCTRL_T;


const static INT8S videoSpeedTable[] = {-4,-3,-2, 0, 2, 3, 4};   //注意中间值必须为0
ALIGNED(4) static VIDEO_PCTRL_T videoPCtrl;
ALIGNED(4) static JPEG_CACHE_T videoJpegCache[4];
ALIGNED(4) static JPEG_CACHE_T videoShowCache[5];
ALIGNED(4) static AUDIO_CACHE_T audioCache[AUDIO_CFG_BUFFER_NUM];
/*******************************************************************************
* Function Name  : playback_vidsMalloc
* Description	 : video vids frame malloc
* Input 		 : None
* Output		 : none 										   
* Return		 : JPEG_CACHE_T * : NULL fail
*******************************************************************************/
static JPEG_CACHE_T * playback_vidsMalloc(void)
{
    INT8U err;
    JPEG_CACHE_T * p = (JPEG_CACHE_T *)XMsgQPend(videoPCtrl.vidsIdleQ,&err); 
    if(err != X_ERR_NONE)
        p = NULL;
    else
    {
        u32 mem = (u32)hal_lcdVideoIdleFrameMalloc();
        if(mem)
            p->mem = mem;
        else
        {
            XMsgQPost(videoPCtrl.vidsIdleQ,(MSG_T *)p);
            p = NULL;
        }   
    }
    
    return p;
}
/*******************************************************************************
* Function Name  : playback_vidsMalloc
* Description	 : video vids frame malloc
* Input 		 : None
* Output		 : none 										   
* Return		 : JPEG_CACHE_T * : NULL fail
*******************************************************************************/
static void playback_vidsFree(JPEG_CACHE_T * frame)
{
    if(frame)
    {
        hal_dispframeFree((lcdshow_frame_t *)(frame->mem));
		frame->mem 		= 0;
		frame->offset 	= 0;
        frame->count 	= 0;
        XMsgQPost(videoPCtrl.vidsIdleQ,(MSG_T *)frame);
    }
}
/*******************************************************************************
* Function Name  : videoPlayBack_read
* Description	 : videoPlayback read from file
* Input 		 : void *buffer,INT32U offset,INT32U len 
* Output		 : none 										   
* Return		 : int: <0 fail, 
*                       >0 success return read bytes cnt
*******************************************************************************/
static int videoPlayBack_read(void *buffer,INT32U offset,INT32U len)
{
	int cnt;
	if(fs_seek(videoPCtrl.arg.avi_arg.fd,offset,0) < 0)
		return -1;
	cnt = fs_read(videoPCtrl.arg.avi_arg.fd,buffer,len);
	if(cnt < 0)
		return -1;
	return cnt;
}
/*******************************************************************************
* Function Name  : videoPlaybackSync
* Description	 : video play back sync, callback by timer interrupt
* Input 		 : None
* Output		 : none 										   
* Return		 : none
*******************************************************************************/
static void videoPlaybackSync(void)
{
	if(videoPCtrl.stat == MEDIA_STAT_PLAY)
	{
		videoPCtrl.syncCnt++;
		if(videoPCtrl.syncCnt<videoPCtrl.syncValue)
			return;
		videoPCtrl.syncCnt = 0;
		videoPCtrl.vflag++;
#if VIDEO_DBG_PLY_EN	
		videoPCtrl.dbg_ptime++;
#endif
	}
}
/*******************************************************************************
* Function Name  : videoPlaybackDacISR
* Description	 : videoPlayback dac isr callback
* Input 		 : int flag: 
* Output		 : none 										   
* Return		 : none
*******************************************************************************/
static void videoPlaybackDacISR(int flag)
{
	INT8U err;
	if(flag & DAC_INT_PEND)
    {
		if(videoPCtrl.auds_nexframe)
			hal_dacFlush(videoPCtrl.auds_nexframe->size);
		if(videoPCtrl.auds_curframe)
		{
			XMsgQPost(videoPCtrl.audsIdleQ,(AUDIO_CACHE_T *)videoPCtrl.auds_curframe);
		}
		videoPCtrl.auds_curframe = videoPCtrl.auds_nexframe;
		videoPCtrl.auds_nexframe = (AUDIO_CACHE_T*)NULL;
		return;
    }

	if((videoPCtrl.dacstat & PLAY_DACSTA_MASK) == PLAY_DACSTA_STOP)
	{
		if((flag & DAC_INT_EMPTY)==0)
			return;
		if(videoPCtrl.auds_curframe)
			XMsgQPost(videoPCtrl.audsIdleQ,(AUDIO_CACHE_T *)videoPCtrl.auds_curframe);
		videoPCtrl.auds_curframe = (AUDIO_CACHE_T*)NULL;
		hal_dacPlayStop();
	    videoPCtrl.dacstat = PLAY_DACSTA_STOP;
	    videoPCtrl.stat	   = MEDIA_STAT_PAUSE;
	    sd_api_unlock();
		return ;
	}
	
	if(flag & DAC_INT_HALF)
	{
		if(videoPCtrl.auds_nexframe == 0)
		{
	        videoPCtrl.auds_nexframe = (AUDIO_CACHE_T*)XMsgQPend(videoPCtrl.audsBusyQ,&err); // get from busy q
			if(err == X_ERR_NONE)
			{
				hal_dacSetBuffer((INT32U)videoPCtrl.auds_nexframe->mem+8,videoPCtrl.auds_nexframe->size);
				videoPCtrl.audiolen = videoPCtrl.auds_nexframe->size;
			}
		    else 
				videoPCtrl.auds_nexframe = (AUDIO_CACHE_T*)NULL;	
		}
	}
	else if(flag & DAC_INT_EMPTY)
	{
		if((videoPCtrl.dacstat & PLAY_DACSTA_MASK) == PLAY_DACSTA_WAITSTOP)
			videoPlaybackStop();
		else
		{
			hal_dacPlayStop();
			if(videoPCtrl.auds_curframe)
				XMsgQPost(videoPCtrl.audsIdleQ,(AUDIO_CACHE_T *)videoPCtrl.auds_curframe);
			if(videoPCtrl.auds_nexframe)
				XMsgQPost(videoPCtrl.audsIdleQ,(AUDIO_CACHE_T *)videoPCtrl.auds_nexframe);
			videoPCtrl.auds_curframe = (AUDIO_CACHE_T*)NULL;
			videoPCtrl.auds_nexframe = (AUDIO_CACHE_T*)NULL;
		    videoPCtrl.dacstat = (videoPCtrl.dacstat & ~PLAY_DACSTA_MASK)|PLAY_DACSTA_STOP;
			VIDEOPLY_DBG("N");
		}
	}
}

/*******************************************************************************
* Function Name  : videoPlaybackClear
* Description    : clear screen
* Input          : INT8U *ybuffer : y buffer
				   INT8U *uvbuffer: uv buffer
				   INT16U width   : width
				   INT16U height : height
				   INT8U y : y-value
				   INT8U uv : uv-value
* Output         : none
* Return         : int 
*******************************************************************************/
void videoPlaybackClear(INT8U *ybuffer,INT8U *uvbuffer,INT16U width,INT16U height,INT8U y,INT8U uv)
{
	memset(ybuffer,y,width*height);
	memset(uvbuffer,uv,width*height/2);
}
/*******************************************************************************
* Function Name  : videoPlaybackInit
* Description    : initial video Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoPlaybackInit(void)
{   
	memset(&videoPCtrl, 0, sizeof(videoPCtrl));
	videoPCtrl.arg.avi_arg.avi_cache_len	= AVI_CFG_IDX1_BUFF_SIZE;
    videoPCtrl.arg.avi_arg.avi_cache 		= (INT8U *)hal_sysMemMalloc(AVI_CFG_IDX1_BUFF_SIZE);
	if(videoPCtrl.arg.avi_arg.avi_cache == NULL)
	{
		VIDEOPLY_DBG("[Video PLY]: cache malloc fail\n");
		return STATUS_FAIL;
	}
	videoPCtrl.stat 		= MEDIA_STAT_STOP;
	videoPCtrl.volume 		= 100;
	
	videoPCtrl.jpegIdleQ 	= XMsgQCreate(videoPCtrl.jpegIStack,JPEG_CFG_CACHE_NUM);
	videoPCtrl.jpegBusyQ	= XMsgQCreate(videoPCtrl.jpegBStack,JPEG_CFG_CACHE_NUM);	
	
	videoPCtrl.vidsIdleQ 	= XMsgQCreate(videoPCtrl.vidsIStack,VIDEO_CFG_BUFFER_NUM);
	videoPCtrl.vidsBusyQ 	= XMsgQCreate(videoPCtrl.vidsBStack,VIDEO_CFG_BUFFER_NUM);
	
    videoPCtrl.audsIdleQ 	= XMsgQCreate(videoPCtrl.audsIStack,AUDIO_CFG_BUFFER_NUM);
	videoPCtrl.audsBusyQ 	= XMsgQCreate(videoPCtrl.audsBStack,AUDIO_CFG_BUFFER_NUM);

	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : avideoPlaybackUninit
* Description    : uninitial video Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoPlaybackUninit(void)
{
	if(videoPCtrl.stat != MEDIA_STAT_STOP)
		videoPlaybackStop();	
	videoPCtrl.stat = MEDIA_STAT_STOP;
#if MEDIA_CFG_SYNC_TIMER_EN == 0	
	XWorkDestory(videoPCtrl.videoSync);
	videoPCtrl.videoSync = NULL;
#else
    hal_timerStop(MEDIA_CFG_TIMER);
#endif
    
	
	XMsgQDestory(videoPCtrl.vidsIdleQ);
    XMsgQDestory(videoPCtrl.audsIdleQ);
	XMsgQDestory(videoPCtrl.audsBusyQ);
    XMsgQDestory(videoPCtrl.vidsBusyQ);
	XMsgQDestory(videoPCtrl.jpegIdleQ);
    XMsgQDestory(videoPCtrl.jpegBusyQ);
	sd_api_unlock();

    if(videoPCtrl.arg.avi_arg.avi_cache)
		hal_sysMemFree(videoPCtrl.arg.avi_arg.avi_cache);
    if(videoPCtrl.jpegcache)
		hal_sysMemFree(videoPCtrl.jpegcache);
	if(videoPCtrl.audiobuffer)
		hal_sysMemFree(videoPCtrl.audiobuffer);
	
	videoPCtrl.arg.avi_arg.avi_cache_len   	= 0;
	videoPCtrl.arg.avi_arg.avi_cache 		= NULL;
	videoPCtrl.jpegcache 					= NULL;
	videoPCtrl.audiobuffer					= NULL;

	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoPlaybackDecodeWait
* Description    : videoPlaybackDecodeWait, wait jpg decode until timeout 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoPlaybackDecodeWait(int timeout)
{
	int curtime;
	curtime = XOSTimeGet();
	while(hal_mjpDecodeBusyCheck())
	{
		if((curtime+timeout)<XOSTimeGet())
		{
			//VIDEOPLY_DBG("[Video PLY]: timeout\n");
			return STATUS_FAIL;		
		}
	}
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : videoPlaybackStart
* Description    : Start video Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int videoPlaybackStart(VIDEO_PARG_T *arg)
{
	int ret,i,size,num=0;
	
    if(arg == NULL)
		return STATUS_FAIL;
	
	if(arg->avi_arg.src_type != MEDIA_SRC_FS)
	{
		return STATUS_FAIL;
	}
	arg->avi_arg.avi_cache 		= videoPCtrl.arg.avi_arg.avi_cache;
	arg->avi_arg.avi_cache_len 	= videoPCtrl.arg.avi_arg.avi_cache_len;
	ret = api_multimedia_init(MEDIA_DECODE, MEDIA_AVI_ODML);
	if(ret < 0 )
	{
		VIDEOPLY_DBG("[Video PLY]: api init fail\n");
		return STATUS_FAIL;	
	}
	arg->avi_arg.media_ch = ret;
	videoPCtrl.arg.avi_arg.media_ch = ret;
	ret = api_multimedia_start(arg->avi_arg.media_ch, (void *)&arg->avi_arg);
	if(ret<0)
	{
		VIDEOPLY_DBG("[Video PLY]: parse fail:%d\n",ret);
		goto VIDEOPLY_START_FAIL;
	}
	ret = api_multimedia_getArg(arg->avi_arg.media_ch, (void *)&arg->avi_arg);
	if(ret < 0)
	{
		VIDEOPLY_DBG("[Video PLY]: get Arg Fail\n");
		goto VIDEOPLY_START_FAIL;		
	}
	if((arg->tar_width > arg->avi_arg.width || arg->tar_height  > arg->avi_arg.height))
	{
		arg->tar_width = hx330x_min(arg->tar_width, arg->avi_arg.width);
		arg->tar_height = hx330x_min(arg->tar_height, arg->avi_arg.height);	
		hal_lcdSetVideoRatioResolution(arg->tar_width, arg->tar_height);
		

	}	
	//deg_Printf("frametime:%d, framecnt:%d,stream_num:%d\n",arg->avi_arg.frametime,arg->avi_arg.framecnt,arg->avi_arg.stream_num);
	//deg_Printf("width:%d, height:%d,samplerate:%d\n",arg->avi_arg.width,arg->avi_arg.height,arg->avi_arg.samplerate);
	
	hx330x_bytes_memcpy((void*)&videoPCtrl.arg,(void*)arg,sizeof(VIDEO_PARG_T));
	
	if(videoPCtrl.audiobuffer == NULL) // malloc audio play buffer
		videoPCtrl.audiobuffer = (INT8U *)hal_sysMemMalloc(AUDIO_CFG_BUFFER_NUM*AUDIO_CFG_BUFFER_SIZE);//audioCache;

    ret = size = (hal_sysMemRemain()- 8192L)&~0x1f;
	if(ret >= JPEG_CFG_CACHE_SIZE)
	{
		num = JPEG_CFG_CACHE_NUM;
		size = (ret/num)&~0x1f;
		videoPCtrl.jpegcache = (INT8U *)hal_sysMemMalloc(size * num);

	}
	else
	{
		videoPCtrl.jpegcache = NULL;
	}
	
	if(videoPCtrl.jpegcache == NULL || videoPCtrl.audiobuffer == NULL)
	{
		VIDEOPLY_DBG("[Video PLY]: memory fail:%x, %x\n",(INT32U)videoPCtrl.jpegcache,(INT32U)videoPCtrl.audiobuffer);
		goto VIDEOPLY_START_FAIL;
	}
	VIDEOPLY_DBG("[Video PLY]:vids num = %d, size = %dkb\n", num, size/1024);
    sd_api_lock();
 
//-------------initial play queue
	XMsgQFlush(videoPCtrl.jpegIdleQ);
	XMsgQFlush(videoPCtrl.jpegBusyQ);
	XMsgQFlush(videoPCtrl.vidsIdleQ);
	XMsgQFlush(videoPCtrl.vidsBusyQ);
    XMsgQFlush(videoPCtrl.audsIdleQ);
	XMsgQFlush(videoPCtrl.audsBusyQ);	
	
	for(i=0;i< num;i++)
	{
		ret = (int)(videoPCtrl.jpegcache+i*size);
		videoJpegCache[i].mem = ret;	
		videoJpegCache[i].max_size = size;
		XMsgQPost(videoPCtrl.jpegIdleQ,(MSG_T *)&videoJpegCache[i]);
		//VIDEOPLY_DBG("[Video PLY]: jpg cache[%d] = %x\n",i,ret);
	}	
	
	for(i=0;i<VIDEO_CFG_BUFFER_NUM;i++)
	{
		videoShowCache[i].mem = 0;
		XMsgQPost(videoPCtrl.vidsIdleQ,(MSG_T *)&videoShowCache[i]);
	}
	for(i=0;i<AUDIO_CFG_BUFFER_NUM;i++)
	{
		ret = (int)(videoPCtrl.audiobuffer+i*AUDIO_CFG_BUFFER_SIZE);
		audioCache[i].mem = ret;
		audioCache[i].max_size = AUDIO_CFG_BUFFER_SIZE;
		audioCache[i].size = 0;
		XMsgQPost(videoPCtrl.audsIdleQ,(MSG_T *)&audioCache[i]);		
	}
    
	//------------initial video frame sync

#if MEDIA_CFG_SYNC_TIMER_EN == 0
	if(videoPCtrl.videoSync==NULL)
		videoPCtrl.videoSync = XWorkCreate(1000/videoPCtrl.arg.avi_arg.fps,videoPlaybackSync); 
#else
    ret = videoPCtrl.arg.avi_arg.fps;
    if(TIMER_FRQ_MIN>ret)
    {
		i = TIMER_FRQ_MIN/ret;
		if((ret*i)<TIMER_FRQ_MIN)
			i++;
		videoPCtrl.syncValue = i;
		ret = i*ret;
    }
	else
	{
		videoPCtrl.syncValue = 1;
	}
    if(hal_timerStart(MEDIA_CFG_TIMER,ret,videoPlaybackSync) == false)
    {
		VIDEOPLY_DBG("[Video PLY]: sync time start fail %d\n",ret);
		goto VIDEOPLY_START_FAIL;
    }
#endif

	videoPCtrl.stat      		= MEDIA_STAT_PLAY;	
	videoPCtrl.dacstat  		= PLAY_DACSTA_VFIRST;  // video first frame flag
	videoPCtrl.vflag    		= 1;

	videoPCtrl.playSpeed 		= ARRAY_NUM(videoSpeedTable)/2;
	//videoPCtrl.prepause 		= 0;
	videoPCtrl.syncCnt			= 0;
	videoPCtrl.audiolen			= 0;
	videoPCtrl.audsoffset 		= 0;
	videoPCtrl.auds_curframe	= 0;
	videoPCtrl.auds_nexframe 	= 0;
	videoPCtrl.vids_lastoffset	= 0;	
	videoPCtrl.vids_curframe	= 0;
    videoPCtrl.PlayFrames		= 0;
	videoPCtrl.videoframe		= NULL;
	videoPCtrl.jpegframe 		= NULL;
	videoPCtrl.lcdframe 		= NULL;
#if VIDEO_DBG_PLY_EN
	videoPCtrl.dbg_time 		= 0;
	videoPCtrl.dbg_time_cnt 	= 0;
    videoPCtrl.dbg_frame_cnt	= 0;
	videoPCtrl.dbg_play_time	= XOSTimeGet();
	videoPCtrl.dbg_ptime 		= 0;
#endif
    
//-------------PLAY FIRST FRAME-------------------------	
	//if(videoPCtrl.arg.firstframe)
	//{
	//	videoPlaybackService();
	//}
	VIDEOPLY_DBG("[Video PLY]: start, vframe_time %dms\n",ret);
	return STATUS_OK;
VIDEOPLY_START_FAIL:
	api_multimedia_uninit(videoPCtrl.arg.avi_arg.media_ch);
	videoPCtrl.arg.avi_arg.media_ch = -1;
	hal_sysMemFree(videoPCtrl.jpegcache);
	hal_sysMemFree(videoPCtrl.audiobuffer);
	videoPCtrl.jpegcache 	= NULL;
	videoPCtrl.audiobuffer 	= NULL;	
	return STATUS_FAIL;
}
/*******************************************************************************
* Function Name  : videoPlaybackStop
* Description    : Stop video Playback 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void videoPlaybackStop(void)
{

	INT8U err = X_ERR_NONE;
    HAL_CRITICAL_INIT();
	HAL_CRITICAL_ENTER();
	deg_Printf("stop stat:%d\n",videoPCtrl.stat);	
	if(videoPCtrl.stat == MEDIA_STAT_STOP)
	{
		HAL_CRITICAL_EXIT();
		return;
	}
	videoPCtrl.PlayFrames = 0;
    hal_dacPlayStop();
	hal_mjpDecodeStop();
	hal_dacCallBackRegister(NULL);
    fs_close(videoPCtrl.arg.avi_arg.fd);
	api_multimedia_uninit(videoPCtrl.arg.avi_arg.media_ch);
	videoPCtrl.arg.avi_arg.media_ch = -1;
	videoPCtrl.stat = MEDIA_STAT_STOP;
    sd_api_unlock();
	hal_sysMemFree(videoPCtrl.jpegcache);
	hal_sysMemFree(videoPCtrl.audiobuffer);
	videoPCtrl.jpegcache 	= NULL;
	videoPCtrl.audiobuffer 	= NULL;
    
    playback_vidsFree((JPEG_CACHE_T *)videoPCtrl.videoframe);
    
    while(err == X_ERR_NONE)
    {
        JPEG_CACHE_T * video = (JPEG_CACHE_T *)XMsgQPend(videoPCtrl.vidsBusyQ,&err);
        if(err == X_ERR_NONE)
            playback_vidsFree(video);
    }
    
#if MEDIA_CFG_SYNC_TIMER_EN == 0	
	XWorkDestory(videoPCtrl.videoSync);
	videoPCtrl.videoSync = NULL;
#else
    hal_timerStop(MEDIA_CFG_TIMER);
#endif
#if VIDEO_DBG_PLY_EN	
	videoPCtrl.dbg_play_time = XOSTimeGet()-videoPCtrl.dbg_play_time;
	VIDEOPLY_DBG("[Video PLY]: play time [%d:%d:%d]\n",(videoPCtrl.dbg_play_time/1000)/60,(videoPCtrl.dbg_play_time/1000)%60,videoPCtrl.dbg_play_time%1000);
#endif
	VIDEOPLY_DBG("[Video PLY]: stop\n"); 
	HAL_CRITICAL_EXIT();
	return;
}
/*******************************************************************************
* Function Name  : videoPlaybackPause
* Description    : Pause video Playback 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void videoPlaybackPause(void)
{
	if(videoPCtrl.stat != MEDIA_STAT_PLAY)
	{
		return;
	}
	if( (videoPCtrl.dacstat & PLAY_DACSTA_MASK) == PLAY_DACSTA_STOP )		// first frame , will pause right now
	{
		hal_dacPlayStop();	
		videoPCtrl.dacstat = PLAY_DACSTA_STOP;
		videoPCtrl.stat    = MEDIA_STAT_PAUSE;
		sd_api_unlock();
	}
	else
	{
    	videoPCtrl.dacstat = PLAY_DACSTA_STOP;

	}
	videoPCtrl.playSpeed = ARRAY_NUM(videoSpeedTable)/2;//wxn-为了快进快退时按暂停隐藏倍数字符23.8.1
    VIDEOPLY_DBG("[Video PLY]: pause\n");
}
/*******************************************************************************
* Function Name  : videoPlaybackResume
* Description    : Resume video Playback 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void videoPlaybackResume(void)
{
	if(videoPCtrl.stat != MEDIA_STAT_PAUSE)
	{
		return;
	}	
	//videoPCtrl.prepause = 0;
	sd_api_lock();
	videoPCtrl.playSpeed = ARRAY_NUM(videoSpeedTable)/2;
	api_multimedia_decodefast(videoPCtrl.arg.avi_arg.media_ch, videoSpeedTable[videoPCtrl.playSpeed]);
	videoPCtrl.stat = MEDIA_STAT_PLAY;
	VIDEOPLY_DBG("[Video PLY]: resume\n");
}
/*******************************************************************************
* Function Name  : videoPlaybackGetStatus
* Description    : get video Playback 
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int videoPlaybackGetStatus(void)
{
	return videoPCtrl.stat;
}



int videoPlaybackGetSpeed(void)
{
	return videoPCtrl.playSpeed;
}

/*******************************************************************************
* Function Name  : audioPlaybackSetVolume
* Description    : set audio Playback volume
* Input          : INT8U volume : 0-100
* Return         : none
*******************************************************************************/
void videoPlaybackSetVolume(INT8U volume)
{
	if(volume>100)
		volume = 100;
	videoPCtrl.volume = volume;
	if(videoPCtrl.stat == MEDIA_STAT_PLAY)
        hal_dacSetVolume(volume);
}
/*******************************************************************************
* Function Name  : audioPlaybackGetVolume
* Description    : get audio Playback volume
* Input          : 
* Return         : INT8U volume : 0-100 
                      
*******************************************************************************/
INT8U videoPlaybackGetVolume(void)
{
	return videoPCtrl.volume;
}

/*******************************************************************************
* Function Name  : videoPlaybackGetTime
* Description    : get video Playback time ms
* Input          : INT32U *total : total time
				   INT32U *curr : current time
* Output         : INT32U *total : total time
				   INT32U *curr : current time
* Return         : int 
*******************************************************************************/
int videoPlaybackGetTime(INT32U *total,INT32U *curr)
{
	return api_multimedia_gettime(videoPCtrl.arg.avi_arg.media_ch, (int*)total,(int*)curr);
}
/*******************************************************************************
* Function Name  : videoPlaybackGetResolution
* Description    : get video Playback resolution
* Input          : none
* Output         : none
* Return         : Media_Res_T *                       
*******************************************************************************/
//Media_Res_T *videoPlaybackGetResolution(void)
//{
//	return &videoPCtrl.src;
//}
AVI_DEC_ARG* videoPlaybabkGetArg(void)
{
	return &videoPCtrl.arg.avi_arg;
}
/*******************************************************************************
* Function Name  : videoGetFirstFrame
* Description    : video decode first frame
* Input          : VIDEO_PARG_T *arg
* Output         : none
* Return         : int: <0 fail, 0 success                      
*******************************************************************************/
int videoGetFirstFrame(VIDEO_PARG_T *arg, u32 * offset, u32* length)
{
	int ret;
	INT32U offset_temp,length_temp = 0;
	
	if(arg == NULL)
		return STATUS_FAIL;

	if(arg->avi_arg.src_type != MEDIA_SRC_FS)
		return STATUS_FAIL;
	ret = api_multimedia_init(MEDIA_DECODE, MEDIA_AVI_ODML);
	if(ret < 0)
	{
		VIDEOPLY_DBG("[Video PLY]: api init fail\n");
		return STATUS_FAIL;		
	}
	arg->avi_arg.media_ch = ret;
	arg->avi_arg.avi_cache_len	= AVI_CFG_IDX1_BUFF_SIZE;
    arg->avi_arg.avi_cache  	= (INT8U *)hal_sysMemMalloc(AVI_CFG_IDX1_BUFF_SIZE);
	ret = api_multimedia_start(arg->avi_arg.media_ch, (void *)&arg->avi_arg);
	if(ret<0)
	{
		VIDEOPLY_DBG("[Video PLY]: start fail\n");
		ret = STATUS_FAIL;
		goto GetFirstFrameEnd;
	}
	ret = api_multimedia_getArg(arg->avi_arg.media_ch, (void *)&arg->avi_arg);
	if(ret < 0)
	{
		VIDEOPLY_DBG("[Video PLY]: get Arg Fail\n");
		ret = STATUS_FAIL;
		goto GetFirstFrameEnd;		
	}
	ret = api_multimedia_decodeframe(arg->avi_arg.media_ch, NULL, (int*)&offset_temp,(int*)&length_temp,AVI_FRAME_DC_VIDEO);
	if(ret < 0 || length_temp == 0)
	{
		do{
			hal_wdtClear();
			ret = api_multimedia_service(arg->avi_arg.media_ch);
			if(ret < 0)
			{
				deg_Printf("videoGetFirstFrame: NO frame\n");
				ret = STATUS_FAIL;
				goto GetFirstFrameEnd;
			}
			ret = api_multimedia_decodeframe(arg->avi_arg.media_ch, NULL, (int*)&offset_temp,(int*)&length_temp,AVI_FRAME_DC_VIDEO);
		}while(ret < 0 || length_temp == 0);
	}
	deg_Printf("ret:%d, offset:%x, length:%x\n",ret, offset_temp, length_temp);
	if(ret<0)
	{
		ret = STATUS_FAIL;
		goto GetFirstFrameEnd;	
	}	
	if(offset)
	{
		*offset = offset_temp + 8;
	}
	if(length)
	{
		*length = length_temp;
	}
	ret = STATUS_OK;
GetFirstFrameEnd:
	api_multimedia_uninit(arg->avi_arg.media_ch);
	if(arg->avi_arg.avi_cache)
	{
		hal_sysMemFree((void*)arg->avi_arg.avi_cache);
		arg->avi_arg.avi_cache = NULL;
	}
	arg->avi_arg.media_ch = -1;
	return ret;
}
/*******************************************************************************
* Function Name  : videoDecodeFirstFrame
* Description    : video decode first frame
* Input          : VIDEO_PARG_T *arg
* Output         : none
* Return         : int: <0 fail, 0 success                      
*******************************************************************************/
int videoDecodeFirstFrame(VIDEO_PARG_T *arg)
{
	int ret;
	INT32U offset,length;
	u8* jpegBuff;
	JPG_DEC_ARG jpg_arg;
	
	if(arg == NULL)
		return STATUS_FAIL;
	ret = videoGetFirstFrame(arg, &offset, &length);
	if(ret < 0)
	{
		return STATUS_FAIL;
	}
	jpegBuff = (u8*)hal_sysMemMalloc(length+8);
	if(jpegBuff == NULL)
	{
		return STATUS_FAIL;
	}	
	ret = videoPlayBack_read((void *)jpegBuff, offset - 8, length+8);
	if(ret<0)
	{
		hal_sysMemFree(jpegBuff);
		return STATUS_FAIL;
	}	
	//debgbuf(jpegBuff,32);
	jpg_arg.type 		= MEDIA_FILE_JPG;
	jpg_arg.wait 		= 1;
	jpg_arg.fd 			= -1;
	jpg_arg.src_type	= MEDIA_SRC_RAM;	
	jpg_arg.dst_width	= arg->tar_width;
	jpg_arg.dst_height	= arg->tar_height;
	jpg_arg.jpgbuf		= jpegBuff + 8;
	jpg_arg.jpgsize		= length;
	jpg_arg.yout  		= arg->yout;
	jpg_arg.uvout 		= arg->uvout;
	jpg_arg.step_yout 	= NULL;	
	jpg_arg.p_lcd_buffer = NULL;
	ret = imageDecodeStart(&jpg_arg);
	hal_sysMemFree(jpegBuff);
	return ret;
}

/*******************************************************************************
* Function Name  : videoPlaybackGetStatus
* Description    : get video Playback 
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int videoPlaybackService(void)
{
	INT8U err;
	INT32U offset,length,addr;
	int ret,res;
	lcdshow_frame_t * p_lcd_buffer;
	JPEG_CACHE_T *jpeg,*video;
	AUDIO_CACHE_T *audio;
    if(videoPCtrl.stat != MEDIA_STAT_PLAY)
		return videoPCtrl.stat;
SERVICE_GET_INDEX:
    videoPCtrl.play_endsta = VIDEOPLAY_NOT_END;

    if((videoPCtrl.dacstat & PLAY_DACSTA_MASK) != PLAY_DACSTA_WAITSTOP) // decode avi
    {   
		 ret = api_multimedia_service(videoPCtrl.arg.avi_arg.media_ch);
		 if(ret < 0)
		 {
		 	videoPCtrl.play_endsta |= VIDEOPLAY_IDX1_END;
			//deg_Printf("***********inx1 end\n");
		 }
//-------cache jpeg data
        jpeg = (JPEG_CACHE_T *)XMsgQPend(videoPCtrl.jpegIdleQ,&err);
        if(err == X_ERR_NONE)
        {
        	videoPCtrl.PlayFrames++;
			ret = api_multimedia_decodeframe(videoPCtrl.arg.avi_arg.media_ch, NULL, (int*)&offset,(int*)&length,AVI_FRAME_DC_VIDEO);
		    if((ret<0)||(videoPCtrl.vflag > 6))// // get video frame fail, may frame end or lcd fps < avi fps
			{
				if(ret<0)
				{
				    videoPCtrl.play_endsta |= VIDEOPLAY_VIDS_END;  //maybe video frame end
					//deg_Printf("***********vids end\n");
				}else
				{
					VIDEOPLY_DBG("d");
					videoPCtrl.vflag--;
				}
	            XMsgQPost(videoPCtrl.jpegIdleQ,(MSG_T *)jpeg);
				goto SERVICE_VIDEO_DECODE;	
			}
			else if((videoPCtrl.vids_lastoffset == offset)|| (0 == length)) // same frame
			{	
				//videoPCtrl.PlayFrames++;
				XMsgQPost(videoPCtrl.jpegIdleQ,(MSG_T *)jpeg);	
				if(videoPCtrl.dacstat &  PLAY_DACSTA_VFIRST)
				{
					goto SERVICE_GET_INDEX;
				}
			#if VIDEO_CFG_JUMP_SAMEFRAME
			    if(length == 0)
					offset = videoPCtrl.vids_lastoffset;
			    jpeg = (JPEG_CACHE_T *)videoPCtrl.jpegframe;
			    if((jpeg) && (jpeg->offset == offset))
					jpeg->count++;
				else
				{
					res = XMsgQCheck(videoPCtrl.jpegBusyQ);
					while(res--)
					{
						jpeg = (JPEG_CACHE_T *)XMsgQPend(videoPCtrl.jpegBusyQ,&err);
						if((jpeg) && (jpeg->offset == offset))
						{
							jpeg->count++;
							//break;
						}
						XMsgQPost(videoPCtrl.jpegBusyQ,(MSG_T *)jpeg);
					}
				}
			#endif	
			    
				goto SERVICE_VIDEO_DECODE;	
			}
			else
		    {
				//deg_Printf("offset:%x, length:%x\n", offset, length);
				if(jpeg->max_size < (length + 8))
				{
					VIDEOPLY_DBG("length overflow:%d,%d\n", length, jpeg->max_size);
			        XMsgQPost(videoPCtrl.jpegIdleQ,(MSG_T *)jpeg);
					if(videoPCtrl.dacstat &  PLAY_DACSTA_VFIRST)
					{
						goto SERVICE_GET_INDEX;
					}
					goto SERVICE_VIDEO_DECODE;				
				}
				res = videoPlayBack_read((void *)jpeg->mem, offset, length+8);
				if(res<0)
				{
					goto VIDEO_PLAYBACK_ERROR;
				}	
			    jpeg->offset = offset;
			    jpeg->count  = 0;
				XMsgQPost(videoPCtrl.jpegBusyQ,(MSG_T *)jpeg);
				videoPCtrl.vids_lastoffset 	= offset;
				videoPCtrl.vids_curframe 	= (INT32U)jpeg;
			}
        }

//mjpeg decode
SERVICE_VIDEO_DECODE:
		if(!(videoPCtrl.dacstat &  PLAY_DACSTA_VFIRST))
		{
			if(hal_mjpDecodeBusyCheck()) // decode busy
				goto SERVICE_AUDIO_DECODE;
			ret = hal_mjpDecodeErrorCheck();
			if(ret!=0) // decode fail.
			{
				VIDEOPLY_DBG("[Video PLY]: decode err:%d, offset =0x%x, %x\n",ret,(INT32U)videoPCtrl.vids_lastoffset,(INT32U)videoPCtrl.jpegframe);
				playback_vidsFree((JPEG_CACHE_T *)videoPCtrl.videoframe);
				videoPCtrl.PlayFrames++;
				videoPCtrl.videoframe = NULL;
			}
			else if(videoPCtrl.videoframe!=NULL)// decode successful.put in showing queue
			{
				XMsgQPost(videoPCtrl.vidsBusyQ,(MSG_T *)videoPCtrl.videoframe);
				videoPCtrl.videoframe = NULL;
				
			#if VIDEO_DBG_PLY_EN		    
				videoPCtrl.dbg_time_cnt += XOSTimeGet()-videoPCtrl.dbg_time;
				videoPCtrl.dbg_frame_cnt++;
			#endif
			}   	 
		}
	//-----release	
		offset = 0;
		if(videoPCtrl.jpegframe)// && ((((INT32U)videoPCtrl.jpegframe)&0x0f)==0))// last jpeg cache using end,back to idle queue
		{
		#if VIDEO_CFG_JUMP_SAMEFRAME
		    jpeg = (JPEG_CACHE_T *)videoPCtrl.jpegframe;
		    if(jpeg->count)
		    {
				jpeg->count--;
		    }
			else
			{
		        offset = (INT32U)videoPCtrl.jpegframe;	
				videoPCtrl.jpegframe = NULL;
			}
		#else
		    XMsgQPost(videoPCtrl.jpegIdleQ,(MSG_T *)videoPCtrl.jpegframe);	
		    videoPCtrl.jpegframe = NULL;	
		#endif										
		}
	//-------register	
#if VIDEO_CFG_JUMP_SAMEFRAME		
		if(videoPCtrl.jpegframe)
		{
            jpeg = (JPEG_CACHE_T *)videoPCtrl.jpegframe;    
			video = (JPEG_CACHE_T *)videoPCtrl.lcdframe;
			if(video && (video->offset == jpeg->offset))
			{
				video->count++;
				goto SERVICE_AUDIO_DECODE;
			}
			res = XMsgQCheck(videoPCtrl.vidsBusyQ);
			ret = 0;
			while(res)
			{
				video = (JPEG_CACHE_T *)XMsgQPend(videoPCtrl.vidsBusyQ,&err);
				if(video && (video->offset == jpeg->offset))
				{
					video->count++;
					ret = 1;
				}
				XMsgQPost(videoPCtrl.vidsBusyQ,(MSG_T *)video);
				res--;
			}
			if(ret)
				goto SERVICE_AUDIO_DECODE;
		}
		else
#endif			
		{			
			jpeg = (JPEG_CACHE_T *)XMsgQPend(videoPCtrl.jpegBusyQ,&err); //get jpeg cache buffer
			if(err!=X_ERR_NONE)// no jpeg cached
			{	
			#if VIDEO_CFG_JUMP_SAMEFRAME		
				if(videoPCtrl.jpegframe)
				{
					((JPEG_CACHE_T *)videoPCtrl.jpegframe)->count++;
				}
				else
				{
					videoPCtrl.jpegframe = (MSG_T *)offset;		
				}
			#endif	
			    
				goto SERVICE_AUDIO_DECODE;
			}
		}
        video = playback_vidsMalloc();
		if(!video)// no idle showing buffer
		{
		#if VIDEO_CFG_JUMP_SAMEFRAME		
			if(videoPCtrl.jpegframe)
			{	
				jpeg->count++;	
			}
			else
			{
				XMsgQPostFront(videoPCtrl.jpegBusyQ,(MSG_T *)jpeg);	 
				videoPCtrl.jpegframe = (MSG_T *)offset;
			}
		#else
		    XMsgQPostFront(videoPCtrl.jpegBusyQ,(MSG_T *)jpeg);	 
       	#endif
		    
			goto SERVICE_AUDIO_DECODE;
		}
    #if VIDEO_CFG_JUMP_SAMEFRAME			
		if(offset)
		{
			XMsgQPost(videoPCtrl.jpegIdleQ,(MSG_T *)offset);
		}
	#endif
	
	#if VIDEO_DBG_PLY_EN	
		videoPCtrl.dbg_time = XOSTimeGet();
	#endif  
#if 0

        p_lcd_buffer = (lcdshow_frame_t *)video->mem;
		if(p_lcd_buffer)
			hal_lcdVideoFrameFlush(p_lcd_buffer, videoPCtrl.arg.pos_x,videoPCtrl.arg.pos_y,
									videoPCtrl.arg.tar_width,videoPCtrl.arg.tar_height,
									p_lcd_buffer->destw,p_lcd_buffer->desth);	
		
		//deg_Printf("*");
		ret = hal_mjpDecodePicture((INT8U *)(jpeg->mem + 8),p_lcd_buffer->y_addr,p_lcd_buffer->uv_addr,p_lcd_buffer->destw,p_lcd_buffer->desth);
		if(ret != true)
		{
			if(videoPCtrl.dacstat &  PLAY_DACSTA_VFIRST) 	
			{
				VIDEOPLY_DBG("[Video PLY]: parse err:%d, %x\n",ret,*(INT32U *)jpeg->mem);
				videoPlaybackStop();
				return MEDIA_STAT_STOP;
			}else
			{
				XMsgQPost(videoPCtrl.jpegIdleQ,(MSG_T *)jpeg);
				playback_vidsFree(video);
				VIDEOPLY_DBG("D");
				videoPCtrl.vflag--;
				goto SERVICE_AUDIO_DECODE;
			}

		}
#else
        if(videoPCtrl.dacstat &  PLAY_DACSTA_VFIRST) 		
        {
			deg_Printf("hal_mjpDecodeReset\n");
            hal_mjpDecodeReset();
        	ret = hal_mjpDecodeParse((INT8U *)(jpeg->mem + 8),videoPCtrl.arg.tar_width,videoPCtrl.arg.tar_height);
			if(ret<0)
			{
				VIDEOPLY_DBG("[Video PLY]: parse err:%d, %x\n",ret,*(INT32U *)jpeg->mem);
				videoPlaybackStop();
			    return MEDIA_STAT_STOP;
			}
			VIDEOPLY_DBG("[Video PLY]: frame [0x%x][0x%x]\n",*((uint32*)(jpeg->mem)),*((uint32*)(jpeg->mem+4)));
        }
        p_lcd_buffer = (lcdshow_frame_t *)video->mem;
		if(p_lcd_buffer)
			hal_lcdVideoFrameFlush(p_lcd_buffer, videoPCtrl.arg.pos_x,videoPCtrl.arg.pos_y,
									videoPCtrl.arg.tar_width,videoPCtrl.arg.tar_height,
									videoPCtrl.arg.dest_width,videoPCtrl.arg.dest_height);	
									//p_lcd_buffer->destw,p_lcd_buffer->desth);	
	#if HAL_CFG_MJPEG_QULITY_AUTO > 0	
		hal_mjpDecodeOneFrame_Ext((INT8U *)(jpeg->mem + 8),p_lcd_buffer->y_addr,p_lcd_buffer->uv_addr);
	#else
		hal_mjpDecodeOneFrame((INT8U *)(jpeg->mem + 8),p_lcd_buffer->y_addr,p_lcd_buffer->uv_addr);
	#endif
#endif
		video->count = 1;
		video->need_show = 1;
		video->offset = jpeg->offset;

		if(videoPCtrl.dacstat & PLAY_DACSTA_VFIRST) 
		{
			while(hal_mjpDecodeBusyCheck());
			
			if(hal_mjpDecodeErrorCheck()!=0) // decode error,fail
			{
                playback_vidsFree(video);
				videoPlaybackStop();
				return MEDIA_STAT_STOP;
			}
			VIDEOPLY_DBG("[Video PLY]: play first frame\n");

			videoPCtrl.lcdframe = (MSG_T *)video;
			videoPCtrl.jpegframe = (MSG_T *)jpeg;
		#if VIDEO_DBG_PLY_EN
            videoPCtrl.dbg_time_cnt += XOSTimeGet()-videoPCtrl.dbg_time;
		    videoPCtrl.dbg_frame_cnt++;
	    #endif
		    if(videoPCtrl.arg.firstframe)
			{
				videoPlaybackPause();
				
			}

		    videoPCtrl.dacstat = PLAY_DACSTA_STOP;// dac initial stat
		    videoPCtrl.vflag = 1;
		    goto SERVICE_SHOW;
		}
		else
		{
			videoPCtrl.jpegframe = (MSG_T *)jpeg;
			videoPCtrl.videoframe = (MSG_T *)video;//p_lcd_buffer;
		}	
//-----audio decode---		
SERVICE_AUDIO_DECODE:	
		audio = (AUDIO_CACHE_T*)XMsgQPend(videoPCtrl.audsIdleQ,&err);//get idle queue
		if(err != X_ERR_NONE)
		    goto SERVICE_SHOW;
		ret = api_multimedia_decodeframe(videoPCtrl.arg.avi_arg.media_ch, NULL, (int*)&offset,(int*)&length,AVI_FRAME_WD_AUDIO);// get audio frame
		if(ret < 0) // fail
		{
			videoPCtrl.play_endsta |= VIDEOPLAY_AUDS_END; // maybe audio frame end
			XMsgQPost(videoPCtrl.audsIdleQ,(AUDIO_CACHE_T *)audio);

			goto SERVICE_SHOW;
		}
		if(length == 0 || (length + 8) > audio->max_size)
		{
			VIDEOPLY_DBG("[Video PLY]: audio size overflow %x,%x\n",length,audio->max_size);
			XMsgQPost(videoPCtrl.audsIdleQ,(AUDIO_CACHE_T *)audio);
			goto SERVICE_SHOW;		
		}
		audio->size = length;

		res = videoPlayBack_read((void *)audio->mem, offset, length+8);
		if(res<0)
		{
			goto VIDEO_PLAYBACK_ERROR;
		}	
		
		if(videoPCtrl.dacstat & PLAY_DACSTA_SPEED)
		{
			XMsgQPost(videoPCtrl.audsIdleQ,(AUDIO_CACHE_T *)audio);
		}else{
			XMsgQPost(videoPCtrl.audsBusyQ,(AUDIO_CACHE_T *)audio);
			if((videoPCtrl.dacstat != PLAY_DACSTA_START) && (videoPCtrl.dacstat != PLAY_DACSTA_VFIRST))
			{	
				
				if(XMsgQCheck(videoPCtrl.audsBusyQ)>=2)
				{
					
					deg_Printf("dac start:%d, %d\n",videoPCtrl.arg.avi_arg.samplerate,videoPCtrl.audiolen);
					hal_dacSetVolume(videoPCtrl.volume);
					videoPCtrl.auds_curframe = (AUDIO_CACHE_T*)XMsgQPend(videoPCtrl.audsBusyQ,&err); 
					videoPCtrl.audiolen 	 = videoPCtrl.auds_curframe->size;
					//addr = (INT32U)XMsgQPend(videoPCtrl.audsBusyQ,&err);
					hal_dacCallBackRegister(videoPlaybackDacISR);  // audio player may register callback function when play key sound
					//deg_Printf("videoPCtrl.auds_curframe:%x\n", videoPCtrl.auds_curframe);
					hal_dacPlayStart(videoPCtrl.arg.avi_arg.samplerate,(INT32U)videoPCtrl.auds_curframe->mem+8,videoPCtrl.auds_curframe->size);//0x3fff);//
					videoPCtrl.dacstat = PLAY_DACSTA_START;
				}
			}
		}


		
    }
SERVICE_SHOW:
	if(videoPCtrl.play_endsta == VIDEOPLAY_ALL_END)// audio and video are to the end
	{
		if(videoPCtrl.playSpeed < ARRAY_NUM(videoSpeedTable)/2)
		{
			deg_Printf("[Video PLY]---audio and video are to the end---\r\n");
			videoPlaybackStop();//videoPlaybackPause();//wxn--为了进度条能清零 快进倍数能消失23.8.1
			return videoPCtrl.stat;
		}
		VIDEOPLY_DBG("[Video PLY]: file end.\n");
		if((videoPCtrl.dacstat & PLAY_DACSTA_MASK) == PLAY_DACSTA_STOP) //auds is end
		{
		    videoPlaybackStop();
			return videoPCtrl.stat;
		}
		else	
		    videoPCtrl.dacstat = PLAY_DACSTA_WAITSTOP; // ready to stop.only wait the last decoded frame play end
	}

    if(videoPCtrl.vflag)
    {
		offset = 0;
		video = 	(JPEG_CACHE_T *)videoPCtrl.lcdframe;
		if(video && (video->count))
		{
			err = X_ERR_NONE;
		}
		else
		{
			offset = (INT32U)videoPCtrl.lcdframe;
		    video = 	(JPEG_CACHE_T *)XMsgQPend(videoPCtrl.vidsBusyQ,&err);
		}
		if(err == X_ERR_NONE)
		{
			videoPCtrl.vflag--;

			if(offset)
            {
                // put last frame in queue
                XMsgQPost(videoPCtrl.vidsIdleQ,(MSG_T *)videoPCtrl.lcdframe);
            }
			if(video->need_show)
			{
				hal_lcdVideoSetFrame((void*)video->mem);
				video->need_show = 0;
			}		
            if(video->count)
			{
				video->count--;	
			}
				
			videoPCtrl.lcdframe = (MSG_T *)video;
		}
		else
		{
			if(videoPCtrl.dacstat == PLAY_DACSTA_WAITSTOP)
			{
				videoPlaybackStop();
			}
		}
    }

	
#if VIDEO_DBG_PLY_EN

    if((videoPCtrl.dbg_frame_cnt>=300)/* || (videoPCtrl.dbg_ptime>300)*/)
    {
		VIDEOPLY_DBG("[Video PLY]:  [%d]decode -> %d ms/frame\n",videoPCtrl.vflag,videoPCtrl.dbg_time_cnt/videoPCtrl.dbg_frame_cnt);
		videoPCtrl.dbg_frame_cnt = 0;
		videoPCtrl.dbg_time_cnt = 0;
		videoPCtrl.dbg_ptime = 0;
    }
#endif
    return STATUS_OK;
	
VIDEO_PLAYBACK_ERROR:  // video player can not handler this error.only stop playback
	videoPlaybackStop();
	return STATUS_FAIL;
}
/*******************************************************************************
* Function Name  : videoPlaybackFastForward
* Description    : ast forward  
* Input          : none
* Output         : none
* Return         : int  : current play speed setp
                 : backward : -4, -3, -2
                   normal   : 0
                   forward  : 2, 3, 4
*******************************************************************************/
int videoPlaybackFastForward(void)
{
	int fast;
    if(videoPCtrl.stat != MEDIA_STAT_PLAY)
		return videoPCtrl.stat;
	if(videoPCtrl.playSpeed >= (ARRAY_NUM(videoSpeedTable)-1) || videoPCtrl.playSpeed < ARRAY_NUM(videoSpeedTable)/2 )
	{
		videoPCtrl.playSpeed = ARRAY_NUM(videoSpeedTable)/2;
	}
	else
	{
		videoPCtrl.playSpeed++;
	}
	fast = videoSpeedTable[videoPCtrl.playSpeed];
	api_multimedia_decodefast(videoPCtrl.arg.avi_arg.media_ch, fast);
	if(videoPCtrl.playSpeed == ARRAY_NUM(videoSpeedTable)/2)
	{
		videoPCtrl.dacstat &= ~PLAY_DACSTA_SPEED;
	}else
	{
		videoPCtrl.dacstat |= PLAY_DACSTA_SPEED;
	}
	VIDEOPLY_DBG("[Video PLY]+++++:fast speed %d\n",fast);  
	return videoPCtrl.playSpeed;
}
/*******************************************************************************
* Function Name  : videoPlaybackFastBackward
* Description    : fast backward
* Input          : none
* Output         : none
* Return         : int  : current play speed setp
                            : backward : 0,1,2,3, -> -16,-8,-4,-1
                              normal     : 4 -0
                              forward   : 5,6,7,8, ->1,4,8,16                      
*******************************************************************************/
int videoPlaybackFastBackward(void)
{
	int fast;
    if(videoPCtrl.stat != MEDIA_STAT_PLAY)
		return videoPCtrl.stat;
	if(videoPCtrl.playSpeed == 0 || videoPCtrl.playSpeed > ARRAY_NUM(videoSpeedTable)/2)
	{
		videoPCtrl.playSpeed = ARRAY_NUM(videoSpeedTable)/2;
	}
	else
	{
		videoPCtrl.playSpeed--;
	}
	

	fast = videoSpeedTable[videoPCtrl.playSpeed];

	api_multimedia_decodefast(videoPCtrl.arg.avi_arg.media_ch, fast);
	if(videoPCtrl.playSpeed == ARRAY_NUM(videoSpeedTable)/2)
	{
		videoPCtrl.dacstat &= ~PLAY_DACSTA_SPEED;
	}else
	{
		videoPCtrl.dacstat |= PLAY_DACSTA_SPEED;
	}
	VIDEOPLY_DBG("[Video PLY]-----:fast speed %d\n",fast);
	return videoPCtrl.playSpeed;	
}


#endif


