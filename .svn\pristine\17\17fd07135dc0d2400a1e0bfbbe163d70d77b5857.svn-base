/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
void menuProcDelCur(winHandle handle,u32 parameNum,u32* parame);
void menuProcDelAll(winHandle handle,u32 parameNum,u32* parame);
// void menuProcLockCur(winHandle handle,u32 parameNum,u32* parame);
// void menuProcUnlockCur(winHandle handle,u32 parameNum,u32* parame);
// void menuProcUnlockAll(winHandle handle,u32 parameNum,u32* parame);

MENU_ITME_START(playBack)
	MENU_ITEM_PROC(menuProcDelCur,R_ID_ICON_MENUDELONE,R_ID_STR_SET_DELETE)
	MENU_ITEM_PROC(menuProcDelAll,R_ID_ICON_MENUDELALL,R_ID_STR_SET_DELETEALL)
	// MENU_ITEM_PROC(menuProcLockCur,R_ID_ICON_MENUUNLOCK,R_ID_STR_SET_LOCKCUR)
	// MENU_ITEM_PROC(menuProcUnlockCur,R_ID_ICON_MENUUNLOCK,R_ID_STR_SET_UNLOCKCUR)
	// MENU_ITEM_PROC(menuProcUnlockAll,R_ID_ICON_MENUUNLOCK,R_ID_STR_SET_UNLOCKALL)
MENU_ITME_END()


MENU_PAGE_START(playBack)
MENU_PAGE_ITEMS(playBack,R_ID_ICON_MTMENU,R_ID_ICON_MTMENU,R_ID_STR_SET_VIDEO)
MENU_PAGE_END()

MENU_DEFINE(playBack)







