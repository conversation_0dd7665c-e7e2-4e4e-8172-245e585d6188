/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuUnlockCurWin.c"
/*******************************************************************************
* Function Name  : getunlockCurResInfor
* Description    : getunlockCurResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : u32: 
*******************************************************************************/
static u32 getunlockCurResInfor(u32 item,u32* image,u32* str)
{
	if(item == 0)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_OK;
	}
	else if(item == 1)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_CANCEL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockCurKeyMsgOk
* Description    : unlockCurKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockCurKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	char *name;
	INT32S list;
	int file_type;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,UNLOCKCUR_SELECT_ID));
		if(item == 0)
		{
			if(app_taskCurId()  == TASK_PLAY_VIDEO)
			{
				if(SysCtrl.spi_jpg_list >= 0)
				{
					list = SysCtrl.spi_jpg_list;
				}else{
					list = SysCtrl.avi_list;
				}
			}else if(app_taskCurId()  == TASK_PLAY_AUDIO)
			{
				list = SysCtrl.wav_list;
			}else
			{
				return 0;
			}
			if(filelist_api_CountGet(list) <= 0)
			{
				task_com_tips_show(TIPS_NO_FILE);
				return 0;
			}
			if(filelist_fnameChecklockByIndex(list,SysCtrl.file_index) > 0)
			{
				name = filelist_GetFileFullNameByIndex(list, SysCtrl.file_index, &file_type); 
				hx330x_str_cpy(SysCtrl.file_fullname,name);
				filenode_filefullnameUnlock(name);
				deg_Printf("unlock : %s -> %s.",SysCtrl.file_fullname,name);
				if(file_type & FILELIST_TYPE_SPI)
				{
					if(nv_jpg_change_lock (filelist_GetFileIndexByIndex(list,SysCtrl.file_index),0) == NV_OK)
					{
						deg_Printf("->ok\n");
						task_com_tips_show(TIPS_COM_SUCCESS);
						filenode_fnameUnlockByIndex(list,SysCtrl.file_index);
					}else
					{
						task_com_tips_show(TIPS_COM_FAIL);
						deg_Printf("->fail\n");
					}
				}else
				{
					if(f_rename(SysCtrl.file_fullname,name) == FR_OK)  // rename in file system
					{
						deg_Printf("->ok\n");
						filenode_fnameUnlockByIndex(list,SysCtrl.file_index);
						task_com_tips_show(TIPS_COM_SUCCESS);
					}
					else
					{
						task_com_tips_show(TIPS_COM_FAIL);
						deg_Printf("->fail\n");
					}
				}
			}		
			else
				task_com_tips_show(TIPS_COM_SUCCESS);
		}
		else
			uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockCurKeyMsgUp
* Description    : unlockCurKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockCurKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,UNLOCKCUR_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockCurKeyMsgDown
* Description    : unlockCurKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockCurKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		 uiItemManageNextItem(winItem(handle,UNLOCKCUR_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockCurKeyMsgMenu
* Description    : unlockCurKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockCurKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockCurKeyMsgMode
* Description    : unlockCurKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockCurKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockCurOpenWin
* Description    : unlockCurOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockCurOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]unlockCurOpenWin\n");
	uiItemManageSetRowSum(winItem(handle,UNLOCKCUR_SELECT_ID),1,Rh(32));
#if UI_SHOW_SMALL_PANEL == 0
	uiItemManageSetColumnSumWithGap(winItem(handle,UNLOCKCUR_SELECT_ID),0,2,Rw(50), Rw(12));
#else
	uiItemManageSetColumnSumWithGap(winItem(handle,UNLOCKCUR_SELECT_ID),0,2,Rw(100),Rw(6));
#endif 
	uiItemManageCreateItem(		winItem(handle,UNLOCKCUR_SELECT_ID),uiItemCreateMenuOption,getunlockCurResInfor,2);
	uiItemManageSetCharInfor(	winItem(handle,UNLOCKCUR_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,UNLOCKCUR_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,UNLOCKCUR_SELECT_ID),R_ID_PALETTE_Gray);

	uiItemManageSetCurItem(		winItem(handle,UNLOCKCUR_SELECT_ID),1);
	
	return 0;
}
/*******************************************************************************
* Function Name  : unlockCurCloseWin
* Description    : unlockCurCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockCurCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]unlockCurCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : unlockCurWinChildClose
* Description    : unlockCurWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockCurWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]unlockCurWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : unlockCurTouchWin
* Description    : unlockCurTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int unlockCurTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("unlockCurTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == UNLOCKCUR_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockCurTouchSlideOff
* Description    : unlockCurTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int unlockCurTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;

	if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	return 0;
}

ALIGNED(4) msgDealInfor unlockCurMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	unlockCurOpenWin},
	{SYS_CLOSE_WINDOW,	unlockCurCloseWin},
	{SYS_CHILE_COLSE,	unlockCurWinChildClose},
	{SYS_TOUCH_WINDOW,  unlockCurTouchWin},
	{SYS_TOUCH_SLIDE_OFF,unlockCurTouchSlideOff},
	{KEY_EVENT_OK,		unlockCurKeyMsgOk},
	{KEY_EVENT_UP,		unlockCurKeyMsgUp},
	{KEY_EVENT_DOWN,	unlockCurKeyMsgDown},
	{KEY_EVENT_MENU,	unlockCurKeyMsgMenu},
	{KEY_EVENT_MODE,	unlockCurKeyMsgMode},
	{EVENT_MAX,			NULL},
};

WINDOW(unlockCurWindow,unlockCurMsgDeal,unlockCurWin)


