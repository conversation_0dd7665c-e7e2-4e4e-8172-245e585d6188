/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_MEM_MANAGE_H
#define UI_WIN_MEM_MANAGE_H

#define HEAD_FLAG_USE		(1<<31)
#define HEAD_SIZE_MASK		(0x7FFFFFFF)

typedef struct heapHead_S
{
	u32 size;
	struct heapHead_S* next;	
}heapHead_T;

#define HEAP_HEAD_SIZE  sizeof(heapHead_T)

typedef struct memPool_S
{
	struct memPool_S* next;
}memPool_T;
typedef struct memPool_Ctl_S
{
	memPool_T* cur_mem;
	u32   blkSize;
	u32   freeBlks;
	u32   maxBlks;
}memPool_Ctl;
/*******************************************************************************
* Function Name  : uiWinHeapInit
* Description    : uiWinHeapInit
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinHeapInit(u32 addr, u32 size);
/*******************************************************************************
* Function Name  : uiWinHeapMemInfo
* Description    : uiWinHeapMemInfo
* Input          : none
* Output         : none                                            
* Return         : winHandle
*******************************************************************************/
void uiWinHeapMemInfo(void);
/*******************************************************************************
* Function Name  : uiWinHeapMalloc
* Description    : uiWinHeapMalloc
* Input          : none
* Output         : none                                            
* Return         : winHandle
*******************************************************************************/
winHandle uiWinHeapMalloc(u32 size);
/*******************************************************************************
* Function Name  : uiWinHeapFree
* Description    : uiWinHeapFree
* Input          : none
* Output         : none                                            
* Return         : winHandle
*******************************************************************************/
void uiWinHeapFree(winHandle handle);

/*******************************************************************************
* Function Name  : uiWinHeapFree
* Description    : uiWinHeapFree
* Input          : none
* Output         : none                                            
* Return         : winHandle
*******************************************************************************/
u32 uiMemPoolCreate(memPool_Ctl *pool,memPool_T* pool_start,u32 pool_size,u32 blkSize);
/*******************************************************************************
* Function Name  : uiMemPoolGet
* Description    : uiMemPoolGet
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void* uiMemPoolGet(memPool_Ctl* pool);
/*******************************************************************************
* Function Name  : uiMemPoolPut
* Description    : uiMemPoolPut
* Input          : none
* Output         : none                                            
* Return         : u32 0 : pool full, >0: pool free blks
*******************************************************************************/
u32 uiMemPoolPut(memPool_Ctl* pool,void* ptr);
/*******************************************************************************
* Function Name  : uiMemPoolGet
* Description    : uiMemPoolGet
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiMemPoolInfo(memPool_Ctl* pool);

#endif
