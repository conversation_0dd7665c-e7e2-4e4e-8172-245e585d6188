/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
typedef enum
{
	THUMBNALL1_RECT_COLOR=0,
	THUMBNALL1_RECT_NO_COLOR,
	THUMBNALL1_STRING,
	THUMBNALL2_RECT_COLOR,
	THUMBNALL2_RECT_NO_COLOR,
	THUMBNALL2_STRING,
	THUMBNALL3_RECT_COLOR,
	THUMBNALL3_RECT_NO_COLOR,
	THUMB<PERSON>LL3_STRING,
	THUMBNALL4_RECT_COLOR,
	THUMBNALL4_RECT_NO_COLOR,
	THUMBNALL4_STRING,
	THUMBNALL5_RECT_COLOR,
	THUMBNALL5_RECT_NO_COLOR,
	THUMBNALL5_STRING,
	THUMBNALL6_RECT_COLOR,
	THUMBNALL6_RECT_NO_COLOR,
	THUMBNALL6_STRING,
	THUMBNALL7_RECT_COLOR,
	THUMBNALL7_RECT_NO_COLOR,
	THUMBNALL7_STRING,
	THUMBNALL8_RECT_COLOR,
	THUMBNALL8_RECT_NO_COLOR,
	THUMBNALL8_STRING,
	THUMBNALL9_RECT_COLOR,
	THUMBNALL9_RECT_NO_COLOR,
	THUMBNALL9_STRING,
	THUMBNALL_PAGE,
}thumbnallID;
ALIGNED(4) static thumbnallID thumbnallIDnum[] =
{
	THUMBNALL1_RECT_COLOR,
	THUMBNALL2_RECT_COLOR,
	THUMBNALL3_RECT_COLOR,
	THUMBNALL4_RECT_COLOR,
	THUMBNALL5_RECT_COLOR,
	THUMBNALL6_RECT_COLOR,
	THUMBNALL7_RECT_COLOR,
	THUMBNALL8_RECT_COLOR,
	THUMBNALL9_RECT_COLOR
};
#define THUMBNALL_NUM      			(sizeof(thumbnallIDnum)/sizeof(thumbnallIDnum[0]))
#define THUMBNALL_RECTID(id)		(thumbnallIDnum[id])
#define THUMBNALL_STRID(id)			(thumbnallIDnum[id] + 2)
#define THUMBNALL_PAGEID(id)		((id)/THUMBNALL_NUM + 1)
#define THUMBNALL_PAGESTARTID(id)	((id/THUMBNALL_NUM)*THUMBNALL_NUM)
#define THUMBNALL_PAGESUM(id)		((id)/THUMBNALL_NUM + (((id)%THUMBNALL_NUM)?1:0)) 
#define THUMBNALL_TOUCHITEM(id)		((id)/3)

#define SELECT_COLOR       			R_ID_PALETTE_Yellow


UNUSED ALIGNED(4) const widgetCreateInfor playVideoThumbnallWin[] =
{
	createFrameWin(							Rx(0),   Ry(0),   Rw(320), Rh(240),	R_ID_PALETTE_Black,		WIN_ABS_POS),
	
	createRect(THUMBNALL1_RECT_COLOR,     	Rx(5),   Ry(20),  Rw(100), Rh(70),	R_ID_PALETTE_Transparent),
	createRect(THUMBNALL1_RECT_NO_COLOR,  	Rx(6),   Ry(21),  Rw(98),  Rh(68),  R_ID_PALETTE_Transparent),
	createStringIcon(THUMBNALL1_STRING,   	Rx(55),  Ry(20),  Rw(50),  Rh(20),  " ",				ALIGNMENT_LEFT,  	R_ID_PALETTE_White, DEFAULT_FONT),
	
	createRect(THUMBNALL2_RECT_COLOR,     	Rx(110), Ry(20),  Rw(100), Rh(70),	R_ID_PALETTE_Transparent),
	createRect(THUMBNALL2_RECT_NO_COLOR,  	Rx(111), Ry(21),  Rw(98),  Rh(68),	R_ID_PALETTE_Transparent),
	createStringIcon(THUMBNALL2_STRING,   	Rx(160), Ry(20),  Rw(50),  Rh(20),	" ",				ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
	createRect(THUMBNALL3_RECT_COLOR,     	Rx(215), Ry(20),  Rw(100), Rh(70),	R_ID_PALETTE_Transparent),
	createRect(THUMBNALL3_RECT_NO_COLOR,  	Rx(216), Ry(21),  Rw(98),  Rh(68),	R_ID_PALETTE_Transparent),
	createStringIcon(THUMBNALL3_STRING,   	Rx(265), Ry(20),  Rw(50),  Rh(20),	" ",				ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
	createRect(THUMBNALL4_RECT_COLOR,     	Rx(5),   Ry(94),  Rw(100), Rh(70),	R_ID_PALETTE_Transparent),
	createRect(THUMBNALL4_RECT_NO_COLOR,  	Rx(6),   Ry(95),  Rw(98),  Rh(68),	R_ID_PALETTE_Transparent),
	createStringIcon(THUMBNALL4_STRING,   	Rx(55),  Ry(94),  Rw(50),  Rh(20),	" ",				ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
	createRect(THUMBNALL5_RECT_COLOR,       Rx(110), Ry(94),  Rw(100), Rh(70),	R_ID_PALETTE_Transparent),
	createRect(THUMBNALL5_RECT_NO_COLOR,  	Rx(111), Ry(95),  Rw(98),  Rh(68),	R_ID_PALETTE_Transparent),
	createStringIcon(THUMBNALL5_STRING,   	Rx(160), Ry(94),  Rw(50),  Rh(20),	" ",				ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
	createRect(THUMBNALL6_RECT_COLOR,     	Rx(215), Ry(94),  Rw(100), Rh(70),	R_ID_PALETTE_Transparent),
	createRect(THUMBNALL6_RECT_NO_COLOR,  	Rx(216), Ry(95),  Rw(98),  Rh(68),	R_ID_PALETTE_Transparent),
	createStringIcon(THUMBNALL6_STRING,   	Rx(265), Ry(94),  Rw(50),  Rh(20),	" ",				ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),

	createRect(THUMBNALL7_RECT_COLOR,     	Rx(5),   Ry(168), Rw(100), Rh(70),	R_ID_PALETTE_Transparent),
	createRect(THUMBNALL7_RECT_NO_COLOR,  	Rx(6),   Ry(169), Rw(98),  Rh(68),	R_ID_PALETTE_Transparent),
	createStringIcon(THUMBNALL7_STRING,   	Rx(55),  Ry(168), Rw(50),  Rh(20),	" ",				ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
	createRect(THUMBNALL8_RECT_COLOR,     	Rx(110), Ry(168), Rw(100), Rh(70),	R_ID_PALETTE_Transparent),
	createRect(THUMBNALL8_RECT_NO_COLOR,  	Rx(111), Ry(169), Rw(98),  Rh(68),	R_ID_PALETTE_Transparent),
	createStringIcon(THUMBNALL8_STRING,   	Rx(160), Ry(168), Rw(50),  Rh(20),	" ",				ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
	createRect(THUMBNALL9_RECT_COLOR,     	Rx(215), Ry(168), Rw(100), Rh(70),	R_ID_PALETTE_Transparent),
	createRect(THUMBNALL9_RECT_NO_COLOR,  	Rx(216), Ry(169), Rw(98),  Rh(68),	R_ID_PALETTE_Transparent),
	createStringIcon(THUMBNALL9_STRING,   	Rx(265), Ry(168), Rw(50),  Rh(20),	" ",				ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
	createStringIcon(THUMBNALL_PAGE,      	Rx(130), Ry(0),   Rw(60),  Rh(20),	" ",				ALIGNMENT_CENTER, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : playVideoThumbnallSelect
* Description    : playVideoThumbnallSelect
* Input          : winHandle handle,u32 num,u32 select
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void playVideoThumbnallSelect(winHandle handle,u32 num,u32 select)
{
	if(select)
	{
		uiWinSetfgColor(winItem(handle,thumbnallIDnum[num]),SELECT_COLOR);
	}
	else
	{
		uiWinSetfgColor(winItem(handle,thumbnallIDnum[num]),R_ID_PALETTE_Transparent);
	}
}
/*******************************************************************************
* Function Name  : playVideoThumbnallPageShow
* Description    : playVideoThumbnallPageShow
* Input          : winHandle handle,u32 curpage,u32 totalPage
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void playVideoThumbnallPageNumShow(winHandle handle,u32 curpage,u32 totalPage)
{
	static char pagestr[12];
	int n;
	n = hx330x_num2str_cnt((char*)&pagestr,curpage,12);
	pagestr[n]='/';
	hx330x_num2str_cnt(&pagestr[n+1],totalPage,12-n-1);
	uiWinSetResid(winItem(handle,THUMBNALL_PAGE),RAM_ID_MAKE(pagestr));
}

