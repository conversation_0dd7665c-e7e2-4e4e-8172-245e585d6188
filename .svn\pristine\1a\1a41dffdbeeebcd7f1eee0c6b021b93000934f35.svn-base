/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	PLAYVIDEOMAIN_MODE_ID=0,
	PLAYVIDEOMAIN_PLAY_TIME_ID,
	PLAYVIDEOMAIN_TOTAL_TIME_ID,
	PLAYVIDEOMAIN_RESOLUTION_ID,

	PLAYVIDEOMAIN_POWERON_TIME_ID,
	PLAYVIDEOMAIN_BATERRY_ID,

	PLAYVIDEOMAIN_FILE_NAME_ID,
	PLAYVI<PERSON>OMAIN_FILE_INDEX_ID,
	PLAYVIDEOMAIN_ERROR_ID,


	//PLAYVIDEOMAIN_IRLED_ID,
	//PLAYVIDEOMAIN_MONITOR_ID, // parking monitoring

	PLAYVIDEOMAIN_LOCK_ID,
	PLAYVIDEOMAIN_SD_ID,
	PLAYVIDEOMAIN_TIMEBAR_ID,
	PLAYVIDEOMAIN_SPEED_ID,
	PLAYVIDEOMAIN_PLYPAUSE_ID,
	PLAYVIDEOMAIN_MAX_ID
};

UNUSED ALIGNED(4) const widgetCreateInfor playVideoMainWin[] =
{
	createFrameWin(									Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent, 		WIN_ABS_POS),

	createImageIcon(PLAYVIDEOMAIN_MODE_ID,          Rx(5),   Ry(0),   Rw(30),  Rh(30), 	R_ID_ICON_MTPLAY2,		ALIGNMENT_LEFT),
	createStringIcon(PLAYVIDEOMAIN_PLAY_TIME_ID,    Rx(10),  Ry(210),   Rw(82), Rh(25),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_Red, DEFAULT_FONT),
	createStringIcon(PLAYVIDEOMAIN_TOTAL_TIME_ID,	Rx(60),  Ry(209),   Rw(82), Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT,	R_ID_PALETTE_Red, DEFAULT_FONT),

	 createStringIcon(PLAYVIDEOMAIN_RESOLUTION_ID,	Rx(32), Ry(0),   Rw(40),  Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White, DEFAULT_FONT),
 	 createStringIcon(PLAYVIDEOMAIN_FILE_NAME_ID,    Rx(68),   Ry(0),  Rw(128), Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_White, DEFAULT_FONT),
	// createStringIcon(PLAYVIDEOMAIN_POWERON_TIME_ID,	Rx(230), Ry(35),   Rw(60),  Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT,  	R_ID_PALETTE_White, DEFAULT_FONT),
 	createStringIcon(PLAYVIDEOMAIN_FILE_INDEX_ID,   Rx(165), Ry(0),  Rw(120), Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White, DEFAULT_FONT),
    // createImageIcon(PLAYVIDEOMAIN_PLYPAUSE_ID,		Rx(5), Ry(210), Rw(40),  Rh(30),	R_ID_ICON_MTPPLAY,		ALIGNMENT_LEFT),

  
	createImageIcon(PLAYVIDEOMAIN_BATERRY_ID,       Rx(290), Ry(0),   Rw(32),  Rh(30), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),


	// createStringIcon(PLAYVIDEOMAIN_FILE_NAME_ID,    Rx(130),   Ry(0),  Rw(160), Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White, DEFAULT_FONT),
	// createStringIcon(PLAYVIDEOMAIN_FILE_INDEX_ID,   Rx(170), Ry(0),  Rw(120), Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White, DEFAULT_FONT),
	createStringIcon(PLAYVIDEOMAIN_ERROR_ID,      	Rx(110), Ry(100), Rw(80),  Rh(40),	RAM_ID_MAKE("ERROR"),	ALIGNMENT_CENTER, 	R_ID_PALETTE_Red, DEFAULT_FONT),

	createImageIcon(PLAYVIDEOMAIN_LOCK_ID,      	Rx(55), Ry(0), Rw(30),  Rh(30), 	R_ID_ICON_MTLOCK,	 	ALIGNMENT_CENTER),
//	createImageIcon(PLAYVIDEOMAIN_SD_ID,        	Rx(280), Ry(0), Rw(30),  Rh(30), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
#if FUN_VIDEO_PLAY_SPEED
	// createProgressBar(PLAYVIDEOMAIN_TIMEBAR_ID,     Rx(0),   Ry(235), Rw(320), Rh(5),R_ID_PALETTE_DarkGray,R_ID_PALETTE_Blue, ALIGNMENT_LEFT),
#endif
	createStringIcon(PLAYVIDEOMAIN_SPEED_ID,		Rx(130),/*32*/ Ry(209),	 Rw(30),  Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER,	R_ID_PALETTE_White, DEFAULT_FONT),
	createImageIcon(PLAYVIDEOMAIN_PLYPAUSE_ID,		Rx(5), Ry(210), Rw(40),  Rh(30),	R_ID_ICON_MTPPLAY,		ALIGNMENT_LEFT),
	widgetEnd(),
};

/*******************************************************************************
* Function Name  : playVideoMainPlayTimeShow
* Description    : playVideoMainPlayTimeShow
* Input          : winHandle handle,u32 playTime,u32 totalTime
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainPlayTimeShow(winHandle handle)
{
	if(videoPlaybackGetStatus() == MEDIA_STAT_PLAY)
	{
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(0)));
		uiWinSetStrInfor(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID), DEFAULT_FONT, ALIGNMENT_CENTER, R_ID_PALETTE_Red);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_SPEED_ID),1);

	}
	else//[Video PLY]:fast speed -2;
	{
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(0)));
		uiWinSetStrInfor(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID), DEFAULT_FONT, ALIGNMENT_CENTER, R_ID_PALETTE_Red);


		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_TOTAL_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(1)));
		uiWinSetStrInfor(winItem(handle,PLAYVIDEOMAIN_TOTAL_TIME_ID), DEFAULT_FONT, ALIGNMENT_CENTER, R_ID_PALETTE_White);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_SPEED_ID),0);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_PLYPAUSE_ID),1);
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_PLYPAUSE_ID),R_ID_ICON_MTPPLAY);

		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_ERROR_ID),0);
	}

	// if(SysCtrl.file_type & FILELIST_TYPE_JPG)
	 if((SysCtrl.file_type & FILELIST_TYPE_JPG) ||(SysCtrl.file_type &FILELIST_TYPE_SPI))
	{
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),0);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_TOTAL_TIME_ID),0);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),0);
	#if FUN_VIDEO_PLAY_SPEED
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID),0);
	#endif
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_PLYPAUSE_ID),0);
	}
	else{

		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),1);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_TOTAL_TIME_ID),1);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_PLAY_TIME_ID),1);

	#if FUN_VIDEO_PLAY_SPEED
		if(videoPlaybackGetStatus() == MEDIA_STAT_STOP)
		{
			uiWinSetProgressRate(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID), 100);
		}else
		{
			uiWinSetProgressRate(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID), (SysCtrl.play_cur_time *100) / SysCtrl.play_total_time);
		}
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_TIMEBAR_ID),1);
	#endif
	}
}
/*******************************************************************************
* Function Name  : playVideoMainResolutionShow
* Description    : playVideoMainResolutionShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainResolutionShow(winHandle handle)
{

	u32 size;
	char* str = " ";
	if(SysCtrl.file_type & FILELIST_TYPE_JPG)
	{
		u16 width,height;
		imageDecodeGetResolution(&width,&height);
		size = width*height;
		if(size <        100000) str = "QVGA";
		else if(size <   500000) str = "VGA";
		else if(size <  1500000) str = "1M";
		else if(size <  2500000) str = "2M";
		else if(size <  4000000) str = "3M";
		else if(size <  6500000) str = "5M";
		else if(size <  9000000) str = "8M";
		else if(size < 11000000) str = "10M";
		else if(size < 14000000) str = "12M";
		else if(size < 17000000) str = "16M";
		else if(size < 19000000) str = "18M";
		else if(size < 22000000) str = "20M";
		else if(size < 32000000) str = "24M";
		else if(size < 44000000) str = "40M";
		else					 str = "48M";
	}
	else if(SysCtrl.file_type & FILELIST_TYPE_AVI)
	{
		AVI_DEC_ARG *arg = videoPlaybabkGetArg();
		size = arg->width*arg->height;
		if(size <        100000) str = "QVGA";
		else if(size <   500000) str = "VGA";
		else if(size <  1500000) str = "720P";
		else if(size <  2500000) str = "1080P";
	}
	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_RESOLUTION_ID),RAM_ID_MAKE(str));
}


/*******************************************************************************
* Function Name  : playVideoMainLockShow
* Description    : playVideoMainLockShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainLockShow(winHandle handle)
{
	if(filelist_fnameChecklockByIndex(SysCtrl.avi_list,SysCtrl.file_index) > 0)
	{
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_LOCK_ID),R_ID_ICON_MTLOCK);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_LOCK_ID),1);
	}
	else
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_LOCK_ID),0);
}
/*******************************************************************************
* Function Name  : playVideoMainSDShow
* Description    : playVideoMainSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_SD_ID),R_ID_ICON_MTSDCNORMAL);
	else
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_SD_ID),R_ID_ICON_MTSDCNULL);
}
/*******************************************************************************
* Function Name  : playVideoMainBaterryShow
* Description    : playVideoMainBaterryShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainBaterryShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERY5;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4:
			//case BATTERY_STAT_5:
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),batid);

}
/*******************************************************************************
* Function Name  : playVideoMainFileNameShow
* Description    : playVideoMainFileNameShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainFileNameShow(winHandle handle)

// (SysCtrl.file_type &FILELIST_TYPE_SPI))
{
	static char fileindexstr[] = {"0000/0000"};
	char* name = filelist_GetFileFullNameByIndex(SysCtrl.avi_list,SysCtrl.file_index,NULL);
	if(name)
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_FILE_NAME_ID),RAM_ID_MAKE(&name[4]));
	else
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_FILE_NAME_ID),RAM_ID_MAKE(" "));
		
	hx330x_num2str(fileindexstr, SysCtrl.file_index + 1, 4);
	fileindexstr[4] = '/';
	// if(SysCtrl.spi_jpg_list > 0)
	if(SysCtrl.file_type &FILELIST_TYPE_SPI)
		hx330x_num2str(fileindexstr + 5, filelist_api_CountGet(SysCtrl.spi_jpg_list), 4);
	else
		hx330x_num2str(fileindexstr + 5, filelist_api_CountGet(SysCtrl.avi_list), 4);
	if(SysCtrl.file_index >= 0)
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_FILE_INDEX_ID),RAM_ID_MAKE(fileindexstr));
}
/*******************************************************************************
* Function Name  : playVideoMainPoweOnTimeShow
* Description    : playVideoPoweOnTimeShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playVideoMainPoweOnTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_POWERON_TIME_ID),RAM_ID_MAKE(task_com_powerOnTime_str()));

}


#if FUN_VIDEO_PLAY_SPEED
static void playVideoSpeedShow(winHandle handle)
{
	static char *fileindexstr[] = {"-8X","-4X","-2X"," ","+2X","+4X","+8X"};
	u8 speed = videoPlaybackGetSpeed();
	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_SPEED_ID),RAM_ID_MAKE(*(fileindexstr+speed)));//
}
#endif

static void playVideoMainPlayPauseShow(winHandle handle,u32 index)
{
	uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_PLYPAUSE_ID),1);
	if(index)
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_PLYPAUSE_ID),R_ID_ICON_MTPPLAY);
	else
		uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_PLYPAUSE_ID),R_ID_ICON_MTPPAUSE);

	
}