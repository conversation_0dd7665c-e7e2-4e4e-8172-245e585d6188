/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskUsbDeviceWin.c"
ALIGNED(4) static u32 usbDeviceLogoTab[] =
{
	R_ID_IMAGE_LOGO_MASS,
	R_ID_IMAGE_LOGO_PCCAM,
	R_ID_IMAGE_LOGO_VIDEO,
	R_ID_IMAGE_LOGO_SETTING
};
#define USBDEVICE_LOGO_MAX		(sizeof(usbDeviceLogoTab)/sizeof(usbDeviceLogoTab[0]))
/*******************************************************************************
* Function Name  : usbDevicelDrawLogo
* Description    : usbDevicelDrawLogo
* Input          : lcdshow_frame_t* p_frame,u32 resid,uiRect* tar_rect
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
static int usbDevicelDrawLogo(lcdshow_frame_t* p_frame,u32 resid,uiRect* tar_rect)
{	
	u16 tar_main_w 	= p_frame->ratio_w;
	u16 tar_main_h 	= p_frame->ratio_h;	
	u16 tar_rect_w	= tar_rect->x1 - tar_rect->x0;
	u16 tar_rect_h 	= tar_rect->y1 - tar_rect->y0;
	u16 tar_rect_x 	= tar_rect->x0;
	u16 tar_rect_y 	= tar_rect->y0;
	//deg_Printf("tar_rect:%d,%d\n",tar_rect_w,tar_rect_h);
	//deg_Printf("pos:%d,%d\n",tar_rect_x,tar_rect_y);
	u32 rectSize;
	u8 *rectBuf;
	u16 tar_rect_stride = (tar_rect_w + 0x1f)&~0x1f;
	if(tar_rect_x + tar_rect_w > tar_main_w)
	{
		deg_Printf("[USB DEV LOGO] w over \n");
		tar_rect_w = (tar_main_w - tar_rect_x)&~0x1;
		return -1 ;
	}
	if(tar_rect_y + tar_rect_h > tar_main_h)
	{
		deg_Printf("[USB DEV LOGO] h over \n");
		tar_rect_h = (tar_main_h - tar_rect_y)&~0x01;	
		return -1;	
	}
	
	tar_main_w  = (tar_main_w + 0x1f)&~0x1f;
	rectSize	= tar_rect_stride*tar_rect_h*3/2;
	//==decode jpeg==
	rectBuf = hal_sysMemMalloc(rectSize);
	if(NULL == rectBuf)
	{
		deg_Printf("[USB DEV LOGO] buf malloc fail :%d\n",rectSize);
		return -1;
	}    
	
	if(res_image_decode(resid, rectBuf, tar_rect_w, tar_rect_h) < 0)
	{
		deg_Printf("[USB DEV LOGO] image decode fail\n");
		hal_sysMemFree(rectBuf);
		return -1;
	}
	u8* tar_main_buf  = p_frame->y_addr + tar_rect_y*tar_main_w + tar_rect_x;
	u8* src_rect_buf = rectBuf;
	u16 i;
	//Y
	for(i = 0; i < tar_rect_h; i++)
	{
		hx330x_mcpy0_sdram2gram((void *)tar_main_buf, (void*)src_rect_buf, tar_rect_w);
		tar_main_buf  += tar_main_w;
		src_rect_buf  += tar_rect_stride;
	}
	//UV
	tar_main_buf = p_frame->uv_addr + tar_rect_y*tar_main_w/2 + tar_rect_x;
	for(i = 0; i < tar_rect_h/2; i++)
	{
		hx330x_mcpy0_sdram2gram((void *)tar_main_buf, (void*)src_rect_buf, tar_rect_w);
		tar_main_buf  += tar_main_w;
		src_rect_buf  += tar_rect_stride;
	}	
	hal_sysMemFree(rectBuf);

	return 0;
}
/*******************************************************************************
* Function Name  : usbDevicelDrawLogoByIndex
* Description    : usbDevicelDrawLogoByIndex
* Input          : winHandle handle, int index,thumbnallID id, lcdshow_frame_t* p_lcd_buffer
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDevicelDrawLogoByIndex(winHandle handle, u32 index, lcdshow_frame_t* p_lcd_buffer)
{
	uiRect rect;
	u16 width, height;
	u32 uiId;
	//if(usbDeviceOp.cur_sel_index == index) //select
		uiId = USBDEV_SEL_ID(index);
	//else
	//	uiId = USBDEV_UNSEL_ID(index);
	uiWinGetPos(winItem(handle,uiId),&rect);

	hal_lcdGetUiResolution(&width,&height);
	if(!(p_lcd_buffer->w == width && p_lcd_buffer->h == height))
	{
		rect.x0 = rect.x0 * p_lcd_buffer->w / width;
		rect.x1 = rect.x1 * p_lcd_buffer->w / width;
		rect.y0 = rect.y0 * p_lcd_buffer->h / height;
		rect.y1 = rect.y1 * p_lcd_buffer->h / height;
	}
	rect.x0 = (rect.x0)&~0x01;
	rect.x1 = (rect.x1 + 2)&~0x01;
	rect.y0 = (rect.y0)&~0x01;
	rect.y1 = (rect.y1 + 2)&~0x01;	
	return usbDevicelDrawLogo(p_lcd_buffer,usbDeviceLogoTab[index],&rect);
}
/*******************************************************************************
* Function Name  : usbDeviceLogoShow
* Description    : usbDeviceLogoShow
* Input          : winHandle handle,int indexStart,u32 num
* Output         : none
* Return         : none
*******************************************************************************/
static void usbDeviceLogoShow(winHandle handle)
{
	u16 x, y, width, height, dest_w, dest_h;
	lcdshow_frame_t *p_lcd_buffer = NULL;
	u32 i;
	u32 page_num	= usbDeviceOp.cur_sel_index/USBDEVICE_LOGO_MAX;
	if(usbDeviceOp.cur_page_num == page_num)
		return;
	u32 start_index = page_num * USBDEVICE_LOGO_MAX;
	u32 num			= USBDEVICE_LOGO_MAX - start_index;
	do{
		p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
	}while(p_lcd_buffer==NULL);
	
#if 1 //按最大video size显示
	hal_lcdGetVideoPos(&x,&y);
	hal_lcdGetVideoResolution(&width,&height);
	hal_lcdGetVideoResolution(&dest_w,&dest_h);
#else //按实际ratio后的size显示
	hal_lcdGetVideoRatioPos(&x,&y);
	hal_lcdGetVideoRatioResolution(&width,&height);
	hal_lcdGetVideoResolution(&dest_w,&dest_h);
#endif
	hal_lcdVideoFrameFlush(p_lcd_buffer,x,y,width,height,dest_w,dest_h);

	usbDeviceOp.cur_page_num = page_num;

	hal_lcdSetBufYUV(p_lcd_buffer,0,0x80,0x80);
	
	for(i = 0;i < num; i++)
	{
		usbDevicelDrawLogoByIndex(handle, start_index + i, p_lcd_buffer);
	}
	hal_lcdVideoSetFrame((void *)p_lcd_buffer);
}
/*******************************************************************************
* Function Name  : usbDeviceKeyMsgOk
* Description    : usbDeviceKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(usbDeviceOp.usb_process_flag == 1)
			return 0;
		if(usbDeviceOp.cur_sel_index <= 1)
		{
			u32 usb_mode;

			if(usbDeviceOp.cur_sel_index == 0)
			{
				res_image_show(R_ID_IMAGE_USB_MODE);
				usb_mode = USB_DEVTYPE_MSC;
			}else
			{
				VIDEO_ARG_T arg1;
				arg1.avi_arg.width	= 1280;
				arg1.avi_arg.height	= 720;
				videoRecordInit(&arg1); // enable csi&mjpeg 
				res_image_show(R_ID_IMAGE_PCCAM_MODE);
				usb_mode = USB_DEVTYPE_COMBINE;
			}
			task_com_sound_wait_end();;

			dusb_api_Init(usb_mode);
			usbDeviceOp.usb_process_flag = 1;
			
		}
		else if(usbDeviceOp.cur_sel_index == 2)
		{
			app_taskStart(TASK_RECORD_VIDEO,0);
			task_com_USB_CS_DM_DP_status_select(1);
		}else if(usbDeviceOp.cur_sel_index == 3)
		{
			uiOpenWindow(&menuItemWindow,0,1,&MENU(record));
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceKeyMsgUp
* Description    : usbDeviceKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		if(usbDeviceOp.usb_process_flag == 1)
			return 0;
		usbDeviceSelect(handle,usbDeviceOp.cur_sel_index,0);
		if(usbDeviceOp.cur_sel_index == 0)
		{
			usbDeviceOp.cur_sel_index = USBDEVICE_LOGO_MAX - 1;
		}else
		{
			usbDeviceOp.cur_sel_index--;
		}
		usbDeviceLogoShow(handle);
		usbDeviceSelect(handle,usbDeviceOp.cur_sel_index,1);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceKeyMsgDown
* Description    : usbDeviceKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		if(usbDeviceOp.usb_process_flag == 1)
			return 0;
		usbDeviceSelect(handle,usbDeviceOp.cur_sel_index,0);
		if(usbDeviceOp.cur_sel_index >= (USBDEVICE_LOGO_MAX - 1))
			usbDeviceOp.cur_sel_index = 0;
		else
			usbDeviceOp.cur_sel_index++;	

		usbDeviceLogoShow(handle);
		usbDeviceSelect(handle,usbDeviceOp.cur_sel_index,1);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceKeyMsgMenu
* Description    : usbDeviceKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceKeyMsgMode
* Description    : usbDeviceKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceKeyMsgMode(winHandle handle,uint32 parameNum,uint32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{

	}
	return 0;
}

static int usbDeviceKeyMsgAll(winHandle handle,uint32 parameNum,uint32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	deg_Printf("usbDeviceKeyMsgAll\n");
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceSysMsgUSBDEV
* Description    : usbDeviceSysMsgUSBDEV
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceSysMsgUSBDEV(winHandle handle,uint32 parameNum,uint32* parame)
{
	//if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		//app_taskStart(TASK_POWER_OFF,0);
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceKeyMsgUSBHOST
* Description    : usbDeviceKeyMsgUSBHOST
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceSysMsgUSBHOST(winHandle handle,uint32 parameNum,uint32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceOpenWin
* Description    : usbDeviceOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	// usbDeviceOp.cur_sel_index = 0;
	// usbDeviceOp.cur_page_num  = 0xff;
	// usbDeviceSelect(handle,usbDeviceOp.cur_sel_index,1);
	// usbDeviceLogoShow(handle);
	deg_Printf("[WIN]usbDeviceOpenWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceCloseWin
* Description    : usbDeviceCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]usbDeviceCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceWinChildClose
* Description    : usbDeviceWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	// usbDeviceSelect(handle,usbDeviceOp.cur_sel_index,1);
	// usbDeviceLogoShow(handle);
	deg_Printf("[WIN]usbDeviceWinChildClose\n");
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceTouchWin
* Description    : usbDeviceTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
/*
parame[0]: widget id;
parame[1]: selected item id(for createItemManage widget)
parame[2]: touch state
*/
	u32 touchItem = 0;
	int curIndexStart;
	if(parameNum!=3)
	{
		//deg_Printf("usbDeviceTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(usbDeviceOp.usb_process_flag == 1)
			return 0;
		touchItem = USBDEV_TOUCHITEM(parame[0]);
		if(touchItem >= USBDEVICE_LOGO_MAX)
			return 0;
		curIndexStart = USBDEV_PAGESTARTID(usbDeviceOp.cur_sel_index);
		usbDeviceOp.cur_sel_index = curIndexStart + touchItem;
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : usbDeviceTouchSlideOff
* Description    : usbDeviceTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int usbDeviceTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	int lastIndexStart,curIndexStart;
	if(parameNum!=1)
	{
		deg_Printf("usbDeviceTouchSlideOff, parame num error %d\n",parameNum);
		return 0;
	}
	if(usbDeviceOp.usb_process_flag == 1)
		return 0;
	if(USBDEVICE_LOGO_MAX <= 4)
	{
		return 0;
	}
	lastIndexStart = USBDEV_PAGESTARTID(usbDeviceOp.cur_sel_index);
	usbDeviceSelect(handle,usbDeviceOp.cur_sel_index,0);
	if(parame[0]== TP_DIR_UP)
	{
		if(lastIndexStart + USBDEV_ID_MAX >= USBDEVICE_LOGO_MAX)
			curIndexStart = 0;
		else
			curIndexStart = lastIndexStart + USBDEV_ID_MAX;
		
	}else if(parame[0]== TP_DIR_DOWN)
	{
		if(lastIndexStart < USBDEV_ID_MAX)
			curIndexStart = USBDEV_PAGESTARTID(USBDEVICE_LOGO_MAX);
		else
			curIndexStart = lastIndexStart - USBDEV_ID_MAX;	
	}
	usbDeviceOp.cur_sel_index = curIndexStart;
	usbDeviceLogoShow(handle);
	usbDeviceSelect(handle,usbDeviceOp.cur_sel_index,1);

	return 0;
}

ALIGNED(4) msgDealInfor usbDeviceMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	usbDeviceOpenWin},
	{SYS_CLOSE_WINDOW,	usbDeviceCloseWin},
	{SYS_CHILE_COLSE,	usbDeviceWinChildClose},
	{SYS_TOUCH_WINDOW,  usbDeviceTouchWin},
	{SYS_TOUCH_SLIDE_OFF,usbDeviceTouchSlideOff},	

	{KEY_EVENT_OK,		usbDeviceKeyMsgAll},
	{KEY_EVENT_UP,		usbDeviceKeyMsgAll},//lcd_up
	{KEY_EVENT_DOWN,	usbDeviceKeyMsgAll},
	{KEY_EVENT_MENU,	usbDeviceKeyMsgAll},
	{KEY_EVENT_MODE,	usbDeviceKeyMsgAll},
	{KEY_EVENT_PLAYVIDEO,usbDeviceKeyMsgAll},
	{KEY_EVENT_UVC_FORM,usbDeviceKeyMsgAll},//recordPhotoKeyMsgUvcForm //recordPhotoKeyMsgrotate
	{KEY_EVENT_UVC_FRAME,usbDeviceKeyMsgAll},
	{KEY_EVENT_ROTATE_ADD,usbDeviceKeyMsgAll},
	{KEY_EVENT_ROTATE_DEC,usbDeviceKeyMsgAll},
	{KEY_EVENT_LED,		usbDeviceKeyMsgAll},
	{KEY_EVENT_POWER,		usbDeviceKeyMsgAll},
	// {KEY_EVENT_ROTATE,		recordPhotoKeyMsgrotate},//翻转180°
	{KEY_EVENT_PHOTO,		usbDeviceKeyMsgAll},
	{SYS_EVENT_USBDEV,	usbDeviceSysMsgUSBDEV},
	{SYS_EVENT_USBHOST, usbDeviceSysMsgUSBHOST},

	{EVENT_MAX,NULL},
};

WINDOW(usbDeviceWindow,usbDeviceMsgDeal,usbDeviceWin)