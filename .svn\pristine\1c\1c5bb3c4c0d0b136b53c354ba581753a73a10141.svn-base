/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef __HAL_LCD_ROTATE_H
#define __HAL_LCD_ROTATE_H

enum rotate_channel_e {
    ROTATE_CH0, //video
    ROTATE_CH1, //ui
    ROTATE_CH_MAX,
};

/*******************************************************************************
* Function Name  : hal_rotateInit
* Description    : init rotate_opt
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_rotateInit(void);
/*******************************************************************************
* Function Name  : hal_rotateAdd
* Description    : add frames to rotate queue
* Input          : u8 ch
*                  lcdshow_frame_t * src
*                  lcdshow_frame_t * dst
* Output         : None
* Return         : None
*******************************************************************************/
void hal_rotateAdd(u8 ch,lcdshow_frame_t * src,lcdshow_frame_t * dst);

#endif
