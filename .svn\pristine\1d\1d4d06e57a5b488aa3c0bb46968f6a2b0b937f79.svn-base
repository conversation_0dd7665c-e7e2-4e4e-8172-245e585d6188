/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../../hal/inc/hal.h"

ALIGNED(4) HUSB_HANDLE* usensor_handle;

/*******************************************************************************
* Function Name  : husb_api_handle_reg
* Description    : husb_api_handle_reg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_handle_reg(void *handle)
{
	usensor_handle = (HUSB_HANDLE*)handle;
}

/*******************************************************************************
* Function Name  : husb_api_usensor_tran_sta
* Description    : husb_api_usensor_tran_sta
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_usensor_tran_sta(void)
{
	if(usensor_handle)
	{
		if(usensor_handle->usbsta.device_sta & USB_UVC_TRAN)
		{
			return true;
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_tran_sta
* Description    : husb_api_usensor_tran_sta
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_usensor_atech_sta(void)
{
	if(usensor_handle)
	{
		if(usensor_handle->usbsta.device_sta & USB_UVC_ATECH)
		{
			return true;
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_res_get
* Description    : husb_api_usensor_res_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
s32 husb_api_usensor_res_get(u16* width, u16* height)
{
	//*width  = 0;
	//*height = 0;
	if(usensor_handle)
	{
		*width  = usensor_handle->usbsta.usensor_res.width;
		*height = usensor_handle->usbsta.usensor_res.height;
		return 0;
	}
	return -1;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_res_get
* Description    : husb_api_usensor_res_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u32 husb_api_usensor_res_type_is_mjp(void)
{

	if(usensor_handle)
	{
		if(usensor_handle->usbsta.usensor_res.type == UVC_FORMAT_MJP)
		{
			return 1;
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_res_get
* Description    : husb_api_usensor_res_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u32 husb_api_usensor_res_type_is_yuv(void)
{

	if(usensor_handle)
	{
		if(usensor_handle->usbsta.usensor_res.type == UVC_FORMAT_YUV)
		{
			return 1;
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : husb_api_astern_set
* Description    : husb_api_astern_set
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_astern_set(bool sta)
{
	if(usensor_handle)
	{
		if(sta)
			usensor_handle->usbsta.device_sta |= USB_ASTERN;
		else
			usensor_handle->usbsta.device_sta &= ~USB_ASTERN;
	}
}
/*******************************************************************************
* Function Name  : husb_api_astern_get
* Description    : husb_api_astern_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_astern_get(void)
{
	if(usensor_handle)
	{
		if(usensor_handle->usbsta.device_sta & USB_ASTERN)
		{
			return true;
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : husb_api_detech_check
* Description    : husb_api_detech_check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_detech_check(void)
{
	if((usensor_handle)&&(usensor_handle->usbsta.device_sta & USB_DTECH))
		return true;
	return false;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_linkingLcd
* Description    : husb_api_usensor_linkingLcd:check fisrt frame and link to LCD SHOW
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_linkingLcd(void)
{
	husb_uvc_linking(usensor_handle);
}
/*******************************************************************************
* Function Name  : husb_api_usensor_relinkLcd_reg
* Description    : husb_api_usensor_relinkLcd_reg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_relinkLcd_reg(void)
{
	husb_uvc_relink_register(usensor_handle);
}
/*******************************************************************************
* Function Name  : husb_api_usensor_dcdown
* Description    : husb_api_usensor_dcdown
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_dcdown(u8 err)
{
	if(usensor_handle)
		huvc_cache_dcd_down(usensor_handle,err);
}
/*******************************************************************************
* Function Name  : husb_api_usensor_detech
* Description    : husb_api_usensor_detech: for software remove usb
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_detech(void)
{
	if(!husb_api_usensor_tran_sta()){
		return;
	}
	if(usensor_handle->usbsta.ch == USB20_CH)
	{
		husb_api_u20_remove();
	}else if(usensor_handle->usbsta.ch == USB11_CH)
	{
		husb_api_u11_remove();
	}
	usensor_handle->usbsta.device_sta = USB_DTECH;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_asterncheck
* Description    : husb_api_usensor_asterncheck: for usensor astern check kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_asterncheck(void)
{
	if(usensor_handle)
	{
		husb_api_ep0_asterncheck_kick(usensor_handle);
	}
}
/*******************************************************************************
* Function Name  : husb_api_usensor_asterncheck
* Description    : husb_api_usensor_asterncheck: for usensor astern check kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_asternset(u32 astern)
{
	if(usensor_handle)
	{
		if(astern)
			usensor_handle->usbsta.device_sta |= USB_ASTERN;
		else
			usensor_handle->usbsta.device_sta &= ~USB_ASTERN;
	}
}
/*******************************************************************************
* Function Name  : husb_api_usensor_frame_read
* Description    : husb_api_usensor_frame_read
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u8 *husb_api_usensor_frame_read(u32 *len)
{
	static u8 *pbuf;
	if(husb_api_usensor_tran_sta())
	{
		if(husb_uvc_frame_read(usensor_handle, &pbuf, len))
		{
			//if(hal_mjpDecodeParse((u8 *)pbuf,usensor_handle->usbsta.usensor_res.width,usensor_handle->usbsta.usensor_res.height) < 0)
			//{
			//	huvc_cache_dcd_down(usensor_handle,1);
			//	return NULL;
			//}
			return (u8 *)pbuf;
		}
	}
	return NULL;
}
/*******************************************************************************
* Function Name  : husb_api_usensor_csi_kick
* Description    : husb_api_usensor_csi_kick: kick usensor yuv input to csi
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_csi_kick(void)
{
	u8 *pbuf;
	u32 plen;
	if(husb_api_usensor_tran_sta()&&usensor_handle->usbsta.usensor_res.type == UVC_FORMAT_YUV)
	{
		//deg_Printf("[k:%x, %x]", XSFR_CSI_CON,XSFR_ISP_CON);

		//free last dcd frame
		huvc_cache_dcd_down(usensor_handle, 0);
		//get cur dcd frame
		if(husb_uvc_frame_read(usensor_handle, &pbuf, &plen))
		{
			hx330x_csiInputAddrSet((u32)pbuf);
			//deg_Printf("kick:%x\n", (u32)pbuf);
		}
	}
}
/*******************************************************************************
* Function Name  : husb_api_usensor_switch_res_kick
* Description    : husb_api_usensor_switch_res_kick: kick usensor to change format or frame
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_usensor_switch_res_kick(u8 switch_format, u8 switch_frame)
{
	u8 switch_type;
	if(switch_format)
	{

		if(usensor_handle->usbsta.usensor_res.type == UVC_FORMAT_MJP)
		{
			switch_type = UVC_FORMAT_YUV;
		}else
		{
			switch_type = UVC_FORMAT_MJP;
		}
		switch_frame = 0;
	}else
	{
		switch_type = usensor_handle->usbsta.usensor_res.type;
	}
	if(usensor_resolution_select(usensor_handle, switch_type,switch_frame) == false)
		return false;
	husb_uvc_detech(usensor_handle);
	
	usensor_handle->usbsta.device_sta  = (usensor_handle->usbsta.device_sta &~ (USB_UVC_ATECH|USB_UVC_TRAN|USB_ASTERN))|USB_UVC_SWITCH;
	HAL_HE_CRITICAL_INIT();
	HAL_HE_CRITICAL_ENTER();
	husb_api_ep0_uvc_switch_kick(usensor_handle);

	if(usensor_handle->usbsta.ch == USB20_CH)
	{
		//hx330x_intEnable(IRQ_USB20,0);
		hx330x_usb20_eprx_register(0, HUSB20_RXEP_UVC,NULL);

		XSFR_USB20_SIE_RX_INTFLG0; ////clr intr
		u32 index_temp	= XSFR_USB20_SIE_EPS;
		XSFR_USB20_SIE_EPS		= HUSB20_RXEP_UVC;
		XSFR_USB20_SIE_EPRX_CTRL0	= HUSB_EPRX_CLRDATATOG|HUSB_EPRX_FLUSHFIFO;
		XSFR_USB20_SIE_EPRX_CTRL1	= 0;
		XSFR_USB20_SIE_EPS		= index_temp;
		//husb_uvc_detech(usensor_handle);
		//usensor_handle->usbsta.device_sta  = USB_INSERT|USB_UVC_SWITCH;
		//hx330x_intEnable(IRQ_USB20,1);

	}else if(usensor_handle->usbsta.ch == USB11_CH)
	{
		//hx330x_intEnable(IRQ_USB11,0);
		hx330x_usb11_host_eprx_register(0, HUSB11_RXEP_UVC,NULL);
		XSFR_USB11_SIE_RX_INTFLG0;////clr intr
		u32 index_temp	= XSFR_USB11_SIE_EPS;
		XSFR_USB11_SIE_EPS		= HUSB11_RXEP_UVC;
		XSFR_USB11_SIE_EPRX_CTRL0	= HUSB_EPRX_CLRDATATOG|HUSB_EPRX_FLUSHFIFO;
		XSFR_USB11_SIE_EPRX_CTRL1	= 0;
		XSFR_USB11_SIE_EPS		= index_temp;
		//husb_uvc_detech(usensor_handle);
		//usensor_handle->usbsta.device_sta  = USB_INSERT|USB_UVC_SWITCH;
		//hx330x_intEnable(IRQ_USB11,1);
	}
	HAL_HE_CRITICAL_EXIT() ;
	//deg_Printf("husb_api_ep0_uvc_switch_kick\n");

	//deg_Printf("husb_api_ep0_uvc_switch_kick   end\n");
	return true;



}
/*******************************************************************************
* Function Name  : husb_api_usensor_notran_check
* Description    : husb_api_usensor_notran_check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_notran_check(void)
{
	//static u32 cnt_out = 0;
	if(husb_api_usensor_tran_sta())
	{
		//deg_Printf(" usensor fps:%d\n",usensor_handle->usbsta.uvc_fstack.jUcnt);
		if(usensor_handle->usbsta.uvc_fstack.jUcnt == 0)
		{
			usensor_handle->usbsta.uvc_fstack.notran_cnt++;
			if(usensor_handle->usbsta.uvc_fstack.notran_cnt >= 1)
			{
				usensor_handle->usbsta.uvc_fstack.notran_cnt = 0;
				deg_Printf("UVC[%d] no transfer!!\n",usensor_handle->usbsta.ch);
				if(usensor_handle->usbsta.ch == USB20_CH)
				{
					husb_api_u20_remove();
				}else if(usensor_handle->usbsta.ch == USB11_CH)
				{
					husb_api_u11_remove();
				}
			}
		}else
		{
			usensor_handle->usbsta.uvc_fstack.notran_cnt = 0;
		}

		usensor_handle->usbsta.uvc_fstack.jUcnt = 0;
	}
}
/*******************************************************************************
* Function Name  : husb_api_usensor_stop_fill
* Description    : husb_api_usensor_stop_fill
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_stop_fill(u32 stop)
{
	husb_uvc_stop_fill(usensor_handle, stop);
}


/*******************************************************************************
* Function Name  : husb_api_usensor_uvcunit_get
* Description    : husb_api_usensor_uvcunit_get
* Input          : u8 uvcunitsel: PU_BACKLIGHT_COMPENSATION_CONTROL ~ PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL
				   u8 request:GET_CUR/GET_MIN/GET_MAX/GET_RES
* Output         : None
* Return         : NULL: FAIL
*******************************************************************************/
u16 husb_api_usensor_uvcunit_get(u8 uvcunitsel, u8 request)
{
	if(husb_api_uvcunit_get_kick(usensor_handle, uvcunitsel, request) == false)
	{
		return 0;
	}
	return husb_api_uvcunit_get_done(usensor_handle);
}

/*******************************************************************************
* Function Name  : husb_api_usensor_uvcunit_set
* Description    : husb_api_usensor_uvcunit_set
* Input          : u8 uvcunitsel: PU_BACKLIGHT_COMPENSATION_CONTROL ~ PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL
				   u16 val
* Output         : None
* Return         : NULL: FAIL
*******************************************************************************/
bool husb_api_usensor_uvcunit_set(u8 uvcunitsel, u16 val)
{
	if(husb_api_uvcunit_set_kick(usensor_handle, uvcunitsel, val) == false)
	{
		return false;
	}
	return husb_api_uvcunit_set_done(usensor_handle);	
}
/*******************************************************************************
* Function Name  : husb_api_usensor_uvcunit_test
* Description    : husb_api_usensor_uvcunit_test: UVC测试用例
* Input          : None
* Output         : None
* Return         : NULL: FAIL
*******************************************************************************/
/*
void husb_api_usensor_uvcunit_test(void)
{
	u8 uvcunitsel,request;
	u16 val;
	for(uvcunitsel = PU_BACKLIGHT_COMPENSATION_CONTROL; uvcunitsel < PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL; uvcunitsel++)
	{
		for(request = GET_CUR; request <= GET_RES; request++)
		{
			val = husb_api_usensor_uvcunit_get(uvcunitsel, request);
			deg_Printf("uvcunitsel:%x, request:%x,val:%x\n", uvcunitsel,request,val);
			if(request == GET_CUR)
				husb_api_usensor_uvcunit_set(uvcunitsel, val);
		}
	}
}*/
