/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_UILZO_H
#define HX330X_UILZO_H

/*******************************************************************************
* Function Name  : hx330x_uiLzoISRRegiser
* Description    : csi isr register
* Input          : void (*isr)(int) : isr
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_uiLzoISRRegiser(void (*isr)(int));
/*******************************************************************************
* Function Name  : hx330x_uiLzoCheckBusy
* Description    : uilzo if not busy
* Input          : 
* Output         : none
* Return         : false : free,true : busy
*******************************************************************************/
bool hx330x_uiLzoCheckBusy(void);
/*******************************************************************************
* Function Name  : hx330x_uiLzoReset
* Description    : uilzo hardware reset
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_uiLzoReset(void);
/*******************************************************************************
* Function Name  : hx330x_uiLzoGetOutSize
* Description    : uilzo output size,unit : byte
* Input          : none
* Output         : none
* Return         : size
*******************************************************************************/
u32 hx330x_uiLzoGetOutSize(void);
/*******************************************************************************
* Function Name  : hx330x_uiLzoWaiDone
* Description    : uiLzo wating done
* Input          : none
* Output         : none
* Return         : true : success,false : timeout
*******************************************************************************/
bool hx330x_uiLzoWaiDone(void);
/*******************************************************************************
* Function Name  : hx330x_uiLzoStart
* Description    : config and start uiLzo,and waitting finish
* Input          : u8 * src : src buffer
                   u32 src_len : src data len(aligne 4)
                   u8 * dst : dst buffer
                   u32 * dst_len : dst buffer len
                   u8 reverse_en : lzo reverse (rotate 180)
* Output         : u32 * dst_len : lzo data len
* Return         : true : success,false : timeout or overflow
*******************************************************************************/
bool hx330x_uiLzoStart(u8 * src,u32 src_len,u8 * dst,u32 * dst_len,u8 reverse_en);
/*******************************************************************************
* Function Name  : hx330x_uiLzoKick
* Description    : config and kick uilzo
* Input          : u8 * src : src buffer
                   u32 src_len : src data len(aligne 4)
                   u8 * dst : dst buffer
                   u32 dstbuf_len : dst buffer len
                   u8 reverse_en : uilzo reverse (rotate 180)
                   u8 int_en : interrupt enable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_uiLzoKick(u8 * src,u32 src_len,u8 * dst,u32 dstbuf_len,u8 reverse_en,u8 int_en);

/*******************************************************************************
* Function Name  : hx330x_uiLzoIRQHandler
* Description    : uilzo isr
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_uiLzoIRQHandler(void);

#endif
