/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#ifndef  HAL_H
   #define  HAL_H


#include "../../mcu/inc/hx330x_cfg.h"
#include "../../dev/dev_api.h"
#include "hal_cfg.h"
#include "hal_sys.h"
#include "hal_adc.h"
#include "hal_csi.h"
#include "hal_isp.h"
#include "hal_dac.h"
#include "hal_eeprom.h"
#include "hal_gpio.h"
#include "hal_iic.h"
#include "hal_lcd.h"
#include "hal_lcdrotate.h"
#include "hal_lcdUi.h"
#include "hal_lcdUiLzo.h"
#include "hal_md.h"
#include "hal_rtc.h"
#include "hal_spi.h"
#include "hal_spi1.h"
#include "hal_timer.h"
#include "hal_uart.h"
#include "hal_dmauart.h"
#include "hal_watermark.h"
#include "hal_auadc.h"
#include "hal_mjpEncode.h"
#include "hal_mjpDecode.h"
#include "hal_wdt.h"
#include "hal_int.h"
#include "hal_stream.h"



#include "../../multimedia/multimedia_api.h"
#include "../../sys_manage/sys_manage_api.h"
#include "../../app/resource/user_res.h"

//---------------------------hal configure---------------------------------------



#define  HAL_CFG_SPI_BUS_MODE        SPI0_1_LINE

#define  HAL_CFG_SPI_READ_MODE       0  //0: standard read mode, 1: fast read mode








#endif