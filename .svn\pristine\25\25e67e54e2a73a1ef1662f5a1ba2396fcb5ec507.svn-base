/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : dev_ir_init
* Description    : dev_ir_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_ir_init(void)
{
	if(hardware_setup.ir_led_en)
	{
		hx330x_gpioDigitalSet(hardware_setup.ir_led_ch,hardware_setup.ir_led_pin,GPIO_DIGITAL_EN);		// set ir io is digital .
		return 0;
	}else
	{
		return -1;
	}
}

/*******************************************************************************
* Function Name  : dev_ir_ioctrl
* Description    : dev_ir_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_ir_ioctrl(u32 op, u32 para)
{
	if(hardware_setup.ir_led_en)
	{
		static u32 ir_state;
		if(op == DEV_IR_READ)
		{
			if(para)
				*(u32*)para = ir_state;
		}else if(op == DEV_IR_WRITE)
		{
			if(para) //IR ON
			{
				hal_gpioInit(hardware_setup.ir_led_ch, hardware_setup.ir_led_pin,GPIO_OUTPUT,GPIO_PULL_UP);
				hal_gpioWrite(hardware_setup.ir_led_ch,hardware_setup.ir_led_pin,GPIO_HIGH);
				ir_state = 1;			
			}else	//IR OFF
			{
				hal_gpioInit(hardware_setup.ir_led_ch, hardware_setup.ir_led_pin,GPIO_OUTPUT,GPIO_PULL_DOWN);
				hal_gpioWrite(hardware_setup.ir_led_ch,hardware_setup.ir_led_pin,GPIO_LOW);
				ir_state = 0;					
			}
		}
	}
	
	return 0;
}

