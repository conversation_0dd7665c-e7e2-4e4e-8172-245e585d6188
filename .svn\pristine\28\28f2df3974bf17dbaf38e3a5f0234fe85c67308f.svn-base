/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiWidgetProc
* Description    : uiWidgetProc:support MSG_WIDGET_GET_ID and MSG_WIDGET_SET_ID opt
* Input          : none
* Output         : none                                            
* Return         : true: msg process success
*******************************************************************************/
bool uiWidgetProc(uiWinMsg* msg)
{
	winHandle 	hWin  = msg->curWin;
	uiWidgetObj *pWin = (uiWidgetObj*)uiHandleToPtr(hWin);
	switch(msg->id)
	{
		case MSG_WIDGET_GET_ID:
			msg->para.v = pWin->id;
			return true;
		case MSG_WIDGET_SET_ID:
			pWin->id = msg->para.v;
			return true;
		default: break;
	}
	return false;
}
/*******************************************************************************
* Function Name  : uiWidgetSetType
* Description    : uiWidgetSetType: set widget's type
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWidgetSetType(winHandle hWin,u16 type)
{
	uiWidgetObj* pWin = (uiWidgetObj*)uiHandleToPtr(hWin);
	if(pWin == NULL)
		return;
	if((pWin->win.style&WIN_WIDGET) == 0) //NOT WIDGET
		return;
	pWin->type = type;
}
/*******************************************************************************
* Function Name  : uiWidgetSetType
* Description    : uiWidgetSetType: set widget's type
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
u16 uiWidgetGetType(winHandle hWin)
{
	uiWidgetObj* pWin = (uiWidgetObj*)uiHandleToPtr(hWin);
	if(pWin == NULL)
		return WIDGET_TYPE_MAX;
	if((pWin->win.style&WIN_WIDGET) == 0)//NOT WIDGET
		return WIDGET_TYPE_MAX;
	return pWin->type;
}
/*******************************************************************************
* Function Name  : uiWidgetGetId
* Description    : uiWidgetGetId: get widget's id
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
u16 uiWidgetGetId(winHandle hWin)
{
	uiWinMsg msg;
	msg.id 		= MSG_WIDGET_GET_ID;
	msg.para.v	= INVALID_WIDGET_ID;
	uiWinSendMsg(hWin,&msg);
	return msg.para.v;
}
/*******************************************************************************
* Function Name  : uiWidgetSetId
* Description    : uiWidgetSetId: set widget's id
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWidgetSetId(winHandle hWin,u16 id)
{
	uiWinMsg msg;
	msg.id		= MSG_WIDGET_SET_ID;
	msg.para.v	= id;
	uiWinSendMsg(hWin,&msg);
}
