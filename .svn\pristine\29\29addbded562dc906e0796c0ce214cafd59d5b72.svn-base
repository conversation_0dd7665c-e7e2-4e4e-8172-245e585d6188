/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef DEV_TYPEDEF_H
#define DEV_TYPEDEF_H



typedef struct DEV_NODE_S
{
	char name[12];

	int (*init)(void);

	int (*ioctrl)(INT32U op,INT32U para);

    INT32U prit;
}DEV_NODE_T;

#define  DEV_NAME_BATTERY      		"battery"
#define  DEV_NAME_GSENSOR      		"g-sensor"
#define  DEV_NAME_IR      	   		"ir"
#define  DEV_NAME_KEY        		"key"
#define  DEV_NAME_LCD          		"lcd"
#define  DEV_NAME_LED          		"led"
#define  DEV_NAME_SENSOR        	"sensor"
#define  DEV_NAME_SDCARD       		"sd-card"
#define  DEV_NAME_DUSB          	"usb-dev"
#define  DEV_NAME_HUSB          	"usb-host"
#define  DEV_NAME_TP          	    "touch-panel"	
#define	 DEV_NAME_LED_PWM			"led_pwm"

#define __IO_R_(x)     				(x|0x00000000)
#define __IO_W_(x)     				(x|0x10000000)
//----------io command-----------------
#define  DEV_BATTERY_READ			__IO_R_(0x100)
#define  DEV_BATTERY_WRITE			__IO_W_(0x100)

#define  DEV_GSENSOR_LOCK_READ		__IO_R_(0x200)
#define  DEV_GSENSOR_PARK_READ		__IO_R_(0x201)
#define  DEV_GSENSOR_NAME_READ		__IO_R_(0x202)
#define  DEV_GSENSOR_MOTION_STABLE	__IO_W_(0x200)
#define  DEV_GSENSOR_LOCK_WRITE		__IO_W_(0x201)
#define  DEV_GSENSOR_PARK_WRITE		__IO_W_(0x202)


#define  DEV_IR_READ				__IO_R_(0x300)
#define  DEV_IR_WRITE				__IO_W_(0x300)

#define  DEV_KEY_AD_READ			__IO_R_(0x400)
#define  DEV_KEY_POWER_READ			__IO_R_(0x401)
#define  DEV_KEY_AD_WRITE			__IO_W_(0x400)
#define  DEV_KEY_POWER_WRITE		__IO_W_(0x401)

#define  DEV_LCD_BK_READ			__IO_R_(0x500)
#define  DEV_LCD_BK_WRITE			__IO_W_(0x500)
#define  DEV_LCD_OFF_WRITE			__IO_W_(0x501)
#define  DEV_LCD_NOCOLOR_CHANGE		__IO_W_(0x502)

#define  DEV_LED_READ				__IO_R_(0x600)
#define  DEV_LED_WRITE				__IO_W_(0x600)
#define	 DEV_LED_PWM_ADJUST			__IO_W_(0x601)
#define	 DEV_LED_ON_OFF_READ		__IO_R_(0x602)
#define	 DEV_LED_ON_OFF_WRITE		__IO_W_(0x602)

#define  DEV_SENSOR_INIT			__IO_W_(0x700)
#define  DEV_SENSOR_UINIT			__IO_W_(0x701)
#define  DEV_SENSOR_CHECK			__IO_W_(0x702)

#define  DEV_SDCARD_READ			__IO_R_(0x800)
#define  DEV_SDCARD_WRITE			__IO_W_(0x800)

#define  DEV_DUSB_PWR_CHECK			__IO_R_(0x900)
#define  DEV_DUSB_HW_CON_CHECK		__IO_R_(0x901)
#define  DEV_DUSB_SW_CON_CHECK		__IO_R_(0x902)
#define  DEV_DUSB_INIT				__IO_W_(0x900)
#define  DEV_DUSB_ONLINE_SET		__IO_W_(0x901)


#define  DEV_HUSB_DET_CHECK			__IO_R_(0xA00)
#define  DEV_HUSB_TYPE_CHECK		__IO_R_(0xA01)
#define  DEV_HUSB_POWER_CTRL		__IO_W_(0xA00)
#define  DEV_HUSB_INIT				__IO_W_(0xA01)

#define  DEV_TOUCHPANEL_READ		__IO_R_(0xB00)

//-----------power flag-------------------
typedef enum
{
	POWERON_FLAG_KEY		= (1 << 0),
	POWERON_FLAG_DCIN		= (1 << 1),
	POWERON_FLAG_GSENSOR	= (1 << 2),
	POWERON_FLAG_RTC		= (1 << 3),
	POWERON_FLAG_MASK		= (0x0f << 0),
	POWERON_FLAG_FIRST		= (1 << 4),		//first power on
	POWERON_FLAG_WAIT		= (1 << 5),		//first power on record wait
	//POWERON_FLAG_KEEPON		= (1 << 7),	//1: POWER KEEP ON, 0: POWER auto off

}POWERON_FLAG;

//-----------battery stat-------------------
typedef enum
{
	BATTERY_STAT_0 = 0,
	BATTERY_STAT_1,
	BATTERY_STAT_2,
	BATTERY_STAT_3,
	BATTERY_STAT_4,
	BATTERY_STAT_5,

	BATTERY_STAT_MAX
}BATTERY_STAT;

//----------key------------
typedef enum
{
	KEY_PRESSED =0,
	KEY_CONTINUE,
	KEY_RELEASE,
	KEY_STATE_INVALID,
}KEY_STATE_T;
//----------srnsor stat----------------
typedef enum
{
	SENSOR_STAT_NULL = 0,
	SENSOR_STAT_NORMAL,

	SENSOR_STAT_MAX
}SENSOR_STATE;
//----------sd card stat----------------
typedef enum
{
	SDC_STAT_NULL = 0,
	SDC_STAT_UNSTABLE,
	SDC_STAT_IN,
	SDC_STAT_ERROR,
	SDC_STAT_FULL,
	SDC_STAT_NORMAL,

	SDC_STAT_MAX
}SDC_STATE;
//-----------usb dev stat-------------------
typedef enum
{
	USBDEV_STAT_NULL = 0,
	USBDEV_STAT_DCIN,
	USBDEV_STAT_DEVIN_CHECK,
	USBDEV_STAT_DEVIN,
	USBDEV_STAT_PC,

	USBDEV_STAT_MAX
}USBDEV_STATE;
typedef enum
{
	USB_DEV_MASS = 0,
	USB_DEV_PCCAMERA,
	USB_DEV_RECORD,
	USB_DEV_MAX_TYPE,
}USB_DEV_TYPE;
//-----------usb host stat-------------------
typedef enum
{
	USBHOST_STAT_NULL = 0,
	USBHOST_STAT_WAIT_STABLE,
	USBHOST_STAT_PWR_ON,
	USBHOST_STAT_IN,
	USBHOST_STAT_SHOW,
	USBHOST_STAT_SHOWOFF,
	USBHOST_STAT_ASTERN,
	USBHOST_STAT_SOFT_STOP,
	USBHOST_STAT_SOFT_RESUME,
	USBHOST_STAT_OUT,
	USBHOST_STAT_MAX
}USBHOST_STATE;
typedef enum{
	HUSB_POWER_OFF = 0,
	HUSB_POWER_ON,
	//HUSB_POWER_FAST,
}HUSB_POWER_CTL;

typedef enum{
	MSG_RECORD_START = 0,
	MSG_RECORD_STOP,
	MSG_RECORD_RESUME,
	MSG_RECORD_RESTART,
	MSG_RECORD_LOCK,
	MSG_RECORD_ERROR,
	MSG_RECORD_MAX,
}MSG_RECORD_TYPE;
typedef enum{
	MSG_PLAY_START = 0,
	MSG_PLAY_STOP,
	MSG_PLAY_RESUME,
	MSG_PLAY_RESTART,
	MSG_PLAY_LOCK,
	MSG_PLAY_ERROR,
	MSG_PLAY_MAX,
}MSG_PLAY_TYPE;



//------------system event table-------------------------
typedef enum
{
	SYS_EVENT_SDC = 1,   // sdc pulled out or in
	SYS_EVENT_USBDEV,   // usb dev pulled out or in
	SYS_EVENT_USBHOST,  // usb host pulled out or in
	SYS_EVENT_BAT,    	// battery state
	SYS_EVENT_FMT,    	// sdc formate
	SYS_EVENT_MD,    	// motion detetion
	SYS_EVENT_500MS,
	SYS_EVENT_1S,
	SYS_EVENT_TIME_UPDATE,
	SYS_OPEN_WINDOW,
	SYS_CLOSE_WINDOW,
	SYS_CHILE_COLSE,
	SYS_CHILE_OPEN,
	SYS_TOUCH_WINDOW,
	SYS_TOUCH_SLIDE_ON,
	SYS_TOUCH_SLIDE_OFF,
	SYS_DRAW_UI,
	SYS_EVENT_RECORD,
	SYS_EVENT_PLAY,
	SYS_EVENT_100MS,
	SYS_EVENT_MAX
}SYS_EVENT_T;
typedef enum
{
	KEY_EVENT_START	= SYS_EVENT_MAX,
	KEY_EVENT_OK	= KEY_EVENT_START,
	KEY_EVENT_UP,
	KEY_EVENT_DOWN,
	KEY_EVENT_MENU,
	KEY_EVENT_MODE,
	KEY_EVENT_PLAYVIDEO,
	KEY_EVENT_UVC_FORM,
	KEY_EVENT_UVC_FRAME,
	KEY_EVENT_ROTATE_ADD,
	KEY_EVENT_ROTATE_DEC,
	KEY_EVENT_LED,
	KEY_EVENT_ROTATE,
	KEY_EVENT_PHOTO,
	KEY_EVENT_VIDEO,
	KEY_EVENT_POWER,
	KEY_EVENT_POWEROFF,
	KEY_EVENT_END,
	EVENT_MAX	=	KEY_EVENT_END,
	
}KEY_EVENT_T;



//-----------device msg id-----------------
enum
{
	DEVICE_MSG_NULL=0,
	DEVICE_MSG_SDC,
	DEVICE_MSG_USB,
	DEVICE_MSG_BATTERY,
	DEVICE_MSG_GSENSOR,

	DEVICE_MSG_MAX
};







#endif
