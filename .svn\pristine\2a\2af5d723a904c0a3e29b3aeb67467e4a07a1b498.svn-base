/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef RES_IMAGE_API_H
#define RES_IMAGE_API_H

/*******************************************************************************
* Function Name  : res_image_show
* Description    : res_image_show 
* Input          : INT16U idx resource id
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
int res_image_show(INT32U idx);

/*******************************************************************************
* Function Name  : res_image_decode
* Description    : res_image_show 
* Input          : INT16U idx resource id
* 				   INT8U *buffer
*                  INT16U dst_width
*                  INT16U dst_height
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
int res_image_decode(INT32U idx, INT8U *buffer, INT16U dst_width, INT16U dst_height);

#endif
