/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT == LCD_RGB_ILI9806E

#define CMD(x)    LCD_CMD_RGB_DAT(x)
#define DAT(x)    LCD_CMD_RGB_DAT((x)|(1<<8))
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
//****************************** Page 1 Command ******************************//
//****************************************************************************//
    CMD(0xFF), DAT(0XFF), DAT(0x98), DAT(0x06), DAT(0x04), DAT(0x01),    // Change to Page 1
    CMD(0x08), DAT(0x10),                //Output SDA
    CMD(0x20), DAT(0x01),                //DE=1 Active
    // B0: 0:DE Low enable,1:DE High enable
    // B1: 0:PCLK fetched rising,1:PCLK fetched falling
    // B2: 0:HS Low enable,1:HS High enable
    // B3: 0:VS Low enable,1:VS High enable
    CMD(0x21), DAT(0x0D),                //DE=1 Active
    //CMD(0x21), DAT(0x01),                //DE=1 Active

    CMD(0x30), DAT(0x03),                // 480 X 640
    CMD(0x31), DAT(0x00),                // Column Inversion

    CMD(0x40), DAT(0x15),
    CMD(0x41), DAT(0x34),
    CMD(0x42), DAT(0x12),
    CMD(0x43), DAT(0x03),
    CMD(0x44), DAT(0x09),
    CMD(0x45), DAT(0x0A),
    CMD(0x46), DAT(0x44),
    CMD(0x47), DAT(0x44),

    CMD(0x50), DAT(0x88),
    CMD(0x51), DAT(0x88),
    CMD(0x52), DAT(0x00),
    CMD(0x53), DAT(0x4C),
    CMD(0x54), DAT(0x00),
    CMD(0x55), DAT(0x54),

    //CMD(0x57), DAT(0x50),
    CMD(0x60), DAT(0x06),
    CMD(0x61), DAT(0x00),
    CMD(0x62), DAT(0x07),
    CMD(0x63), DAT(0x00),
    //++++++++++++++++++ Gamma Setting ++++++++++++++++++//
    CMD(0xA0), DAT(0x00),
    CMD(0xA1), DAT(0x11),
    CMD(0xA2), DAT(0x19),
    CMD(0xA3), DAT(0x06),
    CMD(0xA4), DAT(0x0B),
    CMD(0xA5), DAT(0x0D),
    CMD(0xA6), DAT(0x0D),
    CMD(0xA7), DAT(0x0B),
    CMD(0xA8), DAT(0x03),
    CMD(0xA9), DAT(0x08),
    CMD(0xAA), DAT(0x0E),
    CMD(0xAB), DAT(0x08),
    CMD(0xAC), DAT(0x0E),
    CMD(0xAD), DAT(0x17),
    CMD(0xAE), DAT(0x0E),
    CMD(0xAF), DAT(0x00),
    CMD(0xC0), DAT(0x00),
    CMD(0xC1), DAT(0x11),
    CMD(0xC2), DAT(0x19),
    CMD(0xC3), DAT(0x0C),
    CMD(0xC4), DAT(0x0B),
    CMD(0xC5), DAT(0x0D),
    CMD(0xC6), DAT(0x0E),
    CMD(0xC7), DAT(0x0C),
    CMD(0xC8), DAT(0x03),
    CMD(0xC9), DAT(0x08),
    CMD(0xCA), DAT(0x0F),
    CMD(0xCB), DAT(0x08),
    CMD(0xCC), DAT(0x0F),
    CMD(0xCD), DAT(0x16),
    CMD(0xCE), DAT(0x0F),
    CMD(0xCF), DAT(0x00),

//****************************************************************************//
//****************************** Page 6 Command ******************************//
//****************************************************************************//
    CMD(0xFF), DAT(0xFF), DAT(0x98), DAT(0x06), DAT(0x04), DAT(0x06),     // Change to Page 6
    CMD(0x00), DAT(0x21),
    CMD(0x01), DAT(0x0A),  //STV START
    CMD(0x02), DAT(0x00),
    CMD(0x03), DAT(0x05),
    CMD(0x04), DAT(0x01),  //FTI_1_Rise[ 7:0]
    CMD(0x05), DAT(0x01),  //FTI_2_Rise[ 7:0]
    CMD(0x06), DAT(0x98),
    CMD(0x07), DAT(0x06),  //CLK_A_Rise[ 7:0]
    CMD(0x08), DAT(0x01),  //CLK_A_Fall[ 7:0]
    CMD(0x09), DAT(0x00),  //CLK_B_Rise[10:8]_CLK_Keep[4]
    CMD(0x0A), DAT(0x00),  //CLK_B_Rise[ 7:0]
    CMD(0x0B), DAT(0x00),
    CMD(0x0C), DAT(0x01),  //CLW_1_Rise[ 7:0]
    CMD(0x0D), DAT(0x01),  //CLW_2_Rise[ 7:0]
    CMD(0x0E), DAT(0x00),  //GPM_1[ 7:0]
    CMD(0x0F), DAT(0x00),  //GPM_2[ 7:0]
    CMD(0x10), DAT(0xF7),  //Phase_STV_A[7:6]_Overlap_STV_A[5:4]_Phase_STV_B[3:2]_Overlap_STV_B[1:0]
    CMD(0x11), DAT(0xF0),  //Phase_CLK[7:6]_Overlap_CLK[5:4]
    CMD(0x12), DAT(0x00),
    CMD(0x13), DAT(0x00),  //reg_CLK_Keep_Pos1[7:0]
    CMD(0x14), DAT(0x00),  //reg_CLK_Keep_Pos2[7:0]
    CMD(0x15), DAT(0xC0),  //GIP_01[7] & GIP_10[6]
    CMD(0x16), DAT(0x08),
    CMD(0x17), DAT(0x00),  //FTI_1_Fall[7:0]
    CMD(0x18), DAT(0x00),  //FTI_2_Fall[7:0]
    CMD(0x19), DAT(0x00),  //GPM_3[7:0]
    CMD(0x1A), DAT(0x00),
    CMD(0x1B), DAT(0x00),
    CMD(0x1C), DAT(0x00),
    CMD(0x1D), DAT(0x00),

    CMD(0x20), DAT(0x01),
    CMD(0x21), DAT(0x23),
    CMD(0x22), DAT(0x44),
    CMD(0x23), DAT(0x67),
    CMD(0x24), DAT(0x01),
    CMD(0x25), DAT(0x23),
    CMD(0x26), DAT(0x45),
    CMD(0x27), DAT(0x67),

    CMD(0x30), DAT(0x01),  //BACKWARDS[4]_FORWARDS[0]
    CMD(0x31), DAT(0x22),  //GOUT_01_BW_MUX[7:4]_GOUT_01_FW_MUX[3:0]
    CMD(0x32), DAT(0x22),  //GOUT_02_BW_MUX[7:4]_GOUT_02_FW_MUX[3:0]
    CMD(0x33), DAT(0xBC),  //GOUT_03_BW_MUX[7:4]_GOUT_03_FW_MUX[3:0] CLK1
    CMD(0x34), DAT(0xAD),  //GOUT_04_BW_MUX[7:4]_GOUT_04_FW_MUX[3:0] CLK3
    CMD(0x35), DAT(0xDA),  //GOUT_05_BW_MUX[7:4]_GOUT_05_FW_MUX[3:0] CLK5
    CMD(0x36), DAT(0xCB),  //GOUT_06_BW_MUX[7:4]_GOUT_06_FW_MUX[3:0] CLK7
    CMD(0x37), DAT(0x22),  //GOUT_07_BW_MUX[7:4]_GOUT_07_FW_MUX[3:0] VGL
    CMD(0x38), DAT(0x55),  //GOUT_08_BW_MUX[7:4]_GOUT_08_FW_MUX[3:0] GCH
    CMD(0x39), DAT(0x76),  //GOUT_09_BW_MUX[7:4]_GOUT_09_FW_MUX[3:0] STV1
    CMD(0x3A), DAT(0x67),  //GOUT_10_BW_MUX[7:4]_GOUT_10_FW_MUX[3:0] STV3
    CMD(0x3B), DAT(0x88),  //GOUT_11_BW_MUX[7:4]_GOUT_11_FW_MUX[3:0] STV0
    CMD(0x3C), DAT(0x22),  //GOUT_12_BW_MUX[7:4]_GOUT_12_FW_MUX[3:0] GCL
    CMD(0x3D), DAT(0x11),  //GOUT_13_BW_MUX[7:4]_GOUT_13_FW_MUX[3:0] VDS
    CMD(0x3E), DAT(0x00),  //GOUT_14_BW_MUX[7:4]_GOUT_14_FW_MUX[3:0] VSD
    CMD(0x3F), DAT(0x22),  //GOUT_15_BW_MUX[7:4]_GOUT_15_FW_MUX[3:0]
    CMD(0x40), DAT(0x22),  //GOUT_16_BW_MUX[7:4]_GOUT_16_FW_MUX[3:0]
    CMD(0x52), DAT(0x10),
    CMD(0x53), DAT(0x10),    //LCD_ILI9806E_INDEX(0x12) for VGLO tie VGL_REG
    CMD(0x54), DAT(0x13),    //LVD for XOA GIP

    //****************************************************************************//
    //****************************** Page 7 Command ******************************//
    //****************************************************************************//

    CMD(0xFF), DAT(0xFF), DAT(0x98), DAT(0x06), DAT(0x04), DAT(0x07),    // Change to Page 7
    CMD(0x17), DAT(0x22),             //Default  //LCD_ILI9806E_INDEX(0x32) for VGLO tie VGL_REG
    CMD(0x18), DAT(0x1D),
    CMD(0x02), DAT(0x77),
    CMD(0xE1), DAT(0x79),
    CMD(0x06), DAT(0x13),
    CMD(0x26), DAT(0xB2),
    CMD(0xB3), DAT(0x10),             //LVD for XOA setting
    //+++++++++++++++++++++++++++++++++++++++++++++++++++//
    //****************************************************************************//
    //****************************** Page 0 Command ******************************//
    //****************************************************************************//
    CMD(0xFF), DAT(0xFF), DAT(0x98), DAT(0x06), DAT(0x04), DAT(0x00),     // Change to Page 0
    DLY(10),
    CMD(0x11), DAT(0x00),                 // Sleep-Out
    DLY(120),
    CMD(0x29), DAT(0x00),// Display On
    DLY(10),

LCD_INIT_TAB_END()
//关屏
LCD_UNINIT_TAB_BEGIN()  
    CMD(0x28),
    DLY(10),
LCD_UNINIT_TA_ENDB()

LCD_DESC_BEGIN()
    .name 			= "RGB_ILI9806E",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_24,

    .pclk_div 		= LCD_PCLK_DIV(24000000),
    .clk_per_pixel 	= 1,
    .even_order 	= LCD_BGR,
    .odd_order 		= LCD_BGR,

    .pclk_edge      = LCD_PCLK_EDGE_FALLING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw 			= 1,
    .vbp 			= 20,
    .vfp 			= 10,

    .hlw 			= 2,
    .hbp 			= 3,
    .hfp 			= 140,

    LCD_SPI_DEFAULT(9),

    .data_mode 	= LCD_DATA_MODE0_24BIT_RGB888,

    .screen_w 		= 480,
    .screen_h 		= 640,

    .video_w  		= 640,
    .video_h 	 	= 480,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
    UNINIT_TAB_INIT
LCD_DESC_END()


#endif








