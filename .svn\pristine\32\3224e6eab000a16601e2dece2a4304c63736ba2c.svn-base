/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_INT_H
    #define HX330X_INT_H


typedef enum
{
    IRQ_TIMER0=0,	//0
    IRQ_TIMER1,		//1
    IRQ_TIMER2,		//2
    IRQ_TIMER3,		//3
	IRQ_LCDC,		//4
	IRQ_RTC_WDT,	//5
	IRQ_DAC,		//6
	IRQ_GPIO,		//7
	IRQ_UART0,		//8
    IRQ_SPI0,		//9
	IRQ_SDC0,		//10
	IRQ_SDC1,		//11
	IRQ_SPI1,		//12
	IRQ_LVD,		//13
	IRQ_CSI,		//14
	IRQ_USB20,		//15
	IRQ_JPGA,		//16
    IRQ_IIC0_IIC1,	//17
    IRQ_AUADC,		//18
	IRQ_MCP0,		//19
	IRQ_DMAUART,		//20
	IRQ_LCDSHOW,	//21
    IRQ_JPGB,       //22
	IRQ_ROTATE,		//23
    IRQ_MCP1,       //24
    IRQ_MCP2,       //25
    IRQ_EMI,        //26
    IRQ_UILZO,     //27
    IRQ_PMU,        //28
    IRQ_USB11,      //29
    IRQ_USB11MCP,   //30
    IRQ_TT_MIPI,    //31
    
	IRQ_MAX
}IRQ_IDX_E;


/*******************************************************************************
* Function Name  : hx330x_intEnable
* Description    : int enable set
* Input          : u8 irqNum : irq index,in IRQ_IDX_E
                   u8 en     : 1->enable,0->disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_intEnable(u8 irqNum,u8 en);

/*******************************************************************************
* Function Name  : hx330x_int_priority
* Description    : int enable set
* Input          : u8 irqNum : irq index,in IRQ_IDX_E
                   u8 level     : 1->high priority,0->low priority
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_int_priority(u8 irqNum,u8 level);

/*******************************************************************************
* Function Name  : hx330x_intInit
* Description    : int initial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_intInit(void);

/*******************************************************************************
* Function Name  : soft_delay
* Description    : soft_delay 10us RC 2M
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void soft_delay(u32 t);
/*******************************************************************************
* Function Name  : boot_getChipSN
* Description    : get chip serial number, unique id
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
u32 boot_getChipSN(void);
/*******************************************************************************
* Function Name  : boot_vddrtcCalculate
* Description    : Calculate vddrtc voltage
* Input          : u32 Artc : adc sample value of vddrtc
*                  u32 Abg  : adc sample value of bandgap
* Output         : none
* Return         : vddrtc voltage(unit : mV)
*******************************************************************************/
u32 boot_vddrtcCalculate(u32 Artc,u32 Abg);
/*******************************************************************************
* Function Name  : boot_putchar
* Description    : boot_putchar
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void boot_putchar(char c);
/*******************************************************************************
* Function Name  : boot_uart_puts
* Description    : boot_uart_puts
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void boot_uart_puts(const char * str);
/*******************************************************************************
* Function Name  : exception
* Description    : exception
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void exception(u32 except_id);
/*******************************************************************************
* Function Name  : exception_trigger
* Description    : exception_trigger:触发exception
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void exception_trigger(void);


/*******************************************************************************
* Function Name  : hx330x_intCriticalInit
* Description    : critical init
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
#define hx330x_intCriticalInit()     u32 cpu_sr = 0
/*******************************************************************************
* Function Name  : hx330x_intCriticalEnter
* Description    : critical enter
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
#define hx330x_intCriticalEnter()   do{cpu_sr = SPR_CPU_SR & (1<<1);SPR_CPU_SR &=~(1<<1);barrier();}while(0)
/*******************************************************************************
* Function Name  : hx330x_intCriticalExit
* Description    : critical leave
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
#define hx330x_intCriticalExit()    do{SPR_CPU_SR |= cpu_sr;}while(0)
/*******************************************************************************
* Function Name  : hx330x_intCriticalInit
* Description    : critical init
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
#define hx330x_intHECriticalInit()     u32 cpu_he_sr = 0
/*******************************************************************************
* Function Name  : hx330x_intCriticalEnter
* Description    : critical enter
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
#define hx330x_intHECriticalEnter()   do{cpu_he_sr = SPR_CPU_SR & ((1<<1)|(1<<2));SPR_CPU_SR &=~((1<<1)|(1<<2));barrier();}while(0)
/*******************************************************************************
* Function Name  : hx330x_intCriticalExit
* Description    : critical leave
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
#define hx330x_intHECriticalExit()    do{SPR_CPU_SR |= cpu_he_sr;}while(0)

#endif
