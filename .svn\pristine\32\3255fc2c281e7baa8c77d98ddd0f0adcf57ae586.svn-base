/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_PLAY_AUDIO_H
#define  __TASK_PLAY_AUDIO_H


													
EXTERN_WINDOW(playAudioWindow);
typedef struct PLAYAUDIO_OP_S
{
	//u32  stat;
	//u32  playTotalTime;
	//u32  playLastTime;
	//u32  playCurTime;
	char (*file_fullname)[FILE_FULLNAME_LEN];
}PLAYAUDIO_OP_T;
extern PLAYAUDIO_OP_T  playAudioOp;
extern sysTask_T taskPlayAudio;
/*******************************************************************************
* Function Name  : app_taskPlayAudio_start
* Description    : APP LAYER: app_taskPlayAudio_start
* Input          : int idx
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
int app_taskPlayAudio_start(int idx);






#endif
