/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#define TP_MOVE_TH		8 // 100
UNUSED ALIGNED(4) static const TOUCHPANEL_OP_T  *tp_pool[ ]=
{
	//&tp_ns2009, //注意ns2009没有id，无法适配，如果打开，会直接选择这个tp
	&tp_icnt81,
	
};
ALIGNED(4) TOUCHPANEL_API_T tp_api_t;
/*******************************************************************************
* Function Name  : touchpanel_reset
* Description    : touchpanel_reset
* Input          : 1: reset, 0: reset release
* Output         : none
* Return         : none
*******************************************************************************/
static void touchpanel_reset(u8 reset)
{
	if(hardware_setup.tp_en && hardware_setup.tp_reset_en)
	{
		if(hardware_setup.tp_reset_valid == reset) //0：低电平reset， 1： 高电平reset
		{
			hal_gpioWrite(hardware_setup.tp_reset_ch,hardware_setup.tp_reset_pin,GPIO_HIGH);
		}else
		{
			hal_gpioWrite(hardware_setup.tp_reset_ch,hardware_setup.tp_reset_pin,GPIO_LOW);
		}
	}
}
/*******************************************************************************
* Function Name  : dev_touchpanel_Init
* Description    : initial touchpanel
* Input          : none
* Output         : none
* Return         : wake up state
*******************************************************************************/
static TOUCHPANEL_INFO_T* touchpanel_GetInfo(u32 moveThreshold)
{
	static TP_POINT_T lastPoint;
	static int restart = 1;
	u32 distanceX, distanceY;
	int ret;
	int retry_cnt = 3;
	TP_POINT_T curPoint = {0, 0};
	if(tp_api_t.tp_op == NULL)
	{
		goto END;
	}
	while(1)
	{
		ret = tp_api_t.tp_op->getPoint(&curPoint);
		if(ret >= 0)
		{
			break;
		}
			
		if(retry_cnt > 0)
		{
			retry_cnt--;
		}else
		{
			break;
		}
	}
	if(ret > 0) //-1: ERROR , 0: NONE TOUCH, 1: TOUCH
	{
		curPoint.x = curPoint.x * tp_api_t.lcdSreen_w/tp_api_t.tp_op->tp_width;
		curPoint.y = curPoint.y * tp_api_t.lcdSreen_h/tp_api_t.tp_op->tp_height;
		if( hx330x_data_check(curPoint.x,tp_api_t.lcdui_sx,tp_api_t.lcdui_ex) &&
		    hx330x_data_check(curPoint.y,tp_api_t.lcdui_sy,tp_api_t.lcdui_ey))
		{
			tp_api_t.tp_info.x = curPoint.x - tp_api_t.lcdui_sx;
			tp_api_t.tp_info.y = curPoint.y - tp_api_t.lcdui_sy;
		}else
		{
			ret = 0;
		}
	}
	if(ret > 0)
	{
		if(restart)
		{
			restart = 0;
			tp_api_t.tp_info.tp_type = TP_TYPE_NONE;
			goto END;
		}
		distanceX = hx330x_abs(curPoint.x - lastPoint.x);
		distanceY = hx330x_abs(curPoint.y - lastPoint.y);
		if(tp_api_t.tp_info.tp_type == TP_TYPE_MOVE || distanceX >= moveThreshold || distanceY >= moveThreshold)
		{
			if(distanceX <= 3 && distanceY <= 3)
			{
				tp_api_t.tp_info.tp_speed = 0;
				goto END;
			}
			tp_api_t.tp_info.tp_type = TP_TYPE_MOVE;
			if(distanceX > distanceY)
			{
				if(curPoint.x > lastPoint.x)
				{
					tp_api_t.tp_info.tp_dir = TP_DIR_RIGHT;
				}else
				{
					tp_api_t.tp_info.tp_dir = TP_DIR_LEFT;
				}
				tp_api_t.tp_info.tp_speed = distanceX*1000/tp_api_t.lcdSreen_w;

			}else
			{
				if(curPoint.y > lastPoint.y)
				{
					tp_api_t.tp_info.tp_dir = TP_DIR_DOWN;
				}else
				{
					tp_api_t.tp_info.tp_dir = TP_DIR_UP;
				}
				tp_api_t.tp_info.tp_speed = distanceY*1000/tp_api_t.lcdSreen_h;
			}
		}else
		{
			tp_api_t.tp_info.tp_type = TP_TYPE_PRESS;
		}
	}else if(ret < 0)
	{
		restart = 1;
		tp_api_t.tp_info.tp_type = TP_TYPE_ERROR;
	}else
	{
		if(restart == 0 && tp_api_t.tp_info.tp_type == TP_TYPE_NONE)
		{
			tp_api_t.tp_info.tp_type = TP_TYPE_PRESS;
			
			goto END;
		}
		restart = 1;
		tp_api_t.tp_info.tp_type = TP_TYPE_NONE;
	}
END:
	lastPoint.x = curPoint.x;
	lastPoint.y = curPoint.y;
	return &tp_api_t.tp_info;
}
/*******************************************************************************
* Function Name  : dev_touchpanel_Init
* Description    : initial touchpanel
* Input          : none
* Output         : none
* Return         : wake up state
*******************************************************************************/
int dev_touchpanel_Init(void)
{
	tp_api_t.tp_op = NULL;
	tp_api_t.tp_info.tp_type = TP_TYPE_NONE;
	if(hardware_setup.tp_en)
	{
		int i;
		touchpanel_reset(0); //0: reset release
		if(hardware_setup.tp_reset_en)
			hal_gpioInit(hardware_setup.tp_reset_ch,hardware_setup.tp_reset_pin,GPIO_OUTPUT,GPIO_PULL_UP);
		touchpanel_reset(1); //1: reset
		XOSTimeDly(20);
		touchpanel_reset(0); //0: reset release
		XOSTimeDly(60);
		tp_iic_init();

		for(i = 0; i < sizeof(tp_pool)/sizeof(tp_pool[0]); i++)
		{
			if(tp_pool[i] != NULL)
			{
				if(tp_pool[i]->init)
					tp_pool[i]->init();
				if(tp_pool[i]->match && tp_pool[i]->getPoint)
				{
					if(tp_pool[i]->match())
					{
						tp_api_t.tp_op = (TOUCHPANEL_OP_T *)tp_pool[i];
						break;
					}
				}
			}
		}
		if(tp_api_t.tp_op == NULL)
		{
			deg_Printf("tp : initial fail.can not find touch panel.\n");
			return -1;
		}else
		{
			hal_lcdGetSreenResolution(&tp_api_t.lcdSreen_w,&tp_api_t.lcdSreen_h);
			hal_lcdGetUiPosition(&tp_api_t.lcdui_sx,&tp_api_t.lcdui_sy);
			hal_lcdGetUiResolution(&tp_api_t.lcdui_ex,&tp_api_t.lcdui_ey);
			tp_api_t.lcdui_ex += tp_api_t.lcdui_sx;
			tp_api_t.lcdui_ey += tp_api_t.lcdui_sy;
			deg_Printf("tp : initial %s ok.TP[%d:%d], lcd[%d:%d]\n",
			tp_api_t.tp_op->name, tp_api_t.tp_op->tp_width, tp_api_t.tp_op->tp_height,
			tp_api_t.lcdSreen_w, tp_api_t.lcdSreen_h);
			return 0;
		}
	}
	else
	{
		return -1;
	}
}

/*******************************************************************************
* Function Name  : dev_touchpanel_ioctrl
* Description    : dev_touchpanel_ioctrl
* Input          : none
* Output         : none
* Return         : wake up state
*******************************************************************************/
int dev_touchpanel_ioctrl(INT32U op,INT32U para)
{
	if(op == DEV_TOUCHPANEL_READ)
	{
		return (int)touchpanel_GetInfo(para);
	}
	return 0;

}
