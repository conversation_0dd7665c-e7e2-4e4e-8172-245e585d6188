/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT == LCD_RGB_ST7701FW1604

#define CMD(x)  LCD_CMD_RGB_DAT(x)
#define DAT(x)  LCD_CMD_RGB_DAT((x)|(1<<8))
#define DLY(m)  LCD_CMD_DELAY_MS(m)


//--------------------------- XJ040WV1604 ---------------------------------
LCD_INIT_TAB_BEGIN()
    CMD(0x11),
//------------------------------------------Bank0 Setting----------------------------------------------------//
//------------------------------------Display Control setting----------------------------------------------//
    DLY(100),

    CMD(0xff),
    DAT(0x77),
    DAT(0x01),
    DAT(0x00),
    DAT(0x00),
    DAT(0x10),
    //Set lines = 800
    CMD(0xC0),
    DAT(0x63),
    DAT(0x00),
    //Set VBP = 0x0A,VFP = 0x14
    CMD(0xC1),
    DAT(0x0A),
    DAT(0x14),
    //Set inversion
    CMD(0xC2),
    DAT(0x31),
    DAT(0x08), //set min pclks each line = 512 + 8 * 16 = 640(plck)
    //Set HS/VS/DE/PCLK
    CMD(0xC3),
    DAT(0x00),//enable DE mode,VS low active,HS low active,PCLK sample raise edge,DE high active
    DAT(0x10),//VBP for HV mode
    DAT(0x08),//HBP for HV mode
//-------------------------------------Gamma Cluster Setting-------------------------------------------//
    CMD(0xB0),
    DAT(0x00),
    DAT(0x11),
    DAT(0x19),
    DAT(0x0C),
    DAT(0x10),
    DAT(0x06),
    DAT(0x07),
    DAT(0x0A),
    DAT(0x09),
    DAT(0x22),
    DAT(0x04),
    DAT(0x10),
    DAT(0x0E),
    DAT(0x28),
    DAT(0x30),
    DAT(0x1C),

    CMD(0xB1),
    DAT(0x00),
    DAT(0x12),
    DAT(0x19),
    DAT(0x0D),
    DAT(0x10),
    DAT(0x04),
    DAT(0x06),
    DAT(0x07),
    DAT(0x08),
    DAT(0x23),
    DAT(0x04),
    DAT(0x12),
    DAT(0x11),
    DAT(0x28),
    DAT(0x30),
    DAT(0x1C),
//---------------------------------------End Gamma Setting----------------------------------------------//
//------------------------------------End Display Control setting----------------------------------------//
//-----------------------------------------Bank0 Setting End---------------------------------------------//
//-------------------------------------------Bank1 Setting---------------------------------------------------//
//-------------------------------- Power Control Registers Initial --------------------------------------//
    CMD(0xff),
    DAT(0x77),
    DAT(0x01),
    DAT(0x00),
    DAT(0x00),
    DAT(0x11),

    CMD(0xb0),
    DAT(0x7d),
//-------------------------------------------Vcom Setting---------------------------------------------------//
    CMD(0xb1),
    DAT(0x3E),
//-----------------------------------------End Vcom Setting-----------------------------------------------//
    CMD(0xb2),
    DAT(0x07),

    CMD(0xb3),
    DAT(0x80),

    CMD(0xb5),
    DAT(0x47),

    CMD(0xb7),
    DAT(0x8A),

    CMD(0xb8),
    DAT(0x21),

    CMD(0xC1),
    DAT(0x78),

    CMD(0xC2),
    DAT(0x78),

    CMD(0xD0),
    DAT(0x88),
//---------------------------------End Power Control Registers Initial -------------------------------//
    DLY(100),
//---------------------------------------------GIP Setting----------------------------------------------------//
    CMD(0xe0),
    DAT(0x00),
    DAT(0x00),
    DAT(0x02),

    CMD(0xe1),
    DAT(0x04),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x05),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x20),
    DAT(0x20),

    CMD(0xe2),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),

    CMD(0xe3),
    DAT(0x00),
    DAT(0x00),
    DAT(0x33),
    DAT(0x00),

    CMD(0xe4),
    DAT(0x22),
    DAT(0x00),

    CMD(0xe5),
    DAT(0x04),
    DAT(0x34),
    DAT(0xAA),
    DAT(0xAA),
    DAT(0x06),
    DAT(0x34),
    DAT(0xAA),
    DAT(0xAA),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),

    CMD(0xe6),
    DAT(0x00),
    DAT(0x00),
    DAT(0x33),
    DAT(0x00),

    CMD(0xe7),
    DAT(0x22),
    DAT(0x00),

    CMD(0xe8),
    DAT(0x05),
    DAT(0x34),
    DAT(0xAA),
    DAT(0xAA),
    DAT(0x07),
    DAT(0x34),
    DAT(0xAA),
    DAT(0xAA),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),

    CMD(0xeb),
    DAT(0x02),
    DAT(0x00),
    DAT(0x40),
    DAT(0x40),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),

    CMD(0xed),
    DAT(0xfA),
    DAT(0x45),
    DAT(0x0B),
    DAT(0xff),
    DAT(0xff),
    DAT(0xff),
    DAT(0xff),
    DAT(0xff),
    DAT(0xff),
    DAT(0xff),
    DAT(0xff),
    DAT(0xff),
    DAT(0xff),
    DAT(0xB0),
    DAT(0x54),
    DAT(0xAf),
//---------------------------------------------End GIP Setting-----------------------------------------------//
//------------------------------ Power Control Registers Initial End-----------------------------------//
//------------------------------------------Bank1 Setting----------------------------------------------------//
    CMD(0xFF),
    DAT(0x77),
    DAT(0x01),
    DAT(0x00),
    DAT(0x00),
    DAT(0x00),
    //Set RGB order = RGB
    CMD(0x36),
    DAT(0x00),
    //Set pixel bits = 24bit/pixel
    CMD(0x3A),
    DAT(0x70),

    DLY(100),

    CMD(0x29),
LCD_INIT_TAB_END()

LCD_UNINIT_TAB_BEGIN()  
    CMD(0x28),
    DLY(10),
LCD_UNINIT_TA_ENDB()

LCD_DESC_BEGIN()

    .name 			= "RGB_ST7701_XJ040FW1604",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_24,

    .pclk_div 		= LCD_PCLK_DIV(********),
    .clk_per_pixel 	= 1,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .pclk_edge      = LCD_PCLK_EDGE_FALLING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw 			= 1,
    .vbp 			= 10,
    .vfp 			= 20,

    .hlw 			= 1,
    .hbp 			= 3,
    .hfp 			= 140,

    LCD_SPI_DEFAULT(9),

    .data_mode 	= LCD_DATA_MODE0_24BIT_RGB888,

    .screen_w 		= 480,
    .screen_h 		= 800,

    .video_w  		= 800,
    .video_h  		= 480,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast   	= LCD_SATURATION_115,

    .brightness 	= 4,

    .saturation 	= LCD_SATURATION_145,

    .contra_index 	= 6,

    .gamma_index 	= {4, 4, 5},

    .asawtooth_index = {1, 1},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
    UNINIT_TAB_INIT
LCD_DESC_END()
#endif