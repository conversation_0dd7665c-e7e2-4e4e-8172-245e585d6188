/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hal.h"




/*******************************************************************************
* Function Name  : hal_timerStart
* Description    : timer start
* Input          : u8 timer : timer id.TIMER0/TIMER1/TIMER2/TIMER3
                      u32 frq  : frq .>20hz
                      void (*isr)(void) : callback function
* Output         : None
* Return         : bool : true success
						  false fail 
*******************************************************************************/
/*bool hal_timerStart(u8 htimer,u32 frq,void (*isr)(void))
{
	return hx330x_timerStart(htimer,frq,isr);
}*/
/*******************************************************************************
* Function Name  : hal_timerStop
* Description    : timer stop
* Input          : u8 htimer : timer id.TIMER0/TIMER1/TIMER2/TIMER3
* Output         : None
* Return         : bool : true success
						  false fail 
*******************************************************************************/
/*bool hal_timerStop(u8 htimer)
{
	return hx330x_timerStop(htimer);
}*/
/*******************************************************************************
* Function Name  : hal_timerEnable
* Description    : timer enable /disable
* Input          : u8 htimer : timer id.TIMER0/TIMER1/TIMER2/TIMER3
				   u8 en      : 0:disable,1 :enable
* Output         : None
* Return         : bool : true success
						  false fail 
*******************************************************************************/
bool hal_timerEnable(u8 htimer,u8 en)
{
	if(en)
		return hx330x_timerEnable(htimer);
	else
		return hx330x_timerDisable(htimer);
}
/*******************************************************************************
* Function Name  : hal_timerTickEnable
* Description    : timer tick enable /disable
* Input          : u8 en      : 0:disable,1 :enable
* Output         : None
* Return         : None
*******************************************************************************/
void hal_timerTickEnable(u8 en)
{
	if(en)
		hx330x_timerTickStart();
	else
		hx330x_timerTickStop();
}
/*******************************************************************************
* Function Name  : hal_timerTickGet
* Description    : timer tick count
* Input          : 
* Output         : None
* Return         : u32 : tick count
*******************************************************************************/
/*u32 hal_timerTickGet(void)
{
	return hx330x_timerTickCount();
}*/

/*******************************************************************************
* Function Name  : hal_timerPWMStart
* Description    : timer PWM start
* Input          : timer: timer select: TIMER0 / TIMER1 / TIMER2 / TIMER3
* 				   pwm_map: PWM output group select, check timer0_group_e ~ timer3_group_e
* 				   frq:     PWM frequency configure
* 				   percent: PWM duty rate(<100)
* Output         : None
* Return         : true:sucess, false: fail
*******************************************************************************/
bool hal_timerPWMStart(u8 timer,u8 pwm_map,u32 frq, u8 percent)
{
	deg_Printf("PWM START:[%d, %d]\n",frq, percent);
	return hx330x_timerPWMStart(timer, pwm_map, frq, percent);
}
/*******************************************************************************
* Function Name  : hal_timerPWMStop
* Description    : timer PWM stop
* Input          : timer: timer select
* Output         : None
* Return         : NONE
*******************************************************************************/
SDRAM_TEXT_SECTION
void  hal_timerPWMStop(u8 timer)
{
	return hx330x_timerPWMStop(timer);
}
















