/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_DRAW_H
#define UI_WIN_DRAW_H

typedef u32  					resID;
typedef u8 						charFont;
typedef u8 						uiColor;


typedef struct uiRect_S
{
	s16 x0;
	s16 x1;
	s16 y0;
	s16 y1;
	uiColor rimColor; //INVALID_COLOR: not rim
	u8  round_type;  
	s16 radius; //0: normal rect
	struct uiRect_S* next;
}uiRect;

typedef struct uiCycle_S
{
	uiRect rect;
	s16 x_center;
	s16 y_center;
	s16 radius;
}uiCycle;
typedef struct CHAR_DRAW_S
{	
	uiRect  charRect;
	u16     charW;
	u16     charH;
	uiColor fontColor;
	uiColor bgColor;
	uiColor rimColor;
}CHAR_DRAW_T;

typedef struct STRING_DRAW_S
{	
	resID	 id;
	u8       style;
	u8 		 strAlign;
	charFont font;
	uiColor  fontColor;
	uiColor  bgColor;
	uiColor  rimColor;
	u16      visible;
}STRING_DRAW_T;

typedef struct ICON_DRAW_S
{
	resID 	id;
	u8 		iconAlign;
	uiColor bgColor;
	uiColor iconColor;
	u8      visible;
}ICON_DRAW_T;

typedef struct LINE_DRAW_S
{	
	s16      sx;
	s16      sy;
	s16      ex;
	s16      ey;
	u16      width;
	uiColor  fill_color;
	u8       style;
}LINE_DRAW_T;

typedef struct UIDRAW_CTRL_S
{
	lcdshow_frame_t *drawframe;
	u16 width;
	u16 height;
	uiColor* bufStart;
	uiColor* bufEnd;
	//u32 bufsize;
}UIDRAW_CTRL_T;

extern UIDRAW_CTRL_T ui_draw_ctrl;


#define USER_UI_WIDTH			ui_draw_ctrl.width
#define USER_UI_HEIGHT			ui_draw_ctrl.height

#define DEFAULT_UI_WIDTH       	320		
#define DEFAULT_UI_HEIGHT      	240

#define Rx(n)       			(n)  //  x position rate
#define Ry(n)       			(n)
#define Rw(n)       			(n)
#define Rh(n)       			(n)

#define USER_Rx(n)				((n)*USER_UI_WIDTH/DEFAULT_UI_WIDTH)
#define USER_Ry(n)				((n)*USER_UI_HEIGHT/DEFAULT_UI_HEIGHT)
#define USER_Rw(n)				((n)*USER_UI_WIDTH/DEFAULT_UI_WIDTH)
#define USER_Rh(n)				((n)*USER_UI_HEIGHT/DEFAULT_UI_HEIGHT)


//--------------------define WIN style-------------------------------------------------
#define WIN_INVALID        		(1<<0)	//win need to redraw
#define WIN_WIDGET         		(1<<1)	//win is widget(frame->widget)
#define WIN_VISIBLE        		(1<<2)	//win is visible
#define WIN_ABS_POS        		(1<<3)  // create window by absolute position
#define WIN_FRAME          		(1<<4)	//win is frame
#define WIN_NOT_ZOOM       		(1<<5)	//win size will not zoom to user size
#define WIN_TOUCH_SUPPORT  		(1<<6)  //win support touch func
#define WIN_ROUND_RECT  		(1<<7)  //win round rect with rim
//-------------- define win alignment--------------------------------------------------
#define ALIGNMENT_LEFT  		(1<<0)
#define ALIGNMENT_CENTER  		(1<<1)
#define ALIGNMENT_RIGHT  		(1<<2)
//-------------- define round type--------------------------------------------------
#define ROUND_NONE				0
#define ROUND_LEFTTOP  			(1<<0)
#define ROUND_LEFTBOT  			(1<<1)
#define ROUND_RIGHTTOP  		(1<<2)
#define ROUND_RIGHTBOT  		(1<<3)
#define ROUND_ALL				(0x0f<<0)
//--------------define UI COLOR, if user change palette, should change this------------
#define DEFAULT_COLOR  			R_ID_PALETTE_White
#define INVALID_COLOR  			R_ID_PALETTE_Error
#define TRANSFER_COLOR			R_ID_PALETTE_Transparent

#define RECT_RIM_WITH			1


/*******************************************************************************
* Function Name  : uiWinDrawInit
* Description    : uiWinDrawInit
* Input          : lcdshow_frame_t * drawframe
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawInit(lcdshow_frame_t * drawframe);
/*******************************************************************************
* Function Name  : app_taskRegister
* Description    : app_taskRegister
* Input          : taskID id,sysTask* task
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawUpdate(void);
/*******************************************************************************
* Function Name  : uiWinDrawLine
* Description    : uiWinDrawLine
* Input          : LINE_DRAW_T *line
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawLine(LINE_DRAW_T *line);
/*******************************************************************************
* Function Name  : res_draw_Rect
* Description    : res_draw_Rect
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawRect(uiRect* rect,uiColor color);
/*******************************************************************************
* Function Name  : res_draw_Rect
* Description    : res_draw_Rect
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawRoundRectWithRim(uiRect* rect,uiRect* round_rect,uiColor font_color);
/*******************************************************************************
* Function Name  : uiWinDrawCircle
* Description    : uiWinDrawCircle
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawCircle(uiCycle *cycle,uiColor bgcolor, uiColor fontcolor);
/*******************************************************************************
* Function Name  : uiWinDrawIcon
* Description    : res_draw_Icon
* Input          : uiRect* winRect,uiRect* drawRect,resID id,u8 alignment,uiColor bgColor
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawIcon(uiRect* winRect,uiRect* drawRect,ICON_DRAW_T *icon);

/*******************************************************************************
* Function Name  : uiWinDrawString
* Description    : res_draw_Char
* Input          : uiRect* winRect,uiRect* invalidRect,STRING_DRAW_T *string_draw
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawString(uiRect* winRect,uiRect* invalidRect,STRING_DRAW_T *string_draw);


#endif
