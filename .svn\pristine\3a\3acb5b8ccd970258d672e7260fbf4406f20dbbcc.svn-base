/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT == LCD_RGB_ST7282


LCD_DESC_BEGIN()
    .name 			= "RGB_ST7282",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_0,

#if 1
    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,
#else
    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8<<8,
#endif

    .pclk_div 		= LCD_PCLK_DIV(16000000),
    .clk_per_pixel 	= 3,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .pclk_edge      = LCD_PCLK_EDGE_RISING,
    .de_level 		= LCD_SIG_DISABLE,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw 			= 1,
    .vbp 			= 11,
    .vfp 			= 8,

    .hlw 			= 1,
    .hbp 			= 14,
    .hfp 			= 12,

#if 1
    .data_mode  = LCD_DATA_MODE0_8BIT_RGB888,
#else
    .data_mode  = 0x00628a28,//LCD_DATA_MODE0_8BIT_RGB888,
#endif
    .screen_w 		= 480,
    .screen_h 		= 272,

    .video_w  		= 480,
    .video_h  		= 272,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm 		= {0x110,-0x008,-0x008,
                      -0x008, 0x110,-0x008,
                       0x020,-0x010, 0x0f0,
                       0x000, 0x000, 0x000},

    .lcd_saj         = LCD_SAJ_DEFAULT,
LCD_DESC_END()

#endif








