/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiProgressBarProc
* Description    : uiProgressBarProc
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiProgressBarVerProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiProgressBarVerObj* pProgressBar;
	uiWinObj* pWin;
	u32 length;
	uiRect rect, rect1;
	if(uiWidgetProc(msg))
		return;
	hWin 			= msg->curWin;
	pProgressBar 	= (uiProgressBarVerObj*)uiHandleToPtr(hWin);
	pWin			= &(pProgressBar->widget.win);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			return;
		case MSG_WIN_PAINT:
			uiWinDrawRect(&(pWin->rect),pProgressBar->rimcolor);
			rect.x0 = pWin->rect.x0 + 1;
			rect.x1 = pWin->rect.x1 - 1;
			rect.y0 = pWin->rect.y0 + 1;
			rect.y1 = pWin->rect.y1 - 1;
			
			length = rect.y1 - rect.y0 + 1;
			length = pProgressBar->rate*length/100;
			if(length == 0)
				uiWinDrawRect(&rect,pWin->bgColor);
			else if(pProgressBar->rate == 100)
				uiWinDrawRect(&rect,pProgressBar->color);
			else
			{
				rect1.x0 = rect.x0;
				rect1.x1 = rect.x1;
				if(pProgressBar->align & ALIGNMENT_RIGHT)
				{
					rect1.y0 = rect.y0;
					rect1.y1 = rect.y1 - length - 1;	
					uiWinDrawRect(&rect1,pWin->bgColor);	
					rect1.y0 = rect.y1 - length - 1;
					rect1.y1 = rect.y1;
					uiWinDrawRect(&rect1, pProgressBar->color);
				}else
				{
					rect1.y0 = rect.y0;
					rect1.y1 = rect.y0 + length - 1;
					uiWinDrawRect(&rect1, pProgressBar->color);
					rect1.y0 = rect.y0 + length - 1;
					rect1.y1 = rect.y1;
					uiWinDrawRect(&rect1,pWin->bgColor);
				}

			}
			return;
		case MSG_WIN_PROGRESS_RATE:
			length = msg->para.v;
			if(length > 100)
				length = 100;
			if(pProgressBar->rate == length)
				return;
			pProgressBar->rate = length;
			uiWinUpdateInvalid(hWin);
			return;
		case MSG_WIN_CHANGE_FG_COLOR:
			pProgressBar->color = msg->para.v;
			return;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			((touchInfor *)(msg->para.p))->touchWin		= pWin->parent;
			((touchInfor *)(msg->para.p))->touchHandle	= hWin;
			((touchInfor *)(msg->para.p))->touchID		= pProgressBar->widget.id;
			((touchInfor *)(msg->para.p))->touchItem	= 0;
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiProgressBarVerCreateDirect
* Description    : uiProgressBarVerCreateDirect
* Input          : s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id, u8 align
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiProgressBarVerCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id, u8 align)
{
	winHandle 		  hprogressBar;
	uiProgressBarVerObj *pprogressBar;
	hprogressBar = uiWinCreate(x0,y0,width,height,parent,uiProgressBarVerProc,sizeof(uiProgressBarVerObj),WIN_WIDGET|style);
	uiWidgetSetId(hprogressBar,id);
	if(hprogressBar!=INVALID_HANDLE)
	{
		pprogressBar	    = (uiProgressBarObj*)uiHandleToPtr(hprogressBar);
		pprogressBar->rate  = 0;
		pprogressBar->align = align;
	}
	return hprogressBar;
}
/*******************************************************************************
* Function Name  : uiProgressBarVerCreate
* Description    : uiProgressBarVerCreate
* Input          : widgetCreateInfor* infor,winHandle parent,uiWinCB cb
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiProgressBarVerCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle hprogressBar;
	uiProgressBarVerObj* pProgressBar;
	
	hprogressBar = uiWinCreate(infor->x0,infor->y0,infor->width,infor->height,parent,uiProgressBarVerProc,sizeof(uiProgressBarVerObj),WIN_WIDGET|infor->style);
	
	if(hprogressBar!=INVALID_HANDLE)
	{
		pProgressBar 		= (uiProgressBarVerObj*)uiHandleToPtr(hprogressBar);
		pProgressBar->rate 	= 0;
		pProgressBar->rimcolor = infor->rimColor;
		pProgressBar->align = infor->imageAlign;

		uiWidgetSetId(hprogressBar,infor->id);
		uiWinSetfgColor(hprogressBar, infor->fontColor);
		uiWinSetbgColor(hprogressBar, infor->bgColor);
		
	}
	return hprogressBar;
}
