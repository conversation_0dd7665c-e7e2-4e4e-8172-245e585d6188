/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hal.h"

//=====sensor 480P  check all frame=======
/*
#define  HAL_CFG_MD_SX        0
#define  HAL_CFG_MD_EX        640
#define  HAL_CFG_MD_SY        0
#define  HAL_CFG_MD_EY        480
*/
//=====sensor 720P  check frame center =======
#define  HAL_CFG_MD_SX        320
#define  HAL_CFG_MD_EX        (320+640)
#define  HAL_CFG_MD_SY        180
#define  HAL_CFG_MD_EY        (180+360)

#define  HAL_CFG_MD_THRESHOLD    1  // THRESHOLD LEVEL   0: easy happen, 1: normal  happen, 2 :not easy happen

#if(0 == HAL_CFG_MD_THRESHOLD)
#define  HAL_CFG_MD_PIXELNUM     5	// value more min ,more easy to happen
#define  HAL_CFG_MD_BLOCKNUM     5	// value more min ,more easy to happen
#define  HAL_CFG_MD_FRAMENUM     1	// value more min ,more easy to happen
#elif(1 == HAL_CFG_MD_THRESHOLD)
#define  HAL_CFG_MD_PIXELNUM     20	// value more min ,more easy to happen
#define  HAL_CFG_MD_BLOCKNUM     20	// value more min ,more easy to happen
#define  HAL_CFG_MD_FRAMENUM     1	// value more min ,more easy to happen
#else
#define  HAL_CFG_MD_PIXELNUM     40	// value more min ,more easy to happen
#define  HAL_CFG_MD_BLOCKNUM     40	// value more min ,more easy to happen
#define  HAL_CFG_MD_FRAMENUM     1	// value more min ,more easy to happen
#endif


ALIGNED(4) static u32 halMDFlag=0;
/*******************************************************************************
* Function Name  : hal_mdISR
* Description    : hal layer .motion dectetion isr
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
static void hal_mdCallback(void)
{
	halMDFlag |= 0x10;

}
/*******************************************************************************
* Function Name  : hal_mdInit
* Description    : hal layer .motion dectetion initial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_mdInit(MD_Adapt *pMD)
{
	if((pMD == NULL) ||(pMD->num_th == 0))
	{
		hx330x_mdXPos(HAL_CFG_MD_SX,HAL_CFG_MD_EX);
		
		hx330x_mdYPos(HAL_CFG_MD_SY,HAL_CFG_MD_EY);

		hx330x_mdInit(HAL_CFG_MD_PIXELNUM,HAL_CFG_MD_BLOCKNUM,HAL_CFG_MD_FRAMENUM);
	
	}else
	{
		hx330x_mdXPos(pMD->win_h_start,pMD->win_h_end);
		
		hx330x_mdYPos(pMD->win_v_start,pMD->win_v_end);

		hx330x_mdInit(pMD->pixel_th,pMD->num_th,pMD->update_cnt);		
	}
	
	hx330x_csiISRRegiser(CSI_IRQ_MDT_MOTION_DET,hal_mdCallback);

    halMDFlag = 0;
}
/*******************************************************************************
* Function Name  : hal_dacCallback
* Description    : hal layer .motion dectetion enable  set
* Input          : u8 en : 1-enable,0-disable
* Output         : None
* Return         : none
*******************************************************************************/
void hal_mdEnable(u8 en)
{
//	hal_mdInit();
	hx330x_mdEnable(en);
    if(en)
    {
       halMDFlag = 0x01;
	  // hx330x_csiISRRegiser(CSI_IRQ_MDT_MOTION_DET,hal_mdCallback);
    }
	else
	{
	//	hx330x_csiISRRegiser(CSI_IRQ_MDT_MOTION_DET,NULL);
	}
}


/*******************************************************************************
* Function Name  : hal_mdCheck
* Description    : hal layer .motion dectetion check md event
* Input          : 
* Output         : None
* Return         : u32 : 0 :no md event happend
                             >0: md event happend
*******************************************************************************/
u32 hal_mdCheck(void)
{
	u8 temp;

	temp = halMDFlag;
	halMDFlag &= 0x01;
	
    if(temp&0x01)
	    return (temp&0xf0);
	else
		return 0;
}















