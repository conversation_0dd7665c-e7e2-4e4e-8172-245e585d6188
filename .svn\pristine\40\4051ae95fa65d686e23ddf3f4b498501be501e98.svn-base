/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HX330X_CFG_H
   #define  HX330X_CFG_H

#include <stdio.h>  // c-std lib
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>

#include "typedef.h"
#include "hx330x_spr_defs.h"


#include "../boot/spi_boot_cfg.h"


#include "hx330x_misc.h"
#include "hx330x_sys.h"
#include "hx330x_int.h"
#include "hx330x_dma.h"
#include "hx330x_wdt.h"
#include "hx330x_gpio.h"
#include "hx330x_uart.h"
#include "hx330x_dmauart.h"
#include "hx330x_timer.h"
#include "hx330x_tminf.h"
#include "hx330x_spi0.h"
#include "hx330x_spi1.h"
#include "hx330x_rtc.h"
#include "hx330x_md.h"
#include "hx330x_iic.h"
#include "hx330x_dac.h"
#include "hx330x_adc.h"
#include "hx330x_sd.h"
#include "hx330x_auadc.h"

#include "hx330x_lcd.h"
#include "hx330x_lcdWin.h"
#include "hx330x_lcdrotate.h"
#include "hx330x_lcdUi.h"
#include "hx330x_lcdUiLzo.h"
#include "hx330x_csi.h"
#include "hx330x_mipi.h"
#include "hx330x_jpg.h"
#include "hx330x_isp.h"
#include "hx330x_usb.h"
#include "hx330x_emi.h"


#include "../xos/xos.h"




/*******************************************************************
**Define  MCU LAYER BOARD CFG TAB
********************************************************************/
/*******************************************************************
**Define  MCU LAYER CFG 
********************************************************************/
#define  CFG_DCACHE_EN       1
#define  CFG_ICACHE_EN       1



#define  CFG_WDT_EN          1   // watch dog

#define  CFG_MCU_DBG_EN      0


	

//--------------------------------------basic function define--------------------------------
#define __FP__(f)               if(f){(*f)();}


extern void uart_Printf(const char *pszStr, ...);
#if CFG_MCU_DBG_EN
#define debg(...)   uart_Printf(__VA_ARGS__)
#else
#define debg(...) 
#endif

#define t_mem_set(adr,val)     (uint32_t)(adr), val
#define t_bic_orr(adr,bic,orr) ((uint32_t)(adr))+1, bic, orr
#define t_end				   (uint32_t)0

#define BOOT_TEXT_KEPT_SECTION	SECTION(".vector.kepttext")
#define BOOT_DATA_KEPT_SECTION	ALIGNED(4) SECTION(".vector.keptdata")
#define BOOT_TEXT_SECTION		SECTION(".vector.text")
#define BOOT_DATA_SECTION		ALIGNED(4) SECTION(".vector.data")



#define ISP_SECTION_TABLE      	SECTION(".sensor_res.isp_tab") 

#define USB11FIFO_SECTION		ALIGNED(4) SECTION(".sram_usb11fifo") 
#define USB20FIFO_SECTION		ALIGNED(4) SECTION(".uram_usb20fifo") 
#define GRAM_SECTION			ALIGNED(4) SECTION(".sram_comm") 
#define URAM_SECTION			ALIGNED(4) SECTION(".uram_comm")  

#define SDRAM_TEXT_SECTION		SECTION(".sdram_text")
#define SDRAM_CODE_SECTION		ALIGNED(4) SECTION(".sdram_code")
#define SDRAM_DATA_SECTION		ALIGNED(4) SECTION(".sdram_data")
#define SDRAM_SECTION			ALIGNED(4) SECTION("._sdram_buf_")
/*line_ram*/
#define LCODE_SECTION			ALIGNED(4) SECTION(".line_code")
#define LDATA_SECTION			ALIGNED(4) SECTION(".line_data")
/*mp3_ram*/
#define MP3TEXT_SECTION			SECTION(".mp3_text")
#define MP3CODE_SECTION			ALIGNED(4) SECTION(".mp3_code")
#define MP3DATA_SECTION			ALIGNED(4) SECTION(".mp3_data")
/*mp3_ram*/
#define NES_COM_TEXT_SECTION	SECTION(".nes_com_text")
#define NESCODE_SECTION			ALIGNED(4) SECTION(".nes_code")
#define NESDATA_SECTION			ALIGNED(4) SECTION(".nes_data")
#define NESGAMETAB_SECTION		SECTION(".nes_games_tab")


#ifndef __ASSEMBLER__
extern u32 _boot_vma;
extern u32 _boot_kept_vma;
extern u32 __sram_start, __sram_end;
extern u32 __ufifo_start, __ufifo_end;
extern u32 __line_start;
extern u32 __mp3_text_start, __mp3_text_len, __mp3_text_addr;
extern u32 __mp3_code_start, __mp3_code_len, __mp3_code_addr;
extern u32 __nes_text_start, __nes_text_len, __nes_text_addr;
extern u32 __nes_code_start, __nes_code_len, __nes_code_addr;
extern u32 __nes_data_start, __nes_data_end;
extern u32 __bss_start, __bss_end;
extern u32 _sdram_remian_addr, __sdram_remain_size;
extern u32 _nes_res_lma;

#endif





#endif

