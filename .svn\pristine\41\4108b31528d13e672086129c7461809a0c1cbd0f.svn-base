/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/hal.h"
/*******************************************************************************
* Function Name  : hal_csi_test_hvb_cfg
* Description    : hal_csi_test_hvb_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_csi_test_hvb_cfg(u16 width, u16 height, u8 dvp_clk_div,u8 fps, u8 raw)
{
	u32 dvp_vsync, dvp_hsync, dvp_clk;
	dvp_clk = APB_CLK/2;
	if(raw) //MJP
	{
		dvp_hsync = 30 * (dvp_clk/1000000); //预留hsync 30us

		dvp_vsync = dvp_clk/fps/(width + dvp_hsync) - height;

	}else	//YUV
	{
		dvp_vsync = dvp_clk/1000/(width*2 + width/2);
		dvp_hsync = (dvp_clk/fps/(dvp_vsync+height));
		if(dvp_hsync <= width*2)
		{
			fps = dvp_clk/(height + dvp_vsync)/(width*2 + width/2);
			dvp_hsync = (dvp_clk/fps/(dvp_vsync+height));
		}
		dvp_hsync -= width*2;
	}
	hx330x_csiDvpClkDivSet(dvp_clk_div);
	hx330x_csiTestModeSet(width,height,dvp_vsync, dvp_hsync,hardware_setup.usb_host_none_test_pattern);

	deg_Printf("[CSI TEST :%d]: width:%d,height:%d, fps:%d, hsync:%d, vsync:%d\n", raw, width, height, fps,dvp_hsync, dvp_vsync);
}

/*******************************************************************************
* Function Name  : hal_CSI_Sensor_Input
* Description    : hal layer .CSI get data from DVP
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_CSI_Sensor_Input(void)
{
	u8 type;
	SENSOR_API_T 	*senapi 	= hal_SensorApiGet();
	Sensor_Adpt_T 	*senadpt 	= &senapi->sensorAdpt;
	if(senadpt == NULL)
		return;
	u16 lcd_w , lcd_h;
	u16 usensor_w, usensor_h;
	if(hal_lcdGetVideoResolution(&lcd_w,&lcd_h) >= 0)
	{
		if(husb_api_usensor_tran_sta() && husb_api_usensor_res_type_is_mjp())
		{
			if(husb_api_usensor_res_get(&usensor_w,&usensor_h) >= 0)
			{
				lcd_w = hx330x_min(lcd_w,usensor_w);
				lcd_h = hx330x_min(lcd_h,usensor_h);
			}
		}
		senadpt->senPixelw = senadpt->senCropW_Ed = senadpt->pixelw = lcd_w;
		senadpt->senPixelh = senadpt->senCropH_Ed = senadpt->pixelh = lcd_h;
	}


	type = senadpt->typ & 0xff;
    //mjpAEncCtrl.csi_width = senradpt->pixelw;
	//mjpAEncCtrl.csi_height= senradpt->pixelh;
	hx330x_csiModeSet(0xffffffff,0);  // clear
	hx330x_csiSyncSet(senadpt->hsyn,senadpt->vsyn);
	hx330x_sen_Image_Size_Set(senadpt->senPixelw,senadpt->senPixelh);
	hx330x_csi_in_CropSet(senadpt->senCropW_St,senadpt->senCropW_Ed,senadpt->senCropH_St,senadpt->senCropH_Ed,senadpt->senCropMode);
	hx330x_csiSizeSet(senadpt->pixelw,senadpt->pixelh);
	hx330x_csiTypeSet(senadpt->typ);
	hx330x_csiPrioritySet(senadpt->colrarray);


    if(type == CSI_TYPE_YUV422)
	{
		deg_Printf("--CSI :YUV422\n");	
		if(senadpt->hvb_adapt.pclk > (hardware_setup.sys_clk/3)) //pclk比较高时，需要采用采样边沿模式，同时可能需要开滤波和CSI_TUN
		{
			hx330x_csiModeSet(CSI_MODE_SAMPLE,1);
			hx330x_pclk_digital_fir_Set(senadpt->pclk_dig_fir_step);
			hx330x_pclk_analog_Set(senadpt->pclk_ana_fir_step);
			hx330x_pclk_inv_Set(senadpt->pclk_inv_en);
       	 	hx330x_csi_clk_tun_Set(senadpt->csi_tun);
		}	
	}
	else if((type == CSI_TYPE_RAW8) || (type == CSI_TYPE_RAW10) || (type == CSI_TYPE_RAW12))
	{
		deg_Printf("--CSI : RAW\n");

		hx330x_ispModeSet(ISP_MODE_CFAEN,1);
		hx330x_csiModeSet(CSI_MODE_SAMPLE,1);
		hx330x_pclk_digital_fir_Set(senadpt->pclk_dig_fir_step);
		hx330x_pclk_analog_Set(senadpt->pclk_ana_fir_step);
		hx330x_pclk_inv_Set(senadpt->pclk_inv_en);
        hx330x_csi_clk_tun_Set(senadpt->csi_tun);
	}
	else if (type == CSI_TYPE_COLOR_BAR)
	{
		deg_Printf("--CSI : TEST IMG\n");
		hal_csi_test_hvb_cfg(senadpt->pixelw,senadpt->pixelh, 1,senadpt->hvb_adapt.fps, 1);
		hx330x_ispModeSet(ISP_MODE_CFAEN,1);
		hx330x_csiModeSet(CSI_MODE_SAMPLE|CSI_MODE_TEST_EN,1);

	}
}
/*******************************************************************************
* Function Name  : hal_CSI_RAM_Input
* Description    : hal layer .CSI get data from sdram
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_CSI_Usensor_Ram_Input(u32 addr)
{
	u16 width;
	u16 height;
	u8 dvp_fps;
	//if(addr < 0x02000000)
	//	return;
	if(husb_api_usensor_res_get(&width, &height) < 0)
		return;
	hx330x_csiModeSet(0xffffffff,0);  // clear

	hx330x_csiSyncSet(1,1);

	hx330x_sen_Image_Size_Set(width,height);
	hx330x_csi_in_CropSet(0,width,0,height,CSI_PASS_MODE);
	hx330x_csiSizeSet(width,height);

	hx330x_csiTypeSet(CSI_TYPE_YUV422);
	hx330x_csiPrioritySet(CSI_PRIORITY_Y0CBY1CR);
	hx330x_ispModeSet(ISP_MODE_CFAEN,1);

#if (CURRENT_CHIP == FPGA)

	if(width >= 1280)
		dvp_fps = 15;
	else
		dvp_fps = 30;
#else
	if(width >= 1280)
		dvp_fps = 15;
	else
		dvp_fps = 30;
#endif
	hal_csi_test_hvb_cfg(width, height, 1, dvp_fps, 0);

	//hx330x_csiInputAddrSet(addr);
	hx330x_csiModeSet(CSI_MODE_DVP_EN,1); //APB_CLK/2
	//hal_lcdSetCsiCrop(0, 0, width,  height);


}

/*******************************************************************************
* Function Name  : hal_mjpA_SrcRam
* Description    : hal layer .mjpeg get data from sdram
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_csi_init(void)
{
	hal_CSI_Sensor_Input();
	hal_isp_init();
}
static s32 src_save = 0;
/*******************************************************************************
* Function Name  : hal_mjpA_SrcRam
* Description    : hal layer .mjpeg get data from sdram
* Input          : src : 0 color bar, 1: sdram input
* Output         : None
* Return         : None
*******************************************************************************/
void hal_csi_input_switch(u32 src, u32 addr)
{
	//
	u32 csi_en = (u32)((bool)(hx330x_csiModeGet() & CSI_MODE_ENABLE));
	deg_Printf("[csi input]src:%d, csi_en:%x\n", src, csi_en);
	//if(src == src_save)
	//{
	//	deg_Printf("[csi input] no switch\n");
	//	return;
	//}

	hx330x_csiEnable(0);

	if(src  == 0) //color bar
	{
		hal_CSI_Sensor_Input();
		hx330x_csiISRRegiser(CSI_DVP_INPUT_SDRAM_KICK,NULL);
	}else
	{
		hal_CSI_Usensor_Ram_Input(addr);
		hx330x_csiISRRegiser(CSI_DVP_INPUT_SDRAM_KICK,husb_api_usensor_csi_kick);
	}
	src_save = src;

	hx330x_csiEnable(csi_en);
}
/*******************************************************************************
* Function Name  : hal_csi_test_fps_adj
* Description    : 
* Input          : u32 fps: 0xff:use default, 
* Output         : None
* Return         : None
*******************************************************************************/
void hal_csi_test_fps_adj(u32 fps)
{
	SENSOR_API_T 	*senapi 	= hal_SensorApiGet();
	Sensor_Adpt_T 	*senadpt 	= &senapi->sensorAdpt;
	u16 width;
	u16 height;
	u8  raw;
	u8  dvp_fps;
	if(src_save == 0)
	{
		width 	= senadpt->pixelw;
		height 	= senadpt->pixelh;
		raw		= 1;
		dvp_fps = senadpt->hvb_adapt.fps;
	}else
	{	
		u8 dvp_fps;
		if(husb_api_usensor_res_get(&width, &height) < 0)
			return;
		raw      = 0;
		if(width >= 1280)
			dvp_fps = 15;
		else
			dvp_fps = 30;	
	}
	fps = hx330x_min(fps, dvp_fps);
	hal_csi_test_hvb_cfg(width, height, 1, fps, raw);
}
/*******************************************************************************
* Function Name  : hal_mjpA_SrcRam
* Description    : hal layer .mjpeg get data from sdram
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/

#if HAL_CFG_PQTOOL_EN

#ifndef __ISP_MODE
#define __ISP_MODE *(__sfr32_t*)0xB004
#endif

typedef struct SAVE_RAW_CTL_S{
	int fd;
	int state;
	u8* save_buf;
	u32 save_len;
	u32 pre_ispmode;
	u8  uvc_start_flag;
	u8  save_type;  //0: raw, 1: yuv422
	u8  save_byte;
	u8  reserve;
}SAVE_RAW_CTL_T;
ALIGNED(4) static SAVE_RAW_CTL_T save_raw_op;
/*******************************************************************************
* Function Name  : hal_csi_save_raw_isr
* Description    :
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_csi_save_raw_isr(void)
{
	if(save_raw_op.state != SAVE_RAW_START)
		return;
	hx330x_csiEnable(0);
	hx330x_csiOutputSet(CSI_OUTPUT_MJPGEN, 0);
	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	NULL);
	int res = fs_write(save_raw_op.fd,(const void *)save_raw_op.save_buf,(UINT) save_raw_op.save_len);
	save_raw_op.state = SAVE_RAW_SUCESS;
	if(res < 0)
	{
		save_raw_op.state = SAVE_RAW_FAIL;
	}
	res = fs_close(save_raw_op.fd);
	if(res < 0)
	{
		save_raw_op.state = SAVE_RAW_FAIL;
	}
	if(save_raw_op.save_buf)
	{
		hal_sysMemFree((void*)save_raw_op.save_buf);
		save_raw_op.save_buf = NULL;
	}
	__ISP_MODE = save_raw_op.pre_ispmode;
	if(save_raw_op.uvc_start_flag) uvc_start();
	hx330x_csiEnable(1);

}
/*******************************************************************************
* Function Name  : hal_csi_save_raw_start
* Description    :
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int hal_csi_save_raw_start(char* filename)
{
	int res = 0;
	if(save_raw_op.state == SAVE_RAW_START)
	{
		return -1;
	}
	if(filename == NULL)
	{
		return -2;
	}
	save_raw_op.uvc_start_flag =  uvc_is_start();
	uvc_stop();
	hx330x_csiEnable(0);
	hal_mjpA_EncodeUninit();
	SENSOR_API_T 	*senapi 	= hal_SensorApiGet();
	Sensor_Adpt_T 	*senadpt 	= &senapi->sensorAdpt;
	save_raw_op.save_type = 0;
	save_raw_op.save_byte = 1;
	switch((senadpt->typ & 0xff))
	{
		case CSI_TYPE_YUV422: save_raw_op.save_type = 1; break;
		case CSI_TYPE_RAW10:  save_raw_op.save_byte = 2; break;
		case CSI_TYPE_RAW12:  return -3;
	}
	save_raw_op.save_len = senadpt->pixelh*senadpt->pixelw * save_raw_op.save_byte *(save_raw_op.save_type?2:1);
	save_raw_op.save_buf = (u8*)hal_sysMemMalloc(save_raw_op.save_len);
	if(save_raw_op.save_buf == NULL)
	{
		deg_Printf("save raw buf fail\n");
		res = -3;
		goto SAVE_RAW_START_FAIL;
	}

	save_raw_op.fd = fs_open(filename,FA_CREATE_ALWAYS | FA_WRITE | FA_READ);
	if(save_raw_op.fd < 0)
	{
		deg_Printf("[SAVE RAW] file create fail:%s\n",filename);
		res = -4;
		goto SAVE_RAW_START_FAIL;
	}
	save_raw_op.pre_ispmode = __ISP_MODE;
	__ISP_MODE = ISP_MODE_CSI_SAVEEN;
	hx330x_csiISRRegiser(CSI_IRQ_JPG_FRAME_END,	hal_csi_save_raw_isr);
	hx330x_csiMJPEGFrameSet((u32)save_raw_op.save_buf, (u32)save_raw_op.save_buf + senadpt->pixelh*senadpt->pixelw ,
							senadpt->pixelh,senadpt->pixelw*(save_raw_op.save_type?1:2));
	hx330x_csiOutputSet(CSI_OUTPUT_MJPGEN, 1);

	save_raw_op.state = SAVE_RAW_START;
	hx330x_csiEnable(1);

SAVE_RAW_START_FAIL:
	if(res < 0)
	{
		save_raw_op.state = SAVE_RAW_FAIL;
		if(save_raw_op.save_buf)
		{
			hal_sysMemFree((void*)save_raw_op.save_buf);
			save_raw_op.save_buf = NULL;
		}
		if(save_raw_op.uvc_start_flag) uvc_start();
	}

	return res;
}
/*******************************************************************************
* Function Name  : hal_csi_save_raw_start
* Description    :
* Input          : None
* Output         : None
* Return         : SAVE_RAW_FAIL(失败), SAVE_RAW_SUCESS(成功), SAVE_RAW_START(正在进行)
*******************************************************************************/
int hal_csi_save_raw_state(void)
{
	return save_raw_op.state;
}
#endif
