#ifndef  WAV_API_H
       #define  WAV_API_H
#include "wav_typedef.h"

typedef struct WAV_PARA_S
{
	int media_ch;
	int fd;
	
	u16 type;      //api_multimedia type
	u16 sub_type;  //media subtype
	
	
	u32 src_type;  //read file type, decode use
	u8* cachebuf;  //read file buf, decode use
	u32 cachelen;  //read file buf len, decode use
	
	u8	ch_in;
	u8	ch_out;
	u8  ch_exchage;
	u8	resv;
	u32 volume;
	u32 samplerate;
	u32 totaltime;
	u32 dataoffset;
	u32 datasize;

}WAV_PARA_T;

extern int pcm_encode(s16 *input,s16 *output,u32 len,u32 chtype);
extern int pcm_decode(s16 *input,s16 *output,u32 len,u32 chtype);
extern int alaw_encode(s16 *input,u8 *output,u32 len,u32 chtype);
extern int alaw_decode(u8 *input,s16 *output,u32 len,u32 chtype);
extern int ulaw_encode(s16 *input,u8 *output,u32 len,u32 chtype);
extern int ulaw_decode(u8 *input,s16 *output,u32 len,u32 chtype);
extern int imaadpcm_encode(s16 *input,u8 *output,u32 len, u32 chtype);
extern int imaadpcm_decode(u8 *input,s16 *output,u32 len, u32 chtype);

extern const WAV_PCMHEADER_T wav_pcm_head;
extern const WAV_LAWPCMHEADER_T	wav_lawpcm_head;
extern const WAV_IMAADPCMHEADER_T wav_imaadpcm_head;
extern const WAV_MSADPCMHEADER_T wav_msadpcm_head;



#endif
