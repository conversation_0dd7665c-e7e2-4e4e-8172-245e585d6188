/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuDelCurWin.c"
/*******************************************************************************
* Function Name  : getdelCurResInfor
* Description    : getdelCurResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getdelCurResInfor(u32 item,u32* image,u32* str)
{
	if(item == 0)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_OK;
	}
	else if(item == 1)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_CANCEL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delCurKeyMsgOk
* Description    : delCurKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCurKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	char *name;
	int file_type;
	INT32S list;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,DELCUR_SELECT_ID));
		if(item==0)
		{
			if(app_taskCurId()  == TASK_PLAY_VIDEO)
			{
				if(SysCtrl.spi_jpg_list >= 0)
				{
					list = SysCtrl.spi_jpg_list;
				}else{
					list = SysCtrl.avi_list;
				}
			}else if(app_taskCurId()  == TASK_PLAY_AUDIO)
			{
				list = SysCtrl.wav_list;
			}else
			{
				return 0;
			}
			if(filelist_api_CountGet(list) <= 0)
			{
				task_com_tips_show(TIPS_NO_FILE);
				return -1;
			}
			
			if(filelist_fnameChecklockByIndex(list,SysCtrl.file_index) <= 0) // > 0: lock, 0: AVI and unlock, <0: lock invalid
			{
				name = filelist_GetFileFullNameByIndex(list, SysCtrl.file_index, &file_type);
				deg_Printf("delete : %s.",name);
				if(file_type & FILELIST_TYPE_SPI)
				{
					if(nv_jpgfile_delete(filelist_GetFileIndexByIndex(list,SysCtrl.file_index)) == NV_OK)
					{
						deg_Printf("->ok\n");
						filelist_delFileByIndex(list,SysCtrl.file_index);
						SysCtrl.file_cnt   = filelist_api_CountGet(list);
						SysCtrl.file_index = SysCtrl.file_cnt - 1;
						task_com_tips_show(TIPS_COM_SUCCESS);
					}else
					{
						deg_Printf("->fail\n");
						task_com_tips_show(TIPS_COM_FAIL);
					}
				}else
				{
					if(f_unlink(name)==FR_OK)
					{
						deg_Printf("->ok\n");
						filelist_delFileByIndex(list,SysCtrl.file_index);
						SysCtrl.file_cnt   = filelist_api_CountGet(list);
						SysCtrl.file_index = SysCtrl.file_cnt - 1;

						task_com_sdc_freesize_check();
						task_com_tips_show(TIPS_COM_SUCCESS);
					}
					else
					{
						deg_Printf("->fail\n");
						task_com_tips_show(TIPS_COM_FAIL);
					}
				}
			}
			else
				task_com_tips_show(TIPS_SET_LOCKED);
		}
		else
			uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delCurKeyMsgUp
* Description    : delCurKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCurKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,DELCUR_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delCurKeyMsgDown
* Description    : delCurKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCurKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		 uiItemManageNextItem(winItem(handle,DELCUR_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delCurKeyMsgMenu
* Description    : delCurKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCurKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	uint32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delCurKeyMsgMode
* Description    : delCurKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCurKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delCurOpenWin
* Description    : delCurOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCurOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]delCurOpenWin\n");
	uiItemManageSetRowSum(winItem(handle,DELCUR_SELECT_ID),1,Rh(40));
#if UI_SHOW_SMALL_PANEL == 0
	uiItemManageSetColumnSumWithGap(winItem(handle,DELCUR_SELECT_ID),0,2,Rw(90), Rw(12));
#else
	uiItemManageSetColumnSumWithGap(winItem(handle,DELCUR_SELECT_ID),0,2,Rw(100),Rw(6));
#endif 
	uiItemManageCreateItem(		winItem(handle,DELCUR_SELECT_ID),uiItemCreateMenuOption,getdelCurResInfor,2);
	uiItemManageSetCharInfor(	winItem(handle,DELCUR_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_Black);
	uiItemManageSetSelectColor(	winItem(handle,DELCUR_SELECT_ID),R_ID_PALETTE_Gray);
	uiItemManageSetUnselectColor(winItem(handle,DELCUR_SELECT_ID),R_ID_PALETTE_Gray_SUB_BG);

	uiItemManageSetCurItem(		winItem(handle,DELCUR_SELECT_ID),1);
	
	return 0;
}
/*******************************************************************************
* Function Name  : delCurCloseWin
* Description    : delCurCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCurCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]delCurCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : delCurWinChildClose
* Description    : delCurWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCurWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]delCurWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : delCurTouchWin
* Description    : delCurTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCurTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("delCurTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == DELCUR_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delCurTouchSlideOff
* Description    : delCurTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delCurTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;

	if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	return 0;
}

ALIGNED(4) msgDealInfor delCurMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	delCurOpenWin},
	{SYS_CLOSE_WINDOW,	delCurCloseWin},
	{SYS_CHILE_COLSE,	delCurWinChildClose},
	{SYS_TOUCH_WINDOW,  delCurTouchWin},
	{SYS_TOUCH_SLIDE_OFF,delCurTouchSlideOff},	
	{KEY_EVENT_PHOTO,		delCurKeyMsgOk},
	{KEY_EVENT_UP,		delCurKeyMsgUp},
	{KEY_EVENT_DOWN,	delCurKeyMsgDown},
	{KEY_EVENT_PLAYVIDEO,	delCurKeyMsgMenu},
	{KEY_EVENT_MODE,	delCurKeyMsgMode},
	{EVENT_MAX,NULL},
};

WINDOW(delCurWindow,delCurMsgDeal,delCurWin)


