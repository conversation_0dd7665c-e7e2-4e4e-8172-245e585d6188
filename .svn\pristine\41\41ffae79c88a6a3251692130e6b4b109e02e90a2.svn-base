/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

/*******************************************************************************
* Function Name  : app_taskRecordVideo_start
* Description    : app_taskRecordVideo_start function.
* Input          :
* Output         : none
* Return         : int fd : file handle
*******************************************************************************/
void app_taskRecordVideo_caltime(void)
{
	INT32U size1,size2;
	size1 = videoRecordSizePreSec(1);
	size2 = SysCtrl.sdc_freesize;

    //deg_Printf("rec time : %dkb/s,%dkb\n",size1,size2);
	SysCtrl.rec_remain_time = size2/size1;
}
/*******************************************************************************
* Function Name  : videoTimeCount1S
* Description    : videoTimeCount1S
* Input          : void
* Output         : none
* Return         : none
*******************************************************************************/
void taskRecordvideoTimeCount1S(void)
{
	if(videoRecordGetStatus() == MEDIA_STAT_START)
	{
		u32 frameCntTime = videoRecordGetTimeSec();
		if(SysCtrl.rec_show_time < frameCntTime)
		{
			if(SysCtrl.recordIncrease1S)
				SysCtrl.recordIncrease1S->reload = 900*X_TICK;
		}
		else if(SysCtrl.rec_show_time > frameCntTime)
		{
			if(SysCtrl.recordIncrease1S)
				SysCtrl.recordIncrease1S->reload = 1100*X_TICK;
		}
		else
		{
			if(SysCtrl.recordIncrease1S)
				SysCtrl.recordIncrease1S->reload = 1000*X_TICK;
		}
		SysCtrl.rec_show_time++;
		if(SysCtrl.rec_looptime)
		{
			if(SysCtrl.rec_show_time > SysCtrl.rec_looptime)
				SysCtrl.rec_show_time = 0;
		}

		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
	}
}
/*******************************************************************************
* Function Name  : app_taskRecordVideo_Capture
* Description    : app_taskRecordVideo_Capture function.
* Input          :
* Output         : none
* Return         : int fd : file handle
*******************************************************************************/
void app_taskRecordVideo_Capture(u32 cmd)
{
	int fd, ret;
	char *name;
	if(cmd == CMD_PHOTO_RECORD_START)
	{
		if(videoRecordGetStatus() == MEDIA_STAT_START && videoRecordTakePhotoStatus() == MEDIA_STAT_STOP)
		{
			SysCtrl.jpg_fname.index = FILELIST_FLAG_IVL;
			ret = filelist_SpaceCheck(SysCtrl.jpg_list,&SysCtrl.sdc_freesize,HAL_CFG_MJPEG_1080_SIZE_MAX*2);
			if(ret < 0)
			{
				deg_Printf("[VIDEO REC] tabke photo fail\n");
				return;
			}
			name = filelist_createNewFileFullName(SysCtrl.jpg_list, &SysCtrl.jpg_fname); //try to create new file name

			if(name == NULL)
			{
				deg_Printf("[VIDEO REC] tabke photo create file name fail.\n");
				//task_com_sdc_stat_set(SDC_STAT_FULL);
				return;
			}
			//---------open file from file system
			fd = fs_open(name,FA_CREATE_ALWAYS | FA_WRITE | FA_READ);
			if(fd < 0)
			{
				deg_Printf("[VIDEO REC] tabke photo open file fail.%s\n",name);
				return;
			}
			deg_Printf("[REC] tabke photo  [%s] start\n",name);
			videoRecordTakePhotoCfg(MEDIA_STAT_START,fd);
		}
	}
	else if(cmd == CMD_PHOTO_RECORD_STOP)
	{
		//if(videoRecordTakePhotoStatus() == MEDIA_STAT_READY && audioPlaybackGetStatus() != MEDIA_STAT_PLAY)
		{
			fd = videoRecordTakePhotoFd();
			if(fd >= 0)
			{
				task_com_sdc_freesize_modify(-1,fs_size(fd));
				fs_close(fd);
				filenode_addFileByFname(SysCtrl.jpg_list, &SysCtrl.jpg_fname);
			}
			deg_Printf("[VIDEO REC] tabke photo STOP\n");
			videoRecordTakePhotoCfg(MEDIA_STAT_STOP,-1);
		}
	}else if(cmd == CMD_COM_ERROR)
	{
		fd = videoRecordTakePhotoFd();
		if(fd >= 0)
		{
			fs_close(fd);
			name = filelist_createNewFileFullNameByFname(SysCtrl.jpg_list, &SysCtrl.jpg_fname);
			if(name)
			{
				f_unlink(name);
				deg_Printf("[VIDEO REC] tabke photo unfinish.delete file:%s\n",name);
			}
		}
		videoRecordTakePhotoCfg(MEDIA_STAT_STOP,-1);

	}
}
/*******************************************************************************
* Function Name  : app_taskRecordVideo_error
* Description    : APP LAYER: video callback for rec error, if file exits,delete it
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int app_taskRecordVideo_error_callback(INT32U para)
{
	int *fdt = (int *)para;
	INT32U filesize;
	char * name;
	//SysCtrl.dev_stat_gsensorlock = 0; // clear g-sensor lock active flag

	//if(channel == VIDEO_CH_A)
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_ERROR));
	if(sd_api_CardState_Get() == SDC_STATE_ERROR)
	{
		task_com_sdc_stat_set(SDC_STAT_ERROR);
		return 0;
	}
	deg_Printf("[VIDEO ERR] 0 freesize:%dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,SysCtrl.sdc_freesize&0x3ff);
	if((SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL) && (fdt[0] >= 0) && (!(SysCtrl.new_fname.index & FILELIST_FLAG_IVL)))
	{
		filesize = fs_size(fdt[0]);
		fs_close((int)fdt[0]);
#if (AVI_TYPE_CFG  == MEDIA_AVI_STD)//MEDIA_AVI_ODML //
		fs_close((int)fdt[1]);
#endif
		//SysCtrl.new_fname.index &= ~FILELIST_FLAG_AB;
		name = filelist_createNewFileFullNameByFname(SysCtrl.avi_list, &SysCtrl.new_fname);
		if(name)
		{
			deg_Printf("[VIDEO REC] file delete: %s,size:%dkb\n",name,filesize);
			f_unlink(name);
		}
#if (AVI_TYPE_CFG  == MEDIA_AVI_STD)//MEDIA_AVI_ODML //
		f_unlink(VIDEO_STDAVI_TMP_FILE);

#endif
		if(SysCtrl.file_premalloc)
		{
			task_com_sdc_freesize_modify(1,filesize);
		}
	}
	deg_Printf("[VIDEO ERR] 1 freesize:%dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,SysCtrl.sdc_freesize&0x3ff);
	return -1;
}
/*******************************************************************************
* Function Name  : app_taskRecordVideo_start_callback
* Description    : APP LAYER: video callback for rec start to create file
* Input          : none
* Output         : none
* Return         : return kb
*******************************************************************************/
static int app_taskRecordVideo_filestart_callback(INT32U para)
{
	int *fdt = (int *)para;

	INT32U  rectime, rectime_temp;
	INT32U  asize,filesize; //kbytes
	int 	ret,index;
	char 	*name;
	rectime = rectime_temp = user_configValue2Int(CONFIG_ID_LOOPTIME);
	deg_Printf("[VIDEO REC] 0 freesize:%dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,SysCtrl.sdc_freesize&0x3ff);
	filesize = 0;
	fdt[0] = fdt[1] = -1;
	//-----------------file handle init
	SysCtrl.new_fname.index = FILELIST_FLAG_IVL;
	SysCtrl.old_fname.index = FILELIST_FLAG_IVL;
	SysCtrl.file_premalloc	= 0;
	if(rectime == 0)  // NO LOOP , STOP REC
	{
		//rectime = 3*60; 
#if AVI_FILE_PREMALLOC == 0	
		asize = videoRecordSizePreSec(3*60) + REMAIN_MIN_VIDEO;//at least 3min
#else
		asize = videoRecordSizePreSec(10*60) + REMAIN_MIN_VIDEO;//at least 3min
#endif
		//deg_Printf("asize:%dMb, SysCtrl.sdc_freesiz:%dMb\n",asize>>10,SysCtrl.sdc_freesize>>10);
		if(SysCtrl.sdc_freesize < asize)
		{
			task_com_sdc_stat_set(SDC_STAT_FULL);
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_STOP));
			//deg_Printf("rectime_temp:%d, SysCtrl.sdc_freesize:%d\n",rectime_temp,SysCtrl.sdc_freesize);
			return -1;
		}
#if AVI_FILE_PREMALLOC == 0		
	#if (AVI_TYPE_CFG  == MEDIA_AVI_STD)//MEDIA_AVI_ODML
		asize = (SysCtrl.sdc_freesize> (AVISTD_CFG_FILE_MAX>>10)) ? (AVISTD_CFG_FILE_MAX>>10):(SysCtrl.sdc_freesize);
#else
		asize = (SysCtrl.sdc_freesize> (AVIDML_CFG_FILE_MAX>>10)) ? (AVIDML_CFG_FILE_MAX>>10):(SysCtrl.sdc_freesize);
	#endif
#endif
		asize-= REMAIN_MIN_VIDEO;
	}else
	{
		asize = videoRecordSizePreSec(rectime);
	}
	//deg_Printf("asize:%x, %d\n",asize,rectime);

	name = filelist_createNewFileFullName(SysCtrl.avi_list, &SysCtrl.new_fname); //try to create new file name
	if(name == NULL)
	{
		task_com_sdc_stat_set(SDC_STAT_FULL);
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_STOP));
		return -1;
	}

	//deg_Printf("index:%x, datetime:%x\n",SysCtrl.new_fname.index,SysCtrl.new_fname.datatime);
	hx330x_str_cpy(SysCtrl.file_fullname,name);

	//--when sd pre full, try to find lastest file to replace---
	if(rectime_temp != 0 && asize > SysCtrl.sdc_freesize)  //try to find old using file
	{
		name = filelist_findFirstFileName(SysCtrl.avi_list, &SysCtrl.old_fname,&index);
		if(name)
		{
			fdt[0] = fs_open(name,FA_WRITE | FA_READ);
			if(fdt[0]>=0)
			{
				filesize = fs_size(fdt[0])>>10;
				if(hx330x_data_check(filesize,asize,asize + 100*1024L))
				{
					filelist_delFileByIndex(SysCtrl.avi_list, index);
				}
				else
				{
					fs_close(fdt[0]);
					fdt[0] = -1;
					filesize = 0;
				}

			}
		}
	}

	if(fdt[0]<0)
	{
		fdt[0] = fs_open(SysCtrl.file_fullname,FA_CREATE_ALWAYS | FA_WRITE | FA_READ);  // FA_CREATE_NEW
	}
	else
	{
		fs_close(fdt[0]);
		if(name)
			f_rename(name,SysCtrl.file_fullname);  // must close before rename
		fdt[0] = fs_open(SysCtrl.file_fullname,FA_WRITE | FA_READ);
		deg_Printf("[VIDEO REC] reuse file:%s.%d kb ->",name,filesize);
	}
	deg_Printf("[VIDEO REC] file %s ",SysCtrl.file_fullname);

#if (AVI_TYPE_CFG  == MEDIA_AVI_STD)//MEDIA_AVI_ODML

	fdt[1] = fs_open(VIDEO_STDAVI_TMP_FILE,FA_CREATE_ALWAYS | FA_WRITE | FA_READ);  // FA_CREATE_NEW
#else
	fdt[1] = fdt[0];
#endif
	if((fdt[0]>=0) && (fdt[1]>=0)) // success
	{
		deg_Printf("success.\n");
		if(filesize == 0) // try to alloc size
		{
		//----count video file size
			filesize = asize;

#if (AVI_TYPE_CFG  == MEDIA_AVI_STD)//MEDIA_AVI_ODML
			if(filesize > (AVISTD_CFG_FILE_MAX>>10)) //1G
				filesize = AVISTD_CFG_FILE_MAX>>10;
#else
			if(filesize > (AVIDML_CFG_FILE_MAX>>10)) // 3.8G
				filesize = AVIDML_CFG_FILE_MAX>>10;
#endif
			//deg_Printf("[VIDEO REC] 4 freesize:%dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,SysCtrl.sdc_freesize&0x3ff);
			if(rectime_temp != 0)
			{
				ret = filelist_SpaceCheck(SysCtrl.avi_list,&SysCtrl.sdc_freesize,filesize);
				//deg_Printf("[VIDEO REC] 5 freesize:%dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,SysCtrl.sdc_freesize&0x3ff);
				if(ret < 0)
				{
					deg_Printf("[VIDEO REC] no more space\n");
					task_com_sdc_stat_set(SDC_STAT_FULL);
					//XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_STOP));
					return app_taskRecordVideo_error_callback(para);
				}
			}
			//---------------try to pre malloc file-------------------
#if AVI_FILE_PREMALLOC
			ret = XOSTimeGet();
			if(fs_seek(fdt[0],filesize<<10,FA_CREATE_LINKMAP) < 0)
			{
				deg_Printf("[VIDEO REC] premalloc fail.%dMB,%dKB\n",filesize<<10,filesize);
				//task_com_sdc_stat_set(SDC_STAT_FULL);
				//return app_taskRecordVideo_error_callback(para);
			}else
			{
				SysCtrl.file_premalloc = 1;
				fs_sync(fdt[0]);
				ret = XOSTimeGet()-ret;
				deg_Printf("[VIDEO REC] premalloc time using : %dms,file size : %dkb,speed = %dkb/ms.\n",ret,filesize,filesize/ret);
				task_com_sdc_freesize_modify(-1,filesize<<10); //size change to bytes
			}
			fs_seek(fdt[0],0,0);

#endif
		}else
		{
			SysCtrl.file_premalloc = 1;
		}
		deg_Printf("[VIDEO REC] 1 freesize:%dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,SysCtrl.sdc_freesize&0x3ff);
		deg_Printf("SysCtrl.file_premalloc:%d\n",SysCtrl.file_premalloc);
		return filesize;	//kb
	}
	else //fail
	{
		deg_Printf("fail.\n");
		return app_taskRecordVideo_error_callback(para);
	}
}
/*******************************************************************************
* Function Name  : app_taskRecordVideo_stop_callback
* Description    : APP LAYER: video callback for rec stop to save file
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int app_taskRecordVideo_filestop_callback(INT32U para)
{
	int *fdt = (int *)para;

	char * name;
	if(fdt[0] < 0)
	{
		return 0;
	}
	if(!((SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)||(SysCtrl.dev_stat_sdc == SDC_STAT_FULL)))
	{
		return app_taskRecordVideo_error_callback(para);
	}
	deg_Printf("[VIDEO STOP] 0 freesize:%dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,SysCtrl.sdc_freesize&0x3ff);
	if(SysCtrl.file_premalloc == 0)
	{
		u32 filesize = fs_size((int)fdt[0]);
		deg_Printf("filesize:%d\n",filesize);
		if(filesize > 0)
		{
			task_com_sdc_freesize_modify(-1,filesize);
		}
	}
	fs_close((int)fdt[0]);
#if (AVI_TYPE_CFG  == MEDIA_AVI_STD)//MEDIA_AVI_ODML//
	fs_close((int)fdt[1]);
#endif

	//SysCtrl.new_fname.index &= ~(FILELIST_FLAG_LOK);
	name = filelist_createNewFileFullNameByFname(SysCtrl.avi_list, &SysCtrl.new_fname);

	if(name == NULL)
	{
		SysCtrl.dev_stat_gsensorlock = 0;
		return -1;
	}
	if(SysCtrl.dev_stat_gsensorlock)// video A
	{
		hx330x_str_cpy(SysCtrl.file_fullname,name);
		filenode_filefullnameLock(name);
		SysCtrl.new_fname.index |= FILELIST_FLAG_LOK;
		f_rename(SysCtrl.file_fullname,name);  // rename in file system
		deg_Printf("[VIDEO REC] : rename  .%s->%s\n",SysCtrl.file_fullname,name);
		deg_Printf("[VIDEO REC] : lock this file.\n");
	}
	deg_Printf("[VIDEO REC] : stop ");
	filenode_addFileByFname(SysCtrl.avi_list, &SysCtrl.new_fname);

	deg_Printf(" %s\n",name);
	//app_taskRecordVideo_caltime();
	deg_Printf("[VIDEO STOP] 1 freesize:%dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,SysCtrl.sdc_freesize&0x3ff);
	return 0;
}
/*******************************************************************************
* Function Name  : app_taskRecordVideo_callback
* Description    : app_taskRecordVideo_callback function.user should fill the function,video record service will callback
* Input          :
* Output         : none
* Return         : int fd : file handle
*******************************************************************************/
static int app_taskRecordVideo_callback(INT32U cmd,INT32U para)
{
    switch(cmd)
	{
		case CMD_COM_ERROR:				return app_taskRecordVideo_error_callback(para);
		case CMD_VIDEO_RECORD_START:	return app_taskRecordVideo_filestart_callback(para);
		case CMD_VIDEO_RECORD_STOP:		return app_taskRecordVideo_filestop_callback(para);
		default:break;
	}
	return 0;
}

/*******************************************************************************
* Function Name  : app_taskRecordVideo_start
* Description    : app_taskRecordVideo_start function.
* Input          :
* Output         : none
* Return         : int fd : file handle
*******************************************************************************/
int app_taskRecordVideo_start(void)
{
	int ret;

	if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)
	{
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SysCtrl.dev_stat_sdc));
		return -1;
	}
	if(videoRecordGetStatus() == MEDIA_STAT_START)
		return 0;
    task_com_sound_wait_end();
	res_music_end();
	//SysCtrl.rec_show_time = 0;
	if(!husb_api_usensor_atech_sta())
	{
		return -1;
	}
	u16 width, height;
	//u32 value;
	if(husb_api_usensor_res_get(&width, &height) < 0)
	{
		return -1;
	}else
	{
	#if HAL_MJP_SAVE_CARD_DIRECT	
		u32 value = (width << 16)|height;
		videoRecordCmdSet(CMD_COM_RESOLUTIONN,value);
	#endif

	}
	u32 para = MSG_RECORD_START;
	app_msgDealByType(SYS_EVENT_RECORD,uiWinGetCurrent(),1,&para);

    ret = videoRecordStart();


	deg_Printf("[VIDEO REC] start.%d\n",ret);
	//SysCtrl.dev_stat_power |= POWERON_FLAG_KEEPON;
	return ret;
}
/*******************************************************************************
* Function Name  : app_taskRecordVideo_stop
* Description    : app_taskRecordVideo_stop function.
* Input          :
* Output         : none
* Return         : int: >=0 stop success
*******************************************************************************/
int app_taskRecordVideo_stop(void)
{
	int ret;

	if(videoRecordGetStatus() != MEDIA_STAT_START)
		return -1;
	if(videoRecordTakePhotoStatus() == MEDIA_STAT_START)
	{
		app_taskRecordVideo_Capture(CMD_COM_ERROR);
	}else if(videoRecordTakePhotoStatus() == MEDIA_STAT_READY)
	{
		app_taskRecordVideo_Capture(CMD_PHOTO_RECORD_STOP);
	}
	ret = videoRecordStop();
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_STOP));

	//SysCtrl.dev_stat_gsensorlock = 0; // clear g-sensor lock active flag
	deg_Printf("[VIDEO REC] stop.%d,%d\n",ret,videoRecordGetStatus());
	dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 1);

	return ret;
}



/*******************************************************************************
* Function Name  : taskRecordVideoOpen
* Description    : taskRecordVideoOpen function.
* Input          :
* Output         : none
* Return         : int fd : file handle
*******************************************************************************/
static void taskRecordVideoOpen(u32 arg)
{
	VIDEO_ARG_T video_arg;
	INT32S ret;
	SysCtrl.dev_stat_gsensorlock = 0; // clear g-sensor lock active flag
	SysCtrl.new_fname.index = FILELIST_FLAG_IVL;
	SysCtrl.old_fname.index = FILELIST_FLAG_IVL;
	SysCtrl.jpg_fname.index = FILELIST_FLAG_IVL;
	SysCtrl.rec_md_time		= 0;
	SysCtrl.rec_show_time 	= 0;
	SysCtrl.recordIncrease1S = XWorkCreate(1000*X_TICK, taskRecordvideoTimeCount1S);
	if(SysCtrl.recordIncrease1S  == NULL)
	{
		deg_Printf("[VIDEO REC]:time sync init fail\n");
		return;
	}
	//deg_Printf("video record task enter.\n");
	ret = user_configValue2Int(CONFIG_ID_RESOLUTION);
	video_arg.avi_arg.width			= ret >> 16;
	video_arg.avi_arg.height		= ret&0xffff;
	video_arg.avi_arg.fps 			= HAL_AVI_FRAMRATE;
	video_arg.avi_arg.audio_en 		= user_configValue2Int(CONFIG_ID_AUDIOREC);
	video_arg.avi_arg.samplerate	= MEDIA_CFG_AUDSRATE;
	video_arg.avi_arg.sync_wr		= FUN_VIDEO_SYNC_WRITE;
	video_arg.avi_arg.fd[0]			= -1;
	video_arg.avi_arg.fd[1]			= -1;
	video_arg.avi_arg.avi_cache		= NULL;

	video_arg.rectime 				= user_configValue2Int(CONFIG_ID_LOOPTIME);
	if(video_arg.rectime==0)
	{
		video_arg.rectime = 60*60;  // 10 min
		video_arg.looprecord = 0;
	}
	else
		video_arg.looprecord = 1;
	video_arg.ftype					= AVI_TYPE_CFG;
	video_arg.timestramp 			= user_configValue2Int(CONFIG_ID_TIMESTAMP);
	video_arg.quality 				= JPEG_Q_40;
    video_arg.callback 				= app_taskRecordVideo_callback;


    videoRecordInit(&video_arg);

	//app_taskRecordVideo_caltime(); // re-cal

	//app_lcdCsiVideoShowStart();
	if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW || SysCtrl.dev_husb_stat == USBHOST_STAT_ASTERN )
	{
		if(husb_api_usensor_res_type_is_mjp())
		{
			if(app_lcdCsiVideoLayerEnGet() == 0)
			{
				husb_api_usensor_relinkLcd_reg();
				husb_api_usensor_linkingLcd();	
			}

			SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINB;
		}else
		{
			SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINA;
		}
		
	}else
	{
		SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINA;
	}

    app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
	uiOpenWindow(&recordVideoWindow,0,0);
}
/*******************************************************************************
* Function Name  : taskRecordVideoService
* Description    : taskRecordVideoService function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
static void taskRecordVideoService(u32 arg)
{
	if(videoRecordTakePhotoStatus() == MEDIA_STAT_READY && audioPlaybackGetStatus() != MEDIA_STAT_PLAY)
	{
		app_taskRecordVideo_Capture(CMD_PHOTO_RECORD_STOP);
#if 0
		if(SysCtrl.dev_stat_keysound)
			res_music_start(R_ID_MUSIC_TAKE_PHOTO,0,FUN_KEYSOUND_VOLUME);
#endif
	}
	if(videoRecordGetStatus() == MEDIA_STAT_START)
	{
		if(!husb_api_usensor_atech_sta())
		{
			app_taskRecordVideo_stop();
		}else
		{
			videoRecordService(); // record service
		}
	}

}
/*******************************************************************************
* Function Name  : taskRecordVideoClose
* Description    : taskRecordVideoClose function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
static void taskRecordVideoClose(u32 arg)
{
	SysCtrl.dev_stat_power &= ~(POWERON_FLAG_FIRST|POWERON_FLAG_WAIT);
	app_taskRecordVideo_stop();
	XWorkDestory(SysCtrl.recordIncrease1S);
	SysCtrl.recordIncrease1S = NULL;
	if(SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		SysCtrl.dev_stat_sdc = SDC_STAT_NORMAL;
	}
	task_com_sdlist_scan(1, 1);
	videoRecordUninit();
	task_com_sound_wait_end();
	//下一个任务是record photo，不需要stop
	//app_lcdCsiVideoShowStop();

}
ALIGNED(4) sysTask_T taskRecordVideo =
{
	"Record Video",
	(u32)NULL,
	taskRecordVideoOpen,
	taskRecordVideoClose,
	taskRecordVideoService,
};


