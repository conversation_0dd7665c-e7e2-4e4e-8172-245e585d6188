/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hal.h"



/*******************************************************************************
* Function Name  : hal_gpioInit
* Description    : hal layer .gpio initial for normal using
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 dir  :  dir. ->GPIO_OUTPUT,GPIO_INPUT
				   u8 pull : pull. ->GPIO_PULL_FLOATING,GPIO_PULL_UP,GPIO_PULL_DOWN,GPIO_PULL_UPDOWN
* Output         : None
* Return         : none
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_gpioInit(u8 ch,u32 pin,u8 dir,u8 pull)
{
	hx330x_gpioDigitalSet(ch,pin,GPIO_DIGITAL_EN);
	hx330x_gpioMapSet(ch,pin,GPIO_FUNC_GPIO);     // set as gpio func
	hx330x_gpioDrvSet(ch,pin,GPIO_DRV_NORMAL);   // set normal drving
	hx330x_gpioDirSet(ch,pin,dir);                    // set direction
	hx330x_gpioLedPull(ch,pin,GPIO_PULLE_FLOATING);  // set LED pull level,	
	hx330x_gpioPullSet(ch,pin,pull);                 // set pull level,
}
/*******************************************************************************
* Function Name  : hal_gpioInit
* Description    : hal layer .gpio initial for HIGH VOL IO
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 dir  :  dir. ->GPIO_OUTPUT,GPIO_INPUT
				   u8 pull : pull. ->GPIO_PULL_FLOATING,GPIO_PULL_UP,GPIO_PULL_DOWN,GPIO_PULL_UPDOWN
* Output         : None
* Return         : none
*******************************************************************************/
void hal_gpioInit_io1d1(u8 ch,u32 pin,u8 dir,u8 pull)  //HIGH VOL IO
{
	hx330x_gpioDigitalSet(ch,pin,GPIO_DIGITAL_DIS);
	hx330x_gpioMapSet(ch,pin,GPIO_FUNC_GPIO);     // set as gpio func
	hx330x_gpioDrvSet(ch,pin,GPIO_DRV_NORMAL);   // set normal drving
	hx330x_gpioDirSet(ch,pin,dir);                    // set direction
	hx330x_gpioLedPull(ch,pin,GPIO_PULLE_FLOATING);  // set LED pull level,	
	hx330x_gpioPullSet(ch,pin,pull);                 // set pull level,
}
/*******************************************************************************
* Function Name  : hal_gpioLedInit
* Description    : hal layer .gpio initial for led control
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 pull : pull. ->GPIO_PULLE_FLOATING,GPIO_PULLE_UP,GPIO_PULLE_DOWN,GPIO_PULLE_UPDOWN
* Output         : None
* Return         : none
*******************************************************************************/
SDRAM_TEXT_SECTION
void hal_gpioEPullSet(u8 ch,u32 pin,u8 led_pull)
{
	hx330x_gpioPullSet(ch,pin,GPIO_PULL_FLOATING);  // set pull level,	

	hx330x_gpioLedPull(ch,pin,led_pull);  // set pull level,	
}

