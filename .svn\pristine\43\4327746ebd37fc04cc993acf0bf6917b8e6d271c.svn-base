/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuDefaultWin.c"

/*******************************************************************************
* Function Name  : getdefaultResInfor
* Description    : getdefaultResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static uint32 getdefaultResInfor(u32 item,u32* image,u32* str)
{
	if(item==0)
	{
		if(image)
			*image 	= INVALID_RES_ID;
		if(str)
			*str	= R_ID_STR_COM_OK;
	}
	else if(item==1)
	{
		if(image)
			*image	= INVALID_RES_ID;
		if(str)
			*str	= R_ID_STR_COM_CANCEL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultKeyMsgOk
* Description    : defaultKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	DATE_TIME_T *rtcTime = hal_rtcTimeGet();
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,DEFAULT_SELECT_ID));
		if(item == 0)
		{
			
			userConfig_Reset();
			user_config_cfgSysAll();

			rtcTime->year 	= 2025;
			rtcTime->month 	= 1;
			rtcTime->day 	= 1;
			rtcTime->hour 	= 0;
			rtcTime->min 	= 0;
			rtcTime->sec 	= 0;
			hal_rtcTimeSet(rtcTime);
			task_com_tips_show(TIPS_DEF_SUCCESS);
			XOSTimeDly(1000);
		}
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultKeyMsgUp
* Description    : defaultKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,DEFAULT_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultKeyMsgDown
* Description    : defaultKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		 uiItemManageNextItem(winItem(handle,DEFAULT_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultKeyMsgMenu
* Description    : defaultKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultKeyMsgMode
* Description    : defaultKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultOpenWin
* Description    : defaultOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]defaultOpenWin\n");
	uiItemManageSetRowSum(winItem(handle,DEFAULT_SELECT_ID),1,Rh(40));
#if UI_SHOW_SMALL_PANEL == 0
	uiItemManageSetColumnSumWithGap(winItem(handle,DEFAULT_SELECT_ID),0,2,Rw(90), Rw(0));
#else
	uiItemManageSetColumnSumWithGap(winItem(handle,DEFAULT_SELECT_ID),0,2,Rw(100),Rw(6));
#endif 
	uiItemManageCreateItem(		winItem(handle,DEFAULT_SELECT_ID),uiItemCreateMenuOption,getdefaultResInfor,2);
	uiItemManageSetCharInfor(	winItem(handle,DEFAULT_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_Black);
	uiItemManageSetSelectColor(	winItem(handle,DEFAULT_SELECT_ID),R_ID_PALETTE_Gray);
	uiItemManageSetUnselectColor(winItem(handle,DEFAULT_SELECT_ID),R_ID_PALETTE_Gray_SUB_BG);

	uiItemManageSetCurItem(winItem(handle,DEFAULT_SELECT_ID),1);
	
	return 0;
}
/*******************************************************************************
* Function Name  : defaultCloseWin
* Description    : defaultCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]defaultCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : defaultWinChildClose
* Description    : defaultWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]defaultWinChildClose\n");
	return 0;
}
/*******************************************************************************
* Function Name  : defaultTouchWin
* Description    : defaultTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("defaultTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == DEFAULT_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : defaultTouchSlideOff
* Description    : defaultTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int defaultTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;

	if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	return 0;
}

ALIGNED(4) msgDealInfor defaultMsgDeal[] =
{
	{SYS_OPEN_WINDOW,	defaultOpenWin},
	{SYS_CLOSE_WINDOW,	defaultCloseWin},
	{SYS_CHILE_COLSE,	defaultWinChildClose},
	{SYS_TOUCH_WINDOW,  defaultTouchWin},
	{SYS_TOUCH_SLIDE_OFF,defaultTouchSlideOff},
	{KEY_EVENT_PHOTO,		defaultKeyMsgOk},
	{KEY_EVENT_UP,		defaultKeyMsgUp},
	{KEY_EVENT_DOWN,	defaultKeyMsgDown},
	{KEY_EVENT_PLAYVIDEO,	defaultKeyMsgMenu},
	{KEY_EVENT_MODE,	defaultKeyMsgMode},
	{EVENT_MAX,NULL},
};

WINDOW(defaultWindow,defaultMsgDeal,defaultWin)


