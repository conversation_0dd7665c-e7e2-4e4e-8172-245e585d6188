#ifndef SPI_BOOT_CFG_H
#define SPI_BOOT_CFG_H

//#include "inc/hx330x_cfg.h"

#define  FPGA                0x0080
#define  HX3302B				 0x3220   //LQFP64 CAR
#define  HX3302				 0x3280   //LQFP64 CAR
#define  HX3303				 0x3380	  //LQFP80 HD LCD
#define  HX3304				 0x3480	  //LCD DRIVER
#define  HX3305				 0x3520   //LQFP48 DV
#define  HX3308              0x3880

#define  SDRAM_SIZE_2M       0
#define  SDRAM_SIZE_8M       1

/*******************************************************************
**Define  CURRENT BOARD 
********************************************************************/
#define  CURRENT_CHIP        HX3302B//HX3302    //  current body     

//-----------------------------------------


#if ((CURRENT_CHIP&0xf0)==0x20)
   #define  SDRAM_SIZE       SDRAM_SIZE_2M  // 2M
#elif ((CURRENT_CHIP&0xf0)==0x80)
   #define  SDRAM_SIZE       SDRAM_SIZE_8M  // 8M
#else
   #define  SDRAM_SIZE       SDRAM_SIZE_8M  // 8M
#endif


#define BOOT_ADC_BAUDRATE       400000		//adc of boot

#if (CURRENT_CHIP == FPGA)
#define COMBO_CLOCK             72000000//48000000
#define PLL_DIV                 72000000/COMBO_CLOCK
#define APB_CLK					COMBO_CLOCK
#define PLL_CLK					COMBO_CLOCK
#define BOOT_CLK				72000000
#define BOOT_SPI_BAUDRATE		4000000    //6000000		//spi after boot
#define SPIFLASH_BAUDRATE		48000000    //6000000		//spi after boot
#define LOADER_SPI_BAUDRATE		500000		//spi of boot
#define SPI_BAUD           		((BOOT_CLK) / (LOADER_SPI_BAUDRATE) - 1)

#else
#define COMBO_CLOCK             144000000//48000000
#define PLL_DIV                 2//144000000/COMBO_CLOCK
#define APB_CLK					COMBO_CLOCK
#define PLL_CLK					(APB_CLK*PLL_DIV)
#define BOOT_CLK				144000000
#define LOADER_SPI_BAUDRATE		36000000    //BOOT_CLK/2		//spi after boot
#define BOOT_SPI_BAUDRATE		48000000    //APB_CLK/2		//spi after boot
#define SPIFLASH_BAUDRATE		72000000    //6000000		//spi after boot

#define SPI_BAUD           		((BOOT_CLK/2) / (LOADER_SPI_BAUDRATE) - 1)

#endif





#define SDRAM_CLK				200000000L//200000000L

////////////////////////////////////////////////////////////////////////////////

#define ENCRYPT            		0
#define NO_CHKSUM          		1
#define INVALID_KEY        		0
#define SPI_DMA            		1


//#define SPI_1W             	1

#define ADC_BAUD           		(((BOOT_CLK)/ (2*BOOT_ADC_BAUDRATE)- 1) & 0x7F)

#define ADC_CHANNEL        		1
#define ADKEY_DOWNVALUE    		1
#define ADKEY_UPVALUE      		0x5c
////////////////////////////////////////////////////////////////////////////////
#define USE_RC					0
#define USE_XOSC				1
#define AUTO_CHECK_XOSC			2



#define BOOT_UART_DEBG			0
#define SAVE_SDRAM				0 //是否保存第一次tune的值到irtc ram，以后读ram的值进行配置，加快开机

#endif




