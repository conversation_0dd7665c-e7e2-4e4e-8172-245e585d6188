/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_JD9851

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
CMD(0xDF), //Password
DAT(0x98),
DAT(0x51),
DAT(0xE9),

C<PERSON>(0xDE),
DAT(0x00),

CMD(0xB7),
DAT(0x20),
DAT(0x83),
DAT(0x20),
DAT(0x2D),

CMD(0xC8),
DAT(0x3F),
DAT(0x38),
DAT(0x33),
DAT(0x31),
DAT(0x34),
DAT(0x37),
DAT(0x31),
DAT(0x30),
DAT(0x2E),
DAT(0x2C),
DAT(0x27),
DAT(0x19),
DAT(0x14),
DAT(0x0C),
DAT(0x06),
DAT(0x0A),
DAT(0x3F),
DAT(0x37),
DAT(0x31),
DAT(0x30),
DAT(0x33),
DAT(0x36),
DAT(0x31),
DAT(0x30),
DAT(0x2D),
DAT(0x2B),
DAT(0x26),
DAT(0x19),
DAT(0x14),
DAT(0x0C),
DAT(0x06),
DAT(0x0A),

CMD(0xB9),
DAT(0x33),
DAT(0x08),
DAT(0xCC),

CMD(0xBB),
DAT(0x45),
DAT(0x5A),
DAT(0x30),
DAT(0x30),
DAT(0x7C),
DAT(0x60),
DAT(0x60),
DAT(0x70),

CMD(0xBC),
DAT(0x38),
DAT(0x3C),

CMD(0xC0),
DAT(0x31),
DAT(0x20),

CMD(0xC1),
DAT(0x12),

CMD(0xC3),
DAT(0x08),
DAT(0x00),
DAT(0x0A),
DAT(0x10),
DAT(0x08),
DAT(0x54),
DAT(0x45),
DAT(0x71),
DAT(0x2C),

CMD(0xD0),
DAT(0x04),//
DAT(0x0C),//
DAT(0x6A),//
DAT(0x0F),//
DAT(0x00),//
DAT(0x03),//


CMD(0xD7),
DAT(0x20),
DAT(0x00),

CMD(0xDE),
DAT(0x02),

CMD(0xB8),
DAT(0x1D),
DAT(0x96),
DAT(0xAF),
DAT(0x31),
DAT(0x17),

CMD(0xC1),
DAT(0x10),
DAT(0x66),
DAT(0x66),
DAT(0x01),

CMD(0xC4),
DAT(0x72),
DAT(0x02),

CMD(0xDE),
DAT(0x00),

CMD(0x11),  	// SLPOUT
DLY(120),

CMD(0xDE),
DAT(0x02),

CMD(0xC5),
DAT(0x01), 	//10MHz
DAT(0x00),
DAT(0x00),

CMD(0xDE),
DAT(0x00),

CMD(0x35),  // TE enable
DAT(0x00),
CMD(0x44), DAT(0x00), DAT(0x00), // TE line

CMD(0x36), DAT(0x00),
CMD(0x3A), DAT(0x05),

CMD(0x29),
CMD(0x2C),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name 			= "MCU_jd9851",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,
    .te_mode 		= LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(320*240*2*60),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 240,
    .screen_h 		= 320,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)

    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif
