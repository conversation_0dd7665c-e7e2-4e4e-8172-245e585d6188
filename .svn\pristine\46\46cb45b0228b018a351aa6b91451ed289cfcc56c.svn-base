/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  __MSG_API_H_
#define  __MSG_API_H_

typedef int (*msgDealFunc)(void* handle,uint32 parameNum,uint32* parame);
typedef struct _msgDealInfor
{
	uint32 msgId;
	msgDealFunc func;
}msgDealInfor;
/*******************************************************************************
* Function Name  : taskmsgFuncRegister
* Description    : taskmsgFuncRegister
* Input          : msgDealInfor* info
* Output         : none                                            
* Return         : none
*******************************************************************************/
void taskmsgFuncRegister(msgDealInfor* infor);
/*******************************************************************************
* Function Name  : sysMsgFuncRegister
* Description    : sysMsgFuncRegister
* Input          : msgDealInfor* info
* Output         : none                                            
* Return         : none
*******************************************************************************/
void sysMsgFuncRegister(msgDealInfor* infor);
/*******************************************************************************
* Function Name  : app_msgDeal
* Description    : app_msgDeal
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void app_msgDeal(void);
/*******************************************************************************
* Function Name  : app_msgDeal
* Description    : app_msgDeal
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void app_msgDealByType(u32 msgType,void* handle,u32 parameNum,u32* parame);

/*******************************************************************************
* Function Name  : app_msgDealByInfor
* Description    : app_msgDealByInfor
* Input          : msgDealInfor* infor,u32 msgType,void* handle,u32 parameNum,u32* parame
* Output         : none                                            
* Return         : none
*******************************************************************************/
void app_msgDealByInfor(msgDealInfor* infor,u32 msgType,void* handle,u32 parameNum,u32* parame);


#endif
