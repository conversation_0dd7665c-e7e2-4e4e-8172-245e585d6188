/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HX330X_WDT_H
   #define  HX330X_WDT_H



typedef enum
{
	WDT_TIME_2MS=0,
	WDT_TIME_8MS,
	WDT_TIME_32MS,
	WDT_TIME_127MS,
	WDT_TIME_512MS,
	WDT_TIME_2048MS,
	WDT_TIME_8192MS,
	WDT_TIME_32768MS
}WDT_TIME_E;


/*******************************************************************************
* Function Name  : hx330x_wdtEnable
* Description    : enable watch dog
* Input          : u8 en : 1-enable,1-disable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_wdtEnable(u8 en);

/*******************************************************************************
* Function Name  : hx330x_wdtClear
* Description    : clear watch dog 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_wdtClear(void);
/*******************************************************************************
* Function Name  : hx330x_wdtReset
* Description    : system reset  using wdt
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_wdtReset(void);






#endif
