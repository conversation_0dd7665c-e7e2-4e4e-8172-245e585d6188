/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT == LCD_RGB_ST7701S_LX50FWH40149

#define WriteComm(x)  LCD_CMD_RGB_DAT(x)
#define WriteData(x)  LCD_CMD_RGB_DAT((x)|(1<<8))
#define Delay_ms(m)   LCD_CMD_DELAY_MS(m)


LCD_INIT_TAB_BEGIN()

WriteComm(0xFF),
WriteData(0x77),
WriteData(0x01),
WriteData(0x00),
WriteData(0x00),
WriteData(0x13),

WriteComm(0xEF),
WriteData(0x08),

WriteComm(0xFF),
WriteData(0x77),
WriteData(0x01),
WriteData(0x00),
WriteData(0x00),
WriteData(0x10),

WriteComm(0xC0),
WriteData(0xE9),
WriteData(0x03),

WriteComm(0xC1),
WriteData(0x11),
WriteData(0x02),

WriteComm(0xC2),
WriteData(0x07),
WriteData(0x06),

WriteComm(0xCC),
WriteData(0x18),

WriteComm(0xB0),
WriteData(0x00),
WriteData(0x0D),
WriteData(0x14),
WriteData(0x0D),
WriteData(0x10),
WriteData(0x05),
WriteData(0x02),
WriteData(0x08),
WriteData(0x08),
WriteData(0x1E),
WriteData(0x05),
WriteData(0x13),
WriteData(0x11),
WriteData(0xA3),
WriteData(0x29),
WriteData(0x18),

WriteComm(0xB1),
WriteData(0x00),
WriteData(0x0C),
WriteData(0x14),
WriteData(0x0C),
WriteData(0x10),
WriteData(0x05),
WriteData(0x03),
WriteData(0x08),
WriteData(0x07),
WriteData(0x20),
WriteData(0x05),
WriteData(0x13),
WriteData(0x11),
WriteData(0xA4),
WriteData(0x29),
WriteData(0x18),

WriteComm(0xFF),
WriteData(0x77),
WriteData(0x01),
WriteData(0x00),
WriteData(0x00),
WriteData(0x11),

WriteComm(0xB0),
WriteData(0x6C),

WriteComm(0xB1),
WriteData(0x4D),

WriteComm(0xB2),
WriteData(0x89),

WriteComm(0xB3),
WriteData(0x80),

WriteComm(0xB5),
WriteData(0x4E),

WriteComm(0xB7),
WriteData(0x85),

WriteComm(0xB8),
WriteData(0x20),

WriteComm(0xB9),
WriteData(0x00),
WriteData(0x13),

WriteComm(0xC0),
WriteData(0x09),

WriteComm(0xC1),
WriteData(0x78),

WriteComm(0xC2),
WriteData(0x78),

WriteComm(0xD0),
WriteData(0x88),

WriteComm(0xE0),
WriteData(0x00),
WriteData(0x00),
WriteData(0x02),

WriteComm(0xE1),
WriteData(0x08),
WriteData(0x00),
WriteData(0x0A),
WriteData(0x00),
WriteData(0x07),
WriteData(0x00),
WriteData(0x09),
WriteData(0x00),
WriteData(0x00),
WriteData(0x33),
WriteData(0x33),

WriteComm(0xE2),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),

WriteComm(0xE3),
WriteData(0x00),
WriteData(0x00),
WriteData(0x33),
WriteData(0x33),

WriteComm(0xE4),
WriteData(0x44),
WriteData(0x44),

WriteComm(0xE5),
WriteData(0x0E),
WriteData(0x60),
WriteData(0xA0),
WriteData(0xA0),
WriteData(0x10),
WriteData(0x60),
WriteData(0xA0),
WriteData(0xA0),
WriteData(0x0A),
WriteData(0x60),
WriteData(0xA0),
WriteData(0xA0),
WriteData(0x0C),
WriteData(0x60),
WriteData(0xA0),
WriteData(0xA0),

WriteComm(0xE6),
WriteData(0x00),
WriteData(0x00),
WriteData(0x33),
WriteData(0x33),

WriteComm(0xE7),
WriteData(0x44),
WriteData(0x44),

WriteComm(0xE8),
WriteData(0x0D),
WriteData(0x60),
WriteData(0xA0),
WriteData(0xA0),
WriteData(0x0F),
WriteData(0x60),
WriteData(0xA0),
WriteData(0xA0),
WriteData(0x09),
WriteData(0x60),
WriteData(0xA0),
WriteData(0xA0),
WriteData(0x0B),
WriteData(0x60),
WriteData(0xA0),
WriteData(0xA0),

WriteComm(0xEB),
WriteData(0x02),
WriteData(0x01),
WriteData(0xE4),
WriteData(0xE4),
WriteData(0x44),
WriteData(0x00),
WriteData(0x40),

WriteComm(0xEC),
WriteData(0x02),
WriteData(0x01),

WriteComm(0xED),
WriteData(0xAB),
WriteData(0x89),
WriteData(0x76),
WriteData(0x54),
WriteData(0x01),
WriteData(0xFF),
WriteData(0xFF),
WriteData(0xFF),
WriteData(0xFF),
WriteData(0xFF),
WriteData(0xFF),
WriteData(0x10),
WriteData(0x45),
WriteData(0x67),
WriteData(0x98),
WriteData(0xBA),

WriteComm(0xEF),
WriteData(0x08),
WriteData(0x08),
WriteData(0x08),
WriteData(0x45),
WriteData(0x3F),
WriteData(0x54),

WriteComm(0xFF),
WriteData(0x77),
WriteData(0x01),
WriteData(0x00),
WriteData(0x00),
WriteData(0x13),

WriteComm(0xE8),
WriteData(0x00),
WriteData(0x0E),

WriteComm(0xFF),
WriteData(0x77),
WriteData(0x01),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),

WriteComm(0x11),
WriteData(0x00),
Delay_ms (120), 
WriteComm(0xFF),
WriteData(0x77),
WriteData(0x01),
WriteData(0x00),
WriteData(0x00),
WriteData(0x13),

WriteComm(0xE8),
WriteData(0x00),
WriteData(0x0C),
Delay_ms (10), 
WriteComm(0xE8),
WriteData(0x00),
WriteData(0x00),

WriteComm(0xE6),
WriteData(0x16),
WriteData(0x7C),

WriteComm(0xFF),
WriteData(0x77),
WriteData(0x01),
WriteData(0x00),
WriteData(0x00),
WriteData(0x00),

/*
WriteComm(0xFF),//bist mode
WriteData(0x77),
WriteData(0x01),
WriteData(0x00),
WriteData(0x00),
WriteData(0x12),

WriteComm(0xD1),
WriteData(0x81),

WriteComm(0xD2),
WriteData(0x00),//bist mode 0x00列灰阶分两半 02红03绿04蓝05白边框06棋盘格07黑底白像素网格08RGB彩条
*/

WriteComm(0x29),
WriteData(0x00),
Delay_ms (20), 

LCD_INIT_TAB_END()

// LCD_UNINIT_TAB_BEGIN()
//     WriteComm(0x28),
//     Delay_ms(10),
// LCD_UNINIT_TA_ENDB()

LCD_DESC_BEGIN()

    .name 			= "RGB_ST7701S_LX50FWH40149",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_270,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_24,

    .pclk_div 		= LCD_PCLK_DIV(24000000),
    .clk_per_pixel 	= 1,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .pclk_edge      = LCD_PCLK_EDGE_FALLING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw 			= 4,
    .vbp 			= 20,
    .vfp 			= 20,

    .hlw 			= 10,
    .hbp 			= 50,
    .hfp 			= 50,

    LCD_SPI_DEFAULT(9),

    .data_mode 	= LCD_DATA_MODE0_24BIT_RGB888,

    .screen_w 		= 480,
    .screen_h 		= 854,

    .video_w  		= 854,
    .video_h  		= 480,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)

    .contrast   	= LCD_SATURATION_115,

    .brightness 	= 4,

    .saturation 	= LCD_SATURATION_145,

    .contra_index 	= 6,

    .gamma_index 	= {4, 4, 5},

    .asawtooth_index = {1, 1},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()
#endif
