/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_STRINGEX_H
#define UI_WIN_STRINGEX_H

typedef struct
{
	uiWidgetObj 	widget;
	STRING_DRAW_T	string;
	STRING_DRAW_T	stringSelect;
	u8				margin;
	u8				charH;
	u16 			rows;
	u16				curStrRows;
	u16				curSelRow;
	resID			*strPerRow;
}uiStringExObj;
/*******************************************************************************
* Function Name  : uiStringExCreateDirect
* Description    : uiStringExCreateDirect
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiStringExCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id);
/*******************************************************************************
* Function Name  : uiStringExCreate
* Description    : uiStringExCreate
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiStringExCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);
#endif
