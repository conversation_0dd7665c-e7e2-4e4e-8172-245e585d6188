/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : diskio.c
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : Low level disk I/O module skeleton for FatFs
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : get_fattime
* Description    : get_fattime
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
DWORD get_fattime (void)
{
    DATE_TIME_T *today;
	if(fs_exfunc.getCurTime)
	{
		today = (DATE_TIME_T*)fs_exfunc.getCurTime();
    /* Returns current time packed into a DWORD variable */
		return  ((U32)(today->year - 1980) << 25)    /* Year 2013 */  //can't  < 1980 or show time err!!!
				| ((U32)today->month << 21)          /* Month 7 */        //can't  = 0   or show time err!!!
				| ((U32)today->day << 16)            /* Mday 28 */    // can't = 0   or show time err!!!
				| ((U32)today->hour << 11)           /* Hour 0 */
				| ((U32)today->min << 5)             /* Min 0 */
				| ((U32)today->sec >> 1);            /* Sec 0 */
	}else{
		return  ((FF_NORTC_YEAR - 1980) << 25)    	/* Year 2022 */  //can't  < 1980 or show time err!!!
				| (FF_NORTC_MON << 21)          	/* Month 4 */        //can't  = 0   or show time err!!!
				| (FF_NORTC_MDAY << 16)         	/* Mday 1 */    // can't = 0   or show time err!!!
				| (0 << 11)           				/* Hour 0 */
				| (0 << 5)            			 	/* Min 0 */
				| (0 >> 1);            				/* Sec 0 */		
	}

}
/*******************************************************************************
* Function Name  : disk_status
* Description    : get a drive status
* Input          : BYTE pdrv :Physical drive nmuber to identify the drive
* Output         : none
* Return         : none
*******************************************************************************/
DSTATUS disk_status (
    BYTE pdrv       
)
{
	if(fs_exfunc.dev_sta)
	{
		if (fs_exfunc.dev_sta())
			return DISK_STA_OK;
	}
	return DISK_STA_NODISK;
}
/*******************************************************************************
* Function Name  : disk_initialize
* Description    : Inidialize a Drive 
* Input          : BYTE pdrv :Physical drive nmuber to identify the drive
* Output         : none
* Return         : none
*******************************************************************************/
DSTATUS disk_initialize (
    BYTE pdrv        /* Physical drive nmuber to identify the drive */
)
{
	if(fs_exfunc.dev_init)
	{
		if( fs_exfunc.dev_init(sd_api_GetBusWidth()) == 0)
			return DISK_STA_OK;
	}
	return DISK_STA_NOINIT;
}
/*******************************************************************************
* Function Name  : disk_read
* Description    : Read Sector(s)   
* Input          : BYTE pdrv  	: Physical drive nmuber to identify the drive 
				   BYTE *buff 	: Data buffer to store read data 
				   DWORD sector : Start sector in LBA 
				   UINT count	: Number of sectors to read 
* Output         : none
* Return         : none
*******************************************************************************/
DSTATUS disk_read (
    BYTE pdrv,        /* Physical drive nmuber to identify the drive */
    BYTE *buff,       /* Data buffer to store read data */
    DWORD sector,     /* Start sector in LBA */
    UINT count        /* Number of sectors to read */
)
{
	if(fs_exfunc.dev_read)
	{
		if(fs_exfunc.dev_read((void*)buff,sector,count) == 0)
		{
			return DISK_STA_OK;
		}
	}
	return DISK_STA_ERR;
}
/*******************************************************************************
* Function Name  : disk_write
* Description    : Write Sector(s)  
* Input          : BYTE pdrv  	: Physical drive nmuber to identify the drive 
				   const BYTE *buff : Data to be written 
				   DWORD sector : Start sector in LBA 
				   UINT count	: Number of sectors to write 
* Output         : none
* Return         : none
*******************************************************************************/
DSTATUS disk_write (
    BYTE pdrv,            /* Physical drive nmuber to identify the drive */
    const BYTE *buff,     /* Data to be written */
    DWORD sector,         /* Start sector in LBA */
    UINT count            /* Number of sectors to write */
)
{
	if(fs_exfunc.dev_write)
	{
		if(fs_exfunc.dev_write((void*)buff,sector,count) == 0)
		{
			return DISK_STA_OK;
		}
	}
	return DISK_STA_ERR;	
}
/*******************************************************************************
* Function Name  : disk_ioctl
* Description    : Miscellaneous Functions
* Input          : BYTE pdrv  	: Physical drive nmuber (0..) 
				   BYTE cmd 	: Control code 
				   void *buff 	: Buffer to send/receive control data
* Output         : none
* Return         : none
*******************************************************************************/
DSTATUS disk_ioctl (
    BYTE pdrv,        /* Physical drive nmuber (0..) */
    BYTE cmd,         /* Control code */
    void *buff        /* Buffer to send/receive control data */
)
{
    DSTATUS res = DISK_STA_OK;

    switch(cmd)
    {
        case CTRL_SYNC:
            break;

        case GET_SECTOR_COUNT:
            *(DWORD*)buff =  sd_api_Capacity();
            break;

        case GET_SECTOR_SIZE:
            *(WORD*)buff = 512;        //default set it 
            break;

        case GET_BLOCK_SIZE:
		#if FF_FMT_ALGIN
            *(DWORD*)buff = FF_FMT_ALGIN/512;        //default set it
        #else
            *(DWORD*)buff = 64;        //default set it
		#endif
            break;

        case CTRL_ERASE_SECTOR:
        default:
            res = DISK_STA_OK;            //not finish
            break;
    }

    return res;
}

