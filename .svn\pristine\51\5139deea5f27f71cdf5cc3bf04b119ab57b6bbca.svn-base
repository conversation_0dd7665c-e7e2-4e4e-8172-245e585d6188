/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef HAL_SPI_H
#define HAL_SPI_H






//----------------------spi flash command table-------------------------------
#define		SF_WRITE_ENABLE	       		0x06
#define		SF_WRITE_DISABLE	   		0x04
#define		SF_READ_STATUS	   			0x05
#define		SF_WRITE_STATUS	   			0x01
#define		SF_READ_DATA	   			0x03				// <= SF_SIZE
#define		SF_WRITE_DATA	   			0x02				// 256 BYTE
#define		SF_ERASE_BLOCK	   			0xD8				// 64K
#define		SF_ERASE_SECTOR	   			0x20				// 4k
#define		SF_READ_ID	       			0x9F				// 3 BYTE
#define		SF_ERASE_CHIP	   			0xC7
#define		SF_POWER_DOWN	   			0xB9
#define		SF_RELEASE_POWER_DOWN	   	0xAB
#define		SF_SECRR               	   	0x48
#define		SF_RUID               	   	0x4B




#define     SF_PAGE_SIZE      			256
#define     SF_SECTOR_SIZE   			4096
#define     SF_BLOCK_SIZE    			65536
#define     SF_STATUS_TIMEOUT   		(8000*2000)  // cpu loop

/*******************************************************************************
* Function Name  : hal_spiUpdata_led_show_init
* Description	 : hal_spiUpdata_led_show_init
* Input 		 : interval: ms
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_spiUpdata_led_show(u8 state);	
/*******************************************************************************
* Function Name  : hal_spiUpdata_led_show_init
* Description	 : hal_spiUpdata_led_show_init
* Input 		 : interval: ms
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_spiUpdata_led_show_init(u32 interval);
/*******************************************************************************
* Function Name  : hal_spiInit
* Description	 : spi initial for manual mode
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_spiUpdata_led_show_uinit(void);
/*******************************************************************************
* Function Name  : hal_spi0ExitAutoMode
* Description	 : exit auto mode 
* Input 		 : None
* Output		 : None
* Return		 : none
*******************************************************************************/
#define hal_spi0ExitAutoMode		hx330x_spi0ExitAutoMode
/*******************************************************************************
* Function Name  : hal_spiInit
* Description	 : spi initial
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_spiManualInit(void);
/*******************************************************************************
* Function Name  : hal_spiAutoModeInit
* Description	 : spi initial for auto mode
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_spiAutoModeInit(void);
/*******************************************************************************
* Function Name  : hal_spiModeSwitch
* Description	 : switch spi mode between auto mode and manual mode, spi write should switch to manual mode
* Input 		 : bool swc
* Output		 : None
* Return		 : s32 : >=0 success 
*******************************************************************************/
void hal_spiModeSwitch(u8 auto_mode, u8 ie_critial);


/*******************************************************************************
* Function Name  : hal_spiSendByte
* Description	 : spi send one byte data
* Input 		 : u8 : byte
* Output		 : None
* Return		 : none
*******************************************************************************/
//void hal_spiSendByte(u8 byte);
#define hal_spiSendByte					hx330x_spi0SendByte
/*******************************************************************************
* Function Name  : hal_spiSend
* Description	 : spi send data using DMA
* Input 		 : u32 addr : buffer addr
                    u32 len   : data length
* Output		 : None
* Return		 : none
*******************************************************************************/
//void hal_spiSend(u32 addr,u32 len);		
#define hal_spiSend						hx330x_spi0Send

/*******************************************************************************
* Function Name  : hal_spiRecvByte
* Description	 : spi recv one byte data
* Input 		 : none
* Output		 : None
* Return		 : u8 : 
*******************************************************************************/
//u8 hal_spiRecvByte(void);
#define hal_spiRecvByte					hx330x_spi0RecvByte

/*******************************************************************************
* Function Name  : hal_spiRecv
* Description	 : spi recv data using DMA
* Input 		 : u32 addr : buffer addr
                    u32 len   : data length
* Output		 : None
* Return		 : none
*******************************************************************************/
//void hal_spiRecv(u32 addr,u32 len);
#define hal_spiRecv						hx330x_spi0Recv
/*******************************************************************************
* Function Name  : hal_spiCSConfig
* Description	 : spi cs config
* Input 		 : u8 level : cs level
* Output		 : None
* Return		 : none
*******************************************************************************/
//void hal_spiCSConfig(u8 level)
#define hal_spiCSConfig					hx330x_spi0CS0Config
/*******************************************************************************
* Function Name  : hal_spiFlashReadID
* Description	 : spi flash read id
* Input 		 : none
* Output		 : None
* Return		 : u32 : id
*******************************************************************************/
u32 hal_spiFlashReadID(void);
/*******************************************************************************
* Function Name  : hal_spiFlashWriteEnable
* Description	 : spi flash write enable
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_spiFlashWriteEnable(void);
/*******************************************************************************
* Function Name  : hal_spiFlashWait
* Description	 : wait spi flash idle
* Input 		 : none
* Output		 : None
* Return		 : bool : true success 
                                  false   timeout
*******************************************************************************/
bool hal_spiFlashWait(void);
/*******************************************************************************
* Function Name  : hal_spiFlashReadPage
* Description	 : read spi flash one page
* Input 		 : u32 addr : spi flash addr in byte
				   u32 buffer : buffer addr
* Output		 : None
* Return		 : None 
*******************************************************************************/
void hal_spiFlashReadPage(u32 addr,u32 buffer);
/*******************************************************************************
* Function Name  : hal_spiFlashRead
* Description	 : read spi flash
* Input 		 : u32 addr : spi flash addr in byte
				   u32 buffer : buffer addr
				   u32 len   :  data length
* Output		 : None
* Return		 : None
*******************************************************************************/
void hal_spiFlashRead(u32 addr,u32 buffer,u32 len);
/*******************************************************************************
* Function Name  : hal_spiFlashWritePage
* Description	 : write spi flash one page
* Input 		 : u32 addr : spi flash addr in byte
				   u32 buffer : buffer addr
* Output		 : None
* Return		 : bool : true success 
*******************************************************************************/
bool hal_spiFlashWritePage(u32 addr,u32 buffer);
/*******************************************************************************
* Function Name  : hal_spiFlashWrite
* Description	 : write spi flash
* Input 		 : u32 addr : spi flash addr in byte
                    u32 buffer : buffer addr
                    u32 len   : data length
* Output		 : None
* Return		 : bool: true success 
*******************************************************************************/
bool hal_spiFlashWrite(u32 addr,u32 buffer,u32 len, u32 mode);
/*******************************************************************************
* Function Name  : hal_spiFlashWrite
* Description	 : write spi flash
* Input 		 : u32 addr : spi flash addr in byte
                    u32 buffer : buffer addr
                    u32 len   : data length
* Output		 : None
* Return		 : bool: true success 
*******************************************************************************/
bool hal_spiFlashWriteInManual(u32 addr,u32 buffer,u32 len);
/*******************************************************************************
* Function Name  : hal_spiFlashEraseSector
* Description	 : erase spi flash one sector
* Input 		 : u32 blockAddr : sector addr aglin in byte
* Output		 : None
* Return		 : bool : true success 
*******************************************************************************/
bool hal_spiFlashEraseSector(u32 sectorAddr, u32 mode);
/*******************************************************************************
* Function Name  : hal_spiFlashEraseBlock
* Description	 : erase spi flash one block
* Input 		 : u32 blockAddr : block addr aglin in byte
* Output		 : None
* Return		 : bool : true success 
*******************************************************************************/
bool hal_spiFlashEraseBlock(u32 blockAddr);
/*******************************************************************************
* Function Name  : hal_spiFlashEraseChip
* Description	 : erase spi flash all chip
* Input 		 : 
* Output		 : None
* Return		 : bool : true success 
*******************************************************************************/
bool hal_spiFlashEraseChip(void);
/*******************************************************************************
* Function Name  : hal_spiFlashReadUniqueID
* Description	 : read flash unique id
* Input 		 : u8 *buffer
* Output		 : None
* Return		 : None
*******************************************************************************/
void hal_spiFlashReadUniqueID(u8 buffer[8]);
/*******************************************************************************
* Function Name  : hal_spiFlashReadOTP
* Description	 : read spi flash otp
* Input 		 : u32 otp_addr
				   u8 * buffer
				   u32 size
* Output		 : None
* Return		 : None
*******************************************************************************/
void hal_spiFlashReadOTP(u32 otp_addr, u8 * buffer, u32 size);
/*******************************************************************************
* Function Name  : hal_spiFlashReadManual
* Description	 : read spi flash
* Input 		 : u32 addr : spi flash addr in byte
				   u32 buffer : buffer addr
				   u32 len   :  data length
* Output		 : None
* Return		 : None
*******************************************************************************/
void hal_spiFlashReadManual(u32 addr,u32 buffer,u32 len);

#endif

