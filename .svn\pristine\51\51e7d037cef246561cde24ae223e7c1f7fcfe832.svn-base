/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HAL_MJPDECODE_H
     #define HAL_MJPDECODE_H
/*******************************************************************************
* Function Name  : hal_mjpHeaderParse
* Description    : hal layer.mjpeg decode parse jpeg file 
* Input          : u8 * p_jpeg_header : buffer
* Output         : None
* Return         : None
*******************************************************************************/
int hal_mjpHeaderParse (u8 * p_jpeg_header);
/*******************************************************************************
* Function Name  : hal_mjpDecodeIsYUV422
* Description    : hal_mjpDecodeIsYUV422, after hal_mjpHeaderParse
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u8 hal_mjpDecodeIsYUV422(void);
/*******************************************************************************
* Function Name  : hal_mjpDecodeGetResolution
* Description    : hal layer.mjpeg decode get jpeg size
* Input          :  u16 width: out size width
                      u16 height:out size height
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeGetResolution(u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : hal_mjpDecodeSetResolution
* Description    : hal layer.mjpeg decode Set jpeg size
* Input          :  u16 width: out size width
                      u16 height:out size height
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeSetResolution(u16 width,u16 height);
/*******************************************************************************
* Function Name  : hal_mjpDecodePicture
* Description    : hal layer.mjpeg decode one picture
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv: out uvbuffer
				   u16 dst_width: out size width
				   u16 dst_height:out size height
* Output         : None
* Return         : true:success
*******************************************************************************/
bool hal_mjpDecodePicture(u8 * p_jpeg_start,u8 * p_out_y,u8 * p_out_uv,u16 dst_width,u16 dst_height);
/*******************************************************************************
* Function Name  : hal_mjpegDecodePicture_noisr
* Description    : hal layer.mjpeg decode one picture without isr
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv: out uvbuffer
				   u16 dst_width: out size width
				   u16 dst_height:out size height
* Output         : None
* Return         : true:success
*******************************************************************************/
bool hal_mjpegDecodePicture_noisr(u8 * p_jpeg_start,u8 * p_out_y,u8 * p_out_uv,u16 dst_width,u16 dst_height);
/*******************************************************************************
* Function Name  : hal_mjpegDecodePicture_packet
* Description    : hal layer.mjpeg decode one picture packet by packet without isr
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv: out uvbuffer
				   u16 dst_width: out size width
				   u16 dst_height:out size height
* Output         : None
* Return         : true:success
*******************************************************************************/
bool hal_mjpegDecodePicture_packet(u8 * p_jpeg_start,u8*pkg_end, u8 * p_out_y,u8 * p_out_uv,u16 dst_width,u16 dst_height);
/*******************************************************************************
* Function Name  : hal_mjpDecodeBusyCheck
* Description    : hal layer.mjpeg decode get status
* Input          :  
* Output         : None
* Return         : true : busy
*******************************************************************************/
bool hal_mjpDecodeBusyCheck(void);
/*******************************************************************************
* Function Name  : hal_mjpDecodeErrorCheck
* Description    : hal layer.mjpeg decode get error
* Input          :  
* Output         : None
* Return         : 0 : no error
*******************************************************************************/
u32 hal_mjpDecodeErrorCheck(void);
/*******************************************************************************
* Function Name  : hal_mjpDecodeStop
* Description    : hal layer.mjpeg decode stop
* Input          :  
* Output         : None
* Return         : 
*******************************************************************************/
void hal_mjpDecodeStop(void);
/*******************************************************************************
* Function Name  : hal_mjpDecodeReset
* Description    : hal layer.mjpeg decoder reset
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeReset(void);
/*******************************************************************************
* Function Name  : hal_mjpegDecodeParse
* Description    : hal layer.mjpeg decode stop
* Input          :  u8 * p_jpeg_start : buffer
                       u16 dst_width : out width
                       u16 dst_height: out height
* Output         : None
* Return         : 0 : success
                      <0 : fail
*******************************************************************************/
int hal_mjpDecodeParse(u8 * p_jpeg_start,u16 dst_width,u16 dst_height);
/*******************************************************************************
* Function Name  : hal_mjpegDecodeOneFrame
* Description    : hal layer.mjpeg decode one frame,video file using
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv:out uvbuffer
* Output         : None
* Return         : 0 : success
                      <0 : fail
*******************************************************************************/
void hal_mjpDecodeOneFrame(u8 * p_jpeg_start,u8 * p_out_y,u8 * p_out_uv);
/*******************************************************************************
* Function Name  : hal_mjpegDecodeOneFrame_ext
* Description    : hal layer.mjpeg decode one frame,
* Input          : u8 * p_jpeg_start : buffer
				   u8 * p_out_y : out ybuffer
				   u8 * p_out_uv:out uvbuffer
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeOneFrame_Ext(u8 * p_jpeg_start,u8 * p_out_y,u8 * p_out_uv);
/*******************************************************************************
* Function Name  : hal_mjpegDecodeRestart_Ext
* Description    : hal layer.mjpeg decode one frame,use last configration.
* Input          :  u8 * p_jpeg_start : buffer
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeRestart_Ext(u8 * p_jpeg_start);
/*******************************************************************************
* Function Name  : hal_mjpDecodeOneFrame_Fast
* Description    : quickly mjpeg decoder initial and kick decoder run
*                  NEED call hal_mjpDecodeInit before call this function
* Input          : u8 * p_jpeg_start : buffer
                   u8 * p_out_y : out ybuffer
                   u8 * p_out_uv: out uvbuffer
                   u16 dst_width: out size width
                   u16 dst_height:out size height
                   u16 stride :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecodeOneFrame_Fast(u8 * p_jpeg_start, u8* p_jpeg_end,
                                  u8 * p_out_y,u8 * p_out_uv,
                                  u16 dst_width,u16 dst_height,
                                  u16 stride);
/*******************************************************************************
* Function Name  : hal_mjpDecodeMiniSize
* Description    : hal layer.mjpeg decode mini size caculation
* Input          :  
* Output         : None
* Return         : < 0 fail
*******************************************************************************/
int hal_mjpDecodeMiniSize(u8 * p_jpeg_start,u16 *min_width,u16 *min_height);
/*******************************************************************************
* Function Name  : hal_mjpDecodeODma1Cfg
* Description    : config output dma1(no block-scaler),call this function before init jpeg-decoder
*                  hal_mjpDecodeStop will disable DMA1
* Input          : u32 y_addr
*                  u32 uv_addr
*                  bool dma_en
* Output         : none
* Return         : none
*******************************************************************************/
void hal_mjpDecodeODma1Cfg(u32 y_addr,u32 uv_addr,bool dma_en);
/*******************************************************************************
* Function Name  : hal_BackRecDecodeStatusCheck
* Description    : check back decode status per second, for back usensor online check
* Input          : NONE
* Output         : none
* Return         : TRUE: USENSOR ONLINE AND RECIEVE DATA SUCESS
*******************************************************************************/
bool hal_BackRecDecodeStatusCheck(void);
								  
/*******************************************************************************
* Function Name  : hal_mjpDecode_Down_process
* Description    : hal_mjpDecode_Down_process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecode_Down_process(u32 flg);
/*******************************************************************************
* Function Name  : hal_mjpDecode_Down_process
* Description    : hal_mjpDecode_Down_process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_mjpDecode_Callback(int flg);

#endif /*HAL_MJPDECODE_H*/

