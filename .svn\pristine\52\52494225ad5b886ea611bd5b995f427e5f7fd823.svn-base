/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuOptionWin.c"

ALIGNED(4) static menuItem* currentItem = NULL;
/*******************************************************************************
* Function Name  : getMenuOptionResInfor
* Description    : getMenuOptionResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : u32: 
*******************************************************************************/
static u32 getMenuOptionResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image 	= currentItem->pOption[item].image;
	if(str)
		*str	= currentItem->pOption[item].str;
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionKeyMsgOk
* Description    : menuOptionKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(currentItem)
		{
			user_config_set(currentItem->configId,currentItem->pOption[uiItemManageGetCurrentItem(winItem(handle,OPTION_SELECT_ID))].str);
			user_config_cfgSys(currentItem->configId);
			user_config_save();
		}
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionKeyMsgUp
* Description    : menuOptionKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,OPTION_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionKeyMsgDown
* Description    : menuOptionKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,OPTION_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionKeyMsgMenu
* Description    : menuOptionKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionKeyMsgMode
* Description    : menuOptionKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionSysMsgSD
* Description    : menuOptionSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionOpenWin
* Description    : menuOptionOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	u32 itemNum,config;
	if(parameNum < 1)
	{
		uiWinDestroy(&handle);
		return 0;
	}
	currentItem = (menuItem*)parame[0];
	deg_Printf("[WIN]menuOptionOpenWin\n");
	itemNum = uiItemManageSetHeightAvgGap(winItem(handle,OPTION_SELECT_ID),Rh(32));

	uiItemManageCreateItem(		winItem(handle,OPTION_SELECT_ID),uiItemCreateMenuOption,getMenuOptionResInfor,currentItem->optionSum);
	uiItemManageSetCharInfor(	winItem(handle,OPTION_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_Black);
	uiItemManageSetSelectColor(	winItem(handle,OPTION_SELECT_ID),R_ID_PALETTE_Gray_SUB_SEL);
	uiItemManageSetUnselectColor(winItem(handle,OPTION_SELECT_ID),R_ID_PALETTE_Gray_SUB_BG);
	config  = user_config_get(currentItem->configId);
	itemNum = 0;
	while(itemNum < currentItem->optionSum)
	{
		if(currentItem->pOption[itemNum].str == config)
			break;
		itemNum++;
	}
	if(itemNum >= currentItem->optionSum)
		itemNum = 0;
	uiItemManageSetCurItem(winItem(handle,OPTION_SELECT_ID),itemNum);
	uiWinSetResid(winItem(handle,OPTION_TITLE_ID),currentItem->str);
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionCloseWin
* Description    : menuOptionCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]menuOptionCloseWin\n");
	currentItem = NULL;
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionCloseWin
* Description    : menuOptionCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int menuOptionWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]menuOptionWinChildClose\n");
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionTouchWin
* Description    : menuOptionTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int menuOptionTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("menuOptionTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == OPTION_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuOptionTouchSlideOff
* Description    : menuOptionTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int menuOptionTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;
	if(parame[0] == TP_DIR_UP)
		uiItemManageNextPage(winItem(handle,OPTION_SELECT_ID));
	else if(parame[0] == TP_DIR_DOWN)
		uiItemManagePrePage(winItem(handle,OPTION_SELECT_ID));	
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	else if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	return 0;
}

ALIGNED(4) msgDealInfor menuOptionMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	menuOptionOpenWin},
	{SYS_CLOSE_WINDOW,	menuOptionCloseWin},
	{SYS_CHILE_COLSE,	menuOptionWinChildClose},
	{SYS_TOUCH_WINDOW,  menuOptionTouchWin},
	{SYS_TOUCH_SLIDE_OFF,menuOptionTouchSlideOff},

	{KEY_EVENT_PHOTO,		menuOptionKeyMsgOk},
	{KEY_EVENT_UP,		menuOptionKeyMsgDown},
	{KEY_EVENT_DOWN,	menuOptionKeyMsgUp},
	{KEY_EVENT_PLAYVIDEO,	menuOptionKeyMsgMenu},
	{KEY_EVENT_MODE,	menuOptionKeyMsgMode},
	{SYS_EVENT_SDC,		menuOptionSysMsgSD},
	{EVENT_MAX,			NULL},
};

WINDOW(menuOptionWindow,menuOptionMsgDeal,menuOptionWin)


