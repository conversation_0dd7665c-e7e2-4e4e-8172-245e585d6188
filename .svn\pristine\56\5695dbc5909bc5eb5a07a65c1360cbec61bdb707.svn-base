/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef HX330X_DMAUART_H
#define HX330X_DMAUART_H

//DMAUART STA
#define DMAUART_RX_KICK				(1<<0)
#define DMAUART_STA_OVERFLOW		(1<<1)
#define DMAUART_STA_TX_DONE			(1<<2)
#define DMAUART_STA_RX_DONE			(1<<3)
#define DMAUART_9BIT_DATA			(1<<5)

//DMAUART CON
#define DMAUART_AUTOMODE_ON			(1<<0)
#define DMAUART_OVERFLOW_IE			(1<<1)
#define DMAUART_RX_IE		    	(1<<2)
#define DMAUART_TX_IE		    	(1<<3)
#define DMAUART_ENABLE		    	(1<<4)
#define DMAUART_NIGHTBIT_MODE   	(1<<5) 
#define DMAUART_NIGHTBIT_DATA   	(1<<6)
#define DMAUART_STOPBIT_2       	(1<<7)

#define DMAUART_OVERFLOW_4BYTE		(0<<0)
#define DMAUART_OVERFLOW_8BYTE		(1<<0)
#define DMAUART_OVERFLOW_16BYTE		(2<<0)
#define DMAUART_OVERFLOW_32BYTE		(3<<0)

#define DMAUART_RX_LOOPCNT_16BYTE	(0<<0)
#define DMAUART_RX_LOOPCNT_32BYTE	(1<<0)
#define DMAUART_RX_LOOPCNT_64BYTE	(2<<0)
#define DMAUART_RX_LOOPCNT_128BYTE	(3<<0)
#define DMAUART_RX_LOOPCNT_256BYTE	(4<<0)
#define DMAUART_RX_LOOPCNT_512BYTE	(5<<0)
#define DMAUART_RX_LOOPCNT_1024BYTE	(6<<0)

/*******************************************************************************
* Function Name  : hx330x_uart1IRQHandler
* Description    : uart 1 IRQ handler
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_uart1IRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_DmaUartIOCfg
* Description    : DmaUart rx tx config
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_DmaUartIOCfg(u8 en);
/*******************************************************************************
* Function Name  : hx330x_uart0Init
* Description    : uart0 initial 
* Input          : u32 baudrate : uart0 baudrate
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_DmaUartInit(u32 baudrate);
/*******************************************************************************
* Function Name  : hx330x_DmaUart_CallbackRegister
* Description    : uart 0 IRQ handler
* Input          : void (*isr)(u8) : dmauart  isr callback
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_DmaUart_CallbackRegister(void (*isr)(u8 sta));
/*******************************************************************************
* Function Name  : hx330x_DmaUart_sta_cfg
* Description    : hx330x_DmaUart_sta_cfg
* Input          : u8 sta, u8 en
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_DmaUart_sta_cfg(u8 sta, u8 en);
/*******************************************************************************
* Function Name  : hx330x_DmaUart_con_cfg
* Description    : hx330x_DmaUart_con_cfg
* Input          : u8 sta, u8 en
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_DmaUart_con_cfg(u8 con, u8 en);
/*******************************************************************************
* Function Name  : hx330x_dmauart_sendbyte
* Description    : hx330x_dmauart_sendbyte 
* Input          : u8 data
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dmauart_sendbyte(u8 data);
/*******************************************************************************
* Function Name  : hx330x_dmauart_sendDmakick
* Description    : hx330x_dmauart_sendDmakick 
* Input          : u8 c
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dmauart_sendDmakick(u8 * buf, u32 len);
/*******************************************************************************
* Function Name  : hx330x_dmauart_sendDma
* Description    : hx330x_dmauart_sendDma 
* Input          : u8 c
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dmauart_sendDma(u8 * buf, u32 len);
/*******************************************************************************
* Function Name  : hx330x_dmauart_recvAutoDmakick
* Description    : hx330x_dmauart_recvAutoDmakick 
* Input          : u8* rx_buf
				   u8 rx_overflow_cnt:
				   u8 rx_loopcnt:
				   void (*isr)(u8 data) : rx callback
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dmauart_recvAutoDmakick(u8* rx_buf, u8 rx_overflow_cnt, u8 rx_loopcnt);
/*******************************************************************************
* Function Name  : hx330x_dmauart_recvBytekick
* Description    : hx330x_dmauart_recvBytekick
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_dmauart_recvBytekick(void);
/*******************************************************************************
* Function Name  : hx330x_dmauart_rxOutAdrGet
* Description    : hx330x_dmauart_rxOutAdrGet 
* Input          : NONE
* Output         : None
* Return         : RX FIFO OUT ADDR
*******************************************************************************/
u32 hx330x_dmauart_rxOutAdrGet(void);
/*******************************************************************************
* Function Name  : hx330x_dmauart_rxCntGet
* Description    : hx330x_dmauart_rxCntGet 
* Input          : NONE
* Output         : None
* Return         : RX FIFO OUT ADDR
*******************************************************************************/
u32 hx330x_dmauart_rxCntGet(void);
/*******************************************************************************
* Function Name  : hx330x_dmauart_rxFifoOut
* Description    : hx330x_dmauart_rxFifoOut 
* Input          : NONE
* Output         : None
* Return         : RX FIFO OUT ADDR
*******************************************************************************/
void hx330x_dmauart_rxFifoOut(u32 cnt);



#endif
