/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HAL_LCD_UILZO_H
#define HAL_LCD_UILZO_H


/*******************************************************************************
* Function Name  : hal_uiLzoStart
* Description    : config and start uilzo,and waitting finish
* Input          : u8 * src : src buffer
                   u32 src_len : src data len(aligne 4)
                   u8 * dst : dst buffer
                   u32 * dst_len : dst buffer len
                   u8 reverse_en : uilzo reverse (rotate 180)
* Output         : u32 * dst_len : uilzo data len
* Return         : true : success,false : timeout or overflow
*******************************************************************************/
//bool hal_uiLzoStart(u8 * src,u32 src_len,u8 * dst,u32 * dst_len,u8 reverse_en);
#define hal_uiLzoStart			hx330x_uiLzoStart
/*******************************************************************************
* Function Name  : hal_uiLzoInit
* Description    : init uilzo hal layer
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hal_uiLzoInit(void);
/*******************************************************************************
* Function Name  : hal_uiLzokick
* Description    : config and start uilzo
* Input          : lcdshow_frame_t * src,lcdshow_frame_t * dst
* Output         : none
* Return         : none
*******************************************************************************/
void hal_uiLzokick(lcdshow_frame_t * src,lcdshow_frame_t * dst);




#endif
