/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"



UNUSED ALIGNED(4) static const G_Sensor_OP_T  *gSensorPool[G_SENSOR_MAX]=
{
#if 1//(HAL_CFG_EN_DBG == 0)
	#if G_SENSOR_GMA301_SUPPORT
		&gma301,  // gma301
	#else
		NULL,
	#endif
	
	#if G_SENSOR_SC7A30E_SUPPORT
		&sc7a30e,  // gma301
	#else
		NULL,
	#endif
	
	#if G_SENSOR_DA380_SUPPORT
		&da380,  // gma301
	#else
		NULL,
	#endif	
#else
    NULL,
    NULL,
    NULL,
#endif
};

ALIGNED(4) static G_Sensor_OP_T *gSensor = NULL;
ALIGNED(4) G_Sensor_CTL_T	gsensor_ctl;

/*******************************************************************************
* Function Name  : gsensor_iic_enable
* Description    : enable iic for gsensor r/w
* Input          :none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void gsensor_iic_enable(void)
{
	hal_uartIOShare();
//	hal_gpioEPullSet(GPIO_PA,GPIO_PIN7,GPIO_PULLE_FLOATING);
    hal_iic1Init();	
}
/*******************************************************************************
* Function Name  : gsensor_iic_disable
* Description    : disable gsensor r/w iic
* Input          :none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void gsensor_iic_disable(void)
{
	hal_iic1Uninit();  // release iic1
}


/*******************************************************************************
* Function Name  : gSensorUninit
* Description    : ubinitial gsensor.disable gsensor
* Input          :none
* Output         : none                                            
* Return         : 
*******************************************************************************/
UNUSED static int gSensorUninit(void)
{
	if(gSensor == NULL || gSensor->ioctrl == NULL)
		return -1;
	return gSensor->ioctrl(G_IOCTRL_DISABLE,0);
}
/*******************************************************************************
* Function Name  : gSensorWakeUpGet
* Description    : get wakeup state
* Input          :none
* Output         : none                                            
* Return         : int : 0->no wakeup
                             1->gsensor waked
*******************************************************************************/
static int gSensorWakeUpGet(void)
{
	if(gSensor == NULL)
		return -1;
	
	return gsensor_ctl.activeFlag;
}
/*******************************************************************************
* Function Name  : gSensorWakeupSet
* Description    : set wakeup state
* Input          :int level : wake up level
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
static int gSensorWakeupSet(int level)
{
	if(gSensor == NULL || gSensor->ioctrl == NULL)
		return -1;

	return gSensor->ioctrl(G_IOCTRL_WAKEUPSET,level);
}
/*******************************************************************************
* Function Name  : gSensorActiveGet
* Description    : get active
* Input          :none
* Output         : none                                            
* Return         : int : 0->no active
                             1->gsensor actived
*******************************************************************************/
static int gSensorActiveGet(void)
{
	if(gSensor == NULL || gSensor->ioctrl == NULL)
		return -1;

	return gSensor->ioctrl(G_IOCTRL_ACTIVEGET,0);
}
/*******************************************************************************
* Function Name  : gSensorActiveSet
* Description    : set active state
* Input          :int level : active level
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
static int gSensorActiveSet(int level)
{
	if(gSensor == NULL || gSensor->ioctrl == NULL)
		return -1;
	
	return gSensor->ioctrl(G_IOCTRL_ACTIVESET,level);	
}
/*******************************************************************************
* Function Name  : gSensorRead
* Description    : read gsensor
* Input          :
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
UNUSED static int gSensorRead(int *x,int *y,int *z)
{
	if(gSensor == NULL || gSensor->readxyz == NULL)
		return -1;

	gSensor->readxyz(x,y,z);
	return 0;
}
/*******************************************************************************
* Function Name  : gSensorMircoMotionWait
* Description    : wait gsensor stable
* Input          :
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
static int gSensorMircoMotionWait(int level)
{
    volatile int cnt=0;

	if(gSensor == NULL || gSensor->ioctrl == NULL)
		return -1;


	while(1)
	{
		if(gSensor->ioctrl(G_IOCTRL_ACTIVECHK,level))
		{
			cnt = 0;
		}
		else
			cnt++;
		if(cnt>=10)
			return 1;
	}
	
}
/*******************************************************************************
* Function Name  : gSensorGetName
* Description    : get gsensor name
* Input          :
* Output         : none                                            
* Return         : char *
*******************************************************************************/
char *gSensorGetName(void)
{
	if(gSensor == NULL)
		return ((char *)("NULL"));

	return gSensor->name;
}

/*******************************************************************************
* Function Name  : dev_gSensor_Init
* Description    : initial gsensor
* Input          : none
* Output         : none                                            
* Return         : wake up state
*******************************************************************************/
int dev_gSensor_Init(void)
{

	if(hardware_setup.gsensor_en)
	{
		int i,ret;
		hal_vddGSENEnable(1);
		gsensor_ctl.activeFlag = 0;
	//	hal_sysDelayMS(10);
	//----------------------auto check current gsensor type----------------
		for(i=0;i<G_SENSOR_MAX;i++)
		{
			
			if(gSensorPool[i] && gSensorPool[i]->init)
			{
				deg_Printf("gsensor : check %s\n",gSensorPool[i]->name);
				ret = gSensorPool[i]->init();
				if(ret>=0)
					break;
				
			}
		}
		if(i>=G_SENSOR_MAX)
		{
			deg_Printf("gsensor : initial fail.can not find support gsensor type.\n");
			gSensor = NULL;
			//hx330x_VDDGSENEnable(0);
			return -1;
		}
		deg_Printf("gsensor : initial ok :%s.parkFlag=%d\n",gSensorPool[i]->name,gsensor_ctl.activeFlag);
		gSensor = (G_Sensor_OP_T *)gSensorPool[i];  // save current gsensor type
		return ret;
	
	}
	else
	{
		gSensor = NULL;
		return -1;		
	}
}
/*******************************************************************************
* Function Name  : dev_gSensor_ioctrl
* Description    : dev_gSensor_ioctrl
* Input          : none
* Output         : none                                            
* Return         : wake up state
*******************************************************************************/
int dev_gSensor_ioctrl(INT32U op,INT32U para)
{
	int value = 0;
	switch(op)
	{
		case DEV_GSENSOR_LOCK_READ:
			value = gSensorActiveGet();
			if(para)
				*(INT32U *)para = value;
			break;
		case DEV_GSENSOR_PARK_READ:
			value = gSensorWakeUpGet();
			if(para)
				*(INT32U *)para = value;
			break;	
		case DEV_GSENSOR_NAME_READ:
			if(para)
				strcpy((char *)para,gSensorGetName());
			break;
		case DEV_GSENSOR_MOTION_STABLE:
			gSensorMircoMotionWait(para);
			break;
		case DEV_GSENSOR_LOCK_WRITE:
			return gSensorActiveSet(para);
		case DEV_GSENSOR_PARK_WRITE:
			return gSensorWakeupSet(para);
	}

	return value;
}















