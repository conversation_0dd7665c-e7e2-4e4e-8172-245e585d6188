/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_ILI9335

#define CMD(x)    LCD_CMD_MCU_CMD16(x)
#define DAT(x)    LCD_CMD_MCU_DAT16(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
    CMD(0x00ec),
    DAT(0x1e8f),

    CMD(0x0001),
    DAT(0x0100),

    C<PERSON>(0x0002),
    DAT(0x0200),

    CMD(0x0003),
    DAT(0x1030),

    DLY(10),

    CMD(0x0008),
    DAT(0x0A02),

    CMD(0x0009),
    DAT(0x0000),

    CMD(0x000a),
    DAT(0x0008), //enable TE

    CMD(0x000d),
    DAT(0x0000),

    CMD(0x000f),
    DAT(0x0000),

    CMD(0x0060),
    DAT(0x2700),

    CMD(0x0061),
    DAT(0x0001),

    CMD(0x006a),
    DAT(0x0000),

    DLY(10),

    CMD(0x0010),
    DAT(0x1690),

    CMD(0x0011),
    DAT(0x0227),

    DLY(10),

    CMD(0x0012),
    DAT(0x000d),

    DLY(10),

    CMD(0x0021),
    DAT(0x1600),

    CMD(0x0029),
    DAT(0x0018),

    CMD(0x002b),
    DAT(0x000a),

    DLY(10),

    CMD(0x0020),
    DAT(0x0000),

    CMD(0x0021),
    DAT(0x0000),
        //============Gamma============
    CMD(0x0030),
    DAT(0x0403),

    CMD(0x0031),
    DAT(0x0007),

    CMD(0x0032),
    DAT(0x0404),

    CMD(0x0035),
    DAT(0x0002),

    CMD(0x0036),
    DAT(0x000f),

    CMD(0x0037),
    DAT(0x0003),

    CMD(0x0038),
    DAT(0x0000),

    CMD(0x0039),
    DAT(0x0302),

    CMD(0x003c),
    DAT(0x0200),

    CMD(0x003d),
    DAT(0x0f00),
        //=============================
    // set RAM address
    CMD(0x0050),
    DAT(0x0000),

    CMD(0x0051),
    DAT(0x00ef),

    CMD(0x0052),
    DAT(0x0000),

    CMD(0x0053),
    DAT(0x013f),

    DLY(10),

    CMD(0x0080),
    DAT(0x0000),

    CMD(0x0081),
    DAT(0x0000),

    CMD(0x0082),
    DAT(0x0000),

    CMD(0x0083),
    DAT(0x0000),

    CMD(0x0084),
    DAT(0x0000),

    CMD(0x0085),
    DAT(0x0000),

    CMD(0x0090),
    DAT(0x0010),

    CMD(0x0091),
    DAT(0x0600),

    //display on
    CMD(0x0007),
    DAT(0x0133),

    CMD(0x0022),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name 			= "MCU_ili9335",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,
    .te_mode 		= LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(320*240*2*30),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 240,
    .screen_h 		= 320,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif
