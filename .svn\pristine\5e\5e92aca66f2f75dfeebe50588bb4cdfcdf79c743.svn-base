/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_ST7789

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
    //Sleep Out
    CMD(0x0011),
    DLY(120),  //Delay 120ms

    //TEON,and Not consist of HBlanking
    CMD(0x0035),
    DAT(0x0000),

    //Set Tear Scanline,when display 0th line,the TE output
    CMD(0x0044),
    DAT(0x0000),
    DAT(0x0000),

    //65K-color RGB interface,and 16bit/pixel
    CMD(0x003A),
    DAT(0x0055),

    //Porch Setting(set VBlank),back porch 8,front porch 8
    CMD(0x00B2),
    DAT(0x0008),
    DAT(0x0008),
    DAT(0x0000),
    DAT(0x0022),
    DAT(0x0022),

    //Gate Control
    CMD(0x00B7),
    DAT(0x0035),

    //-------------------Power Setting------------------
    CMD(0x00BB),
    DAT(0x0027),

    CMD(0x00C0),
    DAT(0x002C),

    CMD(0x00C2),
    DAT(0x0001),

    CMD(0x00C6),
    DAT(0x0018),

    CMD(0x00C6),
    DAT(0x000F),

    CMD(0x00C4),
    DAT(0x0020),

    CMD(0x00C3),
    DAT(0x0010),

    CMD(0x00CA),
    DAT(0x000F),

    CMD(0x00D0),
    DAT(0x00A4),
    DAT(0x00A1),

    //-------------------Gamma Setting--------------------
    CMD(0x00E0),
    DAT(0x00D0),
    DAT(0x0000),
    DAT(0x0003),
    DAT(0x0009),
    DAT(0x0013),
    DAT(0x001C),
    DAT(0x003A),
    DAT(0x0055),
    DAT(0x0048),
    DAT(0x0018),
    DAT(0x0012),
    DAT(0x000E),
    DAT(0x0020),
    DAT(0x001E),

    CMD(0x00E1),
    DAT(0x00D0),
    DAT(0x0000),
    DAT(0x0003),
    DAT(0x0009),
    DAT(0x0005),
    DAT(0x0025),
    DAT(0x003A),
    DAT(0x0055),
    DAT(0x0050),
    DAT(0x003D),
    DAT(0x001C),
    DAT(0x001D),
    DAT(0x0021),
    DAT(0x001E),

    //Memory Data Access Control
    //Write Order:
    //    X_Start=0,X_End=239,Y_Start=0,Y_End=319
    //    X_Start->X-End,then Y++ .... -> (X_End,Y_End)
    //LCD Refresh Order:
    //    Top to Bottom
    //    Left to Right
    CMD(0x0036),
    DAT(0x0000),

    //Display on
    CMD(0x0029),

    //Memory Write
    CMD(0x002C),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name 			= "MCU_st7789",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,
    .te_mode 		= LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(320*240*2*60),
    .clk_per_pixel 	= 2,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w 		= 240,
    .screen_h 		= 320,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)

    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()



#endif


