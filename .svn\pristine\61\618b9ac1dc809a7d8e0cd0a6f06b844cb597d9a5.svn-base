/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiStringExProc
* Description    : uiStringExProc
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiStringExProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiStringExObj* pstringEx;
	uiWinObj* pWin;
	uiStrInfo* stringInfor;
	uiStringExInfo* stringExInfor;
	uiRect		showRegion;
	u32 i, startRow, endRow;

	if(uiWidgetProc(msg))
		return;	
	hWin 		= msg->curWin;
	pstringEx	= (uiStringExObj*)uiHandleToPtr(hWin);
	pWin		= &(pstringEx->widget.win);
	startRow	= 0;
	endRow	 	= pstringEx->rows;
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			return;
		case MSG_WIN_DESTROY:
			uiWinHeapFree((winHandle)pstringEx->strPerRow);
			//deg_msg("stringEx Free\n");
			return;
		case MSG_WIN_PAINT:
			if(pstringEx->string.id != INVALID_RES_ID)
			{
				if(pstringEx->curStrRows == 0)
					return;
				if(pstringEx->curStrRows < pstringEx->rows)
				{
					startRow 	= (pstringEx->rows - pstringEx->curStrRows)/2;
					endRow		= startRow + pstringEx->curStrRows;
				}
				showRegion.x0 	= pWin->rect.x0;
				showRegion.x1	= pWin->rect.x1;
				showRegion.y0	= pWin->rect.y0 + pstringEx->margin + startRow * pstringEx->charH;
				showRegion.y1	= showRegion.y0 + (endRow - startRow)*pstringEx->charH - 1;
				if(uiWinOverlapCmp(&showRegion,(uiRect*)(msg->para.p)) < 0)
					return;
				for(i = startRow; i < endRow; i++)
				{
					showRegion.y0	= pWin->rect.y0 + pstringEx->margin + i * pstringEx->charH;
					showRegion.y1	= showRegion.y0 + pstringEx->charH - 1;
					//if(uiWinOverlapCmp(&showRegion,(uiRect*)(msg->para.p)) < 0) //这里应该不用
					//	continue;				
					if(pstringEx->strPerRow[i - startRow])
					{
						if(pstringEx->curSelRow == (i - startRow))
						{
							pstringEx->stringSelect.id = pstringEx->strPerRow[i - startRow];
							uiWinDrawString(&showRegion,(uiRect*)(msg->para.p),&pstringEx->stringSelect);
						}else
						{
							pstringEx->string.id	= pstringEx->strPerRow[i - startRow];
							uiWinDrawString(&showRegion,(uiRect*)(msg->para.p),&pstringEx->string);
						}
					}
				}
			}
			return;
		case MSG_WIN_UPDATE_RESID:
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIDGET_RES_SUM_GET:
			msg->para.v = pstringEx->rows;
			return;
		case MSG_WIDGET_RES_SUM_SET:
			pstringEx->curStrRows = msg->para.v;
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);			
			}
			return;
		case MSG_WIDGET_SET_RESID_BY_NUM:
			stringExInfor = (uiStringExInfo*)msg->para.p;
			if(pstringEx->strPerRow && stringExInfor->num < pstringEx->rows)
			{
				pstringEx->strPerRow[stringExInfor->num] = stringExInfor->str;
				if(stringExInfor->select)
					pstringEx->curSelRow = stringExInfor->num;
			}
			return;
		case MSG_WIN_CHANGE_RESID:
			if( !RES_ID_IS_RAM(pstringEx->string.id) && pstringEx->string.id == msg->para.v)
				return;
			pstringEx->string.id 		= msg->para.v;
			pstringEx->stringSelect.id 	= msg->para.v;
			pstringEx->curStrRows		= 0;
			if(RES_ID_IS_RAM(pstringEx->string.id) && pstringEx->strPerRow)
			{
				pstringEx->strPerRow[0] = pstringEx->string.id;
				for(i = 1; i < pstringEx->rows; i++)
				{
					pstringEx->strPerRow[i] = (resID)uiWinStringExGetNext((char*)pstringEx->strPerRow[i-1]);
					if(pstringEx->strPerRow[i] == 0)
						break;
				}
				pstringEx->curStrRows = i;
			}
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIN_CHANGE_STRINFOR:
			stringInfor = (uiStrInfo*)(msg->para.p);
			pstringEx->string.strAlign 			= stringInfor->strAlign;
			pstringEx->string.font				= stringInfor->font;
			pstringEx->string.fontColor 		= stringInfor->fontColor;
			//pstringEx->stringSelect.strAlign 	= stringInfor->strAlign;
			//pstringEx->stringSelect.font		= stringInfor->font;
			//pstringEx->stringSelect.fontColor = stringInfor->fontColor;
			return;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			((touchInfor *)(msg->para.p))->touchWin		= pWin->parent;
			((touchInfor *)(msg->para.p))->touchHandle	= hWin;
			((touchInfor *)(msg->para.p))->touchID		= pstringEx->widget.id;
			((touchInfor *)(msg->para.p))->touchItem	= 0;
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiStringExCreateDirect
* Description    : uiStringExCreateDirect
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiStringExCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id)
{
	winHandle 		hstringEx;
	uiStringExObj  *pstringEx;
	hstringEx		= uiWinCreate(x0,y0,width,height,parent,uiStringExProc,sizeof(uiStringExObj),WIN_WIDGET|style);
	if(hstringEx != INVALID_HANDLE)
	{
		
		pstringEx = (uiStringExObj*)uiHandleToPtr(hstringEx);
		
		pstringEx->string.id			    = INVALID_RES_ID;
		pstringEx->string.style		    	= style;
		pstringEx->string.strAlign 	    	= ALIGNMENT_CENTER;
		pstringEx->string.font		    	= DEFAULT_FONT;
		pstringEx->string.fontColor 	    = DEFAULT_COLOR;
		pstringEx->string.bgColor 	    	= INVALID_COLOR;
		pstringEx->string.rimColor 	    	= INVALID_COLOR;
		pstringEx->stringSelect.id			= INVALID_RES_ID;
		pstringEx->stringSelect.style		= style;
		pstringEx->stringSelect.strAlign 	= ALIGNMENT_CENTER;
		pstringEx->stringSelect.font		= DEFAULT_FONT;
		pstringEx->stringSelect.fontColor 	= DEFAULT_COLOR;
		pstringEx->stringSelect.bgColor 	= INVALID_COLOR;
		pstringEx->stringSelect.rimColor 	= INVALID_COLOR;		
		uiWidgetSetId(hstringEx,id);
		uiWinSetbgColor(hstringEx, INVALID_COLOR);
	}
	return hstringEx;
}
/*******************************************************************************
* Function Name  : uiStringExCreate
* Description    : uiStringExCreate
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiStringExCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle hstringEx;
	uiStringExObj* pstringEx;
	uiRect		winRect;
	hstringEx = uiWinCreate(infor->x0,infor->y0,infor->width,infor->height,parent,uiStringExProc,sizeof(uiStringExObj),WIN_WIDGET|WIN_TOUCH_SUPPORT|infor->style);
	
	if(hstringEx!=INVALID_HANDLE)
	{
		pstringEx	= (uiStringExObj*)uiHandleToPtr(hstringEx);

		pstringEx->string.id				= infor->str;
		pstringEx->string.style				= infor->style;
		pstringEx->string.strAlign 			= infor->strAlign;
		pstringEx->string.font				= infor->font;
		pstringEx->string.fontColor 		= infor->fontColor;
		pstringEx->string.bgColor 			= infor->bgColor;
		pstringEx->string.rimColor 			= infor->rimColor;
		pstringEx->stringSelect.id			= infor->str;
		pstringEx->stringSelect.style		= infor->style;
		pstringEx->stringSelect.strAlign 	= infor->strAlignS;
		pstringEx->stringSelect.font		= infor->fontS;
		pstringEx->stringSelect.fontColor 	= infor->fontColorS;
		pstringEx->stringSelect.bgColor 	= infor->bgColorS;
		pstringEx->stringSelect.rimColor 	= infor->rimColorS;	
		pstringEx->charH					= infor->imageAlign;
		uiWinGetPos(hstringEx,&winRect);
		pstringEx->rows						= (winRect.y1 - winRect.y0 + 1)/pstringEx->charH;
		pstringEx->margin					= ((winRect.y1 - winRect.y0 + 1)%pstringEx->charH)/2;
		pstringEx->curStrRows				= uiWinStringExRowCal((char*)infor->str);
		pstringEx->curSelRow				= 0xffff;
		pstringEx->strPerRow				= (resID*)uiWinHeapMalloc(sizeof(resID) * pstringEx->rows);
		if(RES_ID_IS_RAM(pstringEx->string.id) && pstringEx->strPerRow)
		{
			u16 i;
			pstringEx->strPerRow[0] = pstringEx->string.id;
			for(i = 1; i < pstringEx->rows; i++)
			{
				pstringEx->strPerRow[i] = (resID)uiWinStringExGetNext((char*)pstringEx->strPerRow[i-1]);
				if(pstringEx->strPerRow[i] == 0)
					break;
			}
			pstringEx->curStrRows = i;
		}
		uiWidgetSetId(hstringEx,infor->id);
		uiWinSetbgColor(hstringEx, infor->bgColor);
		
	}	
	return hstringEx;
}
