/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

#define ICNT81_IIC_ADDR 	0x90
#define ICNT81_IIC_ADDR_NUM	2		//2bytes 地址
#define ICNT81_W     		854
#define ICNT81_H     		480
/*******************************************************************************
* Function Name  : tp_icnt81_init
* Description	 : tp_icnt81_init
* Input		     : NONE
* Output		 : None
* Return		 : none
*******************************************************************************/
static void tp_icnt81_init(void)
{
	u8 buff[2];
	tp_iic_config(ICNT81_IIC_ADDR,ICNT81_IIC_ADDR_NUM);
	
	//hal_gpioInit(TP_INT_CH,TP_INT_PIN,GPIO_INPUT,GPIO_PULL_UP);
	if(tp_iic_read(0x000c, buff, 2)>=0)
	{
		deg_Printf("ICNT81 version:0x%x\n",((u16)buff[0]<<8)|buff[1]);
	}
}
/*******************************************************************************
* Function Name  : tp_icnt81_Match
* Description	 : tp_icnt81_Match
* Input		     : NONE
* Output		 : None
* Return		 : 1: match, 0: no match
*******************************************************************************/
static int tp_icnt81_Match(void)
{
	u8 data = 0;
	u8 retry_cnt = 3;
	tp_iic_config(ICNT81_IIC_ADDR,ICNT81_IIC_ADDR_NUM);
	while(1)
	{
		if(retry_cnt > 0)
		{
			retry_cnt--;
		}else
		{
			break;
		}
		if(tp_iic_read(0xa,&data,1) >= 0)
		{
			if(data == 0x81)
				return 1;
		}

	}
	return 0;
}
/*******************************************************************************
* Function Name  : tp_icnt81_getPoint
* Description	 : tp_icnt81_getPoint
* Input		     : NONE
* Output		 : None
* Return		 : -1: ERROR , 0: NONE TOUCH, 1: TOUCH
*******************************************************************************/
int tp_icnt81_getPoint(TP_POINT_T* p)
{
	u8 buf[5];
	int ret;
	//if(hal_gpioRead(TP_INT_CH,TP_INT_PIN))
	//	return 0;
	ret = tp_iic_read(0x1008,buf,1);
	if(ret < 0)
		return -1;
	if(buf[0] == 4)
		return 0;
	ret = tp_iic_read(0x1000,buf,2);
	if(ret < 0)
		return -1;
	if(buf[1] != 1)
	{
		//deg_Printf("do not surport multi-finger touch\n");
		return -1;
	}
	ret = tp_iic_read(0x1003,buf,4);
	if(ret < 0)
		return -1;
	p->x = ((u16)buf[1]<<8)|buf[0];
	if(p->x > ICNT81_W)
	{
		deg_Printf("icnt81 read x[%d] error\n",p->x);
		return -1;
	}
	p->y = ((u16)buf[3]<<8)|buf[2];
	if(p->y > ICNT81_H)
	{
		deg_Printf("icnt81 read y[%d] error\n",p->y);
		return -1;
	}
	p->x = ICNT81_W - p->x;
	p->y = ICNT81_H - p->y;
	return 1;
}

ALIGNED(4) const TOUCHPANEL_OP_T tp_icnt81 =
{
	.name 		= "TP ICNT81",
	.tp_width 	= ICNT81_W,
	.tp_height 	= ICNT81_H,
	.init		= tp_icnt81_init,
	.match		= tp_icnt81_Match,
	.getPoint	= tp_icnt81_getPoint,
};