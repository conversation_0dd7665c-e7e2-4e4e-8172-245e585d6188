/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"


#define NS2009_I2C_ADDR		0x90// 8bit
#define NS2009_IIC_ADDR_NUM	1	//1bytes 地址

#define NS2009_I2C_READ  	0x91// 8bit

#define NS2009_XY_SWAP  	1
#define NS2009_X_INV  		0
#define NS2009_Y_INV  		1
#define NS2009_HOR_RES     	320
#define NS2009_VER_RES     	240
#define NS2009_X_MIN       	300
#define NS2009_Y_MIN       	300
#define NS2009_X_MAX       	3800
#define NS2009_Y_MAX       	3800
#define NS2009_AVG         	3
#define NS2009_INV         	0

ALIGNED(4) static u16 ns2009_avg_buf_x[NS2009_AVG];
ALIGNED(4) static u16 ns2009_avg_buf_y[NS2009_AVG];
ALIGNED(4) static u8  ns2009_avg_last;

enum
{
    NS2009_LOW_POWER_READ_X = 0xc0,
    NS2009_LOW_POWER_READ_Y = 0xd0,
    NS2009_LOW_POWER_READ_Z1 = 0xe0,
    NS2009_LOW_POWER_READ_Z2 = 0xf0,
};

/*******************************************************************************
* Function Name  : tp_ns2009_corr
* Description	 : tp_ns2009_corr
* Input		     : NONE
* Output		 : None
* Return		 : none
*******************************************************************************/
static void tp_ns2009_corr(int * x, int * y)
{
#if NS2009_XY_SWAP != 0
    u32 swap_tmp;
    swap_tmp = *x;
    *x = *y;
    *y = swap_tmp;
#endif

    if((*x) > NS2009_X_MIN)
		(*x) -= NS2009_X_MIN;
    else	
		(*x) = 0;

    if((*y) > NS2009_Y_MIN)
		(*y) -= NS2009_Y_MIN;
    else
		(*y) = 0;

    (*x) = (u32)((u32)(*x) * NS2009_HOR_RES) /
           (NS2009_X_MAX - NS2009_X_MIN);

    (*y) = (u32)((u32)(*y) * NS2009_VER_RES) /
           (NS2009_Y_MAX - NS2009_Y_MIN);

#if NS2009_X_INV != 0
    (*x) =  NS2009_HOR_RES - (*x);
#endif

#if NS2009_Y_INV != 0
    (*y) =  NS2009_VER_RES - (*y);
#endif
}
/*******************************************************************************
* Function Name  : tp_ns2009_avg
* Description	 : tp_ns2009_avg
* Input		     : NONE
* Output		 : None
* Return		 : none
*******************************************************************************/
static void tp_ns2009_avg(int * x, int * y)
{
    /*Shift out the oldest data*/
    u8 i;
	static u32 diff;
    for(i = NS2009_AVG - 1; i > 0 ; i--) {
        ns2009_avg_buf_x[i] = ns2009_avg_buf_x[i - 1];
        ns2009_avg_buf_y[i] = ns2009_avg_buf_y[i - 1];
    }

    /*Insert the new point*/
    ns2009_avg_buf_x[0] = *x;
	diff = hx330x_dif_abs(ns2009_avg_buf_x[0], ns2009_avg_buf_x[1]);
	ns2009_avg_buf_x[0] = (diff < 5) ? ns2009_avg_buf_x[1]: ns2009_avg_buf_x[0];// inorge small difference,or take average of them, for stable

    ns2009_avg_buf_y[0] = *y;
	diff = hx330x_dif_abs(ns2009_avg_buf_y[0], ns2009_avg_buf_y[1]);
    ns2009_avg_buf_y[0] = (diff < 5) ? ns2009_avg_buf_y[1] : ns2009_avg_buf_y[0];

    if(ns2009_avg_last < NS2009_AVG) ns2009_avg_last++;

    /*Sum the x and y coordinates*/
    s32 x_sum = 0;
    s32 y_sum = 0;
    for(i = 0; i < ns2009_avg_last ; i++) {
        x_sum += ns2009_avg_buf_x[i];
        y_sum += ns2009_avg_buf_y[i];
    }

    /*Normalize the sums*/
    (*x) = (s32)x_sum / ns2009_avg_last;
    (*y) = (s32)y_sum / ns2009_avg_last;
}

/*******************************************************************************
* Function Name  : tp_ns2009_init
* Description	 : tp_ns2009_init
* Input		     : NONE
* Output		 : None
* Return		 : none
*******************************************************************************/
static void tp_ns2009_init(void)
{
	tp_iic_config(NS2009_I2C_ADDR,NS2009_IIC_ADDR_NUM);
	
}
/*******************************************************************************
* Function Name  : tp_ns2009_read
* Description	 : tp_ns2009_read
* Input		     : NONE
* Output		 : None
* Return		 : none
*******************************************************************************/
static int tp_ns2009_read(u8 cmd, int *val)
{
	u8 buf[2];
	if(tp_iic_read(cmd, buf, 2) < 0)
	{
		return -1;
	}
	if (val)
		*val = (buf[0] << 4) | (buf[1] >> 4);
	return 0;
}
/*******************************************************************************
* Function Name  : tp_ns2009Match
* Description	 : tp_ns2009Match
* Input		     : NONE
* Output		 : None
* Return		 : none
*******************************************************************************/
int tp_ns2009_Match(void)
{
	deg_Printf("%s\n",__FUNCTION__);
	return 1;
}
/*******************************************************************************
* Function Name  : tp_ns2009Match
* Description	 : tp_ns2009Match
* Input		     : NONE
* Output		 : None
* Return		 : none
*******************************************************************************/
int tp_ns2009_getPoint(TP_POINT_T * p)
{
    int x,y,z1 = 0;
    static u16 last_x = 0,last_y = 0;

    if (tp_ns2009_read(NS2009_LOW_POWER_READ_Z1,&z1) >= 0)
    {
        if ((z1 > 70) && (z1 < 2000))
        {
            tp_ns2009_read(NS2009_LOW_POWER_READ_X,&y);// swap x y
            tp_ns2009_read(NS2009_LOW_POWER_READ_Y,&x);
            //uart_Printf("x0:%d y0:%d z1:%d\n",x,y,z1);
            if(x>50000)
                x=0;
            if(y>50000)
                y=0;

            tp_ns2009_corr(&x, &y);
            tp_ns2009_avg(&x, &y);
            last_x = x;
            last_y = y;
            p->x = x;
            p->y = y;
            deg_Printf("x1:%d y1:%d z1:%d\n",x,y,z1);
        }else{
            ns2009_avg_last=0;
            //deg_Printf("111 x1:%d y1:%d z1:%d\n",x,y,z1);
    	    return 0;
        }
    }else{
		return -1;
	}
    return 1;
}


ALIGNED(4) const TOUCHPANEL_OP_T tp_ns2009 =
{
	.name 		= "TP NS2009",
	.tp_width 	= NS2009_HOR_RES,
	.tp_height 	= NS2009_VER_RES,
	.init		= tp_ns2009_init,
	.match		= tp_ns2009_Match,
	.getPoint	= tp_ns2009_getPoint,
};