/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../inc/hx330x_cfg.h"


ALIGNED(4) INT32 XOSNesting;
ALIGNED(4) static volatile INT32U XOSTick,Xrandom;
/*******************************************************************************
* Function Name  : XOSInit
* Description    : X os initial
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void XOSInit(void)
{
	XOSNesting = 0;
	XOSTick = 0;
#if X_CFG_MSGQ_USE > 0
    XMsgQInit();
#endif

#if X_CFG_MBOX_USE > 0
    XMboxInit();
#endif

}

/*******************************************************************************
* Function Name  : XOSTickHook
* Description    : X os tick service
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void XOSTickService(void)
{
	XOSNesting++;
	Xrandom++;

    XOSTick+=X_TICK_TIME;

    XWorkService();
	
	XOSNesting--;
}
/*******************************************************************************
* Function Name  : XOSTimeGet
* Description    : X os tick get
* Input          : none
* Output         : none                                            
* Return         : INT32U tick count
*******************************************************************************/
INT32U XOSTimeGet(void)
{	
	return XOSTick;	
}
/*******************************************************************************
* Function Name  : XOSTimeDly
* Description    : X os tick delay
* Input          : INT32U dely : delay ticks
* Output         : none                                            
* Return         : none
*******************************************************************************/
void XOSTimeDly(INT32U dely)
{
	INT32U temp;
	
	temp = XOSTick;
	if(XOSTick == 0)
		return ;
	while(XOSTick<(temp+dely)) hx330x_wdtClear();
}
/*******************************************************************************
* Function Name  : XOSRandom
* Description    : X random value
* Input          : none
* Output         : none                                            
* Return         : INT32U
*******************************************************************************/
INT32U XOSRandom(void)
{
	return Xrandom;
}










