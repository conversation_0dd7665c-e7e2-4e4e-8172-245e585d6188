/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	PHOTO_MODE_ID =0,
	PHOTO_RESOLUTION_ID,
	
	PHOTO_SENSOR_RES_ID,

	PHOTO_POWERON_TIME_ID,
	PHOTO_BATERRY_ID,
	
	PHOTO_SYSTIME_ID,
	PHOTO_LED_ID,
	PHOTO_IRLED_ID,
	PHOTO_SD_ID,
	PHOTO_MIC_ID,
	PHOTO_SCALER_ID,
	PHOTO_FOCUS_ID,
	PHOTO_ROTATE_ID,

	PHOTO_CHACK_SD_ID,

	PHOTO_TEMPERAUE_ID,
	PHOTO_TEMPERAUE_C_ID,
	PHOTO_HIGH_T_ID,
	R_ID_STR_TEMP_C,
	
	
	
	PHOTO_MAX_ID
};
/*******************************************************************************
* Function Name  : recordPhotoWin
* Description    : recordPhotoWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
// #define MENU_OPTION_START(name)  		 		menuOption  menuOption##name[]= {
// #define MENU_OPTION_IMAGE_STR(image,str)		{(u32)image,(u32)str},
// #define MENU_OPTION_IMAGE(image)        		{(u32)image,(u32)0},
// #define MENU_OPTION_STR(str)             		{(u32)0,    (u32)str},
// #define MENU_OPTION_END()           	 		};
// char * task_com_sdcCap_str(void);
UNUSED ALIGNED(4) widgetCreateInfor recordPhotoWin[] =
{
	createFrameWin(							Rx(0),	 Ry(0),   Rw(320), Rh(240),R_ID_PALETTE_Transparent,WIN_ABS_POS),
	createImageIcon(PHOTO_MODE_ID,          Rx(5), 	 Ry(0),   Rw(30),  Rh(30), R_ID_ICON_MTPHOTO,ALIGNMENT_LEFT),
	//eateStringIcon(PHOTO_RESOLUTION_ID,	Rx(120), Ry(0),   Rw(30),  Rh(30), RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
	//eateStringIcon(PHOTO_SENSOR_RES_ID,	Rx(150), Ry(0),   Rw(80), Rh(30), RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White, DEFAULT_FONT),
	createImageIcon(PHOTO_LED_ID,        	Rx(235), Ry(0), Rw(30),  Rh(30), 	R_ID_ICON_MTLED3,	ALIGNMENT_CENTER),
	//eateStringIcon(PHOTO_POWERON_TIME_ID, Rx(230), Ry(0),   Rw(60),  Rh(30), RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White, DEFAULT_FONT),
	createImageIcon(PHOTO_BATERRY_ID,    	Rx(290), Ry(0),   Rw(30),  Rh(30), R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),		
	
	createStringIcon(PHOTO_SYSTIME_ID,      Rx(5),   Ry(210), Rw(285), Rh(30), RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
	createStringIcon(PHOTO_SCALER_ID,		Rx(270),	 Ry(210), Rw(35), Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White, DEFAULT_FONT),
#if DEV_IR_EN 
	createImageIcon(PHOTO_IRLED_ID,     	Rx(290), Ry(150), Rw(30),  Rh(30), 	R_ID_ICON_MTIROFF,		ALIGNMENT_CENTER),
#endif
	createImageIcon(PHOTO_SD_ID,        	Rx(260), Ry(0), Rw(30),  Rh(30), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
//	createImageIcon(PHOTO_MIC_ID,       	Rx(290), Ry(210), Rw(30),  Rh(30), 	R_ID_ICON_MTMICOFF,		ALIGNMENT_CENTER),
	createImageIcon(PHOTO_FOCUS_ID,        Rx(125), Ry(80), Rw(70),  Rh(70), 	R_ID_ICON_MTFOCUS,	ALIGNMENT_CENTER),
	createImageIcon(PHOTO_ROTATE_ID,        Rx(150), Ry(0), Rw(30),  Rh(30), 	R_ID_ICON_MTROTATE,	ALIGNMENT_CENTER),
	widgetEnd(),
};
	

/*******************************************************************************
* Function Name  : recordPhotoResShow
* Description    : recordPhotoResShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static  void recordPhotoResShow(winHandle handle)
{
	switch(user_config_get(CONFIG_ID_PRESLUTION))
	{
		case R_ID_STR_RES_1M: uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("1M")); break;
		case R_ID_STR_RES_2M: uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("2M")); break;
		case R_ID_STR_RES_3M: uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("3M")); break;
		case R_ID_STR_RES_5M: uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("5M")); break;
		case R_ID_STR_RES_8M: uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("8M")); break;
		case R_ID_STR_RES_10M:uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("10M")); break;
		case R_ID_STR_RES_12M:uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("12M")); break;
		case R_ID_STR_RES_16M:uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("16M")); break;
		case R_ID_STR_RES_18M:uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("18M")); break;
		case R_ID_STR_RES_20M:uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("20M")); break;
		case R_ID_STR_RES_24M:uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("24M")); break;
		case R_ID_STR_RES_40M:uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("40M")); break;
		case R_ID_STR_RES_48M:uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("48M")); break;
		default:  			  uiWinSetResid(winItem(handle,PHOTO_RESOLUTION_ID),RAM_ID_MAKE("???")); break;
	}
}
/*******************************************************************************
* Function Name  : videoResolutionShow
* Description    : videoResolutionShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/	
UNUSED static void recordPhotoSensorResShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PHOTO_SENSOR_RES_ID),RAM_ID_MAKE(task_com_sensor_res_str()));
}
/*******************************************************************************
* Function Name  : recordPhotoPowerOnTimeShow
* Description    : recordPhotoPowerOnTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoPowerOnTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PHOTO_POWERON_TIME_ID),RAM_ID_MAKE(task_com_powerOnTime_str()));
}
/*******************************************************************************
* Function Name  : recordPhotoBatteryShow
* Description    : recordPhotoBatteryShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoBatteryShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERY5;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4:
			//case BATTERY_STAT_5:
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetVisible(winItem(handle,PHOTO_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,PHOTO_BATERRY_ID),batid);

}
/*******************************************************************************
* Function Name  : videoSysTimeShow
* Description    : videoSysTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoSysTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,PHOTO_SYSTIME_ID),RAM_ID_MAKE(hal_rtcTime2String(hal_rtcTimeGet())));
}
/*******************************************************************************
* Function Name  : recordPhotoIrLEDShow
* Description    : recordPhotoIrLEDShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoIrLEDShow(winHandle handle)
{
	if(hardware_setup.ir_led_en)
	{
		if(user_config_get(CONFIG_ID_IR_LED)==R_ID_STR_COM_OFF)
			uiWinSetResid(winItem(handle,PHOTO_IRLED_ID),R_ID_ICON_MTIROFF);
		else
			uiWinSetResid(winItem(handle,PHOTO_IRLED_ID),R_ID_ICON_MTIRON);
	}else
	{
		uiWinSetVisible(winItem(handle,PHOTO_IRLED_ID),0);
	}
}


// UNUSED static void PhotoSdShow(winHandle handle)
// {
// 	uiWinSetResid(winItem(handle,PHOTO_CHACK_SD_ID),RAM_ID_MAKE(task_com_sdcCap_str()));
// }

/*******************************************************************************
* Function Name  : recordPhotoSDShow
* Description    : recordPhotoSDShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		uiWinSetResid(winItem(handle,PHOTO_SD_ID),R_ID_ICON_MTSDCNORMAL);
	else
		uiWinSetResid(winItem(handle,PHOTO_SD_ID),R_ID_ICON_MTSDCNULL);
}
/*******************************************************************************
* Function Name  : recordPhotoMicShow
* Description    : recordPhotoMicShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void recordPhotoMicShow(winHandle handle)
{
	if(user_config_get(CONFIG_ID_AUDIOREC)==R_ID_STR_COM_OFF)
		uiWinSetResid(winItem(handle,PHOTO_MIC_ID),R_ID_ICON_MTMICOFF);
	else
		uiWinSetResid(winItem(handle,PHOTO_MIC_ID),R_ID_ICON_MTMICON);
}

UNUSED static void recordPhotoScalerShow(winHandle handle)
{

		uiWinSetVisible(winItem(handle,PHOTO_SCALER_ID),1);
		uiWinSetResid(winItem(handle,PHOTO_SCALER_ID),RAM_ID_MAKE(task_com_scaler_str()));//

}

UNUSED static void recordPhotoScalerHide(winHandle handle)
{
	uiWinSetVisible(winItem(handle,PHOTO_SCALER_ID),0);
}

UNUSED static void recordPhotoLedShow(winHandle handle)
{
		resID batid;
		switch(SysCtrl.led_pwm_level)
		{
			case 0: 					 break;
			case 1: batid = R_ID_ICON_MTLED1; break;
			case 2: batid = R_ID_ICON_MTLED2; break;
			case 3: batid = R_ID_ICON_MTLED3; break;
			default:
										 break;
		}

	if(SysCtrl.led_pwm_level)
	{
		uiWinSetVisible(winItem(handle,PHOTO_LED_ID),1);
		uiWinSetResid(winItem(handle,PHOTO_LED_ID),batid);

	}else
	{
		uiWinSetVisible(winItem(handle,PHOTO_LED_ID),0);
	}




}



	UNUSED static void recordPhotoRotateShow(winHandle handle)
{
	if(SysCtrl.rotate_status)
		uiWinSetVisible(winItem(handle,PHOTO_ROTATE_ID),1);
	else
		uiWinSetVisible(winItem(handle,PHOTO_ROTATE_ID),0);
}



// UNUSED static void recordPhotoTemperaueShow(winHandle handle)
// {
// 	// uiWinSetResid(winItem(handle,PHOTO_TEMPERAUE_ID),RAM_ID_MAKE(" "));
// 	// uiWinSetResid(winItem(handle,PHOTO_TEMPERAUE_C_ID),RAM_ID_MAKE(" "));
// 	// uiWinSetVisible(winItem(handle,PHOTO_HIGH_T_ID),0);
// 	// return;
// 	if((!husb_api_usensor_atech_sta()&&(!SysCtrl.high_T))||(SysCtrl.temperaue_value<0)){
// 		uiWinSetResid(winItem(handle,PHOTO_TEMPERAUE_ID),RAM_ID_MAKE(" "));
// 		uiWinSetResid(winItem(handle,PHOTO_TEMPERAUE_C_ID),RAM_ID_MAKE(" "));
// 		uiWinSetVisible(winItem(handle,PHOTO_HIGH_T_ID),0);
// 	}else
// 	{
// 		if(SysCtrl.temperaue_value>=T_75){
// 			uiWinSetStrInfor(winItem(handle,PHOTO_TEMPERAUE_ID), DEFAULT_FONT, ALIGNMENT_LEFT, R_ID_PALETTE_Red);
// 			uiWinSetStrInfor(winItem(handle,PHOTO_TEMPERAUE_C_ID), DEFAULT_FONT, ALIGNMENT_LEFT, R_ID_PALETTE_Red);
// 			uiWinSetVisible(winItem(handle,PHOTO_HIGH_T_ID),1);//uiWinSetResid(winItem(handle,PHOTO_HIGH_T_ID),R_ID_ICON_MTHIGHTEMP);
// 		}else{
// 			uiWinSetStrInfor(winItem(handle,PHOTO_TEMPERAUE_ID), DEFAULT_FONT, ALIGNMENT_LEFT, R_ID_PALETTE_White);
// 			uiWinSetStrInfor(winItem(handle,PHOTO_TEMPERAUE_C_ID), DEFAULT_FONT, ALIGNMENT_LEFT, R_ID_PALETTE_White);
// 			uiWinSetVisible(winItem(handle,PHOTO_HIGH_T_ID),0);//uiWinSetResid(winItem(handle,PHOTO_HIGH_T_ID),R_ID_ICON_MTNOCAMNULL);

// 		}
// 		uiWinSetResid(winItem(handle,PHOTO_TEMPERAUE_ID),RAM_ID_MAKE(task_com_temperaue_str()));
// 		uiWinSetResid(winItem(handle,PHOTO_TEMPERAUE_C_ID),R_ID_STR_TEMP_C);
	
// 	}	
// }