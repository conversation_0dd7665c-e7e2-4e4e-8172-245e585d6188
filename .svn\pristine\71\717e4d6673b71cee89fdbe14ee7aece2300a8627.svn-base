/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	DATETIME_SELECT_ID = 0,
	DATETIME_SLASH1_ID,
	DATETIME_SLASH2_ID,
	DATETIME_COLON1_ID,
	DATETIME_COLON2_ID,
};
UNUSED ALIGNED(4) const widgetCreateInfor dateTimeWin[] =
{
#if USER_UI_MENU_ROUNDRECT == 0
	createFrameWin(							Rx(70),	<PERSON>y(42), <PERSON>w(180),<PERSON>h(142),R_ID_PALETTE_DimGray,WIN_ABS_POS),
	createItemManage(DATETIME_SELECT_ID,	Rx(0),	Ry(0), 	Rw(180),Rh(142),INVALID_COLOR),
#else
	// createFrameRoundRimWin(						Rx(70),	Ry(42), Rw(180),Rh(142),R_ID_PALETTE_DimGray,USER_UI_MENU_RIMCOLOR, WIN_ABS_POS, ROUND_ALL),
	// createItemManageRoundRim(DATETIME_SELECT_ID,Rx(0),	Ry(0), 	Rw(180),Rh(142),INVALID_COLOR, USER_UI_MENU_RIMCOLOR, ROUND_ALL),
		createFrameRoundRimWin(						Rx(70),	Ry(42), Rw(180),Rh(142),R_ID_PALETTE_Gray_SUB_BG,USER_UI_MENU_RIMCOLOR, WIN_ABS_POS, ROUND_ALL),
	//createItemManageRoundRim(DATETIME_SELECT_ID,Rx(0),	Ry(0), 	Rw(180),Rh(142),INVALID_COLOR, USER_UI_MENU_RIMCOLOR, ROUND_ALL),
	createItemManage(DATETIME_SELECT_ID,	Rx(0),	Ry(0),	Rw(180),Rh(142),INVALID_COLOR),
	createStringIcon(DATETIME_SLASH1_ID,	Rx(60), Ry(13), Rw(30), Rh(30), RAM_ID_MAKE("/"),ALIGNMENT_LEFT, R_ID_PALETTE_Black, DEFAULT_FONT),
	createStringIcon(DATETIME_SLASH2_ID,	Rx(110), Ry(13), Rw(30), Rh(30), RAM_ID_MAKE("/"),ALIGNMENT_LEFT, R_ID_PALETTE_Black, DEFAULT_FONT),
	createStringIcon(DATETIME_COLON1_ID,	Rx(67), Ry(55), Rw(30), Rh(30), RAM_ID_MAKE(":"),ALIGNMENT_LEFT, R_ID_PALETTE_Black, DEFAULT_FONT),
	createStringIcon(DATETIME_COLON2_ID,	Rx(106), Ry(55), Rw(30), Rh(30), RAM_ID_MAKE(":"),ALIGNMENT_LEFT, R_ID_PALETTE_Black, DEFAULT_FONT),
#endif
	widgetEnd(),
};



