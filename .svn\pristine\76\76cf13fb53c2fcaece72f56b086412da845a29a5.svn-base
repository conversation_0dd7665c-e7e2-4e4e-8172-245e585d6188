/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiStringIconProc
* Description    : uiStringIconProc
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiStringIconProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiStringIconObj* pstringIcon;
	uiWinObj* pWin;
	uiStrInfo* stringInfor;
	uiResInfo* resInfor;
	if(uiWidgetProc(msg))
		return;	
	hWin 		= msg->curWin;
	pstringIcon	= (uiStringIconObj*)uiHandleToPtr(hWin);
	pWin		= &(pstringIcon->widget.win);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			return;
		case MSG_WIN_PAINT:
			if(pstringIcon->string.id != INVALID_RES_ID)
			{
				if(pstringIcon->select)
					uiWinDrawString(&pWin->rect,(uiRect*)(msg->para.p),&pstringIcon->stringSelect);
				else
					uiWinDrawString(&pWin->rect,(uiRect*)(msg->para.p),&pstringIcon->string);
			}
			return;
		case MSG_WIN_UPDATE_RESID:
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor==INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIN_UNSELECT:
			if(pstringIcon->select == 0)
				return;
			pstringIcon->select = 0;
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIN_SELECT:
			if(pstringIcon->select == 1)
				return;
			pstringIcon->select = 1;
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIN_CHANGE_RESID:
			if( !RES_ID_IS_RAM(pstringIcon->string.id) && pstringIcon->string.id == msg->para.v)
				return;
			pstringIcon->string.id 		 = msg->para.v;
			pstringIcon->stringSelect.id = msg->para.v;
			if(uiWinIsVisible(hWin))
			{
				if(pWin->bgColor == INVALID_COLOR)
					uiWinParentRedraw(hWin);
				else
					uiWinUpdateInvalid(hWin);
			}
			return;
		case MSG_WIN_SELECT_INFOR_EX:
			resInfor = (uiResInfo*)(msg->para.p);		
			pstringIcon->stringSelect.strAlign 	= resInfor->strAlign;
			pstringIcon->stringSelect.font		= resInfor->font;
			pstringIcon->stringSelect.fontColor = resInfor->fontColor;
			pstringIcon->stringSelect.bgColor 	= resInfor->bgColor;
			pstringIcon->stringSelect.rimColor 	= resInfor->rimColor;
			return;
		case MSG_WIN_UNSELECT_INFOR_EX:
			resInfor = (uiResInfo*)(msg->para.p);		
			pstringIcon->string.strAlign 	= resInfor->strAlign;
			pstringIcon->string.font		= resInfor->font;
			pstringIcon->string.fontColor 	= resInfor->fontColor;
			pstringIcon->string.bgColor 	= resInfor->bgColor;
			pstringIcon->string.rimColor 	= resInfor->rimColor;
			return;
		case MSG_WIN_CHANGE_STRINFOR:
			stringInfor = (uiStrInfo*)(msg->para.p);
			pstringIcon->string.strAlign 	= stringInfor->strAlign;
			pstringIcon->string.font		= stringInfor->font;
			pstringIcon->string.fontColor 	= stringInfor->fontColor;
			pstringIcon->stringSelect.strAlign 	= stringInfor->strAlign;
			pstringIcon->stringSelect.font		= stringInfor->font;
			pstringIcon->stringSelect.fontColor = stringInfor->fontColor;
			return;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			((touchInfor *)(msg->para.p))->touchWin		= pWin->parent;
			((touchInfor *)(msg->para.p))->touchHandle	= hWin;
			((touchInfor *)(msg->para.p))->touchID		= pstringIcon->widget.id;
			((touchInfor *)(msg->para.p))->touchItem	= 0;
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiStringIconCreateDirect
* Description    : uiStringIconCreateDirect
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiStringIconCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id)
{
	winHandle 		hstringIcon;
	uiStringIconObj *pstringIcon;
	hstringIcon		= uiWinCreate(x0,y0,width,height,parent,uiStringIconProc,sizeof(uiStringIconObj),WIN_WIDGET|style);
	if(hstringIcon != INVALID_HANDLE)
	{
		
		pstringIcon = (uiStringIconObj*)uiHandleToPtr(hstringIcon);
		
		pstringIcon->string.id			    = INVALID_RES_ID;
		pstringIcon->string.style		    = style;
		pstringIcon->string.strAlign 	    = ALIGNMENT_CENTER;
		pstringIcon->string.font		    = DEFAULT_FONT;
		pstringIcon->string.fontColor 	    = DEFAULT_COLOR;
		pstringIcon->string.bgColor 	    = INVALID_COLOR;
		pstringIcon->string.rimColor 	    = INVALID_COLOR;
		pstringIcon->stringSelect.id		= INVALID_RES_ID;
		pstringIcon->stringSelect.style		= style;
		pstringIcon->stringSelect.strAlign 	= ALIGNMENT_CENTER;
		pstringIcon->stringSelect.font		= DEFAULT_FONT;
		pstringIcon->stringSelect.fontColor = DEFAULT_COLOR;
		pstringIcon->stringSelect.bgColor 	= INVALID_COLOR;
		pstringIcon->stringSelect.rimColor 	= INVALID_COLOR;		
		pstringIcon->select 				= 0;
		uiWidgetSetId(hstringIcon,id);
		uiWinSetbgColor(hstringIcon, INVALID_COLOR);
	}
	return hstringIcon;
}
/*******************************************************************************
* Function Name  : uiStringIconCreate
* Description    : uiStringIconCreate
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiStringIconCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle hstringIcon;
	uiStringIconObj* pstringIcon;
	hstringIcon = uiWinCreate(infor->x0,infor->y0,infor->width,infor->height,parent,uiStringIconProc,sizeof(uiStringIconObj),WIN_WIDGET|WIN_TOUCH_SUPPORT|infor->style);
	uiWinObj		*pWin;
	if(hstringIcon!=INVALID_HANDLE)
	{
		pstringIcon	= (uiStringIconObj*)uiHandleToPtr(hstringIcon);
		pWin				= &(pstringIcon->widget.win);
		pstringIcon->string.id			= infor->str;
		pstringIcon->string.style		= infor->style;
		pstringIcon->string.strAlign 	= infor->strAlign;
		pstringIcon->string.font		= infor->font;
		pstringIcon->string.fontColor 	= infor->fontColor;
		pstringIcon->string.bgColor 	= infor->bgColor;
		pstringIcon->string.rimColor 	= infor->rimColor;
		pstringIcon->stringSelect.id		= infor->str;
		pstringIcon->stringSelect.style		= infor->style;
		pstringIcon->stringSelect.strAlign 	= infor->strAlignS;
		pstringIcon->stringSelect.font		= infor->fontS;
		pstringIcon->stringSelect.fontColor = infor->fontColorS;
		pstringIcon->stringSelect.bgColor 	= infor->bgColorS;
		pstringIcon->stringSelect.rimColor 	= infor->rimColorS;		
		pstringIcon->select 				= 0;
		uiWidgetSetId(hstringIcon,infor->id);
		uiWinSetbgColor(hstringIcon, infor->bgColor);
		//deg_Printf("uiStringIconCreate[%d,%d,%d,%d][%x,%d,%x]\n",pWin->round_rect.x0, pWin->round_rect.x1, pWin->round_rect.y0, pWin->round_rect.y1,pWin->round_rect.round_type, pWin->round_rect.radius,pWin->round_rect.rimColor);
		
	}	
	return hstringIcon;
}
