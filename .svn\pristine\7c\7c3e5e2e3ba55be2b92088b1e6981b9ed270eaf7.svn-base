/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT == LCD_RGB_ST7701S_LX45FWI4006

#define CMD(x)  LCD_CMD_RGB_DAT(x)
#define DAT(x)  LCD_CMD_RGB_DAT((x)|(1<<8))
#define DELAY(m)  LCD_CMD_DELAY_MS(m)


//--------------------------- LCD_RGB_ST7701S_LX45FWI4006 ---------------------------------
LCD_INIT_TAB_BEGIN()
 
CMD(0x11),
DELAY(120),

CMD(0xFF),
DAT(0x77),
DAT(0x01),
DAT(0x00),
DAT(0x00),
DAT(0x10),

//Display Line Setting
CMD(0xC0),
DAT(0xE9),//854
DAT(0x03),

//Porch Control C100h:Back-Porch  C101h:Front-Porch
CMD(0xC1),
DAT(0x11),
DAT(0x02),

//Set inversion
CMD(0xC2),
DAT(0x37),
DAT(0x08),

CMD (0xCC),
DAT (0x10),

//-------------------------------------Gamma Cluster Setting-------------------------------------------//
CMD(0xB0),
DAT(0x00),
DAT(0x18),
DAT(0x1C),
DAT(0x0E),
DAT(0x15),
DAT(0x05),
DAT(0x48),
DAT(0x08),
DAT(0x08),
DAT(0x23),
DAT(0x05),
DAT(0x14),
DAT(0x12),
DAT(0x28),
DAT(0x30),
DAT(0x1D),

CMD(0xB1),
DAT(0x00),
DAT(0x06),
DAT(0x0F),
DAT(0x0D),
DAT(0x0D),
DAT(0x06),
DAT(0x40),
DAT(0x08),
DAT(0x08),
DAT(0x1C),
DAT(0x04),
DAT(0x0F),
DAT(0x0E),
DAT(0x28),
DAT(0x31),
DAT(0x1D),

CMD(0xFF),
DAT(0x77),
DAT(0x01),
DAT(0x00),
DAT(0x00),
DAT(0x11),

CMD(0xB0),
DAT(0x6C),

CMD(0xB1),
DAT(0x5E),

CMD(0xB2),
DAT(0x07),

CMD(0xB3),
DAT(0x80),

CMD(0xB5),
DAT(0x47),

CMD(0xB7),
DAT(0x85),

CMD(0xB8),
DAT(0x20),

CMD(0xB9),
DAT(0x10),

CMD(0xC1),
DAT(0x78),

CMD(0xC2),
DAT(0x78),

CMD(0xD0),
DAT(0x88),
DELAY(100),

CMD(0xE0),
DAT(0x00),
DAT(0x00),
DAT(0x02),

CMD(0xE1),
DAT(0x08),
DAT(0x00),
DAT(0x0A),
DAT(0x00),
DAT(0x07),
DAT(0x00),
DAT(0x09),
DAT(0x00),
DAT(0x00),
DAT(0x33),
DAT(0x33),

CMD(0xE2),
DAT(0x00),
DAT(0x00),
DAT(0x00),
DAT(0x00),
DAT(0x00),
DAT(0x00),
DAT(0x00),
DAT(0x00),
DAT(0x00),
DAT(0x00),
DAT(0x00),
DAT(0x00),
DAT(0x00),

CMD(0xE3),
DAT(0x00),
DAT(0x00),
DAT(0x33),
DAT(0x33),

CMD(0xE4),
DAT(0x44),
DAT(0x44),

CMD(0xE5),
DAT(0x0E),
DAT(0x60),
DAT(0xA0),
DAT(0xA0),
DAT(0x10),
DAT(0x60),
DAT(0xA0),
DAT(0xA0),
DAT(0x0A),
DAT(0x60),
DAT(0xA0),
DAT(0xA0),
DAT(0x0C),
DAT(0x60),
DAT(0xA0),
DAT(0xA0),

CMD(0xE6),
DAT(0x00),
DAT(0x00),
DAT(0x33),
DAT(0x33),

CMD(0xE7),
DAT(0x44),
DAT(0x44),

CMD(0xE8),
DAT(0x0D),
DAT(0x60),
DAT(0xA0),
DAT(0xA0),
DAT(0x0F),
DAT(0x60),
DAT(0xA0),
DAT(0xA0),
DAT(0x09),
DAT(0x60),
DAT(0xA0),
DAT(0xA0),
DAT(0x0B),
DAT(0x60),
DAT(0xA0),
DAT(0xA0),

CMD(0xEB),
DAT(0x02),
DAT(0x01),
DAT(0xE4),
DAT(0xE4),
DAT(0x44),
DAT(0x00),
DAT(0x40),

CMD(0xEC),
DAT(0x02),
DAT(0x01),

CMD(0xED),
DAT(0xAB),
DAT(0x89),
DAT(0x76),
DAT(0x54),
DAT(0x01),
DAT(0xFF),
DAT(0xFF),
DAT(0xFF),
DAT(0xFF),
DAT(0xFF),
DAT(0xFF),
DAT(0x10),
DAT(0x45),
DAT(0x67),
DAT(0x98),
DAT(0xBA),

CMD(0xFF),
DAT(0x77),
DAT(0x01),
DAT(0x00),
DAT(0x00),
DAT(0x00),

//CMD (0x36),
//DAT (0x08),

/*CMD(0xFF),
DAT(0x77),
DAT(0x01),
DAT(0x00),
DAT(0x00),
DAT(0x12),

CMD(0xD1),
DAT(0x81),

CMD(0xD2),
DAT(0x08),*///测试

CMD(0x29),
DELAY(50),
LCD_INIT_TAB_END()

LCD_UNINIT_TAB_BEGIN()  
    CMD(0x28),
    DELAY(10),
LCD_UNINIT_TA_ENDB()

LCD_DESC_BEGIN()

    .name 			= "RGB_ST7701S_LX45FWI4006",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_270,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_24,

    .pclk_div 		= LCD_PCLK_DIV(24000000),
    .clk_per_pixel 	= 1,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .pclk_edge      = LCD_PCLK_EDGE_FALLING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw 			= 4,
    .vbp 			= 20,
    .vfp 			= 20,

    .hlw 			= 10,
    .hbp 			= 50,
    .hfp 			= 50,

    LCD_SPI_DEFAULT(9),

    .data_mode 	= LCD_DATA_MODE0_24BIT_RGB888,

    .screen_w 		= 480,
    .screen_h 		= 854,

    .video_w  		= 854,
    .video_h  		= 480,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast   	= LCD_SATURATION_115,

    .brightness 	= 4,

    .saturation 	= LCD_SATURATION_145,

    .contra_index 	= 6,

    .gamma_index 	= {4, 4, 5},

    .asawtooth_index = {1, 1},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()
#endif