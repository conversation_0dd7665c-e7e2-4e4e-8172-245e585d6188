/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiRectProc
* Description    : uiRectProc
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiCycleProc(uiWinMsg* msg)
{
	//uiWinDrawCircle(uiCycle *cycle,uiColor bgcolor, uiColor fontcolor);
	winHandle hWin;
	uiCycleObj* pcycle;
//	uiWinObj* pWin;
	if(uiWidgetProc(msg))
		return;
	hWin 	= msg->curWin;
	pcycle	= (uiCycleObj*)uiHandleToPtr(hWin);
//	pWin	= &(pLine->widget.win);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			//deg_msg("line win create\n");
			return;
		case MSG_WIN_PAINT:
			uiWinDrawCircle(&pcycle->cycle,pcycle->bgcolor, pcycle->fontColor);
			//deg_msg("paint button [%d]:[%d %d %d %d]\n",pButton->widget.id,pWin->invalidRect.x0,pWin->invalidRect.y0,pWin->invalidRect.x1,pWin->invalidRect.y1);
			return;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		case MSG_WIN_CHANGE_BG_COLOR:
			if(pcycle->bgcolor != msg->para.v)
			{
				pcycle->bgcolor =  msg->para.v;
				uiWinDrawCircle(&pcycle->cycle,pcycle->bgcolor, pcycle->fontColor);
			}
			break;	
		case MSG_WIN_CHANGE_FG_COLOR:
			if(pcycle->fontColor != msg->para.v)
			{
				pcycle->fontColor =  msg->para.v;
				uiWinDrawCircle(&pcycle->cycle,pcycle->bgcolor, pcycle->fontColor);
			}
			break;	
		case MSG_WIN_CHANGE_CYCLE_RADIUS:
			if(pcycle->cycle.radius != msg->para.v)
			{
				pcycle->cycle.radius =  msg->para.v;
				uiWinDrawCircle(&pcycle->cycle,pcycle->bgcolor, pcycle->fontColor);
			}
			break;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiRectProc
* Description    : uiRectProc
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiCycleCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id)
{
	winHandle hcycle;
	uiCycleObj *pcycle;
	hcycle = uiWinCreate(x0,y0,width,height,parent,uiCycleProc,sizeof(uiCycleObj),WIN_WIDGET|style);
	
	if(hcycle != INVALID_HANDLE)
	{
		pcycle 			= (uiCycleObj*)uiHandleToPtr(hcycle);
		pcycle->select  = 0;
		uiWidgetSetId(hcycle,id);
	}
	return hcycle;
}
/*******************************************************************************
* Function Name  : uiRectProc
* Description    : uiRectProc
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiCycleCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle 	hcycle;
	uiCycleObj	*pcycle;
	if(!(infor->style & WIN_NOT_ZOOM))
	{
		infor->x0 		= USER_Rx(infor->x0);
		infor->y0 		= USER_Ry(infor->y0);
		infor->width 	= USER_Rw(infor->width);
		infor->height 	= USER_Rh(infor->height);
		infor->image 	= USER_Rw(infor->image);		
	}
	hcycle = uiWinCreate(infor->x0,infor->y0,infor->width,infor->height,parent,uiCycleProc,sizeof(uiCycleObj),WIN_WIDGET|WIN_TOUCH_SUPPORT|WIN_NOT_ZOOM);
	if(hcycle != INVALID_HANDLE)
	{
		pcycle = (uiCycleObj*)uiHandleToPtr(hcycle);

		pcycle->cycle.rect.x0 = infor->x0;
		pcycle->cycle.rect.y0 = infor->y0;
		pcycle->cycle.rect.x1 = infor->x0 + infor->width - 1;
		pcycle->cycle.rect.y1 = infor->y0 + infor->height - 1;
		pcycle->cycle.radius  = infor->image;
		pcycle->cycle.x_center = infor->x0 + infor->width/2;
		pcycle->cycle.y_center = infor->y0 + infor->height/2;
		pcycle->bgcolor			= infor->bgColor;
		pcycle->fontColor		= infor->fontColor; 

		pcycle->select 		= 0;
		uiWidgetSetId(hcycle,infor->id);
		uiWinSetbgColor(hcycle, infor->bgColor);
		
	}
	return hcycle;
}
