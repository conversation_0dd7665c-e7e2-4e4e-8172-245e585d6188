/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_MANAGE_H
#define UI_WIN_MANAGE_H

//--------------define ui critial ---------------------------------------------------
#define uiWinLock()
#define uiWinUnlock()
//--------------define ui handle ---------------------------------------------------
typedef void*   				winHandle;
#define INVALID_HANDLE      	(winHandle)NULL
#define uiHandleToPtr(n)    	(n)
#define uiPtrToHandle(n)    	(n)

#define DEFAULT_STYLE  			(WIN_INVALID|WIN_VISIBLE)
#define DEFAULT_FONT			RES_FONT_NUM1
//--------------define ERR TIPS INFO---------------------------------------------------
#define TIPS_FONTCOLOR       	R_ID_PALETTE_Red
#define TIPS_BGCOLOR         	R_ID_PALETTE_TransBlack
#define TIPS_FONTNUM  			RES_FONT_NUM3//0x02		//RES_FONT_NUM3
//--------------define TOUCH STATE---------------------------------------------------
#define TOUCH_NONE       		0
#define TOUCH_PRESS      		1
#define TOUCH_OVER       		2
#define TOUCH_RELEASE    		3

//--------------define MSG id---------------------------------------------------
typedef enum
{
	MSG_WIN_CREATE=0,
	MSG_WIN_DESTROY,
	MSG_WIN_CHILE_DESTROY,
	MSG_WIN_WIDGET_DESTROY,
	MSG_WIN_PARENT_DEAL,
	MSG_WIN_INVALID,
	MSG_WIN_PAINT,
	MSG_WIN_CHANGE_RESID,
	MSG_WIN_CHG_ITEM_SEL_RESID,
	MSG_WIN_INVALID_RESID,
	MSG_WIN_UPDATE_RESID,
	MSG_WIN_VISIBLE_SET,
	MSG_WIN_VISIBLE_GET,
	MSG_WIN_CHANGE_BG_COLOR,
	MSG_WIN_CHANGE_FG_COLOR,
	MSG_WIN_CHANGE_STRINFOR,
	MSG_WIN_PROGRESS_RATE,
	MSG_WIN_CHANGE_CYCLE_RADIUS,
	MSG_WIN_CHANGE_ROUNDRECT_RADIUS,
	MSG_WIN_SELECT_INFOR,
	MSG_WIN_UNSELECT_INFOR,
	MSG_WIN_SELECT_INFOR_EX,
	MSG_WIN_UNSELECT_INFOR_EX,
	MSG_WIN_TOUCH,
	MSG_WIN_TOUCH_GET_INFOR,
	MSG_WIN_SELECT,
	MSG_WIN_UNSELECT,
	MSG_WIN_GET_WIDGET,
	MSG_WIN_ADD_WIDGET,
	MSG_WIDGET_GET_ID,
	MSG_WIDGET_SET_ID,
	MSG_WIDGET_NEXT,
	MSG_WIDGET_PREV,
	MSG_WIDGET_CUR,
	MSG_WIDGET_SELECT,
	MSG_WIDGET_CHANGE_ALL_RESID,
	MSG_WIDGET_RES_SUM_GET,
	MSG_WIDGET_RES_SUM_SET,
	MSG_WIDGET_SET_RESID_BY_NUM,	
	MSG_DIALOG_INIT,
	MSG_DIALOG_CLOSE,
	MSG_MAX,
}uiMsgId;

typedef struct uiWinMsg_S
{
	u32 id;
	winHandle curWin;	
	winHandle childWin; //指向widgethandle
	union
	{
		void* p;
		u32   v;
	}para;
}uiWinMsg;
typedef void (*uiWinCB)(uiWinMsg* msg);

typedef struct uiWinObj_S // all window object inherited winObj,
{
	uiRect 	rect;			// window position in  screen
	uiRect  round_rect;		// window position in  screen
	uiRect 	invalidRect;	// redraw area of the window
	uiColor bgColor;		// window background color
	u8		align;
	u16 	style;
	char* 	name;
	uiWinCB cb;				// window's callback function,process received message
	winHandle parent;
	winHandle child;
	winHandle next; 		// widget window linked here
}uiWinObj;
#define  WINOBJ_SIZE  sizeof(uiWinObj)

typedef struct _touchInfor
{
	uiRect      touchArea;
	winHandle  	touchWin;
	winHandle  	touchHandle;
	u32      	touchID;
	u32      	touchItem;
	u32      	touchState;
}touchInfor;

typedef struct _widgetCreateInfor widgetCreateInfor;
typedef winHandle(*widgetCreateFun)(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);
typedef struct _widgetCreateInfor
{
	widgetCreateFun widgetCreate; 
	
	u16  	id;
	u16 	style;
	s16 	x0;
	s16 	y0;
	u16 	width;
	u16 	height;
	void* 	prvate;
	
	resID  	image;
	u8  	imageAlign;
	u8      imageAligns;
	uiColor bgColor;
	uiColor bgColorS;
	
	resID  	str;
	u8 		strAlign;
	u8 		strAlignS;
	charFont font;
	charFont fontS;
	uiColor fontColor;
	uiColor fontColorS;
	uiColor rimColor;
	uiColor rimColorS;
	
}widgetCreateInfor;

#define createFrameWin(x0,y0,width,height,bgColor,style)	\
{uiFrameWinCreate, (u16)INVALID_WIDGET_ID, (u16)style, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL, \
(resID)INVALID_RES_ID, (u8)0, (u8)0, (uiColor)bgColor, (uiColor)bgColor, \
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }


//rimColor: INVALID_COLOR - no rim, other have rim; round_type: ROUND_ALL- round rect /ROUND_NONE - normal rect
#define createFrameRoundRimWin(x0,y0,width,height,bgColor,rimColor, style, round_type)	\
{uiFrameWinCreate, (u16)INVALID_WIDGET_ID, (u16)style|WIN_ROUND_RECT, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL, \
(resID)INVALID_RES_ID, (u8)round_type, (u8)0, (uiColor)bgColor, (uiColor)bgColor, \
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR, (uiColor)rimColor,(uiColor)rimColor }


#define createItemManage(id,x0,y0,width,height,bgColor)   \
{uiItemManageCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL, \
(resID)INVALID_RES_ID, (u8)0, (u8)0, (uiColor)bgColor, (uiColor)bgColor, \
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }

//rimColor: INVALID_COLOR - no rim, other have rim; round_type: ROUND_ALL- round rect /ROUND_NONE - normal rect
#define createItemManageRoundRim(id,x0,y0,width,height,bgColor, rimColor, round_type)   \
{uiItemManageCreate, (u16)id, (u16)WIN_ROUND_RECT, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL, \
(resID)INVALID_RES_ID, (u8)round_type, (u8)0, (uiColor)bgColor, (uiColor)bgColor, \
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR, (uiColor)rimColor,(uiColor)rimColor }



#define createButton(id,x0,y0,width,height,image,imageAlign,str,strAlign,fontColor,font) \
{uiButtonCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL,\
(resID)image, (u8)imageAlign, (u8)imageAlign, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,\
(resID)str, (u8)strAlign, (u8)strAlign, (charFont)font, (charFont)font, (uiColor)fontColor, (uiColor)fontColor, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }

#define createImageIcon(id,x0,y0,width,height,image,imageAlign) \
{uiImageIconCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL,\
(resID)image, (u8)imageAlign, (u8)imageAlign, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }

#define createRect(id,x0,y0,width,height,fgColor)	\
{uiRectCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)fgColor, (uiColor)fgColor, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }

//rimColor: INVALID_COLOR - no rim, other have rim; round_type: ROUND_ALL- round rect /ROUND_NONE - normal rect
#define createRectRoundRim(id,x0,y0,width,height,fgColor, rimColor, round_type)	\
{uiRectCreate, (u16)id, (u16)WIN_ROUND_RECT, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL,\
(resID)INVALID_RES_ID, (u8)round_type, (u8)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)fgColor, (uiColor)fgColor, (uiColor)rimColor,(uiColor)rimColor }


#define createLine(id,x0,y0,x1,y1,width,color) \
{uiLineCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)x1, (u16)y1, NULL,\
(resID)width, (u8)0, (u8)0, (uiColor)color, (uiColor)INVALID_COLOR,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }

// font: RES_FONT_NUM_DEFAULT  RES_FONT_NUM2 RES_FONT_NUM3 RES_FONT_NUM4 RES_FONT_NUM5
#define createStringIcon(id,x0,y0,width,height,str,strAlign,fontColor,font)	\
 {uiStringIconCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL, \
(resID)INVALID_RES_ID, (u8)0, (u8)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,\
(resID)str, (u8)strAlign, (u8)strAlign, (charFont)font, (charFont)font, (uiColor)fontColor, (uiColor)fontColor, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }        

// font: RES_FONT_NUM_DEFAULT  RES_FONT_NUM2 RES_FONT_NUM3 RES_FONT_NUM4 RES_FONT_NUM5
#define createStringEx(id,x0,y0,width,height,str,strAlign,fontColor,font, strAlignSel,fontColorSel,fontSel,charH)	\
 {uiStringExCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL, \
(resID)INVALID_RES_ID, (u8)charH, (u8)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,\
(resID)str, (u8)strAlign, (u8)strAlignSel, (charFont)font, (charFont)fontSel, (uiColor)fontColor, (uiColor)fontColorSel, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }  

#define createStringRim(id,x0,y0,width,height,str,strAlign,fontColor,font,rimColor)	\
{uiStringIconCreate, (u16)id, (u16)style, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,\
(resID)str, (u8)strAlign, (u8)strAlign, (charFont)font, (charFont)font, (uiColor)fontColor, (uiColor)fontColor, (uiColor)rimColor,(uiColor)rimColor}

#define createProgressBar(id,x0,y0,width,height,bgColor,fgColor, align)	\
{uiProgressBarCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL,\
(resID)INVALID_RES_ID, (u8)align, (u8)0, (uiColor)bgColor, (uiColor)bgColor,  \
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)fgColor, (uiColor)fgColor, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR}

#define createProgressBarVer(id,x0,y0,width,height,bgColor,fgColor,rimColor, align)	\
{uiProgressBarVerCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL,\
(resID)INVALID_RES_ID, (u8)align, (u8)0, (uiColor)bgColor, (uiColor)bgColor,  \
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)fgColor, (uiColor)fgColor, (uiColor)rimColor,(uiColor)rimColor }
		
#define createStringItem(id,x0,y0,width,height,str,strAlign,fontColor,font,bgColor,strAlignSel,fontColorSel,fontSel,bgColorSel)	\
{uiStringIconCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (uiColor)bgColor, (uiColor)bgColorSel,\
(resID)str, (u8)strAlign, (u8)strAlignSel, (charFont)font, (charFont)fontSel, (uiColor)fontColor, (uiColor)fontColorSel, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }

#define createWidgetManage(id,minID,maxID,getResFun)	\
{uiWidgetManageCreate, (u16)id, (u16)0, (s16)0, (s16)0, (u16)minID, (u16)maxID, getResFun,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,  \
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }
 
#define createTips(id,x0,y0,width,height,str,strAlign,fontColor,font,bgColor)	\
{uiTipsCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (uiColor)bgColor, (uiColor)bgColor,\
(resID)str, (u8)strAlign, (u8)strAlign, (charFont)font, (charFont)font, (uiColor)fontColor, (uiColor)fontColor, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }
 
#define createCycle(id,x0,y0,width,height,radius,bgcolor,fgColor)	\
{uiCycleCreate, (u16)id, (u16)0, (s16)x0, (s16)y0, (u16)width, (u16)height, NULL,\
(resID)radius, (u8)0, (u8)0, (uiColor)bgcolor, (uiColor)bgcolor,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)fgColor, (uiColor)fgColor, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }


#define widgetEnd() \
{NULL, (u16)INVALID_WIDGET_ID, (u16)0, (s16)0, (s16)0, (u16)0, (u16)0, NULL,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,\
(resID)INVALID_RES_ID, (u8)0, (u8)0, (charFont)0, (charFont)0, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR, (uiColor)INVALID_COLOR,(uiColor)INVALID_COLOR }
 
typedef struct uiStrInfo_S
{
	charFont font;
	u8 strAlign;
	uiColor fontColor;
	uiColor bgColor;
	uiColor rimColor;
}uiStrInfo;
typedef struct uiResInfo_S
{
	resID   image;
	uiColor color;
	charFont font;
	u8 		strAlign;
	uiColor fontColor;
	uiColor bgColor;
	uiColor rimColor;
}uiResInfo; 
typedef struct uiStringExInfo_S
{
	resID   str;
	u32		num;
	u32		select;
}uiStringExInfo; 
/*******************************************************************************
* Function Name  : uiWinSendMsg
* Description    : uiWinSendMsg ： send message to a window
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSendMsg(winHandle handle,uiWinMsg* msg);
/*******************************************************************************
* Function Name  : uiWinSendMsgId
* Description    : uiWinSendMsgId ： send message id to a window
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSendMsgId(winHandle handle,u32 msg_id);
/*******************************************************************************
* Function Name  : uiWinSendMsgToParent
* Description    : uiWinSendMsgToParent ： send message to cur win's parent window
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSendMsgToParent(winHandle handle,uiWinMsg* msg);
/*******************************************************************************
* Function Name  : uiWinSendMsgToParent
* Description    : uiWinSendMsgToParent ： send message to cur win's parent window
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSendMsgIdToParent(winHandle handle,u32 msg_id);
/*******************************************************************************
* Function Name  : uiWinOverlapCmp
* Description    : uiWinOverlapCmp ： determine whether the two windows overlap
* Input          : uiRect* rect1,uiRect* rect2
* Output         : none                                            
* Return         : int : 0: not overlap, <0: overlap
*******************************************************************************/
int uiWinOverlapCmp(uiRect* rect1,uiRect* rect2);
/*******************************************************************************
* Function Name  : uiWinInsideCmp
* Description    : uiWinInsideCmp ： determine if child rect is in parent rect
* Input          : uiRect* parent,uiRect* child
* Output         : none                                            
* Return         : int : 0: child is inside parent, <0: child is not inside parent
*******************************************************************************/
int uiWinInsideCmp(uiRect* parent,uiRect* child);
/*******************************************************************************
* Function Name  : uiWinStringExRowCal
* Description    : uiWinStringExRowCal
* Input          : char *str
* Output         : none                                            
* Return         : u32 rows
*******************************************************************************/
u32 uiWinStringExRowCal(char *str);
/*******************************************************************************
* Function Name  : uiWinStringExGetByRow
* Description    : uiWinStringExGetByRow
* Input          : char *str, u32 row
* Output         : none                                            
* Return         : char*
*******************************************************************************/
char* uiWinStringExGetByRow(char *str, u32 row);
/*******************************************************************************
* Function Name  : uiWinStringExGetNext
* Description    : uiWinStringExGetNext
* Input          : char *str, u32 row
* Output         : none                                            
* Return         : char*
*******************************************************************************/
char* uiWinStringExGetNext(char *str);
/*******************************************************************************
* Function Name  : uiWinSetName
* Description    : uiWinSetName: set win's name
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinInterSection(uiRect* out,uiRect* win1,uiRect* win2);
/*******************************************************************************
* Function Name  : uiWinHasInvalidRect
* Description    : uiWinHasInvalidRect
* Input          : uiRect* win,uiRect* invalidRect
* Output         : none                                            
* Return         : int : 1: win has invalidRect, 0: win has not invalidRect
*******************************************************************************/
int uiWinHasInvalidRect(uiRect* win,uiRect* invalidRect);
/*******************************************************************************
* Function Name  : uiWinFreeInvalidRect
* Description    : uiWinFreeInvalidRect
* Input          : uiWinObj* pWin
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinFreeInvalidRect(uiWinObj* pWin);
/*******************************************************************************
* Function Name  : uiWinSetbgColor
* Description    : uiWinSetbgColor: set background color
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetbgColor(winHandle hWin,uiColor bgColor);
/*******************************************************************************
* Function Name  : uiWinSetfgColor
* Description    : uiWinSetfgColor: set font background color
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetfgColor(winHandle hWin,uiColor bgColor);
/*******************************************************************************
* Function Name  : uiWinSetCycleRadius
* Description    : uiWinSetCycleRadius:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetCycleRadius(winHandle hWin,s16 radius);
/*******************************************************************************
* Function Name  : uiWinSetRoundRectRadius
* Description    : uiWinSetRoundRectRadius:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetRoundRectRadius(winHandle hWin,s16 radius);
/*******************************************************************************
* Function Name  : uiWinSetVisible
* Description    : uiWinSetVisible: show or hide a widget
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetVisible(winHandle hWin,u32 visible);
/*******************************************************************************
* Function Name  : uiWinIsVisible
* Description    : uiWinIsVisible: return 1 if a widget is visible 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
u32 uiWinIsVisible(winHandle hWin);
/*******************************************************************************
* Function Name  : uiWinSetResid
* Description    : uiWinSetResid: set res id of a widget 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetResid(winHandle hWin,resID id);
/*******************************************************************************
* Function Name  : uiWinSetItemSelResid
* Description    : uiWinSetItemSelResid: set res id of a sel item widget 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetItemSelResid(winHandle hWin,resID id);
/*******************************************************************************
* Function Name  : uiWinSetItemSelResid
* Description    : uiWinSetItemSelResid: set res id of a sel item widget 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinUpdateResId(winHandle hWin);
/*******************************************************************************
* Function Name  : uiWinSetItemSelResid
* Description    : uiWinSetItemSelResid: set res id of a sel item widget 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinUpdateAllResId(void);
/*******************************************************************************
* Function Name  : uiWinSetStrInfor
* Description    : uiWinSetStrInfor: set str info of a widget
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetStrInfor(winHandle hWin,charFont font,u8 strAlign,uiColor fontColor);
/*******************************************************************************
* Function Name  : uiResInforInit
* Description    : uiResInforInit: uiResInfo* resInfo
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiResInforInit(uiResInfo* resInfo);
/*******************************************************************************
* Function Name  : uiWinSetSelectInfor
* Description    : uiWinSetSelectInfor:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetSelectInfor(winHandle hWin,uiResInfo* res_info);
/*******************************************************************************
* Function Name  : uiWinSetUnselectInfor
* Description    : uiWinSetUnselectInfor:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetUnselectInfor(winHandle hWin,uiResInfo* res_info);
/*******************************************************************************
* Function Name  : uiWinGetResSum
* Description    : uiWinGetResSum:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
u32 uiWinGetResSum(winHandle hWin);
/*******************************************************************************
* Function Name  : uiWinSetResSum
* Description    : uiWinSetResSum:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetResSum(winHandle hWin, u32 sum);
/*******************************************************************************
* Function Name  : uiWinSetResidByNum
* Description    : uiWinSetResidByNum:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetResidByNum(winHandle hWin,resID id,u32 num,u32 select);
/*******************************************************************************
* Function Name  : uiWinSetPorgressRate
* Description    : uiWinSetPorgressRate:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetPorgressRate(winHandle hWin,u32 rate);
/*******************************************************************************
* Function Name  : uiWinParentRedraw
* Description    : uiWinParentRedraw: send msg to parent widget to redraw win
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinParentRedraw(winHandle hWin);
/*******************************************************************************
* Function Name  : uiWinGetRelativePos
* Description    : uiWinGetRelativePos: get win relative position of win's parent
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinGetRelativePos(winHandle hWin,uiRect* pos);
/*******************************************************************************
* Function Name  : uiWinGetPos
* Description    : uiWinGetPos: get win's position
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinGetPos(winHandle hWin,uiRect* pos);
/*******************************************************************************
* Function Name  : uiWinUpdateInvalid
* Description    : uiWinUpdateInvalid: update rect to invalid rect
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinUpdateInvalid(winHandle hWin);
/*******************************************************************************
* Function Name  : uiWinSetProgressRate
* Description    : uiWinSetProgressRate: set progress rate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetProgressRate(winHandle hWin,u32 rate);
/*******************************************************************************
* Function Name  : uiWinSetName
* Description    : uiWinSetName: set win's name
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetName(winHandle hWin,char* name);
/*******************************************************************************
* Function Name  : uiWinGetCurrent
* Description    : uiWinGetCurrent: get cur win handle
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiWinGetCurrent(void);
/*******************************************************************************
* Function Name  : uiWinDefaultProc
* Description    : uiWinDefaultProc:all window's callback function must call this function, to handle basic messages 
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinDefaultProc(uiWinMsg* msg);
/*******************************************************************************
* Function Name  : uiWinCreate
* Description    : uiWinCreate: create window or widget
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiWinCreate(s16 x0, s16 y0, u16 width,u16 height, winHandle parent,uiWinCB cb,u32 size,u16 style);
/*******************************************************************************
* Function Name  : uiWinDestroy
* Description    : uiWinDestroy: destroy window or widget
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinDestroy(winHandle* hWin);
/*******************************************************************************
* Function Name  : uiWinTouchProcess
* Description    : uiWinTouchProcess: find which window the rectangular area belongs to,and send MSG_WIN_TOUCH
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
int uiWinTouchProcess(uiRect* rect,uint32 touchState);
/*******************************************************************************
* Function Name  : uiWinDrawProcess
* Description    : uiWinDrawProcess: draw all win
* Input          : none
* Output         : none                                            
* Return         : int : < 0 : no need to draw, 0: updata draw success
*******************************************************************************/
int uiWinDrawProcess(void);
/*******************************************************************************
* Function Name  : uiWinDestroyDeskTopChildWin
* Description    : uiWinDestroyDeskTopChildWin: 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinDestroyDeskTopChildWin(void);
/*******************************************************************************
* Function Name  : uiWinInit
* Description    : uiWinInit
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinInit(void);
/*******************************************************************************
* Function Name  : uiWinUninit
* Description    : uiWinUninit
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinUninit(void);
#endif
