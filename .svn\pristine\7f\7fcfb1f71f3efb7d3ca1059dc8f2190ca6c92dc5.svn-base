/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

#define  BATTERY_STEP     		3	
#define  BATTERY_INV     		12  //6
#define  BATTERY_OFS     		(30) //电池到IC引脚之间的压差,unit:mV
#define  BATTERY_LIMIT			(3480) //最小电压值
const static u16 batteryValueTable[BATTERY_STAT_MAX] = {3400,3500,3630,3730,3900,4000};//{3280,3480,3630,3800,4000,4100};//3.4v

typedef struct BAT_OP_S{
	u8 	step;
	u8 	batlevel;
	u16 bat_limit_value;
	u16 cur_bat_value;
	u16 cur_bgp_value;
}BAT_OP_T;

ALIGNED(4) static BAT_OP_T dev_bat_op;

/*******************************************************************************
* Function Name  : dev_battery_init
* Description    : dev_battery_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_battery_ioctrl(u32 op, u32 para)
{	
	if(op == DEV_BATTERY_READ)
	{

		if(hardware_setup.battery_en)
		{
			int value,i;
			dev_bat_op.cur_bat_value += hal_adcGetChannel(ADC_CH_MVOUT);
			dev_bat_op.cur_bgp_value += hal_adcGetChannel(ADC_CH_BGOP);
			dev_bat_op.step++;
			if(dev_bat_op.step >= BATTERY_STEP)
			{
				if(dev_bat_op.cur_bgp_value == 0)
					value = 0;
				else
					value = hal_adcVDDRTCCalculate(dev_bat_op.cur_bat_value,dev_bat_op.cur_bgp_value) + BATTERY_OFS;
				for(i = 0; i< BATTERY_STAT_MAX; i++)
				{
					if(value <= batteryValueTable[i])
						break;
				}
				//   deg_Printf("-%d-\n",value);
				if(dev_bat_op.batlevel != i)
				{
					if(dev_bat_op.batlevel == 0xff)
						dev_bat_op.batlevel = i;
					else
					{
						if(dev_bat_op.batlevel > i) //电量下降
						{
							if(value < batteryValueTable[dev_bat_op.batlevel] - BATTERY_INV)
								dev_bat_op.batlevel = i;
						}else ////电量上升
						{
							if(value > batteryValueTable[dev_bat_op.batlevel] + BATTERY_INV)
								dev_bat_op.batlevel = i;
						}
						
					}
				}
				dev_bat_op.step = 0;
				dev_bat_op.cur_bat_value = 0;
				dev_bat_op.cur_bgp_value = 0;

				if(para)
					*(u32 *)para = dev_bat_op.batlevel;
				return 0;
			}
		}else{
			dev_bat_op.batlevel = BATTERY_STAT_MAX;
			if(para)
				*(u32 *)para = dev_bat_op.batlevel;	
			return 0;			
		}
	}else if(op == DEV_BATTERY_WRITE)
	{
		
	}

	return -1;
}
/*******************************************************************************
* Function Name  : dev_battery_init
* Description    : dev_battery_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_battery_init(void)
{
	if(hardware_setup.battery_en)
	{
		memset((void*)&dev_bat_op, 0, sizeof(dev_bat_op));
		dev_bat_op.bat_limit_value = BATTERY_LIMIT;
		dev_bat_op.batlevel		   = 0xff;
		hal_batDetectEnable(1);
		
		if(hal_wki0Read() == 0)
		{
			u32 para = 0;
			int i = BATTERY_STEP;
			while((dev_battery_ioctrl(DEV_BATTERY_READ, (u32)&para) < 0) && i--);
			if(para == BATTERY_STAT_0)
			{
				//wki read
				//dev_usb_ioctrl(DEV_USB_DEV_READ,(u32)&para);
				if(para == 0)
				{
					deg_Printf("[DEV] bat low power\n");
					return -1;
				}			
			}	
		}
	
	}
	return 0;

}
