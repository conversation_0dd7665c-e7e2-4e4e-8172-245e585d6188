/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

#define _MSC_ENUM_DEBG_		




/*******************************************************************************
* Function Name  : msc_init
* Description    : msc_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_msc_init(void *handle)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	if(pHusb_handle->msc.ctyp != CLASS_MASSSTORAGE)
		return false;
	pHusb_handle->usbsta.device_sta |= USB_MSC_ATECH;
	if(pHusb_handle->usbsta.ch == USB20_CH)	
	{
		deg_Printf("HUSB20 MSC INIT.\n");
		u32 index_temp	= XSFR_USB20_SIE_EPS;
		XSFR_USB20_SIE_EPS 	= HUSB20_TXEP_MSC;
		XSFR_USB20_SIE_EPTX_CTRL0	= HUSB_EPTX_CLRDATATOG|HUSB_EPTX_FLUSHFIFO;
		XSFR_USB20_SIE_EPTX_CTRL1	= 0;
		XSFR_USB20_SIE_TXPKGMAXL	= (1024>>0)&0xff;
		XSFR_USB20_SIE_TXPKGMAXH	= (1024>>8)&0xff;
		USB20_TxType 	= ( TT_BULK | pHusb_handle->msc.epout);
		XSFR_USB20_SIE_EPS		= HUSB20_RXEP_MSC;
		XSFR_USB20_SIE_EPRX_CTRL0	= HUSB_EPRX_CLRDATATOG|HUSB_EPRX_FLUSHFIFO;
		XSFR_USB20_SIE_EPRX_CTRL1	= 0;
		XSFR_USB20_SIE_RXPKGMAXL	= (1024>>0)&0xff;
		XSFR_USB20_SIE_RXPKGMAXH	= (1024>>8)&0xff;
		XSFR_USB20_SIE_RXTYPE 	= (TT_BULK|(pHusb_handle->msc.epin&0x7f));//SET BULK AND ENDPIONT
		XSFR_USB20_SIE_EPS		= index_temp;
		XSFR_USB20_SDR_DMA_EN &= ~BIT(HUSB20_TXEP_MSC);  //使用sram通道
		(&XSFR_USB20_EP1_TXADDR)[HUSB20_TXEP_MSC*2-2]  = (u32)_USB20_MSC_TXFIFO_;
		(&XSFR_USB20_EP1_RXADDR)[HUSB20_RXEP_MSC*2-2]  = (u32)_USB20_MSC_RXFIFO_;
	}else
	{
		deg_Printf("HUSB11 MSC INIT.\n");
		u32 index_temp	= XSFR_USB11_SIE_EPS;
		XSFR_USB11_SIE_EPS 	= HUSB11_TXEP_MSC;
		XSFR_USB11_SIE_EPTX_CTRL0	= HUSB_EPTX_CLRDATATOG|HUSB_EPTX_FLUSHFIFO;
		XSFR_USB11_SIE_EPTX_CTRL1	= 0;
		XSFR_USB11_SIE_TXPKGMAX	= 250; 
		XSFR_USB11_SIE_TXTYPE 	= ( TT_BULK | pHusb_handle->msc.epout);
		XSFR_USB11_SIE_EPS		= HUSB11_RXEP_MSC;
		XSFR_USB11_SIE_EPRX_CTRL0	= HUSB_EPRX_CLRDATATOG|HUSB_EPRX_FLUSHFIFO;
		XSFR_USB11_SIE_EPRX_CTRL1	= 0;
		XSFR_USB11_SIE_RXPKGMAX	= 250;
		XSFR_USB11_SIE_RXTYPE 	= (TT_BULK|(pHusb_handle->msc.epin&0x7f));//SET BULK AND ENDPIONT
		XSFR_USB11_SIE_EPS		= index_temp;
		(&XSFR_USB11_EP1_TXADDR)[HUSB11_TXEP_MSC*2-2]  = (u32)_USB11_MSC_TXFIFO_;
		(&XSFR_USB11_EP1_RXADDR)[HUSB11_RXEP_MSC*2-2]  = (u32)_USB11_MSC_RXFIFO_;	
	}
	return true;
}
/*******************************************************************************
* Function Name  : epbulk_send_dat
* Description    : epbulk_send_dat
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool epbulk20_send_dat(u8 *buf, u32 len)
{
	
	u32 fifoadr = (&XSFR_USB20_EP1_TXADDR)[HUSB20_TXEP_MSC*2-2];
	u32 blksize = (hx330x_usb20_HighSpeed() )? BULK_MAX_SIZE_HS : BULK_MAX_SIZE_FS; 
	u32 tlen;
	bool ret = true;
	u32 index_temp = XSFR_USB20_SIE_EPS;
	XSFR_USB20_SIE_EPS = HUSB20_TXEP_MSC; 
	hx330x_intHECriticalInit();
	
	
	do{
		tlen = (len > blksize)?blksize:len;
		if(((u32)buf)&0x02000000)  //SDRAM通路
		{
			hx330x_mcpy0_sdram2gram((void *)fifoadr, (void *)buf, tlen);
		}else
		{
			(&XSFR_USB20_EP1_TXADDR)[HUSB20_TXEP_MSC*2-2] = (u32)buf;
		}

		hx330x_intHECriticalEnter();
		XSFR_USB20_EPINTLEN = tlen;
		XSFR_USB20_EPINT    = BIT(HUSB20_TXEP_MSC);
		XSFR_USB20_SIE_EPTX_CTRL0   = HUSB_EPTX_TXPKTRDY; 

		hx330x_intHECriticalExit();
	#ifdef _MSC_ENUM_DEBG_
		debgbuf(buf,tlen);
	#endif
		
		s32 i = 0x2fffff;
		while(1)
		{
			hx330x_wdtClear();
			u32 csr1 = XSFR_USB20_SIE_EPTX_CTRL0;
			if(!(csr1 & (HUSB_EPTX_TXPKTRDY|HUSB_EPTX_FIFONOTEMPTY)))
			{
				ret = true;
				break;
			}
			if(csr1 & HUSB_EPTX_TXERROR)
			{
				deg_Printf("-HUSB20 MSC TX ERROR\n");
				XSFR_USB20_SIE_EPTX_CTRL0 = 0;
				ret = false;
				break;
				
			}
			if(csr1 & HUSB_EPTX_RXSTALL)
			{
				deg_Printf("-HUSB20 MSC TX STALL\n");
				ret = false;
				break;
			}
			if((i--) <= 0)
			{
				deg_Printf("-HUSB20 MSC TX Tout\n");
				ret = false;
				break;
			}
		}
		len -= tlen;
		buf += tlen;
		if(ret == false)
			break;
	}while(len);
	(&XSFR_USB20_EP1_TXADDR)[HUSB20_TXEP_MSC*2-2] = (u32)fifoadr; //恢复fifo地址
	XSFR_USB20_SIE_EPS = index_temp;
	return ret;
}
/*******************************************************************************
* Function Name  : epbulk_send_dat
* Description    : epbulk_send_dat
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool epbulk20_recieve_dat(u8 *buf, u32 len)
{
	u32 fifoadr = (&XSFR_USB20_EP1_RXADDR)[HUSB20_RXEP_MSC*2-2];
	bool ret = true;
	u32 index_temp = XSFR_USB20_SIE_EPS;
	u32 rlen;
	XSFR_USB20_SIE_EPS = HUSB20_RXEP_MSC; 
	
	do{
		if(((u32)buf&0x02000000) == 0)  //SRAM通路
		{
			(&XSFR_USB20_EP1_RXADDR)[HUSB20_RXEP_MSC*2-2] = (u32)buf;
			//XSFR_USB20_SDR_DMA_EN &= ~BIT(epx);
		}
		XSFR_USB20_SIE_EPRX_CTRL0 |= HUSB_EPRX_REQPKT;//request pkt,send a in packet
		while (1)
        {
            u32 csr1 = XSFR_USB20_SIE_EPRX_CTRL0;
			//deg_Printf("csr1:%x\n", csr1);
			hal_wdtClear();
            if (csr1 & HUSB_EPRX_ERROR){
            	XSFR_USB20_SIE_EPRX_CTRL0	=	0;
            	deg_Printf("-HUSB20 MSC RX Err\n");
                ret = false;
				break;
            }
            if (csr1 & HUSB_EPRX_SENTSTALL){
            	deg_Printf("-HUSB20 MSC RX stall\n");
                ret = false;
				break;
            }

            if (csr1 & HUSB_EPRX_RXPKTRDY){
                rlen = (XSFR_USB20_SIE_EPXRXCNTH << 8) + XSFR_USB20_SIE_EPXRXCNTL;
				if((u32)buf&0x02000000)  //SDRAM通路
				{
					//debg("1");
					hx330x_mcpy0_sdram2gram((void *)buf, (void *)fifoadr, rlen);
				}	
			#ifdef _MSC_ENUM_DEBG_
				debgbuf(buf,rlen);
			#endif	
                (len > rlen)?( len -= rlen):(len = 0);
                buf += rlen;
            	XSFR_USB20_SIE_EPRX_CTRL0 |= HUSB_EPRX_FLUSHFIFO;//flush the next packet to be read from the endpoint Rx FIFO.
				ret = true;
				break;
            }
        }
		if(ret == false)
			break;
	}while(len);
	(&XSFR_USB20_EP1_RXADDR)[HUSB20_RXEP_MSC*2-2] = fifoadr;
	XSFR_USB20_SIE_EPS = index_temp; 
	return ret;
}
/*******************************************************************************
* Function Name  : epbulk_send_dat
* Description    : epbulk_send_dat
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool epbulk11_send_dat(u8 *buf, u32 len)
{
	u32 fifoadr = (&XSFR_USB11_EP1_TXADDR)[HUSB11_TXEP_MSC*2-2];
	u32 blksize = BULK_MAX_SIZE_FS; 
	u32 tlen;
	bool ret = true;
	u32 index_temp = XSFR_USB11_SIE_EPS;
	XSFR_USB11_SIE_EPS = HUSB11_TXEP_MSC; 
	hx330x_intCriticalInit();

	do{
		tlen = (len > blksize)?blksize:len;
		if(((u32)buf)&0x02000000)  //SDRAM通路
		{
			hx330x_mcpy0_sdram2gram((void *)fifoadr, (void *)buf, tlen);
		}else
		{
			(&XSFR_USB11_EP1_TXADDR)[HUSB11_TXEP_MSC*2-2] = (u32)buf;
		}

		hx330x_intCriticalEnter();
		XSFR_USB11_EPINTLEN = tlen;
		XSFR_USB11_EPINT    = BIT(HUSB20_TXEP_MSC);
		XSFR_USB11_SIE_EPTX_CTRL0   = HUSB_EPTX_TXPKTRDY; 

		hx330x_intCriticalExit();
	#ifdef _MSC_ENUM_DEBG_
		debgbuf(buf,tlen);
	#endif	
		
		s32 i = 0x2fffff;
		while(1)
		{
			hx330x_wdtClear();
			u32 csr1 = XSFR_USB11_SIE_EPTX_CTRL0;
			if(!(csr1 & (HUSB_EPTX_TXPKTRDY|HUSB_EPTX_FIFONOTEMPTY)))
			{
				ret = true;
				break;
			}
			if(csr1 & HUSB_EPTX_TXERROR)
			{
				deg_Printf("-HUSB11 MSC TX ERROR\n");
				XSFR_USB11_SIE_EPTX_CTRL0 = 0;
				ret = false;
				break;
			}
			if(csr1 & HUSB_EPTX_RXSTALL)
			{
				deg_Printf("-HUSB11 MSC TX STALL\n");
				ret = false;
				break;
			}
			if((i--) <= 0)
			{
				deg_Printf("-HUSB11 MSC TX Tout\n");
				ret = false;
				break;
			}
		}
		if(ret == false)
		{
			break;
		}
		len -= tlen;
		buf += tlen;
	}while(len);
	(&XSFR_USB11_EP1_TXADDR)[HUSB11_TXEP_MSC*2-2] = (u32)fifoadr; //恢复fifo地址
	XSFR_USB11_SIE_EPS = index_temp;
	return ret;
}
/*******************************************************************************
* Function Name  : epbulk_send_dat
* Description    : epbulk_send_dat
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool epbulk11_recieve_dat(u8 *buf, u32 len)
{
	u32 fifoadr = (&XSFR_USB11_EP1_RXADDR)[HUSB11_RXEP_MSC*2-2];
	bool ret = true;
	u32 index_temp = XSFR_USB11_SIE_EPS;
	u32 rlen;
	XSFR_USB11_SIE_EPS = HUSB11_RXEP_MSC; 
	
	do{
		if(((u32)buf&0x02000000) == 0)  //SRAM通路
		{
			(&XSFR_USB11_EP1_RXADDR)[HUSB11_RXEP_MSC*2-2] = (u32)buf;
		}
		//else
		//{
		//	(&XSFR_USB20_EP1_TXADDR)[HUSB20_TXEP_MSC*2-2] = (u32)buf;
		//}
		XSFR_USB11_SIE_EPRX_CTRL0 |= HUSB_EPRX_REQPKT;//request pkt,send a in packet
		while (1)
        {
            u32 csr1 = XSFR_USB11_SIE_EPRX_CTRL0;
			//debg("3");
            if (csr1 & HUSB_EPRX_ERROR){
            	XSFR_USB11_SIE_EPRX_CTRL0	=	0;
            	deg_Printf("-HUSB11 MSC RX Err\n");
                ret = false;
				break;
            }
            if (csr1 & HUSB_EPRX_SENTSTALL){
            	deg_Printf("-HUSB11 MSC RX stall\n");
                ret = false;
				break;
            }

            if (csr1 & HUSB_EPRX_RXPKTRDY){
                rlen = (XSFR_USB11_SIE_EPXRXCNTH << 8) + XSFR_USB11_SIE_EP0RXCNTL;
				if((u32)buf&0x02000000)  //SDRAM通路
				{
					//debg("1");
					hx330x_mcpy0_sdram2gram((void *)buf, (void *)fifoadr, rlen);
				}	
			#ifdef _MSC_ENUM_DEBG_
				debgbuf(buf,rlen);
			#endif		
                (len > rlen)?( len -= rlen):(len = 0);
                buf += rlen;
            	XSFR_USB11_SIE_EPRX_CTRL0 |= HUSB_EPRX_FLUSHFIFO;//flush the next packet to be read from the endpoint Rx FIFO.
				break;
            }
        }
		if(ret == false)
			break;
	}while(len);
	(&XSFR_USB11_EP1_RXADDR)[HUSB11_RXEP_MSC*2-2] = fifoadr;
	XSFR_USB11_SIE_EPS = index_temp; 
	return ret;
}
/*******************************************************************************
* Function Name  : epbulk_send_dat
* Description    : epbulk_send_dat
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool epbulk_send_dat(u8 ch, u8* buf,u32 len)
{
	if(ch == USB20_CH)	
	{
		return epbulk20_send_dat(buf, len);
	}else if(ch == USB11_CH)	
	{
		return epbulk11_send_dat(buf, len);
	}else
		return false;
}
/*******************************************************************************
* Function Name  : epbulk_receive_dat
* Description    : epbulk_receive_dat
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool epbulk_receive_dat(u8 ch, u8* buf, u32 len)
{
	if(ch == USB20_CH)	
	{
		return epbulk20_recieve_dat(buf, len);
	}else if(ch == USB11_CH)	
	{
		return epbulk11_recieve_dat(buf, len);
	}else{
		return false;
	}
}
/*******************************************************************************
* Function Name  : cbw_init
* Description    : cbw_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void cbw_init(CBW *p_cbw)
{
     hx330x_bytes_memset((u8 *)p_cbw ,0,31);
     p_cbw->cbw_signature 	= CBW_SIGNATURE;
     p_cbw->cbw_tag 		= CBW_TAG;
}

/*******************************************************************************
* Function Name  : spc_inquiry
* Description    : spc_inquiry
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool spc_inquiry(HUSB_HANDLE *pHusb_handle)
{
#ifdef _MSC_ENUM_DEBG_
	deg_Printf("====  spc_inquiry  ====\n");
#endif	
	
	CBW *p_cbw = (CBW *)pHusb_handle->tempbuf;
	cbw_init(p_cbw);
    p_cbw->cbw_data_xferlen = 0x00000024;
    p_cbw->cbw_flag			= 0x80;
    //p_cbw->cbw_lun			= 0; //默认只取lun1
    p_cbw->cbw_cbdlen 		= 0x06;
    p_cbw->operation_code 	= INQUIRY;
    p_cbw->cbw_cblba[2]   	= 0x24;

    //send cmd
    if (!epbulk_send_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw, 31)){
        return false;
    }

    //receive device data
    if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw,p_cbw->cbw_data_xferlen)){
        return false;
    }
    //receive device status
    if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw,0)){
        return false;
    }
    return true;
}
/*******************************************************************************
* Function Name  : spc_test_unit_rdy
* Description    : spc_test_unit_rdy
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool spc_test_unit_rdy(HUSB_HANDLE *pHusb_handle)
{
#ifdef _MSC_ENUM_DEBG_
	deg_Printf("====  spc_test_unit_rdy  ====\n");
#endif	
	CBW *p_cbw = (CBW *)pHusb_handle->tempbuf;
	cbw_init(p_cbw);
    //p_cbw->cbw_lun = 0;
    p_cbw->cbw_cbdlen = 0x06; //sizeof(INQUIRY_SPC);

    if (!epbulk_send_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw, 31)){
        return false;
    }

    if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw,0)){
        return false;
    }
	pHusb_handle->usbsta.msc_online = !((bool )((CSW *)p_cbw)->csw_status);
    return true;
}
/*******************************************************************************
* Function Name  : spc_request_sense
* Description    : spc_request_sense
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool spc_request_sense(HUSB_HANDLE *pHusb_handle)
{
#ifdef _MSC_ENUM_DEBG_
	deg_Printf("====  spc_request_sense  ====\n");
#endif	
	CBW *p_cbw = (CBW *)pHusb_handle->tempbuf;
	cbw_init(p_cbw);
	p_cbw->cbw_data_xferlen = 0x00000012;
	p_cbw->cbw_flag 		= 0x80;
	//p_cbw->cbw_lun 			= 0;
	p_cbw->cbw_cbdlen 		= 0x0C;	//sizeof(REQUEST_SENSE_SPC);
	p_cbw->operation_code   = REQUEST_SENSE;
	p_cbw->cbw_cblba[2]     = 0x12;

	if (!epbulk_send_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw, 31)){
		return false;
	}

	if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw,0)){
		return false;
	}

	if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw,0)){
		return false;
	}
	return true;
}

/*******************************************************************************
* Function Name  : rbc_read_capacity
* Description    : rbc_read_capacity
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool rbc_read_capacity(HUSB_HANDLE *pHusb_handle)
{
#ifdef _MSC_ENUM_DEBG_
	deg_Printf("====  rbc_read_capacity  ====\n");
#endif	
	CBW *p_cbw = (CBW *)pHusb_handle->tempbuf;
	cbw_init(p_cbw);

	p_cbw->cbw_data_xferlen = 8;
	//p_cbw->cbw_lun			= 0;
	p_cbw->cbw_flag			= 0x80;
	p_cbw->cbw_cbdlen 		= 0x0a;
	p_cbw->operation_code 	= READ_CAPACITY;

	if (!epbulk_send_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw, 31)){
		return false;
	}

	if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw,p_cbw->cbw_data_xferlen)){
		return false;
	}
	pHusb_handle->usbsta.msc_cap = (pHusb_handle->tempbuf[0] << 24) + (pHusb_handle->tempbuf[1] << 16) + (pHusb_handle->tempbuf[2] << 8) + (pHusb_handle->tempbuf[3] << 0);
	pHusb_handle->usbsta.msc_slen = (pHusb_handle->tempbuf[4] << 24) + (pHusb_handle->tempbuf[5] << 16) + (pHusb_handle->tempbuf[6] << 8) + (pHusb_handle->tempbuf[7] << 0);
	if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw,0)){
		pHusb_handle->usbsta.msc_cap = 0;
		return false;
	}		
	deg_Printf("CAP:%dG%dM%dK\n",pHusb_handle->usbsta.msc_cap>>30,(pHusb_handle->usbsta.msc_cap>>20)&0x3ff,(pHusb_handle->usbsta.msc_cap>>10)&0x3ff);
	deg_Printf("SLEN:%d\n",pHusb_handle->usbsta.msc_slen);
    return true;
}
/*******************************************************************************
* Function Name  : rbc_read_capacity
* Description    : rbc_read_capacity
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool spc_StartStopUnit(HUSB_HANDLE *pHusb_handle)
{
#ifdef _MSC_ENUM_DEBG_
	deg_Printf("====  spc_StartStopUnit  ====\n");
#endif	
	CBW *p_cbw = (CBW *)pHusb_handle->tempbuf;
	cbw_init(p_cbw);

	//p_cbw->cbw_lun 			= 0;
    p_cbw->cbw_cbdlen		= 0x06;
	p_cbw->operation_code	= STOP_START_UNIT;
    p_cbw->cbw_cblba[2] 	= 0x02;

	if (!epbulk_send_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw, 31)){
		return false;
	}

	if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw,0)){
		return false;
	}		
    return true;
}
/*******************************************************************************
* Function Name  : rbc_read_lba
* Description    : rbc_read_lba
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool rbc_read_lba(void *handle,u8 *buffer,u32 lba,u16 len)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	CBW *p_cbw = (CBW *)pHusb_handle->tempbuf;
	cbw_init(p_cbw);
	p_cbw->cbw_data_xferlen = len << 9;
	p_cbw->cbw_flag         = 0x80;          //bmCBWFlags
	p_cbw->cbw_lun          = 0;//bCBWLUN
	p_cbw->cbw_cbdlen       = 10;            //bCBWCBLength

	//CBWCB
	p_cbw->operation_code   = READ_DATA;
	p_cbw->cbw_cblba[3]     = (lba)&0xff;
	p_cbw->cbw_cblba[2]     = (lba>>8)&0xff;
	p_cbw->cbw_cblba[1]     = (lba>>16)&0xff;
	p_cbw->cbw_cblba[0]     = (lba>>24)&0xff;

	p_cbw->cbw_cblength     =  ((len&0xff)<<8)|((len>>8)&0xff);   

	//send read cmd
	if (!epbulk_send_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw, 31)){
		return false;
	}
	//receive data
    if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,buffer,len<<9)){
        return false;
    }

    //receive status
    if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw,0)){
		return false;
	}

    return true;
}

/*******************************************************************************
* Function Name  : rbc_read_lba
* Description    : rbc_read_lba
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool rbc_write_lba(void *handle,u8 *buffer,u32 lba,u16 len)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	CBW *p_cbw = (CBW *)pHusb_handle->tempbuf;
	cbw_init(p_cbw);
    //CBW
    p_cbw->cbw_data_xferlen = len << 9;
    //p_cbw->cbw_flag        	= 0x00;             //bmCBWFlags
    //p_cbw->cbw_lun         	= 0;   //bCBWLUN
    p_cbw->cbw_cbdlen      	= 10;               //bCBWCBLength
    //CBWCB
    p_cbw->operation_code   = WRITE_DATA;            
    p_cbw->cbw_cblba[3]     = (lba)&0xff;
    p_cbw->cbw_cblba[2]     = (lba>>8)&0xff;
    p_cbw->cbw_cblba[1]     = (lba>>16)&0xff;
    p_cbw->cbw_cblba[0]     = (lba>>24)&0xff;

    p_cbw->cbw_cblength     = ((len&0xff)<<8)|((len>>8)&0xff);        

    if (!epbulk_send_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw, 31)){
    	
        return false;
    }

    if (!epbulk_send_dat(pHusb_handle->usbsta.ch,buffer,len<<9)){

        return false;
    }

    if (!epbulk_receive_dat(pHusb_handle->usbsta.ch,(u8*)p_cbw,0))
    {
    	return false;
    }

    return true;
}
/*******************************************************************************
* Function Name  : udisk_cap
* Description    : udisk_cap
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u32 udisk_cap(void *handle)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	return pHusb_handle->usbsta.msc_cap;
}
/*******************************************************************************
* Function Name  : udisk_online
* Description    : udisk_online
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool udisk_online(void *handle)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	return 	(bool)pHusb_handle->usbsta.msc_online;
}

/*******************************************************************************
* Function Name  : udisk_online
* Description    : udisk_online
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool enum_mass_dev(void *handle)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	if((pHusb_handle->usbsta.ch != USB20_CH) && (pHusb_handle->usbsta.ch != USB11_CH))
	{
		deg_Printf("MSC HANDLE CH not support\n");
		return false;
	}
	if((!(pHusb_handle->usbsta.device_type & DEVICE_TYPE_MSC)) || (!(pHusb_handle->usbsta.device_sta & USB_MSC_ATECH)))
	{
		return false;
	}
	
	if((pHusb_handle->usbsta.msc_online)&&(pHusb_handle->usbsta.device_sta & USB_MSC_TRAN))   //only support one msc dev online
	{
		return true;
	}
#ifdef _MSC_ENUM_DEBG_
	deg_Printf("*******************************\n");
	deg_Printf("[ enum_mass_dev ]\n");
	deg_Printf("*******************************\n");
#endif		
	if(!spc_inquiry(pHusb_handle)){
	#ifdef _MSC_ENUM_DEBG_
		deg_Printf("MSC: <inquiry err>\n");
	#endif
		return false;	
	}
	
	u32 i;
	for(i = 0; i < 100; i++){
		if(!spc_test_unit_rdy(pHusb_handle)){
		#ifdef _MSC_ENUM_DEBG_
			deg_Printf("MSC: <TEST UNIT err>\n");
		#endif
			return false;			
		}
		if(!spc_request_sense(pHusb_handle)){
		#ifdef _MSC_ENUM_DEBG_
			deg_Printf("MSC: <sense err>\n");
		#endif
			return false;
		}	
		if(pHusb_handle->usbsta.msc_online){
			break;	
		}		
	}
	if(pHusb_handle->usbsta.msc_online == 0){
		return false;
	}	
	if(!rbc_read_capacity(pHusb_handle)){
	#ifdef _MSC_ENUM_DEBG_	
		deg_Printf("MSC: <capacity err>\n");
	#endif
		return false;		
	}
	pHusb_handle->usbsta.device_sta |= USB_MSC_TRAN;
	pHusb_handle->usbsta.udisk_rd 		= rbc_read_lba;
	pHusb_handle->usbsta.udisk_wr 		= rbc_write_lba;
	pHusb_handle->usbsta.udisk_cap  	= udisk_cap;
	pHusb_handle->usbsta.udisk_online 	= udisk_online;
	if(pHusb_handle->usbsta.ch == USB20_CH)
	{
		deg_Printf("HUSB20 MSC ATECH OK\n");
	}else
	{
		deg_Printf("HUSB11 MSC ATECH OK\n");
	}
	
	return true;
}