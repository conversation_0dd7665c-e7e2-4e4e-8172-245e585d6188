/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

// sd0 buffer
GRAM_SECTION u8 sd0RamBuffer[512];

#define sdRamBuffer         sd0RamBuffer
#define hx330x_sdInit       hx330x_sd0Init
#define hx330x_sdUninit     hx330x_sd0Uninit
#define hx330x_sdSendCmd    hx330x_sd0SendCmd
#define hx330x_sdBusSet     hx330x_sd0BusSet
#define hx330x_sdRecv       hx330x_sd0Recv
#define hx330x_sdSend       hx330x_sd0Send
#define hx330x_sdWaitDAT0   hx330x_sd0WaitDAT0
#define hx330x_sdWaitPend   hx330x_sd0WaitPend
#define hx330x_sdGetRsp     hx330x_sd0GetRsp
#define hx330x_sdCRCCheck   hx330x_sd0CRCCheck
#define hx330x_sdBuffer     hx330x_sd0Buffer
#define hx330x_sdClkSet     hx330x_sd0ClkSet

typedef enum{
	//_SD_STOP_ = 0,	//SD_CMD_STOP
	_SD_RESET_ = 0,		//SD_CMD_IDLE
	_SD_IF_CON_,	//SD_CMD_IF_CON
	_SD_ACTIVE_55_,	//SD_CMD_APP
	_SD_ACTIVE_41_, //SD_ACMD_APP_OP_COND
	
	_MMC_RESET_,	//SD_CMD_IDLE
	_MMC_ACTIVE_1_, //MMC_CMD_OP_COND
	//_SD_SWH_18V,	//SD_CMD_READ_DAT
	_SD_ALLCID_,	//SD_CMD_ALLSEND_CID
	_SD_GET_RCA_,	//SD_CMD_SEND_RCA
	_MMC_SET_RCA_,	//SD_CMD_SEND_RCA
	_SD_GET_CSD_,	//SD_CMD_SEND_CSD
	_SD_SELECT_CARD_, //SD_CMD_SELECT_CARD
	_SD_SET_BLOCK_LEN_, //SD_CMD_SETBLOCKLEN
	
	_SD_PUDIS_55_,	//SD_CMD_APP
	_SD_PUDIS_42_,	//SD_ACMD_SET_CLR_CARD_SELECT
	_SD_LINE_CFG_55_,	//SD_CMD_APP
	_SD_LINE_CFG_6_,	//SD_ACMD_SETBUSWIDTH
	//_SD_LINE_TST_17_,	//SD_CMD_READ_SINGLE_BLOCK

	_SD_FUN_CHECK_,		//SD_CMD_SWITCH_FUNC
	_SD_FUN_SET_,		//SD_CMD_SWITCH_FUNC
	_SD_FUN_VERIFY_,		//SD_CMD_READ_SINGLE_BLOCK
	
	_MMC_FUN_CHECK_,		//SD_CMD_IF_CON
	_MMC_LINE_SET_,			//SD_CMD_SWITCH_FUNC
	_MMC_LINE_TST_17_,		//SD_CMD_READ_SINGLE_BLOCK
	
}SD_IDENT_IDX;

ALIGNED(4) static SDC_STAT_T halSDCState;
/*******************************************************************************
* Function Name  : sd_cmd8_ack
* Description    : sd_cmd8_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_cmd8_ack(void)
{
	if(halSDCState.SdCmdOp.arg == hx330x_sdGetRsp())
	{
		halSDCState.SdCmdOp.next_idx = _SD_ACTIVE_55_;
		halSDCState.loopcnt 		 = 2000;
		set_chrt(halSDCState.chrt,SD_CMD8_CHRT,SD_CMD8_SPT); //支持CMD8
		set_chrt(halSDCState.chrt,SD_FUNC_TYPE,SD_FUNC_SDR25); //支持CMD8
	}
	
	return 0;
}
/*******************************************************************************
* Function Name  : sd_acmd41_probe
* Description    : sd_acmd41_probe
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_acmd41_probe(void)
{
	//支持CMD8尝试用OCR_HC_CCS支持
	if(SD_CMD8_SPT == get_chrt(halSDCState.chrt,SD_CMD8_CHRT))
	{
		halSDCState.SdCmdOp.arg |= OCR_HC_CCS;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sd_acmd41_ack
* Description    : sd_acmd41_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_acmd41_ack(void)
{
	//获得应答OCR
	u32 ocr = hx330x_sdGetRsp();
	set_chrt(halSDCState.chrt,CARD_TYPE,CARD_SD); //标志SD卡
	if((ocr & 0x00ff8000) != 0x00ff8000)
	{
		return -1;
	}
	//deg_Printf("ocr:%x\n",ocr);
	//是否ready
	if(ocr & OCR_ALL_READY)
	{
		halSDCState.SdCmdOp.next_idx = _SD_ALLCID_;
		if(ocr & OCR_HC_CCS){			//支持高容量
			set_chrt(halSDCState.chrt,SD_CAP_TYPE,SD_HCS); 
		}
		return 0;
	}	
	//未激活
	//sd_notative++;
//	if(sd_notative > 250)
//	{
		//sd_notative = 0;
		//se_card_pwr_reset(pcard->ch);
//		//sdtrb[_SD_ACTIVE_41].nsta = _SD_RESET_SD;
//		return false;
//	}
	hal_sysDelayMS(1);	
	return 0;
}
/*******************************************************************************
* Function Name  : mmc_cmd1_ack
* Description    : mmc_cmd1_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int mmc_cmd1_ack(void)
{
	//获得应答OCR
	u32 ocr = hx330x_sdGetRsp();	
	set_chrt(halSDCState.chrt,CARD_TYPE,CARD_MMC); //标志SD卡
	if((ocr & 0x00ff8000) != 0x00ff8000)
	{
		return -1;
	}
	//是否ready
	if(ocr & OCR_ALL_READY)
	{
		halSDCState.SdCmdOp.next_idx = _SD_ALLCID_;
		if(ocr & OCR_HC_CCS){			//支持高容量
			set_chrt(halSDCState.chrt,SD_CAP_TYPE,SD_HCS); //支持高容量
		}
		return 0;
	}		
	return 0;
}
/*******************************************************************************
* Function Name  : sd_cmd2_ack
* Description    : sd_cmd2_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_cmd2_ack(void)
{
	if(CARD_MMC == get_chrt(halSDCState.chrt,CARD_TYPE))
	{
		halSDCState.dwRCA = 1;
		halSDCState.SdCmdOp.next_idx = _MMC_SET_RCA_;
	}

	return 0;
}
/*******************************************************************************
* Function Name  : sd_cmd3_ack
* Description    : sd_cmd3_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_cmd3_ack(void)
{
	halSDCState.dwRCA = hx330x_sdGetRsp();
	return 0;
}
/*******************************************************************************
* Function Name  : sd_cmd3_ack
* Description    : sd_cmd3_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_cmd9_ack(void)
{	
	
	u8  rdBlkLen, cSizeMult,*buf;
	u16 cSize; 
	u16 ccc;
	
	//判断CMD6支持与否
	
    buf = hx330x_sdBuffer(); 
	//CSD结构体128bit, 127:0 -->buf[3]-buf[2]-buf[1]-buf[0]-buf[7]-buf[6]-buf[5]-buf[4]-buf[11]-buf[10]-buf[9]-buf[8]-buf[15]-buf[14]-buf[13]-buf[12]
	//ccc BIT[95:84]
	ccc = (buf[6] & 0x0f) + ((u16)buf[7] <<4);
	set_chrt(halSDCState.chrt,SD_CMD6,(ccc & BIT(10))?SD_CMD6_SPT:SD_CMD6_CAT); //class10以上支持CMD6
	if(SD_HCS == get_chrt(halSDCState.chrt,SD_CAP_TYPE))//支持高容量
	{
		deg_Printf("CAP HCS\n");
		cSize = ((u16)buf[11] << 8) + buf[10]; //bit [69:48]
		halSDCState.dwCap = (((u32)(buf[4] & 0x3f) << 16) + (u32)cSize + 1) << 10;
	}
	else
	{
		rdBlkLen = buf[6] & 0x0f; //BIT[83:80]
		cSize = (((u32)buf[5] & 0x03) << 10) + ((u32)buf[4] << 2) + (((u32)buf[11] & 0xc0) >> 6);	//BIT[73:62]	
		cSizeMult = ((buf[10] & 0x03) << 1) | (buf[9] >> 7); //BIT[49:47]
		halSDCState.dwCap  = ((u32)(cSize + 1)) << (cSizeMult  + rdBlkLen - 7); 
	}	
	deg_Printf("sd capacity : %dGB%dMB%dKB\n",(halSDCState.dwCap>>21)&0x3ff,(halSDCState.dwCap>>11)&0x3ff,(halSDCState.dwCap>>1)&0x3ff);
	if(halSDCState.dwCap == 0)
		return -1;
	return 0;
}
/*******************************************************************************
* Function Name  : sd_cmd16_ack
* Description    : sd_cmd16_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_cmd16_ack(void)
{
	if(CARD_MMC == get_chrt(halSDCState.chrt,CARD_TYPE))//MMC CARD
		halSDCState.SdCmdOp.next_idx = _MMC_FUN_CHECK_;
	return 0;
}
/*******************************************************************************
* Function Name  : sd_rca_cfg
* Description    : sd_rca_cfg
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_rca_cfg(void)
{
	halSDCState.SdCmdOp.arg = halSDCState.dwRCA;
	return 0;
}

/*******************************************************************************
* Function Name  : sd_setbus_probe
* Description    : sd_setbus_probe
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_setbus_probe(void)
{
	if(halSDCState.bus_width == SD_BUS_WIDTH4)
	{
		halSDCState.SdCmdOp.arg = 2;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sd_setbus_ack
* Description    : sd_setbus_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_setbus_ack(void)
{
	if(halSDCState.bus_width == SD_BUS_WIDTH4)
	{
		deg_Printf("<SD> 4 LINE \n");
		hx330x_sdBusSet(SD_BUS_WIDTH4);
	}else
	{
		deg_Printf("<SD> 1 LINE \n");
		hx330x_sdBusSet(SD_BUS_WIDTH1);
	}
	if(SD_CMD6_SPT != get_chrt(halSDCState.chrt,SD_CMD6))	
	{
		set_chrt(halSDCState.chrt,SD_FUNC_TYPE,SD_FUNC_SDR12); 
		halSDCState.SdCmdOp.next_idx = _SD_FUN_VERIFY_;
	}
	
	return 0;
}

/*******************************************************************************
* Function Name  : sd_func_probe
* Description    : sd_func_probe
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_func_probe(void)
{
	
	u8 fun_clk = get_chrt(halSDCState.chrt,SD_FUNC_TYPE) >> 6; 
	//deg_Printf("fun_clk:%x\n",fun_clk);
	halSDCState.SdCmdOp.arg |= fun_clk;
	hx330x_sdRecv((u32)hx330x_sdBuffer(),64);
	return 0;
}
/*******************************************************************************
* Function Name  : sd_checkfun_ack
* Description    : sd_checkfun_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_fun_ack(void)
{
	if (hx330x_sdWaitPend() == false)
	{
		deg_Printf("fun ack pnd err\n");
		return -1;
	}
	if(hx330x_sdCRCCheck(0) == false)
	{
		deg_Printf("fun ack crc err\n");
		return -1;
		
	}
	u8 *buf = hx330x_sdBuffer();
	
	if((buf[16] & 0x0f) != (halSDCState.SdCmdOp.arg & 0x0f))
	{
		
		deg_Printf("func :%x\n",buf[16]);
		return -1;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sd_read_block_probe
* Description    : sd_read_block_probe
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_read_block_probe(void)
{
	if(halSDCState.SdCmdOp.cur_idx == _SD_FUN_VERIFY_)
	{
		switch(get_chrt(halSDCState.chrt,SD_FUNC_TYPE))
		{
			case SD_FUNC_SDR25: hx330x_sdClkSet(SD_HS_CLK, 1); break;
			default: hx330x_sdClkSet(24000000, 0); break;
		}
	
	}
	hx330x_sdRecv((u32)hx330x_sdBuffer(),512);
	return 0;
}
/*******************************************************************************
* Function Name  : sd_read_block_ack
* Description    : sd_read_block_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_read_block_ack(void)
{
	if(hx330x_sdWaitPend() == false)
		return -1;
	if(hx330x_sdCRCCheck(0) == false)
		return -1;
	return 0;
}
/*******************************************************************************
* Function Name  : sd_read_block_ack
* Description    : sd_read_block_ack
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_excsd_ack(void)
{
	if(hx330x_sdWaitPend() == false)
		return -1;
	if(hx330x_sdCRCCheck(0) == false)
		return -1;
	u8 *buf = hx330x_sdBuffer();
	u32 sect_count = *(u32*)(&buf[212]);
	if(sect_count)
		halSDCState.dwCap = sect_count;
	halSDCState.mmcdevtype = buf[MMC_SFR_TYPE];
	return 0;
}
/*******************************************************************************
* Function Name  : sd_mmc_setbus
* Description    : sd_mmc_setbus
* Input          : none
* Output         : None
* Return         : 0: ack ok, <0 : break;
*******************************************************************************/
static int sd_mmc_setbus(void)
{
	if(halSDCState.bus_width == SD_BUS_WIDTH4)
	{
		deg_Printf("<MMC> 4 LINE \n");
		halSDCState.SdCmdOp.arg = (0x03 << 24) | (MMC_SFR_BUSWITH <<16)|(_SDR_4BIT_ << 8)|0x00;
		hx330x_sdBusSet(SD_BUS_WIDTH4);
	}else
	{
		deg_Printf("<MMC> 1 LINE \n");
		halSDCState.SdCmdOp.arg = (0x03 << 24) | (MMC_SFR_BUSWITH <<16)|(_SDR_1BIT_ << 8)|0x00;
		hx330x_sdBusSet(SD_BUS_WIDTH1);
	}
	hx330x_sdClkSet(24000000, 0);
	return 0;
}

/*******************************************************************************
* Function Name  : sd_ident_tab
* Description    : sd_ident_tab
* Input          : none
* Output         : None
* Return         : 
*******************************************************************************/	
ALIGNED(4) const SD_CMD_OP_T sd_ident_tab[]=
{
	//cur_idx,			next_idx,  			cmd, 					rsp, 		arg, 		probe, 				pack
	//{_SD_STOP_, 		_SD_RESET_, 		SD_CMD_STOP, 			RESP_1B, 	0x00000000,	NULL, 				NULL},
	//GO IDLE:CMD0
	{_SD_RESET_, 		_SD_IF_CON_, 		SD_CMD_IDLE, 			NO_RESP, 	0x00000000,	NULL, 				NULL},
	//SD IF COND: CMD8
	{_SD_IF_CON_, 		_SD_RESET_, 		SD_CMD_IF_CON, 			RESP_1, 	0x000001AA,	NULL, 				sd_cmd8_ack},//no ack时，next_idx = _MMC_RESET_
	//SD OP COND: ACMD41
	{_SD_ACTIVE_55_, 	_SD_ACTIVE_41_, 	SD_CMD_APP, 			RESP_1, 	0x00000000, NULL, 				NULL},
	{_SD_ACTIVE_41_, 	_SD_ACTIVE_55_, 	SD_ACMD_APP_OP_COND, 	RESP_3, 	0x00ff8000,	sd_acmd41_probe, 	sd_acmd41_ack}, //ACK READY时next_idx = SD_ALLCID_
	//MMC RESET: CMD0
	{_MMC_RESET_, 		_MMC_ACTIVE_1_, 	SD_CMD_IDLE, 			NO_RESP, 	0x00000000,	NULL, 				NULL},
	//MMC SEND OP COND: CMD1
	{_MMC_ACTIVE_1_, 	_MMC_ACTIVE_1_, 	MMC_CMD_OP_COND, 		RESP_3, 	0x40ff8000,	NULL, 				mmc_cmd1_ack}, //ACK READY时next_idx = SD_ALLCID_
	//GET ALL CID: CMD2
	{_SD_ALLCID_, 		_SD_GET_RCA_, 		SD_CMD_ALLSEND_CID, 	RESP_2, 	0x00000000,	NULL, 				sd_cmd2_ack},
	//SD GET RCA: CMD3
	{_SD_GET_RCA_, 		_SD_GET_CSD_, 		SD_CMD_SEND_RCA, 		RESP_6, 	0x00000000,	NULL, 				sd_cmd3_ack},
	//MMC SET RCA:CMD3
	{_MMC_SET_RCA_, 	_SD_GET_CSD_, 		SD_CMD_SEND_RCA, 		RESP_1, 	0x00010000,	NULL, 				NULL},
	//SD GET CSD: CMD9 --> GET CARD CAP
	{_SD_GET_CSD_, 		_SD_SELECT_CARD_, 	SD_CMD_SEND_CSD, 		RESP_2, 	0x00000000,	sd_rca_cfg, 		sd_cmd9_ack},
	//SD SELECT CARD: CMD7
	{_SD_SELECT_CARD_, 	_SD_SET_BLOCK_LEN_, SD_CMD_SELECT_CARD, 	RESP_1B, 	0x00000000,	sd_rca_cfg, 		NULL},
	//SD SET BLOCK LEN: CMD16
	{_SD_SET_BLOCK_LEN_, _SD_PUDIS_55_, 	SD_CMD_SETBLOCKLEN, 	RESP_1, 	512,		NULL, 				sd_cmd16_ack},
	
	//SELECT CARD: ACMD42
	{_SD_PUDIS_55_, 	_SD_PUDIS_42_, 		SD_CMD_APP, 			RESP_1, 	0x00000000,	sd_rca_cfg, 		NULL},
	{_SD_PUDIS_42_, 	_SD_LINE_CFG_55_, 	SD_ACMD_SET_CLR_CARD_SELECT, RESP_1, 0x00000000,NULL, 				NULL},
	
	//SET BUS: ACMD6 -->SET BUS WIDTH
	{_SD_LINE_CFG_55_, 	_SD_LINE_CFG_6_, 	SD_CMD_APP, 			RESP_1, 	0x00000000,	sd_rca_cfg, 		NULL},
	{_SD_LINE_CFG_6_, 	_SD_FUN_CHECK_, 	SD_ACMD_SETBUSWIDTH, 	RESP_1, 	0x00000000,	sd_setbus_probe, 	sd_setbus_ack},
	//READ ONE BLOCK TEST: CMD17
	//{_SD_LINE_TST_17_, 	_SD_FUN_CHECK_, 	SD_CMD_READ_SINGLE_BLOCK, RESP_1, 	0x00000000,	sd_read_block_probe, sd_read_block_ack},
	//SET FUNC: CMD6-> CONFIG SD speed
	{_SD_FUN_CHECK_, 	_SD_FUN_SET_, 		SD_CMD_SWITCH_FUNC, 	RESP_1, 	0x00fffff0,	sd_func_probe, 		sd_fun_ack},
	{_SD_FUN_SET_, 	    _SD_FUN_VERIFY_, 	SD_CMD_SWITCH_FUNC, 	RESP_1, 	0x80fffff0,	sd_func_probe, 		sd_fun_ack},
	//READ ONE BLOCK TEST: CMD17
	{_SD_FUN_VERIFY_, 	-1, 				SD_CMD_READ_SINGLE_BLOCK, RESP_1, 	0x00000000,	sd_read_block_probe, sd_read_block_ack},	
	
	//MMC SEND IF COND
	{_MMC_FUN_CHECK_, 	_MMC_LINE_SET_, 	SD_CMD_IF_CON, 			RESP_1, 	0x00000000,	sd_read_block_probe, sd_excsd_ack},	
	{_MMC_LINE_SET_, 	_MMC_LINE_TST_17_, 	SD_CMD_SWITCH_FUNC, 	RESP_1B, 	0x00000000,	sd_mmc_setbus, 		 NULL},	
	{_MMC_LINE_TST_17_, -1, 				SD_CMD_READ_SINGLE_BLOCK, RESP_1, 	0x00000000,	sd_read_block_probe, sd_read_block_ack},	
	
};
/*******************************************************************************
* Function Name  : sd_api_init
* Description    : sd_api_init
* Input          : none
* Output         : None
* Return         : 
*******************************************************************************/	
int sd_api_init(u8 bus_width)
{
	int index = 0;
	if(halSDCState.eCardState != SDC_STATE_NULL)
		return 0;	
	memset(&halSDCState,0,sizeof(SDC_STAT_T));
	memset(sdRamBuffer, 0, sizeof(sdRamBuffer));
	hx330x_sdInit(bus_width, hardware_setup.sdcard_pos, sdRamBuffer);
	halSDCState.bus_width = bus_width;
	halSDCState.loopcnt = 10;
	while(1)
	{
		//deg_Printf("sd index:%d\n",index);
		memcpy((void*)&halSDCState.SdCmdOp,(void*)&sd_ident_tab[index],sizeof(SD_CMD_OP_T));
		
		if(halSDCState.SdCmdOp.probe)
		{
			halSDCState.SdCmdOp.probe();
		}
		if(hx330x_sdSendCmd(halSDCState.SdCmdOp.cmd,halSDCState.SdCmdOp.rsp,halSDCState.SdCmdOp.arg) == true)
		{
			if(halSDCState.SdCmdOp.pack)
			{
				if(halSDCState.SdCmdOp.pack() < 0)
				{
					//deg_Printf("ack err:%d\n",index);
					//ret = -1;
					return -1;
				}
			
			}
		}else
		{
			deg_Printf("cmd err:%d\n",index);
			if((halSDCState.SdCmdOp.cmd == SD_CMD_APP) && (halSDCState.SdCmdOp.cur_idx == _SD_ACTIVE_55_))
			{
				halSDCState.SdCmdOp.next_idx = _MMC_RESET_;
				halSDCState.loopcnt = 2000;
			}
			else if(halSDCState.SdCmdOp.cmd == SD_CMD_IF_CON)
			{
				halSDCState.SdCmdOp.next_idx = _SD_ACTIVE_55_;
				halSDCState.loopcnt = 2000;
			}
			else{
				//ret = -2;
				return -2;
			}
		}
		if((halSDCState.SdCmdOp.cur_idx == _SD_IF_CON_ && halSDCState.SdCmdOp.next_idx == _SD_RESET_) ||
			(halSDCState.SdCmdOp.cur_idx == _SD_ACTIVE_41_ && halSDCState.SdCmdOp.next_idx == _SD_ACTIVE_55_) ||
			(halSDCState.SdCmdOp.cur_idx == halSDCState.SdCmdOp.next_idx))
		{
			//ret = 0;
			halSDCState.loopcnt--;
			if(halSDCState.loopcnt < 0)
			{
				if(halSDCState.SdCmdOp.cur_idx == halSDCState.SdCmdOp.next_idx)
				{
					halSDCState.SdCmdOp.next_idx = -1;
				}else
					halSDCState.SdCmdOp.next_idx = halSDCState.SdCmdOp.cur_idx + 1;

			}
		}

		index = halSDCState.SdCmdOp.next_idx;
		if(index < 0)
			break;
			
	}
	
		
	if(CARD_MMC == get_chrt(halSDCState.chrt,CARD_TYPE))
	{
		deg_Printf("[SD] MMC CARD\n");	
	}
	else if(CARD_SD == get_chrt(halSDCState.chrt,CARD_TYPE))
	{
		if(SD_CMD8_SPT == get_chrt(halSDCState.chrt,SD_CMD8_CHRT))
		{
			deg_Printf("[SD] SD 2.0\n");	
		}else
		{
			deg_Printf("[SD] SD 1.0\n");
		}
	}else
	{
		deg_Printf("[SD] NOT SUPPORT\n");
		return -3;
	}
	halSDCState.eCardState = SDC_STATE_FREE;
	
	return 0;
}
/*******************************************************************************
* Function Name  : hal_sdTransferCheck
* Description    : hal layer. transfer state check
* Input          : none
* Output         : None
* Return         : bool    true : ok
                           false: fail
*******************************************************************************/
SDRAM_TEXT_SECTION
static bool sd_transferCheck(void)
{
	bool ret = false;
	volatile int i;

	i = 0x00ffffff;
	while(1)
	{
		if(hx330x_sdSendCmd(SD_CMD_SEND_STATUS, RESP_1,halSDCState.dwRCA) == true)
		{
			u32 rps = hx330x_sdGetRsp();
			if (0x800 == (0x1E00 & rps))
			{
				halSDCState.eCardState = SDC_STATE_FREE;
				ret = true;
				break;
			}
			if((rps == 0x00) || (rps == -1))
			{
				//ret = false;
				break;				
			}
		}

		if((--i)==0)
		{
			//ret = false;
			break;
		}

	}

	return ret;
}
/*******************************************************************************
* Function Name  : sd_api_Stop
* Description    : dev layer. sdc stop tranfer
* Input          : none
* Output         : None
* Return         : bool true: ok

*******************************************************************************/
SDRAM_TEXT_SECTION
bool sd_api_Stop(void)
{
	if (SDC_STATE_FREE != halSDCState.eCardState)
	{
		if ((hx330x_sdSendCmd(SD_CMD_STOP, RESP_1B, 0) == false) || (hx330x_sdWaitDAT0() == false) || (sd_transferCheck() == false))
		{
			return false;
		}
		halSDCState.eCardState = SDC_STATE_FREE;
	}
	return true;
}
/*******************************************************************************
* Function Name  : hal_sdUpdate
* Description    : hal layer. sdc update
* Input          : void *pDataBuf : data buffer
                      u32 dwLBA : block index
                      u32 dwLBANum : block num
                      u8 bRWMode : 1-write,0-read
* Output         : None
* Return         : int 1 : ok
                           <0 : fail
*******************************************************************************/
SDRAM_TEXT_SECTION
static s32 sd_Update(void *pDataBuf, u32 dwLBA, u32 dwLBANum, u8 bRWMode)
{
	u8* buf;

	hal_wdtClear();
	if(((u32)pDataBuf & 0x03) == 0) //buf对齐的时候
	{
		buf = (u8*)pDataBuf;
		(bRWMode == 1)?hx330x_sysDcacheWback((u32)pDataBuf,512) : hx330x_sysDcacheFlush((u32)pDataBuf,512);  // data cache
	}else
	{
		buf = sd0RamBuffer;
		if(bRWMode == 1)
		{
			hx330x_mcpy0_sdram2gram((void *)sd0RamBuffer, pDataBuf, 512);
		}
	}
	
	//deg_Printf("[SD:%x,%x,%x,%x]\n",bRWMode,dwLBA,halSDCState.dwNextLBA,dwLBANum);
	if (0 == dwLBANum)
		return -1;

	if ((dwLBA + dwLBANum) > halSDCState.dwCap)
		return -2;
	
	if (((bRWMode?SDC_STATE_WRITE: SDC_STATE_READ) != halSDCState.eCardState) || (halSDCState.dwNextLBA != dwLBA))
	{
		if(sd_api_Stop() == false)
			return -3;
		halSDCState.eCardState = bRWMode ? SDC_STATE_WRITE : SDC_STATE_READ;
		//if((halSDCState.dwNextLBA & 0x3f) ||(dwLBA&0x3f))
		//	deg_Printf("[SD:%x,%x,%x,%x]\n",bRWMode,dwLBA,halSDCState.dwNextLBA,dwLBANum);
		
		if(!bRWMode)
			hx330x_sdRecv((u32)buf , 512);
		if(hx330x_sdSendCmd(bRWMode ? SD_CMD_WRITE_MULTI_BLOCK : SD_CMD_READ_MULTI_BLOCK, RESP_1,((SD_HCS == get_chrt(halSDCState.chrt,SD_CAP_TYPE))) ? dwLBA : (dwLBA<<9)) == false)		//multiple write/read
		    return -4;
		
		if(bRWMode)
			hx330x_sdSend((u32)buf, 512);
	}
	else
	{
		if(bRWMode)
			hx330x_sdSend((u32)buf , 512);
		else
			hx330x_sdRecv((u32)buf , 512);	
	}

	if (hx330x_sdWaitPend() == false)	{		
		deg_Printf("waitPend\n");
		return -5;
	}
		
	if(hx330x_sdCRCCheck(bRWMode) == false){
		deg_Printf("SD -CRC err\n");		
		return -6;
	}
	if(((u32)pDataBuf & 0x03) && !bRWMode) //buf不对齐的时候
	{
		hx330x_mcpy0_sdram2gram(pDataBuf, (void *)sd0RamBuffer,  512);
	}
	halSDCState.dwNextLBA = dwLBA + dwLBANum;
	

	return 0;
}
/*******************************************************************************
* Function Name  : sd_api_getNextLBA
* Description    : dev layer: sdc next LBA get
* Input          : none
* Output         : None
* Return         : u32
*******************************************************************************/
u32 sd_api_getNextLBA(void)
{
	return halSDCState.dwNextLBA ;
}
/*******************************************************************************
* Function Name  : sd_api_Uninit
* Description    : dev layer. uninitial sd
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void sd_api_Uninit(void)
{
	hx330x_sdUninit();	
}
/*******************************************************************************
* Function Name  : sd_api_lock
* Description    : dev layer.lock sdc state
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void sd_api_lock(void)
{
	if(halSDCState.eCardState)
		halSDCState.busy = 1;
	else
		halSDCState.busy = 0;	
}
/*******************************************************************************
* Function Name  : sd_api_unlock
* Description    : dev layer. unlock sdc state
* Input          : none
* Output         : None
* Return         :none
*******************************************************************************/
void sd_api_unlock(void)
{
	halSDCState.busy = 0;	
}
/*******************************************************************************
* Function Name  : sd_api_Check
* Description    : dev layer.sd check if there is sdc
* Input          : none
* Output         : None
* Return         : bool true: sdc online; false: sdc offline
*******************************************************************************/
static bool sd_api_Check(void)
{
	if(halSDCState.eCardState == SDC_STATE_NULL)
		return false;
		
	if(halSDCState.busy)
		return true;

	if(SDC_STATE_FREE != halSDCState.eCardState)
		sd_api_Stop();

	if(sd_transferCheck() == true)
		return true;
	
	halSDCState.eCardState = SDC_STATE_NULL;
	
	return false;
}

/*******************************************************************************
* Function Name  : void sd_api_CardState_Set(u8 state)
* Description    : dev layer. set sdc state
* Input          : state
* Output         : none
* Return         : none
*******************************************************************************/
void sd_api_CardState_Set(u8 state)
{
	halSDCState.eCardState = state;
}
/*******************************************************************************
* Function Name  : u8 sd_api_CardState_Get(void)
* Description    : dev layer. get sdc state
* Input          : state
* Output         : none
* Return         : none
*******************************************************************************/
u8 sd_api_CardState_Get(void)
{
	return halSDCState.eCardState;
}
/*******************************************************************************
* Function Name  : sd_api_GetBusWidth()
* Description    : dev layer. get sdc bus width
* Input          : none
* Output         : none
* Return         : sd bus width
*******************************************************************************/
u8 sd_api_GetBusWidth(void)
{
	return halSDCState.bus_width;
}

/*******************************************************************************
* Function Name  : sd_api_Capacity
* Description    : dev layer. get sdc capacity
* Input          : none
* Output         : None
* Return         : sd capacity
*******************************************************************************/
u32 sd_api_Capacity(void)
{
	return halSDCState.dwCap;
}
/*******************************************************************************
* Function Name  : sd_api_Exist
* Description    : dev layer.sd check exist
* Input          : none
* Output         : None
* Return         : bool true: sd exist, false: sd not exist
*******************************************************************************/
SDRAM_TEXT_SECTION
bool sd_api_Exist(void)
{
	if(halSDCState.eCardState == SDC_STATE_NULL)
		return false;
	return true;
}
/*******************************************************************************
* Function Name  : sd_api_Write
* Description    : dev layer.sd write
* Input          : void *pDataBuf : data buffer
				   u32 dwLBA : block index
				   u32 dwLBANum : block num
* Output         : None
* Return         : int 1 : ok
                           0 : fail
*******************************************************************************/
volatile u32 hal_sdc_speed = 0;
SDRAM_TEXT_SECTION
s32 sd_api_Write(void *pDataBuf, u32 dwLBA, u32 dwLBANum)
{

	u32 i;
	s32 ret;
	//deg_Printf("SDWRITE:%x,%d\n",dwLBA,dwLBANum);
	for(i = 0;i < dwLBANum; i++)
	{
		ret = sd_Update(((u8 *)pDataBuf + 512 * i), dwLBA + i, 1, 1);
		if(ret<0)
		{	
			halSDCState.eCardState = SDC_STATE_ERROR;
			//memset(&halSDCState,0,sizeof(SDC_STAT_T));
			deg_Printf("[SD ERR] write .%d,sector no :%d,%x\n",ret,dwLBA,pDataBuf);
			//halSDCState.eCardState = SDC_STATE_NULL;
			return ret;
		}
	}
	hal_sdc_speed+=dwLBANum;
	return 0;
}
/*******************************************************************************
* Function Name  : sd_api_speed_debg
* Description    : dev layer. sd write speed debg
* Input          : none
* Output         : None
* Return         : NONE
*******************************************************************************/
void sd_api_speed_debg(void)
{
	u32 jspeed = hal_sdc_speed;
	hal_sdc_speed = 0;
	deg_Printf("sd speed:%dkb/s\n",jspeed/2);
}
/*******************************************************************************
* Function Name  : sd_api_Read
* Description    : dev layer.sd cread
* Input          : void *pDataBuf : data buffer
				   u32 dwLBA : block index
				   u32 dwLBANum : block num
* Output         : None
* Return         : int 1 : ok
                           0 : fail
*******************************************************************************/
SDRAM_TEXT_SECTION
s32 sd_api_Read(void *pDataBuf, u32 dwLBA, u32 dwLBANum)
{
	u32 i;
	s32 ret;
	//deg_Printf("sdRead:%x,%d\n",dwLBA,dwLBANum);
	for( i = 0;i < dwLBANum; i++)
	{
		ret = sd_Update((pDataBuf + 512 * i), dwLBA + i, 1, 0);
		if(ret<0)
		{
			halSDCState.eCardState = SDC_STATE_ERROR;
			//memset(&halSDCState,0,sizeof(SDC_STAT_T));
			deg_Printf("[SD ERR] read .%d,sector no :%d,%x\n",ret,dwLBA,pDataBuf);
			//halSDCState.eCardState = SDC_STATE_NULL;
			return ret;
		}
	}

	return 0;
}
/*******************************************************************************
* Function Name  : dev_sdc_init
* Description    : dev_sdc_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_sdc_init(void)
{
//#if DEV_SDCARD_EN
    hal_sysLDOSet(SYS_LDO_SDC,0,0);  // disable SD_VDD
//#endif
    return 0;
}
/*******************************************************************************
* Function Name  : board_sdc_ioctrl
* Description    : board_sdc_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_sdc_ioctrl(INT32U op,INT32U para)
{
	if(op == DEV_SDCARD_READ)
	{
		if(hardware_setup.sdcard_en && sd_api_Check())
			*(INT32U *)para = 1;
		else
			*(INT32U *)para = 0;

		return 0;
	}

	return -1;
}