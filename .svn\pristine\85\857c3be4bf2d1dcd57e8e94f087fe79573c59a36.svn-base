/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef RES_FONT_API_H
#define RES_FONT_API_H

typedef struct R_STRING_S
{
	u32 r_id;
    u32 r_addr;
	u16 width;
	u16 height;
}R_STRING_T;
/*******************************************************************************
* Function Name  : res_font_Init
* Description    : font initial for res font read
* Input          : INT16U char_indx: resource id
*                  INT16U lan_indx: resource id
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
int res_font_Init(INT16U char_indx, INT16U lan_indx);
/*******************************************************************************
* Function Name  : res_font_SetLanguage
* Description    : set current lanaguage
* Input          : u8 num : lan index 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
int res_font_SetLanguage(u8 num);
/*******************************************************************************
* Function Name  : res_font_GetString
* Description    : res_font_GetString, save string to res_font_Ctrl.strCache, str max len 128
* Input          : 
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
u32 res_font_GetString(u8 num,u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : res_font_GetChar
* Description    : res_font_GetChar, save string to res_font_Ctrl.strCache, str max len 128
* Input          : u8 str_num: str num
*                  u8 index: char index in str
*                  u16 *width
*                  u16 *height
*                  u8* special
* Output         : none                                            
* Return         : int : 
*******************************************************************************/
u8* res_font_GetChar(u8 str_num,u8 index,u16 *width,u16 *height,u8* special);
/*******************************************************************************
* Function Name  : R_loadResource
* Description    : load resource table             
* Input          : R_ICON_T *res : resource table  
                   unsigned long int r_id : resource id
				   unsigned long int cnt  : table length
* Output         : 
* Return         : none
*******************************************************************************/
void res_font_StringTableInit(R_STRING_T *res,u32 r_id_max);
/*******************************************************************************
* Function Name  : R_iconGetDataAndSize
* Description    : get icon data and size        
* Input          :  unsigned long int r_id : icon id
                       unsigned short *width : width
                       unsigned short *heigth:height
* Output        : 
* Return         : int  0 : fail
                            :data  
*******************************************************************************/
u32 res_font_GetAddrAndSize(u32 r_id,u16 *width,u16 *height);



#endif
