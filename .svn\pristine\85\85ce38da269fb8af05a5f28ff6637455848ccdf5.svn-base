/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_BUTTON_H
#define UI_WIN_BUTTON_H

typedef struct
{
	uiWidgetObj 	widget;
	ICON_DRAW_T 	image;
	STRING_DRAW_T 	string;
}uiButtonObj;
/*******************************************************************************
* Function Name  : uiButtonCreate
* Description    : uiButtonCreate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiButtonCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);
#endif
