/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	TEST_RTC_ID		= 0,
	TEST_VERSION_ID,
	TEST_CHIP_ID,
	TEST_BATTERY_ID,
	TEST_GSENSOR_ID,
	TEST_IR_ID,
	TEST_KEY_ID,
	TEST_LCD_ID,
	TEST_SD_ID,
	TEST_SD_CAP_ID,
	TEST_SENSOR_ID,
	TEST_USBDEV_ID,
	TEST_USBHOST_ID,
	TEST_USBHOSTCH_ID,
	TEST_MIC_ID,

};
UNUSED ALIGNED(4) const widgetCreateInfor selfTestWin[] =
{
	createFrameWin(						 Rx(0),	  Ry(0),   Rw(320),	Rh(240), R_ID_PALETTE_TransBlack, WIN_ABS_POS),

	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(0),   Rw(100),	Rh(20),	 RAM_ID_MAKE("Time:"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_RTC_ID,	 	 Rx(100), Ry(0),   Rw(100),	Rh(20),	 RAM_ID_MAKE(" "),			ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),

	createStringIcon(TEST_VERSION_ID,	 Rx(0),	  Ry(20),  Rw(100),	Rh(20),  RAM_ID_MAKE("Ver:"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
#if (CURRENT_CHIP == FPGA)
	createStringIcon(TEST_CHIP_ID,       Rx(100), Ry(20),  Rw(100), Rh(20),  RAM_ID_MAKE("FPGA"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
#elif  ((CURRENT_CHIP&0xff00) == 0x3200)
	createStringIcon(TEST_CHIP_ID,       Rx(100), Ry(20),  Rw(100), Rh(20),  RAM_ID_MAKE("HX3302"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
#elif  ((CURRENT_CHIP&0xff00) == 0x3300)
	createStringIcon(TEST_CHIP_ID,       Rx(100), Ry(20),  Rw(100), Rh(20),  RAM_ID_MAKE("HX3303"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
#elif  ((CURRENT_CHIP&0xff00) == 0x3400)
	createStringIcon(TEST_CHIP_ID,       Rx(100), Ry(20),  Rw(100), Rh(20),  RAM_ID_MAKE("HX3304"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
#elif  ((CURRENT_CHIP&0xff00) == 0x3500)
	createStringIcon(TEST_CHIP_ID,       Rx(100), Ry(20),  Rw(100), Rh(20),  RAM_ID_MAKE("HX3305"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
#else
	createStringIcon(TEST_CHIP_ID,       Rx(100), Ry(20),  Rw(100), Rh(20),  RAM_ID_MAKE("NULL"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
#endif

	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(40),  Rw(100),	Rh(20),	 RAM_ID_MAKE("Bat:"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createImageIcon(TEST_BATTERY_ID,     Rx(100), Ry(40),  Rw(20),  Rh(20), R_ID_ICON_MTBATTERY3,		ALIGNMENT_CENTER),


	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(60),  Rw(100),	Rh(20),	 RAM_ID_MAKE("Gsensor:"),	ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_GSENSOR_ID,	 Rx(100), Ry(60),  Rw(100),	Rh(20),	 RAM_ID_MAKE("NULL"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),

	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(80),  Rw(100),	Rh(20),	 RAM_ID_MAKE("IR:"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_IR_ID,	     Rx(100), Ry(80),  Rw(100),	Rh(20),	 RAM_ID_MAKE("NULL"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),

	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(100), Rw(100),	Rh(20),	 RAM_ID_MAKE("KEY:"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_KEY_ID,	     Rx(100), Ry(100), Rw(100),	Rh(20),	 RAM_ID_MAKE("NULL"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),

	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(120), Rw(100),	Rh(20),	 RAM_ID_MAKE("LCD:"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_LCD_ID,	     Rx(100), Ry(120), Rw(100),	Rh(20),	 RAM_ID_MAKE("NULL"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),

	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(140), Rw(100),	Rh(20),	 RAM_ID_MAKE("SDC:"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_SD_ID,	     Rx(100), Ry(140), Rw(100),	Rh(20),	 RAM_ID_MAKE("NULL"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_SD_CAP_ID,	 Rx(200), Ry(140), Rw(100),	Rh(20),	 RAM_ID_MAKE(" "),			ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),


	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(140), Rw(100),	Rh(20),	 RAM_ID_MAKE("Sensor:"),	ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_SENSOR_ID,	 Rx(100), Ry(140), Rw(100),	Rh(20),	 RAM_ID_MAKE("NULL"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),

	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(160), Rw(100),	Rh(20),	 RAM_ID_MAKE("USBDEV:"),	ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_USBDEV_ID,	 Rx(100), Ry(160), Rw(100),	Rh(20),	 RAM_ID_MAKE("NULL"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),

	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(180), Rw(100),	Rh(20),	 RAM_ID_MAKE("USBHOST:"),	ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_USBHOST_ID,	 Rx(100), Ry(180), Rw(100),	Rh(20),	 RAM_ID_MAKE("NULL"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_USBHOSTCH_ID,	 Rx(200), Ry(180), Rw(100),	Rh(20),	 RAM_ID_MAKE("NULL"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),


	createStringIcon(INVALID_WIDGET_ID,	 Rx(0),	  Ry(200), Rw(100),	Rh(20),	 RAM_ID_MAKE("MIC:"),		ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TEST_MIC_ID,	     Rx(100), Ry(200), Rw(100),	Rh(20),	 RAM_ID_MAKE(" "),			ALIGNMENT_LEFT, R_ID_PALETTE_White,DEFAULT_FONT),


	widgetEnd(),
};


/*******************************************************************************
* Function Name  : selfTestBatShow
* Description    : selfTestBatShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void selfTestBatShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERY5;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4:
			//case BATTERY_STAT_5:
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetVisible(winItem(handle,TEST_BATTERY_ID),1);
	uiWinSetResid(winItem(handle,TEST_BATTERY_ID),batid);
}
/*******************************************************************************
* Function Name  : selfTestKeyShow
* Description    : selfTestKeyShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void selfTestKeyShow(winHandle handle)
{
	switch(getKeyCurEvent())
	{
		case KEY_EVENT_OK: 		uiWinSetResid(winItem(handle,TEST_KEY_ID),	RAM_ID_MAKE("OK")); break;
		case KEY_EVENT_UP: 		uiWinSetResid(winItem(handle,TEST_KEY_ID),	RAM_ID_MAKE("UP")); break;
		case KEY_EVENT_DOWN: 	uiWinSetResid(winItem(handle,TEST_KEY_ID),	RAM_ID_MAKE("DOWN")); break;
		case KEY_EVENT_MENU: 	uiWinSetResid(winItem(handle,TEST_KEY_ID),	RAM_ID_MAKE("MENU")); break;
		case KEY_EVENT_MODE: 	uiWinSetResid(winItem(handle,TEST_KEY_ID),	RAM_ID_MAKE("MODE")); break;
		case KEY_EVENT_POWER: 	uiWinSetResid(winItem(handle,TEST_KEY_ID),	RAM_ID_MAKE("POWER")); break;
		case KEY_EVENT_POWEROFF:uiWinSetResid(winItem(handle,TEST_KEY_ID),	RAM_ID_MAKE("POWEROFF")); break;
		default: 				uiWinSetResid(winItem(handle,TEST_KEY_ID),	RAM_ID_MAKE("NULL")); break;
	}
}
/*******************************************************************************
* Function Name  : selfTestKeyShow
* Description    : selfTestKeyShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void selfTestIrShow(winHandle handle)
{
	u32 para = 0;
	if(dev_ioctrl(SysCtrl.dev_fd_ir,DEV_IR_READ,(u32)&para) >= 0)
	{
		if(para == 0)
		{
			uiWinSetResid(winItem(handle,TEST_IR_ID),		RAM_ID_MAKE("ON"));
		}else
		{
			uiWinSetResid(winItem(handle,TEST_IR_ID),		RAM_ID_MAKE("OFF"));
		}
	}
}
/*******************************************************************************
* Function Name  : selfTestSDShow
* Description    : selfTestSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void selfTestSDShow(winHandle handle)
{
	switch(SysCtrl.dev_stat_sdc)
	{
		case SDC_STAT_NULL: 	uiWinSetResid(winItem(handle,TEST_SD_ID),		RAM_ID_MAKE("NULL")); break;
		case SDC_STAT_UNSTABLE: uiWinSetResid(winItem(handle,TEST_SD_ID),  	RAM_ID_MAKE("Unstable")); break;
		case SDC_STAT_IN: 		uiWinSetResid(winItem(handle,TEST_SD_ID),		RAM_ID_MAKE("IN")); break;
		case SDC_STAT_ERROR: 	uiWinSetResid(winItem(handle,TEST_SD_ID),		RAM_ID_MAKE("ERR")); break;
		case SDC_STAT_FULL: 	uiWinSetResid(winItem(handle,TEST_SD_ID),		RAM_ID_MAKE("FULL")); break;
		case SDC_STAT_NORMAL: 	uiWinSetResid(winItem(handle,TEST_SD_ID),		RAM_ID_MAKE("OK")); break;
		default: 				uiWinSetResid(winItem(handle,TEST_SD_ID),		RAM_ID_MAKE("NULL")); break;
	}
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		static char sdc_str[12];
		u32 sd_cap = sd_api_Capacity()/(2*1024L);	// MB
		hx330x_num2str(sdc_str, sd_cap>>10, 4);
		sdc_str[4] = 'G';
		hx330x_num2str(&sdc_str[5], sd_cap&0x3ff, 4);
		sdc_str[9] = 'M';
		sdc_str[10] = 0;
		uiWinSetResid(winItem(handle,TEST_SD_CAP_ID),	RAM_ID_MAKE(sdc_str));
	}else
	{
		uiWinSetResid(winItem(handle,TEST_SD_CAP_ID),	RAM_ID_MAKE(" "));
	}

}
/*******************************************************************************
* Function Name  : selfTestMicShow
* Description    : selfTestMicShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void selfTestMicShow(winHandle handle)
{
	u16 mic_eng = hx330x_agc_pwr_get();
	if(mic_eng > 0x8000)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("||||||||||||"));
	else if(mic_eng > 0x7000)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("|||||||||||"));
	else if(mic_eng > 0x6000)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("||||||||||"));
	else if(mic_eng > 0x5000)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("|||||||||"));
	else if(mic_eng > 0x4000)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("||||||||"));
	else if(mic_eng > 0x3000)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("|||||||"));
	else if(mic_eng > 0x2000)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("||||||"));
	else if(mic_eng > 0x1000)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("|||||"));
	else if(mic_eng > 0x800)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("||||"));
	else if(mic_eng > 0x400)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("|||"));
	else if(mic_eng > 0x200)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("||"));
	else if(mic_eng > 0x100)
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE("|"));
	else
		uiWinSetResid(winItem(handle,TEST_MIC_ID),	RAM_ID_MAKE(""));
}
/*******************************************************************************
* Function Name  : selfTestUsbDevShow
* Description    : selfTestUsbDevShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void selfTestUsbDevShow(winHandle handle)
{
	switch(SysCtrl.dev_dusb_stat)
	{
		case USBDEV_STAT_DCIN: 	uiWinSetResid(winItem(handle,TEST_USBDEV_ID),	RAM_ID_MAKE("DCIN")); break;
		case USBDEV_STAT_DEVIN_CHECK: 	uiWinSetResid(winItem(handle,TEST_USBDEV_ID),	RAM_ID_MAKE("DEVIN CHECK")); break;
		case USBDEV_STAT_DEVIN: uiWinSetResid(winItem(handle,TEST_USBDEV_ID),	RAM_ID_MAKE("DEVIN")); break;
		case USBDEV_STAT_PC:	uiWinSetResid(winItem(handle,TEST_USBDEV_ID),	RAM_ID_MAKE("PC")); break;
		default:				uiWinSetResid(winItem(handle,TEST_USBDEV_ID),	RAM_ID_MAKE("NULL")); break;
	}
}
/*******************************************************************************
* Function Name  : selfTestUsbHostShow
* Description    : selfTestUsbHostShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void selfTestUsbHostShow(winHandle handle)
{
	switch(SysCtrl.dev_husb_stat)
	{
		case USBHOST_STAT_PWR_ON:		uiWinSetResid(winItem(handle,TEST_USBHOST_ID),RAM_ID_MAKE("PWR")); break;
		case USBHOST_STAT_IN:			uiWinSetResid(winItem(handle,TEST_USBHOST_ID),RAM_ID_MAKE("IN")); break;
		case USBHOST_STAT_SHOW:			uiWinSetResid(winItem(handle,TEST_USBHOST_ID),RAM_ID_MAKE("SHOW")); break;
		case USBHOST_STAT_ASTERN:		uiWinSetResid(winItem(handle,TEST_USBHOST_ID),RAM_ID_MAKE("ASTERN")); break;
		case USBHOST_STAT_OUT:			uiWinSetResid(winItem(handle,TEST_USBHOST_ID),RAM_ID_MAKE("OUT")); break;
		default:						uiWinSetResid(winItem(handle,TEST_USBHOST_ID),RAM_ID_MAKE("NULL")); break;
	}
	if(SysCtrl.dev_husb_ch == USB20_CH )
		uiWinSetResid(winItem(handle,TEST_USBHOSTCH_ID),RAM_ID_MAKE("USB20"));
	else if(SysCtrl.dev_husb_ch == USB11_CH )
		uiWinSetResid(winItem(handle,TEST_USBHOSTCH_ID),RAM_ID_MAKE("USB11"));
	else
		uiWinSetResid(winItem(handle,TEST_USBHOSTCH_ID),RAM_ID_MAKE("NONE"));
}




