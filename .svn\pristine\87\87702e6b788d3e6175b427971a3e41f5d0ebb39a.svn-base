/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef DEV_API_H
#define DEV_API_H

#include "dev_typedef.h"

//battery
#include "battery/inc/battery_api.h"
//fs system
#include "fs/inc/fs_api.h"
//gsensor
#include "gsensor/inc/gsensor_api.h"
//ir
#include "ir/inc/ir_api.h"
//key
#include "key/inc/key_api.h"
//lcd
#include "lcd/inc/lcd_api.h"
//led
#include "led/inc/led_api.h"
//nvfs system
#include "nvfs/inc/nvfs_api.h"
#include "nvfs/inc/nvfs_jpg.h"
//sd
#include "sd/inc/sd_api.h"
//sensor
#include "sensor/inc/sensor_api.h"
//usb device
#include "usb/dusb/inc/dusb_api.h"
//usb host
#include "usb/husb/inc/husb_api.h"

//touch panel
#include "touchpanel/inc/touchpanel_api.h"

//led_pwm
#include "led_pwm/inc/led_pwm_api.h"

/*******************************************************************************
* Function Name  : dev_api_node_init
* Description    : dev_api_node_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dev_api_node_init(void);
/*******************************************************************************
* Function Name  : dev_open
* Description    : dev_open
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int dev_open(char *name);
/*******************************************************************************
* Function Name  : dev_open
* Description    : dev_open
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int dev_ioctrl(int fd, u32 op, u32 para);

void exception_lowpower_io_cfg(void);




#endif
