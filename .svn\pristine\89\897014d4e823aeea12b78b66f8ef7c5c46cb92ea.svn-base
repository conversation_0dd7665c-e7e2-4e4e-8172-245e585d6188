/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

extern int _res_sensor_header_item_start;
extern int _res_sensor_header_len;
SENSOR_HEADER_SECTION const Sensor_Header_T  RES_SensorHeader  = {
	.header_items_addr 			= (u32)&_res_sensor_header_item_start,
	.header_items_total_size 	= (u32)&_res_sensor_header_len, 
	.header_item_size 			= sizeof(Sensor_Ident_T),
};

const Sensor_Adpt_T test_img_adpt = 
{
	.typ 				= CSI_TYPE_COLOR_BAR| CSI_TYPE_DVP,// csi type: 10; 8	

#if  (CURRENT_CHIP == FPGA)
	.mclk 				= 16000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,  //mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#else
	.mclk 				= 16000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,	//mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#endif
	//sensor input -> sensor crop -> csi input
	.senPixelw          = 640, 			//sensor input width
	.senPixelh          = 480,				//sensor input height
	.senCropW_St        = 0,				//sensor crop width start
	.senCropW_Ed        = 640,				//sensor crop width end
	.senCropH_St        = 0,				//sensor crop height start
	.senCropH_Ed        = 480,				//sensor crop height end
	.senCropMode        = CSI_PASS_MODE,	//sensor crop mode: CSI_PASS_MODE, CSI_CROP_MODE , CSI_DIV2_MODE, CSI_CROP_DIV2_MODE

	.pixelw 			= 640,				//csi input width
	.pixelh				= 480,				//csi input height
	.hsyn 				= 1,				//1: hsync valid high, 0: hsync valid low
	.vsyn 				= 1,				//1: vsync valid high, 0: vsync valid low
	.colrarray 			= CSI_PRIORITY_RGGB,//RAW: CSI_PRIORITY_RGGB, CSI_PRIORITY_GRBG, CSI_PRIORITY_BGGR, CSI_PRIORITY_GBRG
											//YUV422: CSI_PRIORITY_CBY0CRY1, CSI_PRIORITY_CRY0CBY1, CSI_PRIORITY_Y0CBY1CR, CSI_PRIORITY_Y0CRY1CB

	.sensorCore			= SYS_VOL_V1_8,		//VDDSENCORE: SYS_VOL_V1_2 ~ SYS_VOL_V3_3
	.sensorIo			= SYS_VOL_V3_0,		//VDDSENIO: SYS_VOL_V1_2 ~ SYS_VOL_V3_56
		
	.mipi_adapt			= {
		.lanes			= 1,			//mipi lane num
		.raw_bit		= CSI_TYPE_RAW10,	//10/8: RAW10/RAW8
		.dphy_pll		= PLL_CLK/5,
		.csi_pclk		= PLL_CLK/8,
		.tclk_settle	= 17,			//TCLK_SETTLE_TIME  = tclk_settle*(1/dphy_pll)
		.tclk_miss		= 4,			//TCLK_MISS_TIME	= tclk_miss*(1/dphy_pll)
		.tclk_prepare	= 2,			//TCLK_PREPARE_TIME = tclk_prepare*(1/dphy_pll)
		.ths_settle		= 2,			//THS_SETTLE_TIME  	= ths_settle*(1/dphy_pll)
		.ths_skip		= 6,			//THS_SKIP_TIME		= ths_skip*(1/dphy_pll)
		.ths_dtermen	= 4,			//THS_DTERMEN_TIME 	= ths_dtermen*(1/dphy_pll)
		.hsa			= 10,				//HSA_TIME			= hsa*(1/csi_pclk)
		.hbp			= 20,				//HBP_TIME			= hbp*(1/csi_pclk)
		.hsd			= 200,				//HSD_TIME			= hsd*(1/csi_pclk)
		.hlines			= 30,
		.vsa_lines		= 3,
		.vbp_lines		= 5,
		.vfp_lines		= 7,
		.vactive_lines	= 0x50
	},
	.rotate_adapt 		= {0},
	.hvb_adapt = {
		.pclk			= APB_CLK/2,		//csi pclk input
		.v_len			= 1000,				//sensor v_len = height + vblank
		.step_val		= 0,				//auto cal
		.step_max		= 0,				//auto cal
		.down_fps_mode	= 0xff,				//0,1,hvb down_fps; 2: exp down_fps, 0xff: turn off down_fps
#if  (CURRENT_CHIP == FPGA)
		.fps			= 15,				//sensor fps set
#else
		.fps			= 30,				//sensor fps set
#endif
		.frequency		= 0					//0: 50hz, 1: 60hz
	},
	//_ISP_DIS_,_ISP_EN_,  _ISP_AUTO_
	.isp_all_mod =  (_ISP_DIS_  <<_BLC_POS_ | _ISP_DIS_  <<_LSC_POS_  | _ISP_DIS_<<_DDC_POS_   | _ISP_DIS_<<_AWB_POS_  \
					|_ISP_DIS_  <<_CCM_POS_ | _ISP_DIS_<<_AE_POS_   | _ISP_DIS_<<_DGAIN_POS_ | _ISP_DIS_<<_YGAMA_POS_ \
					| _ISP_DIS_<<_RGB_GAMA_POS_ | _ISP_DIS_<<_CH_POS_\
					|_ISP_DIS_<<_VDE_POS_ | _ISP_DIS_<<_EE_POS_   | _ISP_DIS_<<_CFD_POS_    |_ISP_DIS_<<_SAJ_POS_
					|_ISP_YUV422_DIS_ << _YUVMOD_POS_),
		.blc_adapt = {	//when _BLC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.blkl_r		= 0,					//BLC red adjust //signed 10bit
		.blkl_gr	= 0,					//BLC green(red) adjust //signed 10bit
		.blkl_gb	= 0,					//BLC green(blue) adjust //signed 10bit
		.blkl_b		= 0,					//BLC blue adjust //signed 10bit
		.blk_rate 	= {0,2,3,4,5,6,7,8},	//_ISP_AUTO_ use, [AE statistic YLOGA/step_len] to adj BLC para, 8 means 1 rate
		.step_len	= 5,					//_ISP_AUTO_ use
	},
	.ddc_adapt = {	//when _DDC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.hot_num 		= 2,				//亮点：目标点比周围24个点中的(24 - (8- hot_num))个点 都亮，差值 >((h_th_rate*p[2][2])/16 + hot_th)
		.dead_num		= 2,				//暗点：目标点比周围24个点中的(24 - (8-dead_num))个点 都暗，差值 >(d_th_rate*AVG/16 + dead_th), AVG为P[2][2]周围8个点平均值
		.hot_th			= 0,				//亮点：判断亮点的阈值，0~1023
		.dead_th		= 0,				//暗点：判断暗点的阈值，0~1023
		.avg_th			= 16,				//暗点/亮点替换：差值平均值的阈值， 0~255
		.d_th_rate		= {4,4,4,4,4,4,4,4},//_ISP_AUTO_时，根据cur_br获取d_th_rate， default使用 d_th_rate[7] , 16 means 1 rate
		.h_th_rate		= {8,8,8,8,8,8,8,8},//_ISP_AUTO_时，根据cur_br获取 h_th_rate， default使用 h_th_rate[7] , 16 means 1 rate
		.dpc_dn_en		= 1,				//1:开启pre_denoise，滤波系数与坐标距离，像素点差值正相关
		.indx_table		= {2,0,0,0,0,0,0,0},//pre_denoise: 取值范围0~7，配置 dn_idx_table, 值越大，滤波开的越大
		.indx_adapt		= {2,1,1,1,0,0,0,0},//_ISP_AUTO_ use：根据yloga/ddc_step查表获得的值，来调整indx_table 表中的值
		.std_th			= {6,20,30,40,50,80,120}, //差值对比表，对应用于获得indx_table的值
		.std_th_rate	= 0,				//用于调整 std_th ，std_th_rate * avg_val / 16;
		.ddc_step		= 7,				//_ISP_AUTO_ use
		.ddc_class		= 7,				//预留用
	},	
	.awb_adapt = {	////when _AWB_POS_ set _ISP_EN_ or _ISP_AUTO_
		.seg_mode		= 0x03,		//AWBStatistic，取值 0~3，根据Y值划分为 (1 << seg_mode)个统计区域
		.rg_start		= 191,		//AWBStatistic yuv_mod_en = 0使用，rgain (g*256/r)起始范围
		.rgmin			= 191,		//AWBStatistic yuv_mod_en = 0 使用，rgain比较的最小值，当rgain落在[rgmin,rgmax]范围内，则落在统计范围内
		.rgmax			= 485, 		//AWBStatistic yuv_mod_en = 0， rgain比较的最大值 // 256 -> 1 gain  500 /256 =about 1.9 gain
		.weight_in		= 3,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_in_low,bgain_in_high]的统计权重值（+1）
		.weight_mid		= 2,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_out_low,bgain_out_high]的统计权重值（+1）
		.ymin			= 0x0a,		//AWBStatistic 统计的Y值区域的最小值
		.ymax			= 0xc0,		//AWBStatistic 统计的Y值区域的最大值
		.hb_rate		= 0xff,		//AWB ADJ bgain <256时使用
		.hb_class		= 0x00,		//AWB ADJ 取值范围 0~3 , bgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hb_class))
		.hr_rate		= 0xff,		//AWB ADJ rgain <256时使用
		.hr_class		= 0x00,		//AWB ADJ 取值范围 0~3 , rgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hr_class))
		.awb_scene_mod	= 0,		//当前使用的AWB RGB GAIN，用于查表manu_awb_gain[]
		.manu_awb_gain	= { 		//定义不同的AWB GAIN表
		//(bgain << 20) | (ggain<< 10) | (rgain<< 0),
			(400 << 20) | (256<< 10) | (380<< 0), 
			(368 << 20) | (256<< 10) | (350<< 0),
			(465 << 20) | (256<< 10) | (225<< 0),
			(370 << 20) | (256<< 10) | (385<< 0),
			(370 << 20) | (256<< 10) | (385<< 0)
		},
		.yuv_mod_en		= 0,										 //1:base Y, 0: Gray World
		.cb_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},  //AWBStatistic yuv_mod_en = 1, 对应不同的Y分区的ABS(CB)阈值，取值范围 0~127
		.cr_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},	 //AWBStatistic yuv_mod_en = 1 ,对应不同的Y分区的ABS(CR)阈值，取值范围 0~127 
		.cbcr_th		= {0x8,0x0f,0x16,0x1e,0x24,0x2d,0x34,0x3c},  //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的ABS(CB)+ABS(CR)阈值，取值范围 0~255 
		.ycbcr_th		= 0x0a,										 //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的y阈值(y-ABS(CB)-ABS(CR))，取值范围 0~255 
		.manu_rgain		= 0,										 //manual AWB时记录配置的rgain
		.manu_ggain		= 0,										 //manual AWB时记录配置的ggain	
		.manu_bgain		= 0,										 //manual AWB时记录配置的bgain
		.rgain			= 0,										 //auto AWB时记录配置的rgain
		.ggain			= 0,										 //auto AWB时记录配置的ggain
		.bgain			= 0,										 //auto AWB时记录配置的bgain
		.seg_gain		= {{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0}}, //AUTO AWB时记录不同Y分区的RGB GAIN值
		.awb_tab		= {	//AWBStatistic yuv_mod_en = 0 用于根据(rgain-rg_start)查表获得目标g值，以16位单位（16*32 = 512）
			178,178,177,175,173,170,167,164,160,156,152,148,144,140,136,129,123,117,113,108,104,100, 94, 90, 87, 81, 78, 78, 76, 76, 76, 73, //bgain_out_high
			178,176,174,171,168,164,160,153,149,146,141,139,134,130,126,121,112,106,102, 96, 92, 88, 86, 83, 81, 77, 75, 74, 73, 72, 73, 74, //bgain_in_high
			178,169,161,150,146,143,138,136,131,125,119,112,113,111,102, 99, 91, 88, 85, 83, 82, 81, 78, 77, 74, 72, 71, 71, 71, 70, 71, 74, //bgain_in_low  
			175,154,143,138,136,130,126,119,116,113,108,106,106, 99, 92, 90, 85, 81, 79, 77, 75, 74, 72, 71, 70, 68, 67, 67, 66, 67, 69, 71 //bgain_out_low
		}		
	},					
	.ccm_adapt = {	//when _CCM_POS_ set _ISP_EN_ or _ISP_AUTO_
		.ccm	= {	//signed 10bit, -512~511, 
			0x100,	0x000,	0x000,  //R: (ccm[0*3 + 0]*R+ccm[0*3 + 1]*G + ccm[0*3 + 2]*B)/256 + s41
			0x000,	0x100,	0x000,  //G: (ccm[1*3 + 0]*R+ccm[1*3 + 1]*G + ccm[1*3 + 2]*B)/256 + s42
			0x00,	0x00,	0x100   //B: (ccm[2*3 + 0]*R+ccm[2*3 + 1]*G + ccm[2*3 + 2]*B)/256 + s43
		},
		.s41	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s42	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s43	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
	},	
	.ae_adapt = {	//when _AE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.exp_adapt = {	//AE auto adj时使用的参数
			.ylog_cal_fnum	= 4,		//_ISP_AUTO_使用：AE统计的frame num，最大32，计算获得ylog_avg 和yloga
			.exp_tag		= {40,50,60,70,75,80,100,136}, //_ISP_AUTO_使用：根据cur_br查表获得目标ylog
			.exp_ext_mod	= 3,		//_ISP_AUTO_使用：低照度下的最小ylog值：exp_ext_mod*8
			.exp_gain		= 195*256,	//当前exp*gain的值
			.k_br			= 12,		//_ISP_AUTO_使用：用于从ylog换算cur_br的系数，值越大，换算的cur_br越大
			.exp_min		= 4,		//限制最小exp值：当exp_gain比较小时，调整gain
			.gain_max		= 1024*4,	//限制最大gain值：当exp_gain比较大时，调整exp
			.frame_nums		= 2,		//_ISP_AUTO_使用：曝光相关调整的帧数间隔
			.ratio_range	= 16,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar 范围不在[32-ratio_range/2,32 + ratio_range]时，加快调整速度
			.weight_in		= 1,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar <= 32时，使用weight_in系数(即目标照度需要降低时)
			.weight_out		= 4,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar > 32时，使用weight_out系数(即目标照度需要提高时)
			.ev_mode		= 0,	    //外部调整整体亮度用：1:在VDE模块调整bright_oft，0：在AE调整中使用
		},	
		.hgrm_adapt = { 
			//AE统计配置的参数，AE统计将整幅图划分为5*5的块进行灰度（Y值）统计
			//X[0 - WIN_X0 - WIN_X1 - WIN_X2 - WIN_X3 - WIDTH]
			//Y[0 - WIN_Y0 - WIN_Y1 - WIN_Y2 - WIN_Y3 - HEIGHT]
			.allow_miss_dots	= 256,	//预留用
			.ae_win_x0			= 160,
			.ae_win_x1			= 320,
			.ae_win_x2			= 960,
			.ae_win_x3			= 1120,
			.ae_win_y0			= 200,
			.ae_win_y1			= 400,
			.ae_win_y2			= 640,
			.ae_win_y3			= 680,
			.weight_0_7			= 0x44111111,//每4bit 对应每个区域的统计权重，区域 0~7
			.weight_8_15		= 0x114f4114,//每4bit 对应每个区域的统计权重，区域 8~15
			.weight_16_23		= 0x11111444,//每4bit 对应每个区域的统计权重，区域 16~23
			.weight_24			= 0x01,		 //每4bit 对应每个区域的统计权重，区域 24
			.hgrm_centre_weight	= {15,14,13,12,11,10,9,8}, //用于根据cur_br调整中间区域，即区域12的权重值
			.hgrm_gray_weight	= {8,8,9,9,10,10,11,12},   //_ISP_AUTO_使用：根据Y值划分区域调整统计的值
		},
	},			
	.rgbdgain_adapt = { //when _DGAIN_POS_ set _ISP_EN_ or _ISP_AUTO_
		.dgain		= {64,64,64,64,64,64,64,64,64},	//配置寄存器：根据Y值的大小划分8个区域来调整
		.dgain_rate	= {64,64,64,64,64,64,64,64}, 	//_ISP_AUTO_使用：根据cur_br获得调整rate，用于调整dgain[]
	},	
	.ygama_adapt = {	//when _YGAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {5,7,9,11,13,14,15,16}, //根据 tab_num[i]的值来选择sensor_ygamma_tab[tab_num[i]]
		.adpt_num		= {5,5,5,5,5,5,5,5},	  //_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_ygamma_tab[]表
		.gam_num0		= 14,					  //当前使用的gamma表index0, 对应sensor_ygamma_tab[index0]
		.gam_num1		= 14,					  //当前使用的gamma表index1, 对应sensor_ygamma_tab[index1]
		.br_mod			= 0,					  //根据br_mod来从index0和index1表中加权平均获得目标的ygamma值
		.bofst			= 0,					  //ymin值 = bosfst << (10 - 8)
		.lofst			= 0xff,					  //ymax值 = lofst << (10 - 8)
		.pad_num		= 1,					  //配置寄存器用，不为0，微调经过ygamma的RGB值
	},
	.rgbgama_adapt = { //when _RGB_GAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {0,1,2,3,4,5,6,7},	//根据 tab_num[i]的值来选择sensor_rgb_gamma[tab_num[i]] 
		.adpt_num		= {3,2,1,1,1,1,1,1},	//_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_rgb_gamma[]表
		.max_oft		= {16,12,12,8,4,0,0,0}, //_ISP_AUTO_: 根据cur_br查表获得当前的max_oft0值
		.gam_num0		= 3,					//当前使用的gamma表index0, 对应sensor_rgb_gamma[index0]
		.gam_num1		= 3,					//当前使用的gamma表index1, 对应sensor_rgb_gamma[index1]
		.max_oft0		= 0,					//用于加大rgbgamma的值
		.br_mod			= 0,					//根据br_mod来从index0和index1表中加权平均获得目标的rgbgamma值
		.rmin			= 0,					//限制最小r值
		.rmax			= 0xff, 				//限制最大r值
		.gmin			= 0,					//限制最小g值
		.gmax			= 0xff,					//限制最大g值
		.bmin			= 0,					//限制最小b值
		.bmax			= 0xff,					//限制最大b值
		.fog_llimt		= 64,					//_ISP_AUTO_: 根据ylog动态调整的 rmin/gmin/bmin的最大值
		.fog_hlimt		= 224,					//_ISP_AUTO_: 根据ylog动态调整的 rmax/gmax/bmax的最小值
		.fog_dotnum		= 4000,					//_ISP_AUTO_: 亮度统计值的目标值，用于计算获得ylog_low和ylog_high
	},
	.ch_adapt = {	//when _CH_POS_ set _ISP_EN_ or _ISP_AUTO_
		.stage0_en	= 1,//enable r g b
		.stage1_en	= 1,//enable y c m
		.enhence	= {0,1,0,0,0,0},//enhance channel  r g b y c m
		//r: >th1[0] && < th0[0], g: [th0[1],th1[1]], b: [th0[2],th1[2]],
		//y(r+g): [th0[3], th1[3]], c(g+b):[th0[4],th1[4]], m(b+r):[th0[5],th1[5]]
		.th1		= {320,192,320,128,256,384},//you can set hue width
		.th0		= {64,  64,192,  0,128,256},//you can set hue width
		//m_x c_x y_x b_x g_r r_x
		.r_rate		= {14,14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.g_rate		= {0, 14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.b_rate		= {0, 14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.sat		= {4,8,12,16,16,16,16,16,16,16,16,16,16,16,16,16,16}, //根据饱和度S按16划分为16个区域进行调整的rate表
		.rate		= {0,16,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/ch_step查表获得rate，用于调整r_rate，g_rate，b_rate，sat表
		.ch_step	= 6,						//_ISP_AUTO_使用
	},
	.vde_adapt = {	//when _VDE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.contra		= 0x80,	//取值范围0~255，对比度调节系数 (contra-128)/128, 配置为0x80时不调节
		.bright_k	= 0x80, //取值范围0~255，亮度调节系数 (bright_k-128)/128, 配置为0x80时不调节
		.bright_oft	= 0x80, //取值范围0~255，亮度增加值： (bright_oft-128), 配置为0x80时不调节
		.hue		= 0x80, //取值范围0~255，色度（UV）调节系数：配置为0x80时不调节
		.sat		= {60,64,68,78,84,88,88,84,80}, //饱和度调节表（调节UV），根据Y值划分为32间隔的8个区域进行取值，64表示1
		.sat_rate	= {10,10,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/vde_step选择sat_rate，用于调整sat[]表的值
		.vde_step	= 6,	//_ISP_AUTO_使用
	},
	.ee_adapt = {	//when _EE_POS_ set _ISP_EN_ or _ISP_AUTO_
		//锐化或降噪的差值区间[ee_dn_th-> ee_keep_th-> ee_sharp_th]
		//ee_dn_th = ee_dn_th + ee_th_adp *avg/256;
		//ee_keep_th = ee_dn_th + (1<<ee_dn_slope);
		//ee_sharp_th = ee_keep_th + (1<<ee_sharp_slope);
		.ee_class		= 1,	//预留用	
		.ee_step		= 6,	//_ISP_AUTO_使用：预留ylog 调整用
		.ee_dn_slope	= {1,1,1,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_dn_slope
		.ee_sharp_slope	= {2,2,2,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_sharp_slope	
		.ee_th_adp		= {8,8,8,8,8,8,8,8},	//_ISP_AUTO_使用：取值范围0~15，根据cur_br查表获得ee_th_adp	
		.ee_dn_th		= {24,24,24,24,24,24,24,24}, //_ISP_AUTO_使用：取值范围0~63，根据cur_br查表获得ee_dn_th	
		.sharp_class	= {0x7,0x9,0xa,0xa,0xa,0xa,0xa,0xa}, //_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得sharp_class,用于配置 ee_sharp_mask[12] = 32-sharp_class
		.dn_class		= {0,0,0,0,0,0,0,0},	//_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得dn_class,用于选择不同的dn_mask表，目前固定用0
	},
	.cfd_adapt = {	//when _EE_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		//根据Y值划分区域，
		//(1) Y < ccf_start的区域，mean_en = 1时，进行高斯滤波处理
		//(2) ccf_start < y < ccf_white_ymin, 使用 (ccf_white_ymin - y)/(16<<wclass)为系数调整UV
		//(3) ccf_white_ymin <= y < ymax的区域，直接配置UV 为128
		//(4) y > ymax同时 UV差值大于 th的区域，使用 rate/16 为系数调整UV
		.rate		= 4, 		// UV调整rate，取值范围0~15，
		.ymax		= 0xe0,		// 强光区 ymax配置，取值范围 0~255
		.th			= 0x20, 	// 配置(ABS(U) + ABS(V))阈值，取值范围 0~127
		.wdc_en		= 1, 		// 1：使能(2)(3)区域的调整	
		.wclass		= 1, 		//ccf_start: wymin - (16<<wclass)   reduce saturation
		.wymin		= 0xff, 	//ccf_white_ymin 
		.mean_en	= 1, 		//ccf_mean: 配置为1，使能(1)区域的调整
		.dn_class	= 0,		//选择ccf_cd_mask[9]表，目前固定配置为0
		.ccf_en		= 1,		//配置为1时，使能(4)区域的调整
	},
	.saj_adapt = {	//when _SAJ_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		.sat		= {12,12,12,12,12,12,12,13,13,14,14,15,15,16,16,16,16}, //取值范围0~31，饱和度调节率表，色饱和度[0,255]划分为16个区域，
		.sat_rate	= {5,6,7,8,9,10,12,16}, //_ISP_AUTO_使用：根据yloga/saj_step查表用于调节sat[]表, 16为单位
		.saj_step	= 6,		//_ISP_AUTO_使用：
	},
	.md_adapt = {	
		.pixel_th		= 20,
		.num_th			= 20,
		.update_cnt		= 1,
		.win_h_start	= (1280/4)*1,	
		.win_h_end		= (1280/4)*3,
		.win_v_start	= (720/4)*1,
		.win_v_end		= (720/4)*3,
	}, 
	.p_fun_adapt = {
		.fp_rotate		= NULL,
		.fp_hvblank		= NULL,
		.fp_exp_gain_wr	= NULL
	},
};
const Sensor_Ident_T test_img_init = 
{
	.sensor_struct_addr   	= (void *)&test_img_adpt,     
	.sensor_struct_size   	= sizeof(Sensor_Adpt_T),
	.sensor_init_tab_adr  	= NULL,     
	.sensor_init_tab_size 	= 0x00,
	.lsc_tab_adr 			= NULL,     
	.lsc_tab_size 			= 0x00,   
	.sensor_name	  		= "NULL",		
	.w_cmd            		= 0x00,                   
	.r_cmd            		= 0x00,                   
	.addr_num         		= 0x00,                   
	.data_num         		= 0x00,   
	.id               		= 0xffff,
	.id_reg           		= 0x00, 
   	
};
SENSOR_YGAMMA_TAB_SECTION const u16 sensor_ygamma_tab[SENSOR_YGAMMA_CLASSES][256]=
{
	{	//y_gama[0]   r =0.8
		0x000,0x001,0x002,0x004,0x005,0x007,0x009,0x00b,0x00d,0x00f,0x012,0x014,0x016,0x018,0x01b,0x01d,
		0x020,0x022,0x025,0x027,0x02a,0x02d,0x02f,0x032,0x035,0x038,0x03b,0x03d,0x040,0x043,0x046,0x049,
		0x04c,0x04f,0x052,0x055,0x058,0x05b,0x05e,0x061,0x064,0x068,0x06b,0x06e,0x071,0x074,0x078,0x07b,
		0x07e,0x082,0x085,0x088,0x08c,0x08f,0x092,0x096,0x099,0x09d,0x0a0,0x0a3,0x0a7,0x0aa,0x0ae,0x0b1,
		0x0b5,0x0b9,0x0bc,0x0c0,0x0c3,0x0c7,0x0ca,0x0ce,0x0d2,0x0d5,0x0d9,0x0dd,0x0e0,0x0e4,0x0e8,0x0eb,
		0x0ef,0x0f3,0x0f7,0x0fa,0x0fe,0x102,0x106,0x10a,0x10e,0x111,0x115,0x119,0x11d,0x121,0x125,0x129,
		0x12c,0x130,0x134,0x138,0x13c,0x140,0x144,0x148,0x14c,0x150,0x154,0x158,0x15c,0x160,0x164,0x168,
		0x16c,0x170,0x175,0x179,0x17d,0x181,0x185,0x189,0x18d,0x191,0x195,0x19a,0x19e,0x1a2,0x1a6,0x1aa,
		0x1af,0x1b3,0x1b7,0x1bb,0x1bf,0x1c4,0x1c8,0x1cc,0x1d0,0x1d5,0x1d9,0x1dd,0x1e2,0x1e6,0x1ea,0x1ef,
		0x1f3,0x1f7,0x1fc,0x200,0x204,0x209,0x20d,0x211,0x216,0x21a,0x21f,0x223,0x227,0x22c,0x230,0x235,
		0x239,0x23e,0x242,0x246,0x24b,0x24f,0x254,0x258,0x25d,0x261,0x266,0x26a,0x26f,0x273,0x278,0x27d,
		0x281,0x286,0x28a,0x28f,0x293,0x298,0x29d,0x2a1,0x2a6,0x2aa,0x2af,0x2b4,0x2b8,0x2bd,0x2c1,0x2c6,
		0x2cb,0x2cf,0x2d4,0x2d9,0x2dd,0x2e2,0x2e7,0x2ec,0x2f0,0x2f5,0x2fa,0x2fe,0x303,0x308,0x30d,0x311,
		0x316,0x31b,0x320,0x324,0x329,0x32e,0x333,0x337,0x33c,0x341,0x346,0x34b,0x34f,0x354,0x359,0x35e,
		0x363,0x368,0x36c,0x371,0x376,0x37b,0x380,0x385,0x38a,0x38e,0x393,0x398,0x39d,0x3a2,0x3a7,0x3ac,
		0x3b1,0x3b6,0x3bb,0x3c0,0x3c4,0x3c9,0x3ce,0x3d3,0x3d8,0x3dd,0x3e2,0x3e7,0x3ec,0x3f1,0x3f6,0x3fb
	},
	{	//y_gama[1]   r =0.86
		0x000,0x001,0x003,0x006,0x008,0x00a,0x00d,0x00f,0x012,0x015,0x017,0x01a,0x01d,0x020,0x023,0x026,
		0x029,0x02c,0x02f,0x032,0x035,0x038,0x03b,0x03e,0x041,0x044,0x048,0x04b,0x04e,0x051,0x055,0x058,
		0x05b,0x05e,0x062,0x065,0x069,0x06c,0x06f,0x073,0x076,0x07a,0x07d,0x081,0x084,0x088,0x08b,0x08f,
		0x092,0x096,0x099,0x09d,0x0a0,0x0a4,0x0a8,0x0ab,0x0af,0x0b2,0x0b6,0x0ba,0x0bd,0x0c1,0x0c5,0x0c9,
		0x0cc,0x0d0,0x0d4,0x0d7,0x0db,0x0df,0x0e3,0x0e6,0x0ea,0x0ee,0x0f2,0x0f6,0x0f9,0x0fd,0x101,0x105,
		0x109,0x10d,0x110,0x114,0x118,0x11c,0x120,0x124,0x128,0x12c,0x130,0x134,0x138,0x13b,0x13f,0x143,
		0x147,0x14b,0x14f,0x153,0x157,0x15b,0x15f,0x163,0x167,0x16b,0x16f,0x173,0x177,0x17b,0x17f,0x184,
		0x188,0x18c,0x190,0x194,0x198,0x19c,0x1a0,0x1a4,0x1a8,0x1ac,0x1b1,0x1b5,0x1b9,0x1bd,0x1c1,0x1c5,
		0x1c9,0x1ce,0x1d2,0x1d6,0x1da,0x1de,0x1e2,0x1e7,0x1eb,0x1ef,0x1f3,0x1f7,0x1fc,0x200,0x204,0x208,
		0x20d,0x211,0x215,0x219,0x21e,0x222,0x226,0x22a,0x22f,0x233,0x237,0x23b,0x240,0x244,0x248,0x24d,
		0x251,0x255,0x25a,0x25e,0x262,0x266,0x26b,0x26f,0x274,0x278,0x27c,0x281,0x285,0x289,0x28e,0x292,
		0x296,0x29b,0x29f,0x2a4,0x2a8,0x2ac,0x2b1,0x2b5,0x2ba,0x2be,0x2c2,0x2c7,0x2cb,0x2d0,0x2d4,0x2d8,
		0x2dd,0x2e1,0x2e6,0x2ea,0x2ef,0x2f3,0x2f8,0x2fc,0x301,0x305,0x309,0x30e,0x312,0x317,0x31b,0x320,
		0x324,0x329,0x32d,0x332,0x336,0x33b,0x33f,0x344,0x348,0x34d,0x352,0x356,0x35b,0x35f,0x364,0x368,
		0x36d,0x371,0x376,0x37a,0x37f,0x384,0x388,0x38d,0x391,0x396,0x39a,0x39f,0x3a4,0x3a8,0x3ad,0x3b1,
		0x3b6,0x3bb,0x3bf,0x3c4,0x3c8,0x3cd,0x3d2,0x3d6,0x3db,0x3e0,0x3e4,0x3e9,0x3ed,0x3f2,0x3f7,0x3fb
	},
	{	//y_gama[2]   r =0.927
		0x000,0x002,0x005,0x008,0x00b,0x00f,0x012,0x015,0x018,0x01c,0x01f,0x022,0x026,0x029,0x02c,0x030,
		0x033,0x037,0x03a,0x03e,0x041,0x045,0x048,0x04c,0x050,0x053,0x057,0x05a,0x05e,0x062,0x065,0x069,
		0x06d,0x070,0x074,0x078,0x07b,0x07f,0x083,0x086,0x08a,0x08e,0x092,0x095,0x099,0x09d,0x0a1,0x0a4,
		0x0a8,0x0ac,0x0b0,0x0b4,0x0b7,0x0bb,0x0bf,0x0c3,0x0c7,0x0cb,0x0ce,0x0d2,0x0d6,0x0da,0x0de,0x0e2,
		0x0e6,0x0e9,0x0ed,0x0f1,0x0f5,0x0f9,0x0fd,0x101,0x105,0x109,0x10c,0x110,0x114,0x118,0x11c,0x120,
		0x124,0x128,0x12c,0x130,0x134,0x138,0x13c,0x140,0x144,0x148,0x14c,0x150,0x154,0x157,0x15b,0x15f,
		0x163,0x167,0x16b,0x16f,0x173,0x177,0x17b,0x17f,0x184,0x188,0x18c,0x190,0x194,0x198,0x19c,0x1a0,
		0x1a4,0x1a8,0x1ac,0x1b0,0x1b4,0x1b8,0x1bc,0x1c0,0x1c4,0x1c8,0x1cc,0x1d0,0x1d4,0x1d9,0x1dd,0x1e1,
		0x1e5,0x1e9,0x1ed,0x1f1,0x1f5,0x1f9,0x1fd,0x201,0x206,0x20a,0x20e,0x212,0x216,0x21a,0x21e,0x222,
		0x226,0x22b,0x22f,0x233,0x237,0x23b,0x23f,0x243,0x248,0x24c,0x250,0x254,0x258,0x25c,0x260,0x265,
		0x269,0x26d,0x271,0x275,0x279,0x27e,0x282,0x286,0x28a,0x28e,0x292,0x297,0x29b,0x29f,0x2a3,0x2a7,
		0x2ac,0x2b0,0x2b4,0x2b8,0x2bc,0x2c1,0x2c5,0x2c9,0x2cd,0x2d1,0x2d6,0x2da,0x2de,0x2e2,0x2e6,0x2eb,
		0x2ef,0x2f3,0x2f7,0x2fb,0x300,0x304,0x308,0x30c,0x311,0x315,0x319,0x31d,0x322,0x326,0x32a,0x32e,
		0x333,0x337,0x33b,0x33f,0x344,0x348,0x34c,0x350,0x355,0x359,0x35d,0x361,0x366,0x36a,0x36e,0x372,
		0x377,0x37b,0x37f,0x383,0x388,0x38c,0x390,0x395,0x399,0x39d,0x3a1,0x3a6,0x3aa,0x3ae,0x3b3,0x3b7,
		0x3bb,0x3bf,0x3c4,0x3c8,0x3cc,0x3d1,0x3d5,0x3d9,0x3de,0x3e2,0x3e6,0x3ea,0x3ef,0x3f3,0x3f7,0x3fc
	},
	{	//y_gama[3]   r =1
		0x000,0x004,0x008,0x00c,0x010,0x014,0x018,0x01c,0x020,0x024,0x028,0x02c,0x030,0x034,0x038,0x03c,
		0x040,0x044,0x048,0x04c,0x050,0x054,0x058,0x05c,0x060,0x064,0x068,0x06c,0x070,0x074,0x078,0x07c,
		0x080,0x084,0x088,0x08c,0x090,0x094,0x098,0x09c,0x0a0,0x0a4,0x0a8,0x0ac,0x0b0,0x0b4,0x0b8,0x0bc,
		0x0c0,0x0c4,0x0c8,0x0cc,0x0d0,0x0d4,0x0d8,0x0dc,0x0e0,0x0e4,0x0e8,0x0ec,0x0f0,0x0f4,0x0f8,0x0fc,
		0x100,0x104,0x108,0x10c,0x110,0x114,0x118,0x11c,0x120,0x124,0x128,0x12c,0x130,0x134,0x138,0x13c,
		0x140,0x144,0x148,0x14c,0x150,0x154,0x158,0x15c,0x160,0x164,0x168,0x16c,0x170,0x174,0x178,0x17c,
		0x180,0x184,0x188,0x18c,0x190,0x194,0x198,0x19c,0x1a0,0x1a4,0x1a8,0x1ac,0x1b0,0x1b4,0x1b8,0x1bc,
		0x1c0,0x1c4,0x1c8,0x1cc,0x1d0,0x1d4,0x1d8,0x1dc,0x1e0,0x1e4,0x1e8,0x1ec,0x1f0,0x1f4,0x1f8,0x1fc,
		0x200,0x204,0x208,0x20c,0x210,0x214,0x218,0x21c,0x220,0x224,0x228,0x22c,0x230,0x234,0x238,0x23c,
		0x240,0x244,0x248,0x24c,0x250,0x254,0x258,0x25c,0x260,0x264,0x268,0x26c,0x270,0x274,0x278,0x27c,
		0x280,0x284,0x288,0x28c,0x290,0x294,0x298,0x29c,0x2a0,0x2a4,0x2a8,0x2ac,0x2b0,0x2b4,0x2b8,0x2bc,
		0x2c0,0x2c4,0x2c8,0x2cc,0x2d0,0x2d4,0x2d8,0x2dc,0x2e0,0x2e4,0x2e8,0x2ec,0x2f0,0x2f4,0x2f8,0x2fc,
		0x300,0x304,0x308,0x30c,0x310,0x314,0x318,0x31c,0x320,0x324,0x328,0x32c,0x330,0x334,0x338,0x33c,
		0x340,0x344,0x348,0x34c,0x350,0x354,0x358,0x35c,0x360,0x364,0x368,0x36c,0x370,0x374,0x378,0x37c,
		0x380,0x384,0x388,0x38c,0x390,0x394,0x398,0x39c,0x3a0,0x3a4,0x3a8,0x3ac,0x3b0,0x3b4,0x3b8,0x3bc,
		0x3c0,0x3c4,0x3c8,0x3cc,0x3d0,0x3d4,0x3d8,0x3dc,0x3e0,0x3e4,0x3e8,0x3ec,0x3f0,0x3f4,0x3f8,0x3fc
	},		
	{	//y_gama[4]   r =1.08
		0x000,0x006,0x00c,0x011,0x016,0x01b,0x020,0x025,0x029,0x02e,0x033,0x038,0x03c,0x041,0x046,0x04a,
		0x04f,0x053,0x058,0x05c,0x061,0x065,0x06a,0x06e,0x072,0x077,0x07b,0x080,0x084,0x088,0x08d,0x091,
		0x095,0x09a,0x09e,0x0a2,0x0a7,0x0ab,0x0af,0x0b3,0x0b8,0x0bc,0x0c0,0x0c4,0x0c9,0x0cd,0x0d1,0x0d5,
		0x0d9,0x0de,0x0e2,0x0e6,0x0ea,0x0ee,0x0f2,0x0f7,0x0fb,0x0ff,0x103,0x107,0x10b,0x10f,0x113,0x118,
		0x11c,0x120,0x124,0x128,0x12c,0x130,0x134,0x138,0x13c,0x140,0x145,0x149,0x14d,0x151,0x155,0x159,
		0x15d,0x161,0x165,0x169,0x16d,0x171,0x175,0x179,0x17d,0x181,0x185,0x189,0x18d,0x191,0x195,0x199,
		0x19d,0x1a1,0x1a5,0x1a9,0x1ad,0x1b1,0x1b5,0x1b9,0x1bd,0x1c1,0x1c5,0x1c9,0x1cd,0x1d0,0x1d4,0x1d8,
		0x1dc,0x1e0,0x1e4,0x1e8,0x1ec,0x1f0,0x1f4,0x1f8,0x1fc,0x200,0x204,0x207,0x20b,0x20f,0x213,0x217,
		0x21b,0x21f,0x223,0x227,0x22b,0x22e,0x232,0x236,0x23a,0x23e,0x242,0x246,0x24a,0x24d,0x251,0x255,
		0x259,0x25d,0x261,0x265,0x269,0x26c,0x270,0x274,0x278,0x27c,0x280,0x283,0x287,0x28b,0x28f,0x293,
		0x297,0x29a,0x29e,0x2a2,0x2a6,0x2aa,0x2ae,0x2b1,0x2b5,0x2b9,0x2bd,0x2c1,0x2c5,0x2c8,0x2cc,0x2d0,
		0x2d4,0x2d8,0x2db,0x2df,0x2e3,0x2e7,0x2eb,0x2ee,0x2f2,0x2f6,0x2fa,0x2fe,0x301,0x305,0x309,0x30d,
		0x311,0x314,0x318,0x31c,0x320,0x323,0x327,0x32b,0x32f,0x333,0x336,0x33a,0x33e,0x342,0x345,0x349,
		0x34d,0x351,0x354,0x358,0x35c,0x360,0x363,0x367,0x36b,0x36f,0x372,0x376,0x37a,0x37e,0x381,0x385,
		0x389,0x38d,0x390,0x394,0x398,0x39c,0x39f,0x3a3,0x3a7,0x3aa,0x3ae,0x3b2,0x3b6,0x3b9,0x3bd,0x3c1,
		0x3c5,0x3c8,0x3cc,0x3d0,0x3d3,0x3d7,0x3db,0x3df,0x3e2,0x3e6,0x3ea,0x3ed,0x3f1,0x3f5,0x3f9,0x3fc
	},		
	{	//y_gama[5]   r =1.165
		0x001,0x009,0x010,0x017,0x01d,0x023,0x029,0x02f,0x034,0x03a,0x03f,0x045,0x04a,0x04f,0x055,0x05a,
		0x05f,0x064,0x069,0x06e,0x073,0x078,0x07d,0x082,0x086,0x08b,0x090,0x095,0x099,0x09e,0x0a3,0x0a7,
		0x0ac,0x0b1,0x0b5,0x0ba,0x0be,0x0c3,0x0c7,0x0cc,0x0d0,0x0d5,0x0d9,0x0dd,0x0e2,0x0e6,0x0eb,0x0ef,
		0x0f3,0x0f8,0x0fc,0x100,0x105,0x109,0x10d,0x112,0x116,0x11a,0x11e,0x123,0x127,0x12b,0x12f,0x133,
		0x138,0x13c,0x140,0x144,0x148,0x14c,0x150,0x155,0x159,0x15d,0x161,0x165,0x169,0x16d,0x171,0x175,
		0x179,0x17d,0x181,0x185,0x189,0x18d,0x191,0x195,0x199,0x19d,0x1a1,0x1a5,0x1a9,0x1ad,0x1b1,0x1b5,
		0x1b9,0x1bd,0x1c1,0x1c5,0x1c9,0x1cd,0x1d1,0x1d5,0x1d9,0x1dc,0x1e0,0x1e4,0x1e8,0x1ec,0x1f0,0x1f4,
		0x1f8,0x1fb,0x1ff,0x203,0x207,0x20b,0x20f,0x213,0x216,0x21a,0x21e,0x222,0x226,0x229,0x22d,0x231,
		0x235,0x239,0x23c,0x240,0x244,0x248,0x24b,0x24f,0x253,0x257,0x25a,0x25e,0x262,0x266,0x269,0x26d,
		0x271,0x275,0x278,0x27c,0x280,0x283,0x287,0x28b,0x28f,0x292,0x296,0x29a,0x29d,0x2a1,0x2a5,0x2a8,
		0x2ac,0x2b0,0x2b3,0x2b7,0x2bb,0x2be,0x2c2,0x2c6,0x2c9,0x2cd,0x2d1,0x2d4,0x2d8,0x2db,0x2df,0x2e3,
		0x2e6,0x2ea,0x2ee,0x2f1,0x2f5,0x2f8,0x2fc,0x300,0x303,0x307,0x30a,0x30e,0x312,0x315,0x319,0x31c,
		0x320,0x323,0x327,0x32b,0x32e,0x332,0x335,0x339,0x33c,0x340,0x344,0x347,0x34b,0x34e,0x352,0x355,
		0x359,0x35c,0x360,0x363,0x367,0x36a,0x36e,0x371,0x375,0x378,0x37c,0x380,0x383,0x387,0x38a,0x38e,
		0x391,0x395,0x398,0x39c,0x39f,0x3a3,0x3a6,0x3a9,0x3ad,0x3b0,0x3b4,0x3b7,0x3bb,0x3be,0x3c2,0x3c5,
		0x3c9,0x3cc,0x3d0,0x3d3,0x3d7,0x3da,0x3de,0x3e1,0x3e4,0x3e8,0x3eb,0x3ef,0x3f2,0x3f6,0x3f9,0x3fc
	},
	{	//y_gama[6]   r =1.255
		0x002,0x00d,0x016,0x01e,0x026,0x02d,0x034,0x03b,0x041,0x047,0x04e,0x054,0x05a,0x060,0x065,0x06b,
		0x071,0x076,0x07c,0x081,0x086,0x08c,0x091,0x096,0x09b,0x0a1,0x0a6,0x0ab,0x0b0,0x0b5,0x0ba,0x0bf,
		0x0c3,0x0c8,0x0cd,0x0d2,0x0d7,0x0db,0x0e0,0x0e5,0x0e9,0x0ee,0x0f3,0x0f7,0x0fc,0x100,0x105,0x109,
		0x10e,0x112,0x117,0x11b,0x120,0x124,0x128,0x12d,0x131,0x135,0x13a,0x13e,0x142,0x147,0x14b,0x14f,
		0x153,0x158,0x15c,0x160,0x164,0x168,0x16c,0x171,0x175,0x179,0x17d,0x181,0x185,0x189,0x18d,0x191,
		0x195,0x199,0x19d,0x1a1,0x1a5,0x1a9,0x1ad,0x1b1,0x1b5,0x1b9,0x1bd,0x1c1,0x1c5,0x1c9,0x1cd,0x1d1,
		0x1d5,0x1d9,0x1dc,0x1e0,0x1e4,0x1e8,0x1ec,0x1f0,0x1f4,0x1f7,0x1fb,0x1ff,0x203,0x207,0x20a,0x20e,
		0x212,0x216,0x219,0x21d,0x221,0x225,0x228,0x22c,0x230,0x234,0x237,0x23b,0x23f,0x242,0x246,0x24a,
		0x24d,0x251,0x255,0x258,0x25c,0x260,0x263,0x267,0x26b,0x26e,0x272,0x275,0x279,0x27d,0x280,0x284,
		0x287,0x28b,0x28f,0x292,0x296,0x299,0x29d,0x2a0,0x2a4,0x2a7,0x2ab,0x2ae,0x2b2,0x2b6,0x2b9,0x2bd,
		0x2c0,0x2c4,0x2c7,0x2cb,0x2ce,0x2d2,0x2d5,0x2d9,0x2dc,0x2df,0x2e3,0x2e6,0x2ea,0x2ed,0x2f1,0x2f4,
		0x2f8,0x2fb,0x2fe,0x302,0x305,0x309,0x30c,0x310,0x313,0x316,0x31a,0x31d,0x321,0x324,0x327,0x32b,
		0x32e,0x332,0x335,0x338,0x33c,0x33f,0x342,0x346,0x349,0x34c,0x350,0x353,0x356,0x35a,0x35d,0x360,
		0x364,0x367,0x36a,0x36e,0x371,0x374,0x378,0x37b,0x37e,0x382,0x385,0x388,0x38b,0x38f,0x392,0x395,
		0x399,0x39c,0x39f,0x3a2,0x3a6,0x3a9,0x3ac,0x3af,0x3b3,0x3b6,0x3b9,0x3bc,0x3c0,0x3c3,0x3c6,0x3c9,
		0x3cd,0x3d0,0x3d3,0x3d6,0x3d9,0x3dd,0x3e0,0x3e3,0x3e6,0x3ea,0x3ed,0x3f0,0x3f3,0x3f6,0x3fa,0x3fd
	},
	{	//y_gama[7]   r =1.35
		0x003,0x012,0x01d,0x027,0x030,0x038,0x040,0x048,0x04f,0x056,0x05d,0x064,0x06a,0x071,0x077,0x07d,
		0x084,0x08a,0x090,0x095,0x09b,0x0a1,0x0a6,0x0ac,0x0b2,0x0b7,0x0bc,0x0c2,0x0c7,0x0cc,0x0d1,0x0d6,
		0x0dc,0x0e1,0x0e6,0x0eb,0x0f0,0x0f4,0x0f9,0x0fe,0x103,0x108,0x10d,0x111,0x116,0x11b,0x11f,0x124,
		0x128,0x12d,0x131,0x136,0x13a,0x13f,0x143,0x148,0x14c,0x151,0x155,0x159,0x15e,0x162,0x166,0x16a,
		0x16f,0x173,0x177,0x17b,0x180,0x184,0x188,0x18c,0x190,0x194,0x198,0x19c,0x1a1,0x1a5,0x1a9,0x1ad,
		0x1b1,0x1b5,0x1b9,0x1bd,0x1c1,0x1c4,0x1c8,0x1cc,0x1d0,0x1d4,0x1d8,0x1dc,0x1e0,0x1e4,0x1e8,0x1eb,
		0x1ef,0x1f3,0x1f7,0x1fb,0x1fe,0x202,0x206,0x20a,0x20d,0x211,0x215,0x219,0x21c,0x220,0x224,0x227,
		0x22b,0x22f,0x232,0x236,0x23a,0x23d,0x241,0x245,0x248,0x24c,0x24f,0x253,0x256,0x25a,0x25e,0x261,
		0x265,0x268,0x26c,0x26f,0x273,0x276,0x27a,0x27d,0x281,0x284,0x288,0x28b,0x28f,0x292,0x296,0x299,
		0x29d,0x2a0,0x2a3,0x2a7,0x2aa,0x2ae,0x2b1,0x2b5,0x2b8,0x2bb,0x2bf,0x2c2,0x2c5,0x2c9,0x2cc,0x2d0,
		0x2d3,0x2d6,0x2da,0x2dd,0x2e0,0x2e4,0x2e7,0x2ea,0x2ed,0x2f1,0x2f4,0x2f7,0x2fb,0x2fe,0x301,0x304,
		0x308,0x30b,0x30e,0x312,0x315,0x318,0x31b,0x31e,0x322,0x325,0x328,0x32b,0x32f,0x332,0x335,0x338,
		0x33b,0x33f,0x342,0x345,0x348,0x34b,0x34e,0x352,0x355,0x358,0x35b,0x35e,0x361,0x365,0x368,0x36b,
		0x36e,0x371,0x374,0x377,0x37a,0x37d,0x381,0x384,0x387,0x38a,0x38d,0x390,0x393,0x396,0x399,0x39c,
		0x39f,0x3a3,0x3a6,0x3a9,0x3ac,0x3af,0x3b2,0x3b5,0x3b8,0x3bb,0x3be,0x3c1,0x3c4,0x3c7,0x3ca,0x3cd,
		0x3d0,0x3d3,0x3d6,0x3d9,0x3dc,0x3df,0x3e2,0x3e5,0x3e8,0x3eb,0x3ee,0x3f1,0x3f4,0x3f7,0x3fa,0x3fd
	},		
	{	//y_gama[8]   r =1.45
		0x005,0x018,0x025,0x031,0x03b,0x045,0x04e,0x056,0x05e,0x066,0x06e,0x075,0x07c,0x083,0x08a,0x091,
		0x098,0x09e,0x0a4,0x0ab,0x0b1,0x0b7,0x0bd,0x0c3,0x0c8,0x0ce,0x0d4,0x0d9,0x0df,0x0e4,0x0ea,0x0ef,
		0x0f4,0x0f9,0x0ff,0x104,0x109,0x10e,0x113,0x118,0x11d,0x122,0x126,0x12b,0x130,0x135,0x13a,0x13e,
		0x143,0x147,0x14c,0x151,0x155,0x15a,0x15e,0x163,0x167,0x16b,0x170,0x174,0x179,0x17d,0x181,0x185,
		0x18a,0x18e,0x192,0x196,0x19a,0x19f,0x1a3,0x1a7,0x1ab,0x1af,0x1b3,0x1b7,0x1bb,0x1bf,0x1c3,0x1c7,
		0x1cb,0x1cf,0x1d3,0x1d7,0x1db,0x1df,0x1e3,0x1e6,0x1ea,0x1ee,0x1f2,0x1f6,0x1fa,0x1fd,0x201,0x205,
		0x209,0x20c,0x210,0x214,0x217,0x21b,0x21f,0x222,0x226,0x22a,0x22d,0x231,0x235,0x238,0x23c,0x23f,
		0x243,0x247,0x24a,0x24e,0x251,0x255,0x258,0x25c,0x25f,0x263,0x266,0x26a,0x26d,0x271,0x274,0x277,
		0x27b,0x27e,0x282,0x285,0x288,0x28c,0x28f,0x293,0x296,0x299,0x29d,0x2a0,0x2a3,0x2a7,0x2aa,0x2ad,
		0x2b1,0x2b4,0x2b7,0x2ba,0x2be,0x2c1,0x2c4,0x2c7,0x2cb,0x2ce,0x2d1,0x2d4,0x2d8,0x2db,0x2de,0x2e1,
		0x2e4,0x2e8,0x2eb,0x2ee,0x2f1,0x2f4,0x2f7,0x2fb,0x2fe,0x301,0x304,0x307,0x30a,0x30d,0x310,0x314,
		0x317,0x31a,0x31d,0x320,0x323,0x326,0x329,0x32c,0x32f,0x332,0x335,0x338,0x33b,0x33f,0x342,0x345,
		0x348,0x34b,0x34e,0x351,0x354,0x357,0x35a,0x35d,0x360,0x363,0x366,0x368,0x36b,0x36e,0x371,0x374,
		0x377,0x37a,0x37d,0x380,0x383,0x386,0x389,0x38c,0x38f,0x392,0x394,0x397,0x39a,0x39d,0x3a0,0x3a3,
		0x3a6,0x3a9,0x3ac,0x3ae,0x3b1,0x3b4,0x3b7,0x3ba,0x3bd,0x3bf,0x3c2,0x3c5,0x3c8,0x3cb,0x3ce,0x3d0,
		0x3d3,0x3d6,0x3d9,0x3dc,0x3df,0x3e1,0x3e4,0x3e7,0x3ea,0x3ec,0x3ef,0x3f2,0x3f5,0x3f8,0x3fa,0x3fd
	},
	{	//y_gama[9]   r =1.555
		0x007,0x01f,0x02f,0x03c,0x048,0x052,0x05c,0x066,0x06f,0x077,0x080,0x088,0x090,0x097,0x09e,0x0a6,
		0x0ad,0x0b3,0x0ba,0x0c1,0x0c7,0x0cd,0x0d4,0x0da,0x0e0,0x0e6,0x0eb,0x0f1,0x0f7,0x0fd,0x102,0x108,
		0x10d,0x112,0x118,0x11d,0x122,0x127,0x12c,0x131,0x136,0x13b,0x140,0x145,0x14a,0x14f,0x154,0x158,
		0x15d,0x162,0x166,0x16b,0x16f,0x174,0x178,0x17d,0x181,0x186,0x18a,0x18f,0x193,0x197,0x19b,0x1a0,
		0x1a4,0x1a8,0x1ac,0x1b0,0x1b5,0x1b9,0x1bd,0x1c1,0x1c5,0x1c9,0x1cd,0x1d1,0x1d5,0x1d9,0x1dd,0x1e1,
		0x1e5,0x1e9,0x1ec,0x1f0,0x1f4,0x1f8,0x1fc,0x200,0x203,0x207,0x20b,0x20e,0x212,0x216,0x21a,0x21d,
		0x221,0x225,0x228,0x22c,0x22f,0x233,0x237,0x23a,0x23e,0x241,0x245,0x248,0x24c,0x24f,0x253,0x256,
		0x25a,0x25d,0x261,0x264,0x267,0x26b,0x26e,0x272,0x275,0x278,0x27c,0x27f,0x282,0x286,0x289,0x28c,
		0x290,0x293,0x296,0x299,0x29d,0x2a0,0x2a3,0x2a6,0x2aa,0x2ad,0x2b0,0x2b3,0x2b7,0x2ba,0x2bd,0x2c0,
		0x2c3,0x2c6,0x2ca,0x2cd,0x2d0,0x2d3,0x2d6,0x2d9,0x2dc,0x2df,0x2e2,0x2e5,0x2e9,0x2ec,0x2ef,0x2f2,
		0x2f5,0x2f8,0x2fb,0x2fe,0x301,0x304,0x307,0x30a,0x30d,0x310,0x313,0x316,0x319,0x31c,0x31f,0x322,
		0x325,0x328,0x32a,0x32d,0x330,0x333,0x336,0x339,0x33c,0x33f,0x342,0x345,0x347,0x34a,0x34d,0x350,
		0x353,0x356,0x359,0x35b,0x35e,0x361,0x364,0x367,0x36a,0x36c,0x36f,0x372,0x375,0x378,0x37a,0x37d,
		0x380,0x383,0x385,0x388,0x38b,0x38e,0x390,0x393,0x396,0x399,0x39b,0x39e,0x3a1,0x3a3,0x3a6,0x3a9,
		0x3ac,0x3ae,0x3b1,0x3b4,0x3b6,0x3b9,0x3bc,0x3be,0x3c1,0x3c4,0x3c6,0x3c9,0x3cc,0x3ce,0x3d1,0x3d4,
		0x3d6,0x3d9,0x3db,0x3de,0x3e1,0x3e3,0x3e6,0x3e9,0x3eb,0x3ee,0x3f0,0x3f3,0x3f6,0x3f8,0x3fb,0x3fd
	},
	{	//y_gama[10]   r =1.66
		0x00a,0x026,0x039,0x048,0x055,0x061,0x06c,0x076,0x080,0x089,0x092,0x09a,0x0a3,0x0ab,0x0b2,0x0ba,
		0x0c1,0x0c8,0x0cf,0x0d6,0x0dd,0x0e3,0x0ea,0x0f0,0x0f6,0x0fc,0x102,0x108,0x10e,0x114,0x11a,0x11f,
		0x125,0x12a,0x130,0x135,0x13a,0x13f,0x145,0x14a,0x14f,0x154,0x159,0x15e,0x163,0x167,0x16c,0x171,
		0x176,0x17a,0x17f,0x184,0x188,0x18d,0x191,0x196,0x19a,0x19e,0x1a3,0x1a7,0x1ab,0x1b0,0x1b4,0x1b8,
		0x1bc,0x1c0,0x1c5,0x1c9,0x1cd,0x1d1,0x1d5,0x1d9,0x1dd,0x1e1,0x1e5,0x1e9,0x1ed,0x1f1,0x1f4,0x1f8,
		0x1fc,0x200,0x204,0x208,0x20b,0x20f,0x213,0x216,0x21a,0x21e,0x221,0x225,0x229,0x22c,0x230,0x234,
		0x237,0x23b,0x23e,0x242,0x245,0x249,0x24c,0x250,0x253,0x257,0x25a,0x25d,0x261,0x264,0x268,0x26b,
		0x26e,0x272,0x275,0x278,0x27c,0x27f,0x282,0x285,0x289,0x28c,0x28f,0x292,0x296,0x299,0x29c,0x29f,
		0x2a2,0x2a6,0x2a9,0x2ac,0x2af,0x2b2,0x2b5,0x2b8,0x2bb,0x2bf,0x2c2,0x2c5,0x2c8,0x2cb,0x2ce,0x2d1,
		0x2d4,0x2d7,0x2da,0x2dd,0x2e0,0x2e3,0x2e6,0x2e9,0x2ec,0x2ef,0x2f2,0x2f5,0x2f8,0x2fb,0x2fe,0x300,
		0x303,0x306,0x309,0x30c,0x30f,0x312,0x315,0x318,0x31a,0x31d,0x320,0x323,0x326,0x329,0x32b,0x32e,
		0x331,0x334,0x337,0x339,0x33c,0x33f,0x342,0x344,0x347,0x34a,0x34d,0x34f,0x352,0x355,0x357,0x35a,
		0x35d,0x360,0x362,0x365,0x368,0x36a,0x36d,0x370,0x372,0x375,0x378,0x37a,0x37d,0x380,0x382,0x385,
		0x387,0x38a,0x38d,0x38f,0x392,0x394,0x397,0x39a,0x39c,0x39f,0x3a1,0x3a4,0x3a6,0x3a9,0x3ac,0x3ae,
		0x3b1,0x3b3,0x3b6,0x3b8,0x3bb,0x3bd,0x3c0,0x3c2,0x3c5,0x3c7,0x3ca,0x3cc,0x3cf,0x3d1,0x3d4,0x3d6,
		0x3d9,0x3db,0x3de,0x3e0,0x3e3,0x3e5,0x3e8,0x3ea,0x3ec,0x3ef,0x3f1,0x3f4,0x3f6,0x3f9,0x3fb,0x3fd
	},
	{	//y_gama[11]   r =1.77
		0x00d,0x02f,0x044,0x054,0x063,0x070,0x07c,0x087,0x091,0x09b,0x0a5,0x0ae,0x0b6,0x0bf,0x0c7,0x0cf,
		0x0d6,0x0de,0x0e5,0x0ec,0x0f3,0x0fa,0x100,0x107,0x10d,0x113,0x11a,0x120,0x126,0x12b,0x131,0x137,
		0x13c,0x142,0x147,0x14d,0x152,0x157,0x15d,0x162,0x167,0x16c,0x171,0x176,0x17b,0x180,0x184,0x189,
		0x18e,0x192,0x197,0x19c,0x1a0,0x1a5,0x1a9,0x1ae,0x1b2,0x1b6,0x1bb,0x1bf,0x1c3,0x1c7,0x1cc,0x1d0,
		0x1d4,0x1d8,0x1dc,0x1e0,0x1e4,0x1e8,0x1ec,0x1f0,0x1f4,0x1f8,0x1fc,0x200,0x204,0x207,0x20b,0x20f,
		0x213,0x216,0x21a,0x21e,0x222,0x225,0x229,0x22c,0x230,0x234,0x237,0x23b,0x23e,0x242,0x245,0x249,
		0x24c,0x250,0x253,0x257,0x25a,0x25d,0x261,0x264,0x267,0x26b,0x26e,0x271,0x275,0x278,0x27b,0x27f,
		0x282,0x285,0x288,0x28b,0x28f,0x292,0x295,0x298,0x29b,0x29e,0x2a2,0x2a5,0x2a8,0x2ab,0x2ae,0x2b1,
		0x2b4,0x2b7,0x2ba,0x2bd,0x2c0,0x2c3,0x2c6,0x2c9,0x2cc,0x2cf,0x2d2,0x2d5,0x2d8,0x2db,0x2de,0x2e1,
		0x2e4,0x2e7,0x2e9,0x2ec,0x2ef,0x2f2,0x2f5,0x2f8,0x2fb,0x2fd,0x300,0x303,0x306,0x309,0x30b,0x30e,
		0x311,0x314,0x317,0x319,0x31c,0x31f,0x322,0x324,0x327,0x32a,0x32c,0x32f,0x332,0x334,0x337,0x33a,
		0x33c,0x33f,0x342,0x344,0x347,0x34a,0x34c,0x34f,0x352,0x354,0x357,0x359,0x35c,0x35f,0x361,0x364,
		0x366,0x369,0x36b,0x36e,0x370,0x373,0x375,0x378,0x37b,0x37d,0x380,0x382,0x385,0x387,0x38a,0x38c,
		0x38e,0x391,0x393,0x396,0x398,0x39b,0x39d,0x3a0,0x3a2,0x3a5,0x3a7,0x3a9,0x3ac,0x3ae,0x3b1,0x3b3,
		0x3b5,0x3b8,0x3ba,0x3bd,0x3bf,0x3c1,0x3c4,0x3c6,0x3c8,0x3cb,0x3cd,0x3cf,0x3d2,0x3d4,0x3d6,0x3d9,
		0x3db,0x3dd,0x3e0,0x3e2,0x3e4,0x3e7,0x3e9,0x3eb,0x3ee,0x3f0,0x3f2,0x3f4,0x3f7,0x3f9,0x3fb,0x3fe
	},
	{	//y_gama[12]   r =1.885
		0x011,0x039,0x050,0x062,0x072,0x080,0x08d,0x099,0x0a4,0x0ae,0x0b8,0x0c1,0x0cb,0x0d3,0x0dc,0x0e4,
		0x0ec,0x0f3,0x0fb,0x102,0x109,0x110,0x117,0x11e,0x124,0x12a,0x131,0x137,0x13d,0x143,0x149,0x14e,
		0x154,0x15a,0x15f,0x165,0x16a,0x16f,0x174,0x17a,0x17f,0x184,0x189,0x18e,0x192,0x197,0x19c,0x1a1,
		0x1a5,0x1aa,0x1af,0x1b3,0x1b8,0x1bc,0x1c1,0x1c5,0x1c9,0x1ce,0x1d2,0x1d6,0x1da,0x1de,0x1e3,0x1e7,
		0x1eb,0x1ef,0x1f3,0x1f7,0x1fb,0x1ff,0x203,0x207,0x20a,0x20e,0x212,0x216,0x21a,0x21d,0x221,0x225,
		0x228,0x22c,0x230,0x233,0x237,0x23a,0x23e,0x242,0x245,0x249,0x24c,0x24f,0x253,0x256,0x25a,0x25d,
		0x261,0x264,0x267,0x26b,0x26e,0x271,0x274,0x278,0x27b,0x27e,0x281,0x285,0x288,0x28b,0x28e,0x291,
		0x294,0x297,0x29b,0x29e,0x2a1,0x2a4,0x2a7,0x2aa,0x2ad,0x2b0,0x2b3,0x2b6,0x2b9,0x2bc,0x2bf,0x2c2,
		0x2c5,0x2c8,0x2cb,0x2ce,0x2d0,0x2d3,0x2d6,0x2d9,0x2dc,0x2df,0x2e2,0x2e4,0x2e7,0x2ea,0x2ed,0x2f0,
		0x2f2,0x2f5,0x2f8,0x2fb,0x2fe,0x300,0x303,0x306,0x308,0x30b,0x30e,0x311,0x313,0x316,0x319,0x31b,
		0x31e,0x320,0x323,0x326,0x328,0x32b,0x32e,0x330,0x333,0x335,0x338,0x33a,0x33d,0x340,0x342,0x345,
		0x347,0x34a,0x34c,0x34f,0x351,0x354,0x356,0x359,0x35b,0x35e,0x360,0x363,0x365,0x368,0x36a,0x36c,
		0x36f,0x371,0x374,0x376,0x379,0x37b,0x37d,0x380,0x382,0x384,0x387,0x389,0x38c,0x38e,0x390,0x393,
		0x395,0x397,0x39a,0x39c,0x39e,0x3a1,0x3a3,0x3a5,0x3a8,0x3aa,0x3ac,0x3ae,0x3b1,0x3b3,0x3b5,0x3b7,
		0x3ba,0x3bc,0x3be,0x3c1,0x3c3,0x3c5,0x3c7,0x3c9,0x3cc,0x3ce,0x3d0,0x3d2,0x3d5,0x3d7,0x3d9,0x3db,
		0x3dd,0x3df,0x3e2,0x3e4,0x3e6,0x3e8,0x3ea,0x3ed,0x3ef,0x3f1,0x3f3,0x3f5,0x3f7,0x3f9,0x3fc,0x3fe
	},
	{	//y_gama[13]   r =2.005
		0x016,0x044,0x05d,0x071,0x082,0x091,0x09f,0x0ab,0x0b7,0x0c2,0x0cc,0x0d6,0x0df,0x0e8,0x0f1,0x0f9,
		0x101,0x109,0x111,0x118,0x120,0x127,0x12d,0x134,0x13b,0x141,0x148,0x14e,0x154,0x15a,0x160,0x165,
		0x16b,0x171,0x176,0x17c,0x181,0x186,0x18c,0x191,0x196,0x19b,0x1a0,0x1a5,0x1aa,0x1ae,0x1b3,0x1b8,
		0x1bc,0x1c1,0x1c6,0x1ca,0x1ce,0x1d3,0x1d7,0x1dc,0x1e0,0x1e4,0x1e8,0x1ed,0x1f1,0x1f5,0x1f9,0x1fd,
		0x201,0x205,0x209,0x20d,0x211,0x214,0x218,0x21c,0x220,0x224,0x227,0x22b,0x22f,0x232,0x236,0x23a,
		0x23d,0x241,0x244,0x248,0x24b,0x24f,0x252,0x256,0x259,0x25c,0x260,0x263,0x267,0x26a,0x26d,0x270,
		0x274,0x277,0x27a,0x27d,0x281,0x284,0x287,0x28a,0x28d,0x290,0x294,0x297,0x29a,0x29d,0x2a0,0x2a3,
		0x2a6,0x2a9,0x2ac,0x2af,0x2b2,0x2b5,0x2b8,0x2bb,0x2be,0x2c1,0x2c3,0x2c6,0x2c9,0x2cc,0x2cf,0x2d2,
		0x2d5,0x2d7,0x2da,0x2dd,0x2e0,0x2e3,0x2e5,0x2e8,0x2eb,0x2ee,0x2f0,0x2f3,0x2f6,0x2f8,0x2fb,0x2fe,
		0x300,0x303,0x306,0x308,0x30b,0x30e,0x310,0x313,0x315,0x318,0x31b,0x31d,0x320,0x322,0x325,0x327,
		0x32a,0x32c,0x32f,0x331,0x334,0x336,0x339,0x33b,0x33e,0x340,0x343,0x345,0x348,0x34a,0x34c,0x34f,
		0x351,0x354,0x356,0x358,0x35b,0x35d,0x360,0x362,0x364,0x367,0x369,0x36b,0x36e,0x370,0x372,0x375,
		0x377,0x379,0x37c,0x37e,0x380,0x382,0x385,0x387,0x389,0x38b,0x38e,0x390,0x392,0x394,0x397,0x399,
		0x39b,0x39d,0x39f,0x3a2,0x3a4,0x3a6,0x3a8,0x3aa,0x3ad,0x3af,0x3b1,0x3b3,0x3b5,0x3b7,0x3ba,0x3bc,
		0x3be,0x3c0,0x3c2,0x3c4,0x3c6,0x3c8,0x3cb,0x3cd,0x3cf,0x3d1,0x3d3,0x3d5,0x3d7,0x3d9,0x3db,0x3dd,
		0x3df,0x3e1,0x3e3,0x3e5,0x3e8,0x3ea,0x3ec,0x3ee,0x3f0,0x3f2,0x3f4,0x3f6,0x3f8,0x3fa,0x3fc,0x3fe
	},
	{	//y_gama[14]   r =2.13
		0x01c,0x050,0x06b,0x081,0x093,0x0a3,0x0b1,0x0be,0x0ca,0x0d6,0x0e0,0x0ea,0x0f4,0x0fd,0x106,0x10f,
		0x117,0x11f,0x127,0x12e,0x136,0x13d,0x144,0x14b,0x151,0x158,0x15e,0x164,0x16b,0x171,0x176,0x17c,
		0x182,0x188,0x18d,0x193,0x198,0x19d,0x1a2,0x1a7,0x1ac,0x1b1,0x1b6,0x1bb,0x1c0,0x1c5,0x1c9,0x1ce,
		0x1d3,0x1d7,0x1dc,0x1e0,0x1e5,0x1e9,0x1ed,0x1f1,0x1f6,0x1fa,0x1fe,0x202,0x206,0x20a,0x20e,0x212,
		0x216,0x21a,0x21e,0x222,0x226,0x229,0x22d,0x231,0x234,0x238,0x23c,0x23f,0x243,0x247,0x24a,0x24e,
		0x251,0x255,0x258,0x25b,0x25f,0x262,0x266,0x269,0x26c,0x26f,0x273,0x276,0x279,0x27c,0x280,0x283,
		0x286,0x289,0x28c,0x28f,0x293,0x296,0x299,0x29c,0x29f,0x2a2,0x2a5,0x2a8,0x2ab,0x2ae,0x2b1,0x2b4,
		0x2b6,0x2b9,0x2bc,0x2bf,0x2c2,0x2c5,0x2c8,0x2cb,0x2cd,0x2d0,0x2d3,0x2d6,0x2d8,0x2db,0x2de,0x2e1,
		0x2e3,0x2e6,0x2e9,0x2eb,0x2ee,0x2f1,0x2f3,0x2f6,0x2f9,0x2fb,0x2fe,0x301,0x303,0x306,0x308,0x30b,
		0x30d,0x310,0x312,0x315,0x318,0x31a,0x31d,0x31f,0x322,0x324,0x326,0x329,0x32b,0x32e,0x330,0x333,
		0x335,0x337,0x33a,0x33c,0x33f,0x341,0x343,0x346,0x348,0x34a,0x34d,0x34f,0x351,0x354,0x356,0x358,
		0x35b,0x35d,0x35f,0x361,0x364,0x366,0x368,0x36a,0x36d,0x36f,0x371,0x373,0x376,0x378,0x37a,0x37c,
		0x37e,0x381,0x383,0x385,0x387,0x389,0x38b,0x38e,0x390,0x392,0x394,0x396,0x398,0x39a,0x39c,0x39f,
		0x3a1,0x3a3,0x3a5,0x3a7,0x3a9,0x3ab,0x3ad,0x3af,0x3b1,0x3b3,0x3b5,0x3b7,0x3b9,0x3bb,0x3bd,0x3c0,
		0x3c2,0x3c4,0x3c6,0x3c8,0x3ca,0x3cc,0x3ce,0x3d0,0x3d1,0x3d3,0x3d5,0x3d7,0x3d9,0x3db,0x3dd,0x3df,
		0x3e1,0x3e3,0x3e5,0x3e7,0x3e9,0x3eb,0x3ed,0x3ef,0x3f1,0x3f2,0x3f4,0x3f6,0x3f8,0x3fa,0x3fc,0x3fe
	},
	{	//y_gama[15]   r =2.26
		0x023,0x05c,0x07a,0x091,0x0a4,0x0b5,0x0c4,0x0d1,0x0de,0x0ea,0x0f5,0x0ff,0x109,0x113,0x11c,0x124,
		0x12d,0x135,0x13d,0x144,0x14c,0x153,0x15a,0x161,0x168,0x16e,0x175,0x17b,0x181,0x187,0x18d,0x193,
		0x198,0x19e,0x1a3,0x1a9,0x1ae,0x1b3,0x1b8,0x1bd,0x1c3,0x1c7,0x1cc,0x1d1,0x1d6,0x1db,0x1df,0x1e4,
		0x1e8,0x1ed,0x1f1,0x1f6,0x1fa,0x1fe,0x202,0x207,0x20b,0x20f,0x213,0x217,0x21b,0x21f,0x223,0x227,
		0x22a,0x22e,0x232,0x236,0x23a,0x23d,0x241,0x245,0x248,0x24c,0x24f,0x253,0x256,0x25a,0x25d,0x261,
		0x264,0x267,0x26b,0x26e,0x271,0x275,0x278,0x27b,0x27e,0x282,0x285,0x288,0x28b,0x28e,0x291,0x294,
		0x297,0x29a,0x29d,0x2a0,0x2a3,0x2a6,0x2a9,0x2ac,0x2af,0x2b2,0x2b5,0x2b8,0x2bb,0x2be,0x2c1,0x2c3,
		0x2c6,0x2c9,0x2cc,0x2cf,0x2d1,0x2d4,0x2d7,0x2d9,0x2dc,0x2df,0x2e2,0x2e4,0x2e7,0x2e9,0x2ec,0x2ef,
		0x2f1,0x2f4,0x2f7,0x2f9,0x2fc,0x2fe,0x301,0x303,0x306,0x308,0x30b,0x30d,0x310,0x312,0x315,0x317,
		0x31a,0x31c,0x31f,0x321,0x323,0x326,0x328,0x32a,0x32d,0x32f,0x332,0x334,0x336,0x339,0x33b,0x33d,
		0x340,0x342,0x344,0x346,0x349,0x34b,0x34d,0x34f,0x352,0x354,0x356,0x358,0x35b,0x35d,0x35f,0x361,
		0x363,0x366,0x368,0x36a,0x36c,0x36e,0x370,0x372,0x375,0x377,0x379,0x37b,0x37d,0x37f,0x381,0x383,
		0x385,0x387,0x38a,0x38c,0x38e,0x390,0x392,0x394,0x396,0x398,0x39a,0x39c,0x39e,0x3a0,0x3a2,0x3a4,
		0x3a6,0x3a8,0x3aa,0x3ac,0x3ae,0x3b0,0x3b2,0x3b4,0x3b6,0x3b8,0x3b9,0x3bb,0x3bd,0x3bf,0x3c1,0x3c3,
		0x3c5,0x3c7,0x3c9,0x3cb,0x3cd,0x3ce,0x3d0,0x3d2,0x3d4,0x3d6,0x3d8,0x3da,0x3dc,0x3dd,0x3df,0x3e1,
		0x3e3,0x3e5,0x3e7,0x3e8,0x3ea,0x3ec,0x3ee,0x3f0,0x3f1,0x3f3,0x3f5,0x3f7,0x3f9,0x3fa,0x3fc,0x3fe
	},
	{	//y_gama[16]   r =2.395
		0x02a,0x06a,0x08a,0x0a2,0x0b6,0x0c8,0x0d7,0x0e5,0x0f2,0x0fe,0x109,0x114,0x11e,0x128,0x131,0x13a,
		0x142,0x14b,0x152,0x15a,0x162,0x169,0x170,0x177,0x17d,0x184,0x18a,0x191,0x197,0x19d,0x1a3,0x1a8,
		0x1ae,0x1b4,0x1b9,0x1be,0x1c4,0x1c9,0x1ce,0x1d3,0x1d8,0x1dd,0x1e2,0x1e6,0x1eb,0x1f0,0x1f4,0x1f9,
		0x1fd,0x201,0x206,0x20a,0x20e,0x213,0x217,0x21b,0x21f,0x223,0x227,0x22b,0x22f,0x233,0x236,0x23a,
		0x23e,0x242,0x245,0x249,0x24d,0x250,0x254,0x257,0x25b,0x25e,0x262,0x265,0x269,0x26c,0x26f,0x273,
		0x276,0x279,0x27c,0x280,0x283,0x286,0x289,0x28c,0x290,0x293,0x296,0x299,0x29c,0x29f,0x2a2,0x2a5,
		0x2a8,0x2ab,0x2ae,0x2b1,0x2b3,0x2b6,0x2b9,0x2bc,0x2bf,0x2c2,0x2c4,0x2c7,0x2ca,0x2cd,0x2d0,0x2d2,
		0x2d5,0x2d8,0x2da,0x2dd,0x2e0,0x2e2,0x2e5,0x2e8,0x2ea,0x2ed,0x2ef,0x2f2,0x2f4,0x2f7,0x2f9,0x2fc,
		0x2fe,0x301,0x303,0x306,0x308,0x30b,0x30d,0x310,0x312,0x315,0x317,0x319,0x31c,0x31e,0x320,0x323,
		0x325,0x327,0x32a,0x32c,0x32e,0x331,0x333,0x335,0x337,0x33a,0x33c,0x33e,0x340,0x343,0x345,0x347,
		0x349,0x34c,0x34e,0x350,0x352,0x354,0x356,0x358,0x35b,0x35d,0x35f,0x361,0x363,0x365,0x367,0x369,
		0x36b,0x36e,0x370,0x372,0x374,0x376,0x378,0x37a,0x37c,0x37e,0x380,0x382,0x384,0x386,0x388,0x38a,
		0x38c,0x38e,0x390,0x392,0x394,0x396,0x398,0x39a,0x39b,0x39d,0x39f,0x3a1,0x3a3,0x3a5,0x3a7,0x3a9,
		0x3ab,0x3ad,0x3ae,0x3b0,0x3b2,0x3b4,0x3b6,0x3b8,0x3ba,0x3bb,0x3bd,0x3bf,0x3c1,0x3c3,0x3c5,0x3c6,
		0x3c8,0x3ca,0x3cc,0x3ce,0x3cf,0x3d1,0x3d3,0x3d5,0x3d6,0x3d8,0x3da,0x3dc,0x3de,0x3df,0x3e1,0x3e3,
		0x3e4,0x3e6,0x3e8,0x3ea,0x3eb,0x3ed,0x3ef,0x3f1,0x3f2,0x3f4,0x3f6,0x3f7,0x3f9,0x3fb,0x3fc,0x3fe
	},
	{	//y_gama[17]   r =2.535
		0x032,0x078,0x09a,0x0b4,0x0c8,0x0da,0x0ea,0x0f9,0x106,0x112,0x11e,0x129,0x133,0x13d,0x146,0x14f,
		0x158,0x160,0x168,0x170,0x177,0x17e,0x185,0x18c,0x193,0x199,0x1a0,0x1a6,0x1ac,0x1b2,0x1b8,0x1bd,
		0x1c3,0x1c9,0x1ce,0x1d3,0x1d8,0x1de,0x1e3,0x1e8,0x1ec,0x1f1,0x1f6,0x1fb,0x1ff,0x204,0x208,0x20d,
		0x211,0x215,0x21a,0x21e,0x222,0x226,0x22a,0x22e,0x232,0x236,0x23a,0x23e,0x242,0x246,0x249,0x24d,
		0x251,0x254,0x258,0x25b,0x25f,0x262,0x266,0x269,0x26d,0x270,0x274,0x277,0x27a,0x27d,0x281,0x284,
		0x287,0x28a,0x28d,0x291,0x294,0x297,0x29a,0x29d,0x2a0,0x2a3,0x2a6,0x2a9,0x2ac,0x2af,0x2b2,0x2b4,
		0x2b7,0x2ba,0x2bd,0x2c0,0x2c3,0x2c5,0x2c8,0x2cb,0x2ce,0x2d0,0x2d3,0x2d6,0x2d8,0x2db,0x2de,0x2e0,
		0x2e3,0x2e5,0x2e8,0x2eb,0x2ed,0x2f0,0x2f2,0x2f5,0x2f7,0x2fa,0x2fc,0x2ff,0x301,0x304,0x306,0x308,
		0x30b,0x30d,0x310,0x312,0x314,0x317,0x319,0x31b,0x31e,0x320,0x322,0x325,0x327,0x329,0x32b,0x32e,
		0x330,0x332,0x334,0x337,0x339,0x33b,0x33d,0x33f,0x341,0x344,0x346,0x348,0x34a,0x34c,0x34e,0x350,
		0x352,0x355,0x357,0x359,0x35b,0x35d,0x35f,0x361,0x363,0x365,0x367,0x369,0x36b,0x36d,0x36f,0x371,
		0x373,0x375,0x377,0x379,0x37b,0x37d,0x37f,0x381,0x383,0x385,0x387,0x388,0x38a,0x38c,0x38e,0x390,
		0x392,0x394,0x396,0x397,0x399,0x39b,0x39d,0x39f,0x3a1,0x3a3,0x3a4,0x3a6,0x3a8,0x3aa,0x3ac,0x3ad,
		0x3af,0x3b1,0x3b3,0x3b5,0x3b6,0x3b8,0x3ba,0x3bc,0x3bd,0x3bf,0x3c1,0x3c3,0x3c4,0x3c6,0x3c8,0x3c9,
		0x3cb,0x3cd,0x3cf,0x3d0,0x3d2,0x3d4,0x3d5,0x3d7,0x3d9,0x3da,0x3dc,0x3de,0x3df,0x3e1,0x3e3,0x3e4,
		0x3e6,0x3e8,0x3e9,0x3eb,0x3ec,0x3ee,0x3f0,0x3f1,0x3f3,0x3f5,0x3f6,0x3f8,0x3f9,0x3fb,0x3fd,0x3fe
	}
};

SENSOR_RGBGAMMA_TAB_SECTION const u8 sensor_rgb_gamma[SENSOR_RGBGAMMA_CLASSES][256] =
{
	{   //rgb_gamma[0] r = 0.3
		0x00,0x00,0x00,0x00,0x01,0x02,0x02,0x03,0x03,0x04,0x04,0x05,0x05,0x06,0x06,0x07,
		0x07,0x08,0x09,0x0a,0x0a,0x0b,0x0c,0x0d,0x0d,0x0e,0x0f,0x0f,0x10,0x11,0x11,0x12,
		0x13,0x13,0x14,0x15,0x16,0x17,0x18,0x18,0x19,0x1a,0x1b,0x1c,0x1d,0x1d,0x1e,0x1f,
		0x20,0x21,0x22,0x22,0x23,0x24,0x25,0x26,0x27,0x27,0x28,0x29,0x2a,0x2b,0x2c,0x2c,
		0x2d,0x2e,0x2f,0x30,0x31,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3a,0x3b,
		0x3c,0x3d,0x3e,0x3f,0x40,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4a,0x4b,
		0x4c,0x4d,0x4e,0x4f,0x50,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5a,0x5b,
		0x5c,0x5d,0x5e,0x5f,0x60,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6a,0x6b,
		0x6c,0x6d,0x6e,0x6f,0x70,0x71,0x72,0x73,0x75,0x76,0x77,0x78,0x79,0x7a,0x7b,0x7c,
		0x7d,0x7e,0x80,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8b,0x8c,0x8d,0x8e,
		0x8f,0x90,0x91,0x92,0x93,0x94,0x96,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9d,0x9e,0x9f,
		0xa1,0xa2,0xa3,0xa4,0xa5,0xa6,0xa8,0xa9,0xaa,0xab,0xac,0xad,0xaf,0xb0,0xb1,0xb2,
		0xb3,0xb4,0xb6,0xb7,0xb8,0xb9,0xba,0xbb,0xbd,0xbe,0xbf,0xc0,0xc1,0xc3,0xc4,0xc5,
		0xc6,0xc7,0xc9,0xca,0xcb,0xcc,0xcd,0xcf,0xd0,0xd1,0xd2,0xd3,0xd5,0xd6,0xd7,0xd8,
		0xda,0xdb,0xdc,0xdd,0xde,0xdf,0xe1,0xe2,0xe3,0xe4,0xe5,0xe7,0xe8,0xe9,0xea,0xeb,
		0xed,0xee,0xef,0xf0,0xf1,0xf3,0xf4,0xf5,0xf6,0xf7,0xf9,0xfa,0xfb,0xfc,0xfd,0xff
	},
	{	//rgb_gamma[1] r = 0.4,
		0x00,0x00,0x01,0x01,0x02,0x03,0x03,0x04,0x04,0x05,0x05,0x06,0x07,0x08,0x09,0x0a,
		0x0a,0x0b,0x0c,0x0d,0x0d,0x0e,0x0f,0x10,0x10,0x11,0x12,0x13,0x13,0x14,0x15,0x16,
		0x17,0x17,0x18,0x19,0x1a,0x1b,0x1c,0x1c,0x1d,0x1e,0x1f,0x20,0x21,0x21,0x22,0x23,
		0x24,0x25,0x26,0x27,0x28,0x29,0x2a,0x2b,0x2c,0x2c,0x2d,0x2e,0x2f,0x30,0x31,0x32,
		0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,0x40,0x41,0x42,
		0x43,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4a,0x4b,0x4c,0x4d,0x4e,0x4f,0x50,0x51,
		0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5a,0x5b,0x5c,0x5d,0x5e,0x5f,0x60,0x61,
		0x62,0x63,0x64,0x65,0x67,0x68,0x69,0x6a,0x6b,0x6c,0x6d,0x6e,0x6f,0x70,0x71,0x72,
		0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7a,0x7b,0x7c,0x7d,0x7e,0x7f,0x80,0x81,0x82,
		0x83,0x84,0x86,0x87,0x88,0x89,0x8a,0x8b,0x8c,0x8d,0x8e,0x8f,0x91,0x92,0x93,0x94,
		0x95,0x96,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9d,0x9e,0x9f,0xa0,0xa1,0xa2,0xa3,0xa4,
		0xa6,0xa7,0xa8,0xa9,0xaa,0xab,0xac,0xad,0xaf,0xb0,0xb1,0xb2,0xb3,0xb4,0xb5,0xb6,
		0xb8,0xb9,0xba,0xbb,0xbc,0xbd,0xbe,0xbf,0xc1,0xc2,0xc3,0xc4,0xc5,0xc6,0xc7,0xc8,
		0xca,0xcb,0xcc,0xcd,0xce,0xcf,0xd0,0xd1,0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,0xd9,0xda,
		0xdc,0xdd,0xde,0xdf,0xe0,0xe1,0xe2,0xe3,0xe5,0xe6,0xe7,0xe8,0xe9,0xea,0xeb,0xec,
		0xee,0xef,0xf0,0xf1,0xf2,0xf3,0xf4,0xf5,0xf7,0xf8,0xf9,0xfa,0xfb,0xfc,0xfd,0xff
	},
	{	//rgb_gamma[2] r = 0.5
		0x00,0x00,0x01,0x02,0x03,0x04,0x04,0x05,0x06,0x07,0x07,0x08,0x09,0x0a,0x0b,0x0c,
		0x0c,0x0d,0x0e,0x0f,0x10,0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1a,0x1b,
		0x1c,0x1c,0x1d,0x1e,0x1f,0x20,0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x27,0x28,0x29,
		0x2a,0x2b,0x2c,0x2d,0x2e,0x2f,0x30,0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,
		0x3a,0x3b,0x3c,0x3d,0x3e,0x3e,0x3f,0x40,0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,
		0x49,0x4a,0x4b,0x4c,0x4d,0x4e,0x4f,0x50,0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,
		0x59,0x5a,0x5b,0x5c,0x5d,0x5e,0x5f,0x60,0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,
		0x69,0x6a,0x6b,0x6c,0x6e,0x6f,0x70,0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x79,
		0x7a,0x7b,0x7c,0x7d,0x7e,0x7f,0x80,0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,
		0x8a,0x8b,0x8c,0x8d,0x8e,0x8f,0x90,0x91,0x92,0x93,0x94,0x95,0x97,0x98,0x99,0x9a,
		0x9b,0x9c,0x9d,0x9e,0x9f,0xa0,0xa1,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xaa,
		0xac,0xad,0xae,0xaf,0xb0,0xb1,0xb2,0xb3,0xb4,0xb5,0xb6,0xb7,0xb8,0xb9,0xba,0xbb,
		0xbc,0xbd,0xbe,0xbf,0xc0,0xc1,0xc2,0xc3,0xc5,0xc6,0xc7,0xc8,0xc9,0xca,0xcb,0xcc,
		0xcd,0xce,0xcf,0xd0,0xd1,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,0xd9,0xda,0xdb,0xdc,
		0xde,0xdf,0xe0,0xe1,0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0xe8,0xe9,0xea,0xeb,0xec,0xed,
		0xef,0xf0,0xf1,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf9,0xfa,0xfb,0xfc,0xfd,0xff
	},
	{	//rgb_gamma[3] r = 0.6
		0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,0x10,
		0x11,0x12,0x13,0x14,0x15,0x16,0x17,0x18,0x19,0x1a,0x1b,0x1c,0x1d,0x1e,0x1f,0x20,
		0x21,0x22,0x23,0x24,0x25,0x26,0x27,0x28,0x29,0x2a,0x2b,0x2c,0x2d,0x2e,0x2f,0x30,
		0x31,0x32,0x33,0x34,0x35,0x36,0x37,0x38,0x39,0x3a,0x3b,0x3c,0x3d,0x3e,0x3f,0x40,
		0x41,0x42,0x43,0x44,0x45,0x46,0x47,0x48,0x49,0x4a,0x4b,0x4c,0x4d,0x4e,0x4f,0x50,
		0x51,0x52,0x53,0x54,0x55,0x56,0x57,0x58,0x59,0x5a,0x5b,0x5c,0x5d,0x5e,0x5f,0x60,
		0x61,0x62,0x63,0x64,0x65,0x66,0x67,0x68,0x69,0x6a,0x6b,0x6c,0x6d,0x6e,0x6f,0x70,
		0x71,0x72,0x73,0x74,0x75,0x76,0x77,0x78,0x79,0x7a,0x7b,0x7c,0x7d,0x7e,0x7f,0x80,
		0x81,0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8a,0x8b,0x8c,0x8d,0x8e,0x8f,0x90,
		0x91,0x92,0x93,0x94,0x95,0x96,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9d,0x9e,0x9f,0xa0,
		0xa1,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xaa,0xab,0xac,0xad,0xae,0xaf,0xb0,
		0xb1,0xb2,0xb3,0xb4,0xb5,0xb6,0xb7,0xb8,0xb9,0xba,0xbb,0xbc,0xbd,0xbe,0xbf,0xc0,
		0xc1,0xc2,0xc3,0xc4,0xc5,0xc6,0xc7,0xc8,0xc9,0xca,0xcb,0xcc,0xcd,0xce,0xcf,0xd0,
		0xd1,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,0xd9,0xda,0xdb,0xdc,0xdd,0xde,0xdf,0xe0,
		0xe1,0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0xe8,0xe9,0xea,0xeb,0xec,0xed,0xee,0xef,0xf0,
		0xf1,0xf1,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf9,0xfa,0xfb,0xfc,0xfd,0xfe,0xff
	},
	{	//rgb_gamma[4] r = 0.7
		0x01,0x04,0x07,0x09,0x0c,0x0f,0x11,0x14,0x16,0x19,0x1b,0x1d,0x20,0x22,0x24,0x27,
		0x29,0x2b,0x2e,0x30,0x32,0x35,0x37,0x3a,0x3c,0x3e,0x40,0x42,0x45,0x47,0x49,0x4b,
		0x4e,0x50,0x52,0x54,0x56,0x58,0x5b,0x5d,0x5f,0x61,0x63,0x65,0x68,0x6a,0x6c,0x6e,
		0x70,0x72,0x75,0x77,0x79,0x7b,0x7d,0x7f,0x82,0x84,0x86,0x88,0x8a,0x8c,0x8f,0x91,
		0x93,0x95,0x97,0x99,0x9c,0x9e,0xa0,0xa2,0xa4,0xa6,0xa8,0xaa,0xac,0xae,0xb0,0xb2,
		0xb5,0xb7,0xb9,0xbb,0xbd,0xbf,0xc1,0xc3,0xc6,0xc8,0xca,0xcc,0xce,0xd0,0xd2,0xd4,
		0xd7,0xd9,0xdb,0xdd,0xdf,0xe1,0xe3,0xe5,0xe7,0xe9,0xeb,0xed,0xef,0xf1,0xf3,0xf5,
		0xf7,0xf9,0xfb,0xfd,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[5] r = 0.8
		0x02,0x05,0x09,0x0b,0x0e,0x11,0x13,0x16,0x19,0x1c,0x1e,0x21,0x23,0x26,0x28,0x2b,
		0x2d,0x2f,0x31,0x34,0x36,0x38,0x3a,0x3d,0x3f,0x41,0x43,0x45,0x48,0x4a,0x4c,0x4e,
		0x51,0x53,0x55,0x57,0x59,0x5b,0x5d,0x5f,0x61,0x63,0x65,0x67,0x6a,0x6c,0x6e,0x70,
		0x72,0x74,0x76,0x78,0x7a,0x7c,0x7e,0x80,0x82,0x84,0x86,0x88,0x8a,0x8c,0x8e,0x90,
		0x92,0x94,0x96,0x98,0x9a,0x9b,0x9d,0x9f,0xa1,0xa3,0xa5,0xa6,0xa8,0xaa,0xac,0xae,
		0xb0,0xb1,0xb3,0xb5,0xb7,0xb9,0xbb,0xbd,0xbf,0xc0,0xc2,0xc4,0xc6,0xc8,0xca,0xcc,
		0xce,0xcf,0xd1,0xd3,0xd5,0xd7,0xd8,0xda,0xdc,0xde,0xe0,0xe1,0xe3,0xe5,0xe7,0xe9,
		0xea,0xec,0xee,0xf0,0xf2,0xf2,0xf3,0xf3,0xf4,0xf5,0xf5,0xf6,0xf7,0xf7,0xf8,0xf9,
		0xf9,0xfa,0xfb,0xfb,0xfc,0xfd,0xfd,0xfe,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[6] r = 0.9
		0x03,0x07,0x0b,0x0e,0x11,0x14,0x16,0x19,0x1c,0x1f,0x21,0x24,0x26,0x29,0x2b,0x2e,
		0x30,0x32,0x34,0x37,0x39,0x3b,0x3d,0x40,0x42,0x44,0x46,0x48,0x4b,0x4d,0x4f,0x51,
		0x54,0x55,0x57,0x59,0x5b,0x5d,0x5f,0x61,0x63,0x65,0x67,0x69,0x6b,0x6c,0x6e,0x70,
		0x72,0x74,0x76,0x78,0x7a,0x7c,0x7e,0x80,0x82,0x83,0x85,0x87,0x89,0x8b,0x8d,0x8e,
		0x90,0x92,0x94,0x96,0x98,0x99,0x9b,0x9d,0x9f,0xa0,0xa2,0xa4,0xa6,0xa7,0xa9,0xab,
		0xad,0xae,0xb0,0xb1,0xb3,0xb5,0xb6,0xb8,0xba,0xbb,0xbd,0xbe,0xc0,0xc2,0xc3,0xc5,
		0xc7,0xc8,0xca,0xcb,0xcd,0xcf,0xd0,0xd2,0xd4,0xd5,0xd7,0xd9,0xda,0xdc,0xde,0xdf,
		0xe1,0xe3,0xe4,0xe6,0xe8,0xe9,0xea,0xeb,0xec,0xed,0xee,0xf0,0xf1,0xf2,0xf3,0xf4,
		0xf5,0xf6,0xf8,0xf9,0xfa,0xfb,0xfc,0xfd,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[7] r = 1.0
		0x05,0x09,0x0d,0x10,0x13,0x17,0x1a,0x1d,0x20,0x23,0x25,0x28,0x2a,0x2d,0x2f,0x32,
		0x34,0x36,0x38,0x3b,0x3d,0x3f,0x41,0x44,0x46,0x48,0x4a,0x4c,0x4e,0x50,0x52,0x54,
		0x56,0x57,0x59,0x5b,0x5d,0x5f,0x61,0x63,0x65,0x67,0x69,0x6b,0x6d,0x6e,0x70,0x72,
		0x74,0x75,0x77,0x79,0x7b,0x7c,0x7e,0x80,0x82,0x83,0x85,0x87,0x88,0x8a,0x8c,0x8d,
		0x8f,0x91,0x92,0x94,0x96,0x97,0x99,0x9b,0x9c,0x9e,0xa0,0xa1,0xa3,0xa5,0xa6,0xa8,
		0xaa,0xab,0xad,0xae,0xb0,0xb1,0xb3,0xb4,0xb6,0xb7,0xb9,0xba,0xbc,0xbd,0xbf,0xc0,
		0xc2,0xc3,0xc4,0xc6,0xc7,0xc9,0xca,0xcc,0xcd,0xcf,0xd0,0xd1,0xd3,0xd4,0xd6,0xd7,
		0xd9,0xda,0xdc,0xdd,0xdf,0xe0,0xe1,0xe3,0xe4,0xe6,0xe7,0xe8,0xea,0xeb,0xed,0xee,
		0xef,0xf1,0xf2,0xf4,0xf5,0xf6,0xf8,0xf9,0xfb,0xfb,0xfb,0xfb,0xfb,0xfc,0xfc,0xfc,
		0xfc,0xfc,0xfd,0xfd,0xfd,0xfd,0xfd,0xfe,0xfe,0xfe,0xfe,0xfe,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[8] r = 1.2
		0x06,0x0b,0x10,0x13,0x17,0x1b,0x1d,0x20,0x23,0x26,0x28,0x2b,0x2e,0x30,0x33,0x36,
		0x38,0x3a,0x3c,0x3f,0x41,0x43,0x45,0x48,0x49,0x4b,0x4d,0x4f,0x51,0x53,0x55,0x57,
		0x59,0x5a,0x5c,0x5e,0x60,0x62,0x64,0x65,0x67,0x69,0x6b,0x6d,0x6f,0x70,0x72,0x74,
		0x75,0x77,0x79,0x7a,0x7c,0x7e,0x7f,0x81,0x83,0x84,0x86,0x87,0x89,0x8a,0x8c,0x8e,
		0x8f,0x91,0x92,0x94,0x96,0x97,0x98,0x9a,0x9b,0x9d,0x9e,0x9f,0xa1,0xa2,0xa4,0xa5,
		0xa7,0xa8,0xa9,0xab,0xac,0xae,0xaf,0xb1,0xb2,0xb3,0xb5,0xb6,0xb8,0xb9,0xbb,0xbc,
		0xbe,0xbf,0xc0,0xc1,0xc3,0xc4,0xc5,0xc7,0xc8,0xc9,0xcb,0xcc,0xcd,0xce,0xd0,0xd1,
		0xd2,0xd4,0xd5,0xd6,0xd8,0xd9,0xda,0xdb,0xdd,0xde,0xdf,0xe0,0xe2,0xe3,0xe4,0xe5,
		0xe7,0xe8,0xe9,0xea,0xec,0xed,0xee,0xef,0xf1,0xf1,0xf2,0xf3,0xf3,0xf4,0xf5,0xf5,
		0xf6,0xf7,0xf8,0xf8,0xf9,0xfa,0xfa,0xfb,0xfc,0xfc,0xfd,0xfe,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[9] r = 1.4
		0x08,0x0d,0x13,0x16,0x1a,0x1e,0x21,0x24,0x27,0x2a,0x2c,0x2f,0x32,0x34,0x37,0x3a,
		0x3c,0x3e,0x40,0x42,0x44,0x46,0x48,0x4b,0x4c,0x4e,0x50,0x52,0x54,0x56,0x58,0x5a,
		0x5c,0x5d,0x5f,0x61,0x63,0x64,0x66,0x68,0x6a,0x6b,0x6d,0x6f,0x71,0x72,0x74,0x75,
		0x77,0x78,0x7a,0x7c,0x7d,0x7f,0x80,0x82,0x84,0x85,0x86,0x88,0x89,0x8b,0x8c,0x8d,
		0x8f,0x90,0x92,0x93,0x95,0x96,0x97,0x99,0x9a,0x9c,0x9d,0x9e,0xa0,0xa1,0xa3,0xa4,
		0xa6,0xa7,0xa8,0xa9,0xab,0xac,0xad,0xae,0xb0,0xb1,0xb2,0xb3,0xb5,0xb6,0xb7,0xb8,
		0xba,0xbb,0xbc,0xbd,0xbe,0xc0,0xc1,0xc2,0xc3,0xc4,0xc6,0xc7,0xc8,0xc9,0xca,0xcc,
		0xcd,0xce,0xcf,0xd0,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,0xda,0xdb,0xdc,0xdd,0xde,
		0xdf,0xe0,0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0xe9,0xea,0xeb,0xec,0xed,0xee,0xef,0xf0,
		0xf1,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf9,0xfa,0xfb,0xfc,0xfe,0xfe,0xfe,0xfe,
		0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[10] r = 1.6
		0x0b,0x10,0x16,0x19,0x1d,0x21,0x24,0x27,0x2a,0x2e,0x30,0x33,0x35,0x38,0x3a,0x3d,
		0x3f,0x41,0x43,0x45,0x47,0x49,0x4b,0x4e,0x4f,0x51,0x53,0x55,0x57,0x59,0x5b,0x5d,
		0x5f,0x60,0x62,0x64,0x65,0x67,0x69,0x6a,0x6c,0x6e,0x6f,0x71,0x73,0x74,0x76,0x77,
		0x79,0x7a,0x7c,0x7d,0x7f,0x80,0x82,0x83,0x85,0x86,0x87,0x89,0x8a,0x8b,0x8d,0x8e,
		0x8f,0x91,0x92,0x93,0x95,0x96,0x97,0x98,0x9a,0x9b,0x9c,0x9d,0x9f,0xa0,0xa1,0xa2,
		0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xab,0xac,0xad,0xae,0xaf,0xb1,0xb2,0xb3,0xb4,0xb5,
		0xb7,0xb8,0xb9,0xba,0xbb,0xbc,0xbd,0xbe,0xbf,0xc0,0xc2,0xc3,0xc4,0xc5,0xc6,0xc7,
		0xc8,0xc9,0xca,0xcb,0xcd,0xce,0xcf,0xd0,0xd1,0xd2,0xd3,0xd4,0xd5,0xd6,0xd7,0xd8,
		0xd9,0xda,0xdb,0xdc,0xdd,0xde,0xdf,0xe0,0xe1,0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0xe8,
		0xe9,0xea,0xeb,0xec,0xed,0xee,0xef,0xf0,0xf1,0xf2,0xf3,0xf4,0xf5,0xf5,0xf6,0xf6,
		0xf7,0xf7,0xf8,0xf8,0xf9,0xf9,0xfa,0xfa,0xfb,0xfb,0xfc,0xfc,0xfd,0xfd,0xfe,0xfe,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[11] r = 1.8
		0x0d,0x13,0x19,0x1d,0x21,0x25,0x28,0x2b,0x2e,0x31,0x33,0x36,0x39,0x3b,0x3e,0x41,
		0x43,0x45,0x47,0x49,0x4b,0x4d,0x4f,0x51,0x52,0x54,0x56,0x58,0x5a,0x5c,0x5e,0x60,
		0x62,0x63,0x65,0x66,0x68,0x69,0x6b,0x6d,0x6e,0x70,0x71,0x73,0x75,0x76,0x77,0x79,
		0x7a,0x7b,0x7d,0x7e,0x7f,0x81,0x82,0x83,0x85,0x86,0x87,0x89,0x8a,0x8b,0x8d,0x8e,
		0x8f,0x91,0x92,0x93,0x95,0x96,0x97,0x98,0x99,0x9a,0x9c,0x9d,0x9e,0x9f,0xa0,0xa1,
		0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xaa,0xab,0xac,0xad,0xae,0xaf,0xb0,0xb1,0xb2,
		0xb4,0xb5,0xb6,0xb7,0xb8,0xb9,0xba,0xbb,0xbc,0xbd,0xbe,0xbf,0xc0,0xc1,0xc2,0xc3,
		0xc4,0xc5,0xc6,0xc7,0xc9,0xc9,0xca,0xcb,0xcc,0xcd,0xce,0xcf,0xd0,0xd1,0xd2,0xd3,
		0xd4,0xd5,0xd6,0xd7,0xd8,0xd9,0xda,0xdb,0xdc,0xdc,0xdd,0xde,0xdf,0xe0,0xe1,0xe1,
		0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0xe7,0xe8,0xe9,0xea,0xeb,0xec,0xed,0xed,0xee,0xef,
		0xf0,0xf1,0xf2,0xf2,0xf3,0xf4,0xf5,0xf6,0xf7,0xf8,0xf8,0xf9,0xfa,0xfb,0xfc,0xfd,
		0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,
		0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[12] r = 2.0
		0x10,0x16,0x1c,0x20,0x24,0x28,0x2b,0x2e,0x31,0x35,0x37,0x3a,0x3c,0x3f,0x41,0x44,
		0x46,0x48,0x4a,0x4c,0x4e,0x50,0x52,0x55,0x56,0x58,0x5a,0x5b,0x5d,0x5f,0x60,0x62,
		0x64,0x65,0x67,0x68,0x6a,0x6b,0x6d,0x6e,0x70,0x71,0x73,0x74,0x76,0x77,0x78,0x7a,
		0x7b,0x7c,0x7e,0x7f,0x80,0x82,0x83,0x84,0x86,0x87,0x88,0x89,0x8b,0x8c,0x8d,0x8e,
		0x90,0x91,0x92,0x93,0x95,0x96,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9d,0x9e,0x9f,0xa0,
		0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xaa,0xab,0xac,0xad,0xae,0xaf,0xb0,0xb1,
		0xb2,0xb2,0xb3,0xb4,0xb5,0xb6,0xb7,0xb8,0xb9,0xba,0xbb,0xbc,0xbd,0xbe,0xbf,0xc0,
		0xc1,0xc2,0xc3,0xc4,0xc5,0xc5,0xc6,0xc7,0xc8,0xc9,0xca,0xca,0xcb,0xcc,0xcd,0xce,
		0xcf,0xd0,0xd0,0xd1,0xd2,0xd3,0xd4,0xd5,0xd6,0xd6,0xd7,0xd8,0xd9,0xda,0xdb,0xdb,
		0xdc,0xdd,0xde,0xdf,0xe0,0xe1,0xe1,0xe2,0xe3,0xe4,0xe5,0xe6,0xe7,0xe7,0xe8,0xe9,
		0xea,0xea,0xeb,0xec,0xed,0xed,0xee,0xef,0xf0,0xf0,0xf1,0xf2,0xf3,0xf3,0xf4,0xf5,
		0xf6,0xf6,0xf6,0xf7,0xf7,0xf7,0xf8,0xf8,0xf9,0xf9,0xf9,0xfa,0xfa,0xfa,0xfb,0xfb,
		0xfc,0xfc,0xfc,0xfd,0xfd,0xfd,0xfe,0xfe,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[13] r = 2.2
		0x13,0x19,0x1f,0x23,0x27,0x2c,0x2f,0x32,0x35,0x39,0x3b,0x3e,0x40,0x43,0x45,0x48,
		0x4a,0x4c,0x4e,0x50,0x52,0x54,0x56,0x58,0x59,0x5b,0x5d,0x5e,0x60,0x62,0x63,0x65,
		0x67,0x68,0x69,0x6b,0x6c,0x6e,0x6f,0x70,0x72,0x73,0x75,0x76,0x78,0x79,0x7a,0x7c,
		0x7d,0x7e,0x80,0x81,0x82,0x84,0x85,0x86,0x88,0x89,0x8a,0x8b,0x8c,0x8d,0x8e,0x8f,
		0x90,0x91,0x92,0x93,0x95,0x96,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9d,0x9e,0x9f,0xa0,
		0xa2,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xaa,0xab,0xac,0xad,0xae,0xaf,0xb0,
		0xb1,0xb1,0xb2,0xb3,0xb4,0xb5,0xb6,0xb6,0xb7,0xb8,0xb9,0xba,0xbb,0xbc,0xbc,0xbd,
		0xbe,0xbf,0xc0,0xc1,0xc2,0xc2,0xc3,0xc4,0xc5,0xc6,0xc6,0xc7,0xc8,0xc9,0xca,0xca,
		0xcb,0xcc,0xcd,0xce,0xce,0xcf,0xd0,0xd1,0xd2,0xd2,0xd3,0xd4,0xd5,0xd5,0xd6,0xd7,
		0xd8,0xd8,0xd9,0xda,0xdb,0xdb,0xdc,0xdd,0xde,0xde,0xdf,0xe0,0xe1,0xe1,0xe2,0xe3,
		0xe3,0xe4,0xe5,0xe5,0xe6,0xe7,0xe8,0xe8,0xe9,0xea,0xea,0xeb,0xec,0xec,0xed,0xee,
		0xef,0xef,0xf0,0xf1,0xf1,0xf2,0xf3,0xf3,0xf4,0xf5,0xf5,0xf6,0xf7,0xf7,0xf8,0xf9,
		0xf9,0xfa,0xfb,0xfb,0xfc,0xfd,0xfd,0xfe,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[14] r = 2.4
		0x16,0x1c,0x23,0x27,0x2b,0x30,0x33,0x36,0x39,0x3c,0x3e,0x41,0x43,0x46,0x48,0x4b,
		0x4d,0x4f,0x51,0x53,0x55,0x57,0x59,0x5b,0x5c,0x5e,0x60,0x61,0x63,0x65,0x66,0x68,
		0x6a,0x6b,0x6c,0x6e,0x6f,0x70,0x72,0x73,0x74,0x76,0x77,0x78,0x7a,0x7b,0x7c,0x7d,
		0x7f,0x80,0x81,0x82,0x84,0x85,0x86,0x87,0x89,0x8a,0x8b,0x8c,0x8d,0x8e,0x8f,0x90,
		0x91,0x92,0x93,0x94,0x96,0x96,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9d,0x9e,0x9f,0xa0,
		0xa1,0xa1,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa9,0xaa,0xab,0xac,0xad,0xae,0xaf,
		0xb0,0xb0,0xb1,0xb2,0xb3,0xb4,0xb4,0xb5,0xb6,0xb7,0xb8,0xb8,0xb9,0xba,0xbb,0xbc,
		0xbc,0xbd,0xbe,0xbf,0xc0,0xc0,0xc1,0xc2,0xc3,0xc3,0xc4,0xc5,0xc6,0xc6,0xc7,0xc8,
		0xc9,0xc9,0xca,0xcb,0xcc,0xcc,0xcd,0xce,0xcf,0xcf,0xd0,0xd0,0xd1,0xd2,0xd2,0xd3,
		0xd4,0xd4,0xd5,0xd6,0xd6,0xd7,0xd8,0xd8,0xd9,0xda,0xda,0xdb,0xdc,0xdc,0xdd,0xdd,
		0xde,0xdf,0xdf,0xe0,0xe1,0xe1,0xe2,0xe3,0xe3,0xe4,0xe5,0xe5,0xe6,0xe7,0xe7,0xe8,
		0xe9,0xe9,0xea,0xea,0xeb,0xec,0xec,0xed,0xee,0xee,0xef,0xef,0xf0,0xf1,0xf1,0xf2,
		0xf3,0xf3,0xf4,0xf4,0xf5,0xf6,0xf6,0xf7,0xf8,0xf8,0xf8,0xf8,0xf9,0xf9,0xf9,0xfa,
		0xfa,0xfa,0xfa,0xfb,0xfb,0xfb,0xfc,0xfc,0xfc,0xfc,0xfd,0xfd,0xfd,0xfe,0xfe,0xfe,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[15] r = 2.6
		0x19,0x1f,0x26,0x2a,0x2e,0x33,0x36,0x39,0x3c,0x40,0x42,0x45,0x47,0x4a,0x4c,0x4f,
		0x50,0x52,0x54,0x56,0x58,0x5a,0x5c,0x5e,0x5f,0x61,0x62,0x64,0x65,0x67,0x68,0x6a,
		0x6c,0x6d,0x6e,0x70,0x71,0x72,0x74,0x75,0x76,0x78,0x79,0x7a,0x7c,0x7d,0x7e,0x7f,
		0x80,0x81,0x83,0x84,0x85,0x86,0x87,0x88,0x8a,0x8b,0x8c,0x8d,0x8e,0x8f,0x90,0x91,
		0x92,0x93,0x94,0x95,0x96,0x96,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9d,0x9e,0x9f,0xa0,
		0xa1,0xa1,0xa2,0xa3,0xa4,0xa5,0xa6,0xa7,0xa8,0xa8,0xa9,0xaa,0xab,0xac,0xad,0xae,
		0xaf,0xaf,0xb0,0xb1,0xb2,0xb2,0xb3,0xb4,0xb5,0xb5,0xb6,0xb7,0xb8,0xb8,0xb9,0xba,
		0xbb,0xbb,0xbc,0xbd,0xbe,0xbe,0xbf,0xc0,0xc0,0xc1,0xc2,0xc2,0xc3,0xc4,0xc5,0xc5,
		0xc6,0xc7,0xc7,0xc8,0xc9,0xc9,0xca,0xcb,0xcc,0xcc,0xcd,0xcd,0xce,0xcf,0xcf,0xd0,
		0xd0,0xd1,0xd2,0xd2,0xd3,0xd3,0xd4,0xd5,0xd5,0xd6,0xd6,0xd7,0xd8,0xd8,0xd9,0xd9,
		0xda,0xdb,0xdb,0xdc,0xdc,0xdd,0xde,0xde,0xdf,0xdf,0xe0,0xe1,0xe1,0xe2,0xe2,0xe3,
		0xe4,0xe4,0xe5,0xe5,0xe6,0xe6,0xe7,0xe7,0xe8,0xe8,0xe9,0xe9,0xea,0xeb,0xeb,0xec,
		0xec,0xed,0xed,0xee,0xee,0xef,0xef,0xf0,0xf1,0xf1,0xf2,0xf2,0xf3,0xf3,0xf4,0xf4,
		0xf5,0xf5,0xf6,0xf6,0xf7,0xf8,0xf8,0xf9,0xf9,0xfa,0xfa,0xfb,0xfb,0xfc,0xfc,0xfd,
		0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,0xfe,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[16] r = 2.8
		0x1d,0x23,0x2a,0x2e,0x32,0x37,0x3a,0x3d,0x40,0x44,0x46,0x48,0x4b,0x4d,0x4f,0x52,
		0x53,0x55,0x57,0x59,0x5b,0x5d,0x5f,0x61,0x62,0x64,0x65,0x67,0x68,0x6a,0x6b,0x6d,
		0x6f,0x70,0x71,0x72,0x74,0x75,0x76,0x77,0x79,0x7a,0x7b,0x7c,0x7e,0x7f,0x80,0x81,
		0x82,0x83,0x84,0x85,0x86,0x87,0x88,0x89,0x8b,0x8c,0x8d,0x8e,0x8f,0x90,0x91,0x92,
		0x93,0x94,0x95,0x96,0x97,0x97,0x98,0x99,0x9a,0x9b,0x9c,0x9c,0x9d,0x9e,0x9f,0xa0,
		0xa1,0xa1,0xa2,0xa3,0xa4,0xa5,0xa5,0xa6,0xa7,0xa8,0xa9,0xa9,0xaa,0xab,0xac,0xad,
		0xae,0xae,0xaf,0xb0,0xb0,0xb1,0xb2,0xb2,0xb3,0xb4,0xb5,0xb5,0xb6,0xb7,0xb7,0xb8,
		0xb9,0xb9,0xba,0xbb,0xbc,0xbc,0xbd,0xbd,0xbe,0xbf,0xbf,0xc0,0xc1,0xc1,0xc2,0xc3,
		0xc3,0xc4,0xc5,0xc5,0xc6,0xc7,0xc7,0xc8,0xc9,0xc9,0xca,0xca,0xcb,0xcc,0xcc,0xcd,
		0xcd,0xce,0xcf,0xcf,0xd0,0xd0,0xd1,0xd2,0xd2,0xd3,0xd3,0xd4,0xd5,0xd5,0xd6,0xd6,
		0xd7,0xd7,0xd8,0xd8,0xd9,0xd9,0xda,0xdb,0xdb,0xdc,0xdc,0xdd,0xdd,0xde,0xde,0xdf,
		0xe0,0xe0,0xe1,0xe1,0xe2,0xe2,0xe3,0xe3,0xe4,0xe4,0xe5,0xe5,0xe6,0xe6,0xe7,0xe7,
		0xe8,0xe8,0xe9,0xe9,0xea,0xea,0xeb,0xeb,0xec,0xec,0xec,0xed,0xed,0xee,0xee,0xef,
		0xef,0xf0,0xf0,0xf1,0xf1,0xf1,0xf2,0xf2,0xf3,0xf3,0xf4,0xf4,0xf5,0xf5,0xf6,0xf6,
		0xf7,0xf7,0xf8,0xf8,0xf9,0xf9,0xfa,0xfa,0xfb,0xfb,0xfc,0xfc,0xfd,0xfd,0xfe,0xfe,
		0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff,0xff
	},
	{	//rgb_gamma[17] r = 3.0
		0x21,0x27,0x2e,0x32,0x36,0x3b,0x3e,0x41,0x44,0x47,0x49,0x4c,0x4e,0x51,0x53,0x56,
		0x57,0x59,0x5b,0x5d,0x5e,0x60,0x62,0x64,0x65,0x67,0x68,0x6a,0x6b,0x6d,0x6e,0x70,
		0x72,0x73,0x74,0x75,0x76,0x77,0x79,0x7a,0x7b,0x7c,0x7d,0x7e,0x80,0x81,0x82,0x83,
		0x84,0x85,0x86,0x87,0x88,0x89,0x8a,0x8b,0x8d,0x8d,0x8e,0x8f,0x90,0x91,0x92,0x93,
		0x94,0x95,0x96,0x97,0x98,0x98,0x99,0x9a,0x9b,0x9c,0x9d,0x9d,0x9e,0x9f,0xa0,0xa1,
		0xa2,0xa2,0xa3,0xa4,0xa5,0xa5,0xa6,0xa7,0xa8,0xa8,0xa9,0xaa,0xab,0xab,0xac,0xad,
		0xae,0xae,0xaf,0xaf,0xb0,0xb1,0xb1,0xb2,0xb3,0xb3,0xb4,0xb5,0xb5,0xb6,0xb7,0xb7,
		0xb8,0xb9,0xb9,0xba,0xbb,0xbb,0xbc,0xbc,0xbd,0xbe,0xbe,0xbf,0xbf,0xc0,0xc1,0xc1,
		0xc2,0xc2,0xc3,0xc4,0xc4,0xc5,0xc5,0xc6,0xc7,0xc7,0xc8,0xc8,0xc9,0xc9,0xca,0xca,
		0xcb,0xcb,0xcc,0xcd,0xcd,0xce,0xce,0xcf,0xcf,0xd0,0xd0,0xd1,0xd2,0xd2,0xd3,0xd3,
		0xd4,0xd4,0xd5,0xd5,0xd6,0xd6,0xd7,0xd7,0xd8,0xd8,0xd9,0xd9,0xda,0xda,0xdb,0xdb,
		0xdc,0xdc,0xdc,0xdd,0xdd,0xde,0xde,0xdf,0xdf,0xe0,0xe0,0xe1,0xe1,0xe1,0xe2,0xe2,
		0xe3,0xe3,0xe4,0xe4,0xe5,0xe5,0xe6,0xe6,0xe7,0xe7,0xe7,0xe8,0xe8,0xe9,0xe9,0xea,
		0xea,0xeb,0xeb,0xec,0xec,0xec,0xed,0xed,0xee,0xee,0xef,0xef,0xf0,0xf0,0xf1,0xf1,
		0xf2,0xf2,0xf2,0xf3,0xf3,0xf4,0xf4,0xf5,0xf5,0xf5,0xf6,0xf6,0xf7,0xf7,0xf8,0xf8,
		0xf9,0xf9,0xf9,0xfa,0xfa,0xfb,0xfb,0xfb,0xfc,0xfc,0xfd,0xfd,0xfd,0xfe,0xfe,0xff
	},
};

