/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HAL_IIC_H
    #define  HAL_IIC_H

/*******************************************************************************
* Function Name  : hal_iicInit
* Description    : hal layer .iic0 initial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic0Init(void);

/*******************************************************************************
* Function Name  : hal_iicUninit
* Description    : hal layer .iic0 uninitial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic0Uninit(void);

/*******************************************************************************
* Function Name  : hal_iic8bitAddrWriteData
* Description    : hal layer .iic0 write data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 data    : data
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic08bitAddrWriteData(u8 slaveid,u8 addr,u8 data);

/*******************************************************************************
* Function Name  : hal_iic8bitAddrReadData
* Description    : hal layer .iic0 read data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 *data   : data buffer
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic08bitAddrReadData(u8 slaveid,u8 addr,u8 *data);

/*******************************************************************************
* Function Name  : hal_iic8bitAddrWrite
* Description    : hal layer .iic0 write data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 *data   : data buffer
				   u8 len     : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic08bitAddrWrite(u8 slaveid,u8 addr,u8 *data,u8 len);

/*******************************************************************************
* Function Name  : hal_iic8bitAddrRead
* Description    : hal layer .iic0 read data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 *data   : data buffer
				   u8 len     : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic08bitAddrRead(u8 slaveid,u8 addr,u8  *data,u8 len);

/*******************************************************************************
* Function Name  : hal_iic16bitAddrWriteData
* Description    : hal layer .iic write data for 16bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 data    : data
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic016bitAddrWriteData(u8 slaveid,u16 addr,u8 data);

/*******************************************************************************
* Function Name  : hal_iic16bitAddrReadData
* Description    : hal layer .iic0 read data for 16bit address slave
* Input          : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len     : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic016bitAddrReadData(u8 slaveid,u16 addr,u8 *data);

/*******************************************************************************
* Function Name  : hal_iic16bitAddrWrite
* Description    : hal layer .iic0 write data for 16bit address slave
* Input          : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len     : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic016bitAddrWrite(u8 slaveid,u16 addr,u8 *data,u8 len);

/*******************************************************************************
* Function Name  : hal_iic16bitAddrRead
* Description    : hal layer .iic0 read data for 16bit address slave
* Input          : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len     : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic016bitAddrRead(u8 slaveid,u16 addr,u8  *data,u8 len);

/*******************************************************************************
* Function Name  : hal_iic1IOShare
* Description    : hal layer .iic1 io share
* Input          : none
* Output         : None
* Return         : u8 : 0->current is iic1 using,1->other using
*******************************************************************************/
u8 hal_iic1IOShare(void);

/*******************************************************************************
* Function Name  : hal_uartIOShareCheck
* Description    : hal layer.uart io share flag check
* Input          : none
* Output         : None
* Return         : 
*******************************************************************************/
void hal_iic1IOShareCheck(void);

/*******************************************************************************
* Function Name  : hal_iicInit
* Description    : hal layer .iic1 initial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic1Init(void);

/*******************************************************************************
* Function Name  : hal_iicUninit
* Description    : hal layer .iic1 uninitial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic1Uninit(void);

/*******************************************************************************
* Function Name  : hal_iic8bitAddrWriteData
* Description    : hal layer .iic1 write data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 data    : data
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic18bitAddrWriteData(u8 slaveid,u8 addr,u8 data);

/*******************************************************************************
* Function Name  : hal_iic8bitAddrReadData
* Description    : hal layer .iic1 read data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 *data   : data buffer
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic18bitAddrReadData(u8 slaveid,u8 addr,u8 *data);

/*******************************************************************************
* Function Name  : hal_iic8bitAddrWrite
* Description    : hal layer .iic1 write data for 8bit address slave
* Input          : u8 slaveid : slave id
				   u8 addr    : slave addr
				   u8 *data   : data buffer
				   u8 len     : data length
* Output         : None
* Return         : none
*******************************************************************************/
void hal_iic18bitAddrWrite(u8 slaveid,u8 addr,u8 *data,u8 len);

/*******************************************************************************
* Function Name  : hal_iic8bitAddrRead
* Description	 : hal layer .iic1 read data for 8bit address slave
* Input		     : u8 slaveid : slave id
				   u8 addr	  : slave addr
				   u8 *data   : data buffer
				   u8 len	  : data length
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_iic18bitAddrRead(u8 slaveid,u8 addr,u8  *data,u8 len);

/*******************************************************************************
* Function Name  : hal_iic16bitAddrWriteData
* Description	 : hal layer .iic write data for 16bit address slave
* Input 		 : u8 slaveid : slave id
				   u8 addr	  : slave addr
				   u8 data	  : data
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_iic116bitAddrWriteData(u8 slaveid,u16 addr,u8 data);

/*******************************************************************************
* Function Name  : hal_iic16bitAddrReadData
* Description	 : hal layer .iic1 read data for 16bit address slave
* Input		     : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len	  : data length
* Output		 : None
* Return		 : none
*******************************************************************************/
void hal_iic116bitAddrReadData(u8 slaveid,u16 addr,u8 *data);

/*******************************************************************************
* Function Name  : hal_iic16bitAddrWrite
* Description	 : hal layer .iic1 write data for 16bit address slave
* Input		     : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len	  : data length
* Output		  : None
* Return		  : none
*******************************************************************************/
void hal_iic116bitAddrWrite(u8 slaveid,u16 addr,u8 *data,u8 len);

/*******************************************************************************
* Function Name  : hal_iic16bitAddrRead
* Description	 : hal layer .iic1 read data for 16bit address slave
* Input		     : u8 slaveid : slave id
				   u16 addr   : slave addr
				   u8 *data   : data buffer
				   u8 len	  : data length
* Output		  : None
* Return		  : none
*******************************************************************************/
void hal_iic116bitAddrRead(u8 slaveid,u16 addr,u8  *data,u8 len);




#endif


