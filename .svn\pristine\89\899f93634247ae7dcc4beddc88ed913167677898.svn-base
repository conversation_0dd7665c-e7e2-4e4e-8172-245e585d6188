#ifndef  WAV_TYPEDEF_H
       #define  WAV_TYPEDEF_H

#define WAV_TAG_RIFF     				0x46464952
#define WAV_TAG_WAVE     				0x45564157
#define WAV_TAG_FMT      				0x20746d66
#define WAV_TAG_DATA     				0x61746164
#define WAV_TAG_FACT     				0x74636166	


#define MS_ADPCM_COFNUM    				7

//WAV ERROR DEFINE
#define WAV_ERR_NONE          	 		0   // no error
#define WAV_ERR_GENERAL       			-1   // general error
#define WAV_ERR_READ          			-2   // read error
#define WAV_ERR_WRITE         			-3  // write error
#define WAV_ERR_MEMORY        			-4  // memory error
#define WAV_ERR_TYPE        			-4  // wav type error
#define WAV_ERR_ENCODE        			-5  // wav encode error
#define WAV_ERR_DECODE        			-6  // wav encode error
#define WAV_ERR_CACHE        			-7  // wav decode cache read error
#define WAV_ERR_TAG        				-8  // wav decode TAG error
#define WAV_ERR_COFNUM        			-9  // wav decode cofnum error
#define WAV_ERR_DATA        			-10  // wav decode DATA error
typedef struct WAV_RIFFCHUNK_S{
	u32 dwId;
	u32 dwSize;
	u32 dwType;
}WAV_RIFFCHUNK_T;

typedef struct WAV_FMTCHUNK_S{
	u16 wFmtTag;
	u16 wChannels;
	u32 dwSamplesPerSec;
	u32 dwAvgBytesPerSec;
	u16 wBlockAlign;
	u16 wBitsPerSample;
}WAV_FMTCHUNK_T;

typedef struct WAV_ADPCM_COEFSET_S{
	s16	iCoef1;
	s16	iCoef2;
}WAV_ADPCM_COEFSET_T;

typedef struct WAV_PCMFMTCHUNK_S{
	u32 			dwFmtId;
	u32 			dwFmtSize;
	WAV_FMTCHUNK_T	wfmt;
}WAV_PCMFMTCHUNK_T;
typedef struct WAV_LAWFMTCHUNK_S{
	u32 			dwFmtId;
	u32 			dwFmtSize;	
	WAV_FMTCHUNK_T	wfmt;
	u16				wCbsize;
}WAV_LAWFMTCHUNK_T;
typedef struct WAV_IMAADFMTCHUNK_S{
	u32 			dwFmtId;
	u32 			dwFmtSize;
	WAV_FMTCHUNK_T	wfmt;
	u16				wCbsize;
	u16				wCbdata;
}WAV_IMAADFMTCHUNK_T;
typedef struct WAV_MSADFMTCHUNK_S{
	u32 			dwFmtId;
	u32 			dwFmtSize;
	WAV_FMTCHUNK_T	wfmt;
	u16				wCbsize;
	u16				wSamplesPerBlock;
	u16				wNumCoef;
	WAV_ADPCM_COEFSET_T	aCoeff[MS_ADPCM_COFNUM];
}WAV_MSADFMTCHUNK_T;

typedef struct WAV_FACTCHUNK_S{
	u32				dwFactId;
	u32				dwFactSize;
	u32				dWFactInfo;
}WAV_FACTCHUNK_T;

typedef struct WAV_DATACHUNK_S{
	u32 			dwDataId;
	u32 			dwDataSize;
}WAV_DATACHUNK_T;

typedef struct WAW_PCMHEADER_S{
	WAV_RIFFCHUNK_T		riff_chunk;
	WAV_PCMFMTCHUNK_T	fmt_chunk;
	WAV_DATACHUNK_T		dat_chunk;
}__attribute__((packed)) WAV_PCMHEADER_T;
typedef struct WAW_LAWPCMHEADER_S{
	WAV_RIFFCHUNK_T		riff_chunk;
	WAV_LAWFMTCHUNK_T	fmt_chunk;
	WAV_FACTCHUNK_T		fact_chunk;
	WAV_DATACHUNK_T		dat_chunk;
}__attribute__((packed)) WAV_LAWPCMHEADER_T;

typedef struct WAW_IMAADPCMHEADER_S{
	WAV_RIFFCHUNK_T		riff_chunk;
	WAV_IMAADFMTCHUNK_T	fmt_chunk;
	WAV_FACTCHUNK_T		fact_chunk;
	WAV_DATACHUNK_T		dat_chunk;
}__attribute__((packed)) WAV_IMAADPCMHEADER_T;

typedef struct WAW_MSADPCMHEADER_S{
	WAV_RIFFCHUNK_T		riff_chunk;
	WAV_MSADFMTCHUNK_T	fmt_chunk;
	WAV_FACTCHUNK_T		fact_chunk;
	WAV_DATACHUNK_T		dat_chunk;
}__attribute__((packed)) WAV_MSADPCMHEADER_T;

typedef union WAV_HEADER_S{
	WAV_PCMHEADER_T			pcm_h;		
	WAV_LAWPCMHEADER_T		lawpcm_h;
	WAV_IMAADPCMHEADER_T	imaadpcm_h;
	WAV_MSADPCMHEADER_T		msadpcm_h;
}WAV_HEADER_T;





typedef struct MAX_WAV_S
{
	INT32U filesize;	

    INT16U  fmtcode;
    INT16U  channel;
	INT32U  samplerate;
	INT32U  bitrate;

	INT16U  blocksize;
	INT16U  samplesize;

	INT16U  cbsize;
	INT16U  samplepreblock;
    INT16U  cofnum;
	INT32U  coftable[MS_ADPCM_COFNUM];

	INT32U  factsize;


    INT32U  datasize;
	INT32U  dataoffset;
}MAX_WAV_T;


enum
{
    WAV_TYPE_PCM		= 1,
	WAV_TYPE_ALAW		= 6,
	WAV_TYPE_ULAW		= 7,
	WAV_TYPE_MSADPCM	= 2,
	WAV_TYPE_IMADPCM	= 0x11,
	WAV_TYPE_AUTO		= 0
}WAV_TYPE_E;
enum//BIT[0]:input ch,  0: 1 ch , 1: 2ch; BIT[1] output ch 
{
	MONO_TO_MONO 	= 0x00,
	STRE0_TO_MONO	= 0x01,
	MONO_TO_STREO   = 0x02,
	STREO_TO_STREO	= 0x03
}CHANNEL_EXCHANGE_E;



typedef struct WAV_INFO_S
{
    INT16U type;
	INT16U channel;

	INT32U samplerate;
	INT32U totaltime;  //ms
}WAV_INFO_T;




#endif
