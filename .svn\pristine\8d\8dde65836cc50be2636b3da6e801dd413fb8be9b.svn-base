/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
/*******************************************************************************
* Function Name  : taskPowerOffOpen
* Description    : taskPowerOffOpen
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskPowerOffOpen(uint32 arg)
{
	// if(SysCtrl.dev_stat_battery  == 0)
	// {
	// 	u32 temp;
	// 	hal_sysUninit();
	// 	task_com_usbhost_set(USBHOST_STAT_OUT);
	// 	husb_api_usensor_detech();
	// 	dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_BK_WRITE, 0); // back light off
	// 	dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_PWR_CHECK, (INT32U)&temp);
	// 	if(!(temp&& (SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)))
	// 	{
	// 		hal_wkiWakeupTriger(1); //wki wakeup rising trigger
	// 		hal_wkiCleanPending();
	// 		hal_vddWKOEnable(0);
	// 		hx330x_sysCpuMsDelay(50);
	// 		while(1);
	// 	}
	// }
	app_lcdCsiVideoShowStop();
	task_com_usbhost_set(USBHOST_STAT_OUT);
	husb_api_usensor_detech();
	// #if POWER_OFF_DONT_SHOW_LOGO == 0
    app_logo_show(0,1,0);                                       // power off.music en,wait music end
	//#endif
	XOSTimeDly(100);
    uiWinUninit();
	app_uninit();
}

ALIGNED(4) sysTask_T taskPowerOff =
{
	"Power Off",
	0,
	taskPowerOffOpen,
	NULL,
	NULL,
};


