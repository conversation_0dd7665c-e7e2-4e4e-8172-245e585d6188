/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT == LCD_RGB_ILI9806E_4522

#define CMD(x)    LCD_CMD_RGB_DAT(x)
#define DAT(x)    LCD_CMD_RGB_DAT((x)|(1<<8))
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
CMD(0xFF),
DAT(0xFF),
DAT(0x98),
DAT(0x06),
DAT(0x04),
DAT(0x01),     

CMD(0x08),
DAT(0x10),                

// CMD(0x20),
// DAT(0x01),

CMD(0x21),
DAT(0x01),                 

CMD(0x30),
DAT(0x01),                
CMD(0x31),
DAT(0x00),               

CMD(0x40),
DAT(0x14),             
CMD(0x41),   // DVDDH DVDDL clamp
DAT(0x22),              
CMD(0x42),
DAT(0x02),                 
CMD(0x43),   // VGH/VGL
DAT(0x89),                 
CMD(0x44),   // VGH/VGL
DAT(0x8A),             

CMD(0x50),   // VGMP
DAT(0x98),                
CMD(0x51),   // VGMN
DAT(0x98),               
CMD(0x52),   //Flicker
DAT(0x00),                   
CMD(0x53),   //Flicker
DAT(0x38),    //8c             

CMD(0x60),
DAT(0x07),                 
CMD(0x61),
DAT(0x06),               
CMD(0x62),
DAT(0x06),                
CMD(0x63),
DAT(0x04),               

//****************************************************************
// Gamma Setting 

CMD(0xFF),
DAT(0xFF),
DAT(0x98),
DAT(0x06),
DAT(0x04),
DAT(0x01),    

CMD(0xA0),
DAT(0x00),  // Gamma 0 

CMD(0xA1),
DAT(0x0E),  // Gamma 4 

CMD(0xA2),
DAT(0x19),  // Gamma 8

CMD(0xA3),
DAT(0x10),  // Gamma 16

CMD(0xA4),
DAT(0x06),  // Gamma 24

CMD(0xA5),
DAT(0x0F),  // Gamma 52

CMD(0xA6),
DAT(0x09),  // Gamma 80

CMD(0xA7),
DAT(0x06),  // Gamma 108

CMD(0xA8),
DAT(0x0C),  // Gamma 147

CMD(0xA9),
DAT(0x0E),  // Gamma 175

CMD(0xAA),
DAT(0x16),  // Gamma 203

CMD(0xAB),
DAT(0x0D),  // Gamma 231

CMD(0xAC),
DAT(0x15),  // Gamma 239

CMD(0xAD),
DAT(0x0F),  // Gamma 247

CMD(0xAE),
DAT(0x11),  // Gamma 251

CMD(0xAF),
DAT(0x00),  // Gamma 255

//****************************************************************
//Nagitive

CMD(0xC0),
DAT(0x00),  // Gamma 0 

CMD(0xC1),
DAT(0x24),  // Gamma 4

CMD(0xC2),
DAT(0x29),  // Gamma 8

CMD(0xC3),
DAT(0x0C),  // Gamma 16

CMD(0xC4),
DAT(0x07),  // Gamma 24

CMD(0xC5),
DAT(0x03), // Gamma 52

CMD(0xC6),
DAT(0x03),  // Gamma 80

CMD(0xC7),
DAT(0x03),  // Gamma 108

CMD(0xC8),
DAT(0x03),  // Gamma 147

CMD(0xC9),
DAT(0x09),  // Gamma 175

CMD(0xCA),
DAT(0x0D),  // Gamma 203

CMD(0xCB),
DAT(0x01),  // Gamma 231

CMD(0xCC),
DAT(0x06),  // Gamma 239

CMD(0xCD),
DAT(0x1B),  // Gamma 247

CMD(0xCE),
DAT(0x08),  // Gamma 251

CMD(0xCF),
DAT(0x00),  // Gamma 255

//****************************************************************
//Page 6 Command 
CMD(0xFF),
DAT(0xFF),
DAT(0x98),
DAT(0x06),
DAT(0x04),
DAT(0x06),     // Change to Page 6

CMD(0x00),
DAT(0x20),

CMD(0x01),
DAT(0x04),

CMD(0x02),
DAT(0x00),    

CMD(0x03),
DAT(0x00),

CMD(0x04),
DAT(0x01),

CMD(0x05),
DAT(0x01),

CMD(0x06),
DAT(0x88),    

CMD(0x07),
DAT(0x04),

CMD(0x08),
DAT(0x01),

CMD(0x09),
DAT(0x90),    

CMD(0x0A),
DAT(0x03),    

CMD(0x0B),
DAT(0x01),    

CMD(0x0C),
DAT(0x01),

CMD(0x0D),
DAT(0x01),

CMD(0x0E),
DAT(0x00),

CMD(0x0F),
DAT(0x00),

CMD(0x10),
DAT(0x55),

CMD(0x11),
DAT(0x53),

CMD(0x12),
DAT(0x01),

CMD(0x13),
DAT(0x0D),

CMD(0x14),
DAT(0x0D),

CMD(0x15),
DAT(0x43),

CMD(0x16),
DAT(0x0B),

CMD(0x17),
DAT(0x00),

CMD(0x18),
DAT(0x00),

CMD(0x19),
DAT(0x00),

CMD(0x1A),
DAT(0x00),

CMD(0x1B),
DAT(0x00),

CMD(0x1C),
DAT(0x00),

CMD(0x1D),
DAT(0x00),

CMD(0x20),
DAT(0x01),

CMD(0x21),
DAT(0x23),

CMD(0x22),
DAT(0x45),

CMD(0x23),
DAT(0x67),

CMD(0x24),
DAT(0x01),

CMD(0x25),
DAT(0x23),

CMD(0x26),
DAT(0x45),

CMD(0x27),
DAT(0x67),

CMD(0x30),
DAT(0x02),

CMD(0x31),
DAT(0x22),

CMD(0x32),
DAT(0x11),

CMD(0x33),
DAT(0xAA),

CMD(0x34),
DAT(0xBB),

CMD(0x35),
DAT(0x66),

CMD(0x36),
DAT(0x00),

CMD(0x37),
DAT(0x22),

CMD(0x38),
DAT(0x22),

CMD(0x39),
DAT(0x22),

CMD(0x3A),
DAT(0x22),

CMD(0x3B),
DAT(0x22),

CMD(0x3C),
DAT(0x22),

CMD(0x3D),
DAT(0x22),

CMD(0x3E),
DAT(0x22),

CMD(0x3F),
DAT(0x22),

CMD(0x40),
DAT(0x22),

//****************************************************************
// Page 5 Command 
CMD(0xFF),
DAT(0xFF),
DAT(0x98),
DAT(0x06),
DAT(0x04),
DAT(0x05),   

CMD(0x09),
DAT(0xFC), 

CMD(0x07),
DAT(0xBC), 

//****************************************************************

CMD(0xFF),
DAT(0xFF),
DAT(0x98),
DAT(0x06),
DAT(0x04),
DAT(0x00),   

CMD(0x3A),
DAT(0x55),   

CMD(0x11),        // Sleep-Out
DLY(120),       

CMD(0x29),        // Display On
DLY(10), 
LCD_INIT_TAB_END()
//关屏
LCD_UNINIT_TAB_BEGIN()  
    CMD(0x28),
    DLY(10),
LCD_UNINIT_TA_ENDB()

LCD_DESC_BEGIN()
    .name 			= "RGB_ILI9806E",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_16,

    .pclk_div 		= LCD_PCLK_DIV(24000000),
    .clk_per_pixel 	= 1,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .pclk_edge      = LCD_PCLK_EDGE_FALLING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw 			= 4,
    .vbp 			= 10,
    .vfp 			= 10,

    .hlw 			= 10,
    .hbp 			= 40,
    .hfp 			= 80,

    LCD_SPI_DEFAULT(9),

    .data_mode 	= LCD_DATA_MODE0_16BIT_RGB565,

    .screen_w 		= 480,
    .screen_h 		= 854,

    .video_w  		= 852,
    .video_h 	 	= 480,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= 0,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
    UNINIT_TAB_INIT
LCD_DESC_END()


#endif








