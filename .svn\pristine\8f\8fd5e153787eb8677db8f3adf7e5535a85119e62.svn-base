/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

#define DEV_KEY_SCANTIME	  		3
#define DEV_KEY_CONTINUE_INTERVAL  (10)

typedef struct DEV_KEY_OP_S
{
	u16  keyType;      // key event
	u16  longkeyType;
	u16  longPressTh;  // if longPressTh > 0, when press long ,return longkeyType 
	u16  keyADCvalue;  // adc value of key, MAX  1024 means 3.3V
	u16  keyADCmin;    
	u16  keyADCmax;   
}DE<PERSON>_KEY_OP_T;

typedef struct DEV_KEY_CTL_S
{
	u16  key_scan_nolongtype; // 0: scan longtype, 1: no scan longtype
	u16  cur_scan_event_sp; //1: key have longtype but short press
	u16	 key_press_cnt;
	u16  key_scan_time;
	u16  cur_key_ADCvalue;
	s16  key_continue_cnt;
	
	u16  cur_key_event;	
	u16  cur_longkeytype;
	u16  cur_longkeyth;
	u16  cur_scan_event;  
	
	
	u16  last_key_event;
	u16  last_longkeytype;
	u16  last_longkeyth;	
	u16  last_scan_event;
	
	
}DEV_KEY_CTL_T;
ALIGNED(4) static DEV_KEY_CTL_T dev_key_ctl;
ALIGNED(4) DEV_KEY_OP_T dev_key_tab[10];

/*******************************************************************************
* Function Name  : dev_key_init
* Description    : dev_key_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_key_init(void)
{
	if(hardware_setup.adkey_en)
	{
		int i;
		if(hardware_setup.adkey_use_inner_10k)
		{
			hardware_setup.adkey_pullup_resistance += 10000;
			hal_gpioInit(hardware_setup.adkey_ch, hardware_setup.adkey_pin,GPIO_INPUT,GPIO_PULL_UP); //ADKEY0 PULL 
		}else
		{
			hal_gpioInit(hardware_setup.adkey_ch, hardware_setup.adkey_pin,GPIO_INPUT,GPIO_PULL_FLOATING); //ADKEY0 PULL 
		}
		for(i = 0; i < hardware_setup.adkey_num; i++)
		{
			dev_key_tab[i].keyType 		= hardware_setup.adkey_tab[i].keytype;
			dev_key_tab[i].longkeyType	= hardware_setup.adkey_tab[i].longkeytype;
			dev_key_tab[i].longPressTh	= hardware_setup.adkey_tab[i].longkeyTime;
			dev_key_tab[i].keyADCvalue	= ((u32)hardware_setup.adkey_tab[i].resistance * 1024)/
										  (hardware_setup.adkey_tab[i].resistance + hardware_setup.adkey_pullup_resistance);
			if(dev_key_tab[i].keyADCvalue > hardware_setup.adkey_value_range)
				dev_key_tab[i].keyADCmin = dev_key_tab[i].keyADCvalue - hardware_setup.adkey_value_range;
			dev_key_tab[i].keyADCmax =  dev_key_tab[i].keyADCvalue + hardware_setup.adkey_value_range;
			if(dev_key_tab[i].keyADCmax > 1024)
				dev_key_tab[i].keyADCmax = 1024;
		}
		if(hardware_setup.pwrkey_use_adc == 0)
		{
			hal_gpioInit(hardware_setup.pwrkey_ch,hardware_setup.pwrkey_pin,GPIO_INPUT,GPIO_PULL_FLOATING);
		}
		hx330x_bytes_memset((u8*)&dev_key_ctl,0, sizeof(dev_key_ctl));
		dev_key_ctl.key_continue_cnt	= 0;
		return 0;
	}else
		return -1;
}

/*******************************************************************************
* Function Name  : dev_key_ADCScan
* Description    : dev_key_ADCScan, updata dev_key_ctl.cur_key_event
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
static int dev_key_ADCScan(void)
{
	int value 	= 0;
	u32 i;
	u32 key  	= 0;
	dev_key_ctl.cur_longkeytype = 0;
	dev_key_ctl.cur_longkeyth   = 0;
	
	if(hardware_setup.pwrkey_use_adc == 0)
	{
		if(hardware_setup.pwrkey_valid)
		{
			value = hal_gpioRead(hardware_setup.pwrkey_ch, hardware_setup.pwrkey_pin);
		}else
		{
			value = !hal_gpioRead(hardware_setup.pwrkey_ch, hardware_setup.pwrkey_pin);
		}
	}

	if(value)
	{
		key = KEY_EVENT_POWER;   // power key ,key value
		dev_key_ctl.cur_longkeytype = KEY_EVENT_POWEROFF;
		dev_key_ctl.cur_longkeyth   = 10;	
		//dev_key_ctl.key_scan_time   = DEV_KEY_SCANTIME;	
	}
	else
	{
		value = hal_adcGetChannel(hardware_setup.adkey_pos);
		if(value <= 1024)
			dev_key_ctl.cur_key_ADCvalue = value;
		for(i = 0; i < hardware_setup.adkey_num; i++)
		{
			if(hx330x_data_check(value, dev_key_tab[i].keyADCmin, dev_key_tab[i].keyADCmax) == true)
			{
				key = dev_key_tab[i].keyType;
				dev_key_ctl.cur_longkeytype = dev_key_tab[i].longkeyType;
				dev_key_ctl.cur_longkeyth   = dev_key_tab[i].longPressTh;	
			}
		}
	}
	if(key != dev_key_ctl.cur_scan_event)
	{
		dev_key_ctl.cur_scan_event = key;
		dev_key_ctl.key_scan_time  = 1;
	}else
	{
		dev_key_ctl.key_scan_time++;
	}
	if(dev_key_ctl.key_scan_time > DEV_KEY_SCANTIME)
	{
		dev_key_ctl.key_scan_time = 0;
	
		return 0;
	}
	return -1;
} 
/*******************************************************************************
* Function Name  : dev_key_ADCScan
* Description    : dev_key_ADCScan
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
static u32 dev_keyEvent_Scan(void)
{
	u32 key_event = KEY_EVENT_END;
	if(dev_key_ADCScan() >= 0)  //dev_key_ctl.cur_scan_event 
	{
		key_event = dev_key_ctl.cur_scan_event;
		//if(key_event)
		//	deg_Printf("key_event:%d\n", key_event);
		//if((dev_key_ctl.last_longkeyth > 0) || (dev_key_ctl.cur_longkeyth > 0) )
		//{
		//	deg_Printf("press_cnt:%d\n",dev_key_ctl.key_press_cnt);
		//	deg_Printf("last:%d,%d,%d\n",dev_key_ctl.last_scan_event,dev_key_ctl.last_longkeytype,dev_key_ctl.last_longkeyth);
		//	deg_Printf("cur:%d,%d,%d\n",dev_key_ctl.cur_scan_event,dev_key_ctl.cur_longkeytype,dev_key_ctl.cur_longkeyth);
		//}
		if(dev_key_ctl.last_scan_event != dev_key_ctl.cur_scan_event) //不同按键按下
		{
			//if(dev_key_ctl.last_scan_event || dev_key_ctl.cur_scan_event)
			//deg_Printf("111:%d,%d,%d,%d\n", dev_key_ctl.last_scan_event, dev_key_ctl.cur_scan_event, dev_key_ctl.last_longkeyth, dev_key_ctl.key_press_cnt);
			if(dev_key_ctl.key_scan_nolongtype == 0 || dev_key_ctl.cur_longkeytype == KEY_EVENT_POWEROFF || dev_key_ctl.last_longkeytype == KEY_EVENT_POWEROFF)
			{
				if(dev_key_ctl.last_longkeyth > 0) //上一次按键有longkeytype，释放按键，发送longkeytype
				{
					if(dev_key_ctl.key_press_cnt < dev_key_ctl.last_longkeyth) //longkeytype shortpress, return keytype
					{
						key_event = dev_key_ctl.last_scan_event;
						dev_key_ctl.cur_scan_event_sp = 1;
					}
				}else if(dev_key_ctl.cur_longkeyth > 0) //当前按键有longkeytype，先不发送按键，等待按键释放
				{
					key_event = KEY_EVENT_END;
					dev_key_ctl.cur_scan_event_sp = 0;
				}

			}

			dev_key_ctl.last_longkeytype	= dev_key_ctl.cur_longkeytype;
			dev_key_ctl.last_longkeyth 		= dev_key_ctl.cur_longkeyth;
			dev_key_ctl.last_scan_event		= dev_key_ctl.cur_scan_event;
			dev_key_ctl.key_press_cnt 	= 0;			
		}else
		{
			//if(dev_key_ctl.last_scan_event || dev_key_ctl.cur_scan_event)
			//deg_Printf("2222:%d,%d,%d,%d\n", dev_key_ctl.last_scan_event, dev_key_ctl.cur_scan_event, dev_key_ctl.last_longkeyth, dev_key_ctl.key_press_cnt);
			if(dev_key_ctl.key_press_cnt < 0xffff)
				dev_key_ctl.key_press_cnt++;
			if(dev_key_ctl.key_scan_nolongtype == 0 || dev_key_ctl.cur_longkeytype == KEY_EVENT_POWEROFF || dev_key_ctl.last_longkeytype == KEY_EVENT_POWEROFF)
			{
				if(dev_key_ctl.last_longkeyth > 0)
				{
					//if((dev_key_ctl.key_press_cnt%dev_key_ctl.last_longkeyth) == 0)
					if(dev_key_ctl.key_press_cnt > dev_key_ctl.last_longkeyth)	
					{
						key_event = dev_key_ctl.last_longkeytype;
						//dev_key_ctl.key_press_cnt  = 1;
					}else
					{
						key_event = KEY_EVENT_END;
					}
				}
			}

		}
	}
	return key_event;
}
/*******************************************************************************
* Function Name  : dev_key_ADCScan
* Description    : dev_key_ADCScan,updata dev_key_ctl.last_key_event
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
static u32 dev_key_Scan(void)
{
	u32 keysta  = 0;
	u32 keyEvent = dev_keyEvent_Scan(); //dev_key_ctl.cur_key_event
	if(keyEvent != KEY_EVENT_END)
	{
		//if(keyEvent || dev_key_ctl.cur_key_event)
		//deg_Printf("dev_key_Scan:%d,%d\n", keyEvent, dev_key_ctl.cur_key_event);
		if(dev_key_ctl.cur_key_event != keyEvent)
		{

			if(keyEvent >= KEY_EVENT_START && keyEvent < KEY_EVENT_END)
			{
				keysta = (u32)makeMSG(keyEvent,KEY_PRESSED);		
			}
				
			else
			{
				if(dev_key_ctl.cur_key_event >= KEY_EVENT_START && dev_key_ctl.cur_key_event < KEY_EVENT_END)
					keysta = (u32)makeMSG(dev_key_ctl.cur_key_event,KEY_RELEASE);
			}
			//if(keysta)
			//	deg_Printf("keyEvent:%x, keysta:%x\n",keyEvent,keysta);
			if(dev_key_ctl.cur_scan_event_sp)
			{
				dev_key_ctl.cur_scan_event_sp = 0;
				dev_key_ctl.cur_key_event	  = 0;
			}else
			{
				dev_key_ctl.cur_key_event = keyEvent;	
			}
			
			dev_key_ctl.key_continue_cnt = 0;
		}else
		{
			if(dev_key_ctl.cur_key_event)
			{
				if(dev_key_ctl.key_continue_cnt < DEV_KEY_CONTINUE_INTERVAL)
					dev_key_ctl.key_continue_cnt++;
				if(dev_key_ctl.key_continue_cnt >= DEV_KEY_CONTINUE_INTERVAL)
				{
					keysta = (u32)makeMSG(dev_key_ctl.cur_key_event,KEY_CONTINUE);
					//dev_key_ctl.key_continue_cnt = 0;
				}
					
			}
			//if(keysta)
			//	deg_Printf("keyEvent:%x, keysta:%x\n",keyEvent,keysta);
		}	
	}
	return keysta;
	
}
/*******************************************************************************
* Function Name  : dev_key_ioctrl
* Description    : dev_key_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_key_ioctrl(u32 op, u32 para)
{
	if(hardware_setup.adkey_en)
	{
		int value;
		switch(op)
		{
			case DEV_KEY_AD_READ:
			{
				value = dev_key_Scan();
				if(para)
					*((u32*)para) = value;
				if(value == 0)
					return -1;
				break;	
			}	
			case DEV_KEY_POWER_READ:
			{
				
				if(hardware_setup.pwrkey_use_adc == 0)
				{
					if(hardware_setup.pwrkey_valid)
					{
						value = hal_gpioRead(hardware_setup.pwrkey_ch, hardware_setup.pwrkey_pin);
					}else
					{
						value = !hal_gpioRead(hardware_setup.pwrkey_ch, hardware_setup.pwrkey_pin);
					}
				}else
				{	
					int i, adcvalue;
					value = 0;
					adcvalue = hal_adcGetChannel(hardware_setup.adkey_pos);
					for(i = 0; i < hardware_setup.adkey_num; i++)
					{
						if(hx330x_data_check(adcvalue, dev_key_tab[i].keyADCmin, dev_key_tab[i].keyADCmax) == true)
						{
							if(dev_key_tab[i].keyType == KEY_EVENT_POWER)
							{
								value = 1; 
							}
						}
					}			
				}
				if(para)
					*((u32*)para) = value;

				break;
			}
			case DEV_KEY_AD_WRITE:
				break;
			case DEV_KEY_POWER_WRITE:
				hal_gpioInit(hardware_setup.pwrkey_ch, hardware_setup.pwrkey_pin,GPIO_OUTPUT,GPIO_PULL_DOWN);
				hal_gpioWrite(hardware_setup.pwrkey_ch, hardware_setup.pwrkey_pin,GPIO_LOW);
				break;
		}
		return 0;		
	}else
	{
		return -1;
	}	
}
/*******************************************************************************
* Function Name  : getKeyADCvalue
* Description    : getKeyADCvalue
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
u32 getKeyADCvalue(void)
{
	return dev_key_ctl.cur_key_ADCvalue;
}
/*******************************************************************************
* Function Name  : getKeyADCvalue
* Description    : getKeyADCvalue
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
u32 getKeyCurEvent(void)
{
	return dev_key_ctl.cur_key_event;
}
/*******************************************************************************
* Function Name  : keyLongTypeScanModeSet
* Description    : keyLongTypeScanModeSet
* Input          : nolongtype: 1: no scan longtype press
* Output         : none                                            
* Return         : none
*******************************************************************************/
void keyLongTypeScanModeSet(u8 nolongtype)
{
	dev_key_ctl.key_scan_nolongtype = nolongtype;
}