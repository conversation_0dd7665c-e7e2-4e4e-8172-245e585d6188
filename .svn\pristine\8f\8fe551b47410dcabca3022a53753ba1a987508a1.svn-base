/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HAL_WDT_H
#define  HAL_WDT_H


/*******************************************************************************
* Function Name  : hal_wdtEnable(u8 en)
* Description    : enable watch dog
* Input          : u8 en : 1-enable,1-disable
* Output         : None
* Return         : None
*******************************************************************************/
#define hal_wdtEnable 		hx330x_wdtEnable
/*******************************************************************************
* Function Name  : hal_wdtClear(void)
* Description    : clear watch dog 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
#define hal_wdtClear 		hx330x_wdtClear
/*******************************************************************************
* Function Name  : hal_wdtReset(void)
* Description    : system reset  using wdt
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
#define hal_wdtReset		hx330x_wdtReset





#endif


