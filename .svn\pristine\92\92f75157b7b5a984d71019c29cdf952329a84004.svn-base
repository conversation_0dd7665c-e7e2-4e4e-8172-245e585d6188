/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_LCD_ROTATE_H
#define HX330X_LCD_ROTATE_H

/*******************************************************************************
* Function Name  : hx330x_rotateISRRegiser
* Description    : rotate isr register
* Input          : void (*isr)(void) : isr
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_rotateISRRegiser(void (*isr)(void));
/*******************************************************************************
* Function Name  : hx330x_rotateCheckBusy
* Description    : rotate if not busy
* Input          : 
* Output         : none
* Return         : false : free, true : busy
*******************************************************************************/
bool hx330x_rotateCheckBusy(void);
/*******************************************************************************
* Function Name  : hx330x_rotateReset
* Description    : rotate hardware reset
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_rotateReset(void);
/*******************************************************************************
* Function Name  : hx330x_rotateStart
* Description    : config and start rotate
* Input          : u8 src_mode, 0 : video,1 : ui layer
                   u32 src_width,
                   u32 src_height,
                   u8 *dst_y : dst y addr(aligned 32)
                   u8 *dst_uv : dst uv addr(aligned 32)
                   u32 dst_stride : dst stride(aligned 4byte)
                   u8 *src_y : src y addr(aligned 32)
                   u8 *src_uv : src uv addr(aligned 32)
                   u32 src_stride : src stride(aligned 4byte)
                   u8 int_en : frame done init en
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_rotateStart(
    u8 src_mode,
    u32 src_width,u32 src_height,
    u8 *dst_y,u8 *dst_uv,u32 dst_stride,
    u8 *src_y,u8 *src_uv,u32 src_stride,
    u8 int_en);
/*******************************************************************************
* Function Name  : hx330x_rotateWaitFrameDone
* Description    : rotate wating frame done
* Input          : none
* Output         : none
* Return         : true : success, false: timeout
*******************************************************************************/
bool hx330x_rotateWaitFrameDone(void);
/*******************************************************************************
* Function Name  : hx330x_rotateIRQHandler
* Description    : rotate isr
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_rotateIRQHandler(void);

/*******************************************************************************
* Function Name  : hx330x_videoGetYAddr
* Description    : set video layer current y buffer addr
* Input          :  
* Output         : None
* Return         : buffer addr
*******************************************************************************/
u32 hx330x_rotateGetSrcYAddr(void);
#endif
