/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskRecordPhotoWin.c"


#define SHORT_PRESS_COUNT_THRESHOLD  2
#define LONG_PRESS_COUNT_THRESHOLD   5
static int lcd_rotate_flag ;  // lcd画面旋转标志位
static int key_press_count = 0;  // 按键按下计数器
static int key_is_pressed = 0;   // 按键是否按下标志位
static int short_press_executed = 0; // 短按操作是否已经执行的标志位
static int long_press_triggered = 0; // 长按操作是否已经触发的标志位
static int lcd_zoom_level = 0; // 记录当前 LCD 画面放大档位，共 3 档，范围 0 - 2


static int recordPhotoKeyMsgled(winHandle handle,u32 parameNum,u32* parame)
{
    KEY_STATE_T keyState = KEY_STATE_INVALID;
    int ret;
    if (parameNum == 1)
        keyState = (KEY_STATE_T)parame[0];

    if (keyState == KEY_PRESSED)
    {
        if (!key_is_pressed)
        {
            key_is_pressed = 1;
            key_press_count = 0;
            short_press_executed = 0; // 重置短按执行标志位
            long_press_triggered = 0; // 重置长按触发标志位
        }
        else
        {
            key_press_count++;
        }
    }
	 else if (keyState == KEY_RELEASE)
    {
        if (key_is_pressed)
        {
            key_is_pressed = 0;
            if (key_press_count < SHORT_PRESS_COUNT_THRESHOLD)
            {

                // #if FUN_VIDEO_PREVIEW_PAUSE_EN
                //     if (hal_lcd_pause_sta_get())
                //     {
                //         hal_lcd_pause_set(0);
                //         return 0;
                //     }
                // #endif

                // task_com_reshowUI_and_RotateRestore();

						// if (SysCtrl.enter_zoominout_mode)
						// {
                    // #if (1 == LCDSHOW_SCALE_EN)
                    //     if (SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
                    //     {
                    //         if (SysCtrl.lcd_scaler_level == 100/LCDSHOW_SCALER_MIN)
                    //         {
                    //             // SysCtrl.enter_zoominout_mode = 0;
                    //             app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
                    //         }
                    //         else
                    //             app_lcdVideoShowScaler_cfg(1, VIDEO_SCALER_CENTER);
                    //         recordPhotoScalerShow(handle);
                    //     }
                    // #endif
               				 // }

                    if (husb_api_usensor_tran_sta())
                    {
                        task_com_LedPwm_ctrl(1);
                        recordPhotoLedShow(handle);
                        short_press_executed = 1; // 标记短按操作已执行
                    }

            }
            key_press_count = 0;
            short_press_executed = 0; // 重置短按执行标志位
            long_press_triggered = 0; // 重置长按触发标志位
        }
    }
    else if (keyState == KEY_CONTINUE)
    {
        if (key_is_pressed)
        {
            key_press_count++;
            if (key_press_count >= LONG_PRESS_COUNT_THRESHOLD && !long_press_triggered)
            {
                // task_com_reshowUI_and_RotateRestore();


		if(SysCtrl.mirro_status)
		{
			SysCtrl.mirro_status = 0;
			// app_lcdVideoShowMirro_cfg(1);
			//recordPhotoMirroShow(handle);
			hal_lcdUiEnable(UI_LAYER0,1);
			return 0;
		}
	SysCtrl.rotate_status^=1;
	if(SysCtrl.rotate_status)
	{
		//hal_lcdUiEnable(UI_LAYER0,0);
		app_lcdVideoShowRotate_cfg(1);
		// lcd_rotate_flag = 1;
		// deg_Printf("in 180++++++++++=%d\n",lcd_rotate_flag);
	}else
	{
		app_lcdVideoShowRotate_cfg(1);
		//hal_lcdUiEnable(UI_LAYER0,1);
		// lcd_rotate_flag = 0;
		// deg_Printf("tui 180----------=%d\n",lcd_rotate_flag);

	}
	 recordPhotoRotateShow(handle);
	//  is_screen_flipped = SysCtrl.rotate_status;

	deg_Printf("Switch_Photo_lcd_rotate: %d\n", SysCtrl.rotate_status);


                key_press_count = 0; // 处理完长按后重置计数器
                long_press_triggered = 1; // 标记长按操作已触发
            }
        }
    }

    recordPhotoOp.upkeystate = keyState;
    return 0;
}





// static int recordPhotoKeyMsgled(winHandle handle,u32 parameNum,u32* parame)
// {
// 	u32 keyState = KEY_STATE_INVALID;
// 	if(parameNum == 1)
// 		keyState = parame[0];
// 	if(keyState == KEY_PRESSED)
// 	{
// 		deg_Printf("husb_api_usensor_tran_sta():%x\n", husb_api_usensor_tran_sta());
// 		if(husb_api_usensor_tran_sta())
// 		{
// 			task_com_LedPwm_ctrl(1);
// 			recordPhotoLedShow(handle);
// 		}

// 	}
// 	return 0;
// }

static int recordPhotoKeyMsgrotate(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_lcdVideoShowRotate_cfg(1);
	}
	return 0;
}


static int recordPhotoKeyMsgphoto(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	int ret;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_sound_wait_end();

		if(husb_api_usensor_tran_sta())
		{
//			dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 0);
			// if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)
			// {
			// 	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SysCtrl.dev_stat_sdc));
			// 	return 0;
			// }
			task_com_tips_show(TIPS_TAKEPHOTO_SUCCESS);
			ret = taskRecordPhotoProcess();
			app_draw_Service(1);

//			dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 1);

			if((ret>=0)/*&&SysCtrl.dev_stat_keysound*/)
			{
				ret = res_music_start(R_ID_MUSIC_TAKE_PHOTO,1,FUN_KEYSOUND_VOLUME);
			}

		}
	}
	return 0;
}


static int recordPhotoKeyMsgvideo(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{

		if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)
		{
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SysCtrl.dev_stat_sdc));
			return 0;
		}
		app_task_rec_Change();
//		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
//		{
//			husb_api_usensor_switch_res_kick(1, 0);
//			task_com_tips_show(TIPS_COM_WAITING_2S);
//		}

	}
	return 0;
}

/*******************************************************************************
* Function Name  : recordPhotoKeyMsgOk
* Description    : recordPhotoKeyMsgOk
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	int ret;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
//		task_com_sound_wait_end();
//
//		if(husb_api_usensor_tran_sta())
//		{
////			dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 0);
//			if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)
//			{
//				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SysCtrl.dev_stat_sdc));
//				return 0;
//			}
//			task_com_tips_show(TIPS_TAKEPHOTO_SUCCESS);
//			ret = taskRecordPhotoProcess();
//			app_draw_Service(1);
//
////			dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 1);
//
//			if((ret>=0)/*&&SysCtrl.dev_stat_keysound*/)
//			{
//				ret = res_music_start(R_ID_MUSIC_TAKE_PHOTO,1,FUN_KEYSOUND_VOLUME);
//			}
//
//		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgUp
* Description    : recordPhotoKeyMsgUp
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_lcdVideoShowRotate_cfg(1);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgDown
* Description    : recordPhotoKeyMsgDown
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
	#if (1 == LCDSHOW_SCALE_EN)
		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
		{
			if(SysCtrl.lcd_scaler_level == 100/LCDSHOW_SCALER_MIN)
				app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
			else
				app_lcdVideoShowScaler_cfg(1, VIDEO_SCALER_CENTER);
			recordPhotoScalerShow(handle);
		}
	#endif
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgMenu
* Description    : recordPhotoKeyMsgMenu
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiOpenWindow(&menuItemWindow,0,1,&MENU(record));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoKeyMsgMode
* Description    : recordPhotoKeyMsgMode
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_taskChange();
	}
	return 0;
}


static int recordplayviodeMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	deg_Printf("recordplayviodeMode\n");
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_taskStart(TASK_PLAY_VIDEO,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgUvcForm
* Description    : videoKeyMsgUvcForm
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgUvcForm(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		// if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL)
		// {
		// 	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SysCtrl.dev_stat_sdc));
		// 	return 0;
		// }
		// // app_task_rec_Change();
		// if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
		// {
		// 	husb_api_usensor_switch_res_kick(1, 0);
		// 	task_com_tips_show(TIPS_COM_WAITING_2S);
		// }
		deg_Printf("recordPhotoKeyMsgUvcForm SysCtrl.sensor_change_en=%d\n",SysCtrl.sensor_change_en);
		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW&&SysCtrl.sensor_change_en)
		{
			husb_api_usensor_switch_res_kick(0, 1);
			task_com_tips_show(TIPS_COM_WAITING_2S);
		}


	}
	return 0;
}


static int recordPhotoKeyMsgUvcFrame(winHandle handle,u32 parameNum,u32* parame)//change res
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
#if FUN_VIDEO_PREVIEW_PAUSE_EN
			if(hal_lcd_pause_sta_get())
			{
				hal_lcd_pause_set(0);
				return 0;
			}
#endif

		deg_Printf("SysCtrl.sensor_change_en=%d\n",SysCtrl.sensor_change_en);
		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW&&SysCtrl.sensor_change_en)
		{
			// task_com_reshowUI_and_RotateRestore();
			husb_api_usensor_switch_res_kick(0, 1);
			task_com_tips_show(TIPS_COM_WAITING_2S);
		}else
		{

			// if(SysCtrl.mirro_status)
			// {
			// 	//hal_lcdUiEnable(UI_LAYER0,0);
			// 	app_lcdVideoShowMirro_cfg(1);
			// }else
			// {
			// 	app_lcdVideoShowMirro_cfg(1);
			// 	//hal_lcdUiEnable(UI_LAYER0,1);

			// }
			// recordPhotoMirroShow(handle);

		}

	}
	return 0;
}//原SDK

// static int recordPhotoKeyMsgUvcFrame(winHandle handle,u32 parameNum,u32* parame)//change res
// {
// 	u32 keyState = KEY_STATE_INVALID;
// 	if(parameNum == 1)
// 		keyState = parame[0];
// 	if(keyState == KEY_PRESSED)
// 	{

// #if FUN_VIDEO_PREVIEW_PAUSE_EN
// 			if(hal_lcd_pause_sta_get())
// 			{
// 				hal_lcd_pause_set(0);
// 				return 0;
// 			}
// #endif

// 		deg_Printf("SysCtrl.sensor_change_en=%d\n",SysCtrl.sensor_change_en);
// 		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW&&SysCtrl.sensor_change_en)
// 		{
// 			task_com_reshowUI_and_RotateRestore();
// 			husb_api_usensor_switch_res_kick(0, 1);
// 			task_com_tips_show(TIPS_COM_WAITING_2S);
// 		}else
// 		{
// 			//return 0;
// 			// if(SysCtrl.rotate_status)
// 			// {
// 			// 	SysCtrl.rotate_status = 0 ;
// 			// 	app_lcdVideoShowRotate_cfg(1);
// 			// 	recordPhotoRotateShow(handle);
// 			// 	//hal_lcdUiEnable(UI_LAYER0,1);
// 			// 	//return 0;
// 			// }
// 			// SysCtrl.mirro_status^=1;
// 			if(SysCtrl.mirro_status)
// 			{
// 				//hal_lcdUiEnable(UI_LAYER0,0);
// 				app_lcdVideoShowMirro_cfg(1);
// 				deg_Printf("SysCtrl.sensor_change_11111111111111111111111111111111111\n");
// 			}else
// 			{
// 				app_lcdVideoShowMirro_cfg(1);
// 				//hal_lcdUiEnable(UI_LAYER0,1);
// 				//deg_Printf("SysCtrl.sensor_change_222222222222222222222222222222222222222\n");

// 			}
// 			recordPhotoMirroShow(handle);


// 		}

// 	}
// 	return 0;
// }

/*******************************************************************************
* Function Name  : videoKeyMsgUvcForm
* Description    : videoKeyMsgUvcForm
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
// int lcd_color_flag = 0;
// static int recordPhotoKeyMsgUvcFrame(winHandle handle,u32 parameNum,u32* parame)
// {
// 	u32 keyState = KEY_STATE_INVALID;
// 	if(parameNum == 1)
// 		keyState = parame[0];
// 	if(keyState == KEY_PRESSED)
// 	{
// #if FUN_VIDEO_NOCOLOR_EN
// 		if (lcd_color_flag == 0) {
// 			lcd_color_flag = 1;
// 			dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_NOCOLOR_CHANGE, 0);
// 		} else {
// 			lcd_color_flag = 0;
// 			dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_NOCOLOR_CHANGE, 1);
// 		}
// #endif

// 	}
// 	return 0;
// }





/*******************************************************************************
* Function Name  : videoKeyMsgRotateAdd
* Description    : videoKeyMsgRotateAdd
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgRotateAdd(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_lcdVideoShowRotate_cfg(1);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgRotateDec
* Description    : videoKeyMsgRotateDec
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoKeyMsgRotateDec(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_lcdVideoShowRotate_cfg(-1);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgSD
* Description    : recordPhotoSysMsgSD
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	recordPhotoSDShow(handle);
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 0);
	taskRecordPhotoRemainCal();
	if(SysCtrl.spi_jpg_list < 0)
	{
		if(recordPhotoOp.capture_kick == 0)
		{
				task_com_tips_show(TIPS_TYPE_SD);
			return 0;
		}
	}

	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgUSB
* Description    : recordPhotoSysMsgUSB
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgUSB(winHandle handle,uint32 parameNum,uint32* parame)
{
	recordPhotoBatteryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgUSB
* Description    : recordPhotoSysMsgUSB
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgUSBHOST(winHandle handle,uint32 parameNum,uint32* parame)
{
	// PhotoSdShow(handle);
	recordPhotoSensorResShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgBattery
* Description    : recordPhotoSysMsgBattery
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsgBattery(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		recordPhotoBatteryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSysMsgSec
* Description    : recordPhotoSysMsgSec
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSysMsg1s(winHandle handle,uint32 parameNum,uint32* parame)
{
	recordPhotoSysTimeShow(handle);
	recordPhotoPowerOnTimeShow(handle);
	// uiWinSetResid(winItem(handle,PHOTO_CHACK_SD_ID),RAM_ID_MAKE(task_com_sdcCap_str()));

	recordPhotoIrLEDShow(handle);
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	{
		recordPhotoBatteryShow(handle);
//		if(uiWinIsVisible(winItem(handle,PHOTO_BATERRY_ID)))
//			uiWinSetVisible(winItem(handle,PHOTO_BATERRY_ID),0);
//		else
//		{
//		    uiWinSetVisible(winItem(handle,PHOTO_BATERRY_ID),1);
//			uiWinSetResid(winItem(handle,PHOTO_BATERRY_ID),R_ID_ICON_MTBATTERY5);
//
//		}
	}
	if(SysCtrl.lcd_scaler_level == 0){
		SysCtrl.scaler_0_hide_str_count++;
		if(SysCtrl.scaler_0_hide_str_count>3)
			recordPhotoScalerHide(handle);
	}else
	{
		SysCtrl.scaler_0_hide_str_count = 0;
		recordPhotoScalerShow(handle);
	}
	// recordPhotoTemperaueShow(handle);
	return 0;


	//SBATTERY_STAT_0
}
/*******************************************************************************
* Function Name  : recordPhotoOpenWin
* Description    : recordPhotoOpenWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoOpenWin(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("[WIN]recordPhotoOpenWin\n");

	uiWinSetResid(winItem(handle,PHOTO_MODE_ID),R_ID_ICON_MTPHOTO);
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 0);
	taskRecordPhotoRemainCal();
	// recordPhotoSysTimeShow(handle);
	// recordPhotoPowerOnTimeShow(handle);
	// recordPhotoResShow(handle);
	// recordPhotoSensorResShow(handle);
	// recordPhotoIrLEDShow(handle);
	 recordPhotoSDShow(handle);
	//  PhotoSdShow(handle);
	// recordPhotoMicShow(handle);
	recordPhotoBatteryShow(handle);

	recordPhotoLedShow(handle);
	// recordPhotoTemperaueShow(handle);
	task_com_LedOnOff_ctrl(1);
	recordPhotoRotateShow(handle);
	uiWinSetVisible(winItem(handle,PHOTO_FOCUS_ID),0);
#if (ENCRYPT_FUNC_SWITCH == 1)
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL&&!SysCtrl.flash_md5_check_value){//wxn-?????flash???
		task_com_tips_show(TIPS_ERROR);
		XOSTimeDly(500);
		app_taskStart(TASK_POWER_OFF,0);
	}
#endif
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoCloseWin
* Description    : recordPhotoCloseWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoCloseWin(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("[WIN]recordPhotoCloseWin\n");
	return 0;
}

static int recordPhotoSysMsg100ms(winHandle handle,uint32 parameNum,uint32* parame)
{
	u8 uvcunitsel,request;
	//u16 val;
	static u32 pre_val,val,i;
	if(husb_api_usensor_atech_sta())
	{
		pre_val = val;
		val = husb_api_usensor_uvcunit_get(PU_SATURATION_CONTROL, GET_CUR);

		//deg_Printf("val[%d],pre_val[%d]\n",val,pre_val);
		// if(pre_val == val)
		// {
		// 	uiWinSetVisible(winItem(handle,PHOTO_FOCUS_ID),0);
		// 	i = 0;
		// }else
		// {
		// 	i++;
		// 	if(i>2)
		// 	uiWinSetVisible(winItem(handle,PHOTO_FOCUS_ID),1);
		// }
	}

	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoTouchWin
* Description    : recordPhotoTouchWin
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoTouchWin(winHandle handle,uint32 parameNum,uint32* parame)
{
/*
parame[0]: widget id;
parame[1]: selected item id(for createItemManage widget)
parame[2]: touch state
*/
	if(parameNum!=3)
	{
		deg_Printf("photoTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{

	}
	return 0;
}
/*******************************************************************************
* Function Name  : recordPhotoSlideOff
* Description    : recordPhotoSlideOff
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static int recordPhotoSlideOff(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(parameNum!=1)
	{
		deg_Printf("photoSlidRelease, parame num error %d\n",parameNum);
		return 0;
	}
	if(parame[0] == TP_DIR_DOWN)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MENU,KEY_PRESSED));
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	else if(parame[0] == TP_DIR_LEFT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	return 0;
}

ALIGNED(4) msgDealInfor photoEncodeMsgDeal[] =
{
	{SYS_OPEN_WINDOW,	recordPhotoOpenWin},
	{SYS_CLOSE_WINDOW,	recordPhotoCloseWin},
	{SYS_CHILE_COLSE,	recordPhotoOpenWin},
	{SYS_TOUCH_WINDOW,  recordPhotoTouchWin},
	{SYS_TOUCH_SLIDE_OFF,recordPhotoSlideOff},
	{KEY_EVENT_OK,		recordPhotoKeyMsgOk},
	{KEY_EVENT_UP,		recordPhotoKeyMsgDown},//lcd_up
	{KEY_EVENT_DOWN,	recordPhotoKeyMsgled},
	{KEY_EVENT_MENU,	recordPhotoKeyMsgMenu},
	{KEY_EVENT_MODE,	recordPhotoKeyMsgMode},
	{KEY_EVENT_PLAYVIDEO,recordplayviodeMode},
	{KEY_EVENT_UVC_FORM,recordPhotoKeyMsgUvcForm},//recordPhotoKeyMsgUvcForm //recordPhotoKeyMsgrotate
	{KEY_EVENT_UVC_FRAME,recordPhotoKeyMsgUvcFrame},
	{KEY_EVENT_ROTATE_ADD,recordPhotoKeyMsgRotateAdd},
	{KEY_EVENT_ROTATE_DEC,recordPhotoKeyMsgRotateDec},
	{KEY_EVENT_LED,		recordPhotoKeyMsgled},
	{KEY_EVENT_POWER,		recordPhotoKeyMsgMenu},
	// {KEY_EVENT_ROTATE,		recordPhotoKeyMsgrotate},//翻转180°
	{KEY_EVENT_PHOTO,		recordPhotoKeyMsgphoto},
	{KEY_EVENT_VIDEO,		recordPhotoKeyMsgvideo},
	{SYS_EVENT_SDC,		recordPhotoSysMsgSD},
	{SYS_EVENT_USBDEV,	recordPhotoSysMsgUSB},
	{SYS_EVENT_USBHOST,	recordPhotoSysMsgUSBHOST},
	{SYS_EVENT_BAT,		recordPhotoSysMsgBattery},
	{SYS_EVENT_1S,		recordPhotoSysMsg1s},
	{SYS_EVENT_100MS,		recordPhotoSysMsg100ms},
	{EVENT_MAX,			NULL},
};
WINDOW(recordPhotoWindow,photoEncodeMsgDeal,recordPhotoWin)




