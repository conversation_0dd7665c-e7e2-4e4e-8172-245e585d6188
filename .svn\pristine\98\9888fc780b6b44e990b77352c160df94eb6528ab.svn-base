/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"


/*******************************************************************************
* Function Name  : res_ascii_get
* Description    : ascii table get
* Input          : char c : char value to draw
* 				   unsigned char *width: font width
*                  unsigned char *heigth:font height
*                  unsigned char font: font num
* Output         : None
* Return         : None
*******************************************************************************/
const u8 *res_ascii_get(u8 c,u16 *width,u16 *heigth,u8 font)
{
	const u8 *table;
	u8 index;
	if(c<32 || c == 34 || c>126)
		return NULL;
	if(c<34)
		index = c-32;
	else
		index = c-33;
	font&=0x0f;

	switch(font)
	{
		//case RES_FONT_NUM1: 
		case RES_FONT_NUM2:	table = ascii_num2_table[index]; break;
		case RES_FONT_NUM3:	table = ascii_num3_table[index]; break;
		case RES_FONT_NUM4:	table = ascii_num4_table[index]; break;
		default:			table = ascii_num1_table[index]; break;
	}
	if(width)
		*width = table[0];
	if(heigth)
		*heigth = table[1];
		
	return &table[2];    
}
/*******************************************************************************
* Function Name  : res_getAsciiCharSize
* Description    : get ascii char size        
* Input          : char c : char value
				   unsigned short *width : width
				   unsigned short *heigth:height
* Output        : 
* Return         : int -1 : fail
                            0 :success  
*******************************************************************************/
int res_getAsciiCharSize(u8 c,u16 *width,u16 *heigth,u8 font)
{
	if(res_ascii_get(c,width,heigth,font) == NULL)
		return -1;
	return 0;
}
/*******************************************************************************
* Function Name  : res_getAsciiStringSize
* Description    : get ascii string size        
* Input          : char c : string
				   u16 *width : width
				   u16 *heigth:height
* Output        : 
* Return         : int -1 : fail
                            0 :success  
*******************************************************************************/
int res_getAsciiStringSize(u8 *str,u16 *width,u16 *heigth,u8 font)
{
    u16 w,h;
    u16 tw,th;
	u32 num = 0;
	tw = 0;
	th = 0;

	while(*str)
	{
		if(res_getAsciiCharSize(*str++,&w,&h,font)>=0)
		{
			tw += w;
			if(h > th)
				th = h;
		}
		num++;
	}

	 if(width)
		 *width = tw;
	 if(heigth)
		 *heigth = th;
	return num;
}