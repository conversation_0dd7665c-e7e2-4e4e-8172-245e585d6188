/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_IMAGEICON_H
#define UI_WIN_IMAGEICON_H

typedef struct
{
	uiWidgetObj widget;
	ICON_DRAW_T image;
	ICON_DRAW_T imageSelect;
	u32			select;
}uiImageIconObj;
/*******************************************************************************
* Function Name  : imageIconCreate
* Description    : imageIconCreate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiImageIconCreateDirect(s16 x0,s16 y0,u16 width,u16 height,winHandle parent,u16 style,u16 id);
/*******************************************************************************
* Function Name  : imageIconCreate
* Description    : imageIconCreate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiImageIconCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);
#endif
