/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if SENSOR_DVP_VGA_NT99142 > 0


SENSOR_INIT_SECTION const u8 NT99142InitTable[]=
{
	
	//----NT99142--25_30FPS_20150608------
	//=======Ini Setting=====      
	0x30, 0x69, 0x01,   // Pad_Config_Pix_Out
	0x30, 0x6A, 0x02,   // Pclk_Odrv
	0x30, 0x12, 0x02,   
	0x30, 0x13, 0x00,
	0x30, 0xA0, 0x03,   // FPN
	0x30, 0xA1, 0x23,   // FPN_Ctrl_1
	0x30, 0xA2, 0x70,   // FPN_Corr_Again
	0x30, 0xA3, 0x01,   // FPN_DPC_Diff
	0x30, 0x3E, 0x04,   // Clamp_En
	0x30, 0x3F, 0x32,
	0x30, 0x51, 0x3A,   // ABLC_Ofs_Wgt
	0x30, 0x52, 0x0F,   // OB_Mul
	0x30, 0x55, 0x00,
	0x30, 0x56, 0x18,   // ABLC_Ofs_Cst
	0x30, 0x5F, 0x33,
	0x30, 0x8B, 0x2F,   // HG_BLC_Thr_U
	0x30, 0x8C, 0x28,   // HG_BLC_Thr_L
	0x30, 0x8D, 0x30,   // HG_ABLC_Ofs_Cst
	0x30, 0x8E, 0x3A,   // HG_ABLC_Ofs_Wgt
	0x30, 0x8F, 0x0D,   // HG_OB_Mul
	0x31, 0x00, 0x03,   // Analog Setting
	0x31, 0x01, 0x00,
	0x31, 0x02, 0x0A,
	0x31, 0x03, 0x00,
	0x31, 0x05, 0x03,
	0x31, 0x06, 0x04,
	0x31, 0x07, 0x10,
	0x31, 0x08, 0x00,
	0x31, 0x09, 0x00,
	0x30, 0x7D, 0x00,
	0x31, 0x0A, 0x05,
	0x31, 0x0C, 0x00,
	0x31, 0x0D, 0x80,
	0x31, 0x10, 0x33,
	0x31, 0x11, 0x59,
	0x31, 0x12, 0x44,
	0x31, 0x13, 0x66,
	0x31, 0x14, 0x66,
	0x31, 0x1D, 0x40,
	0x31, 0x27, 0x01,
	0x31, 0x29, 0x44,
	0x31, 0x36, 0x59,
	0x31, 0x3F, 0x02,
	0x32, 0x4F, 0x01,   // 0: Load default LSC parameter
	0x32, 0x10, 0x16,   // Gain0 of R 
	0x32, 0x11, 0x16,   // Gain1 of R 
	0x32, 0x12, 0x16,   // Gain2 of R 
	0x32, 0x13, 0x16,   // Gain3 of R 
	0x32, 0x30, 0x00,   // Inner R gain (signed  -16 to 15) 
	0x32, 0x33, 0x00,   // Inner B gain (signed  -16 to 15) 
	0x32, 0x34, 0x00,   // Outer R gain (signed  -16 to 15) 
	0x32, 0x37, 0x00,   // Outer B gain (signed  -16 to 15) 
	0x32, 0x4F, 0x00,   // D light LSC
	0x32, 0x10, 0x10,
	0x32, 0x11, 0x10,
	0x32, 0x12, 0x10,
	0x32, 0x13, 0x10,
	0x32, 0x14, 0x0C,
	0x32, 0x15, 0x0C,
	0x32, 0x16, 0x0C,
	0x32, 0x17, 0x0C,
	0x32, 0x18, 0x0C,
	0x32, 0x19, 0x0C,
	0x32, 0x1A, 0x0C,
	0x32, 0x1B, 0x0C,
	0x32, 0x1C, 0x0B,
	0x32, 0x1D, 0x0B,
	0x32, 0x1E, 0x0B,
	0x32, 0x1F, 0x0B,
	0x32, 0x30, 0x03,
	0x32, 0x31, 0x00,
	0x32, 0x32, 0x00,
	0x32, 0x33, 0x03,
	0x32, 0x34, 0x00,
	0x32, 0x35, 0x00,
	0x32, 0x36, 0x00,
	0x32, 0x37, 0x00,
	0x32, 0x38, 0x18,
	0x32, 0x39, 0x18,
	0x32, 0x3A, 0x18,
	0x32, 0x41, 0x48,
	0x32, 0x43, 0xC3,
	0x32, 0x44, 0x00,
	0x32, 0x45, 0x00,
	0x32, 0x4F, 0x00,
	0x32, 0x50, 0x28,   // CA_RG_Top_D
	0x32, 0x51, 0x20,   // CA_RG_Bot_D
	0x32, 0x52, 0x2F,   // CA_BG_Top_D
	0x32, 0x53, 0x20,   // CA_BG_Bot_D
	0x32, 0x54, 0x33,   // CA_RG_Top_F     
	0x32, 0x55, 0x26,   // CA_RG_Bot_F    
	0x32, 0x56, 0x25,   // CA_BG_Top_F    
	0x32, 0x57, 0x19,   // CA_BG_Bot_F    
	0x32, 0x58, 0x44,   // CA_RG_Top_A    
	0x32, 0x59, 0x35,   // CA_RG_Bot_A    
	0x32, 0x5A, 0x24,   // CA_BG_Top_A    
	0x32, 0x5B, 0x16,   // CA_BG_Bot_A    
	0x32, 0x5C, 0xA0,
	0x32, 0x62, 0x00,
	0x32, 0x68, 0x01,   // CA_Outdoor_Ctrl
	/*0x32, 0x70, 0x00,   // Gamma_Tab_0  // Gamma3  
	0x32, 0x71, 0x0B,   // Gamma_Tab_1 
	0x32, 0x72, 0x16,   // Gamma_Tab_2
	0x32, 0x73, 0x2B,   // Gamma_Tab_3
	0x32, 0x74, 0x3F,   // Gamma_Tab_4
	0x32, 0x75, 0x51,   // Gamma_Tab_5
	0x32, 0x76, 0x72,   // Gamma_Tab_6
	0x32, 0x77, 0x8F,   // Gamma_Tab_7
	0x32, 0x78, 0xA7,   // Gamma_Tab_8
	0x32, 0x79, 0xBC,   // Gamma_Tab_9
	0x32, 0x7A, 0xDC,   // Gamma_Tab_10
	0x32, 0x7B, 0xF0,   // Gamma_Tab_11
	0x32, 0x7C, 0xFA,   // Gamma_Tab_12
	0x32, 0x7D, 0xFE,   // Gamma_Tab_13
	0x32, 0x7E, 0xFF,   // Gamma_Tab_14
	*/
	0x32, 0x70, 0x00,   // Gamma_Tab_0  // Gamma3  
	0x32, 0x71, 0x0C,   // Gamma_Tab_1 
	0x32, 0x72, 0x18,   // Gamma_Tab_2
	0x32, 0x73, 0x32,   // Gamma_Tab_3
	0x32, 0x74, 0x44,   // Gamma_Tab_4
	0x32, 0x75, 0x54,   // Gamma_Tab_5
	0x32, 0x76, 0x70,   // Gamma_Tab_6
	0x32, 0x77, 0x88,   // Gamma_Tab_7
	0x32, 0x78, 0x9D,   // Gamma_Tab_8
	0x32, 0x79, 0xB0,   // Gamma_Tab_9
	0x32, 0x7A, 0xCF,   // Gamma_Tab_10
	0x32, 0x7B, 0xE2,   // Gamma_Tab_11
	0x32, 0x7C, 0xEF,   // Gamma_Tab_12
	0x32, 0x7D, 0xF7,   // Gamma_Tab_13
	0x32, 0x7E, 0xFF,   // Gamma_Tab_14
	
	0x32, 0x90, 0x77,
	0x32, 0x92, 0x73,
	0x32, 0x97, 0x03,   // AWB_Speed
	0x32, 0x9E, 0x01,   // AWB_Ctrl_1
	0x32, 0x9F, 0x00,   // AWB_Ctrl_2
	0x32, 0xB0, 0x46,   // LA_Win_Ctrl_0
	0x32, 0xB1, 0xBB,   // LA_Win_Ctrl_1
	0x32, 0xB2, 0x14,
	0x32, 0xB3, 0x80,
	0x32, 0xB4, 0x20,
	0x32, 0xB8, 0x06,
	0x32, 0xB9, 0x06,
	0x32, 0xBC, 0x36,
	0x32, 0xBD, 0x04,
	0x32, 0xBE, 0x04,
	0x32, 0xCB, 0x20,  //0x30 
	0x32, 0xCC, 0x70,
	0x32, 0xCD, 0xA0,
	0x32, 0xF1, 0x05,
	0x32, 0xF2, 0x80,   // Y_Component
	0x32, 0xFC, 0x00,   // Brt_Ofs
	0x33, 0x02, 0x00,   // Matrix_RR_H
	0x33, 0x03, 0x4C,   // Matrix_RR_L
	0x33, 0x04, 0x00,   // Matrix_RG_H
	0x33, 0x05, 0x96,   // Matrix_RG_L
	0x33, 0x06, 0x00,   // Matrix_RB_H  
	0x33, 0x07, 0x1D,   // Matrix_RB_L  
	0x33, 0x08, 0x07,   // Matrix_GR_H  
	0x33, 0x09, 0xCE,   // Matrix_GR_L  
	0x33, 0x0A, 0x06,   // Matrix_GG_H
	0x33, 0x0B, 0xEC,   // Matrix_GG_L
	0x33, 0x0C, 0x01,   // Matrix_GB_H   
	0x33, 0x0D, 0x46,   // Matrix_GB_L   
	0x33, 0x0E, 0x00,   // Matrix_BR_H
	0x33, 0x0F, 0xD2,   // Matrix_BR_L
	0x33, 0x10, 0x07,   // Matrix_BG_H
	0x33, 0x11, 0x35,   // Matrix_BG_L
	0x33, 0x12, 0x07,   // Matrix_BB_H
	0x33, 0x13, 0xFA,   // Matrix_BB_L
	0x33, 0x26, 0x0C,   // Eext_Mul
	0x33, 0x27, 0x05,   // Eext_Sel
	0x33, 0x32, 0x80,   // Emap_B
	0x33, 0x60, 0x10,
	0x33, 0x61, 0x20,
	0x33, 0x62, 0x28,   
	0x33, 0x63, 0x31,  
	0x33, 0x64, 0x0B,
	0x33, 0x65, 0x06, //0x02  // EmapA_0
	0x33, 0x66, 0x0A, //0x06 // EmapA_1
	0x33, 0x67, 0x10, //0x0A  // EmapA_2
	0x33, 0x68, 0x1C,//0x24   // Edge_Enhance_0
	0x33, 0x69, 0x14,//0x1c   // Edge_Enhance_1
	0x33, 0x6B, 0x0A,//0x12   // Edge_Enhance_2
	0x33, 0x6D, 0x20,   // NR_DPC_Ratio_0
	0x33, 0x6E, 0x16,   // NR_DPC_Ratio_1
	0x33, 0x70, 0x09,   // NR_DPC_Ratio_2
	0x33, 0x71, 0x30, //0x20   // NR_Wgt_0
	0x33, 0x72, 0x38, //0x27   // NR_Wgt_1
	0x33, 0x74, 0x3F, //0x2E  // NR_Wgt_2
	0x33, 0x75, 0x10,   // Edark_Gain_0
	0x33, 0x76, 0x0E,   // Edark_Gain_1
	0x33, 0x78, 0x0C,   // Edark_Gain_2
	0x33, 0x79, 0x04,   // NR_Comp_Max_0
	0x33, 0x7A, 0x08,   // NR_Comp_Max_1
	0x33, 0x7C, 0x0C,   // NR_Comp_Max_2
	0x33, 0xA0, 0x70,   // AS_EC_Thr_Bri
	0x33, 0xA1, 0x80,   // AS_EC_Thr_Dark
	0x33, 0xA2, 0x20,   
	0x33, 0xA3, 0x30,   
	0x33, 0xA4, 0x00,  
	0x33, 0xA5, 0x80,  
	0x33, 0xA7, 0x00,   // Y_Offset
	0x33, 0xA8, 0x00,
	0x33, 0xA9, 0x00,   // NR_Post_Thr_0
	0x33, 0xAA, 0x02,   // NR_Post_Thr_1
	0x33, 0xAC, 0x04,   // NR_Post_Thr_2
	0x33, 0xAD, 0x00,   // NR_Post_EThr_0
	0x33, 0xAE, 0x02,   // NR_Post_EThr_1
	0x33, 0xB0, 0x04,   // NR_Post_EThr_2
	0x33, 0xB1, 0x00,   // ChromaMap_0
	0x33, 0xB4, 0x48,   // ChromaMap_4
	0x33, 0xB5, 0x44,   // ChromaMap_Y
	0x33, 0xB6, 0xA0,
	0x33, 0xB9, 0x03,
	0x33, 0xBD, 0x00,
	0x33, 0xBE, 0x08,
	0x33, 0xBF, 0x10,
	0x33, 0xC0, 0x01,
	0x37, 0x00, 0x08,   // Gamma_Tab_D_0
	0x37, 0x01, 0x12,   // Gamma_Tab_D_1
	0x37, 0x02, 0x1D,   // Gamma_Tab_D_2
	0x37, 0x03, 0x35,   // Gamma_Tab_D_3
	0x37, 0x04, 0x45,   // Gamma_Tab_D_4
	0x37, 0x05, 0x54,   // Gamma_Tab_D_5
	0x37, 0x06, 0x6D,   // Gamma_Tab_D_6
	0x37, 0x07, 0x83,   // Gamma_Tab_D_7
	0x37, 0x08, 0x96,   // Gamma_Tab_D_8
	0x37, 0x09, 0xA8,   // Gamma_Tab_D_9
	0x37, 0x0A, 0xC4,   // Gamma_Tab_D_10
	0x37, 0x0B, 0xD5,   // Gamma_Tab_D_11
	0x37, 0x0C, 0xE1,   // Gamma_Tab_D_12
	0x37, 0x0D, 0xE8,   // Gamma_Tab_D_13
	0x37, 0x0E, 0xF0,   // Gamma_Tab_D_14
	0x37, 0x10, 0x07,
	0x37, 0x1E, 0x02,
	0x37, 0x1F, 0x02,
	0x38, 0x00, 0x00,
	0x38, 0x13, 0x07,
	
	//======= 25~30fps 24mclk =======   
	//MCLK:      24.00MHz                 
	//PCLK:      72.00MHz                 
	//Size:      1280x720                 
	//FPS:       25.00~30.01                              
	0x32, 0xBB, 0x67,     //50Hz Anti-Flicker  //AE Start  
	//0x32, 0xBB, 0x77,   //60Hz Anti-Flicker  
	0x32, 0xBF, 0x60, 
	0x32, 0xC0, 0x60, 
	0x32, 0xC1, 0x60, 
	0x32, 0xC2, 0x60, 
	0x32, 0xC3, 0x00, 
	0x32, 0xC4, 0x2B, //0x2F
	0x32, 0xC5, 0x2B, //0x30
	0x32, 0xC6, 0x2B, //0x30
	0x32, 0xD3, 0x00, 
	0x32, 0xD4, 0xC3, 
	0x32, 0xD5, 0x7C, 
	0x32, 0xD6, 0x00, 
	0x32, 0xD7, 0xA3, 
	0x32, 0xD8, 0x77,  //AE End
	0x32, 0xF0, 0x01,  //Output Format
	0x32, 0x00, 0x7E,  //Mode Control
	0x32, 0x01, 0x3D,  //Mode Control
	0x30, 0x2A, 0x80,  //PLL Start
	0x30, 0x2C, 0x17, 
	0x30, 0x2D, 0x11,  //PLL End
	0x30, 0x22, 0x03,  //Timing Start
	0x30, 0x0A, 0x07, //0x06
	0x30, 0x0B, 0x32, 
	0x30, 0x0C, 0x02, 
	0x30, 0x0D, 0xF4,  //Timing End
	0x32, 0x0A, 0x00, 
	0x30, 0x21, 0x02, 
	0x30, 0x60, 0x01,     
	          
	SENSOR_TAB_END
};

//=====flag: 0 is rotate 0',  1 is rotate 180'  flip====
static void NT99142_rotate(u32 r)
{
	//	sensor_iic_enable();
	//	sensor_iic_info();

	if(0 == r)
	{
		u8 rot_buf[3];
		rot_buf[0] = 0x30;
		rot_buf[1] = 0x22;
		rot_buf[2] = 0x00;
		sensor_iic_write(rot_buf);
		//deg_Printf("sensor_rotate 0 \n");
	}
	else if(1 == r)
	{

		u8 rot_buf[3];
		rot_buf[0] = 0x30;
		rot_buf[1] = 0x22;
		rot_buf[2] = 0x03;
		sensor_iic_write(rot_buf);
		//deg_Printf("sensor_rotate 180 \n");
	}	
	//	 sensor_iic_diable();

}

SENSOR_OP_SECTION const Sensor_Adpt_T nt99142_adpt = 
{
	.typ 				= CSI_TYPE_YUV422| CSI_TYPE_DVP,// csi type: 10; 8	

#if  (CURRENT_CHIP == FPGA)
	.mclk 				= 24000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,  //mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#else
	.mclk 				= 24000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,	//mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#endif
	//sensor input -> sensor crop -> csi input
	.senPixelw          = 640, 			//sensor input width
	.senPixelh          = 480,				//sensor input height
	.senCropW_St        = 0,				//sensor crop width start
	.senCropW_Ed        = 640,				//sensor crop width end
	.senCropH_St        = 0,				//sensor crop height start
	.senCropH_Ed        = 480,				//sensor crop height end
	.senCropMode        = CSI_PASS_MODE,	//sensor crop mode: CSI_PASS_MODE, CSI_CROP_MODE , CSI_DIV2_MODE, CSI_CROP_DIV2_MODE

	.pixelw 			= 640,				//csi input width
	.pixelh				= 480,				//csi input height
	.hsyn 				= 1,				//1: hsync valid high, 0: hsync valid low
	.vsyn 				= 1,				//1: vsync valid high, 0: vsync valid low
	.colrarray 			= CSI_PRIORITY_Y0CBY1CR,//RAW: CSI_PRIORITY_RGGB, CSI_PRIORITY_GRBG, CSI_PRIORITY_BGGR, CSI_PRIORITY_GBRG
											//YUV422: CSI_PRIORITY_CBY0CRY1, CSI_PRIORITY_CRY0CBY1, CSI_PRIORITY_Y0CBY1CR, CSI_PRIORITY_Y0CRY1CB

	.sensorCore			= SYS_VOL_V1_5,		//VDDSENCORE: SYS_VOL_V1_2 ~ SYS_VOL_V3_3
	.sensorIo			= SYS_VOL_V3_1,		//VDDSENIO: SYS_VOL_V1_2 ~ SYS_VOL_V3_56		

	.mipi_adapt			= {
		.lanes			= 1,			//mipi lane num
		.raw_bit		= CSI_TYPE_RAW10,	//10/8: RAW10/RAW8
		.dphy_pll		= PLL_CLK/5,
		.csi_pclk		= PLL_CLK/8,
		.tclk_settle	= 17,			//TCLK_SETTLE_TIME  = tclk_settle*(1/dphy_pll)
		.tclk_miss		= 4,			//TCLK_MISS_TIME	= tclk_miss*(1/dphy_pll)
		.tclk_prepare	= 2,			//TCLK_PREPARE_TIME = tclk_prepare*(1/dphy_pll)
		.ths_settle		= 2,			//THS_SETTLE_TIME  	= ths_settle*(1/dphy_pll)
		.ths_skip		= 6,			//THS_SKIP_TIME		= ths_skip*(1/dphy_pll)
		.ths_dtermen	= 4,			//THS_DTERMEN_TIME 	= ths_dtermen*(1/dphy_pll)
		.hsa			= 10,				//HSA_TIME			= hsa*(1/csi_pclk)
		.hbp			= 20,				//HBP_TIME			= hbp*(1/csi_pclk)
		.hsd			= 200,				//HSD_TIME			= hsd*(1/csi_pclk)
		.hlines			= 30,
		.vsa_lines		= 3,
		.vbp_lines		= 5,
		.vfp_lines		= 7,
		.vactive_lines	= 0x50
	},
	.rotate_adapt 		= {0},

	.hvb_adapt = {
		.pclk			= 48000000,			//csi pclk input	
		.v_len			= 800,				//sensor v_len = height + vblank
		.step_val		= 0,				//auto cal
		.step_max		= 0,				//auto cal
		.down_fps_mode	= 0xff,				//0,1,hvb down_fps; 2: exp down_fps, 0xff: turn off down_fps
#if  (CURRENT_CHIP == FPGA)
		.fps			= 15,				//sensor fps set
#else
		.fps			= 25,				//sensor fps set
#endif
		.frequency		= 0					//0: 50hz, 1: 60hz
	},
	//_ISP_DIS_,_ISP_EN_,  _ISP_AUTO_
	.isp_all_mod =  (_ISP_DIS_  <<_BLC_POS_ | _ISP_DIS_  <<_LSC_POS_  | _ISP_DIS_<<_DDC_POS_   | _ISP_DIS_<<_AWB_POS_  \
					|_ISP_DIS_  <<_CCM_POS_ | _ISP_DIS_<<_AE_POS_   | _ISP_DIS_<<_DGAIN_POS_ | _ISP_DIS_<<_YGAMA_POS_ \
					| _ISP_DIS_<<_RGB_GAMA_POS_ | _ISP_DIS_<<_CH_POS_\
					|_ISP_DIS_<<_VDE_POS_ | _ISP_DIS_<<_EE_POS_   | _ISP_DIS_<<_CFD_POS_    |_ISP_DIS_<<_SAJ_POS_
					|_ISP_YUV422_DIS_ << _YUVMOD_POS_),
	.blc_adapt = {	//when _BLC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.blkl_r		= 0,					//BLC red adjust //signed 10bit
		.blkl_gr	= 0,					//BLC green(red) adjust //signed 10bit
		.blkl_gb	= 0,					//BLC green(blue) adjust //signed 10bit
		.blkl_b		= 0,					//BLC blue adjust //signed 10bit
		.blk_rate 	= {0,2,3,4,5,6,7,8},	//_ISP_AUTO_ use, [AE statistic YLOGA/step_len] to adj BLC para, 8 means 1 rate
		.step_len	= 5,					//_ISP_AUTO_ use
	},
	.ddc_adapt = {	//when _DDC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.hot_num 		= 2,				//亮点：目标点比周围24个点中的(24 - (8- hot_num))个点 都亮，差值 >((h_th_rate*p[2][2])/16 + hot_th)
		.dead_num		= 2,				//暗点：目标点比周围24个点中的(24 - (8-dead_num))个点 都暗，差值 >(d_th_rate*AVG/16 + dead_th), AVG为P[2][2]周围8个点平均值
		.hot_th			= 0,				//亮点：判断亮点的阈值，0~1023
		.dead_th		= 0,				//暗点：判断暗点的阈值，0~1023
		.avg_th			= 16,				//暗点/亮点替换：差值平均值的阈值， 0~255
		.d_th_rate		= {4,4,4,4,4,4,4,4},//_ISP_AUTO_时，根据cur_br获取d_th_rate， default使用 d_th_rate[7] , 16 means 1 rate
		.h_th_rate		= {8,8,8,8,8,8,8,8},//_ISP_AUTO_时，根据cur_br获取 h_th_rate， default使用 h_th_rate[7] , 16 means 1 rate
		.dpc_dn_en		= 1,				//1:开启pre_denoise，滤波系数与坐标距离，像素点差值正相关
		.indx_table		= {2,0,0,0,0,0,0,0},//pre_denoise: 取值范围0~7，配置 dn_idx_table, 值越大，滤波开的越大
		.indx_adapt		= {2,1,1,1,0,0,0,0},//_ISP_AUTO_ use：根据yloga/ddc_step查表获得的值，来调整indx_table 表中的值
		.std_th			= {6,20,30,40,50,80,120}, //差值对比表，对应用于获得indx_table的值
		.std_th_rate	= 0,				//用于调整 std_th ，std_th_rate * avg_val / 16;
		.ddc_step		= 7,				//_ISP_AUTO_ use
		.ddc_class		= 7,				//预留用
	},	
	.awb_adapt = {	////when _AWB_POS_ set _ISP_EN_ or _ISP_AUTO_
		.seg_mode		= 0x03,		//AWBStatistic，取值 0~3，根据Y值划分为 (1 << seg_mode)个统计区域
		.rg_start		= 191,		//AWBStatistic yuv_mod_en = 0使用，rgain (g*256/r)起始范围
		.rgmin			= 191,		//AWBStatistic yuv_mod_en = 0 使用，rgain比较的最小值，当rgain落在[rgmin,rgmax]范围内，则落在统计范围内
		.rgmax			= 485, 		//AWBStatistic yuv_mod_en = 0， rgain比较的最大值 // 256 -> 1 gain  500 /256 =about 1.9 gain
		.weight_in		= 3,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_in_low,bgain_in_high]的统计权重值（+1）
		.weight_mid		= 2,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_out_low,bgain_out_high]的统计权重值（+1）
		.ymin			= 0x0a,		//AWBStatistic 统计的Y值区域的最小值
		.ymax			= 0xc0,		//AWBStatistic 统计的Y值区域的最大值
		.hb_rate		= 0xff,		//AWB ADJ bgain <256时使用
		.hb_class		= 0x00,		//AWB ADJ 取值范围 0~3 , bgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hb_class))
		.hr_rate		= 0xff,		//AWB ADJ rgain <256时使用
		.hr_class		= 0x00,		//AWB ADJ 取值范围 0~3 , rgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hr_class))
		.awb_scene_mod	= 0,		//当前使用的AWB RGB GAIN，用于查表manu_awb_gain[]
		.manu_awb_gain	= { 		//定义不同的AWB GAIN表
		//(bgain << 20) | (ggain<< 10) | (rgain<< 0),
			(400 << 20) | (256<< 10) | (380<< 0), 
			(368 << 20) | (256<< 10) | (350<< 0),
			(465 << 20) | (256<< 10) | (225<< 0),
			(370 << 20) | (256<< 10) | (385<< 0),
			(370 << 20) | (256<< 10) | (385<< 0)
		},
		.yuv_mod_en		= 0,										 //1:base Y, 0: Gray World
		.cb_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},  //AWBStatistic yuv_mod_en = 1, 对应不同的Y分区的ABS(CB)阈值，取值范围 0~127
		.cr_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},	 //AWBStatistic yuv_mod_en = 1 ,对应不同的Y分区的ABS(CR)阈值，取值范围 0~127 
		.cbcr_th		= {0x8,0x0f,0x16,0x1e,0x24,0x2d,0x34,0x3c},  //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的ABS(CB)+ABS(CR)阈值，取值范围 0~255 
		.ycbcr_th		= 0x0a,										 //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的y阈值(y-ABS(CB)-ABS(CR))，取值范围 0~255 
		.manu_rgain		= 0,										 //manual AWB时记录配置的rgain
		.manu_ggain		= 0,										 //manual AWB时记录配置的ggain	
		.manu_bgain		= 0,										 //manual AWB时记录配置的bgain
		.rgain			= 0,										 //auto AWB时记录配置的rgain
		.ggain			= 0,										 //auto AWB时记录配置的ggain
		.bgain			= 0,										 //auto AWB时记录配置的bgain
		.seg_gain		= {{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0}}, //AUTO AWB时记录不同Y分区的RGB GAIN值
		.awb_tab		= {	//AWBStatistic yuv_mod_en = 0 用于根据(rgain-rg_start)查表获得目标g值，以16位单位（16*32 = 512）
			178,178,177,175,173,170,167,164,160,156,152,148,144,140,136,129,123,117,113,108,104,100, 94, 90, 87, 81, 78, 78, 76, 76, 76, 73, //bgain_out_high
			178,176,174,171,168,164,160,153,149,146,141,139,134,130,126,121,112,106,102, 96, 92, 88, 86, 83, 81, 77, 75, 74, 73, 72, 73, 74, //bgain_in_high
			178,169,161,150,146,143,138,136,131,125,119,112,113,111,102, 99, 91, 88, 85, 83, 82, 81, 78, 77, 74, 72, 71, 71, 71, 70, 71, 74, //bgain_in_low  
			175,154,143,138,136,130,126,119,116,113,108,106,106, 99, 92, 90, 85, 81, 79, 77, 75, 74, 72, 71, 70, 68, 67, 67, 66, 67, 69, 71 //bgain_out_low
		}		
	},					
	.ccm_adapt = {	//when _CCM_POS_ set _ISP_EN_ or _ISP_AUTO_
		//注意 CCM TAB排列顺序如下，即 竖着看，第一列为调整R， 第二列调整G，第三列调整B
		// RR,  GR, BR,
		// RG,  GG, BG,
		// RB,  GB, BB,
		//R:  (RR*R+RG*G + RB*B)/256 + s41
		//G:  (GR*R+GG*G + GB*B)/256 + s42
		//B:  (BR*R+BG*G + BB*B)/256 + s43
		.ccm	= {	//signed 10bit, -512~511, 
			0x100,	0x000,	0x000,  
			0x000,	0x100,	0x000,  
			0x00,	0x00,	0x100   
		},
		.s41	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s42	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s43	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
	},	
	.ae_adapt = {	//when _AE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.exp_adapt = {	//AE auto adj时使用的参数
			.ylog_cal_fnum	= 4,		//_ISP_AUTO_使用：AE统计的frame num，最大32，计算获得ylog_avg 和yloga
			.exp_tag		= {40,50,60,70,75,80,100,136}, //_ISP_AUTO_使用：根据cur_br查表获得目标ylog
			.exp_ext_mod	= 3,		//_ISP_AUTO_使用：低照度下的最小ylog值：exp_ext_mod*8
			.exp_gain		= 195*256,	//当前exp*gain的值
			.k_br			= 12,		//_ISP_AUTO_使用：用于从ylog换算cur_br的系数，值越大，换算的cur_br越大
			.exp_min		= 4,		//限制最小exp值：当exp_gain比较小时，调整gain
			.gain_max		= 1024*4,	//限制最大gain值：当exp_gain比较大时，调整exp
			.frame_nums		= 2,		//_ISP_AUTO_使用：曝光相关调整的帧数间隔
			.ratio_range	= 16,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar 范围不在[32-ratio_range/2,32 + ratio_range]时，加快调整速度
			.weight_in		= 1,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar <= 32时，使用weight_in系数(即目标照度需要降低时)
			.weight_out		= 4,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar > 32时，使用weight_out系数(即目标照度需要提高时)
			.ev_mode		= 0,	    //外部调整整体亮度用：1:在VDE模块调整bright_oft，0：在AE调整中使用
		},
		.hgrm_adapt = { 
			//AE统计配置的参数，AE统计将整幅图划分为5*5的块进行灰度（Y值）统计
			//X[0 - WIN_X0 - WIN_X1 - WIN_X2 - WIN_X3 - WIDTH]
			//Y[0 - WIN_Y0 - WIN_Y1 - WIN_Y2 - WIN_Y3 - HEIGHT]
			.allow_miss_dots	= 256,	//预留用
			.ae_win_x0			= 80,
			.ae_win_x1			= 160,
			.ae_win_x2			= 480,
			.ae_win_x3			= 560,
			.ae_win_y0			= 60,
			.ae_win_y1			= 120,
			.ae_win_y2			= 360,
			.ae_win_y3			= 420,
			.weight_0_7			= 0x44111111,//每4bit 对应每个区域的统计权重，区域 0~7
			.weight_8_15		= 0x114f4114,//每4bit 对应每个区域的统计权重，区域 8~15
			.weight_16_23		= 0x11111444,//每4bit 对应每个区域的统计权重，区域 16~23
			.weight_24			= 0x01,		 //每4bit 对应每个区域的统计权重，区域 24
			.hgrm_centre_weight	= {15,14,13,12,11,10,9,8}, //用于根据cur_br调整中间区域，即区域12的权重值
			.hgrm_gray_weight	= {8,8,9,9,10,10,11,12},   //_ISP_AUTO_使用：根据Y值划分区域调整统计的值
		},
	},		
	.rgbdgain_adapt = { //when _DGAIN_POS_ set _ISP_EN_ or _ISP_AUTO_
		.dgain		= {64,64,64,64,64,64,64,64,64},	//配置寄存器：根据Y值的大小划分8个区域来调整
		.dgain_rate	= {64,64,64,64,64,64,64,64}, 	//_ISP_AUTO_使用：根据cur_br获得调整rate，用于调整dgain[]
	},	
	.ygama_adapt = {	//when _YGAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {5,7,9,11,13,14,15,16}, //根据 tab_num[i]的值来选择sensor_ygamma_tab[tab_num[i]]
		.adpt_num		= {5,5,5,5,5,5,5,5},	  //_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_ygamma_tab[]表
		.gam_num0		= 14,					  //当前使用的gamma表index0, 对应sensor_ygamma_tab[index0]
		.gam_num1		= 14,					  //当前使用的gamma表index1, 对应sensor_ygamma_tab[index1]
		.br_mod			= 0,					  //根据br_mod来从index0和index1表中加权平均获得目标的ygamma值
		.bofst			= 0,					  //ymin值 = bosfst << (10 - 8)
		.lofst			= 0xff,					  //ymax值 = lofst << (10 - 8)
		.pad_num		= 1,					  //配置寄存器用，不为0，微调经过ygamma的RGB值
	},
	.rgbgama_adapt = { //when _RGB_GAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {0,1,2,3,4,5,6,7},	//根据 tab_num[i]的值来选择sensor_rgb_gamma[tab_num[i]] 
		.adpt_num		= {3,2,1,1,1,1,1,1},	//_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_rgb_gamma[]表
		.max_oft		= {16,12,12,8,4,0,0,0}, //_ISP_AUTO_: 根据cur_br查表获得当前的max_oft0值
		.gam_num0		= 3,					//当前使用的gamma表index0, 对应sensor_rgb_gamma[index0]
		.gam_num1		= 3,					//当前使用的gamma表index1, 对应sensor_rgb_gamma[index1]
		.max_oft0		= 0,					//用于加大rgbgamma的值
		.br_mod			= 0,					//根据br_mod来从index0和index1表中加权平均获得目标的rgbgamma值
		.rmin			= 0,					//限制最小r值
		.rmax			= 0xff, 				//限制最大r值
		.gmin			= 0,					//限制最小g值
		.gmax			= 0xff,					//限制最大g值
		.bmin			= 0,					//限制最小b值
		.bmax			= 0xff,					//限制最大b值
		.fog_llimt		= 64,					//_ISP_AUTO_: 根据ylog动态调整的 rmin/gmin/bmin的最大值
		.fog_hlimt		= 224,					//_ISP_AUTO_: 根据ylog动态调整的 rmax/gmax/bmax的最小值
		.fog_dotnum		= 4000,					//_ISP_AUTO_: 亮度统计值的目标值，用于计算获得ylog_low和ylog_high
	},
	.ch_adapt = {	//when _CH_POS_ set _ISP_EN_ or _ISP_AUTO_
		.stage0_en	= 1,//enable r g b
		.stage1_en	= 1,//enable y c m
		.enhence	= {0,1,0,0,0,0},//enhance channel  r g b y c m
		//r: >th1[0] && < th0[0], g: [th0[1],th1[1]], b: [th0[2],th1[2]],
		//y(r+g): [th0[3], th1[3]], c(g+b):[th0[4],th1[4]], m(b+r):[th0[5],th1[5]]
		.th1		= {320,192,320,128,256,384},//you can set hue width
		.th0		= {64,  64,192,  0,128,256},//you can set hue width
		//m_x c_x y_x b_x g_r r_x
		.r_rate		= {14,14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.g_rate		= {0, 14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.b_rate		= {0, 14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.sat		= {4,8,12,16,16,16,16,16,16,16,16,16,16,16,16,16,16}, //根据饱和度S按16划分为16个区域进行调整的rate表
		.rate		= {0,16,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/ch_step查表获得rate，用于调整r_rate，g_rate，b_rate，sat表
		.ch_step	= 6,						//_ISP_AUTO_使用
	},
	.vde_adapt = {	//when _VDE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.contra		= 0x80,	//取值范围0~255，对比度调节系数 (contra-128)/128, 配置为0x80时不调节
		.bright_k	= 0x80, //取值范围0~255，亮度调节系数 (bright_k-128)/128, 配置为0x80时不调节
		.bright_oft	= 0x80, //取值范围0~255，亮度增加值： (bright_oft-128), 配置为0x80时不调节
		.hue		= 0x80, //取值范围0~255，色度（UV）调节系数：配置为0x80时不调节
		.sat		= {60,64,68,78,84,88,88,84,80}, //饱和度调节表（调节UV），根据Y值划分为32间隔的8个区域进行取值，64表示1
		.sat_rate	= {10,10,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/vde_step选择sat_rate，用于调整sat[]表的值
		.vde_step	= 6,	//_ISP_AUTO_使用
	},
	.ee_adapt = {	//when _EE_POS_ set _ISP_EN_ or _ISP_AUTO_
		//锐化或降噪的差值区间[ee_dn_th-> ee_keep_th-> ee_sharp_th]
		//ee_dn_th = ee_dn_th + ee_th_adp *avg/256;
		//ee_keep_th = ee_dn_th + (1<<ee_dn_slope);
		//ee_sharp_th = ee_keep_th + (1<<ee_sharp_slope);
		.ee_class		= 1,	//预留用	
		.ee_step		= 6,	//_ISP_AUTO_使用：预留ylog 调整用
		.ee_dn_slope	= {1,1,1,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_dn_slope
		.ee_sharp_slope	= {2,2,2,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_sharp_slope	
		.ee_th_adp		= {8,8,8,8,8,8,8,8},	//_ISP_AUTO_使用：取值范围0~15，根据cur_br查表获得ee_th_adp	
		.ee_dn_th		= {24,24,24,24,24,24,24,24}, //_ISP_AUTO_使用：取值范围0~63，根据cur_br查表获得ee_dn_th	
		.sharp_class	= {0x7,0x9,0xa,0xa,0xa,0xa,0xa,0xa}, //_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得sharp_class,用于配置 ee_sharp_mask[12] = 32-sharp_class
		.dn_class		= {0,0,0,0,0,0,0,0},	//_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得dn_class,用于选择不同的dn_mask表，目前固定用0
	},
	.cfd_adapt = {	//when _EE_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		//根据Y值划分区域，
		//(1) Y < ccf_start的区域，mean_en = 1时，进行高斯滤波处理
		//(2) ccf_start < y < ccf_white_ymin, 使用 (ccf_white_ymin - y)/(16<<wclass)为系数调整UV
		//(3) ccf_white_ymin <= y < ymax的区域，直接配置UV 为128
		//(4) y > ymax同时 UV差值大于 th的区域，使用 rate/16 为系数调整UV
		.rate		= 4, 		// UV调整rate，取值范围0~15，
		.ymax		= 0xe0,		// 强光区 ymax配置，取值范围 0~255
		.th			= 0x20, 	// 配置(ABS(U) + ABS(V))阈值，取值范围 0~127
		.wdc_en		= 1, 		// 1：使能(2)(3)区域的调整	
		.wclass		= 1, 		//ccf_start: wymin - (16<<wclass)   reduce saturation
		.wymin		= 0xff, 	//ccf_white_ymin 
		.mean_en	= 1, 		//ccf_mean: 配置为1，使能(1)区域的调整
		.dn_class	= 0,		//选择ccf_cd_mask[9]表，目前固定配置为0
		.ccf_en		= 1,		//配置为1时，使能(4)区域的调整
	},
	.saj_adapt = {	//when _SAJ_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		.sat		= {12,12,12,12,12,12,12,13,13,14,14,15,15,16,16,16,16}, //取值范围0~31，饱和度调节率表，色饱和度[0,255]划分为16个区域，
		.sat_rate	= {5,6,7,8,9,10,12,16}, //_ISP_AUTO_使用：根据yloga/saj_step查表用于调节sat[]表, 16为单位
		.saj_step	= 6,		//_ISP_AUTO_使用：
	},
	.md_adapt = {	
		.pixel_th		= 20,
		.num_th			= 20,
		.update_cnt		= 1,
		.win_h_start	= (640/4)*1,	
		.win_h_end		= (640/4)*3,
		.win_v_start	= (480/4)*1,
		.win_v_end		= (480/4)*3,
	}, 

	.p_fun_adapt = {
		.fp_rotate		= NT99142_rotate,
		.fp_hvblank		= NULL,
		.fp_exp_gain_wr	= NULL
	},
};
SENSOR_HEADER_ITEM_SECTION const Sensor_Ident_T nt99142_init  =
{
	.sensor_struct_addr   	= (u32 *)&nt99142_adpt,     
	.sensor_struct_size   	= sizeof(Sensor_Adpt_T),
	.sensor_init_tab_adr  	= (u32 *)NT99142InitTable,     
	.sensor_init_tab_size 	= sizeof(NT99142InitTable),
	.lsc_tab_adr 			= (u32 *)NULL,     
	.lsc_tab_size 			= 0, 
	.sensor_name	  		= "NT99142_VGA",
	.w_cmd            		= 0x54,                   
	.r_cmd            		= 0x55,                   
	.addr_num         		= 0x02,                   
	.data_num         		= 0x01,   
	.id               		= 0x14, 
	.id_reg           		= 0x3000,                   
};


#endif
