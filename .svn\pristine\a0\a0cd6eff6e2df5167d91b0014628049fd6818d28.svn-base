/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/app_api.h"

/*******************************************************************************
* Function Name  : main
* Description    :
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
int main(void)
{
//--------------power on--------
    app_init();       // system power on configure
	#if (ENCRYPT_FUNC_SWITCH == 1)
	SysCtrl.flash_md5_check_value = spi_flash_check_md5();
	#endif
#if FUN_BATTERY_CHARGE_SHOW
	if(SysCtrl.dev_stat_power & POWERON_FLAG_DCIN)
	{
		deg_Printf("[main]--app_taskStart(TASK_BAT_CHARGE,1);--\r\n");
		app_taskStart(TASK_BAT_CHARGE,1);
	}
	else
#endif
	{
		SysCtrl.start_judge_usb = 1;
		//task_com_USB_CS_DM_DP_status_select(1);		
		app_taskStart(TASK_SHOW_LOGO,0);		
		
	}
	app_taskService();
	return 2; // for usb upgrade
}



