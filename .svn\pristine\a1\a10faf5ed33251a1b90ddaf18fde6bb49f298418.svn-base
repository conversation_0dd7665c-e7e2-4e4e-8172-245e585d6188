/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_HX8352B

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
    CMD(0xE2),DAT(0x15),
    CMD(0xE5),DAT(0x00),
    C<PERSON>(0xE7),DAT(0x00),
    CMD(0xE8),DAT(0x70),
    CMD(0xEC),DAT(0x08),
    CMD(0xED),DAT(0x47),
    CMD(0xEE),DAT(0x20),
    CMD(0xEF),DAT(0x59),
    /*CMD(0x23),DAT(0x79),
    DLY(10),
    CMD(0x24),DAT(0x7c),
    DLY(10),
    CMD(0x25),DAT(0x55),
    DLY(10),*/
    CMD(0x29),DAT(0x01),
    CMD(0x2F),DAT(0x1D),
    CMD(0x2B),DAT(0x03),


    CMD(0x1B),DAT(0x1E),
    CMD(0x19),DAT(0x01),
    DLY(10),

    /*CMD(0x1F),DAT(0x8C),
    CMD(0x1F),DAT(0x84),
    DLY(10),

    CMD(0x1F),DAT(0x94),
    DLY(10),,*/

    CMD(0x1F),DAT(0xD4),

    /*CMD(0x1c),DAT(0x05),
    CMD(0x1d),DAT(0x07),
    CMD(0x1e),DAT(0x07),*/

    CMD(0x23),DAT(0x79),
    DLY(10),
    CMD(0x24),DAT(0x40),
    DLY(10),
    CMD(0x25),DAT(0x50),
    DLY(10),


    CMD(0x28),DAT(0x3C),

    CMD(0x02),DAT(0x00),
    CMD(0x03),DAT(0x00),

    CMD(0x08),DAT(0x01),
    CMD(0x09),DAT(0x8f),

    CMD(0x06),DAT(0x00),
    CMD(0x07),DAT(0x00),

    CMD(0x04),DAT(0x00),
    CMD(0x05),DAT(0xEF),

    CMD(0x40),DAT(0x00),
    CMD(0x41),DAT(0x25),
    CMD(0x42),DAT(0x24),
    CMD(0x43),DAT(0x3C),
    CMD(0x44),DAT(0x3A),
    CMD(0x45),DAT(0x3F),
    CMD(0x46),DAT(0x1D),
    CMD(0x47),DAT(0x71),
    CMD(0x48),DAT(0x07),
    CMD(0x49),DAT(0x04),
    CMD(0x4A),DAT(0x05),
    CMD(0x4B),DAT(0x09),
    CMD(0x4C),DAT(0x14),
    CMD(0x50),DAT(0x00),
    CMD(0x51),DAT(0x05),
    CMD(0x52),DAT(0x03),
    CMD(0x53),DAT(0x1B),
    CMD(0x54),DAT(0x1A),
    CMD(0x55),DAT(0x3F),
    CMD(0x56),DAT(0x0E),
    CMD(0x57),DAT(0x62),
    CMD(0x58),DAT(0x0B),
    CMD(0x59),DAT(0x16),
    CMD(0x5A),DAT(0x1A),
    CMD(0x5B),DAT(0x1B),
    CMD(0x5C),DAT(0x18),
    CMD(0x5D),DAT(0xFF),
    CMD(0x17),DAT(0x05),
    CMD(0x16),DAT(0x08),
    CMD(0x36),DAT(0x00),
    CMD(0x28),DAT(0x20),
    CMD(0x60),DAT(0x08),
    DLY(10),
    CMD(0x28),DAT(0x38),
    DLY(10),
    CMD(0x28),DAT(0x3C),

    CMD(0x22),
    DLY(10),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name           = "MCU_HX8352B",
    .lcd_bus_type   = LCD_IF_GET(),
    .scan_mode      = LCD_DISPLAY_ROTATE_90,
    .te_mode        = LCD_MCU_TE_ENABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div       = LCD_PCLK_DIV(400*240*2*60),
    .clk_per_pixel  = 2,
    .even_order     = LCD_RGB,
    .odd_order      = LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w       = 240,
    .screen_h       = 400,

    .video_w        = 400,
    .video_h        = 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)

    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness     = -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index   = 8,

    .gamma_index    = {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif




























