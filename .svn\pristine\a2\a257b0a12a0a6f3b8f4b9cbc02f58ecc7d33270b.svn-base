/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef HX330X_UART_H
#define HX330X_UART_H



#define  UART0   0
#define  UART1   1


/*******************************************************************************
* Function Name  : hx330x_uart0IRQHandler
* Description    : uart 0 IRQ handler
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_uart0IRQHandler(void);
void hx330x_uart0rx_test(void);
/*******************************************************************************
* Function Name  : hx330x_uart0IOCfg
* Description    : uart0 rx tx config
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_uart0IOCfg(u8 en, u8 rx_reinit);
/*******************************************************************************
* Function Name  : hx330x_uart0Init
* Description    : uart0 initial 
* Input          : u32 baudrate : uart0 baudrate
				   void (*isr)(u8 data) : rx callback
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_uart0Init(u32 baudrate,void (*isr)(u8 data));
/*******************************************************************************
* Function Name  : hx330x_uart0SendByte
* Description    : uart0 send data
* Input          : u8 data : send data
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_uart0SendByte(u8 data);





#endif
