/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiItemManageProc
* Description    : uiItemManageProc
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiItemManageProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiItemManageObj *pItemManage;
	uiWinObj *pWin;
	touchInfor *tInfor;
	u32 i;	
	if(uiWidgetProc(msg))
		return;	
	hWin		= msg->curWin;
	pItemManage	= (uiItemManageObj*)uiHandleToPtr(hWin);
	pWin		= &(pItemManage->widget.win);

	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			return;
		case MSG_WIN_PAINT:
			//deg_Printf("uiItemManageProc\n");
			uiWinDrawRoundRectWithRim((uiRect*)(msg->para.p),(uiRect*)&pWin->round_rect,pWin->bgColor);
			//if(pWin->bgColor != INVALID_COLOR)
			//{
			//	uiWinDrawRect((uiRect*)(msg->para.p),pWin->bgColor);
			//}
			//deg_Printf("uiItemManageProc:%x\n",pWin->bgColor);
			//if(pWin->bgColor != INVALID_COLOR)
				
			//deg_msg("paint itemManage [%d]:[%d %d %d %d]\n",pItemManage->widget.id,pWin->invaliditemManage.x0,pWin->invaliditemManage.y0,pWin->invaliditemManage.x1,pWin->invaliditemManage.y1);
			return;
		case MSG_WIN_TOUCH:
			tInfor = (touchInfor *)(msg->para.p);
			if(tInfor->touchState == TOUCH_PRESS)
			{
				uiItemManageSetCurItem(hWin,tInfor->touchItem);
			}
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			uiItemManageGetTouchInfor(hWin,(touchInfor *)(msg->para.p));
			return;
		case MSG_WIN_SELECT_INFOR_EX:
		case MSG_WIN_UNSELECT_INFOR_EX:
			for(i = 0;i < pItemManage->itemSum;i++)
			{
				uiWinSendMsg(pItemManage->itemHandle[i],msg);
			}
			return;
		case MSG_WIN_CHANGE_ROUNDRECT_RADIUS:
			for(i = 0;i < pItemManage->itemSum;i++)
			{
				uiWinSendMsg(pItemManage->itemHandle[i],msg);
			}
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiItemManageCreate
* Description    : uiItemManageCreate
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiItemManageCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle hItemManage;
	uiItemManageObj* pItemManage;
	uiWinObj		*pWin;
	if(infor->imageAlign == 0)
	{
		infor->style &=~WIN_ROUND_RECT;
	}
	hItemManage = uiWinCreate(infor->x0,infor->y0,infor->width,infor->height,parent,uiItemManageProc,sizeof(uiItemManageObj),WIN_WIDGET|WIN_TOUCH_SUPPORT|infor->style);

	if(hItemManage!=INVALID_HANDLE)
	{
		pItemManage = (uiItemManageObj*)uiHandleToPtr(hItemManage);
		pWin		= &(pItemManage->widget.win);
		pItemManage->currentRes = 0xffffffff;
		pItemManage->style      = infor->style;
		//deg_Printf("uiItemManageCreate:%x\n", pItemManage->style);
		uiWidgetSetId(hItemManage,infor->id);
		uiWidgetSetType(hItemManage,WIDGET_ITEM_MANAGE);
		//if(infor->bgColor != INVALID_COLOR)
			uiWinSetbgColor(hItemManage, infor->bgColor);
		if(infor->rimColor != INVALID_COLOR)
		{
			pWin->rect.rimColor = pWin->round_rect.rimColor = infor->rimColor;
		}
		if(infor->style & WIN_ROUND_RECT)
		{
			pWin->round_rect.round_type = infor->imageAlign;
		}	
		//if(pWin->round_rect.round_type)
		//{
		//	x0 += pWin->round_rect.radius;
		//	width -= pWin->round_rect.radius * 2;
		//}
		//deg_Printf("uiItemManageCreate[%d,%d,%d,%d][%x,%d,%x]\n",pWin->round_rect.x0, pWin->round_rect.x1, pWin->round_rect.y0, pWin->round_rect.y1,pWin->round_rect.round_type, pWin->round_rect.radius,pWin->round_rect.rimColor);
	}
	return hItemManage;
}
/*******************************************************************************
* Function Name  : uiItemManageSetDefaultWidth
* Description    : uiItemManageSetDefaultWidth
* Input          : uiItemManageObj* pitemManage,u16 width
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiItemManageSetDefaultWidth(uiItemManageObj* pitemManage,u16 width)
{
	int i;
	for(i=0;i < MAX_ROW_NUM;i++)
	{
		pitemManage->rowItem[i].firstItemGap 	= 0;
		pitemManage->rowItem[i].itemGap			= 0;
		pitemManage->rowItem[i].itemWidth		= width;
		pitemManage->rowItem[i].itemWSum		= 1;
	}
}
/*******************************************************************************
* Function Name  : uiItemManageSetItemHeight
* Description    : uiItemManageSetItemHeight
* Input          : winHandle hitemManage,u16 itemHeight
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageSetItemHeight(winHandle hitemManage,u16 itemHeight)
{
	uiItemManageObj *pitemManage;
	uiWinObj *pWin;
	u16 winHeight;
	u16 winGap;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return 0;
	if(hitemManage != INVALID_HANDLE)
	{

		pitemManage	= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		pWin		= &(pitemManage->widget.win);
		if(!(pitemManage->style & WIN_NOT_ZOOM))
			itemHeight 	= USER_Rh(itemHeight);
		winHeight	= pWin->rect.y1 - pWin->rect.y0 + 1;
		if(itemHeight > winHeight)
			return 0;
		uiItemManageSetDefaultWidth(pitemManage, pWin->rect.x1 - pWin->rect.x0 + 1);
		pitemManage->itemHeight	= itemHeight;
		pitemManage->itemSum	= winHeight/itemHeight;
		if(pitemManage->itemSum > MAX_ROW_NUM)
			pitemManage->itemSum = MAX_ROW_NUM;
		winGap = winHeight - itemHeight*pitemManage->itemSum;
		if(winGap == 0 && pitemManage->itemSum > 1)
		{
			pitemManage->itemSum--;
		}
		winGap = winHeight - itemHeight*pitemManage->itemSum;
		if(pitemManage->itemSum == 1)
		{
			pitemManage->itemGap 		= 0;
			pitemManage->firstItemGap 	= (winHeight - pitemManage->itemHeight)/2;
		}
		else
		{
			pitemManage->itemGap		= winGap/(pitemManage->itemSum-1);
			pitemManage->firstItemGap	= (winGap - pitemManage->itemGap*(pitemManage->itemSum-1))/2; 
		}
		return pitemManage->itemSum;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : uiItemManageSetHeightAvgGap
* Description    : uiItemManageSetHeightAvgGap
* Input          : winHandle hitemManage,u16 itemHeight
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageSetHeightAvgGap(winHandle hitemManage,u16 itemHeight)
{
	uiItemManageObj* pitemManage;
	uiWinObj* pWin;
	u16 winHeight;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return 0;
	if(hitemManage != INVALID_HANDLE)
	{

		pitemManage	= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		pWin		= &(pitemManage->widget.win);
		if(!(pitemManage->style & WIN_NOT_ZOOM))
			itemHeight 	= USER_Rh(itemHeight);
		winHeight	= pWin->rect.y1 - pWin->rect.y0 + 1;
		if(itemHeight > winHeight)
			return 0;
		uiItemManageSetDefaultWidth(pitemManage, pWin->rect.x1 - pWin->rect.x0 + 1);
		pitemManage->itemHeight	= itemHeight;
		pitemManage->itemSum	= winHeight/itemHeight;
		if(pitemManage->itemSum > MAX_ROW_NUM)
			pitemManage->itemSum = MAX_ROW_NUM;
		pitemManage->itemGap		= (winHeight%itemHeight)/(pitemManage->itemSum+1);
		pitemManage->firstItemGap	= pitemManage->itemGap;
		if(pitemManage->itemGap == 0 && pitemManage->itemSum>1)
		{
			pitemManage->itemSum--;
			pitemManage->itemGap = (winHeight-itemHeight*pitemManage->itemSum)/(pitemManage->itemSum+1);
			pitemManage->firstItemGap = pitemManage->itemGap;
		}
		//deg_Printf("winHeight:%d, itemHeight:%d,pitemManage->itemGap:%d,pitemManage->firstItemGap:%d\n",winHeight, itemHeight, pitemManage->itemGap,  pitemManage->firstItemGap);
		return pitemManage->itemSum;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : uiItemManageSetHeightNotGap
* Description    : uiItemManageSetHeightNotGap
* Input          : winHandle hitemManage,u16 itemHeight
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageSetHeightNotGap(winHandle hitemManage,u16 itemHeight)
{
	uiItemManageObj* pitemManage;
	uiWinObj* pWin;
	u16 winHeight;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return 0;
	if(hitemManage != INVALID_HANDLE)
	{
		pitemManage	= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		pWin		= &(pitemManage->widget.win);
		deg_Printf("pitemManage->style:%x\n", pitemManage->style);
		if(!(pitemManage->style & WIN_NOT_ZOOM))
			itemHeight 	= USER_Rh(itemHeight);
		winHeight	= pWin->rect.y1 - pWin->rect.y0 + 1;
		if(itemHeight > winHeight)
			return 0;
		uiItemManageSetDefaultWidth(pitemManage, pWin->rect.x1 - pWin->rect.x0 + 1);
		pitemManage->itemHeight	= itemHeight;
		pitemManage->itemSum	= winHeight/itemHeight;
		if(pitemManage->itemSum > MAX_ROW_NUM)
			pitemManage->itemSum = MAX_ROW_NUM;
		pitemManage->itemGap		= (winHeight%itemHeight)/(pitemManage->itemSum+1);
		pitemManage->firstItemGap	= pitemManage->itemGap;
		//deg_Printf("winHeight:%d, itemHeight:%d,pitemManage->itemGap:%d,pitemManage->firstItemGap:%d\n",winHeight, itemHeight, pitemManage->itemGap,  pitemManage->firstItemGap);
		return pitemManage->itemSum;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : uiItemManageSetRowSum
* Description    : uiItemManageSetRowSum
* Input          : winHandle hitemManage,u16 itemSum,u16 itemHeight
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageSetRowSum(winHandle hitemManage,u16 itemSum,u16 itemHeight)
{
	uiItemManageObj* pitemManage;
	uiWinObj* pWin;
	u16 winHeight;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return 0;
	if(hitemManage != INVALID_HANDLE)
	{

		
		pitemManage	= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		pWin		= &(pitemManage->widget.win);
		if(!(pitemManage->style & WIN_NOT_ZOOM))
			itemHeight 	= USER_Rh(itemHeight); 
		winHeight	= pWin->rect.y1 - pWin->rect.y0 + 1;
		if(itemSum > MAX_ROW_NUM)
			itemSum = MAX_ROW_NUM;
		if(itemSum == 0 || itemHeight == 0)
			return 0;
		uiItemManageSetDefaultWidth(pitemManage, pWin->rect.x1 - pWin->rect.x0 + 1);
		if(itemSum*itemHeight > (winHeight - 2*(itemSum+1)))
			pitemManage->itemHeight = (winHeight - 2*(itemSum+1))/itemSum;
		else
			pitemManage->itemHeight = itemHeight;
		pitemManage->itemSum 		= itemSum;
		pitemManage->itemGap 		= (winHeight-pitemManage->itemHeight*itemSum)/(pitemManage->itemSum+1);
		pitemManage->firstItemGap 	= pitemManage->itemGap;
		return pitemManage->itemSum;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : uiItemManageSetColumnSumWithGap
* Description    : uiItemManageSetColumnSumWithGap
* Input          : winHandle hitemManage,u8 rowNum,u16 itemSum,u16 itemWidth,u16 gap
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageSetColumnSumWithGap(winHandle hitemManage,u16 rowNum,u16 itemSum,u16 itemWidth,u16 gap)
{
	uiItemManageObj* pitemManage;
	uiWinObj* pWin;
	u16 winWidth;
	wItemInfor* rowInfor;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return 0;
	if(hitemManage != INVALID_HANDLE)
	{


		pitemManage	= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		pWin		= &(pitemManage->widget.win);
		if(!(pitemManage->style & WIN_NOT_ZOOM))
		{
			itemWidth 	= USER_Rx(itemWidth);
			gap		  	= USER_Rw(gap);
		}
		if(rowNum >= pitemManage->itemSum)
			return 0;
		if(itemSum == 0|| itemWidth == 0)
			return 0;
		if(itemSum > MAX_COLUMN_NUM)
			itemSum = MAX_COLUMN_NUM;	
		winWidth = pWin->rect.x1 - pWin->rect.x0 + 1;
		rowInfor = &(pitemManage->rowItem[rowNum]);
		if(itemSum * (itemWidth + gap) > winWidth)
		{
			rowInfor->itemWidth = (winWidth - 2*(itemSum-1))/itemSum;
			gap = 2;
		}
		else
			rowInfor->itemWidth = itemWidth;
		rowInfor->itemGap		= gap;
		rowInfor->firstItemGap  = (winWidth - rowInfor->itemWidth*itemSum-gap*(itemSum-1))>>1;
		rowInfor->itemWSum		= itemSum;
		return rowInfor->itemWSum;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : uiItemManageCreateRowItem
* Description    : uiItemManageCreateRowItem
* Input          : uiItemManageObj* pitemManage,u16 rowNum,itemCreateFunc func,s16 x,s16 y
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiItemManageCreateRowItem(uiItemManageObj* pitemManage,u16 rowNum,itemCreateFunc func,s16 x,s16 y)
{
	u16 i;
	uiWinObj		*pWin;
	if(pitemManage->itemSum >= MAX_ITEM_NUM)
		return;
	pWin 			= &(pitemManage->widget.win);
	x += pitemManage->rowItem[rowNum].firstItemGap;
	
	for(i = 0;i < pitemManage->rowItem[rowNum].itemWSum; i++)
	{
		pitemManage->itemHandle[pitemManage->itemSum] = func(x,y, pitemManage->rowItem[rowNum].itemWidth, pitemManage->itemHeight, pitemManage->style, pWin->round_rect.rimColor, pWin->round_rect.round_type);
		pitemManage->itemSum++;
		if(pitemManage->itemSum >= MAX_ITEM_NUM)
			break;
		x += pitemManage->rowItem[rowNum].itemGap + pitemManage->rowItem[rowNum].itemWidth;
	}
}
/*******************************************************************************
* Function Name  : uiItemManageCreateItem
* Description    : uiItemManageCreateItem
* Input          : winHandle hitemManage,itemCreateFunc func,getResInfor getRes,u32 resItemSum
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageCreateItem(winHandle hitemManage,itemCreateFunc func,getResInfor getRes,u32 resItemSum)
{
	uiItemManageObj* pitemManage;
	u16 i,rowSum;
	uiRect pos;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage!=INVALID_HANDLE)
	{
		uiWinGetRelativePos(hitemManage,&pos);
		pitemManage 		= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		pitemManage->getRes	= getRes;
		pitemManage->resSum	= resItemSum;
		if(func == NULL)
			return;
		pos.y0 += pitemManage->firstItemGap;
		rowSum  = pitemManage->itemSum;
		pitemManage->itemSum = 0;
		for(i=0;i < rowSum;i++)
		{
			uiItemManageCreateRowItem(pitemManage,i,func,pos.x0,pos.y0);
			pos.y0 += pitemManage->itemHeight + pitemManage->itemGap;
			if(pitemManage->itemSum >= MAX_ITEM_NUM)
				break;
		}
	}
}
/*******************************************************************************
* Function Name  : uiItemManageUpdateRes
* Description    : uiItemManageUpdateRes
* Input          : winHandle hitemManage,u32 resItemSum,u32 curResItem
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageUpdateRes(winHandle hitemManage,u32 resItemSum,u32 curResItem)
{
	uiItemManageObj* pitemManage;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage!=INVALID_HANDLE)
	{
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		if(pitemManage->currentRes < pitemManage->resSum)
			uiWinSendMsgId(pitemManage->itemHandle[pitemManage->currentRes % pitemManage->itemSum],MSG_WIN_UNSELECT);
		pitemManage->resSum 	= resItemSum;
		pitemManage->currentRes	= 0xffffffff;
		uiItemManageSetCurItem(hitemManage,curResItem);
	}
}
/*******************************************************************************
* Function Name  : uiItemManageUpdateAllItem
* Description    : uiItemManageUpdateAllItem
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageUpdateAllItem(winHandle hitemManage)
{
	uiItemManageObj* pitemManage;
	u32 curResItem;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage != INVALID_HANDLE)
	{
		pitemManage	= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		curResItem	= pitemManage->currentRes;
		pitemManage->currentRes = 0xffffffff;
		uiItemManageSetCurItem(hitemManage,curResItem);
	}
}
/*******************************************************************************
* Function Name  : uiItemManageSetResInforFuncEx
* Description    : uiItemManageSetResInforFuncEx
* Input          : winHandle hitemManage,getResInforEx getResEx
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetResInforFuncEx(winHandle hitemManage,getResInforEx getResEx)
{
	uiItemManageObj* pitemManage;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage!=INVALID_HANDLE)
	{
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		pitemManage->getResEx = getResEx;
	}
}
/*******************************************************************************
* Function Name  : uiItemManageSetCurItem
* Description    : uiItemManageSetCurItem
* Input          : winHandle hitemManage,u32 itemResNum
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetCurItem(winHandle hitemManage,u32 itemResNum)
{
	uiItemManageObj* pitemManage;
	u32 itemResStart,itemResEnd,i,cnt;
	resID image,str,selectImage,selectStr;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage != INVALID_HANDLE)
	{
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		if(itemResNum >= pitemManage->resSum)
			return;
		itemResStart = (itemResNum/pitemManage->itemSum)*pitemManage->itemSum;
		itemResEnd   = itemResStart + pitemManage->itemSum - 1;
		if(itemResEnd >= pitemManage->resSum)
			itemResEnd = pitemManage->resSum-1;
		if(pitemManage->currentRes < pitemManage->resSum)
			uiWinSendMsgId(pitemManage->itemHandle[pitemManage->currentRes%pitemManage->itemSum],MSG_WIN_UNSELECT);
		if(pitemManage->currentRes < itemResStart || pitemManage->currentRes > itemResEnd)
		{
			cnt = 0;
			if(pitemManage->getResEx)
			{
				for(i = itemResStart; i <= itemResEnd;i++)
				{
					pitemManage->getResEx(i,&image,&str,&selectImage,&selectStr);
					uiWinSendMsgId(pitemManage->itemHandle[cnt],MSG_WIN_INVALID_RESID);
					if(image != INVALID_RES_ID)
						uiWinSetResid(pitemManage->itemHandle[cnt],image);
					if(str != INVALID_RES_ID)
						uiWinSetResid(pitemManage->itemHandle[cnt],str);
					if(selectImage != INVALID_RES_ID)
						uiWinSetItemSelResid(pitemManage->itemHandle[cnt],selectImage);
					if(selectStr!=INVALID_RES_ID)
						uiWinSetItemSelResid(pitemManage->itemHandle[cnt],selectStr);
					cnt++;
				}
			}
			else if(pitemManage->getRes)
			{
				for(i = itemResStart;i <= itemResEnd;i++)
				{
					pitemManage->getRes(i,&image,&str);
					uiWinSendMsgId(pitemManage->itemHandle[cnt],MSG_WIN_INVALID_RESID);
					if(image != INVALID_RES_ID)
						uiWinSetResid(pitemManage->itemHandle[cnt],image);
					if(str != INVALID_RES_ID)
						uiWinSetResid(pitemManage->itemHandle[cnt],str);
					cnt++;
				}
			}
			for(i = 0;i < pitemManage->itemSum;i++)
				uiWinSetVisible(pitemManage->itemHandle[i],1);
			cnt = itemResEnd - itemResStart + 1;
			if(cnt < pitemManage->itemSum)
			{
				for(i = cnt;i < pitemManage->itemSum;i++)
				{
					uiWinSendMsgId(pitemManage->itemHandle[i],MSG_WIN_INVALID_RESID);
				}
			}
		}
		//if(pitemManage->currentRes < pitemManage->resSum)
		//	uiWinSendMsgId(pitemManage->itemHandle[pitemManage->currentRes%pitemManage->itemSum],MSG_WIN_UNSELECT);
		pitemManage->currentRes = itemResNum;
		uiWinSendMsgId(pitemManage->itemHandle[pitemManage->currentRes-itemResStart],MSG_WIN_SELECT);
	}
}
/*******************************************************************************
* Function Name  : uiItemManageUpdateCurItem
* Description    : uiItemManageUpdateCurItem
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageUpdateCurItem(winHandle hitemManage)
{
	uiItemManageObj* pitemManage;
	u32 itemNum;
	resID image,str,selectImage,selectStr;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage!=INVALID_HANDLE)
	{
		pitemManage=(uiItemManageObj*)uiHandleToPtr(hitemManage);
		if(pitemManage->currentRes >= pitemManage->resSum)
			return;
		itemNum = pitemManage->currentRes%pitemManage->itemSum;
		if(pitemManage->getResEx)
		{
			pitemManage->getResEx(pitemManage->currentRes,&image,&str,&selectImage,&selectStr);
			uiWinSendMsgId(pitemManage->itemHandle[itemNum],MSG_WIN_INVALID_RESID);
			if(image != INVALID_RES_ID)
				uiWinSetResid(pitemManage->itemHandle[itemNum],image);
			if(str != INVALID_RES_ID)
				uiWinSetResid(pitemManage->itemHandle[itemNum],str);
			if(selectImage != INVALID_RES_ID)
				uiWinSetItemSelResid(pitemManage->itemHandle[itemNum],selectImage);
			if(selectStr != INVALID_RES_ID)
				uiWinSetItemSelResid(pitemManage->itemHandle[itemNum],selectStr);
		}
		else if(pitemManage->getRes)
		{
			pitemManage->getRes(pitemManage->currentRes,&image,&str);
			uiWinSendMsgId(pitemManage->itemHandle[itemNum],MSG_WIN_INVALID_RESID);
			if(image!=INVALID_RES_ID)
				uiWinSetResid(pitemManage->itemHandle[itemNum],image);
			if(str!=INVALID_RES_ID)
				uiWinSetResid(pitemManage->itemHandle[itemNum],str);
		}
		uiWinSendMsgId(pitemManage->itemHandle[itemNum],MSG_WIN_UNSELECT);
		uiWinSendMsgId(pitemManage->itemHandle[itemNum],MSG_WIN_SELECT);
	}
	return ;
}
/*******************************************************************************
* Function Name  : uiItemManageNextItem
* Description    : uiItemManageNextItem
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageNextItem(winHandle hitemManage)
{
	uiItemManageObj* pitemManage;
	u32 itemResNum;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage != INVALID_HANDLE)
	{
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		itemResNum  = pitemManage->currentRes+1;
		if(itemResNum >= pitemManage->resSum)
			itemResNum = 0;
		uiItemManageSetCurItem(hitemManage,itemResNum);	
	}
}
/*******************************************************************************
* Function Name  : uiItemManagePreItem
* Description    : uiItemManagePreItem
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManagePreItem(winHandle hitemManage)
{
	uiItemManageObj* pitemManage;
	u32 itemResNum;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage != INVALID_HANDLE)
	{
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		itemResNum  = pitemManage->currentRes - 1;
		if(itemResNum >= pitemManage->resSum)
			itemResNum = pitemManage->resSum - 1;
		uiItemManageSetCurItem(hitemManage,itemResNum);	
	}
}
/*******************************************************************************
* Function Name  : uiItemManageNextPage
* Description    : uiItemManageNextPage
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageNextPage(winHandle hitemManage)
{
	uiItemManageObj* pitemManage;
	u32 itemResNum;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage!=INVALID_HANDLE)
	{
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		if(pitemManage->resSum <= pitemManage->itemSum)
			return;
		itemResNum = (pitemManage->currentRes/pitemManage->itemSum)*pitemManage->itemSum+pitemManage->itemSum;
		if(itemResNum >= pitemManage->resSum)
			itemResNum = 0;
		uiItemManageSetCurItem(hitemManage,itemResNum);	
	}
}
/*******************************************************************************
* Function Name  : uiItemManagePrePage
* Description    : uiItemManagePrePage
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManagePrePage(winHandle hitemManage)
{
	uiItemManageObj* pitemManage;
	u32 itemResNum,itemResStart;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage != INVALID_HANDLE)
	{
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		if(pitemManage->resSum <= pitemManage->itemSum)
			return;
		itemResStart = (pitemManage->currentRes/pitemManage->itemSum)*pitemManage->itemSum;
		if(itemResStart == 0)
			itemResNum = (pitemManage->resSum/pitemManage->itemSum)*pitemManage->itemSum;
		else
			itemResNum = itemResStart - pitemManage->itemSum;
		uiItemManageSetCurItem(hitemManage,itemResNum);	
	}
}
/*******************************************************************************
* Function Name  : uiItemManageGetCurrentItem
* Description    : uiItemManageGetCurrentItem
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageGetCurrentItem(winHandle hitemManage)
{
	uiItemManageObj* pitemManage;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return 0;
	if(hitemManage != INVALID_HANDLE)
	{
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		if(pitemManage->currentRes >= pitemManage->resSum)
			return 0;
		return pitemManage->currentRes;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : uiItemManageSetCharInfor
* Description    : uiItemManageSetCharInfor
* Input          : winHandle hitemManage,charFont font,u8 strAlign,uiColor fontColor
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetCharInfor(winHandle hitemManage,charFont font,u8 strAlign,uiColor fontColor)
{
	uiItemManageObj* pitemManage;
	u32 i;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage != INVALID_HANDLE)
	{
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		for(i=0;i < pitemManage->itemSum;i++)
			uiWinSetStrInfor(pitemManage->itemHandle[i],font,strAlign,fontColor);
	}
}
/*******************************************************************************
* Function Name  : uiItemManageSetSelectColor
* Description    : uiItemManageSetSelectColor
* Input          : winHandle hitemManage,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetSelectColor(winHandle hitemManage,uiColor color)
{
	uiItemManageObj* pitemManage;
	u32 i;
	uiResInfo infor;
	uiWinMsg msg;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage!=INVALID_HANDLE)
	{
		uiResInforInit(&infor);
		infor.color = color;
		msg.id		= MSG_WIN_SELECT_INFOR;
		msg.para.p	= (void*)&infor;
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		for(i = 0;i < pitemManage->itemSum; i++)
			uiWinSendMsg(pitemManage->itemHandle[i],&msg);
	}
}
/*******************************************************************************
* Function Name  : uiItemManageSetSelectImage
* Description    : uiItemManageSetSelectImage
* Input          : winHandle hitemManage,resID image
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetSelectImage(winHandle hitemManage,resID image)
{
	uiItemManageObj* pitemManage;
	u32 i;
	uiResInfo infor;
	uiWinMsg msg;

	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage != INVALID_HANDLE)
	{
		uiResInforInit(&infor);
		infor.image = image;
		msg.id		= MSG_WIN_SELECT_INFOR;
		msg.para.p 	= (void*)&infor;
		pitemManage	= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		for(i = 0;i < pitemManage->itemSum;i++)
			uiWinSendMsg(pitemManage->itemHandle[i],&msg);
	}
}
/*******************************************************************************
* Function Name  : uiItemManageSetUnselectColor
* Description    : uiItemManageSetUnselectColor
* Input          : winHandle hitemManage,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetUnselectColor(winHandle hitemManage,uiColor color)
{
	uiItemManageObj* pitemManage;
	u32 i;
	uiResInfo infor;
	uiWinMsg msg;

	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage != INVALID_HANDLE)
	{
		uiResInforInit(&infor);
		infor.color	= color;
		msg.id		= MSG_WIN_UNSELECT_INFOR;
		msg.para.p	= (void*)&infor;
		pitemManage	= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		for(i = 0;i < pitemManage->itemSum;i++)
			uiWinSendMsg(pitemManage->itemHandle[i],&msg);
	}
}
/*******************************************************************************
* Function Name  : uiItemManageSetUnselectImage
* Description    : uiItemManageSetUnselectImage
* Input          : winHandle hitemManage,resID image
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetUnselectImage(winHandle hitemManage,resID image)
{
	uiItemManageObj* pitemManage;
	u32 i;
	uiResInfo infor;
	uiWinMsg msg;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage != INVALID_HANDLE)
	{
		uiResInforInit(&infor);
		infor.image	= image;
		msg.id 		= MSG_WIN_UNSELECT_INFOR;
		msg.para.p	= (void*)&infor;
		pitemManage	= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		for(i = 0;i < pitemManage->itemSum;i++)
			uiWinSendMsg(pitemManage->itemHandle[i],&msg);
	}
}
/*******************************************************************************
* Function Name  : uiItemManageGetTouchInfor
* Description    : uiItemManageGetTouchInfor
* Input          : winHandle hitemManage,touchInfor* infor
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageGetTouchInfor(winHandle hitemManage,touchInfor* infor)
{
	uiWinObj* pWin;
	uiItemManageObj* pitemManage;
	u32 itemResStart, itemResNum, i;

	pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
	for(i = 0;i < pitemManage->itemSum;i++)
	{
		pWin = (uiWinObj*)uiHandleToPtr(pitemManage->itemHandle[i]);
		if(uiWinInsideCmp(&(pWin->rect),&(infor->touchArea)) == 0)
			break;
	}
	itemResStart = (pitemManage->currentRes/pitemManage->itemSum)*pitemManage->itemSum;
	itemResNum	 = itemResStart + i;
	if(i >= pitemManage->itemSum || itemResNum >= pitemManage->resSum)
		return ;
	infor->touchWin		= pitemManage->widget.win.parent;
	infor->touchHandle	= hitemManage;
	infor->touchID		= pitemManage->widget.id;
	infor->touchItem	= itemResNum;
}
/*******************************************************************************
* Function Name  : uiItemManageSetSelectColorEx
* Description    : uiItemManageSetSelectColorEx
* Input          : winHandle hitemManage,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetSelectColorEx(winHandle hitemManage,charFont font,u8 strAlign, uiColor fontcolor, uiColor bgcolor)
{
	uiItemManageObj* pitemManage;
	u32 i;
	uiResInfo infor;
	uiWinMsg msg;
	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage!=INVALID_HANDLE)
	{
		uiResInforInit(&infor);
		infor.font = font;
		infor.strAlign = strAlign;
		infor.fontColor = fontcolor;
		infor.color     = bgcolor;
		msg.id		= MSG_WIN_SELECT_INFOR_EX;
		msg.para.p	= (void*)&infor;
		pitemManage = (uiItemManageObj*)uiHandleToPtr(hitemManage);
		for(i = 0;i < pitemManage->itemSum; i++)
			uiWinSendMsg(pitemManage->itemHandle[i],&msg);
	}
}
/*******************************************************************************
* Function Name  : uiItemManageSetUnselectColorEx
* Description    : uiItemManageSetUnselectColorEx
* Input          : winHandle hitemManage,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetUnselectColorEx(winHandle hitemManage,charFont font,u8 strAlign, uiColor fontcolor, uiColor bgcolor)
{
	uiItemManageObj* pitemManage;
	u32 i;
	uiResInfo infor;
	uiWinMsg msg;

	if(uiWidgetGetType(hitemManage) != WIDGET_ITEM_MANAGE)
		return ;
	if(hitemManage != INVALID_HANDLE)
	{
		uiResInforInit(&infor);
		infor.font = font;
		infor.strAlign = strAlign;
		infor.fontColor = fontcolor;
		infor.color     = bgcolor;
		msg.id		= MSG_WIN_UNSELECT_INFOR_EX;
		msg.para.p	= (void*)&infor;
		pitemManage	= (uiItemManageObj*)uiHandleToPtr(hitemManage);
		for(i = 0;i < pitemManage->itemSum;i++)
			uiWinSendMsg(pitemManage->itemHandle[i],&msg);
	}
}