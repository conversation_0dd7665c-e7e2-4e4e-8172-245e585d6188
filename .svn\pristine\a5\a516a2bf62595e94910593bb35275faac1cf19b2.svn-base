/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HAL_ADC_H
    #define  HAL_ADC_H


/*******************************************************************************
* Function Name  : hal_adcInit
* Description    : adc init
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_adcInit(void);
/*******************************************************************************
* Function Name  : hal_adcRead
* Description    : read adc value
* Input          : none
* Output         : None
* Return         : u16 : value
*******************************************************************************/
u16 hal_adcRead(void);
/*******************************************************************************
* Function Name  : hal_adcSetChannel
* Description    : change adc channel
* Input          : u8 ch : channel
* Output         : None
* Return         : u16 : value,last channel value
*******************************************************************************/
u16 hal_adcSetChannel(u8 ch);
/*******************************************************************************
* Function Name  : hal_adcGetChannel
* Description    : change adc channel & get value
* Input          : u8 ch : channel
* Output         : None
* Return         : u16 : value,cur channel value
*******************************************************************************/
u16 hal_adcGetChannel(u8 ch);
/*******************************************************************************
* Function Name  : boot_vddrtcCalculate
* Description    : Calculate vddrtc voltage
* Input          : u32 Artc : adc sample value of vddrtc
*                  u32 Abg  : adc sample value of bandgap
* Output         : none
* Return         : vddrtc voltage(unit : mV)
*******************************************************************************/
//u32 hal_adcVDDRTCCalculate(u32 Artc,u32 Abg);
#define hal_adcVDDRTCCalculate				boot_vddrtcCalculate






























#endif


