/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	ITEM_SELECT_ID=0,
	ITEM_MODE_ID,
	ITEM_HIDE_RECT_ID,
	ITEM_RESOLUTION_ID,
	ITEM_MD_ID,// motion detect
	ITEM_MONITOR_ID, // parking monitoring
	ITEM_IRLED_ID,
	ITEM_SD_ID,
	ITEM_MIC_ID,
	ITEM_BATERRY_ID,
	ITEM_POWERON_TIME_ID,
	ITEM_SYSTIME_ID,
};

UNUSED ALIGNED(4) const widgetCreateInfor menuItemWin[] =
{
#if UI_SHOW_SMALL_PANEL > 0
	createFrameWin(							Rx(0),	Ry(0), Rw(320),Rh(240),	R_ID_PALETTE_DarkGreen,0),
	createRect(ITEM_HIDE_RECT_ID,           Rx(0),	Ry(0), Rw(320),Rh(25),	R_ID_PALETTE_Black),
	createImageIcon(ITEM_MODE_ID,           Rx(0), 	Ry(0), Rw(24), Rh(25), 	R_ID_ICON_MTMENU, ALIGNMENT_LEFT),
	createStringIcon(ITEM_RESOLUTION_ID,	Rx(132),Ry(0), Rw(40), Rh(25),	" ", ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	createImageIcon(ITEM_MD_ID,             Rx(172),Ry(0), Rw(20), Rh(25), 	R_ID_ICON_MTMOTION,ALIGNMENT_CENTER),
	createImageIcon(ITEM_MONITOR_ID,     	Rx(192),Ry(0), Rw(22), Rh(25), 	R_ID_ICON_MTPARKOFF,ALIGNMENT_CENTER),
	createImageIcon(ITEM_IRLED_ID,          Rx(214),Ry(0), Rw(22), Rh(25), 	R_ID_ICON_MTIROFF,ALIGNMENT_CENTER),

	createImageIcon(ITEM_SD_ID,             Rx(258),Ry(0), Rw(22), Rh(25), 	R_ID_ICON_MTSDCNORMAL,ALIGNMENT_CENTER),
	createImageIcon(ITEM_MIC_ID,            Rx(280),Ry(0), Rw(20), Rh(25), 	R_ID_ICON_MTMICOFF,ALIGNMENT_CENTER),
	createImageIcon(ITEM_BATERRY_ID,       	Rx(300),Ry(0), Rw(20), Rh(25), 	R_ID_ICON_MTBATTERY3,ALIGNMENT_CENTER),
	//createStringIcon(ITEM_POWERON_TIME_ID,Rx(270),Ry(0), Rw(45), Rh(25),RAM_ID_MAKE(" "),ALIGNMENT_RIGHT, R_ID_PALETTE_White,DEFAULT_FONT),
	createItemManage(ITEM_SELECT_ID,        Rx(0),	Ry(25),Rw(320),Rh(215),	INVALID_COLOR),
#else
	createFrameWin(							Rx(0),	Ry(0), Rw(320),Rh(240),	R_ID_PALETTE_DarkGreen,0),
	createRect(ITEM_HIDE_RECT_ID,           Rx(0),	Ry(0), Rw(320),Rh(25),	R_ID_PALETTE_Black),
	createImageIcon(ITEM_MODE_ID,           Rx(5), 	Ry(0), Rw(20), Rh(25), 	R_ID_ICON_MTMENU, ALIGNMENT_LEFT),
//	createStringIcon(ITEM_RESOLUTION_ID,	Rx(100),Ry(0), Rw(30), Rh(25),	" ", ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
//	createImageIcon(ITEM_MD_ID,             Rx(130),Ry(0), Rw(20), Rh(25), 	R_ID_ICON_MTMOTION, ALIGNMENT_CENTER),
//	createImageIcon(ITEM_MONITOR_ID,     	Rx(150),Ry(0), Rw(20), Rh(25), 	R_ID_ICON_MTPARKOFF,ALIGNMENT_CENTER),
//	createImageIcon(ITEM_IRLED_ID,          Rx(170),Ry(0), Rw(20), Rh(25), 	R_ID_ICON_MTIROFF,	ALIGNMENT_CENTER),
//
//	createImageIcon(ITEM_SD_ID,             Rx(210),Ry(0), Rw(20), Rh(25), 	R_ID_ICON_MTSDCNORMAL,ALIGNMENT_CENTER),
//	createImageIcon(ITEM_MIC_ID,            Rx(230),Ry(0), Rw(20), Rh(25), 	R_ID_ICON_MTMICOFF,	ALIGNMENT_CENTER),
//	createImageIcon(ITEM_BATERRY_ID,       	Rx(250),Ry(0), Rw(20), Rh(25), 	R_ID_ICON_MTBATTERY3,ALIGNMENT_CENTER),
//	createStringIcon(ITEM_POWERON_TIME_ID,	Rx(270),Ry(0), Rw(45), Rh(25),	" ", ALIGNMENT_RIGHT, R_ID_PALETTE_White,DEFAULT_FONT),
	createItemManage(ITEM_SELECT_ID,        Rx(0),Ry(25), Rw(320),Rh(215),	INVALID_COLOR),
#endif
	widgetEnd(),
};

/*******************************************************************************
* Function Name  : menuResolutionShow
* Description    : menuResolutionShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void menuResolutionShow(winHandle handle)
{
	switch(user_config_get(CONFIG_ID_RESOLUTION))
	{
		case R_ID_STR_RES_HD:  uiWinSetResid(winItem(handle,ITEM_RESOLUTION_ID),RAM_ID_MAKE("HD")); break;
		case R_ID_STR_RES_FHD: uiWinSetResid(winItem(handle,ITEM_RESOLUTION_ID),RAM_ID_MAKE("FHD")); break;
		case R_ID_STR_RES_VGA: uiWinSetResid(winItem(handle,ITEM_RESOLUTION_ID),RAM_ID_MAKE("VGA")); break;
		case R_ID_STR_RES_QVGA: uiWinSetResid(winItem(handle,ITEM_RESOLUTION_ID),RAM_ID_MAKE("QVGA")); break;
		default:			   uiWinSetResid(winItem(handle,ITEM_RESOLUTION_ID),RAM_ID_MAKE("???")); break;
	}
}
/*******************************************************************************
* Function Name  : menuMDShow
* Description    : menuMDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void menuMDShow(winHandle handle)
{
	if(user_config_get(CONFIG_ID_MOTIONDECTION)==R_ID_STR_COM_ON)
	{
		uiWinSetVisible(winItem(handle,ITEM_MD_ID),1);
		uiWinSetResid(winItem(handle,ITEM_MD_ID),R_ID_ICON_MTMOTION);
	}
	else
		uiWinSetVisible(winItem(handle,ITEM_MD_ID),0);
}
/*******************************************************************************
* Function Name  : menuMonitorShow
* Description    : menuMonitorShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void menuMonitorShow(winHandle handle)
{
	if(user_config_get(CONFIG_ID_PARKMODE)==R_ID_STR_COM_ON)
		uiWinSetResid(winItem(handle,ITEM_MONITOR_ID),R_ID_ICON_MTPARKON);
	else
		uiWinSetResid(winItem(handle,ITEM_MONITOR_ID),R_ID_ICON_MTPARKOFF);
}
/*******************************************************************************
* Function Name  : menuIrLedShow
* Description    : menuIrLedShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void menuIrLedShow(winHandle handle)
{
	if(hardware_setup.ir_led_en)
	{
		if(user_config_get(CONFIG_ID_IR_LED)==R_ID_STR_COM_OFF)
			uiWinSetResid(winItem(handle,ITEM_IRLED_ID),R_ID_ICON_MTIROFF);
		else
			uiWinSetResid(winItem(handle,ITEM_IRLED_ID),R_ID_ICON_MTIRON);
	}else
	{
		uiWinSetVisible(winItem(handle,ITEM_IRLED_ID),0);
	}
}
/*******************************************************************************
* Function Name  : menuSDShow
* Description    : menuSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void menuSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		uiWinSetResid(winItem(handle,ITEM_SD_ID),R_ID_ICON_MTSDCNORMAL);
	else
		uiWinSetResid(winItem(handle,ITEM_SD_ID),R_ID_ICON_MTSDCNULL);

}
/*******************************************************************************
* Function Name  : menuMicShow
* Description    : menuMicShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void menuMicShow(winHandle handle)
{
	if(user_config_get(CONFIG_ID_AUDIOREC)==R_ID_STR_COM_OFF)
		uiWinSetResid(winItem(handle,ITEM_MIC_ID),R_ID_ICON_MTMICOFF);
	else
		uiWinSetResid(winItem(handle,ITEM_MIC_ID),R_ID_ICON_MTMICON);
}
/*******************************************************************************
* Function Name  : menuBaterryShow
* Description    : menuBaterryShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void menuBaterryShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERY5;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4:
			//case BATTERY_STAT_5:
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetVisible(winItem(handle,ITEM_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,ITEM_BATERRY_ID),batid);
}
/*******************************************************************************
* Function Name  : menuPoweOnTimeShow
* Description    : menuPoweOnTimeShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
#if UI_SHOW_SMALL_PANEL == 0
UNUSED static void menuPoweOnTimeShow(winHandle handle,u32 sec)
{
	static char powerOnTimeStr[] = "00:00";
	powerOnTimeStr[0] = sec/36000+'0';  // h
	powerOnTimeStr[1] = ((sec/3600)%10)+'0';   // h
	powerOnTimeStr[2] = ':';
	sec				  = sec % 3600;
	powerOnTimeStr[3] = sec/600+'0';  // m
	powerOnTimeStr[4] = ((sec/60)%10)+'0';   // m
	powerOnTimeStr[5] = 0;
	uiWinSetResid(winItem(handle,ITEM_POWERON_TIME_ID),RAM_ID_MAKE(powerOnTimeStr));
}
#endif
