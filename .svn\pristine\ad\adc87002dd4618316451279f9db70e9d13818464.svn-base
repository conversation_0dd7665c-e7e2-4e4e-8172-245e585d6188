/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

ALIGNED(4) u32 task_play_audio_stat;
/*******************************************************************************
* Function Name  : app_taskPlayAudio_start
* Description    : APP LAYER: app_taskPlayAudio_start
* Input          : int idx
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
int app_taskPlayAudio_start(int idx)
{
	WAV_PARA_T arg;
	char *name;
	if(audioPlaybackGetStatus() != MEDIA_STAT_STOP)
	{
		audioPlaybackStop();
	}
	name = filelist_GetFileFullNameByIndex(SysCtrl.wav_list, idx, NULL);
	if(name == NULL)
		return -1;
	arg.fd = (int)fs_open(name,FA_READ);
	if(arg.fd < 0)
		return -1;
	deg_Printf("name:%s\n", name);
	arg.ch_out		= 1;
	arg.type 		= MEDIA_WAV;
	arg.src_type 	= MEDIA_SRC_FS;
	arg.volume		= FUN_KEYSOUND_VOLUME;
	//if(audioPlaybackInit() != STATUS_OK)
	//	return -1;
	if(audioPlaybackStart(&arg) != STATUS_OK)
	{
		deg_Printf("audio task : play fail\n");
		return -1;
		
	}
	audioPlaybackFirstPause();
	audioPlaybackGetTime(&SysCtrl.play_total_time,&SysCtrl.play_cur_time);
	SysCtrl.play_last_time = SysCtrl.play_cur_time;
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
	deg_Printf("audio task : play ok.total time = %dm %ds %dms\n",(SysCtrl.play_total_time/1000)/60,(SysCtrl.play_total_time/1000)%60,SysCtrl.play_total_time%1000);	
	task_play_audio_stat = MEDIA_STAT_START;
	return 0;
}
/*******************************************************************************
* Function Name  : app_taskPlayAudioOpen
* Description    : APP LAYER: app_taskPlayAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskPlayAudioOpen(u32 arg)
{
	audioPlaybackInit();
	SysCtrl.file_index = 0;
	SysCtrl.play_total_time = 0;
	SysCtrl.play_cur_time   = 0;
	SysCtrl.play_last_time  = 0;
	task_play_audio_stat 	= MEDIA_STAT_STOP;
	uiOpenWindow(&playAudioWindow, 0, 0);
	
}
/*******************************************************************************
* Function Name  : app_taskPlayAudioOpen
* Description    : APP LAYER: app_taskPlayAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskPlayAudioClose(u32 arg)
{
	if(audioPlaybackGetStatus() != MEDIA_STAT_STOP)
		audioPlaybackStop();	
    audioPlaybackUninit();
	task_play_audio_stat = MEDIA_STAT_STOP;
	deg_Printf("audio play task exit\n");
}
/*******************************************************************************
* Function Name  : app_taskPlayAudioOpen
* Description    : APP LAYER: app_taskPlayAudioOpen
* Input          : u32 arg
* Output         : none
* Return         : none
*******************************************************************************/
static void app_taskPlayAudioService(u32 arg)
{
	if(task_play_audio_stat == MEDIA_STAT_START)
	{
		audioPlaybackService();
		audioPlaybackGetTime(NULL,&SysCtrl.play_cur_time);

		if(audioPlaybackGetStatus() == MEDIA_STAT_STOP)
		{
			task_play_audio_stat = MEDIA_STAT_STOP;
			SysCtrl.play_last_time = SysCtrl.play_cur_time;
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
		}else if((SysCtrl.play_last_time/1000 != SysCtrl.play_cur_time/1000)&&(SysCtrl.play_cur_time!=0))
		{
			SysCtrl.play_last_time = SysCtrl.play_cur_time;
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
		}
	
	}
	
}

ALIGNED(4) sysTask_T taskPlayAudio =
{
	"Play Audio",
	0,
	app_taskPlayAudioOpen,
	app_taskPlayAudioClose,
	app_taskPlayAudioService,
};



