/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

ALIGNED(4) static heapHead_T * uiStartHead;

/*******************************************************************************
* Function Name  : uiWinHeapInit
* Description    : uiWinHeapInit
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinHeapInit(u32 addr, u32 size)
{
	uiStartHead = (heapHead_T*)addr;
	uiStartHead->size = (size&HEAD_SIZE_MASK);
	uiStartHead->next = NULL;
	//uiWinHeapMemInfo();
}
/*******************************************************************************
* Function Name  : uiWinHeapMemInfo
* Description    : uiWinHeapMemInfo
* Input          : none
* Output         : none                                            
* Return         : winHandle
*******************************************************************************/
void uiWinHeapMemInfo(void)
{
	heapHead_T* head = uiStartHead;
	deg_Printf("[uiWinHeapMemInfo]\n");
	while(head)
	{
		deg_Printf("[UI HEAP][%d]%x,%x\n",(bool)(head->size&HEAD_FLAG_USE), head->size &HEAD_SIZE_MASK,head->next );
		head = head->next;
	}
}
/*******************************************************************************
* Function Name  : uiWinHeapMalloc
* Description    : uiWinHeapMalloc
* Input          : none
* Output         : none                                            
* Return         : winHandle
*******************************************************************************/
winHandle uiWinHeapMalloc(u32 size)
{
	heapHead_T* head = uiStartHead;
	heapHead_T* nexthead;
	u32 head_size;
	if(size == 0)
		return INVALID_HANDLE;
	size = (size + HEAP_HEAD_SIZE + 3) & ~3;
	
	while(head)
	{
		if(!(head->size & HEAD_FLAG_USE) && ((head->size & HEAD_SIZE_MASK) >= size) )
		{
			nexthead  = head->next;
			head_size = head->size;
			
			if(head_size - size >= HEAP_HEAD_SIZE)
			{
				head->size = size | HEAD_FLAG_USE;
				head->next = (heapHead_T*)((u32)head + size);
				head->next->next = nexthead;
				head->next->size = head_size - size;
			}else
			{
				head->size |= HEAD_FLAG_USE;
				size = head_size;
			}
			//deg_Printf("[UI HEAP MALLOC] %x %x\n", (u32)head, size);
			memset((void*)((u32)head + HEAP_HEAD_SIZE),0,size - HEAP_HEAD_SIZE);
			return (winHandle)((u32)head + HEAP_HEAD_SIZE); 
		}
		head = head->next;
	}
	//uiWinHeapMemInfo();
	return INVALID_HANDLE;
}
/*******************************************************************************
* Function Name  : uiWinHeapFree
* Description    : uiWinHeapFree
* Input          : none
* Output         : none                                            
* Return         : winHandle
*******************************************************************************/
void uiWinHeapFree(winHandle handle)
{
	heapHead_T* head = uiStartHead;
	heapHead_T* freehead;
	if(handle == INVALID_HANDLE)
		return;
	freehead = (heapHead_T*)((u32)handle - HEAP_HEAD_SIZE); 
	freehead->size &=~HEAD_FLAG_USE;
	//deg_Printf("[UI HEAP Free] %x %x\n", (u32)freehead, freehead->size);
	//处理与后面的head，//如果下一个也是freehead，那么合并
	if((freehead->next) && !(freehead->next->size & HEAD_FLAG_USE)) 
	{
		freehead->size += freehead->next->size;
		freehead->next  = freehead->next->next;
	}
	//处理与前面的head //如果前一个也是freehead，那么合并
	while(head)
	{
		if(head->next == freehead)
		{
			if(!(head->size & HEAD_FLAG_USE))
			{
				head->size += freehead->size;
				head->next = freehead->next;
			}
			
			break;
		}
		head = head->next;
	}
	//uiWinHeapMemInfo();
}
/*******************************************************************************
* Function Name  : uiWinHeapFree
* Description    : uiWinHeapFree
* Input          : none
* Output         : none                                            
* Return         : winHandle
*******************************************************************************/
u32 uiMemPoolCreate(memPool_Ctl *pool,memPool_T* pool_start,u32 pool_size,u32 blkSize)
{
	memPool_T* pool_next;
	memPool_T* pool_end;
	u32 blkCnt;
	if(blkSize < sizeof(memPool_T))
		blkSize = sizeof(memPool_T);
	if(blkSize > pool_size)
	{
		pool->cur_mem = (memPool_T*)NULL;
		return 0;
	}
	pool->cur_mem 	= pool_start;
	pool_end 		= (memPool_T*)((u32)pool_start + pool_size);
	blkCnt = 0;
	while(1)
	{
		pool_next 		 = (memPool_T*)((u32)pool_start + blkSize);
		if(pool_next >= pool_end)
		{
			pool_start->next = (memPool_T*)NULL;
			break;
		}
		pool_start->next = pool_next;
		pool_start		 = pool_next;
		blkCnt++;
	}
	pool->blkSize 	= blkSize;
	pool->freeBlks  = blkCnt;
	pool->maxBlks	= blkCnt;
	return blkCnt;
}
/*******************************************************************************
* Function Name  : uiMemPoolGet
* Description    : uiMemPoolGet
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void* uiMemPoolGet(memPool_Ctl* pool)
{
	memPool_T* pool_get;
	if(pool->cur_mem == (memPool_T*)NULL || pool->freeBlks == 0)
	{
		return (void*)NULL;
	}
	pool_get =  pool->cur_mem;
	pool->cur_mem = pool->cur_mem->next;
	pool->freeBlks--;
	return (void*)pool_get;
}
/*******************************************************************************
* Function Name  : uiMemPoolPut
* Description    : uiMemPoolPut
* Input          : none
* Output         : none                                            
* Return         : u32 0 : pool full, >0: pool free blks
*******************************************************************************/
u32 uiMemPoolPut(memPool_Ctl* pool,void* ptr)
{	
	memPool_T* pool_put;
	if(pool->freeBlks >= pool->maxBlks)
	{
		return 0;
	}
	pool_put = (memPool_T*)ptr;
	pool_put->next 	= pool->cur_mem;
	pool->cur_mem	= pool_put;
	pool->freeBlks++;
	return pool->freeBlks;
}
/*******************************************************************************
* Function Name  : uiMemPoolGet
* Description    : uiMemPoolGet
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiMemPoolInfo(memPool_Ctl* pool)
{
	deg_Printf("[UI MEMPOOL] blksize: %d, freeblks:%d, maxblks:%d\n", pool->blkSize, pool->freeBlks, pool->maxBlks);
}