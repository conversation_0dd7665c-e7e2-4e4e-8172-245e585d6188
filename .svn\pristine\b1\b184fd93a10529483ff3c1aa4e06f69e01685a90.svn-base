/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  XDEF_H
    #define  XDEF_H

//------------------x os CPU config--------------------------------------
#define X_CRITIAL_ENTER()   


#define X_CRITIAL_EXIT()



typedef INT32U  MSG_T;

typedef struct X_MSG_S
{
    INT8U stat;
    INT8U len;    
    INT8U oput;
    INT8U iput;
    INT32U *msg;
}XMbox,XMsgQ,XMsg;

typedef enum{
	X_ERR_NONE		= 0,
	X_ERR_QFULL		= -1,
	X_ERR_QEMPTY	= -2,
	X_ERR_TIMEOUT	= -3,
}X_ERR_TYPE;



#define makeMSG(type,value)  (MSG_T *)((type)|((value)<<16))
#define getType(msg)        (((uint32)msg)&0xffff)
#define getValue(msg)        ((((uint32)msg)>>16)&0xffff)

































#endif


