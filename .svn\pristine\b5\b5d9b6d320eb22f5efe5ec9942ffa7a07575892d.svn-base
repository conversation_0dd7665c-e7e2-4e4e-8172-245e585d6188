/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

typedef struct LCD_DEV_S{
    lcddev_t        dev_opt;
}LCD_DEV_T;

static LCD_DEV_T lcd_dev_api;

/*******************************************************************************
* Function Name  : lcd_initTab_config
* Description    : lcd inittab config
* Input          : None
* Output         : None
* Return         : none
*******************************************************************************/
void lcd_initTab_config(u32 *tab)
{
    if(tab == NULL)
        return;
    if(lcd_dev_api.dev_opt.lcd_bus_type == LCD_BUS_RGB)
    {
        hx330x_lcdSPIMode(lcd_dev_api.dev_opt.spi_cpol,lcd_dev_api.dev_opt.spi_cpha,lcd_dev_api.dev_opt.spi_order,lcd_dev_api.dev_opt.spi_bits);
        hx330x_lcdSPIInit();
    }
    while (1)
    {
        u32 tab_val = *tab++;
        if (tab_val == LCD_CMD_TAB_END)
            break;

        u32 type = tab_val & LCD_CMD_TYPE_MASK;
        u32 val = tab_val & LCD_CMD_VALUE_MASK;

        if (type == LCD_CMD_TYPE_RGB_DAT)
            hx330x_lcdSPISendData(val);
        else if (type == LCD_CMD_TYPE_DELAY_MS)
            hx330x_sysCpuMsDelay(val);
        else if (type == LCD_CMD_TYPE_MCU_CMD8)
            hx330x_lcdMcuSendCmd(val);
        else if (type == LCD_CMD_TYPE_MCU_DAT8)
            hx330x_lcdMcuSendData(val);
        else if (type == LCD_CMD_TYPE_MCU_CMD16)
            hx330x_lcdMcuSendCmd16(val);
        else if (type == LCD_CMD_TYPE_MCU_DAT16)
            hx330x_lcdMcuSendData16(val);
    }
    if(lcd_dev_api.dev_opt.lcd_bus_type == LCD_BUS_RGB)
    {
        hx330x_lcdSPIUninit();
    }
}

/*******************************************************************************
* Function Name  : lcdInit
* Description    : lcd initial
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
static void lcdInit(void)
{
    hal_lcdParaLoad(&lcd_dev_api.dev_opt);

    if (hal_lcdRegister(&lcd_dev_api.dev_opt) < 0)
    {
        deg_Printf("ERR : fail.");
    }
    else
    {
        deg_Printf("lcd = %s.",lcd_dev_api.dev_opt.name);
    }
}
/*******************************************************************************
* Function Name  : LcdGetName
* Description    : get lcd name
* Input          :
* Output         : none
* Return         : char *
*******************************************************************************/
const char *LcdGetName(void)
{
    return lcd_dev_api.dev_opt.name;
}
/*******************************************************************************
* Function Name  : dev_ir_init
* Description    : dev_ir_init
* Input          : NONE
* Output         : none
* Return         : none
*******************************************************************************/
int dev_lcd_init(void)
{
    if (LCD_TAG_SELECT != LCD_NONE)
    {
    #if (CURRENT_CHIP == FPGA)
        //LCD RESET, FPGA需要一个IO口来RESET LCD
        hx330x_gpioMapSet(GPIO_PF,GPIO_PIN14,GPIO_FUNC_GPIO);
        hx330x_gpioDirSet(GPIO_PF,GPIO_PIN14,GPIO_OUTPUT);
        hx330x_gpioDataSet(GPIO_PF,GPIO_PIN14,GPIO_LOW);
        hx330x_sysCpuMsDelay(50);
        hx330x_gpioDataSet(GPIO_PF,GPIO_PIN14,GPIO_HIGH);
        hx330x_sysCpuMsDelay(10);
    #endif
        //LCD 背光IO口初始化
        hal_gpioInit(hardware_setup.lcd_backlight_ch, hardware_setup.lcd_backlight_pin ,GPIO_OUTPUT,GPIO_PULL_UP);
        hal_gpioEPullSet(hardware_setup.lcd_backlight_ch, hardware_setup.lcd_backlight_pin,GPIO_PULLE_DOWN);
        hal_gpioWrite(hardware_setup.lcd_backlight_ch, hardware_setup.lcd_backlight_pin,GPIO_LOW);

        hx330x_lcdReset();

        lcdInit();
        return 0;
    }
    else
    {
        return -1;
    }


}

/*******************************************************************************
* Function Name  : dev_lcd_ioctrl
* Description    : dev_lcd_ioctrl
* Input          : NONE
* Output         : none
* Return         : none
*******************************************************************************/
static u32 dev_lcd_nocolor = 0;
u8 lcd_saj_nocolor[5] = {0x00,0x00, 0x00, 0x00, 0x00};
int dev_lcd_ioctrl(u32 op, u32 para)
{
    if (LCD_TAG_SELECT != LCD_NONE)
    {
        static u32 dev_lcd_bk_state = 0;
        if (op == DEV_LCD_BK_READ)
        {
            if (para)
                *(u32*)para = dev_lcd_bk_state;
        }
        else if (op == DEV_LCD_BK_WRITE)
        {
            dev_lcd_bk_state = para;
    #if (CURRENT_CHIP != FPGA)
            if (para)
            {
                hal_gpioEPullSet(hardware_setup.lcd_backlight_ch, hardware_setup.lcd_backlight_pin, GPIO_PULLE_UP);
                hal_gpioWrite(hardware_setup.lcd_backlight_ch, hardware_setup.lcd_backlight_pin, GPIO_HIGH);
            }
            else
            {
                hal_gpioEPullSet(hardware_setup.lcd_backlight_ch, hardware_setup.lcd_backlight_pin, GPIO_PULLE_DOWN);
                hal_gpioWrite(hardware_setup.lcd_backlight_ch, hardware_setup.lcd_backlight_pin, GPIO_LOW);
            }
    #endif
        }
        else if (op == DEV_LCD_OFF_WRITE)
        {
            hal_lcdLCMPowerOff();
        }else if(op == DEV_LCD_NOCOLOR_CHANGE)
        {
            dev_lcd_nocolor ^= 1;

            if(dev_lcd_nocolor) //no color
            {
                hx330x_lcdVideo_SAJ_cfg(&lcd_saj_nocolor);
            }else{ //normal color
                hx330x_lcdVideo_SAJ_cfg(&lcd_dev_api.dev_opt.lcd_saj);

            }
        }
    }
    return 0;
}
/*******************************************************************************
* Function Name  : dev_lcd_nocolor_status
* Description    : dev_lcd_nocolor_status
* Input          : NONE
* Output         : none
* Return         : 1: nocolor, 0: normal
*******************************************************************************/
u32 dev_lcd_nocolor_status(void)
{
    return dev_lcd_nocolor;
}
