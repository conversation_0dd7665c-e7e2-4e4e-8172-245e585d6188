/******************** (C) COPYRIGHT 2022 HX ************************
* File Name          : user_hardware_cfg_hx3308_demo
* Author             : WNJ
* Version            : v1
* Date               : 08/01/2022
* Description        : 用户需要根据自己的板子所使用的硬件接口来配置该文件
***************************************************************************/
#ifndef  USER_HARDWARE_CFG_HX3308_DEMO_H
    #define  USER_HARDWARE_CFG_HX3308_DEMO_H


/********************************************************************************************
*晶振使用情况
*1. 支持 晶振在线自动检测
* USE_RC：  不使用晶振
* USE_XOSC: 使用晶振
* AUTO_CHECK_XOSC ： 自动检测晶振
**********************************************************************************************/
#define USER_USE_XOSC						AUTO_CHECK_XOSC

/********************************************************************************************
* uart串口相关, 如果 定义宏 HAL_CFG_EN_DBG  为 0, 这里可以不配置
*1. 确认 uart 波特率：一般为115200
*2. 确认 uart tx pos：即发送给电脑用的IO口, UART0_POS_NONE代表不使用
*3. 如果使用uart rx， 需要再确认uart rx pos,  UART0_POS_NONE代表不使用
**********************************************************************************************/
#define USER_UART_BAUDRATE					115200
#define USER_UART_TX_POS					UART0_POS_PE1
#define USER_UART_RX_POS					UART0_POS_NONE
/********************************************************************************************
* dma uart串口相关
*1. 确认 uart 波特率：一般为115200
*2. 确认 uart tx pos：即发送给电脑用的IO口, UART0_POS_NONE代表不使用
*3. 如果使用uart rx， 需要再确认uart rx pos,  UART0_POS_NONE代表不使用
**********************************************************************************************/
#define USER_DMAUART_BAUDRATE				115200
#define USER_DMAUART_TX_POS					UART1_POS_NONE//UART1_POS_PF0
#define USER_DMAUART_RX_POS					UART1_POS_NONE//UART1_POS_PF1
/********************************************************************************************
* LED 指示灯 , 复用 UART TX
*1. 确认是否使用LED指示灯： led_en = 1， 使用； = 0， 不使用
*2. 确认LED灯极性： led_valid： 0：IO口拉低亮灯， 1： IO口拉高亮灯
*3. 确认LED灯使用的IO口：
**********************************************************************************************/
#define USER_USE_LED						1 //0:不使用LED(LED其他参数可不管) ;  1: 使用LED
#define USER_LED_VALID						1 //0 - 低电平点亮， 1 - 高电平点亮
#define USER_LED_CH							GPIO_PE
#define USER_LED_PIN						GPIO_PIN1
/********************************************************************************************
*LCD屏相关接口 LCD_RGB_ST7701S_LX50FWH40149
*1. 如果不使用LCD屏， 将 LCD_TAG_SELECT 配置为 LCD_NONE
*1. 确认LCD POS：即LCD使用了哪一组的IO mapping
*                可通过查看pin_func表格或该文件末尾定义
*2. 确认LCD 背光：定义LCD背光使用的IO口
*3. 如果是RGB屏： 定义SPI接口的CS，CLK， DAT使用的IO口. CLK和DAT可复用LCD的DATA脚
*4. 如果是MCU屏： 定义MCU 的RS使用的IO口
**********************************************************************************************/
#define		LCD_MAP_POS						LCD_POS9
#define		LCD_BACKLIGHT_CH				GPIO_PA
#define 	LCD_BACKLIGHT_PIN				GPIO_PIN9
//RGB屏使用的SPI接口定义
#define 	LCD_SPI_CS_CH					GPIO_PA
#define 	LCD_SPI_CS_PIN					GPIO_PIN5
#define 	LCD_SPI_CLK_CH					GPIO_PD
#define		LCD_SPI_CLK_PIN					GPIO_PIN8
#define		LCD_SPI_DAT_CH					GPIO_PD
#define		LCD_SPI_DAT_PIN					GPIO_PIN7
//MCU屏使用的RS 接口定义
#define		LCD_MCU_RS_CH					GPIO_PF
#define 	LCD_MCU_RS_PIN					GPIO_PIN8
/********************************************************************************************
*CMOS-SENSOR相关接口
*1. 如果不使用CMOS-SENSOR， 将 USER_USE_CMOS_SENSOR 配置为0，将会呈现COLORBAR
*1. 确认CMOS SENSOR POS：   即 CMOS SENSOR DATA/MCLK/PCLK/HSYNC/VSYNC等使用了哪一组的IO mapping
*                           可通过查看pin_func表格或该文件末尾定义，注意CSI_POS2~CSI_POS4复用MIPI的IO口
*2. 确认IIC POS：定义SENSOR使用的IIC接口使用的IO MAP
********************************************************************************************/
#define     USER_USE_CMOS_SENSOR			1		//1：带CMOS-SENSOR， 0：不带 CMOS-SENSOR, 将显示COLOR-BAR
#define		CMOS_SENSOR_POS					CSI_POS2
#define 	CMOS_SENSOR_IIC_POS				IIC0_POS_PE13PE15
#define     CMOS_SENSOR_IIC_BAUDRATE		400000   // 400k
//CMOS_SENSOR_IIC_POS定义为 IIC0_POS_MAX 时，由软件指定IO口，配置以下参数
#define		CMOS_SENSOR_IIC_SOFT_DELAY		6
#define		CMOS_SENSOR_IIC_SOFT_SCL_CH		GPIO_PE
#define		CMOS_SENSOR_IIC_SOFT_SCL_PIN	GPIO_PIN13
#define		CMOS_SENSOR_IIC_SOFT_SDA_CH		GPIO_PE
#define		CMOS_SENSOR_IIC_SOFT_SDA_PIN	GPIO_PIN15

/********************************************************************************************
* AD-KEY 按键相关定义，
*1. 如果不使用 AD-KEY， 将 USER_USE_ADKEY 配置为0，将会呈现COLORBAR
*1. 确认CMOS SENSOR POS：   即 CMOS SENSOR DATA/MCLK/PCLK/HSYNC/VSYNC等使用了哪一组的IO mapping
*                           可通过查看pin_func表格或该文件末尾定义，注意CSI_POS2~CSI_POS4复用MIPI的IO口
*2. 确认IIC POS：定义SENSOR使用的IIC接口使用的IO MAP
*******************************************************************************************/
#define 	USER_USE_ADKEY					1		//1：有按键 ， 0：没有按键
#define 	ADKEY_POS						ADC_CH_PA15
#define     ADKEY_CH						GPIO_PA
#define     ADKEY_PIN						GPIO_PIN15
#define		POWERKEY_USE_ADC				0		//1: 与 ADKEY 使用同一个IO口, 0: 不与 ADKEY使用同一个IO口
//POWERKEY_USE_ADC 为 0时，需要指定 POWERKEY使用的IO口：  POWERKEY_CH， POWERKEY_PIN
#define		POWERKEY_VALID					1		//0：按下时拉低， 1： 按下时拉高
#define		POWERKEY_CH						GPIO_PA
#define		POWERKEY_PIN					GPIO_PIN14

#define		ADKEY_NUM						5		//5个按键 ( POWERKEY_USE_ADC = 0时不包含POWER KEY)
#define 	ADKEY_USE_INNER_PULLUP_10K		0		//如果外部没有上拉电阻时，将这里配置为1
#define     ADKEY_OUT_PULLUP_RESISTANCE		(100*1000)	//外部使用的上拉电阻阻值，单位(欧)
#define     ADKEY_VALUE_RANGE				(40)	//键值范围，如果电阻太接近，需要减小这个值

//根据板子上按键的电阻和 按键对应的功能定义以下参数，如果长按要转换为其他按键，需定义长按时间 ADKEY_x_LONGKEYTIME和对应的 ADKEY_x_LONGTYPE
//按键1
#define     ADKEY_1_RESITANCE				0				//按键使用的电阻阻值，单位(欧)
#define     ADKEY_1_TYPE					KEY_EVENT_OK	//按键对应的功能
#define		ADKEY_1_LONGTYPE				KEY_EVENT_OK	//LONGKEYTIME不为 0 时， 按键长按对应的功能
#define		ADKEY_1_LONGKEYTIME				0				//长按时间，单位30ms
//按键2
#define     ADKEY_2_RESITANCE				(43*1000)			//按键使用的电阻阻值，单位(欧)
#define     ADKEY_2_TYPE					KEY_EVENT_DOWN	//按键对应的功能
#define		ADKEY_2_LONGTYPE				KEY_EVENT_UVC_FORM	//LONGKEYTIME不为 0 时， 按键长按对应的功能
#define		ADKEY_2_LONGKEYTIME				10				//长按时间，单位30ms
//按键3
#define     ADKEY_3_RESITANCE				(91*1000)			//按键使用的电阻阻值，单位(欧)
#define     ADKEY_3_TYPE					KEY_EVENT_UP	//按键对应的功能
#define		ADKEY_3_LONGTYPE				KEY_EVENT_UVC_FRAME	//LONGKEYTIME不为 0 时， 按键长按对应的功能
#define		ADKEY_3_LONGKEYTIME				10				//长按时间，单位30ms
//按键4
#define     ADKEY_4_RESITANCE				(180*1000)			//按键使用的电阻阻值，单位(欧)
#define     ADKEY_4_TYPE					KEY_EVENT_MENU	//按键对应的功能
#define		ADKEY_4_LONGTYPE				KEY_EVENT_ROTATE_ADD	//LONGKEYTIME不为 0 时， 按键长按对应的功能
#define		ADKEY_4_LONGKEYTIME				10				//长按时间，单位30ms
//按键5
#define     ADKEY_5_RESITANCE				(390*1000)			//按键使用的电阻阻值，单位(欧)
#define     ADKEY_5_TYPE					KEY_EVENT_MODE	//按键对应的功能
#define		ADKEY_5_LONGTYPE				KEY_EVENT_ROTATE_DEC	//LONGKEYTIME不为 0 时， 按键长按对应的功能
#define		ADKEY_5_LONGKEYTIME				10				//长按时间，单位30ms
/********************************************************************************************
* SD-CARD 卡相关定义，
*1. 如果不使用 SD-CARD ， 将 USER_USE_SDCARD 配置为 0
*2. 确认SD 卡 IO使用的MAP:
*3. 确认SD 卡使用单线还是4线
*******************************************************************************************/
#define 	USER_USE_SDCARD					1		//1：支持SD卡， 0：不支持SD卡
#define		SDCARD_POS						SD0_POS0
#define		SDCARD_BUS_WIDTH				SD_BUS_WIDTH4
/********************************************************************************************
* IR-LED 相关定义，
*1. 如果不使用 IR-LED ， 将 USER_USE_IR 配置为 0
*2. 确认 IR使用的IO口
*******************************************************************************************/
#define		USER_USE_IR						0
#define 	IR_LED_CH						GPIO_PA
#define 	IR_LED_PIN						GPIO_PIN9

/********************************************************************************************
* USB20 DEV 相关定义
*1. 如果不使用 USB20_DEV连接 PC ， 将 USER_USE_USB_DEV 配置为 0，则USB20 DEV从只做供电
*******************************************************************************************/
#define		USER_USE_USB_DEV				1
/********************************************************************************************
* USB HOST 相关定义 : 支持 USB20 或 USB11 作为主机接设备（例如后拉）
*1. 如果不使用 USB_HOST 连接 设备 ， 将 USER_USE_USB_HOST 配置为 0
*2. 如果使用 USB20 作为HOST，同时 USB20_DEV使用，需要定义 USB_HOST_DOUBLE_BOND为 1
*3. 确认 USB HOST使用 USB1.1 还是 USB2.0, 如果自动检测，则定义为 USBAUTO_CH
*4. 确认 设备 供电 方式， USB_HOST_POWER_IO
*(1) 配置 IO1_CH_NONE ： 表示外围硬件供电常开，没有控制电源开关的IO口
*(2) 配置 IO1_CH_PA7 或 IO1_CH_PD1: 表示使用IO控制电源开关
*5. 确认 设备 插入检测 方式， USB_HOST_DECTCTRL
*(1) 配置 0 ： 不使用硬件DECT IO口，仅由软件判断
*(2) 配置 1 : 使用硬件DECT IO口， 需要定义 USB_HOST_DET_CH 和 USB_HOST_DET_PIN
*******************************************************************************************/
#define		USER_USE_USB_HOST				1
#define		USB_HOST_DOUBLE_BOND			1//0		//USB20: USB DEV and USB HOST use same DPDM
#define		USB_HOST_CH						USB20_CH//USB11_CH//USB20_CH, USB11_CH, AUTO_CH
#define		USB_HOST_POWER_IO				IO1_CH_NONE	//IO1_CH_NONE: POWER常开， IO1_CH_PD1,IO1_CH_PA7：通过这两个IO口控制
#define     USB_HOST_DECTCTRL               0       //1: 需要硬件DECT,    0:  使用软件DECT
#define		USB_HOST_DET_CH					GPIO_PA
#define		USB_HOST_DET_PIN				GPIO_PIN6

/********************************************************************************************
* G-SENSOR 相关定义 :
*1. 如果不使用 G-SENSOR ， 将 USER_USE_GSENSOR 配置为 0
*2. 确认GSENSOR使用的IIC1 POS: 一般复用UART TX口
*******************************************************************************************/
#define		USER_USE_GSENSOR				0
#define		GSENSOR_IIC_POS					IIC1_POS_PE0PE1
/********************************************************************************************
* BATTERY 相关定义 :
*1. 如果不使用 BATTERY 电池 ， 将 USER_USE_BATTERY 配置为 0

*******************************************************************************************/
#define		USER_USE_BATTERY				1
/********************************************************************************************
* SPI1 相关定义 :
*1. 如果使用SPI1， 需要定义SPI1使用的IO MAP
*   SPI1_POS    CLK     FCS    DATx     D1
*   SPI1_POS0   PA2     PA3    PA1		PA0
*   SPI1_POS1   PD2     PD4    PD1		PD3
*   SPI1_POS2   PD8     PD10   PD7		PD9
*   SPI1_POS3   PF2     PF4    PF1		PF3
*SPI1_BUS_MODE : 0:3 LINE(DIN, DOUT,CLK) ,1 :2 LINE(CLK, DIN/DOUT)
*SPI1_CLK_IDLE ：  0： 空闲时低电平, 1: 空闲时高电平
*SPI1_SAMPLE_EDGE: if clk_idle = 0,  sample_edge = 0 , falling edge
				   if clk_idle = 0,  sample_edge = 1 , rising edge
				   if clk_idle = 1,  sample_edge = 1 , falling edge
				   if clk_idle = 1,  sample_edge = 0 , rising edge
*******************************************************************************************/
#define		SPI1_POS						SPI1_POS_NONE//SPI1_POS3
#define     SPI1_BAURATE                    2000000L   //2M 注意配置不小于300KHZ
#define     SPI1_BUS_MODE                   1
#define     SPI1_CLK_IDLE                   0
#define     SPI1_SAMPLE_EDGE                1

/********************************************************************************************
* touchpanel 触摸屏相关定义 :
*1. 如果使用touchpanel， 需要定义TOUCHPANEL_EN 为 1， 否则定义为0
*2. 确认 RESET脚的定义：
*  TOUCHPANEL_RESET_EN ： 为 1 时，表示需要软件配置IO口进行RESET
                          为 0 时，表示RESET由外部硬件电路实现
*  TOUCHPANEL_RESET_VALID ： （TOUCHPANEL_RESET_EN = 1 时生效）
*				          0：低电平reset， 1： 高电平reset
* 3. 确认 IIC 引脚定义：IIC使用软件IIC，波特率由 TOUCHPANEL_IIC_BAUDRATE 控制
*******************************************************************************************/
#define		TOUCHPANEL_EN					0
#define     TOUCHPANEL_RESET_EN				1
#define 	TOUCHPANEL_RESET_VALID			0 //0：低电平reset， 1： 高电平reset
#define     TOUCHPANEL_RESET_CH				GPIO_PF//GPIO_PD
#define     TOUCHPANEL_RESET_PIN			GPIO_PIN0
#define		TOUCHPANEL_IIC_SCL_CH			GPIO_PF//GPIO_PD
#define		TOUCHPANEL_IIC_SCL_PIN			GPIO_PIN1//GPIO_PIN7
#define		TOUCHPANEL_IIC_SDA_CH			GPIO_PF//GPIO_PD
#define		TOUCHPANEL_IIC_SDA_PIN			GPIO_PIN3//GPIO_PIN8
#define		TOUCHPANEL_IIC_BAUDRATE			100000L	//

/********************************************************************************************
* WKI唤醒功能 :
*1. 如果需要其他 唤醒开机的功能，如GSENSOR唤醒，RTC唤醒，则需要定义 USER_USE_WKI_WAKEUP 为 1
*   注意此时WKO输出口需要接二极管
*2. 否则，定义 USER_USE_WKI_WAKEUP为0，此时WKO输出 接二极管或电阻都可以
*******************************************************************************************/
#define		USER_USE_WKI_WAKEUP				1

/*********************LCD MAPPING GROUP*************************************************************************************
* NOTICE: PF10(USB11DP), PF11(USB11DM), when use these pins, USB11 is diable
* -------------------------------------------------------------------------------------------------------------------------
*|  GROUP   || POS0 | POS1 | POS2 | POS3 | POS4 | POS5 | POS6 | POS7 | POS8 | POS9 | POS10 | POS11 | POS12 | POS13 | POS14 |
*|----------||------|------|------|------|------|------|------|------|------|------|-------|-------|-------|-------|-------|
*|	CLK(WR) || PF7  | PF1  | PF1  | PA12 | PA12 | PD7  | PD7  | PF1  | PF1  | PF7  | PF7   | PD11  | PF1   | PA12  | PF7   |
*|	RS(HS)  || PF8  | PF2  | PF2  | PA13 | PA13 | PD8  | PD8  | PF2  | PF2  | PF8  | PF8   | PD12  | PF2   | PF8   | PF8   |
*|	CS(DE)  || PA2  | PF4  | PF4  | PA3  | PA3  | PD9  | PD9  | PF4  | PF4  | PA4  | PA4   | PD14  | PF4   | PA13  | PA4   |
*|	TE(VS)  || PF9  | PF3  | PD10 | PA4  | PF11 | PF11 | PA4  | PF3  | PD12 | PF9  | PF9   | PD13  | PF3   | PA3   | PF9   |
*|----------||------|------|------|------|------|------|------|------|------|------|-------|-------|-------|-------|-------|
*|	D0      || PF10 | PD3  | PF3  | PD10 | PD10 | PD10 | PD10 | PF10 | PF3  | PF11 | PD3   | PD3   | PD5   | PD10  | PD6   |
*|	D1      || PF11 | PD4  | X    | X    | X    | X    | X    | PF11 | X    | PD0  | PD4   | PD4   | PD6   | X     | PD7   |
*|	D2      || PD1  | PD5  | X    | X    | X    | X    | X    | PD5  | X    | PD1  | PD5   | PD5   | PD7   | X     | PD8   |
*|	D3      || PD2  | PD6  | X    | X    | X    | X    | X    | PD6  | X    | PD2  | PD6   | PD6   | PD8   | X     | PD9   |
*|	D4      || PD3  | PD7  | X    | X    | X    | X    | X    | PD7  | X    | PD3  | PD7   | PD7   | PD9   | X     | PD10  |
*|	D5      || PD4  | PD8  | X    | X    | X    | X    | X    | PD8  | X    | PD4  | PD8   | PD8   | PD10  | X     | PD11  |
*|	D6      || PD5  | PD9  | X    | X    | X    | X    | X    | PD9  | X    | PD5  | PD9   | PD9   | PD11  | X     | PD12  |
*|	D7      || PD6  | PD10 | X    | X    | X    | X    | X    | PD10 | X    | PD6  | PD10  | PD10  | PD12  | X     | PD13  |
*|----------||------|------|------|------|------|------|------|------|------|------|-------|-------|-------|-------|-------|
*|	D8      || PD7  | X    | X    | X    | X    | X    | X    | X    | X    | PD7  | PD11  | X     | X     | X     | PD14  |
*|	D9      || PD8  | X    | X    | X    | X    | X    | X    | X    | X    | PD8  | PD12  | X     | X     | X     | PF0   |
*|	D10     || PD9  | X    | X    | X    | X    | X    | X    | X    | X    | PD9  | PD13  | X     | X     | X     | PF1   |
*|	D11     || PD10 | X    | X    | X    | X    | X    | X    | X    | X    | PD10 | PD14  | X     | X     | X     | PF2   |
*|	D12     || PF1  | X    | X    | X    | X    | X    | X    | X    | X    | PD11 | PF1   | X     | X     | X     | PF3   |
*|	D13     || PF2  | X    | X    | X    | X    | X    | X    | X    | X    | PD12 | PF2   | X     | X     | X     | PF4   |
*|	D14     || PF3  | X    | X    | X    | X    | X    | X    | X    | X    | PD13 | PF3   | X     | X     | X     | PF5   |
*|	D15     || PF4  | X    | X    | X    | X    | X    | X    | X    | X    | PD14 | PF4   | X     | X     | X     | PF6   |
*|----------||------|------|------|------|------|------|------|------|------|------|-------|-------|-------|-------|-------|
*|	D16     || X    | X    | X    | X    | X    | X    | X    | X    | X    | PD15 | PF5   | X     | X     | X     | X     |
*|	D17     || X    | X    | X    | X    | X    | X    | X    | X    | X    | PF0  | PF6   | X     | X     | X     | X     |
*|	D18     || X    | X    | X    | X    | X    | X    | X    | X    | X    | PF1  | X     | X     | X     | X     | X     |
*|	D19     || X    | X    | X    | X    | X    | X    | X    | X    | X    | PF2  | X     | X     | X     | X     | X     |
*|	D20     || X    | X    | X    | X    | X    | X    | X    | X    | X    | PF3  | X     | X     | X     | X     | X     |
*|	D21     || X    | X    | X    | X    | X    | X    | X    | X    | X    | PF4  | X     | X     | X     | X     | X     |
*|	D22     || X    | X    | X    | X    | X    | X    | X    | X    | X    | PF5  | X     | X     | X     | X     | X     |
*|	D23     || X    | X    | X    | X    | X    | X    | X    | X    | X    | PF6  | X     | X     | X     | X     | X     |
* -------------------------------------------------------------------------------------------------------------------------
*********************LCD MAPPING GROUP************************************************************************************/

// CSI MAPPING GROUP
// ----------------------------------------------------------------------------------------------------------------------------------------
//   GROUP               MCLK    PCLK   VSYNC    HSYNC     D0      D1      D2      D3      D4      D5      D6      D7      D8      D9
// ----------------------------------------------------------------------------------------------------------------------------------------
//   0(HIGH 8BIT)        PE10	  PE7    PE14     PE12     XX      XX      PE4     PE2     PE0     PE3     PE5     PE8     PE9     PE11
// ----------------------------------------------------------------------------------------------------------------------------------------
//   1(ALL 10BIT)        PE10     PE7    PE14     PE12     PE0     PE1     PE5     PE3     PE2     PE4     PE6     PE8     PE9     PE11
// ----------------------------------------------------------------------------------------------------------------------------------------
//   2(复用MIPI)         PE10     PE7    PE14     PE12     PE0     PE1     CLKP    DATAP0  DATAN0  CLKN    PE6     PE8     PE9     PE11
// ----------------------------------------------------------------------------------------------------------------------------------------
//   3(复用MIPI)         PE10     PE7    PE14     PE12     DATAN0  DATAP0  DATAP1  CLKP    CLKN    DATAN1  PE6     PE8     PE9     PE11
// ----------------------------------------------------------------------------------------------------------------------------------------
//   4(复用MIPI)         PE10     PE7    PE14     PE12     DATAN0  DATAP0  PE1     CLKP    CLKN    PE0     PE6     PE8     PE9     PE11
// ----------------------------------------------------------------------------------------------------------------------------------------




#endif




