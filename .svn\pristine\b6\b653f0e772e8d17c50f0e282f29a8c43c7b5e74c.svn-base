Archive member included to satisfy reference by file (symbol)

..\lib\libboot.a(boot.o)      obj\Debug\dev\battery\src\battery_api.o (boot_vddrtcCalculate)
..\lib\libboot.a(boot_loader.o)
                              obj\Debug\mcu\boot\spi_boot_cfg.o (.bootsect)
..\lib\libboot.a(reset.o)     obj\Debug\mcu\boot\spi_boot_cfg.o (_start)
..\lib\libboot.a(boot_lib.o)  ..\lib\libboot.a(boot_loader.o) (boot_sdram_init)
..\lib\libmcu.a(hx330x_adc.o)
                              obj\Debug\hal\src\hal_adc.o (hx330x_adcEnable)
..\lib\libmcu.a(hx330x_auadc.o)
                              obj\Debug\hal\src\hal_auadc.o (hx330x_auadcHalfIRQRegister)
..\lib\libmcu.a(hx330x_csi.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_csiInit)
..\lib\libmcu.a(hx330x_dac.o)
                              obj\Debug\hal\src\hal_dac.o (hx330x_dacTypeCfg)
..\lib\libmcu.a(hx330x_dma.o)
                              obj\Debug\app\app_common\src\app_lcdshow.o (hx330x_dmaNocWinA)
..\lib\libmcu.a(hx330x_dmauart.o)
                              obj\Debug\hal\src\hal_dmauart.o (hx330x_DmaUart_con_cfg)
..\lib\libmcu.a(hx330x_gpio.o)
                              ..\lib\libmcu.a(hx330x_csi.o) (hx330x_gpioSFRSet)
..\lib\libmcu.a(hx330x_iic.o)
                              obj\Debug\hal\src\hal_iic.o (hx330x_iic0Init)
..\lib\libmcu.a(hx330x_int.o)
                              ..\lib\libboot.a(reset.o) (fast_isr)
..\lib\libmcu.a(hx330x_isp.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_isp_mask_tab_cfg)
..\lib\libmcu.a(hx330x_isp_tab.o)
                              ..\lib\libmcu.a(hx330x_isp.o) (GAOS3X3_TAB)
..\lib\libmcu.a(hx330x_jpg.o)
                              obj\Debug\hal\src\hal_lcdshow.o (hx330x_mjpA_EncodeISRRegister)
..\lib\libmcu.a(hx330x_jpg_tab.o)
                              ..\lib\libmcu.a(hx330x_jpg.o) (hx330x_mjpA_table_init)
..\lib\libmcu.a(hx330x_lcd.o)
                              obj\Debug\dev\lcd\src\lcd_api.o (hx330x_lcdReset)
..\lib\libmcu.a(hx330x_lcdrotate.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_rotateIRQHandler)
..\lib\libmcu.a(hx330x_lcdui.o)
                              obj\Debug\dev\usb\dusb\src\dusb_msc.o (hx330x_lcdShowWaitDone)
..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_uiLzoIRQHandler)
..\lib\libmcu.a(hx330x_lcdwin.o)
                              obj\Debug\hal\src\hal_lcdshow.o (hx330x_lcdWinABConfig)
..\lib\libmcu.a(hx330x_md.o)  obj\Debug\hal\src\hal_md.o (hx330x_mdEnable)
..\lib\libmcu.a(hx330x_mipi.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_MipiCSIUinit)
..\lib\libmcu.a(hx330x_misc.o)
                              obj\Debug\dev\touchpanel\src\touchpanel_api.o (hx330x_abs)
..\lib\libmcu.a(hx330x_rtc.o)
                              obj\Debug\hal\src\hal_rtc.o (hx330x_rtcRamRead)
..\lib\libmcu.a(hx330x_sd.o)  obj\Debug\dev\sd\src\sd_api.o (hx330x_sd0Init)
..\lib\libmcu.a(hx330x_spi0.o)
                              obj\Debug\hal\src\hal_spi.o (hx330x_spi0ManualInit)
..\lib\libmcu.a(hx330x_spi1.o)
                              obj\Debug\hal\src\hal_spi1.o (hx330x_spi1_CS_Config)
..\lib\libmcu.a(hx330x_sys.o)
                              obj\Debug\dev\gsensor\src\gsensor_da380.o (hx330x_sysCpuMsDelay)
..\lib\libmcu.a(hx330x_timer.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_timer0IRQHandler)
..\lib\libmcu.a(hx330x_tminf.o)
                              obj\Debug\hal\src\hal_watermark.o (hx330x_mjpA_TimeinfoEnable)
..\lib\libmcu.a(hx330x_uart.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_uart0IRQHandler)
..\lib\libmcu.a(hx330x_usb.o)
                              obj\Debug\dev\usb\dusb\src\dusb_api.o (hx330x_usb20_CallbackRegister)
..\lib\libmcu.a(hx330x_wdt.o)
                              obj\Debug\dev\usb\dusb\src\dusb_msc.o (hx330x_wdtEnable)
..\lib\libmcu.a(hx330x_emi.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_emiIRQHandler)
..\lib\libisp.a(hal_isp.o)    obj\Debug\dev\sensor\src\sensor_api.o (hal_sensor_fps_adpt)
..\lib\libjpg.a(hal_jpg.o)    obj\Debug\hal\src\hal_mjpAEncode.o (hal_mjp_enle_init)
..\lib\liblcd.a(hal_lcd.o)    obj\Debug\hal\src\hal_lcdshow.o (lcd_show_ctrl)
..\lib\liblcd.a(hal_lcdMem.o)
                              ..\lib\liblcd.a(hal_lcd.o) (hal_lcdAddrCalculate)
..\lib\liblcd.a(hal_lcdrotate.o)
                              ..\lib\liblcd.a(hal_lcd.o) (hal_rotateInit)
..\lib\liblcd.a(hal_lcdUi.o)  obj\Debug\app\app_common\src\app_lcdshow.o (hal_uiDrawBufMalloc)
..\lib\liblcd.a(hal_lcdUiLzo.o)
                              ..\lib\liblcd.a(hal_lcdUi.o) (hal_uiLzokick)
..\lib\liblcd.a(lcd_tab.o)    obj\Debug\dev\lcd\src\lcd_api.o (hal_lcdParaLoad)
..\lib\libmultimedia.a(api_multimedia.o)
                              obj\Debug\multimedia\audio\audio_playback.o (api_multimedia_init)
..\lib\libmultimedia.a(avi_dec.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_dec_func)
..\lib\libmultimedia.a(avi_odml_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_odml_enc_func)
..\lib\libmultimedia.a(avi_std_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_std_enc_func)
..\lib\libmultimedia.a(wav_dec.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (wav_dec_func)
..\lib\libmultimedia.a(wav_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (wav_enc_func)
..\lib\libmultimedia.a(wav_pcm.o)
                              ..\lib\libmultimedia.a(wav_enc.o) (pcm_encode)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                              obj\Debug\app\user_config\src\mbedtls_md5.o (memcmp)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                              obj\Debug\dev\sd\src\sd_api.o (memcpy)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                              obj\Debug\dev\fs\src\fs_api.o (memset)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                              obj\Debug\dev\gsensor\src\gsensor_api.o (strcpy)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                              obj\Debug\dev\fs\src\ff.o (__udivdi3)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                              obj\Debug\dev\fs\src\ff.o (__umoddi3)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
                              D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__udivsi3)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
                              D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__umodsi3)
D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                              D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__clz_tab)

Allocating common symbols
Common symbol       size              file

WAV_TYPE_E          0x4               obj\Debug\dev\battery\src\battery_api.o
SysCtrl             0x130             obj\Debug\app\app_common\src\app_init.o
gsensor_ctl         0x8               obj\Debug\dev\gsensor\src\gsensor_api.o
RGB_GMMA_Tab        0x300             ..\lib\libmcu.a(hx330x_isp.o)
ui_draw_ctrl        0x10              obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
usb_dev_ctl         0x29c             obj\Debug\dev\usb\dusb\src\dusb_api.o
rc128k_div          0x4               ..\lib\libmcu.a(hx330x_rtc.o)
husb_ctl            0x1484            obj\Debug\dev\usb\husb\src\husb_api.o
rtcSecondISR        0x4               ..\lib\libmcu.a(hx330x_rtc.o)
task_play_audio_stat
                    0x4               obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
lcdshow_frame_op    0x1e8             ..\lib\liblcd.a(hal_lcdMem.o)
sd_update_op        0x64              obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
mediaVideoCtl       0x74              obj\Debug\multimedia\video\video_record.o
Y_GMA_Tab           0x200             ..\lib\libmcu.a(hx330x_isp.o)
rtcAlamISR          0x4               ..\lib\libmcu.a(hx330x_rtc.o)
usensor_handle      0x4               obj\Debug\dev\usb\husb\src\husb_usensor.o
USB_CH              0x4               obj\Debug\dev\battery\src\battery_api.o
UVC_CACHE_STA       0x4               obj\Debug\dev\battery\src\battery_api.o
fs_exfunc           0x14              obj\Debug\dev\fs\src\fs_api.o
SDCON0_T            0x4               obj\Debug\dev\battery\src\battery_api.o
usbDeviceOp         0xc               obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
XOSNesting          0x4               obj\Debug\mcu\xos\xos.o
smph_dmacopy        0x4               ..\lib\libmcu.a(hx330x_sys.o)
SDCON1_T            0x4               obj\Debug\dev\battery\src\battery_api.o
tp_api_t            0x20              obj\Debug\dev\touchpanel\src\touchpanel_api.o
hx330x_lcdISR       0x14              ..\lib\libmcu.a(hx330x_lcd.o)
dev_key_tab         0x78              obj\Debug\dev\key\src\key_api.o
hx330x_timerISR     0x10              ..\lib\libmcu.a(hx330x_timer.o)
uhub_handle         0x4               obj\Debug\dev\usb\husb\src\husb_hub.o
UVC_FSTACK_STA      0x4               obj\Debug\dev\battery\src\battery_api.o
rc128k_rtc_cnt      0x4               ..\lib\libmcu.a(hx330x_rtc.o)
lcd_show_ctrl       0x84              ..\lib\liblcd.a(hal_lcd.o)
recordPhotoOp       0x20              obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
rc128k_timer_cnt    0x4               ..\lib\libmcu.a(hx330x_rtc.o)
playVideoOp         0x44              obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
CHANNEL_EXCHANGE_E  0x4               obj\Debug\dev\battery\src\battery_api.o

Memory Configuration

Name             Origin             Length             Attributes
boot             0x01fffc00         0x00000200
ram_boot         0x00000000         0x00006c00
ram_user         0x00000000         0x00007000
usb_ram          0x00008000         0x00008000
line_ram         0x00200000         0x00010000
mp3_text         0x00008000         0x00008000
mp3_ram          0x00200000         0x00010000
nes_text         0x00008000         0x00008000
nes_ram          0x00200000         0x00010000
sdram            0x02000000         0x00800000
flash            0x06000000         0x00800000
exsdram          0x00000000         0x00800000
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD obj\Debug\dev\battery\src\battery_api.o
LOAD obj\Debug\dev\dev_api.o
LOAD obj\Debug\dev\fs\src\diskio.o
LOAD obj\Debug\dev\fs\src\ff.o
LOAD obj\Debug\dev\fs\src\ffunicode.o
LOAD obj\Debug\dev\fs\src\fs_api.o
LOAD obj\Debug\dev\gsensor\src\gsensor_api.o
LOAD obj\Debug\dev\gsensor\src\gsensor_da380.o
LOAD obj\Debug\dev\gsensor\src\gsensor_gma301.o
LOAD obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
LOAD obj\Debug\dev\ir\src\ir_api.o
LOAD obj\Debug\dev\key\src\key_api.o
LOAD obj\Debug\dev\lcd\src\lcd_api.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
LOAD obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
LOAD obj\Debug\dev\led\src\led_api.o
LOAD obj\Debug\dev\led_pwm\src\led_pwm_api.o
LOAD obj\Debug\dev\nvfs\src\nvfs_api.o
LOAD obj\Debug\dev\nvfs\src\nvfs_jpg.o
LOAD obj\Debug\dev\sd\src\sd_api.o
LOAD obj\Debug\dev\sensor\src\sensor_api.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
LOAD obj\Debug\dev\sensor\src\sensor_tab.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_api.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_iic.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_api.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_enum.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_msc.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_uac.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_uvc.o
LOAD obj\Debug\dev\usb\husb\src\husb_api.o
LOAD obj\Debug\dev\usb\husb\src\husb_enum.o
LOAD obj\Debug\dev\usb\husb\src\husb_hub.o
LOAD obj\Debug\dev\usb\husb\src\husb_tpbulk.o
LOAD obj\Debug\dev\usb\husb\src\husb_usensor.o
LOAD obj\Debug\dev\usb\husb\src\husb_uvc.o
LOAD obj\Debug\hal\src\hal_adc.o
LOAD obj\Debug\hal\src\hal_auadc.o
LOAD obj\Debug\hal\src\hal_csi.o
LOAD obj\Debug\hal\src\hal_dac.o
LOAD obj\Debug\hal\src\hal_dmauart.o
LOAD obj\Debug\hal\src\hal_eeprom.o
LOAD obj\Debug\hal\src\hal_gpio.o
LOAD obj\Debug\hal\src\hal_iic.o
LOAD obj\Debug\hal\src\hal_int.o
LOAD obj\Debug\hal\src\hal_lcdshow.o
LOAD obj\Debug\hal\src\hal_md.o
LOAD obj\Debug\hal\src\hal_mjpAEncode.o
LOAD obj\Debug\hal\src\hal_mjpBEncode.o
LOAD obj\Debug\hal\src\hal_mjpDecode.o
LOAD obj\Debug\hal\src\hal_rtc.o
LOAD obj\Debug\hal\src\hal_spi.o
LOAD obj\Debug\hal\src\hal_spi1.o
LOAD obj\Debug\hal\src\hal_stream.o
LOAD obj\Debug\hal\src\hal_sys.o
LOAD obj\Debug\hal\src\hal_timer.o
LOAD obj\Debug\hal\src\hal_uart.o
LOAD obj\Debug\hal\src\hal_watermark.o
LOAD obj\Debug\hal\src\hal_wdt.o
LOAD obj\Debug\mcu\boot\spi_boot_cfg.o
LOAD obj\Debug\mcu\xos\xmbox.o
LOAD obj\Debug\mcu\xos\xmsgq.o
LOAD obj\Debug\mcu\xos\xos.o
LOAD obj\Debug\mcu\xos\xwork.o
LOAD obj\Debug\multimedia\audio\audio_playback.o
LOAD obj\Debug\multimedia\audio\audio_record.o
LOAD obj\Debug\multimedia\image\image_decode.o
LOAD obj\Debug\multimedia\image\image_encode.o
LOAD obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
LOAD obj\Debug\multimedia\video\video_playback.o
LOAD obj\Debug\multimedia\video\video_record.o
LOAD obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
LOAD obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
LOAD obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
LOAD obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
LOAD obj\Debug\sys_manage\res_manage\res_manage_api.o
LOAD obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
LOAD obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
LOAD obj\Debug\app\app_common\src\app_init.o
LOAD obj\Debug\app\app_common\src\app_lcdshow.o
LOAD obj\Debug\app\app_common\src\main.o
LOAD obj\Debug\app\resource\user_res.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
LOAD obj\Debug\app\task_windows\msg_api.o
LOAD obj\Debug\app\task_windows\task_api.o
LOAD obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
LOAD obj\Debug\app\task_windows\task_common\src\task_common.o
LOAD obj\Debug\app\task_windows\task_common\src\task_common_msg.o
LOAD obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
LOAD obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
LOAD obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
LOAD obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
LOAD obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
LOAD obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
LOAD obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
LOAD obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
LOAD obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
LOAD obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
LOAD obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
LOAD obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
LOAD obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
LOAD obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
LOAD obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
LOAD obj\Debug\app\task_windows\windows_api.o
LOAD obj\Debug\app\user_config\src\mbedtls_md5.o
LOAD obj\Debug\app\user_config\src\user_config_api.o
LOAD obj\Debug\app\user_config\src\user_config_tab.o
                0x00200000                __sdram_size = (boot_sdram_size == 0x1)?0x800000:0x200000

.bootsec        0x01fffc00      0x200 load address 0x00000000
 *(.bootsec)
 .bootsec       0x01fffc00      0x200 ..\lib\libboot.a(boot_loader.o)
                0x01fffc10                hex
                0x01fffc10                .hex
                0x01fffd1c                .bootsect

.boot_code      0x00000000     0x29a8 load address 0x00000200
                0x00000000                _boot_vma = .
 *(.vector)
 .vector        0x00000000      0x380 ..\lib\libboot.a(reset.o)
                0x000002a0                _start
                0x000002ec                _step_in
                0x00000330                _step_out
 *(.vector.kepttext)
 .vector.kepttext
                0x00000380       0x6c obj\Debug\dev\dev_api.o
                0x00000380                exception_lowpower_io_cfg
 .vector.kepttext
                0x000003ec      0x44c ..\lib\libboot.a(boot.o)
                0x000004d8                boot_vddrtcCalculate
                0x00000504                boot_putchar
                0x000005b0                boot_uart_puts
                0x00000604                exception
                0x0000081c                exception_trigger
 .vector.kepttext
                0x00000838       0x20 ..\lib\libboot.a(boot_lib.o)
                0x00000838                boot_getChipSN
 .vector.kepttext
                0x00000858      0x210 ..\lib\libmcu.a(hx330x_gpio.o)
                0x00000858                exception_gpioDataSet
                0x000008e4                hx330x_gpioDataGet
                0x00000968                hx330x_gpioCommonDataGet
                0x000009b0                exception_io1d1_softstart_clr
 .vector.kepttext
                0x00000a68      0x304 ..\lib\libmcu.a(hx330x_rtc.o)
                0x00000a68                hx330x_rtcWriteByte
                0x00000ab8                hx330x_rtcReadByte
                0x00000b1c                hx330x_rtcDataRead
                0x00000ba4                hx330x_rtcDataWrite
                0x00000c2c                hx330x_WKI1WakeupEnable
 .vector.kepttext
                0x00000d6c      0x1f0 ..\lib\libmcu.a(hx330x_sys.o)
                0x00000d6c                hx330x_sysCpuMsDelay
                0x00000dc0                hx330x_sysCpuNopDelay
                0x00000dfc                hx330x_bytes_memcpy
                0x00000e6c                hx330x_bytes_memset
                0x00000ec4                hx330x_bytes_cmp
 .vector.kepttext
                0x00000f5c       0x74 ..\lib\libmcu.a(hx330x_uart.o)
                0x00000f5c                hx330x_uart0SendByte
 .vector.kepttext
                0x00000fd0       0x84 ..\lib\libmcu.a(hx330x_wdt.o)
                0x00000fd0                hx330x_wdtEnable
                0x00001004                hx330x_wdtClear
                0x00001038                hx330x_wdtReset
 *(.vector.keptdata)
 .vector.keptdata
                0x00001054      0x148 obj\Debug\app\user_config\src\user_config_tab.o
                0x00001054                hardware_setup
 .vector.keptdata
                0x0000119c       0x3c ..\lib\libboot.a(boot.o)
                0x0000119c                vbg_param
 .vector.keptdata
                0x000011d8      0x100 ..\lib\libboot.a(reset.o)
                0x000011d8                _step_data
 .vector.keptdata
                0x000012d8        0x4 ..\lib\libboot.a(boot_lib.o)
 .vector.keptdata
                0x000012dc        0x4 ..\lib\libmcu.a(hx330x_gpio.o)
 .vector.keptdata
                0x000012e0        0x4 ..\lib\libmcu.a(hx330x_spi0.o)
                0x000012e0                spi_auto_mode
                0x000012e4                . = ALIGN (0x4)
                0x000012e4                _boot_kept_vma = .
 *(.vector.text)
 .vector.text   0x000012e4      0x8f4 ..\lib\libboot.a(boot.o)
                0x00001554                boot_vbg_pre_init
                0x000015ec                boot_vbg_back_init
                0x00001634                exception_init
                0x0000166c                bool_pll_init
                0x00001ae4                boot_clktun_check
                0x00001b70                boot_clktun_save
 .vector.text   0x00001bd8      0xbbc ..\lib\libboot.a(boot_lib.o)
                0x00001dc0                boot_sdram_init
 *(.vector.data)
 .vector.data   0x00002794        0x4 ..\lib\libboot.a(boot.o)
                0x00002794                vbg_adc
 .vector.data   0x00002798      0x210 ..\lib\libboot.a(boot_lib.o)
                0x00002798                tune_values
                0x00002918                tune_by
                0x00002924                tune_tab_2_clk
                0x00002944                tune_tab_1_clk
                0x00002964                tuning_test_addr
                0x00002984                tuning_test_data
                0x000029a4                SDRTUN2_CON

.ram            0x00000000     0x36d0
                0x000012e4                . = _boot_kept_vma
 *fill*         0x00000000     0x12e4 
                0x000012e4                __sram_start = .
 *(.sram_usb11fifo)
 .sram_usb11fifo
                0x000012e4      0x96c obj\Debug\hal\src\hal_sys.o
                0x000012e4                usb11_fifo
 *(.sram_comm)
 .sram_comm     0x00001c50      0xd60 obj\Debug\dev\fs\src\fs_api.o
 .sram_comm     0x000029b0      0x200 obj\Debug\dev\sd\src\sd_api.o
                0x000029b0                sd0RamBuffer
 .sram_comm     0x00002bb0       0x40 obj\Debug\hal\src\hal_dmauart.o
                0x00002bb0                dmauart_fifo
 .sram_comm     0x00002bf0      0x400 obj\Debug\hal\src\hal_mjpDecode.o
 .sram_comm     0x00002ff0       0x60 obj\Debug\mcu\xos\xmsgq.o
 .sram_comm     0x00003050      0x400 ..\lib\libmcu.a(hx330x_jpg.o)
                0x00003050                jpg_dri_tab
 .sram_comm     0x00003450      0x280 ..\lib\libisp.a(hal_isp.o)
                0x000036d0                __sram_end = .

.usb_ram        0x00008000     0x3630
                0x00008000                __ufifo_start = .
 *(.uram_usb20fifo)
 .uram_usb20fifo
                0x00008000     0x1d30 obj\Debug\hal\src\hal_sys.o
                0x00008000                usb20_fifo
 *(.uram_comm)
 .uram_comm     0x00009d30     0x1900 obj\Debug\hal\src\hal_watermark.o
                0x00009d30                tminf_font
                0x0000b630                __ufifo_end = .

.line_ram       0x00200000        0x0
                0x00200000                __line_start = .
 *(.lram_comm)
                0x00200000                __line_end = .

.on_sdram       0x02000000     0x9b9c load address 0x00002c00
                0x02000000                _onsdram_start = .
 *(.sdram_text)
 .sdram_text    0x02000000       0x3c obj\Debug\dev\fs\src\ff.o
                0x02000000                clst2sect
 .sdram_text    0x0200003c       0x58 obj\Debug\dev\fs\src\fs_api.o
                0x0200003c                fs_getClustStartSector
 .sdram_text    0x02000094      0x174 obj\Debug\dev\nvfs\src\nvfs_jpg.o
                0x02000094                nv_jpg_write_by_linkmap
 .sdram_text    0x02000208      0x4f8 obj\Debug\dev\sd\src\sd_api.o
                0x020002a8                sd_api_Stop
                0x02000538                sd_api_Exist
                0x02000560                sd_api_Write
                0x02000640                sd_api_Read
 .sdram_text    0x02000700       0x3c obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .sdram_text    0x0200073c      0x260 obj\Debug\dev\usb\husb\src\husb_hub.o
 .sdram_text    0x0200099c     0x110c obj\Debug\dev\usb\husb\src\husb_uvc.o
                0x02001890                huvc_cache_dcd_down
                0x0200194c                husb_uvc_frame_read
 .sdram_text    0x02001aa8       0x3c obj\Debug\hal\src\hal_adc.o
                0x02001aa8                hal_adcGetChannel
 .sdram_text    0x02001ae4       0x48 obj\Debug\hal\src\hal_dac.o
 .sdram_text    0x02001b2c      0x104 obj\Debug\hal\src\hal_gpio.o
                0x02001b2c                hal_gpioInit
                0x02001bd4                hal_gpioEPullSet
 .sdram_text    0x02001c30      0x12c obj\Debug\hal\src\hal_lcdshow.o
                0x02001c30                hal_lcd_enc_kick
 .sdram_text    0x02001d5c      0x9f8 obj\Debug\hal\src\hal_spi.o
                0x02001d8c                hal_spiUpdata_led_show
                0x02001e44                hal_spiUpdata_led_show_init
                0x02001ec4                hal_spiUpdata_led_show_uinit
                0x02001f1c                hal_spiManualInit
                0x02001f48                hal_spiAutoModeInit
                0x02001f78                hal_spiModeSwitch
                0x02001ff4                hal_spiFlashReadID
                0x02002064                hal_spiFlashWriteEnable
                0x02002098                hal_spiFlashWait
                0x02002128                hal_spiFlashReadPage
                0x02002180                hal_spiFlashRead
                0x0200220c                hal_spiFlashWritePage
                0x020022a0                hal_spiFlashWrite
                0x02002380                hal_spiFlashWriteInManual
                0x02002414                hal_spiFlashEraseSector
                0x020024b8                hal_spiFlashEraseBlock
                0x02002538                hal_spiFlashEraseChip
                0x02002574                hal_spiFlashReadUniqueID
                0x020025fc                hal_spiFlashReadOTP
                0x020026a8                hal_spiFlashReadManual
 .sdram_text    0x02002754      0x460 obj\Debug\hal\src\hal_stream.o
                0x02002754                hal_streamMalloc
                0x0200286c                hal_streamIn
                0x0200294c                hal_streamOut
                0x02002a48                hal_streamOutNext
                0x02002b10                hal_streamfree
 .sdram_text    0x02002bb4       0x24 obj\Debug\hal\src\hal_timer.o
                0x02002bb4                hal_timerPWMStop
 .sdram_text    0x02002bd8      0x158 obj\Debug\hal\src\hal_uart.o
                0x02002bd8                uart_Printf
 .sdram_text    0x02002d30      0xc34 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02002d30                taskSdGetClst
                0x02002da4                taskSdReadBuf
                0x02002ee0                taskSdUpdateProcess
 .sdram_text    0x02003964      0x330 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                0x02003964                taskSdUpdateDrawStartAddrCal
                0x02003a3c                taskSdUpdateDrawAddrReCal
                0x02003ba8                taskSdUpdate_uiProgress
 .sdram_text    0x02003c94      0x11c ..\lib\libmcu.a(hx330x_adc.o)
                0x02003c94                hx330x_adcEnable
                0x02003cf0                hx330x_adcSetBaudrate
                0x02003d24                hx330x_adcConverStart
                0x02003d48                hx330x_adcRead
 .sdram_text    0x02003db0       0x90 ..\lib\libmcu.a(hx330x_auadc.o)
                0x02003db0                hx330x_auadcIRQHandler
 .sdram_text    0x02003e40      0x170 ..\lib\libmcu.a(hx330x_csi.o)
                0x02003e40                hx330x_csiIRQHandler
 .sdram_text    0x02003fb0      0x1e8 ..\lib\libmcu.a(hx330x_dac.o)
                0x02003fb0                hx330x_dacIRQHandler
                0x02004084                hx330x_dacBufferSet
                0x020040a4                hx330x_dacBufferFlush
                0x020040c4                hx330x_dacStart
                0x02004140                hx330x_check_dacstop
 .sdram_text    0x02004198       0xa4 ..\lib\libmcu.a(hx330x_dmauart.o)
                0x02004198                hx330x_uart1IRQHandler
 .sdram_text    0x0200423c      0x584 ..\lib\libmcu.a(hx330x_gpio.o)
                0x0200423c                hx330x_gpioDirSet
                0x020042e0                hx330x_gpioPullSet
                0x020043a0                hx330x_gpioPinPollSet
                0x020043ec                hx330x_gpioDrvSet
                0x02004478                hx330x_gpioDataSet
                0x02004504                hx330x_gpioPinDataSet
                0x02004550                hx330x_gpioMapSet
                0x020045dc                hx330x_gpioDigitalSet
                0x02004668                hx330x_gpioLedPull
                0x02004728                hx330x_gpioIRQHandler
 .sdram_text    0x020047c0      0x230 ..\lib\libmcu.a(hx330x_int.o)
                0x020047c0                hx330x_intHandler
                0x020047d4                fast_isr
                0x020048ac                slow_isr
                0x020049a8                hx330x_intEnable
 .sdram_text    0x020049f0      0x1a4 ..\lib\libmcu.a(hx330x_jpg.o)
                0x020049f0                hx330x_mjpA_EncodeLoadAddrGet
                0x02004a0c                hx330x_mjpA_EncodeStartAddrGet
                0x02004a28                hx330x_mjpA_EncodeDriTabGet
                0x02004a44                hx330x_mjpA_IRQHandler
                0x02004a90                hx330x_mjpB_IRQHandler
 .sdram_text    0x02004b94      0x17c ..\lib\libmcu.a(hx330x_lcd.o)
                0x02004b94                hx330x_lcdKick
                0x02004bb8                hx330x_lcdIRQHandler
 .sdram_text    0x02004d10       0x50 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                0x02004d10                hx330x_rotateIRQHandler
 .sdram_text    0x02004d60      0x470 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x02004d60                hx330x_lcdShowWaitDone
                0x02004dac                hx330x_lcdShowKick
                0x02004dd0                hx330x_lcdShowIRQHandler
                0x02004e30                hx330x_lcdVideoSetScanMode
                0x02004ec4                hx330x_lcdVideoSetAddr
                0x02004ee0                hx330x_lcdVideoSetStride
                0x02004f04                hx330x_lcdvideoSetPosition
                0x02004f28                hx330x_lcdUiEnable
                0x02004f7c                hx330x_lcdUiSetAddr
                0x02005030                hx330x_lcdVideoUpScaler_cfg
                0x02005128                hx330x_lcdVideoUpScalerSoftRotate_cfg
 .sdram_text    0x020051d0       0x4c ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                0x020051d0                hx330x_uiLzoIRQHandler
 .sdram_text    0x0200521c       0x60 ..\lib\libmcu.a(hx330x_misc.o)
                0x0200521c                hx330x_min
                0x02005240                hx330x_data_check
 .sdram_text    0x0200527c      0x224 ..\lib\libmcu.a(hx330x_rtc.o)
                0x0200527c                hx330x_rtcIRQHandler
                0x02005348                hx330x_WKOEnable
                0x020053c4                hx330x_WKI1WakeupTriger
                0x02005414                hx330x_WKI0WakeupTriger
                0x02005464                hx330x_WakeUpCleanPending
 .sdram_text    0x020054a0      0x554 ..\lib\libmcu.a(hx330x_sd.o)
                0x020054a0                hx330x_sd0SendCmd
                0x0200556c                hx330x_sd0Recv
                0x020055c8                hx330x_sd0Send
                0x02005600                hx330x_sd0WaitDAT0
                0x020056a0                hx330x_sd0WaitPend
                0x02005728                hx330x_sd0GetRsp
                0x02005744                hx330x_sd0CRCCheck
                0x020057a8                hx330x_sd1SendCmd
                0x02005874                hx330x_sd1Recv
                0x020058d0                hx330x_sd1Send
                0x02005908                hx330x_sd1WaitPend
                0x02005990                hx330x_sd1CRCCheck
 .sdram_text    0x020059f4      0x678 ..\lib\libmcu.a(hx330x_spi0.o)
                0x020059f4                hx330x_spi0PinConfig
                0x02005a74                hx330x_spi0ManualInit
                0x02005ba0                hx330x_spi0SendByte
                0x02005bf4                hx330x_spi0RecvByte
                0x02005c48                hx330x_spi0Send
                0x02005d00                hx330x_spi0Recv
                0x02005dc4                hx330x_spi0CS0Config
                0x02005e08                hx330x_spi0AutoModeInit
                0x02005ff4                hx330x_spi0ExitAutoMode
 .sdram_text    0x0200606c       0x64 ..\lib\libmcu.a(hx330x_spi1.o)
                0x0200606c                hx330x_spi1DMAIRQHandler
 .sdram_text    0x020060d0      0x63c ..\lib\libmcu.a(hx330x_sys.o)
                0x020060d0                table_init_sfr
                0x02006150                hx330x_sysDcacheWback
                0x020061ec                hx330x_sysDcacheFlush
                0x02006288                hx330x_sysClkSet
                0x020062e4                hx330x_mcpy0_sdram2gram
                0x020063c0                hx330x_mcpy0_sdram2gram_nocache
                0x02006468                hx330x_mcpy1_sdram2gram_nocache_waitdone
                0x020064dc                hx330x_mcpy1_sdram2gram_nocache_kick
                0x0200658c                hx330x_mcpy1_sdram2gram
                0x02006658                hx330x_mcpy1_sdram2gram_nocache
 .sdram_text    0x0200670c      0x1fc ..\lib\libmcu.a(hx330x_timer.o)
                0x0200670c                hx330x_timer0IRQHandler
                0x02006750                hx330x_timer1IRQHandler
                0x02006794                hx330x_timer2IRQHandler
                0x020067d8                hx330x_timer3IRQHandler
                0x0200681c                hx330x_timerTickStart
                0x02006850                hx330x_timerTickStop
                0x0200686c                hx330x_timerTickCount
                0x02006888                hx330x_timerPWMStop
 .sdram_text    0x02006908       0x64 ..\lib\libmcu.a(hx330x_uart.o)
                0x02006908                hx330x_uart0IRQHandler
 .sdram_text    0x0200696c      0x76c ..\lib\libmcu.a(hx330x_usb.o)
                0x020069f4                hx330x_usb20_Func_Call
                0x02006a20                hx330x_bulk20_tx
                0x02006be4                hx330x_bulk20_rx
                0x02006d64                hx330x_usb20DevIRQHanlder
                0x02006e9c                hx330x_usb20_hostIRQHanlder
                0x02006fa4                hx330x_usb11_Func_Call
                0x02006fd0                hx330x_usb11_hostIRQHanlder
 .sdram_text    0x020070d8       0x5c ..\lib\libmcu.a(hx330x_emi.o)
                0x020070d8                hx330x_emiIRQHandler
 .sdram_text    0x02007134       0x68 ..\lib\libjpg.a(hal_jpg.o)
                0x02007134                hal_mjp_enle_tab_get
 .sdram_text    0x0200719c      0x358 ..\lib\liblcd.a(hal_lcd.o)
 .sdram_text    0x020074f4      0x228 ..\lib\liblcd.a(hal_lcdMem.o)
                0x020074f4                hal_lcdAddrCalculate
                0x020076b8                hal_dispframeFree
 .sdram_text    0x0200771c      0x18c ..\lib\liblcd.a(hal_lcdUi.o)
                0x0200771c                hal_uiBuffFree
                0x02007744                hal_lcdUiEnable
                0x0200779c                hal_lcdUiBuffFlush
 *(.sdram_code)
 .sdram_code    0x020078a8      0x110 ..\lib\libmcu.a(hx330x_spi0.o)
                0x020078a8                spi0PinCfg_tab
                0x020078b8                SPI0_4_LINE_tab
                0x020078f8                SPI0_2_LINE1_tab
                0x02007938                SPI0_2_LINE0_tab
                0x02007978                SPI0_1_LINE_tab
 *(.sdram_data)
 *(.data*)
 .data          0x020079b8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .data          0x020079b8      0x120 obj\Debug\dev\dev_api.o
                0x020079b8                dev_node
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\diskio.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\ff.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\ffunicode.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\fs_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .data          0x02007ad8        0x0 obj\Debug\dev\ir\src\ir_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\key\src\key_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .data          0x02007ad8        0x0 obj\Debug\dev\led\src\led_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\nvfs\src\nvfs_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sd\src\sd_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .data          0x02007ad8        0x4 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .data          0x02007adc        0x0 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .data          0x02007adc      0x208 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x02007adc                dusb_com_cfgdsc
                0x02007cc4                dusb_msc_cfgdsc
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_api.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_enum.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_hub.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_adc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_auadc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_csi.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_dac.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_dmauart.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_eeprom.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_gpio.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_iic.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_int.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_lcdshow.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_md.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpAEncode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpBEncode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpDecode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_rtc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_spi.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_spi1.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_stream.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_sys.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_timer.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_uart.o
 .data          0x02007ce4       0x48 obj\Debug\hal\src\hal_watermark.o
 .data          0x02007d2c        0x0 obj\Debug\hal\src\hal_wdt.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xmbox.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xmsgq.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xos.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xwork.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\audio\audio_playback.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\audio\audio_record.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\image\image_decode.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\image\image_encode.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\video\video_playback.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\video\video_record.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .data          0x02007d2c        0x0 obj\Debug\app\app_common\src\app_init.o
 .data          0x02007d2c        0x8 obj\Debug\app\app_common\src\app_lcdshow.o
 .data          0x02007d34        0x0 obj\Debug\app\app_common\src\main.o
 .data          0x02007d34      0xb7c obj\Debug\app\resource\user_res.o
                0x02007d34                User_Icon_Table
                0x02008154                User_String_Table
 .data          0x020088b0        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .data          0x020088b0       0x48 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
                0x020088b0                menuplayBack
                0x020088bc                menuPageplayBack
                0x020088d0                menuItemplayBack
 .data          0x020088f8        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .data          0x020088f8      0x2a0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                0x020088f8                menurecord
                0x02008904                menuPagerecord
                0x02008918                menuItemrecord
                0x020089b8                menuOptionversion
                0x020089c0                menuOptionscreenSave
                0x020089e0                menuOptionirLed
                0x020089f8                menuOptionfrequency
                0x02008a08                menuOptionlanguage
                0x02008a78                menuOptionautoPowerOff
                0x02008a90                menuOptionkeySound
                0x02008aa0                menuOptiongsensor
                0x02008ac0                menuOptiontimeStamp
                0x02008ad0                menuOptionparking
                0x02008ae0                menuOptionaudio
                0x02008af0                menuOptionmd
                0x02008b00                menuOptionev
                0x02008b28                menuOptionawb
                0x02008b50                menuOptionloopRecord
                0x02008b70                menuOptionphotoResolution
                0x02008b88                menuOptionvideoResolution
 .data          0x02008b98       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                0x02008b98                dateTimeWindow
                0x02008ba8                dateTimeMsgDeal
 .data          0x02008c08       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                0x02008c08                defaultWindow
                0x02008c18                defaultMsgDeal
 .data          0x02008c70       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                0x02008c70                delAllWindow
                0x02008c80                delAllMsgDeal
 .data          0x02008cd8       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                0x02008cd8                delCurWindow
                0x02008ce8                delCurMsgDeal
 .data          0x02008d40       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                0x02008d40                formatWindow
                0x02008d50                formatMsgDeal
 .data          0x02008da8       0x98 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x02008db0                menuItemWindow
                0x02008dc0                menuItemMsgDeal
 .data          0x02008e40       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                0x02008e40                lockCurWindow
                0x02008e50                lockCurMsgDeal
 .data          0x02008ea8       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                0x02008ea8                menuOptionWindow
                0x02008eb8                menuOptionMsgDeal
 .data          0x02008f18       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                0x02008f18                unlockAllWindow
                0x02008f28                unlockAllMsgDeal
 .data          0x02008f80       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                0x02008f80                unlockCurWindow
                0x02008f90                unlockCurMsgDeal
 .data          0x02008fe8        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .data          0x02008fe8       0x38 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                0x02008fe8                asternWindow
                0x02008ff8                asternMsgDeal
 .data          0x02009020       0x68 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                0x02009020                noFileWindow
                0x02009030                noFileMsgDeal
 .data          0x02009088       0x98 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                0x02009088                selfTestWindow
                0x02009098                selfTestMsgDeal
 .data          0x02009120       0xcc obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                0x02009120                tips1Window
                0x02009130                tips1MsgDeal
 .data          0x020091ec       0xcc obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                0x020091ec                tipsWindow
                0x020091fc                tipsMsgDeal
 .data          0x020092b8       0x5c obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                0x020092b8                takephotoiconWindow
                0x020092c8                TpIconMsgDeal
 .data          0x02009314        0x0 obj\Debug\app\task_windows\msg_api.o
 .data          0x02009314        0x0 obj\Debug\app\task_windows\task_api.o
 .data          0x02009314        0x0 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .data          0x02009314       0x4c obj\Debug\app\task_windows\task_common\src\task_common.o
 .data          0x02009360        0x0 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .data          0x02009360       0x14 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x02009360                taskPlayAudio
 .data          0x02009374       0x80 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                0x02009374                playAudioWindow
                0x02009384                playAudioMsgDeal
 .data          0x020093f4       0x14 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x020093f4                taskPlayVideo
 .data          0x02009408       0xf8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                0x02009430                playVideoMainWindow
                0x02009440                playVideoMainMsgDeal
 .data          0x02009500       0x9c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                0x0200950c                playVideoSlideWindow
                0x0200951c                playVideoSlideMsgDeal
 .data          0x0200959c       0x88 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                0x0200959c                playVideoThumbnallWindow
                0x020095ac                playVideoThumbnallMsgDeal
 .data          0x02009624       0x14 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
                0x02009624                taskPowerOff
 .data          0x02009638       0x14 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
                0x02009638                taskRecordAudio
 .data          0x0200964c       0x50 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                0x0200964c                RecordAudioWindow
                0x0200965c                recordAudioMsgDeal
 .data          0x0200969c       0x14 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x0200969c                taskRecordPhoto
 .data          0x020096b0      0x270 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                0x020096b0                recordPhotoWindow
                0x020096c0                photoEncodeMsgDeal
                0x02009790                recordPhotoWin
 .data          0x02009920       0x14 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                0x02009920                taskRecordVideo
 .data          0x02009934       0xe8 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                0x02009934                recordVideoWindow
                0x02009944                recordVideoMsgDeal
 .data          0x02009a1c       0x14 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02009a1c                taskSDUpdate
 .data          0x02009a30        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .data          0x02009a30       0x14 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
                0x02009a30                taskShowLogo
 .data          0x02009a44       0x30 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                0x02009a44                ShowLogoWindow
                0x02009a54                ShowLogoMsgDeal
 .data          0x02009a74       0x14 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                0x02009a74                taskUSBDevice
 .data          0x02009a88       0xb8 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                0x02009a88                usbDeviceWindow
                0x02009a98                usbDeviceMsgDeal
 .data          0x02009b40        0x0 obj\Debug\app\task_windows\windows_api.o
 .data          0x02009b40        0x8 obj\Debug\app\user_config\src\mbedtls_md5.o
                0x02009b40                MY_KEY
 .data          0x02009b48        0x0 obj\Debug\app\user_config\src\user_config_api.o
 .data          0x02009b48        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .data          0x02009b48        0x0 ..\lib\libboot.a(boot.o)
 .data          0x02009b48        0x0 ..\lib\libboot.a(boot_loader.o)
 .data          0x02009b48        0x0 ..\lib\libboot.a(reset.o)
 .data          0x02009b48        0x0 ..\lib\libboot.a(boot_lib.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_auadc.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_csi.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_dac.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_dma.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_dmauart.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_gpio.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_iic.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_int.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_isp.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .data          0x02009b48        0x0 ..\lib\libmcu.a(hx330x_jpg.o)
 .data          0x02009b48        0x1 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 *fill*         0x02009b49        0x3 
 .data          0x02009b4c        0x4 ..\lib\libmcu.a(hx330x_lcd.o)
 .data          0x02009b50        0x0 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .data          0x02009b50        0x8 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x02009b50                video_scanmode_tab
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_md.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_mipi.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_misc.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_rtc.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_sd.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_spi1.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_sys.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_timer.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_tminf.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_uart.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_usb.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_wdt.o)
 .data          0x02009b58        0x0 ..\lib\libmcu.a(hx330x_emi.o)
 .data          0x02009b58        0x4 ..\lib\libisp.a(hal_isp.o)
 .data          0x02009b5c        0x0 ..\lib\libjpg.a(hal_jpg.o)
 .data          0x02009b5c       0x40 ..\lib\liblcd.a(hal_lcd.o)
 .data          0x02009b9c        0x0 ..\lib\liblcd.a(hal_lcdMem.o)
 .data          0x02009b9c        0x0 ..\lib\liblcd.a(hal_lcdrotate.o)
 .data          0x02009b9c        0x0 ..\lib\liblcd.a(hal_lcdUi.o)
 .data          0x02009b9c        0x0 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .data          0x02009b9c        0x0 ..\lib\liblcd.a(lcd_tab.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(api_multimedia.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(avi_dec.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(avi_std_enc.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(wav_dec.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(wav_enc.o)
 .data          0x02009b9c        0x0 ..\lib\libmultimedia.a(wav_pcm.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
 .data          0x02009b9c        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.bss            0x02009b9c    0x12b2c load address 0x0000c79c
                0x02009b9c                __bss_start = .
 *(.bss*)
 .bss           0x02009b9c        0x8 obj\Debug\dev\battery\src\battery_api.o
 .bss           0x02009ba4        0x8 obj\Debug\dev\dev_api.o
 .bss           0x02009bac        0x0 obj\Debug\dev\fs\src\diskio.o
 .bss           0x02009bac      0x468 obj\Debug\dev\fs\src\ff.o
 .bss           0x0200a014        0x0 obj\Debug\dev\fs\src\ffunicode.o
 .bss           0x0200a014        0x0 obj\Debug\dev\fs\src\fs_api.o
 .bss           0x0200a014        0x4 obj\Debug\dev\gsensor\src\gsensor_api.o
 .bss           0x0200a018        0x0 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .bss           0x0200a018        0x0 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .bss           0x0200a018        0x0 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .bss           0x0200a018        0x4 obj\Debug\dev\ir\src\ir_api.o
 .bss           0x0200a01c       0x1c obj\Debug\dev\key\src\key_api.o
 .bss           0x0200a038       0xbc obj\Debug\dev\lcd\src\lcd_api.o
                0x0200a038                lcd_saj_nocolor
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .bss           0x0200a0f4        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .bss           0x0200a0f4        0x4 obj\Debug\dev\led\src\led_api.o
 .bss           0x0200a0f8        0x4 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .bss           0x0200a0fc       0x18 obj\Debug\dev\nvfs\src\nvfs_api.o
 .bss           0x0200a114       0x10 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .bss           0x0200a124       0x2c obj\Debug\dev\sd\src\sd_api.o
                0x0200a124                hal_sdc_speed
 .bss           0x0200a150     0x1218 obj\Debug\dev\sensor\src\sensor_api.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .bss           0x0200b368        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .bss           0x0200b368        0x8 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .bss           0x0200b370        0x0 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .bss           0x0200b370        0x4 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .bss           0x0200b374       0x1c obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .bss           0x0200b390        0x4 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .bss           0x0200b394        0x0 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .bss           0x0200b394        0x0 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .bss           0x0200b394        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .bss           0x0200b394        0x0 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .bss           0x0200b394        0x2 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 *fill*         0x0200b396        0x2 
 .bss           0x0200b398        0xc obj\Debug\dev\usb\husb\src\husb_api.o
 .bss           0x0200b3a4        0x0 obj\Debug\dev\usb\husb\src\husb_enum.o
 .bss           0x0200b3a4        0x0 obj\Debug\dev\usb\husb\src\husb_hub.o
 .bss           0x0200b3a4        0x0 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .bss           0x0200b3a4        0x4 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .bss           0x0200b3a8        0x0 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .bss           0x0200b3a8        0x0 obj\Debug\hal\src\hal_adc.o
 .bss           0x0200b3a8       0xc8 obj\Debug\hal\src\hal_auadc.o
                0x0200b3a8                auadccnt
 .bss           0x0200b470        0x4 obj\Debug\hal\src\hal_csi.o
 .bss           0x0200b474        0x8 obj\Debug\hal\src\hal_dac.o
 .bss           0x0200b47c       0x50 obj\Debug\hal\src\hal_dmauart.o
 .bss           0x0200b4cc        0x0 obj\Debug\hal\src\hal_eeprom.o
 .bss           0x0200b4cc        0x0 obj\Debug\hal\src\hal_gpio.o
 .bss           0x0200b4cc        0x1 obj\Debug\hal\src\hal_iic.o
 .bss           0x0200b4cd        0x0 obj\Debug\hal\src\hal_int.o
 *fill*         0x0200b4cd        0x3 
 .bss           0x0200b4d0        0x8 obj\Debug\hal\src\hal_lcdshow.o
 .bss           0x0200b4d8        0x4 obj\Debug\hal\src\hal_md.o
 .bss           0x0200b4dc      0x3b4 obj\Debug\hal\src\hal_mjpAEncode.o
 .bss           0x0200b890      0x3a0 obj\Debug\hal\src\hal_mjpBEncode.o
 .bss           0x0200bc30       0x74 obj\Debug\hal\src\hal_mjpDecode.o
 .bss           0x0200bca4       0x34 obj\Debug\hal\src\hal_rtc.o
 .bss           0x0200bcd8        0x8 obj\Debug\hal\src\hal_spi.o
                0x0200bcd8                spi_updata_led
 .bss           0x0200bce0       0x10 obj\Debug\hal\src\hal_spi1.o
 .bss           0x0200bcf0        0x0 obj\Debug\hal\src\hal_stream.o
 .bss           0x0200bcf0      0x50c obj\Debug\hal\src\hal_sys.o
 .bss           0x0200c1fc        0x0 obj\Debug\hal\src\hal_timer.o
 .bss           0x0200c1fc        0x8 obj\Debug\hal\src\hal_uart.o
 .bss           0x0200c204        0x8 obj\Debug\hal\src\hal_watermark.o
 .bss           0x0200c20c        0x0 obj\Debug\hal\src\hal_wdt.o
 .bss           0x0200c20c        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .bss           0x0200c20c        0x0 obj\Debug\mcu\xos\xmbox.o
 .bss           0x0200c20c        0x0 obj\Debug\mcu\xos\xmsgq.o
 .bss           0x0200c20c        0x8 obj\Debug\mcu\xos\xos.o
 .bss           0x0200c214       0x80 obj\Debug\mcu\xos\xwork.o
 .bss           0x0200c294       0x60 obj\Debug\multimedia\audio\audio_playback.o
 .bss           0x0200c2f4       0x48 obj\Debug\multimedia\audio\audio_record.o
 .bss           0x0200c33c        0x0 obj\Debug\multimedia\image\image_decode.o
 .bss           0x0200c33c        0x0 obj\Debug\multimedia\image\image_encode.o
 .bss           0x0200c33c        0x0 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .bss           0x0200c33c      0x1c8 obj\Debug\multimedia\video\video_playback.o
 .bss           0x0200c504        0x0 obj\Debug\multimedia\video\video_record.o
 .bss           0x0200c504        0xc obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .bss           0x0200c510        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .bss           0x0200c510      0x530 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .bss           0x0200ca40       0x94 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .bss           0x0200cad4        0x0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .bss           0x0200cad4        0x0 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .bss           0x0200cad4       0x60 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .bss           0x0200cb34        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .bss           0x0200cb34     0x5068 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .bss           0x02011b9c        0x4 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .bss           0x02011ba0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .bss           0x02011ba0       0x50 obj\Debug\app\app_common\src\app_init.o
 .bss           0x02011bf0       0x10 obj\Debug\app\app_common\src\app_lcdshow.o
 .bss           0x02011c00        0x0 obj\Debug\app\app_common\src\main.o
 .bss           0x02011c00        0x0 obj\Debug\app\resource\user_res.o
 .bss           0x02011c00        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .bss           0x02011c00        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .bss           0x02011c00        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .bss           0x02011c00        0x8 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                0x02011c00                menuOptionmemory
 .bss           0x02011c08       0x48 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .bss           0x02011c50        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .bss           0x02011c50        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .bss           0x02011c50        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .bss           0x02011c50        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .bss           0x02011c50        0x8 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
 .bss           0x02011c58        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .bss           0x02011c58        0x4 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .bss           0x02011c5c        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .bss           0x02011c5c        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .bss           0x02011c5c        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .bss           0x02011c5c        0x0 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .bss           0x02011c5c        0x0 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .bss           0x02011c5c        0xc obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .bss           0x02011c68        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .bss           0x02011c6c        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .bss           0x02011c70        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .bss           0x02011c74      0x128 obj\Debug\app\task_windows\msg_api.o
 .bss           0x02011d9c       0x3c obj\Debug\app\task_windows\task_api.o
 .bss           0x02011dd8        0x1 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 *fill*         0x02011dd9        0x3 
 .bss           0x02011ddc       0x3c obj\Debug\app\task_windows\task_common\src\task_common.o
 .bss           0x02011e18        0x0 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .bss           0x02011e18        0x0 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .bss           0x02011e18        0x4 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .bss           0x02011e1c        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .bss           0x02011e1c        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .bss           0x02011e1c        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .bss           0x02011e1c        0xc obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .bss           0x02011e28        0x0 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .bss           0x02011e28        0x0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .bss           0x02011e28        0x0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .bss           0x02011e28        0x0 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .bss           0x02011e28       0x18 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .bss           0x02011e40        0x0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
 .bss           0x02011e40       0x18 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .bss           0x02011e58        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .bss           0x02011e58        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .bss           0x02011e58        0x0 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .bss           0x02011e58        0x1 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .bss           0x02011e59        0x0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .bss           0x02011e59        0x0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .bss           0x02011e59        0x0 obj\Debug\app\task_windows\windows_api.o
 .bss           0x02011e59        0x0 obj\Debug\app\user_config\src\mbedtls_md5.o
 *fill*         0x02011e59        0x3 
 .bss           0x02011e5c      0x20c obj\Debug\app\user_config\src\user_config_api.o
 .bss           0x02012068        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .bss           0x02012068        0x0 ..\lib\libboot.a(boot.o)
 .bss           0x02012068        0x0 ..\lib\libboot.a(boot_loader.o)
 .bss           0x02012068        0x0 ..\lib\libboot.a(reset.o)
 .bss           0x02012068        0x0 ..\lib\libboot.a(boot_lib.o)
 .bss           0x02012068        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .bss           0x02012068        0x8 ..\lib\libmcu.a(hx330x_auadc.o)
 .bss           0x02012070       0x3c ..\lib\libmcu.a(hx330x_csi.o)
 .bss           0x020120ac        0x4 ..\lib\libmcu.a(hx330x_dac.o)
 .bss           0x020120b0        0x0 ..\lib\libmcu.a(hx330x_dma.o)
 .bss           0x020120b0        0x4 ..\lib\libmcu.a(hx330x_dmauart.o)
 .bss           0x020120b4       0x6c ..\lib\libmcu.a(hx330x_gpio.o)
 .bss           0x02012120        0x0 ..\lib\libmcu.a(hx330x_iic.o)
 .bss           0x02012120        0x0 ..\lib\libmcu.a(hx330x_int.o)
 .bss           0x02012120        0xc ..\lib\libmcu.a(hx330x_isp.o)
                0x02012120                isp_ccf_dn_tab
                0x02012124                isp_ee_dn_tab
                0x02012128                isp_ee_sharp_tab
 .bss           0x0201212c        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .bss           0x0201212c       0x54 ..\lib\libmcu.a(hx330x_jpg.o)
                0x0201212c                mjpBEncAvgSize
                0x02012130                mjpAEncAvgSize
 .bss           0x02012180        0x0 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 .bss           0x02012180        0x0 ..\lib\libmcu.a(hx330x_lcd.o)
 .bss           0x02012180        0x4 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .bss           0x02012184        0x8 ..\lib\libmcu.a(hx330x_lcdui.o)
 .bss           0x0201218c        0x4 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .bss           0x02012190        0x0 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .bss           0x02012190        0x0 ..\lib\libmcu.a(hx330x_md.o)
 .bss           0x02012190        0x0 ..\lib\libmcu.a(hx330x_mipi.o)
 .bss           0x02012190        0x0 ..\lib\libmcu.a(hx330x_misc.o)
 .bss           0x02012190        0x4 ..\lib\libmcu.a(hx330x_rtc.o)
                0x02012190                rtcAlarmFlag
 .bss           0x02012194       0x10 ..\lib\libmcu.a(hx330x_sd.o)
 .bss           0x020121a4        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .bss           0x020121a4        0x4 ..\lib\libmcu.a(hx330x_spi1.o)
 .bss           0x020121a8        0x4 ..\lib\libmcu.a(hx330x_sys.o)
                0x020121a8                mcp1_lock
 .bss           0x020121ac        0x0 ..\lib\libmcu.a(hx330x_timer.o)
 .bss           0x020121ac       0x28 ..\lib\libmcu.a(hx330x_tminf.o)
 .bss           0x020121d4        0x4 ..\lib\libmcu.a(hx330x_uart.o)
 .bss           0x020121d8       0x7c ..\lib\libmcu.a(hx330x_usb.o)
 .bss           0x02012254        0x0 ..\lib\libmcu.a(hx330x_wdt.o)
 .bss           0x02012254        0x4 ..\lib\libmcu.a(hx330x_emi.o)
 .bss           0x02012258       0xe0 ..\lib\libisp.a(hal_isp.o)
 .bss           0x02012338       0x4c ..\lib\libjpg.a(hal_jpg.o)
 .bss           0x02012384       0x16 ..\lib\liblcd.a(hal_lcd.o)
 .bss           0x0201239a        0x0 ..\lib\liblcd.a(hal_lcdMem.o)
 *fill*         0x0201239a        0x2 
 .bss           0x0201239c       0x28 ..\lib\liblcd.a(hal_lcdrotate.o)
 .bss           0x020123c4       0x28 ..\lib\liblcd.a(hal_lcdUi.o)
 .bss           0x020123ec        0x8 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .bss           0x020123f4        0x0 ..\lib\liblcd.a(lcd_tab.o)
 .bss           0x020123f4       0x60 ..\lib\libmultimedia.a(api_multimedia.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(avi_dec.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(avi_std_enc.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(wav_dec.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(wav_enc.o)
 .bss           0x02012454        0x0 ..\lib\libmultimedia.a(wav_pcm.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
 .bss           0x02012454        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
 *(COMMON)
 COMMON         0x02012454       0x1c obj\Debug\dev\battery\src\battery_api.o
                0x02012454                WAV_TYPE_E
                0x02012458                USB_CH
                0x0201245c                UVC_CACHE_STA
                0x02012460                SDCON0_T
                0x02012464                SDCON1_T
                0x02012468                UVC_FSTACK_STA
                0x0201246c                CHANNEL_EXCHANGE_E
 COMMON         0x02012470       0x14 obj\Debug\dev\fs\src\fs_api.o
                0x02012470                fs_exfunc
 COMMON         0x02012484        0x8 obj\Debug\dev\gsensor\src\gsensor_api.o
                0x02012484                gsensor_ctl
 COMMON         0x0201248c       0x78 obj\Debug\dev\key\src\key_api.o
                0x0201248c                dev_key_tab
 COMMON         0x02012504       0x20 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                0x02012504                tp_api_t
 COMMON         0x02012524      0x29c obj\Debug\dev\usb\dusb\src\dusb_api.o
                0x02012524                usb_dev_ctl
 COMMON         0x020127c0     0x1484 obj\Debug\dev\usb\husb\src\husb_api.o
                0x020127c0                husb_ctl
 COMMON         0x02013c44        0x4 obj\Debug\dev\usb\husb\src\husb_hub.o
                0x02013c44                uhub_handle
 COMMON         0x02013c48        0x4 obj\Debug\dev\usb\husb\src\husb_usensor.o
                0x02013c48                usensor_handle
 COMMON         0x02013c4c        0x4 obj\Debug\mcu\xos\xos.o
                0x02013c4c                XOSNesting
 COMMON         0x02013c50       0x74 obj\Debug\multimedia\video\video_record.o
                0x02013c50                mediaVideoCtl
 COMMON         0x02013cc4       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                0x02013cc4                ui_draw_ctrl
 COMMON         0x02013cd4      0x130 obj\Debug\app\app_common\src\app_init.o
                0x02013cd4                SysCtrl
 COMMON         0x02013e04        0x4 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x02013e04                task_play_audio_stat
 COMMON         0x02013e08       0x44 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x02013e08                playVideoOp
 COMMON         0x02013e4c       0x20 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x02013e4c                recordPhotoOp
 COMMON         0x02013e6c       0x64 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02013e6c                sd_update_op
 COMMON         0x02013ed0        0xc obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                0x02013ed0                usbDeviceOp
 COMMON         0x02013edc      0x500 ..\lib\libmcu.a(hx330x_isp.o)
                0x02013edc                RGB_GMMA_Tab
                0x020141dc                Y_GMA_Tab
 COMMON         0x020143dc       0x14 ..\lib\libmcu.a(hx330x_lcd.o)
                0x020143dc                hx330x_lcdISR
 COMMON         0x020143f0       0x14 ..\lib\libmcu.a(hx330x_rtc.o)
                0x020143f0                rc128k_div
                0x020143f4                rtcSecondISR
                0x020143f8                rtcAlamISR
                0x020143fc                rc128k_rtc_cnt
                0x02014400                rc128k_timer_cnt
 COMMON         0x02014404        0x4 ..\lib\libmcu.a(hx330x_sys.o)
                0x02014404                smph_dmacopy
 COMMON         0x02014408       0x10 ..\lib\libmcu.a(hx330x_timer.o)
                0x02014408                hx330x_timerISR
 COMMON         0x02014418       0x84 ..\lib\liblcd.a(hal_lcd.o)
                0x02014418                lcd_show_ctrl
 COMMON         0x0201449c      0x1e8 ..\lib\liblcd.a(hal_lcdMem.o)
                0x0201449c                lcdshow_frame_op
 *(.big_buffer*)
 *(._sdram_buf_)
 ._sdram_buf_   0x02014684     0x8044 obj\Debug\dev\fs\src\fs_api.o
                0x02014684                work_fatfs
                0x0201c700                _sdram_remian_addr = (ALIGN (0x40) + 0x0)
                0x0000c800                _text_lma = (((LOADADDR (.on_sdram) + SIZEOF (.on_sdram)) + 0x1ff) & 0xfffffe00)
                0x0600c800                _text_vma = (ORIGIN (flash) + _text_lma)

.text           0x0600c800    0x8943c load address 0x0000c800
 *(.text*)
 .text          0x0600c800      0x244 obj\Debug\dev\battery\src\battery_api.o
                0x0600c800                dev_battery_ioctrl
                0x0600c984                dev_battery_init
 .text          0x0600ca44      0x254 obj\Debug\dev\dev_api.o
                0x0600ca44                dev_api_node_init
                0x0600cb68                dev_open
                0x0600cc28                dev_ioctrl
 .text          0x0600cc98      0x244 obj\Debug\dev\fs\src\diskio.o
                0x0600cc98                get_fattime
                0x0600cd18                disk_status
                0x0600cd68                disk_initialize
                0x0600cdbc                disk_read
                0x0600ce18                disk_write
                0x0600ce74                disk_ioctl
 .text          0x0600cedc     0x8084 obj\Debug\dev\fs\src\ff.o
                0x06010cc4                f_mount
                0x06010d74                f_open
                0x060112c4                f_read
                0x060115fc                f_write
                0x0601198c                f_sync
                0x06011bf0                f_close
                0x06011c3c                f_ftime
                0x06011c98                f_lseek
                0x0601241c                f_opendir
                0x06012574                f_closedir
                0x060125b0                f_readdir
                0x06012654                f_findnext
                0x060126f8                f_findfirst
                0x0601274c                f_stat
                0x060127e0                f_getfree
                0x06012b5c                f_truncate
                0x06012cb4                f_unlink
                0x06012e68                f_mkdir
                0x06013158                f_rename
                0x0601343c                f_chmod
                0x06013524                f_utime
                0x06013608                f_expand
                0x060138ec                f_mkfs
                0x06014be4                FEX_getlink_clust
                0x06014c1c                f_merge
                0x06014dcc                _f_bound
 .text.unlikely
                0x06014f60       0x30 obj\Debug\dev\fs\src\ff.o
 .text          0x06014f90      0x204 obj\Debug\dev\fs\src\ffunicode.o
                0x06014f90                ff_uni2oem
                0x06015008                ff_oem2uni
                0x06015080                ff_wtoupper
 .text          0x06015194      0xa2c obj\Debug\dev\fs\src\fs_api.o
                0x06015194                fs_exfunc_init
                0x060151ec                fs_nodeinit
                0x0601521c                fs_mount
                0x06015318                fs_open
                0x060153e8                fs_close
                0x06015460                fs_read
                0x060154d8                fs_write
                0x060155c8                fs_seek
                0x06015694                fs_getcltbl
                0x060156e4                fs_getclusize
                0x06015734                fs_mkdir
                0x06015760                fs_alloc
                0x060157d4                fs_sync
                0x0601583c                fs_merge
                0x060158c8                fs_bound
                0x06015954                fs_getclustersize
                0x06015978                fs_size
                0x060159c4                fs_pre_size
                0x06015a10                fs_tell
                0x06015a5c                fs_free_size
                0x06015ae0                fs_check
                0x06015b08                fs_getStartSector
                0x06015b64                fs_ftime
 .text          0x06015bc0      0x36c obj\Debug\dev\gsensor\src\gsensor_api.o
                0x06015bc0                gsensor_iic_enable
                0x06015be4                gsensor_iic_disable
                0x06015c04                gSensorGetName
                0x06015c38                dev_gSensor_Init
                0x06015d58                dev_gSensor_ioctrl
 .text          0x06015f2c      0x550 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .text          0x0601647c      0x80c obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .text          0x06016c88      0x4fc obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .text          0x06017184      0x12c obj\Debug\dev\ir\src\ir_api.o
                0x06017184                dev_ir_init
                0x060171cc                dev_ir_ioctrl
 .text          0x060172b0      0x624 obj\Debug\dev\key\src\key_api.o
                0x060172b0                dev_key_init
                0x06017404                dev_key_ioctrl
                0x06017870                getKeyADCvalue
                0x06017890                getKeyCurEvent
                0x060178b0                keyLongTypeScanModeSet
 .text          0x060178d4      0x318 obj\Debug\dev\lcd\src\lcd_api.o
                0x060178d4                lcd_initTab_config
                0x060179e4                LcdGetName
                0x06017a00                dev_lcd_init
                0x06017ab0                dev_lcd_ioctrl
                0x06017bcc                dev_lcd_nocolor_status
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .text          0x06017bec      0x104 obj\Debug\dev\led\src\led_api.o
                0x06017bec                dev_led_init
                0x06017c04                dev_led_ioctrl
 .text          0x06017cf0       0xf0 obj\Debug\dev\led_pwm\src\led_pwm_api.o
                0x06017cf0                dev_led_pwm_init
                0x06017d08                dev_led_pwm_ioctrl
 .text          0x06017de0      0x30c obj\Debug\dev\nvfs\src\nvfs_api.o
                0x06017de0                nv_port_read
                0x06017e00                nv_init
                0x06017f30                nv_uninit
                0x06017f48                nv_configAddr
                0x06017fc0                nv_open
                0x0601803c                nv_size
                0x060180bc                nv_read
 .text          0x060180ec     0x1f0c obj\Debug\dev\nvfs\src\nvfs_jpg.o
                0x06018974                nv_dir_read
                0x06018ab8                nv_dir_readfirst
                0x06018b28                nv_dir_readnext
                0x06018b7c                nv_jpg_ex_force_init
                0x06018b9c                nv_jpg_ex_init
                0x06018c78                nv_jpg_formart_init
                0x06018e48                nv_jpg_init
                0x06019088                nv_jpg_uinit
                0x060190f0                nv_jpg_format
                0x06019180                nv_jpg_open
                0x060194b8                nv_jpg_change_lock
                0x06019594                nv_jpg_close
                0x0601966c                nv_jpgfile_read
                0x0601986c                nv_jpgfile_seek
                0x06019a88                nv_jpgfile_delete
                0x06019b80                nv_jpgfile_size
                0x06019bc0                nvjpg_free_size
                0x06019bf4                nv_jpgfile_write
                0x06019fc8                nvjpg_free_dir
 .text          0x06019ff8      0xc28 obj\Debug\dev\sd\src\sd_api.o
                0x0601a78c                sd_api_init
                0x0601a9d8                sd_api_getNextLBA
                0x0601a9f8                sd_api_Uninit
                0x0601aa18                sd_api_lock
                0x0601aa58                sd_api_unlock
                0x0601aa84                sd_api_CardState_Set
                0x0601aaa4                sd_api_CardState_Get
                0x0601aac4                sd_api_GetBusWidth
                0x0601aae4                sd_api_Capacity
                0x0601ab04                sd_api_speed_debg
                0x0601ab50                dev_sdc_init
                0x0601ab80                dev_sdc_ioctrl
 .text          0x0601ac20      0x708 obj\Debug\dev\sensor\src\sensor_api.o
                0x0601ac20                sensor_iic_write
                0x0601ac8c                sensor_iic_read
                0x0601ad0c                sensor_rgbgamma_tab_load
                0x0601add4                sensor_ygamma_tab_load
                0x0601ae9c                sensor_lsc_tab_load
                0x0601aeec                SensorGetName
                0x0601af08                dev_sensor_init
                0x0601af20                dev_sensor_ioctrl
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .text          0x0601b328      0x504 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                0x0601b388                dev_touchpanel_Init
                0x0601b524                dev_touchpanel_ioctrl
 .text          0x0601b82c      0x1ec obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                0x0601b82c                tp_icnt81_getPoint
 .text          0x0601ba18      0x74c obj\Debug\dev\touchpanel\src\touchpanel_iic.o
                0x0601be5c                tp_iic_init
                0x0601bed0                tp_iic_config
                0x0601bef4                tp_iic_write
                0x0601c004                tp_iic_read
 .text          0x0601c164      0x374 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                0x0601c164                tp_ns2009_Match
                0x0601c208                tp_ns2009_getPoint
 .text          0x0601c4d8      0x3f4 obj\Debug\dev\usb\dusb\src\dusb_api.o
                0x0601c530                dusb_api_online
                0x0601c550                dusb_api_Init
                0x0601c64c                dusb_api_Uninit
                0x0601c68c                dusb_api_offline
                0x0601c6c4                dusb_api_Process
                0x0601c734                dev_dusb_init
                0x0601c770                dev_dusb_ioctrl
 .text          0x0601c8cc      0xb50 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x0601ca74                dusb_stall_ep
                0x0601cb00                dusb_ep0_tx
                0x0601cb7c                dusb_ep0_recieve_set
                0x0601cbb4                dusb_ep0_process
                0x0601d308                dusb_ep0_cfg
                0x0601d35c                dusb_cfg_reg
 .text          0x0601d41c     0x1150 obj\Debug\dev\usb\dusb\src\dusb_msc.o
                0x0601d594                msc_epx_cfg
                0x0601d630                dusb_WriteToMem
                0x0601d670                dusb_ReadFromMem
                0x0601d694                rbc_mem_read
                0x0601d704                rbc_mem_write
                0x0601d77c                rbc_mem_rxfunc
                0x0601d7b8                sdk_returnmask
                0x0601d8f4                cbw_updatartc
                0x0601d93c                mscCmd_ufmod
                0x0601d988                sent_csw
                0x0601da64                mscCmd_Read
                0x0601db64                mscCmd_Write
                0x0601dc64                scsi_cmd_analysis
                0x0601e1c0                get_cbw
                0x0601e40c                rbc_rec_pkg
                0x0601e4ac                rbc_process
 .text          0x0601e56c        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .text          0x0601e56c      0x5a4 obj\Debug\dev\usb\dusb\src\dusb_uac.o
                0x0601e56c                uac_set_volume
                0x0601e5b8                uac_set_mute
                0x0601e600                uac_isr_process
                0x0601e720                uac_epx_cfg
                0x0601e794                uac_get_volume
                0x0601e824                uac_unit_ctl_hal
                0x0601e8f8                UacHandleToStreaming
                0x0601e994                uac_start
                0x0601ea58                UacReceiveSetSamplingFreqCallback
                0x0601eab4                uac_stop
 .text          0x0601eb10      0x8f0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                0x0601eb10                uvc_pic_callback
                0x0601eb38                unitsel_set
                0x0601eb74                uvc_video_probe_control_callback
                0x0601eba8                uvc_still_probe_control_callback
                0x0601ebdc                uvc_epx_cfg
                0x0601ed30                uvc_unit_ctl_hal
                0x0601edd8                uvc_video_probe_control
                0x0601ee20                uvc_still_probe_control
                0x0601ee68                uvc_still_trigger_control
                0x0601ee9c                uvc_probe_ctl_hal
                0x0601ef18                uvc_start
                0x0601efbc                uvc_stop
                0x0601f020                uvc_is_start
                0x0601f040                uvc_pic_sanp
                0x0601f060                uvc_header_fill
                0x0601f148                uvc_isr_process
                0x0601f2f4                uvc_process
 .text          0x0601f400      0x8f0 obj\Debug\dev\usb\husb\src\husb_api.o
                0x0601f400                husb_api_u20_remove
                0x0601f48c                husb_api_u11_remove
                0x0601f728                husb_api_handle_get
                0x0601f758                husb_api_init
                0x0601f8b4                husb_api_devicesta
                0x0601f8e8                husb_api_devicesta_set
                0x0601f93c                husb_api_msc_try_tran
                0x0601f978                dev_husb_io_power_set
                0x0601fa60                dev_husb_init
                0x0601fb24                dev_husb_ioctrl
 .text          0x0601fcf0     0x2970 obj\Debug\dev\usb\husb\src\husb_enum.o
                0x060201b4                husb_hub_clrport_feature_ack
                0x06020214                husb_hub_set_port_devaddr_ack
                0x060203c0                husb_get_pcommit_cs100_ack
                0x060204a4                husb_uvcunit_ack
                0x06020bb8                husb_setport_feature_ack
                0x06020be0                husb_hub_port_reset_ack
                0x06020c08                husb_get_max_lun_ack
                0x06020c3c                husb_set_intfs_uvc_1_ack
                0x06020c60                husb_hub_inf_ack
                0x06020e14                husb_hub_getport_status_ack
                0x06020eb4                husb_set_pcommit_cs200_ack
                0x06020ef0                husb20_ep0_cfg
                0x06020f10                husb11_ep0_cfg
                0x06020f30                usensor_resolution_select
                0x0602125c                husb_all_get_cfg_desc_ack
                0x06021dec                husb_astern_check
                0x06021e28                husb_astern_ack
                0x06021eac                husb_api_ep0_kick
                0x06021f8c                husb_api_ep0_process
                0x060220d4                husb_api_ep0_uvc_switch_kick
                0x06022110                husb_api_ep0_asterncheck_kick
                0x0602215c                husb_api_uvcunit_get_kick
                0x060222f0                husb_api_uvcunit_get_done
                0x060223a4                husb_api_uvcunit_set_kick
                0x06022540                husb_api_uvcunit_set_done
                0x060225e8                husb_api_hub_check_kick
 .text          0x06022660      0x230 obj\Debug\dev\usb\husb\src\husb_hub.o
                0x06022660                husb_hub_init
                0x06022804                husb_hub_uinit
 .text          0x06022890     0x1138 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
                0x06022890                udisk_cap
                0x060228b4                udisk_online
                0x060228e4                husb_msc_init
                0x06022a70                epbulk20_send_dat
                0x06022c34                epbulk20_recieve_dat
                0x06022da4                epbulk11_send_dat
                0x06022f44                epbulk11_recieve_dat
                0x060230a8                epbulk_send_dat
                0x060230f8                epbulk_receive_dat
                0x06023148                cbw_init
                0x06023194                rbc_read_lba
                0x06023298                rbc_write_lba
                0x0602338c                spc_inquiry
                0x06023440                spc_test_unit_rdy
                0x060234e0                spc_request_sense
                0x06023594                rbc_read_capacity
                0x06023724                spc_StartStopUnit
                0x060237ac                enum_mass_dev
 .text          0x060239c8      0x7c4 obj\Debug\dev\usb\husb\src\husb_usensor.o
                0x060239c8                husb_api_handle_reg
                0x060239e8                husb_api_usensor_tran_sta
                0x06023a20                husb_api_usensor_atech_sta
                0x06023a58                husb_api_usensor_res_get
                0x06023aa0                husb_api_usensor_res_type_is_mjp
                0x06023adc                husb_api_usensor_res_type_is_yuv
                0x06023b18                husb_api_astern_set
                0x06023b78                husb_api_astern_get
                0x06023bb0                husb_api_detech_check
                0x06023be8                husb_api_usensor_linkingLcd
                0x06023c14                husb_api_usensor_relinkLcd_reg
                0x06023c40                husb_api_usensor_dcdown
                0x06023c78                husb_api_usensor_detech
                0x06023cec                husb_api_usensor_asterncheck
                0x06023d20                husb_api_usensor_asternset
                0x06023d7c                husb_api_usensor_frame_read
                0x06023de8                husb_api_usensor_csi_kick
                0x06023e58                husb_api_usensor_switch_res_kick
                0x06023fe4                husb_api_usensor_notran_check
                0x060240b0                husb_api_usensor_stop_fill
                0x060240e0                husb_api_usensor_uvcunit_get
                0x0602413c                husb_api_usensor_uvcunit_set
 .text          0x0602418c      0x738 obj\Debug\dev\usb\husb\src\husb_uvc.o
                0x060243a8                husb_uvc_atech_codec
                0x06024478                husb_uvc_dcd_cache_get
                0x0602449c                husb_uvc_init
                0x060246bc                husb_uvc_linking
                0x06024780                husb_uvc_relink_register
                0x060247b0                husb_uvc_detech
                0x060247f8                husb_uvc_stop_fill
 .text          0x060248c4       0xcc obj\Debug\hal\src\hal_adc.o
                0x060248c4                hal_adcInit
                0x06024904                hal_adcRead
                0x06024938                hal_adcSetChannel
 .text          0x06024990      0x784 obj\Debug\hal\src\hal_auadc.o
                0x06024c48                hal_auadc_stamp_out
                0x06024c68                hal_auadc_stamp_next
                0x06024c88                hal_auadcInit
                0x06024cc8                hal_auadcMemInit
                0x06024d78                hal_auadc_pcmsize_get
                0x06024d98                hal_auadcMemUninit
                0x06024df4                hal_auadc_cnt
                0x06024e20                hal_auadcmutebuf_get
                0x06024e40                hal_auadcStart
                0x06024fe0                hal_auadcBufferGet
                0x0602504c                hal_auadcBufferRelease
                0x06025074                hal_auadcStop
                0x060250b4                hal_adcBuffer_prefull
                0x060250f4                hal_adc_volume_set
 .text          0x06025114      0x4fc obj\Debug\hal\src\hal_csi.o
                0x06025414                hal_csi_init
                0x06025438                hal_csi_input_switch
                0x0602556c                hal_csi_test_fps_adj
 .text          0x06025610      0x1f0 obj\Debug\hal\src\hal_dac.o
                0x06025610                hal_dacInit
                0x06025674                hal_dacPlayInit
                0x060256fc                hal_dacPlayStart
                0x06025758                hal_dacPlayStop
                0x06025780                hal_dacSetVolume
                0x060257e0                hal_dacCallBackRegister
 .text          0x06025800      0x42c obj\Debug\hal\src\hal_dmauart.o
                0x06025800                hal_dmaUartRxOverWait
                0x06025894                hal_dmaUartRxDataOut
                0x06025930                hal_dmaUartIRQHandler
                0x060259b8                hal_dmauartInit
                0x06025a60                hal_dmauartTxDma
                0x06025ac0                hal_dmauartTxDmaKick
                0x06025b18                hal_dmauartTest
 .text          0x06025c2c        0x0 obj\Debug\hal\src\hal_eeprom.o
 .text          0x06025c2c       0xa8 obj\Debug\hal\src\hal_gpio.o
                0x06025c2c                hal_gpioInit_io1d1
 .text          0x06025cd4      0xa50 obj\Debug\hal\src\hal_iic.o
                0x06025cd4                hal_iic0Init
                0x06025d04                hal_iic0Uninit
                0x06025d24                hal_iic08bitAddrWriteData
                0x06025d90                hal_iic08bitAddrReadData
                0x06025e18                hal_iic08bitAddrWrite
                0x06025ea8                hal_iic08bitAddrRead
                0x06025f48                hal_iic016bitAddrWriteData
                0x06025fc0                hal_iic016bitAddrReadData
                0x06026054                hal_iic016bitAddrWrite
                0x060260f0                hal_iic016bitAddrRead
                0x0602619c                hal_iic1IOShare
                0x060261cc                hal_iic1IOShareCheck
                0x06026214                hal_iic1Init
                0x06026254                hal_iic1Uninit
                0x0602628c                hal_iic18bitAddrWriteData
                0x060262fc                hal_iic18bitAddrReadData
                0x06026388                hal_iic18bitAddrWrite
                0x0602641c                hal_iic18bitAddrRead
                0x060264c0                hal_iic116bitAddrWriteData
                0x0602653c                hal_iic116bitAddrReadData
                0x060265d4                hal_iic116bitAddrWrite
                0x06026674                hal_iic116bitAddrRead
 .text          0x06026724       0x34 obj\Debug\hal\src\hal_int.o
                0x06026724                hal_intInit
 .text          0x06026758     0x18cc obj\Debug\hal\src\hal_lcdshow.o
                0x060267ec                hal_lcdSetCsiScaler
                0x060268a0                hal_lcdSetVideoScaler
                0x06026910                hal_lcd_scaler_done_check
                0x06026968                hal_lcdVideoSetRotate
                0x06026a60                hal_lcdUiSetRotate
                0x06026ac0                hal_lcdSetRatio
                0x06026cfc                hal_lcdSetBufYUV
                0x06026d90                hal_lcdSetBufYUV_2
                0x06026e14                hal_lcdSetWINAB
                0x06026ee8                hal_lcdWinEnablePreSet
                0x06026f08                hal_lcdSetWinEnable
                0x06026f40                lcd_struct_get
                0x06026f60                hal_lcdLCMPowerOff
                0x06026fe4                hal_lcd_decwin_done
                0x060272f0                hal_lcd_encwin_done
                0x060273a0                hal_lcd_frame_enc_func_register
                0x060273f0                hal_CSI_lcdFrameEndCallback
                0x06027838                hal_lcd_fps_debg
                0x06027880                hal_lcdGetSreenResolution
                0x06027908                hal_lcdGetUiResolution
                0x06027964                hal_lcdGetUiPosition
                0x060279c0                hal_lcdUiScanModeGet
                0x060279f0                hal_lcdGetVideoRatioResolution
                0x06027a4c                hal_lcdSetVideoRatioResolution
                0x06027a84                hal_lcdGetVideoRatioPos
                0x06027ae0                hal_lcdGetVideoResolution
                0x06027b3c                hal_lcdGetVideoPos
                0x06027b98                hal_lcdVideoScanModeGet
                0x06027bc8                hal_lcdVideoScalerTypeAdj
                0x06027c80                hal_lcd_enc_start
                0x06027d14                hal_lcd_enc_stop
                0x06027d80                hal_lcd_enc_checkdone
                0x06027dac                hal_lcd_enc_frame_get
                0x06027dec                hal_lcd_enc_frame_res_get
                0x06027e30                hal_lcd_pause_set
                0x06027e84                hal_lcd_pause_sta_get
                0x06027ed4                hal_lcdSetGamma
 .text          0x06028024      0x154 obj\Debug\hal\src\hal_md.o
                0x0602804c                hal_mdInit
                0x060280f8                hal_mdEnable
                0x06028140                hal_mdCheck
 .text          0x06028178     0x1dc0 obj\Debug\hal\src\hal_mjpAEncode.o
                0x0602823c                hal_mjpA_Start
                0x0602826c                hal_mjpA_Restart
                0x060284d4                hal_mjpA_Sizecalculate
                0x06028500                hal_mjpA_EncState
                0x06028b38                hal_jA_fcnt_mnt
                0x06028b8c                hal_mjpA_Linebuf_nocolor_change
                0x06028e0c                hal_mjpAEncodePhotoResumePKG
                0x06028ecc                hal_mjpAEncodePhotoResumeLLPKG
                0x06028f7c                hal_mjpAEncodePhotoResumeRam
                0x06029078                hal_mjpA_EncodeInit
                0x060290d4                hal_mjpA_LineBuf_get
                0x06029100                hal_mjpA_src_res_get
                0x06029134                hal_mjpA_buf_MenInit
                0x060291e4                hal_mjpA_linebufUninit
                0x0602922c                hal_mjpA_MemUninit
                0x0602927c                hal_mjpA_EncodeUninit
                0x06029308                hal_mjpA_EncVideo_Start
                0x06029694                hal_mjpA_EncPhoto_Start
                0x06029a28                hal_mjpA_EncPhotoLcd_Start
                0x06029cd8                hal_mjpA_photo_encode_mode
                0x06029cf8                hal_mjpA_RawBufferfree
                0x06029d20                hal_mjpA_RawBufferGet
                0x06029e1c                hal_mjpA_RkgBufferGet
                0x06029ec0                hal_mjpA_Buffer_prefull
                0x06029efc                hal_mjpA_Buffer_halffull
 .text          0x06029f38      0x85c obj\Debug\hal\src\hal_mjpBEncode.o
                0x06029f38                hal_mjpBEncodeKickManual
                0x0602a030                hal_mjpB_Sizecalculate
                0x0602a058                hal_jB_fcnt_mnt
                0x0602a0ac                hal_mjpBEnc_state
                0x0602a0d8                hal_mjpBEncodeDoneCfg
                0x0602a0fc                hal_mjpBEncodeDoneManual
                0x0602a20c                hal_mjpB_LineBuf_MenInit
                0x0602a2a8                hal_mjpB_LineBuf_cfg
                0x0602a2cc                hal_mjpB_buf_MenInit
                0x0602a384                hal_mjpB_MemUninit
                0x0602a3e4                hal_mjpB_usb_resolution_set
                0x0602a408                hal_mjpB_DecodeODMA1En
                0x0602a44c                hal_mjpB_Enc_Start
                0x0602a5c4                hal_mjpB_Enc_Stop
                0x0602a5f8                hal_mjpB_RawBufferfree
                0x0602a620                hal_mjpB_RawBufferGet
                0x0602a71c                hal_mjpB_Buffer_prefull
                0x0602a758                hal_mjpB_Buffer_halffull
 .text          0x0602a794      0xe50 obj\Debug\hal\src\hal_mjpDecode.o
                0x0602a794                hal_mjpHeaderParse
                0x0602ae44                hal_mjpDecodeIsYUV422
                0x0602ae64                hal_mjpDecodeGetResolution
                0x0602aea8                hal_mjpDecodeSetResolution
                0x0602aecc                hal_mjpDecodeBusyCheck
                0x0602aeec                hal_mjpDecodeErrorCheck
                0x0602af0c                hal_mjpDecodeStop
                0x0602af2c                hal_mjpDecodeReset
                0x0602af58                hal_mjpDecodePicture
                0x0602afe8                hal_mjpegDecodePicture_noisr
                0x0602b074                hal_mjpegDecodePicture_packet
                0x0602b124                hal_mjpDecodeParse
                0x0602b154                hal_mjpDecodeOneFrame
                0x0602b1cc                hal_mjpDecodeOneFrame_Ext
                0x0602b264                hal_mjpDecodeRestart_Ext
                0x0602b2d0                hal_mjpDecodeOneFrame_Fast
                0x0602b3cc                hal_mjpDecodeMiniSize
                0x0602b550                hal_mjpDecodeODma1Cfg
                0x0602b574                hal_BackRecDecodeStatusCheck
 .text          0x0602b5e4      0xc80 obj\Debug\hal\src\hal_rtc.o
                0x0602b610                hal_rtcCallBackRegister
                0x0602b670                hal_rtcCallBackRelease
                0x0602b6ac                hal_rtcUninit
                0x0602b6e8                hal_rtcTimeGet
                0x0602b704                hal_rtcTimeGetExt
                0x0602b738                hal_rtcSecondGet
                0x0602b758                hal_rtcTime2String
                0x0602ba0c                hal_rtcTime2StringExt
                0x0602ba70                hal_rtcLeapYear
                0x0602bc90                hal_rtcTime
                0x0602bda4                hal_rtcInit
                0x0602bee4                hal_rtcSecondSet
                0x0602bf20                hal_rtcValue
                0x0602c030                hal_rtcTimeSet
                0x0602c0d4                hal_rtcAlarmSet
                0x0602c164                hal_rtcAlarmSetExt
                0x0602c208                hal_rtcAlarmStatusGet
                0x0602c244                hal_rtcTrimCallBack
 .text          0x0602c264        0x0 obj\Debug\hal\src\hal_spi.o
 .text          0x0602c264      0x504 obj\Debug\hal\src\hal_spi1.o
                0x0602c264                hal_spi1DmaCallback
                0x0602c2f8                hal_spi1DmaDoneCheck
                0x0602c378                hal_spi1Init
                0x0602c3d0                hal_spi1SendByte
                0x0602c3f4                hal_spi1RecvByte
                0x0602c414                hal_spi1SendDmaKick
                0x0602c4ac                hal_spi1SendDma
                0x0602c504                hal_spi1RecvDmaKick
                0x0602c5a0                hal_spi1RecvDma
                0x0602c5f8                hal_spi1_test
 .text          0x0602c768      0x21c obj\Debug\hal\src\hal_stream.o
                0x0602c768                hal_streamInit
                0x0602c8c4                hal_streamMallocDrop
                0x0602c96c                hal_stream_size
 .text          0x0602c984      0x9ac obj\Debug\hal\src\hal_sys.o
                0x0602cc80                hal_sysMemPrint
                0x0602cd8c                hal_sysMemMalloc
                0x0602cea4                hal_sysMemMallocLast
                0x0602cfd0                hal_sysMemFree
                0x0602d1ac                hal_sysMemRemain
                0x0602d204                hal_sysInit
 .text          0x0602d330       0xe0 obj\Debug\hal\src\hal_timer.o
                0x0602d330                hal_timerEnable
                0x0602d368                hal_timerTickEnable
                0x0602d39c                hal_timerPWMStart
 .text          0x0602d410      0x540 obj\Debug\hal\src\hal_uart.o
                0x0602d474                hal_uartIOShare
                0x0602d4b0                hal_uartIOShareCheck
                0x0602d4fc                hal_uartInit
                0x0602d554                hal_uartSendData
                0x0602d578                hal_uartRXIsrRegister
                0x0602d598                uart_PutChar_n
                0x0602d5cc                uart_PutStr
                0x0602d60c                uart_Put_hex
                0x0602d738                uart_Put_udec
                0x0602d7cc                uart_Put_dec
                0x0602d874                uart_PrintfBuf
                0x0602d90c                hal_uartPrintString
 .text          0x0602d950      0xa04 obj\Debug\hal\src\hal_watermark.o
                0x0602d9b8                hal_watermarkInit
                0x0602da68                hal_watermarkClose
                0x0602dafc                hal_watermarkClear
                0x0602db7c                hal_watermarkOpen
                0x0602dc1c                hal_watermarkColor
                0x0602dc7c                hal_watermarkAddr
                0x0602dc94                hal_watermarkSize
                0x0602dce0                hal_watermarkPosition
                0x0602dd2c                hal_watermarkCallbackRegister
                0x0602dd74                hal_watermarkRam
                0x0602df6c                hal_watermarkEnable
                0x0602e0bc                hal_jpg_watermark_init
                0x0602e14c                hal_jpg_watermark_uinit
                0x0602e190                hal_jpg_watermarkStart
                0x0602e2e4                hal_jpgB_watermarkPos_Adjust
 .text          0x0602e354        0x0 obj\Debug\hal\src\hal_wdt.o
 .text          0x0602e354        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .text          0x0602e354        0x0 obj\Debug\mcu\xos\xmbox.o
 .text          0x0602e354      0x2e4 obj\Debug\mcu\xos\xmsgq.o
                0x0602e354                XMsgQInit
                0x0602e394                XMsgQCreate
                0x0602e424                XMsgQDestory
                0x0602e45c                XMsgQFlush
                0x0602e49c                XMsgQPost
                0x0602e514                XMsgQPostFront
                0x0602e590                XMsgQPend
                0x0602e620                XMsgQCheck
 .text          0x0602e638      0x14c obj\Debug\mcu\xos\xos.o
                0x0602e638                XOSInit
                0x0602e674                XOSTickService
                0x0602e6e4                XOSTimeGet
                0x0602e704                XOSTimeDly
                0x0602e764                XOSRandom
 .text          0x0602e784      0x168 obj\Debug\mcu\xos\xwork.o
                0x0602e784                XWorkInit
                0x0602e7c4                XWorkCreate
                0x0602e850                XWorkDestory
                0x0602e880                XWorkService
 .text          0x0602e8ec      0x998 obj\Debug\multimedia\audio\audio_playback.o
                0x0602e8ec                audioPlaybackInit
                0x0602e938                audioPlaybackParse
                0x0602ea28                audioPlaybackStop
                0x0602eb30                audioPlaybackUninit
                0x0602eb68                audioPlaybackStart
                0x0602ef38                audioPlaybackPause
                0x0602ef74                audioPlaybackFirstPause
                0x0602efd0                audioPlaybackResume
                0x0602f038                audioPlaybackGetStatus
                0x0602f058                audioPlaybackGetTime
                0x0602f0d4                audioPlaybackSetVolume
                0x0602f128                audioPlaybackGetVolume
                0x0602f148                audioPlaybackService
 .text          0x0602f284      0x528 obj\Debug\multimedia\audio\audio_record.o
                0x0602f2b8                audioRecordInit
                0x0602f380                audioRecordUninit
                0x0602f3d4                audioRecordStop
                0x0602f480                audioRecordStart
                0x0602f5bc                audioRecordPuase
                0x0602f5ec                audioRecordResume
                0x0602f61c                audioRecordGetStatus
                0x0602f63c                audioRecordSetStatus
                0x0602f65c                audioRecordGetTime
                0x0602f67c                audioRecordService
 .text          0x0602f7ac      0xbf4 obj\Debug\multimedia\image\image_decode.o
                0x0602f7ac                imageDecodeSubCheck
                0x0602f928                imageDecodeStart
                0x0602fef0                imageDecodeSpiStart
                0x06030100                imageDecodeGetResolution
                0x06030124                imageDecodeDirect
 .text          0x060303a0      0x9c8 obj\Debug\multimedia\image\image_encode.o
                0x060303a0                imageEncodeInit
                0x060303cc                imageEncodeUninit
                0x060303f0                imageEncodeStart
                0x06030a60                imageLcdEncodeStart
                0x06030b4c                imageEncodeToSpi
 .text          0x06030d68      0x158 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                0x06030d68                jpg_encode
 .text          0x06030ec0     0x19a0 obj\Debug\multimedia\video\video_playback.o
                0x06030ff4                videoPlaybackClear
                0x06031058                videoPlaybackInit
                0x06031134                videoPlaybackDecodeWait
                0x06031180                videoPlaybackStart
                0x06031598                videoPlaybackStop
                0x060316d0                videoPlaybackUninit
                0x06031944                videoPlaybackPause
                0x060319c8                videoPlaybackResume
                0x06031a2c                videoPlaybackGetStatus
                0x06031a4c                videoPlaybackGetSpeed
                0x06031a6c                videoPlaybackSetVolume
                0x06031abc                videoPlaybackGetVolume
                0x06031adc                videoPlaybackGetTime
                0x06031b14                videoPlaybabkGetArg
                0x06031b30                videoGetFirstFrame
                0x06031d0c                videoDecodeFirstFrame
                0x06031e20                videoPlaybackService
                0x060326d4                videoPlaybackFastForward
                0x0603279c                videoPlaybackFastBackward
 .text          0x06032860      0xed8 obj\Debug\multimedia\video\video_record.o
                0x06032860                videoRecordInit
                0x06032920                videoRecordUninit
                0x0603296c                videoRecordFileStart
                0x06032adc                videoRecordFileStop
                0x06032ba4                videoRecordFileError
                0x06032c0c                videoRecordError
                0x06032c5c                videoRecordStart
                0x06032d78                videoRecordGetTimeSec
                0x06032dbc                videoRecordGetStatus
                0x06032ddc                videoRecordJunkSync
                0x06032e18                videoRecordStop
                0x06032e90                videoRecordRestart
                0x06032f8c                videoRecordFrameProcess
                0x06033468                videoRecordService
                0x0603359c                videoRecordCmdSet
                0x06033648                videoRecordSizePreSec
                0x060336b4                videoRecordTakePhotoCfg
                0x060336d8                videoRecordTakePhotoStatus
                0x060336f8                videoRecordSetPhotoStatus
                0x06033718                videoRecordTakePhotoFd
 .text          0x06033738     0x19f0 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
                0x0603382c                filelist_listFlush
                0x06033930                filelist_api_Init
                0x0603395c                filelist_api_nodecreate
                0x06033cc4                filelist_api_nodedestory
                0x06033de0                filelist_api_scan
                0x06033fb0                filenode_api_CountGet
                0x06033fe0                filelist_api_CountGet
                0x06034020                filelist_api_MaxCountGet
                0x06034050                filelist_GetFileNameByIndex
                0x060340cc                filelist_GetFileFullNameByIndex
                0x06034230                filelist_GetFileShortNameByIndex
                0x06034350                filelist_GetFileIndexByIndex
                0x060343fc                filelist_findFirstFileName
                0x0603447c                filelist_findFileNameByFname
                0x06034550                filelist_delFileByIndex
                0x06034668                filelist_delFileByFname
                0x06034720                filelist_listDelAll
                0x06034894                filelist_createNewFileFullName
                0x060348fc                filelist_createNewFileFullNameByFname
                0x0603493c                filenode_addFileByFname
                0x06034984                filenode_filefullnameLock
                0x06034a58                filenode_filefullnameUnlock
                0x06034b2c                filenode_fnameLockByIndex
                0x06034ba4                filenode_fnameUnlockByIndex
                0x06034c20                filelist_fnameChecklockByIndex
                0x06034cac                filenode_parentdir_get
                0x06034d54                filelist_GetLrcFileFullNameByIndex
                0x06034dfc                filelist_SpaceCheck
 .text          0x06035128      0xedc obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                0x06035128                filelist_NameChangeSufType
                0x06035188                filenode_fname_check
                0x060351c4                filenode_fname_createNew
                0x06035328                filenode_filename_CreateByFname
                0x06035678                filenode_filefullname_CreateByFname
                0x060356f4                filenode_AddFileByFname
                0x060358b0                filenode_Scan
                0x06035e64                filenode_api_findfirst
                0x06035f44                filenode_api_findByFname
 .text          0x06036004      0x200 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
                0x06036004                res_ascii_get
                0x060360ec                res_getAsciiCharSize
                0x06036124                res_getAsciiStringSize
 .text          0x06036204        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .text          0x06036204        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .text          0x06036204        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .text          0x06036204        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .text          0x06036204      0x65c obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
                0x06036204                res_font_Init
                0x06036334                res_font_SetLanguage
                0x06036404                res_font_GetString
                0x0603651c                res_font_GetChar
                0x060366e4                res_font_StringTableInit
                0x06036770                res_font_GetAddrAndSize
 .text          0x06036860      0x738 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
                0x060368f8                res_iconInit
                0x060369f8                res_iconBuffInit
                0x06036a64                res_iconGetSizeByIndex
                0x06036b18                res_iconGetAddrByIndex
                0x06036b98                res_icon_GetAddrAndSize
                0x06036c64                res_icon_GetTColor
                0x06036c98                res_iconBuffTimeUpdate
                0x06036cdc                res_iconGetData
                0x06036f14                res_iconGetPalette
 .text          0x06036f98      0x1f0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
                0x06036f98                res_image_show
                0x060370b0                res_image_decode
 .text          0x06037188      0x118 obj\Debug\sys_manage\res_manage\res_manage_api.o
                0x06037188                res_GetStringInfor
                0x060371f0                res_GetCharInfor
 .text          0x060372a0      0x368 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
                0x060372a0                res_music_end
                0x060372f8                res_music_start
                0x06037440                res_keysound_init
                0x06037574                res_keysound_play
                0x060375a8                res_keysound_stop
 .text          0x06037608        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .text          0x06037608      0x1c8 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
                0x060376d4                uiButtonCreate
 .text          0x060377d0      0x2f8 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
                0x060378cc                uiCycleCreateDirect
                0x06037958                uiCycleCreate
 .text          0x06037ac8      0x1ec obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
                0x06037ac8                uiDialogCreate
                0x06037bfc                uiDialogItem
 .text          0x06037cb4     0x19cc obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                0x06038244                uiWinDrawInit
                0x0603829c                uiWinDrawUpdate
                0x060382f8                uiWinDrawLine
                0x060385f8                uiWinDrawRect
                0x0603883c                uiWinDrawRoundRectWithRim
                0x06038be0                uiWinDrawPoint
                0x06038c80                uiWinDrawCircle
                0x06038e14                uiWinDrawIcon
                0x06039100                uiWinDrawString
 .text          0x06039680      0x29c obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
                0x06039804                uiFrameWinCreate
 .text          0x0603991c      0x3b0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
                0x06039b10                uiImageIconCreateDirect
                0x06039bf0                uiImageIconCreate
 .text          0x06039ccc     0x18c0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
                0x06039ccc                uiItemManageCreate
                0x06039dbc                uiItemManageSetItemHeight
                0x06039f5c                uiItemManageSetHeightAvgGap
                0x0603a0dc                uiItemManageSetHeightNotGap
                0x0603a218                uiItemManageSetRowSum
                0x0603a380                uiItemManageSetColumnSumWithGap
                0x0603a500                uiItemManageCreateItem
                0x0603a6ac                uiItemManageSetResInforFuncEx
                0x0603a70c                uiItemManageSetCurItem
                0x0603a9ec                uiItemManageUpdateRes
                0x0603aaa4                uiItemManageUpdateAllItem
                0x0603ab08                uiItemManageUpdateCurItem
                0x0603acac                uiItemManageNextItem
                0x0603ad1c                uiItemManagePreItem
                0x0603ad8c                uiItemManageNextPage
                0x0603ae10                uiItemManagePrePage
                0x0603ae98                uiItemManageGetCurrentItem
                0x0603aefc                uiItemManageSetCharInfor
                0x0603afb4                uiItemManageSetSelectColor
                0x0603b04c                uiItemManageSetSelectImage
                0x0603b0e4                uiItemManageSetUnselectColor
                0x0603b17c                uiItemManageSetUnselectImage
                0x0603b214                uiItemManageGetTouchInfor
                0x0603b40c                uiItemManageSetSelectColorEx
                0x0603b4cc                uiItemManageSetUnselectColorEx
 .text          0x0603b58c      0x444 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
                0x0603b824                uiItemCreateItemMenu
 .text          0x0603b9d0      0x5c4 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
                0x0603bd80                uiItemCreateMenuItemEx
 .text          0x0603bf94      0x390 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
                0x0603c1ac                uiItemCreateMenuOption
 .text          0x0603c324      0x238 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
                0x0603c3bc                uiLineCreate
 .text          0x0603c55c     0x1ccc obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
                0x0603c55c                uiWinSendMsg
                0x0603c59c                uiWinSendMsgId
                0x0603c5c4                uiWinSendMsgToParent
                0x0603c5f4                uiWinSendMsgIdToParent
                0x0603c61c                uiWinOverlapCmp
                0x0603c680                uiWinInsideCmp
                0x0603c6e4                uiWinStringExRowCal
                0x0603c74c                uiWinStringExGetByRow
                0x0603c7b0                uiWinStringExGetNext
                0x0603c814                uiWinInterSection
                0x0603c894                uiWinHasInvalidRect
                0x0603c930                uiWinFreeInvalidRect
                0x0603cbec                uiWinSetbgColor
                0x0603cc20                uiWinSetCycleRadius
                0x0603cc58                uiWinSetRoundRectRadius
                0x0603cc90                uiWinSetfgColor
                0x0603ccc4                uiWinSetVisible
                0x0603cd48                uiWinIsVisible
                0x0603cd84                uiWinSetResid
                0x0603cdb4                uiWinSetItemSelResid
                0x0603cde4                uiWinUpdateResId
                0x0603ce08                uiWinUpdateAllResId
                0x0603ce6c                uiWinSetStrInfor
                0x0603ceb8                uiResInforInit
                0x0603cf04                uiWinSetSelectInfor
                0x0603cf34                uiWinSetUnselectInfor
                0x0603cf64                uiWinGetResSum
                0x0603cfa4                uiWinSetResSum
                0x0603cfd4                uiWinSetResidByNum
                0x0603d024                uiWinSetPorgressRate
                0x0603d054                uiWinParentRedraw
                0x0603d0c4                uiWinGetRelativePos
                0x0603d154                uiWinGetPos
                0x0603d198                uiWinUpdateInvalid
                0x0603d210                uiWinSetProgressRate
                0x0603d240                uiWinSetName
                0x0603d260                uiWinGetCurrent
                0x0603d280                uiWinDefaultProc
                0x0603d628                uiWinCreate
                0x0603daf8                uiWinDestroy
                0x0603dca4                uiWinGetTouchInfor
                0x0603dcd4                uiWinSetTouchInfor
                0x0603dd04                uiWinTouchProcess
                0x0603df0c                uiWinDrawProcess
                0x0603e020                uiWinDestroyDeskTopChildWin
                0x0603e094                uiWinInit
                0x0603e1ac                uiWinUninit
 .text          0x0603e228      0x380 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
                0x0603e228                uiWinHeapInit
                0x0603e268                uiWinHeapMemInfo
                0x0603e2e4                uiWinHeapMalloc
                0x0603e3b4                uiWinHeapFree
                0x0603e464                uiMemPoolCreate
                0x0603e4e4                uiMemPoolGet
                0x0603e52c                uiMemPoolPut
                0x0603e568                uiMemPoolInfo
 .text          0x0603e5a8      0x2e4 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
                0x0603e748                uiProgressBarCreateDirect
                0x0603e7e4                uiProgressBarCreate
 .text          0x0603e88c      0x2dc obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
                0x0603e9f0                uiRectCreateDirect
                0x0603ea7c                uiRectCreate
 .text          0x0603eb68      0x678 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
                0x0603eef4                uiStringExCreateDirect
                0x0603eff0                uiStringExCreate
 .text          0x0603f1e0      0x3fc obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
                0x0603f3d4                uiStringIconCreateDirect
                0x0603f4d8                uiStringIconCreate
 .text          0x0603f5dc      0x250 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
                0x0603f760                uiTipsCreate
 .text          0x0603f82c      0x120 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
                0x0603f82c                uiWidgetProc
                0x0603f878                uiWidgetSetType
                0x0603f8ac                uiWidgetGetType
                0x0603f8e0                uiWidgetGetId
                0x0603f918                uiWidgetSetId
 .text          0x0603f94c      0x310 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
                0x0603fbbc                uiWidgetManageCreate
 .text          0x0603fc5c      0x8d8 obj\Debug\app\app_common\src\app_init.o
                0x0603fc5c                app_logo_show
                0x0603fce0                app_version_get
                0x0603fd14                app_draw_init
                0x0603fd44                app_uninit
                0x0603ff14                app_init
                0x06040484                app_sendDrawUIMsg
                0x060404b8                app_draw_Service
                0x06040500                app_systemService
 .text          0x06040534     0x107c obj\Debug\app\app_common\src\app_lcdshow.o
                0x06040534                app_lcdVideoShowScaler_cfg
                0x06040ca0                app_lcdVideoShowRotate_cfg
                0x06040e78                app_lcdVideoShowMirro_cfg
                0x0604104c                app_lcdCsiVideoShowStart
                0x060410cc                app_lcdCsiVideoLayerEnGet
                0x060410ec                app_lcdCsiVideoShowStop
                0x06041160                app_lcdVideoIdleFrameGet
                0x06041180                app_lcdUiShowInit
                0x060412ac                app_lcdUiShowUinit
                0x060412fc                app_lcdUiDrawIdleFrameGet
                0x06041320                app_lcdShowWinModeCfg
 .text          0x060415b0        0x0 obj\Debug\app\app_common\src\main.o
 .text.startup  0x060415b0       0x4c obj\Debug\app\app_common\src\main.o
                0x060415b0                main
 .text          0x060415fc        0x0 obj\Debug\app\resource\user_res.o
 .text          0x060415fc       0xf0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
                0x060415fc                menuProcDelCur
                0x0604162c                menuProcDelAll
                0x0604165c                menuProcLockCur
                0x0604168c                menuProcUnlockCur
                0x060416bc                menuProcUnlockAll
 .text          0x060416ec        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .text          0x060416ec       0x90 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
                0x060416ec                menuProcDateTime
                0x0604171c                menuProcFormat
                0x0604174c                menuProcDefault
 .text          0x0604177c        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
 .text          0x0604177c      0x970 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .text          0x060420ec      0x450 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .text          0x0604253c      0x518 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .text          0x06042a54      0x58c obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .text          0x06042fe0      0x548 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .text          0x06043528      0xef0 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x060443f8                menuWinIsOpen
 .text          0x06044418      0x554 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .text          0x0604496c      0x4f8 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .text          0x06044e64      0x588 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .text          0x060453ec      0x564 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .text          0x06045950        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .text          0x06045950      0x118 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .text          0x06045a68      0x1f8 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .text          0x06045c60      0xad4 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .text          0x06046734      0x434 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .text          0x06046b68      0x44c obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .text          0x06046fb4      0x1a8 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .text          0x0604715c      0x368 obj\Debug\app\task_windows\msg_api.o
                0x0604715c                taskmsgFuncRegister
                0x06047214                sysMsgFuncRegister
                0x06047290                app_msgDeal
                0x060473cc                app_msgDealByType
                0x06047468                app_msgDealByInfor
 .text          0x060474c4      0x4bc obj\Debug\app\task_windows\task_api.o
                0x0604756c                app_taskInit
                0x0604763c                app_taskCurId
                0x0604765c                app_taskStart
                0x06047728                app_taskChange
                0x060477a4                app_task_rec_Change
                0x06047820                app_taskService
 .text          0x06047980      0x550 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .text          0x06047ed0     0x2154 obj\Debug\app\task_windows\task_common\src\task_common.o
                0x06047ed0                task_com_para_init
                0x06047f40                task_com_usbhost_set
                0x060481a8                task_com_usb_dev_out
                0x060481ec                task_com_spijpg_Init
                0x06048200                task_com_sdlist_scan
                0x06048214                task_com_sdc_stat_set
                0x0604830c                task_com_lcdbk_set
                0x06048380                task_com_sreen_check
                0x0604843c                task_com_auto_poweroff
                0x060484f0                task_com_keysound_play
                0x0604853c                task_com_sound_wait_end
                0x06048570                task_com_sdc_freesize_check
                0x06048628                task_com_fs_scan
                0x06048764                task_com_sdc_freesize_modify
                0x060487f8                task_com_ir_set
                0x0604885c                task_com_powerOnTime_str
                0x060488f0                task_com_sensor_res_str
                0x060489c4                task_com_rec_show_time_str
                0x06048a84                task_com_rec_remain_time_str
                0x06048b48                task_com_play_time_str
                0x06048d28                task_com_tips_show
                0x06048f18                task_com_LedPwm_ctrl
                0x06049050                task_com_LedOnOff_ctrl
                0x060491a0                task_com_scaler_str
                0x060491dc                task_com_USB_CS_DM_DP_status_select
                0x060494ec                task_com_service
 .text          0x0604a024       0x44 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .text          0x0604a068      0x2c0 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x0604a1cc                app_taskPlayAudio_start
 .text          0x0604a328      0xa34 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .text          0x0604ad5c     0x1464 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x0604af48                taskPlayVideoThumbnallDrawImage
                0x0604b150                taskPlayVideoSlideOpen
                0x0604b2a0                taskPlayVideoSlideClose
                0x0604b2f8                taskPlayVideoSlidePause
                0x0604b33c                taskPlayVideoSlideStart
                0x0604b48c                taskPlayVideoMainStart
                0x0604c164                taskPlayVideoMainScalerCfg
 .text          0x0604c1c0     0x1514 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .text          0x0604d6d4      0x3c8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .text          0x0604da9c      0xb10 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .text          0x0604e5ac       0x4c obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .text          0x0604e5f8      0x364 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .text          0x0604e95c      0x20c obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .text          0x0604eb68      0x748 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x0604ec50                taskRecordPhotoRemainCal
                0x0604eda8                app_taskRecordPhoto_callback
                0x0604f0e0                taskRecordPhotoProcess
 .text          0x0604f2b0      0xd8c obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .text          0x0605003c      0xcf0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                0x06050824                taskRecordvideoTimeCount1S
                0x060508f4                app_taskRecordVideo_caltime
                0x0605092c                app_taskRecordVideo_Capture
                0x06050ae0                app_taskRecordVideo_start
                0x06050ba4                app_taskRecordVideo_stop
 .text          0x06050d2c     0x10fc obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .text          0x06051e28       0x20 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .text          0x06051e48       0xd8 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                0x06051e48                taskSdUpdate_uiInit
 .text          0x06051f20       0x68 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .text          0x06051f88      0x120 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .text          0x060520a8      0x1bc obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .text          0x06052264      0x1a0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .text          0x06052404      0x3a0 obj\Debug\app\task_windows\windows_api.o
                0x060525fc                uiParentDealMsg
                0x0605262c                uiOpenWindow
                0x06052774                windowIsOpen
 .text          0x060527a4     0x1488 obj\Debug\app\user_config\src\mbedtls_md5.o
                0x060527a4                mbedtls_md5_init
                0x060527cc                mbedtls_md5_free
                0x0605280c                mbedtls_md5_clone
                0x06052830                mbedtls_md5_starts
                0x06052880                mbedtls_md5_process
                0x06053860                mbedtls_md5_update
                0x0605388c                mbedtls_md5_finish
                0x060539c8                mbedtls_md5
                0x06053a3c                check_uid_entryption
                0x06053c0c                spi_flash_check_md5
 .text          0x06053c2c      0x4e8 obj\Debug\app\user_config\src\user_config_api.o
                0x06053c2c                user_config_set
                0x06053c64                user_config_get
                0x06053ca0                user_config_save
                0x06053d48                userConfig_Reset
                0x06053db0                userConfig_Init
                0x06053e98                userConfigInitial
                0x06053eb8                user_configValue2Int
                0x06053f20                user_config_Language
                0x06053f88                user_config_cfgSys
                0x060540d8                user_config_cfgSysAll
 .text          0x06054114        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .text          0x06054114        0x0 ..\lib\libboot.a(boot.o)
 .text          0x06054114        0x0 ..\lib\libboot.a(boot_loader.o)
 .text          0x06054114        0x0 ..\lib\libboot.a(reset.o)
 .text          0x06054114        0x0 ..\lib\libboot.a(boot_lib.o)
 .text          0x06054114        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .text          0x06054114      0x420 ..\lib\libmcu.a(hx330x_auadc.o)
                0x06054114                hx330x_auadcHalfIRQRegister
                0x06054134                hx330x_auadcEndIRQRegister
                0x06054154                hx330x_auadcEnable
                0x0605420c                hx330x_auadcAGCEnable
                0x0605429c                hx330x_auadcGainSet
                0x06054334                hx330x_auadcBufferSet
                0x06054394                hx330x_auadcInit
                0x06054484                hx330x_auadcSetSampleSet
                0x06054514                hx330x_agc_pwr_get
 .text          0x06054534     0x125c ..\lib\libmcu.a(hx330x_csi.o)
                0x06054534                hx330x_CSI_LBFDMAERR_callback
                0x060545b8                hx330x_CSI_SenSizeErr_callback
                0x06054620                hx330x_csiIOConfig
                0x060546bc                hx330x_csi_reset
                0x06054700                hx330x_csiInit
                0x0605474c                hx330x_csi_fcnt_mnt
                0x06054778                hx330x_csiISRRegiser
                0x060547b4                hx330x_csiOutputSet
                0x060547f0                hx330x_csiMclkSet
                0x060548cc                hx330x_csiSyncSet
                0x06054930                hx330x_csiPrioritySet
                0x0605496c                hx330x_csiTypeSet
                0x060549f0                hx330x_csiModeSet
                0x06054a2c                hx330x_csiModeGet
                0x06054a48                hx330x_pclk_digital_fir_Set
                0x06054aac                hx330x_pclk_analog_Set
                0x06054afc                hx330x_pclk_inv_Set
                0x06054b40                hx330x_csi_clk_tun_Set
                0x06054b98                hx330x_csiSizeSet
                0x06054bd0                hx330x_sen_Image_Size_Set
                0x06054c08                hx330x_csi_in_CropSet
                0x06054c8c                hx330x_csiInputAddrSet
                0x06054ca4                hx330x_csiTestModeSet
                0x06054d04                hx330x_csiDvpClkDivSet
                0x06054d44                hx330x_csiINTSet
                0x06054d80                hx330x_csiEnable
                0x06054e10                hx330x_csiLCDScalerDoneCheck
                0x06054e38                hx330x_csiToMjpAddrCfg
                0x06054e54                hx330x_csiMJPEGFrameSet
                0x06054ed8                hx330x_csiWifiFrameSet
                0x06054f30                hx330x_csiLCDFrameSet
                0x06054f5c                hx330x_csi_YUVFrameSet
                0x06054fbc                hx330x_csiMJPEGScaler
                0x06055140                hx330x_csiMJPEGCrop
                0x06055240                hx330x_csiWifiScaler
                0x060553cc                hx330x_csiSetLsawbtooth
                0x06055400                hx330x_csiLCDScaler
                0x06055514                hx330x_csiMJPEGDmaEnable
                0x060555b4                hx330x_csiWifiDmaEnable
                0x06055654                hx330x_csiLCDDmaEnable
                0x06055708                hx330x_csiLCDDmaKick
                0x0605574c                hx330x_csi_common_int_set
 .text          0x06055790      0x4b0 ..\lib\libmcu.a(hx330x_dac.o)
                0x060557d0                hx330x_dacTypeCfg
                0x06055814                hx330x_dacSampleRateSet
                0x06055884                hx330x_dacEnable
                0x06055908                hx330x_dacVolumeSet
                0x06055980                hx330x_dacReset
                0x060559a4                hx330x_dacStop
                0x060559d4                hx330x_dacISRRegister
                0x060559f4                hx330x_dacHPSet
                0x06055a88                eq_coeff_init
                0x06055af8                eq_gain_init
                0x06055b68                hx330x_dacInit
 .text          0x06055c40      0x13c ..\lib\libmcu.a(hx330x_dma.o)
                0x06055c40                hx330x_dmaNocCfg
                0x06055cb4                hx330x_dmaNocWinA
                0x06055cdc                hx330x_dmaNocWinB
                0x06055d04                hx330x_dmaNocWinDis
                0x06055d2c                hx330x_dmaChannelEnable
 .text          0x06055d7c      0x41c ..\lib\libmcu.a(hx330x_dmauart.o)
                0x06055d7c                hx330x_DmaUart_sta_cfg
                0x06055dbc                hx330x_DmaUart_con_cfg
                0x06055dfc                hx330x_DmaUartIOCfg
                0x06055f54                hx330x_DmaUartInit
                0x06055fc4                hx330x_DmaUart_CallbackRegister
                0x06055fe4                hx330x_dmauart_sendbyte
                0x06056014                hx330x_dmauart_sendDmakick
                0x06056048                hx330x_dmauart_sendDma
                0x060560c4                hx330x_dmauart_recvAutoDmakick
                0x06056110                hx330x_dmauart_recvBytekick
                0x06056144                hx330x_dmauart_rxOutAdrGet
                0x06056160                hx330x_dmauart_rxCntGet
                0x0605617c                hx330x_dmauart_rxFifoOut
 .text          0x06056198      0x8e8 ..\lib\libmcu.a(hx330x_gpio.o)
                0x06056198                hx330x_gpioSFRSet
                0x060563dc                hx330x_gpioSFRGet
                0x06056598                hx330x_gpioHystersisSet
                0x06056624                hx330x_GPIO_FUNC
                0x06056690                hx330x_gpioCommonConfig
                0x06056724                hx330x_gpioLedInit
                0x060567b4                hx330x_gpioINTCheck
                0x060567ec                hx330x_gpioINTClear
                0x06056810                hx330x_gpioINTInit
                0x0605692c                hx330x_io1d1_softstart
                0x060569e8                hx330x_io1d1_pd_enable
 .text          0x06056a80      0xbd0 ..\lib\libmcu.a(hx330x_iic.o)
                0x06056b50                soft_iic0_sdaout
                0x06056b9c                soft_iic0_sdain
                0x06056be8                soft_iic0_sda_set
                0x06056c1c                soft_iic0_scl_set
                0x06056cd0                soft_iic0_sda_get
                0x06056d00                soft_iic0_init
                0x06056d30                soft_iic0_start
                0x06056db0                soft_iic0_stop
                0x06056e30                soft_iic0_wait_ack
                0x06056ef8                hx330x_iic0Init
                0x06056fa0                hx330x_iic0Uninit
                0x06056fe4                hx330x_iic0Start
                0x06057024                hx330x_iic0Stop
                0x060570a8                hx330x_iic0RecvACK
                0x060570fc                hx330x_iic0SendACK
                0x06057144                hx330x_iic0SendByte
                0x06057264                hx330x_iic0RecvByte
                0x06057394                hx330x_iic1Init
                0x0605742c                hx330x_iic1Uninit
                0x060574a4                hx330x_iic1Start
                0x060574c0                hx330x_iic1Stop
                0x06057520                hx330x_iic1RecvACK
                0x06057548                hx330x_iic1SendACK
                0x0605756c                hx330x_iic1SendByte
                0x060575dc                hx330x_iic1RecvByte
 .text          0x06057650      0x18c ..\lib\libmcu.a(hx330x_int.o)
                0x06057650                hx330x_int_priority
                0x06057698                hx330x_intInit
 .text          0x060577dc     0x15d4 ..\lib\libmcu.a(hx330x_isp.o)
                0x060577dc                hx330x_isp_mask_tab_cfg
                0x06057814                hx330x_ispModeSet
                0x06057850                hx330x_isp_BLC_cfg
                0x0605789c                hx330x_isp_LSC_cfg
                0x060578d4                hx330x_isp_DDC_cfg
                0x06057a4c                hx330x_isp_AWB_GAIN_adj
                0x06057a80                hx330x_isp_whtpnt_stat_cfg
                0x06057cec                hx330x_isp_CCM_cfg
                0x06057db0                hx330x_isp_RGB_DGAIN_adj
                0x06057e40                hx330x_isp_hist_stat_cfg
                0x06057ec8                hx330x_isp_YGAMMA_cfg
                0x06057ff8                hx330x_isp_ylog_ygamma_cal
                0x0605803c                hx330x_isp_RGBGAMMA_cfg
                0x060581cc                hx330x_isp_CH_cfg
                0x06058524                hx330x_isp_VDE_cfg
                0x060585f8                hx330x_isp_EE_cfg
                0x060589c4                hx330x_isp_CCF_cfg
                0x06058b10                hx330x_isp_SAJ_cfg
                0x06058c28                hx330x_isp_kick_stat
                0x06058c58                hx330x_isp_stat_en
                0x06058c9c                hx330x_isp_stat_cp_kick_st
                0x06058cc0                hx330x_isp_stat_cp_done
                0x06058cf0                hx330x_isp_model_cfg
 .text          0x06058db0        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .text          0x06058db0     0x19b8 ..\lib\libmcu.a(hx330x_jpg.o)
                0x06058db0                hx330x_mjpA_EncodeISRRegister
                0x06058dd0                hx330x_MJPA_EncodeLcdPreRegister
                0x06058df0                hx330x_MJPA_EncodeLcdPre_Func_call
                0x06058e24                hx330x_MJPA_EncodeLcdPre_Func_Check
                0x06058e50                hx330x_MJPA_EncodeLcdBackRegister
                0x06058e70                hx330x_MJPA_EncodeLcdBack_Func_call
                0x06058ea4                hx330x_MJPA_EncodeLcdBack_Func_Check
                0x06058ed0                hx330x_mjpB_EncodeISRRegister
                0x06058ef0                hx330x_mjpA_isr_check
                0x06058f1c                hx330x_mjpB_DecodeISRRegister
                0x06058f3c                hx330x_mjpB_Encode_StartFunc_Check
                0x06058f68                hx330x_mjpB_Encode_StartFunc_Reg
                0x06058f88                hx330x_mjpB_Encode_StartFunc_call
                0x06058fbc                hx330x_mjpA_Encode_StartFunc_Check
                0x06058fe8                hx330x_mjpA_Encode_StartFunc_Reg
                0x06059008                hx330x_mjpA_Encode_StartFunc_call
                0x0605903c                hx330x_mjpA_reset
                0x06059080                hx330x_mjpB_reset
                0x060590d0                hx330x_mjpA_EncodeSizeSet
                0x06059178                hx330x_mjpA_EncodeSizeSet2
                0x06059300                hx330x_mjpA_EncodeQuilitySet
                0x06059340                hx330x_mjpA_EncodeInfoSet
                0x06059380                hx330x_mjpA_EncodeBufferSet
                0x060593cc                hx330x_mjpA_Encode_inlinebuf_init
                0x06059424                hx330x_mjpA_Encode_manual_on
                0x06059484                hx330x_mjpA_Encode_manual_stop
                0x060594b0                hx330x_mjpA_EncodeEnable
                0x0605952c                hx330x_mjpA_EncodeQadj
                0x060595f4                hx330x_mjpA_EncodeInit
                0x0605969c                hx330x_mjpA_EncodeDriModeSet
                0x06059704                hx330x_cal_jASize
                0x06059734                hx330x_mjpA_Encode_check
                0x060598d0                hx330x_mjpA_Flag_Clr
                0x060598e8                hx330x_mjpB_EncodeQuilitySet
                0x06059928                hx330x_mjpB_EncodeQadj
                0x060599f0                hx330x_mjpB_Encodeinit
                0x06059b74                hx330x_cal_jBSize
                0x06059ba4                hx330x_mjpB_Encode_inlinebuf_init
                0x06059bd0                hx330x_mjpB_Encode_output_init
                0x06059bfc                hx330x_mjpB_Encode_manual_stop
                0x06059c64                hx330x_mjpB_Encode_manual_start
                0x06059cb0                hx330x_mjpB_EncodeLoadAddrGet
                0x06059ccc                hx330x_mjpB_as_Encode
                0x06059cf8                hx330x_mjpB_DecodeScalerCal
                0x06059dc8                hx330x_mjpB_DecodeSetSize
                0x06059ffc                hx330x_mjpB_DecodeOutputSet
                0x0605a018                hx330x_mjpB_DecodeInputSet
                0x0605a05c                hx330x_mjpB_DecodeInputResume
                0x0605a094                hx330x_mjpB_DecodeDriSet
                0x0605a0ac                hx330x_mjpB_DecodeCompressSet
                0x0605a0c4                hx330x_mjpB_DecodeInitTable
                0x0605a0f4                hx330x_mjpB_yuvfmt_set
                0x0605a140                hx330x_mjpB_DecodeInit
                0x0605a298                hx330x_mjpB_DecodeDCTimeSet
                0x0605a348                hx330x_mjpB_DecodeEnable
                0x0605a3b8                hx330x_mjpB_DecodeKick
                0x0605a400                hx330x_mjpB_DecodeStop
                0x0605a43c                hx330x_mjpB_DecodeQDTCfg
                0x0605a490                hx330x_mjpB_DecodeBusyCheck
                0x0605a4c4                hx330x_mjpB_DecodeCheck
                0x0605a4f4                hx330x_mjpB_DecodeODma1Cfg
                0x0605a5a4                hx330x_mjpB_DecodePacket_check
                0x0605a668                hx330x_mjpB_Decode_InResume
                0x0605a68c                hx330x_mjpB_Decode_check
 .text          0x0605a768      0x1c8 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                0x0605a768                hx330x_mjpA_table_init
                0x0605a840                hx330x_mjpB_table_init
 .text          0x0605a930     0x10a0 ..\lib\libmcu.a(hx330x_lcd.o)
                0x0605a930                hx330x_lcdReset
                0x0605a944                hx330x_lcdSPIMode
                0x0605a970                hx330x_lcdSPIInit
                0x0605aa90                hx330x_lcdSPIUninit
                0x0605aaec                hx330x_lcdSPISendData
                0x0605ac60                hx330x_lcdMcuSendCmd
                0x0605acd4                hx330x_lcdMcuSendData
                0x0605ad30                hx330x_lcdMcuSendCmd16
                0x0605adc4                hx330x_lcdMcuSendData16
                0x0605ae58                hx330x_lcdInit
                0x0605aea4                hx330x_lcdIRQEnable
                0x0605aeec                hx330x_lcdPreLineSet
                0x0605af08                hx330x_lcdSignalSet
                0x0605afa0                hx330x_lcdBusWidth
                0x0605aff4                hx330x_lcdBusEnable
                0x0605b044                hx330x_lcdClkSet
                0x0605b060                hx330x_lcdSyncSet
                0x0605b084                hx330x_lcdDESignalSet
                0x0605b0a8                hx330x_lcdPositionSet
                0x0605b0c4                hx330x_lcdResolutionSet
                0x0605b0e0                hx330x_lcdWindowSizeSet
                0x0605b0fc                hx330x_lcdDataModeSet
                0x0605b140                hx330x_lcdClkNumberSet
                0x0605b158                hx330x_lcdEndLineSet
                0x0605b174                hx330x_lcdPanelMode
                0x0605b1bc                hx330x_lcdEnable
                0x0605b220                hx330x_lcdTeMode
                0x0605b268                hx330x_lcdTeCheck
                0x0605b2e4                hx330x_lcdISRRegister
                0x0605b328                hx330x_lcdRGBTimimgInit
                0x0605b578                hx330x_lcdMCUTimimgInit
                0x0605b74c                hx330x_lcdRGBIOConfig
                0x0605b8ac                hx330x_lcdMCUIOConfig
 .text          0x0605b9d0      0x238 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                0x0605b9d0                hx330x_rotateISRRegiser
                0x0605b9f0                hx330x_rotateCheckBusy
                0x0605ba10                hx330x_rotateReset
                0x0605ba54                hx330x_rotateStart
                0x0605bb74                hx330x_rotateWaitFrameDone
                0x0605bbd8                hx330x_rotateGetSrcYAddr
 .text          0x0605bc08      0x874 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x0605bc08                hx330x_checkLcdShowStatus
                0x0605bc2c                hx330x_lcdShowISRRegister
                0x0605bc4c                hx330x_lcdshowInit
                0x0605bcc8                hx330x_lcdshowSetCritical
                0x0605bd0c                hx330x_lcdSetVideoBgColor
                0x0605bd40                hx330x_lcdSetVideoBSC
                0x0605bd8c                hx330x_lcdSetVideoBrightness
                0x0605bde4                hx330x_lcdVideoSetRgbWidth
                0x0605be50                hx330x_lcdVideoSetScalePara
                0x0605be9c                hx330x_lcdVideoSetScaleLine
                0x0605bf04                hx330x_lcdVideoGetYAddr
                0x0605bf34                hx330x_lcdVideoGetUVAddr
                0x0605bf64                hx330x_lcdVideoSetSize
                0x0605bf9c                hx330x_lcdvideoMemcpy
                0x0605bffc                hx330x_lcdvideoEnable
                0x0605c040                hx330x_lcdVideoSetGAMA
                0x0605c0ec                hx330x_lcdVideo_CCM_cfg
                0x0605c1d4                hx330x_lcdVideo_SAJ_cfg
                0x0605c240                hx330x_lcdvideoGammaEnable
                0x0605c284                hx330x_lcdUiSetSize
                0x0605c2d0                hx330x_lcdUiSetPosition
                0x0605c314                hx330x_lcdUiSetPalette
                0x0605c378                hx330x_UiGetAddr
                0x0605c3c8                hx330x_lcdUiSetAlpha
                0x0605c42c                hx330x_lcdUiLzoSoftCreate
 .text          0x0605c47c      0x32c ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                0x0605c47c                hx330x_uiLzoISRRegiser
                0x0605c49c                hx330x_uiLzoCheckBusy
                0x0605c4c0                hx330x_uiLzoReset
                0x0605c504                hx330x_uiLzoGetOutSize
                0x0605c524                hx330x_uiLzoWaiDone
                0x0605c5cc                hx330x_uiLzoStart
                0x0605c6cc                hx330x_uiLzoKick
 .text          0x0605c7a8      0x14c ..\lib\libmcu.a(hx330x_lcdwin.o)
                0x0605c7a8                hx330x_lcdWinABConfig
                0x0605c824                hx330x_lcdWinABEnable
                0x0605c868                hx330x_lcdWinReset
                0x0605c8a8                hx330x_lcdWinGetTopLayer
                0x0605c8cc                hx330x_lcdWinGetBotLayer
 .text          0x0605c8f4      0x118 ..\lib\libmcu.a(hx330x_md.o)
                0x0605c8f4                hx330x_mdEnable
                0x0605c95c                hx330x_mdEnable_check
                0x0605c97c                hx330x_mdInit
                0x0605c9c4                hx330x_mdXPos
                0x0605c9e8                hx330x_mdYPos
 .text          0x0605ca0c      0x304 ..\lib\libmcu.a(hx330x_mipi.o)
                0x0605ca0c                hx330x_mipiClkCfg
                0x0605ca60                hx330x_MipiCSIUinit
                0x0605cac0                hx330x_MipiCSIInit
 .text          0x0605cd10      0x860 ..\lib\libmcu.a(hx330x_misc.o)
                0x0605cd10                hx330x_sin
                0x0605cd58                hx330x_cos
                0x0605cd7c                hx330x_abs
                0x0605cd9c                hx330x_dif_abs
                0x0605cdc0                hx330x_max
                0x0605cde4                hx330x_clip
                0x0605ce14                hx330x_str_cpy
                0x0605ce78                hx330x_str_ncpy
                0x0605cef0                hx330x_str_char
                0x0605cf38                hx330x_str_cmp
                0x0605cfbc                hx330x_str_ncmp
                0x0605d064                hx330x_str_len
                0x0605d0a0                hx330x_str_cat
                0x0605d110                hx330x_str_seek
                0x0605d17c                hx330x_strTransform
                0x0605d1d0                hx330x_dec_num2str
                0x0605d210                hx330x_char2hex
                0x0605d264                hx330x_str2num
                0x0605d2bc                hx330x_hex2str
                0x0605d334                hx330x_num2str
                0x0605d398                hx330x_num2str_cnt
                0x0605d424                hx330x_CountToString
                0x0605d510                hx330x_str_noftcpy
                0x0605d538                hx330x_greatest_divisor
 .text          0x0605d570      0xd00 ..\lib\libmcu.a(hx330x_rtc.o)
                0x0605d570                hx330x_rtcRamRead
                0x0605d60c                hx330x_rtcRamWrite
                0x0605d6a8                hx330x_rtcSecondTrim
                0x0605d83c                hx330x_rtcInit
                0x0605da8c                hx330x_rtc128K_div_cfg
                0x0605db18                hx330x_rtc128K_trim
                0x0605dbc8                hx330x_rtcSencodEnable
                0x0605dc44                hx330x_rtcAlamEnable
                0x0605dd2c                hx330x_rtcAlamSet
                0x0605dd74                hx330x_rtcGet
                0x0605ddc8                hx330x_rtc_alarm_weakup_reset
                0x0605de50                hx330x_rtcSet
                0x0605de98                hx330x_VDDGSENEnable
                0x0605df08                hx330x_WKI0InputEnable
                0x0605df78                hx330x_WKI1InputEnable
                0x0605dfe8                hx330x_WKI1Read
                0x0605e00c                hx330x_WKI0Read
                0x0605e030                hx330x_WKI0WakeupEnable
                0x0605e170                hx330x_rtcBatDecEnable
                0x0605e1e0                hx330x_rtcSenHVEnable
                0x0605e250                hx330x_rtcAlarmWakeUpFlag
 .text          0x0605e270      0x788 ..\lib\libmcu.a(hx330x_sd.o)
                0x0605e270                hx330x_sd0Init
                0x0605e3c4                hx330x_sd0Uninit
                0x0605e498                hx330x_sd0BusSet
                0x0605e4dc                hx330x_sd0Buffer
                0x0605e4fc                hx330x_sd0ClkSet
                0x0605e594                hx330x_sd1Init
                0x0605e73c                hx330x_sd1Uninit
                0x0605e840                hx330x_sd1BusSet
                0x0605e884                hx330x_sd1WaitDAT0
                0x0605e924                hx330x_sd1GetRsp
                0x0605e940                hx330x_sd1Buffer
                0x0605e960                hx330x_sd1ClkSet
 .text          0x0605e9f8        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .text          0x0605e9f8      0x4ac ..\lib\libmcu.a(hx330x_spi1.o)
                0x0605e9f8                hx330x_spi1_pin_config
                0x0605ea78                hx330x_spi1_CS_Config
                0x0605ead4                hx330x_spi1Init
                0x0605eb84                hx330x_spi1DMAIRQ_CallbackRegister
                0x0605eba4                hx330x_spi1SendByte
                0x0605ec24                hx330x_spi1RecvByte
                0x0605ec6c                hx330x_sp1RecvDmaKick
                0x0605ecc4                hx330x_spi1DmaDoneCheck
                0x0605ece8                hx330x_sp1SendDmaKick
                0x0605ed44                hx330x_sp1RecvDma
                0x0605ede0                hx330x_sp1SendDma
                0x0605ee60                hx330x_sp1Enable
 .text          0x0605eea4      0x8bc ..\lib\libmcu.a(hx330x_sys.o)
                0x0605eea4                hx330x_word_memcpy
                0x0605ef14                hx330x_halfword_memcpy
                0x0605ef84                hx330x_word_memset
                0x0605efe0                hx330x_mtsfr_memcpy
                0x0605f044                hx330x_mfsfr_memcpy
                0x0605f0a8                table_init_data
                0x0605f128                hx330x_sysDcacheInit
                0x0605f16c                hx330x_sysIcacheInit
                0x0605f1b0                hx330x_sysDcacheInvalid
                0x0605f248                hx330x_sysSRAMClear
                0x0605f29c                hx330x_sysBSSClear
                0x0605f300                hx330x_sysLDOSet
                0x0605f4b0                hx330x_sysReset
                0x0605f4f0                hx330x_mcpy0_llp
                0x0605f580                hx330x_sysInit
                0x0605f6ac                hx330x_sysUninit
 .text          0x0605f760      0x658 ..\lib\libmcu.a(hx330x_timer.o)
                0x0605f760                hx330x_timerISRRegister
                0x0605f7a4                hx330x_timerStart
                0x0605f8f0                hx330x_timerStop
                0x0605f9c0                hx330x_timerEnable
                0x0605fa4c                hx330x_timerDisable
                0x0605faf0                hx330x_timerPWMStart
 .text          0x0605fdb8      0x444 ..\lib\libmcu.a(hx330x_tminf.o)
                0x0605fdb8                hx330x_mjpA_TimeinfoEnable
                0x0605fe10                hx330x_mjpB_TimeinfoEnable
                0x0605fe7c                hx330x_mjpA_TimeinfoColor
                0x0605fec0                hx330x_mjpB_TimeinfoColor
                0x0605ff14                hx330x_mjpA_TimeinfoSize
                0x0605ff70                hx330x_mjpB_TimeinfoSize
                0x0605fff8                hx330x_mjpA_TimeinfoPos
                0x06060054                hx330x_mjpB_TimeinfoPos
                0x060600dc                hx330x_mjpA_TimeinfoAddr
                0x06060120                hx330x_mjpB_TimeinfoAddr
                0x06060190                hx330x_recfg_mjpb_tminf
 .text          0x060601fc      0x224 ..\lib\libmcu.a(hx330x_uart.o)
                0x060601fc                hx330x_uart0IOCfg
                0x06060380                hx330x_uart0Init
 .text          0x06060420      0xe10 ..\lib\libmcu.a(hx330x_usb.o)
                0x06060420                hx330x_usb20_CallbackInit
                0x06060450                hx330x_usb20_CallbackRegister
                0x0606048c                hx330x_usb20_eptx_register
                0x06060524                hx330x_usb20_eprx_register
                0x060605b4                hx330x_USB20_EPTX_Flush
                0x060605e0                hx330x_usb20_HighSpeed
                0x0606060c                hx330x_iso20_tx
                0x06060764                hx330x_iso20_tx_kick
                0x06060820                hx330x_usb20_dev_reset
                0x06060894                hx330x_usb20_host_speed_connect
                0x06060934                hx330x_usb20_host_reset
                0x0606099c                hx330x_usb20_dev_init
                0x06060af8                hx330x_usb20_host_init
                0x06060cdc                hx330x_usb20_uinit
                0x06060d54                get_u16softcnt
                0x06060d74                hx330x_usb11_CallbackInit
                0x06060da4                hx330x_usb11_CallbackRegister
                0x06060de0                hx330x_usb11_host_init
                0x06060ed4                hx330x_usb11_host_eprx_register
                0x06060f80                hx330x_usb11_host_reset
                0x06060fd8                hx330x_usb11_host_speed_connect
                0x06061020                hx330x_usb11_uinit
                0x060610e8                hx330x_usb20_dev_check_init
                0x06061104                hx330x_usb20_host_check_init
                0x06061120                hx330x_usb11_host_check_init
                0x0606113c                hx330x_usb20_device_check
                0x06061170                hx330x_usb20_host_check
                0x06061204                hx330x_usb11_host_check
 .text          0x06061230       0x14 ..\lib\libmcu.a(hx330x_wdt.o)
                0x06061230                hx330x_wdtTimeSet
 .text          0x06061244      0x184 ..\lib\libmcu.a(hx330x_emi.o)
                0x06061244                hx330x_emiInit
                0x060612f4                hx330x_emiISRRegister
                0x06061328                hx330x_emiKick
                0x06061384                hx330x_emiCheckBusy
                0x060613a4                hx330x_emiCheckRXError
 .text          0x060613c8     0x1c34 ..\lib\libisp.a(hal_isp.o)
                0x060616f0                hal_sensor_fps_adpt
                0x060617d0                hal_isp_process
                0x06062734                hal_sensor_awb_scene_set
                0x06062824                hal_sensor_EV_set
                0x0606293c                hal_isp_br_get
                0x0606295c                hal_ispService
                0x06062a3c                hal_isp_init
                0x06062ddc                hal_SensorRegister
                0x06062e74                hal_SensorApiGet
                0x06062e94                hal_SensorResolutionGet
                0x06062ef0                hal_SensorRotate
                0x06062f5c                hal_isplog_cnt
                0x06062fac                hal_isp_cur_yloga
 .text          0x06062ffc      0xd64 ..\lib\libjpg.a(hal_jpg.o)
                0x0606312c                hal_mjp_enle_init
                0x0606315c                hal_mjp_enle_unit
                0x06063200                hal_mjp_enle_check
                0x060637b0                hal_mjp_enle_manual_kickstart
                0x06063960                hal_mjp_enle_buf_mdf
                0x06063ad4                hal_mjp_enle_manual_done
 .text          0x06063d60      0xe58 ..\lib\liblcd.a(hal_lcd.o)
                0x06063db4                hal_lcdWinUpdata
                0x06063eb0                hal_lcdVideoRotateUpdata
                0x06063ff8                hal_lcdCsiShowStop
                0x06064078                hal_lcdVideoShowFrameGet
                0x060640e8                hal_lcdVideoIdleFrameMalloc
                0x0606412c                hal_lcdCsiShowStart
                0x06064294                hal_lcdVideoSetFrameWait
                0x060642d8                hal_lcdVideoSetFrame
                0x060643bc                hal_lcdBrightnessGet
                0x060643ec                hal_lcdRegister
                0x06064ab4                hal_lcdFrameEndCallBackRegister
                0x06064afc                hal_lcd_send_cmd
 .text          0x06064bb8      0xe88 ..\lib\liblcd.a(hal_lcdMem.o)
                0x06064c88                hal_lcdframes_vr_init
                0x06064e08                hal_lcdframes_vd_init
                0x06064f98                hal_dispframePrintf
                0x06064fe8                hal_dispframeInit
                0x06065778                hal_dispframeUinit
                0x06065804                hal_dispframeMalloc
                0x060658cc                hal_lcdVideoFrameFlush
 .text          0x06065a40      0x384 ..\lib\liblcd.a(hal_lcdrotate.o)
                0x06065bc8                hal_rotateInit
                0x06065c1c                hal_rotateAdd
 .text          0x06065dc4      0x718 ..\lib\liblcd.a(hal_lcdUi.o)
                0x06065dc4                hal_uiRotateBufMalloc
                0x06065de8                hal_uiLzoBufMalloc
                0x06065e0c                hal_uiDrawBufMalloc
                0x06065e7c                hal_lcdUiKickWait
                0x06065ed8                hal_lcdUiKick
                0x06065f64                hal_lcdUiSetAddr
                0x06065fd4                hal_lcdUiSetBuffer
                0x06066068                hal_lcdUiSetBufferWaitDone
                0x060660bc                hal_lcdUiInit
                0x06066258                hal_lcdUiSetPosition
                0x060662d4                hal_lcdUiSetPalette
                0x06066328                hal_lcdUiSetSize
                0x060663a4                hal_lcdUiSetAlpha
                0x06066420                hal_lcdUiResolutionGet
                0x0606648c                hal_lcdVideo_CCM_cfg
                0x060664b4                hal_lcdVideo_SAJ_cfg
 .text          0x060664dc      0x1a8 ..\lib\liblcd.a(hal_lcdUiLzo.o)
                0x06066578                hal_uiLzokick
                0x0606665c                hal_uiLzoInit
 .text          0x06066684      0x1ec ..\lib\liblcd.a(lcd_tab.o)
                0x06066684                hal_lcdParaLoad
                0x06066748                hal_lcdSetLsawbtooth
                0x060667fc                hal_lcdPQToolGetInfo
 .text          0x06066870      0xa70 ..\lib\libmultimedia.a(api_multimedia.o)
                0x06066a28                api_multimedia_cache_init
                0x06066b24                api_multimedia_cachePreRead
                0x06066bb4                api_multimedia_cacheRead
                0x06066c30                api_multimedia_init
                0x06066d80                api_multimedia_uninit
                0x06066e04                api_multimedia_start
                0x06066e80                api_multimedia_end
                0x06066efc                api_multimedia_service
                0x06066f78                api_multimedia_addjunk
                0x06066ff4                api_multimedia_encodeframe
                0x06067074                api_multimedia_decodeframe
                0x060670f0                api_multimedia_gettime
                0x0606716c                api_multimedia_getsta
                0x060671e8                api_multimedia_decodefast
                0x06067264                api_multimedia_getArg
 .text          0x060672e0     0x1440 ..\lib\libmultimedia.a(avi_dec.o)
 .text          0x06068720      0xed4 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .text          0x060695f4      0xaa4 ..\lib\libmultimedia.a(avi_std_enc.o)
                0x06069c4c                avi_stdEncidxEnd
                0x06069d40                avi_stdEncEnd
 .text          0x0606a098      0x958 ..\lib\libmultimedia.a(wav_dec.o)
 .text          0x0606a9f0      0x414 ..\lib\libmultimedia.a(wav_enc.o)
                0x0606a9f0                wav_EncEnd
 .text          0x0606ae04      0x1c8 ..\lib\libmultimedia.a(wav_pcm.o)
                0x0606ae04                pcm_encode
                0x0606aeb4                pcm_decode
 .text          0x0606afcc       0xb0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                0x0606afcc                memcmp
 .text          0x0606b07c      0x158 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                0x0606b07c                memcpy
 .text          0x0606b1d4      0x15c D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                0x0606b1d4                memset
 .text          0x0606b330       0xc0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                0x0606b330                strcpy
 .text          0x0606b3f0      0x7c8 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                0x0606b3f0                __udivdi3
 .text          0x0606bbb8      0x7c8 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                0x0606bbb8                __umoddi3
 .text          0x0606c380       0xf4 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
                0x0606c380                __udivsi3
                0x0606c380                __udivsi3_internal
 .text          0x0606c474       0x1c D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
                0x0606c474                __umodsi3
 .text          0x0606c490        0x0 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
 *(.rodata*)
 .rodata.str1.1
                0x0606c490       0x15 obj\Debug\dev\battery\src\battery_api.o
 *fill*         0x0606c4a5        0x3 
 .rodata        0x0606c4a8        0xc obj\Debug\dev\battery\src\battery_api.o
 .rodata.str1.1
                0x0606c4b4       0x49 obj\Debug\dev\dev_api.o
 .rodata.str1.1
                0x0606c4fd       0x65 obj\Debug\dev\fs\src\ff.o
                                 0x69 (size before relaxing)
 *fill*         0x0606c562        0x2 
 .rodata.cst4   0x0606c564        0x4 obj\Debug\dev\fs\src\ff.o
 .rodata        0x0606c568       0xb0 obj\Debug\dev\fs\src\ff.o
 .rodata        0x0606c618      0x3d4 obj\Debug\dev\fs\src\ffunicode.o
 .rodata.str1.1
                0x0606c9ec       0xb7 obj\Debug\dev\fs\src\fs_api.o
                                 0xb8 (size before relaxing)
 .rodata.str1.1
                0x0606caa3       0x7a obj\Debug\dev\gsensor\src\gsensor_api.o
 *fill*         0x0606cb1d        0x3 
 .rodata        0x0606cb20        0xc obj\Debug\dev\gsensor\src\gsensor_api.o
 .rodata        0x0606cb2c       0x38 obj\Debug\dev\gsensor\src\gsensor_da380.o
                0x0606cb44                da380
 .rodata        0x0606cb64       0x38 obj\Debug\dev\gsensor\src\gsensor_gma301.o
                0x0606cb7c                gma301
 .rodata        0x0606cb9c       0x38 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
                0x0606cbb4                sc7a30e
 .rodata.str1.1
                0x0606cbd4       0x16 obj\Debug\dev\lcd\src\lcd_api.o
 .rodata.str1.1
                0x0606cbea       0x1d obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .rodata.str1.1
                0x0606cc07       0x6a obj\Debug\dev\nvfs\src\nvfs_api.o
 .rodata.str1.1
                0x0606cc71       0x8b obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .rodata.str1.1
                0x0606ccfc      0x143 obj\Debug\dev\sd\src\sd_api.o
 *fill*         0x0606ce3f        0x1 
 .rodata        0x0606ce40      0x160 obj\Debug\dev\sd\src\sd_api.o
                0x0606ce40                sd_ident_tab
 .rodata.str1.1
                0x0606cfa0       0xc3 obj\Debug\dev\sensor\src\sensor_api.o
 *fill*         0x0606d063        0x1 
 .rodata        0x0606d064       0x44 obj\Debug\dev\sensor\src\sensor_api.o
                0x0606d064                user_ccf_dn_tab
                0x0606d070                user_ee_dn_tab
                0x0606d08c                user_ee_sharp_tab
 .rodata        0x0606d0a8      0x758 obj\Debug\dev\sensor\src\sensor_tab.o
                0x0606d0a8                test_img_init
                0x0606d0ec                test_img_adpt
 .rodata.str1.1
                0x0606d800       0x57 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .rodata.str1.1
                0x0606d857       0x47 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 *fill*         0x0606d89e        0x2 
 .rodata        0x0606d8a0       0x1c obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                0x0606d8a0                tp_icnt81
 .rodata.str1.1
                0x0606d8bc       0xa8 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .rodata.str1.1
                0x0606d964       0x13 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                                 0x17 (size before relaxing)
 *fill*         0x0606d977        0x1 
 .rodata        0x0606d978       0x2c obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                0x0606d988                tp_ns2009
 .rodata.str1.1
                0x0606d9a4       0x50 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .rodata        0x0606d9f4      0x100 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x0606da40                StringDescTbl
                0x0606da58                DEV_QAULIFIER_DESC_DATA
                0x0606da64                UsbStrDescSerialNumber
                0x0606da84                UsbStrDescProduct_1
                0x0606da9c                UsbStrDescProduct_0
                0x0606dab4                UsbStrDescManufacturer
                0x0606dac4                UsbLanguageID
                0x0606dac8                dusb_com_devdsc
                0x0606dadc                dusb_msc_devdsc
                0x0606daf0                SDK_CHIP_INF
 .rodata.str1.1
                0x0606daf4       0x4d obj\Debug\dev\usb\dusb\src\dusb_msc.o
 *fill*         0x0606db41        0x3 
 .rodata        0x0606db44       0x40 obj\Debug\dev\usb\dusb\src\dusb_msc.o
                0x0606db44                MscSenseCode
                0x0606db50                device_inquiry_data
 .rodata.str1.1
                0x0606db84        0xc obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .rodata        0x0606db90        0x4 obj\Debug\dev\usb\dusb\src\dusb_uac.o
                0x0606db90                uac_vol_tbl
 .rodata        0x0606db94      0x1e0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                0x0606dba8                uvc_ctl_tab
                0x0606dce8                unit_callback
                0x0606dd38                vc_still_probe_commit_desc
                0x0606dd44                vc_probe_commit_desc
                0x0606dd60                uvc_res_tab
 .rodata.str1.1
                0x0606dd74       0x69 obj\Debug\dev\usb\husb\src\husb_api.o
 .rodata.str1.1
                0x0606dddd      0x5e4 obj\Debug\dev\usb\husb\src\husb_enum.o
                                0x5e6 (size before relaxing)
 *fill*         0x0606e3c1        0x3 
 .rodata        0x0606e3c4      0x2c0 obj\Debug\dev\usb\husb\src\husb_enum.o
                0x0606e3f4                husb_uvcunit_set_hcdtrb
                0x0606e414                husb_uvcunit_get_hcdtrb
                0x0606e434                husb_astern_hcdtrb
                0x0606e454                husb_uvc_switch_hcdtrb
                0x0606e4d4                husb_enum_hcdtrb
 .rodata.str1.1
                0x0606e684       0xa8 obj\Debug\dev\usb\husb\src\husb_hub.o
 .rodata.str1.1
                0x0606e72c      0x271 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .rodata.str1.1
                0x0606e99d       0x17 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .rodata.str1.1
                0x0606e9b4      0x17b obj\Debug\dev\usb\husb\src\husb_uvc.o
                                0x198 (size before relaxing)
 .rodata.str1.1
                0x0606eb2f       0xc1 obj\Debug\hal\src\hal_auadc.o
 .rodata.str1.1
                0x0606ebf0       0x8c obj\Debug\hal\src\hal_csi.o
 .rodata        0x0606ec7c       0x2c obj\Debug\hal\src\hal_dac.o
 .rodata.str1.1
                0x0606eca8       0x6b obj\Debug\hal\src\hal_dmauart.o
 .rodata.str1.1
                0x0606ed13       0x8d obj\Debug\hal\src\hal_lcdshow.o
                                 0x93 (size before relaxing)
 .rodata        0x0606eda0       0x14 obj\Debug\hal\src\hal_lcdshow.o
 .rodata.str1.1
                0x0606edb4      0x193 obj\Debug\hal\src\hal_mjpAEncode.o
                                0x195 (size before relaxing)
 *fill*         0x0606ef47        0x1 
 .rodata        0x0606ef48        0xc obj\Debug\hal\src\hal_mjpAEncode.o
                0x0606ef48                mjpegQualityTable
 .rodata.str1.1
                0x0606ef54       0xbb obj\Debug\hal\src\hal_mjpBEncode.o
 .rodata.str1.1
                0x0606f00f       0x47 obj\Debug\hal\src\hal_mjpDecode.o
 *fill*         0x0606f056        0x2 
 .rodata        0x0606f058        0x8 obj\Debug\hal\src\hal_mjpDecode.o
 .rodata.str1.1
                0x0606f060        0xa obj\Debug\hal\src\hal_rtc.o
 *fill*         0x0606f06a        0x2 
 .rodata        0x0606f06c        0xc obj\Debug\hal\src\hal_rtc.o
                0x0606f06c                monDaysTable
 .rodata.str1.1
                0x0606f078       0xdf obj\Debug\hal\src\hal_spi1.o
 .rodata.str1.1
                0x0606f157       0xc7 obj\Debug\hal\src\hal_sys.o
 .rodata.str1.1
                0x0606f21e       0x14 obj\Debug\hal\src\hal_timer.o
 .rodata.str1.1
                0x0606f232       0x3a obj\Debug\hal\src\hal_watermark.o
 .rodata.str1.1
                0x0606f26c      0x116 obj\Debug\multimedia\audio\audio_playback.o
 .rodata.str1.1
                0x0606f382       0x98 obj\Debug\multimedia\audio\audio_record.o
 .rodata.str1.1
                0x0606f41a       0xc5 obj\Debug\multimedia\image\image_decode.o
 .rodata.str1.1
                0x0606f4df      0x1a4 obj\Debug\multimedia\image\image_encode.o
 *fill*         0x0606f683        0x1 
 .rodata        0x0606f684      0x2dc obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                0x0606f684                exif_head
 .rodata.str1.1
                0x0606f960      0x323 obj\Debug\multimedia\video\video_playback.o
                                0x327 (size before relaxing)
 *fill*         0x0606fc83        0x1 
 .rodata        0x0606fc84        0x8 obj\Debug\multimedia\video\video_playback.o
 .rodata.str1.1
                0x0606fc8c      0x31e obj\Debug\multimedia\video\video_record.o
 .rodata.str1.1
                0x0606ffaa      0x1a6 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .rodata.str1.1
                0x06070150       0x95 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                                 0xb1 (size before relaxing)
 *fill*         0x060701e5        0x3 
 .rodata        0x060701e8      0xeb0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
                0x060701e8                ascii_num1_table
                0x06070360                ascii_num1_126
                0x06070384                ascii_num1_125
                0x060703a8                ascii_num1_124
                0x060703cc                ascii_num1_123
                0x060703f0                ascii_num1_122
                0x06070414                ascii_num1_121
                0x06070438                ascii_num1_120
                0x0607045c                ascii_num1_119
                0x06070480                ascii_num1_118
                0x060704a4                ascii_num1_117
                0x060704c8                ascii_num1_116
                0x060704ec                ascii_num1_115
                0x06070510                ascii_num1_114
                0x06070534                ascii_num1_113
                0x06070558                ascii_num1_112
                0x0607057c                ascii_num1_111
                0x060705a0                ascii_num1_110
                0x060705c4                ascii_num1_109
                0x060705e8                ascii_num1_108
                0x0607060c                ascii_num1_107
                0x06070630                ascii_num1_106
                0x06070654                ascii_num1_105
                0x06070678                ascii_num1_104
                0x0607069c                ascii_num1_103
                0x060706c0                ascii_num1_102
                0x060706e4                ascii_num1_101
                0x06070708                ascii_num1_100
                0x0607072c                ascii_num1_99
                0x06070750                ascii_num1_98
                0x06070774                ascii_num1_97
                0x06070798                ascii_num1_96
                0x060707bc                ascii_num1_95
                0x060707e0                ascii_num1_94
                0x06070804                ascii_num1_93
                0x06070828                ascii_num1_92
                0x0607084c                ascii_num1_91
                0x06070870                ascii_num1_90
                0x06070894                ascii_num1_89
                0x060708b8                ascii_num1_88
                0x060708dc                ascii_num1_87
                0x06070900                ascii_num1_86
                0x06070924                ascii_num1_85
                0x06070948                ascii_num1_84
                0x0607096c                ascii_num1_83
                0x06070990                ascii_num1_82
                0x060709b4                ascii_num1_81
                0x060709d8                ascii_num1_80
                0x060709fc                ascii_num1_79
                0x06070a20                ascii_num1_78
                0x06070a44                ascii_num1_77
                0x06070a68                ascii_num1_76
                0x06070a8c                ascii_num1_75
                0x06070ab0                ascii_num1_74
                0x06070ad4                ascii_num1_73
                0x06070af8                ascii_num1_72
                0x06070b1c                ascii_num1_71
                0x06070b40                ascii_num1_70
                0x06070b64                ascii_num1_69
                0x06070b88                ascii_num1_68
                0x06070bac                ascii_num1_67
                0x06070bd0                ascii_num1_66
                0x06070bf4                ascii_num1_65
                0x06070c18                ascii_num1_64
                0x06070c3c                ascii_num1_63
                0x06070c60                ascii_num1_62
                0x06070c84                ascii_num1_61
                0x06070ca8                ascii_num1_60
                0x06070ccc                ascii_num1_59
                0x06070cf0                ascii_num1_58
                0x06070d14                ascii_num1_57
                0x06070d38                ascii_num1_56
                0x06070d5c                ascii_num1_55
                0x06070d80                ascii_num1_54
                0x06070da4                ascii_num1_53
                0x06070dc8                ascii_num1_52
                0x06070dec                ascii_num1_51
                0x06070e10                ascii_num1_50
                0x06070e34                ascii_num1_49
                0x06070e58                ascii_num1_48
                0x06070e7c                ascii_num1_47
                0x06070ea0                ascii_num1_46
                0x06070ec4                ascii_num1_45
                0x06070ee8                ascii_num1_44
                0x06070f0c                ascii_num1_43
                0x06070f30                ascii_num1_42
                0x06070f54                ascii_num1_41
                0x06070f78                ascii_num1_40
                0x06070f9c                ascii_num1_39
                0x06070fc0                ascii_num1_38
                0x06070fe4                ascii_num1_37
                0x06071008                ascii_num1_36
                0x0607102c                ascii_num1_35
                0x06071050                ascii_num1_33
                0x06071074                ascii_num1_32
 .rodata        0x06071098     0x1a70 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
                0x06071098                ascii_num2_table
                0x06071210                ascii_num2_126
                0x06071254                ascii_num2_125
                0x06071298                ascii_num2_124
                0x060712dc                ascii_num2_123
                0x06071320                ascii_num2_122
                0x06071364                ascii_num2_121
                0x060713a8                ascii_num2_120
                0x060713ec                ascii_num2_119
                0x06071430                ascii_num2_118
                0x06071474                ascii_num2_117
                0x060714b8                ascii_num2_116
                0x060714fc                ascii_num2_115
                0x06071540                ascii_num2_114
                0x06071584                ascii_num2_113
                0x060715c8                ascii_num2_112
                0x0607160c                ascii_num2_111
                0x06071650                ascii_num2_110
                0x06071694                ascii_num2_109
                0x060716d8                ascii_num2_108
                0x0607171c                ascii_num2_107
                0x06071760                ascii_num2_106
                0x060717a4                ascii_num2_105
                0x060717e8                ascii_num2_104
                0x0607182c                ascii_num2_103
                0x06071870                ascii_num2_102
                0x060718b4                ascii_num2_101
                0x060718f8                ascii_num2_100
                0x0607193c                ascii_num2_99
                0x06071980                ascii_num2_98
                0x060719c4                ascii_num2_97
                0x06071a08                ascii_num2_96
                0x06071a4c                ascii_num2_95
                0x06071a90                ascii_num2_94
                0x06071ad4                ascii_num2_93
                0x06071b18                ascii_num2_92
                0x06071b5c                ascii_num2_91
                0x06071ba0                ascii_num2_90
                0x06071be4                ascii_num2_89
                0x06071c28                ascii_num2_88
                0x06071c6c                ascii_num2_87
                0x06071cb0                ascii_num2_86
                0x06071cf4                ascii_num2_85
                0x06071d38                ascii_num2_84
                0x06071d7c                ascii_num2_83
                0x06071dc0                ascii_num2_82
                0x06071e04                ascii_num2_81
                0x06071e48                ascii_num2_80
                0x06071e8c                ascii_num2_79
                0x06071ed0                ascii_num2_78
                0x06071f14                ascii_num2_77
                0x06071f58                ascii_num2_76
                0x06071f9c                ascii_num2_75
                0x06071fe0                ascii_num2_74
                0x06072024                ascii_num2_73
                0x06072068                ascii_num2_72
                0x060720ac                ascii_num2_71
                0x060720f0                ascii_num2_70
                0x06072134                ascii_num2_69
                0x06072178                ascii_num2_68
                0x060721bc                ascii_num2_67
                0x06072200                ascii_num2_66
                0x06072244                ascii_num2_65
                0x06072288                ascii_num2_64
                0x060722cc                ascii_num2_63
                0x06072310                ascii_num2_62
                0x06072354                ascii_num2_61
                0x06072398                ascii_num2_60
                0x060723dc                ascii_num2_59
                0x06072420                ascii_num2_58
                0x06072464                ascii_num2_57
                0x060724a8                ascii_num2_56
                0x060724ec                ascii_num2_55
                0x06072530                ascii_num2_54
                0x06072574                ascii_num2_53
                0x060725b8                ascii_num2_52
                0x060725fc                ascii_num2_51
                0x06072640                ascii_num2_50
                0x06072684                ascii_num2_49
                0x060726c8                ascii_num2_48
                0x0607270c                ascii_num2_47
                0x06072750                ascii_num2_46
                0x06072794                ascii_num2_45
                0x060727d8                ascii_num2_44
                0x0607281c                ascii_num2_43
                0x06072860                ascii_num2_42
                0x060728a4                ascii_num2_41
                0x060728e8                ascii_num2_40
                0x0607292c                ascii_num2_39
                0x06072970                ascii_num2_38
                0x060729b4                ascii_num2_37
                0x060729f8                ascii_num2_36
                0x06072a3c                ascii_num2_35
                0x06072a80                ascii_num2_33
                0x06072ac4                ascii_num2_32
 .rodata        0x06072b08     0x5430 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
                0x06072b08                ascii_num3_table
                0x06072c80                ascii_num3_126
                0x06072d84                ascii_num3_125
                0x06072e08                ascii_num3_124
                0x06072e8c                ascii_num3_123
                0x06072f10                ascii_num3_122
                0x06072fd4                ascii_num3_121
                0x06073098                ascii_num3_120
                0x0607315c                ascii_num3_119
                0x06073260                ascii_num3_118
                0x06073324                ascii_num3_117
                0x06073428                ascii_num3_116
                0x060734ac                ascii_num3_115
                0x06073570                ascii_num3_114
                0x060735f4                ascii_num3_113
                0x060736f8                ascii_num3_112
                0x060737fc                ascii_num3_111
                0x06073900                ascii_num3_110
                0x06073a04                ascii_num3_109
                0x06073b48                ascii_num3_108
                0x06073bcc                ascii_num3_107
                0x06073c90                ascii_num3_106
                0x06073d14                ascii_num3_105
                0x06073d98                ascii_num3_104
                0x06073e9c                ascii_num3_103
                0x06073fa0                ascii_num3_102
                0x06074024                ascii_num3_101
                0x06074128                ascii_num3_100
                0x0607422c                ascii_num3_99
                0x060742f0                ascii_num3_98
                0x060743f4                ascii_num3_97
                0x060744f8                ascii_num3_96
                0x0607457c                ascii_num3_95
                0x06074640                ascii_num3_94
                0x06074704                ascii_num3_93
                0x06074788                ascii_num3_92
                0x0607480c                ascii_num3_91
                0x06074890                ascii_num3_90
                0x06074994                ascii_num3_89
                0x06074a98                ascii_num3_88
                0x06074b9c                ascii_num3_87
                0x06074d20                ascii_num3_86
                0x06074e24                ascii_num3_85
                0x06074f28                ascii_num3_84
                0x0607502c                ascii_num3_83
                0x06075130                ascii_num3_82
                0x06075234                ascii_num3_81
                0x06075378                ascii_num3_80
                0x0607547c                ascii_num3_79
                0x060755c0                ascii_num3_78
                0x060756c4                ascii_num3_77
                0x06075808                ascii_num3_76
                0x0607590c                ascii_num3_75
                0x06075a10                ascii_num3_74
                0x06075ad4                ascii_num3_73
                0x06075b58                ascii_num3_72
                0x06075c5c                ascii_num3_71
                0x06075da0                ascii_num3_70
                0x06075ea4                ascii_num3_69
                0x06075fa8                ascii_num3_68
                0x060760ac                ascii_num3_67
                0x060761b0                ascii_num3_66
                0x060762b4                ascii_num3_65
                0x060763b8                ascii_num3_64
                0x0607653c                ascii_num3_63
                0x06076640                ascii_num3_62
                0x06076744                ascii_num3_61
                0x06076848                ascii_num3_60
                0x0607694c                ascii_num3_59
                0x060769d0                ascii_num3_58
                0x06076a54                ascii_num3_57
                0x06076b58                ascii_num3_56
                0x06076c5c                ascii_num3_55
                0x06076d60                ascii_num3_54
                0x06076e64                ascii_num3_53
                0x06076f68                ascii_num3_52
                0x0607706c                ascii_num3_51
                0x06077170                ascii_num3_50
                0x06077274                ascii_num3_49
                0x06077378                ascii_num3_48
                0x0607747c                ascii_num3_47
                0x06077500                ascii_num3_46
                0x06077584                ascii_num3_45
                0x06077608                ascii_num3_44
                0x0607768c                ascii_num3_43
                0x06077790                ascii_num3_42
                0x06077854                ascii_num3_41
                0x060778d8                ascii_num3_40
                0x0607795c                ascii_num3_39
                0x060779e0                ascii_num3_38
                0x06077ae4                ascii_num3_37
                0x06077c28                ascii_num3_36
                0x06077d2c                ascii_num3_35
                0x06077e30                ascii_num3_33
                0x06077eb4                ascii_num3_32
 .rodata        0x06077f38     0x7870 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
                0x06077f38                ascii_num4_table
                0x060780b0                ascii_num4_126
                0x060781f4                ascii_num4_125
                0x06078338                ascii_num4_124
                0x0607847c                ascii_num4_123
                0x060785c0                ascii_num4_122
                0x06078704                ascii_num4_121
                0x06078848                ascii_num4_120
                0x0607898c                ascii_num4_119
                0x06078ad0                ascii_num4_118
                0x06078c14                ascii_num4_117
                0x06078d58                ascii_num4_116
                0x06078e9c                ascii_num4_115
                0x06078fe0                ascii_num4_114
                0x06079124                ascii_num4_113
                0x06079268                ascii_num4_112
                0x060793ac                ascii_num4_111
                0x060794f0                ascii_num4_110
                0x06079634                ascii_num4_109
                0x06079778                ascii_num4_108
                0x060798bc                ascii_num4_107
                0x06079a00                ascii_num4_106
                0x06079b44                ascii_num4_105
                0x06079c88                ascii_num4_104
                0x06079dcc                ascii_num4_103
                0x06079f10                ascii_num4_102
                0x0607a054                ascii_num4_101
                0x0607a198                ascii_num4_100
                0x0607a2dc                ascii_num4_99
                0x0607a420                ascii_num4_98
                0x0607a564                ascii_num4_97
                0x0607a6a8                ascii_num4_96
                0x0607a7ec                ascii_num4_95
                0x0607a930                ascii_num4_94
                0x0607aa74                ascii_num4_93
                0x0607abb8                ascii_num4_92
                0x0607acfc                ascii_num4_91
                0x0607ae40                ascii_num4_90
                0x0607af84                ascii_num4_89
                0x0607b0c8                ascii_num4_88
                0x0607b20c                ascii_num4_87
                0x0607b350                ascii_num4_86
                0x0607b494                ascii_num4_85
                0x0607b5d8                ascii_num4_84
                0x0607b71c                ascii_num4_83
                0x0607b860                ascii_num4_82
                0x0607b9a4                ascii_num4_81
                0x0607bae8                ascii_num4_80
                0x0607bc2c                ascii_num4_79
                0x0607bd70                ascii_num4_78
                0x0607beb4                ascii_num4_77
                0x0607bff8                ascii_num4_76
                0x0607c13c                ascii_num4_75
                0x0607c280                ascii_num4_74
                0x0607c3c4                ascii_num4_73
                0x0607c508                ascii_num4_72
                0x0607c64c                ascii_num4_71
                0x0607c790                ascii_num4_70
                0x0607c8d4                ascii_num4_69
                0x0607ca18                ascii_num4_68
                0x0607cb5c                ascii_num4_67
                0x0607cca0                ascii_num4_66
                0x0607cde4                ascii_num4_65
                0x0607cf28                ascii_num4_64
                0x0607d06c                ascii_num4_63
                0x0607d1b0                ascii_num4_62
                0x0607d2f4                ascii_num4_61
                0x0607d438                ascii_num4_60
                0x0607d57c                ascii_num4_59
                0x0607d6c0                ascii_num4_58
                0x0607d804                ascii_num4_57
                0x0607d948                ascii_num4_56
                0x0607da8c                ascii_num4_55
                0x0607dbd0                ascii_num4_54
                0x0607dd14                ascii_num4_53
                0x0607de58                ascii_num4_52
                0x0607df9c                ascii_num4_51
                0x0607e0e0                ascii_num4_50
                0x0607e224                ascii_num4_49
                0x0607e368                ascii_num4_48
                0x0607e4ac                ascii_num4_47
                0x0607e5f0                ascii_num4_46
                0x0607e734                ascii_num4_45
                0x0607e878                ascii_num4_44
                0x0607e9bc                ascii_num4_43
                0x0607eb00                ascii_num4_42
                0x0607ec44                ascii_num4_41
                0x0607ed88                ascii_num4_40
                0x0607eecc                ascii_num4_39
                0x0607f010                ascii_num4_38
                0x0607f154                ascii_num4_37
                0x0607f298                ascii_num4_36
                0x0607f3dc                ascii_num4_35
                0x0607f520                ascii_num4_33
                0x0607f664                ascii_num4_32
 .rodata.str1.1
                0x0607f7a8       0xae obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .rodata.str1.1
                0x0607f856       0x5c obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .rodata.str1.1
                0x0607f8b2       0x1b obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .rodata.str1.1
                0x0607f8cd       0x27 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .rodata        0x0607f8f4      0x71c obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
                0x0607f8f4                res_key_music
 .rodata.str1.1
                0x06080010       0x13 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .rodata.str1.1
                0x06080023       0x2e obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 *fill*         0x06080051        0x3 
 .rodata        0x06080054       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .rodata.str1.1
                0x06080064       0x2f obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 *fill*         0x06080093        0x1 
 .rodata        0x06080094       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .rodata        0x060800a4       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .rodata.str1.1
                0x06080110       0x17 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 *fill*         0x06080127        0x1 
 .rodata        0x06080128       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .rodata        0x06080194       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .rodata        0x06080200       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .rodata.str1.1
                0x0608026c      0x198 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .rodata        0x06080404       0x1c obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .rodata.str1.1
                0x06080420       0x5b obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 *fill*         0x0608047b        0x1 
 .rodata        0x0608047c       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .rodata        0x060804e8       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .rodata        0x06080554       0x64 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .rodata.str1.1
                0x060805b8      0x11c obj\Debug\app\app_common\src\app_init.o
                                0x123 (size before relaxing)
 .rodata.str1.1
                0x060806d4       0x58 obj\Debug\app\app_common\src\app_lcdshow.o
 .rodata        0x0608072c       0x14 obj\Debug\app\app_common\src\app_lcdshow.o
 .rodata.str1.1
                0x06080740       0x8a obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                                 0x8e (size before relaxing)
 *fill*         0x060807ca        0x2 
 .rodata        0x060807cc      0x140 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                0x060807f4                dateTimeWin
 .rodata.str1.1
                0x0608090c       0x46 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 *fill*         0x06080952        0x2 
 .rodata        0x06080954      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                0x06080954                defaultWin
 .rodata.str1.1
                0x06080a6c       0x43 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 *fill*         0x06080aaf        0x1 
 .rodata        0x06080ab0      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                0x06080ab0                delAllWin
 .rodata.str1.1
                0x06080bc8       0x5e obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 *fill*         0x06080c26        0x2 
 .rodata        0x06080c28      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                0x06080c28                delCurWin
 .rodata.str1.1
                0x06080d40       0x6c obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                                 0x6d (size before relaxing)
 .rodata        0x06080dac      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                0x06080dac                formatWin
 .rodata.str1.1
                0x06080ec4       0x56 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                                 0x5d (size before relaxing)
 *fill*         0x06080f1a        0x2 
 .rodata        0x06080f1c       0xd8 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x06080f2c                menuItemWin
 .rodata.str1.1
                0x06080ff4       0x46 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                                 0x65 (size before relaxing)
 *fill*         0x0608103a        0x2 
 .rodata        0x0608103c       0x78 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                0x0608103c                lockCurWin
 .rodata.str1.1
                0x060810b4       0x4f obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                                 0x51 (size before relaxing)
 *fill*         0x06081103        0x1 
 .rodata        0x06081104       0xc8 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                0x06081104                menuOptionWin
 .rodata.str1.1
                0x060811cc       0x5f obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                                 0x6d (size before relaxing)
 *fill*         0x0608122b        0x1 
 .rodata        0x0608122c       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                0x0608122c                unlockAllWin
 .rodata.str1.1
                0x060812cc       0x4c obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                                 0x6d (size before relaxing)
 .rodata        0x06081318       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                0x06081318                unlockCurWin
 .rodata.str1.1
                0x060813b8       0x5d obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 *fill*         0x06081415        0x3 
 .rodata        0x06081418      0x348 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                0x06081418                asternWin
 .rodata.str1.1
                0x06081760       0x43 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 *fill*         0x060817a3        0x1 
 .rodata        0x060817a4       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                0x060817a4                noFileWin
 .rodata.str1.1
                0x0608181c      0x11a obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                                0x17b (size before relaxing)
 *fill*         0x06081936        0x2 
 .rodata        0x06081938      0x4e8 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                0x060819c0                selfTestWin
 .rodata.str1.1
                0x06081e20       0x40 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                                 0x42 (size before relaxing)
 .rodata        0x06081e60       0xa0 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                0x06081e60                tips1Win
 .rodata.str1.1
                0x06081f00       0x46 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                                 0x48 (size before relaxing)
 *fill*         0x06081f46        0x2 
 .rodata        0x06081f48       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                0x06081f48                tipsWin
 .rodata.str1.1
                0x06081fc0       0x43 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 *fill*         0x06082003        0x1 
 .rodata        0x06082004       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                0x06082004                TpIconWin
 .rodata.str1.1
                0x0608207c      0x148 obj\Debug\app\task_windows\task_api.o
 .rodata        0x060821c4       0x28 obj\Debug\app\task_windows\task_api.o
 .rodata.str1.1
                0x060821ec       0x73 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 *fill*         0x0608225f        0x1 
 .rodata        0x06082260       0x68 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
                0x06082260                sysComMsgDeal
 .rodata.str1.1
                0x060822c8      0x25a obj\Debug\app\task_windows\task_common\src\task_common.o
                                0x266 (size before relaxing)
 *fill*         0x06082522        0x2 
 .rodata        0x06082524       0x90 obj\Debug\app\task_windows\task_common\src\task_common.o
 .rodata.str1.1
                0x060825b4       0x15 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 *fill*         0x060825c9        0x3 
 .rodata        0x060825cc       0x10 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
                0x060825cc                taskComMsgDeal
 .rodata.str1.1
                0x060825dc       0x72 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .rodata.str1.1
                0x0608264e       0x63 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                                 0x65 (size before relaxing)
 *fill*         0x060826b1        0x3 
 .rodata        0x060826b4      0x128 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                0x060826c4                playAudioWin
 .rodata.str1.1
                0x060827dc      0x211 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .rodata.str1.1
                0x060829ed      0x124 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                                0x156 (size before relaxing)
 *fill*         0x06082b11        0x3 
 .rodata        0x06082b14      0x218 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                0x06082b24                playVideoMainWin
 .rodata.str1.1
                0x06082d2c       0x5b obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                                 0x5d (size before relaxing)
 *fill*         0x06082d87        0x1 
 .rodata        0x06082d88       0xc8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                0x06082d88                playVideoSlideWin
 .rodata.str1.1
                0x06082e50       0x8d obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                                 0x97 (size before relaxing)
 *fill*         0x06082edd        0x3 
 .rodata        0x06082ee0      0x4d4 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                0x06082ee0                playVideoThumbnallWin
 .rodata.str1.1
                0x060833b4        0xa obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .rodata.str1.1
                0x060833be       0xb1 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .rodata.str1.1
                0x0608346f        0x9 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                                 0x16 (size before relaxing)
 .rodata        0x06083478       0xa0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                0x06083478                RecordAudioWin
 .rodata.str1.1
                0x06083518      0x17d obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .rodata.str1.1
                0x06083695       0xe6 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                                0x11f (size before relaxing)
 *fill*         0x0608377b        0x1 
 .rodata        0x0608377c       0x10 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .rodata.str1.1
                0x0608378c      0x315 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                                0x321 (size before relaxing)
 .rodata.str1.1
                0x06083aa1       0xaf obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                                 0xfc (size before relaxing)
 .rodata        0x06083b50      0x1f0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                0x06083b60                recordVideoWin
 .rodata.str1.1
                0x06083d40      0x147 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                                0x159 (size before relaxing)
 .rodata.str1.1
                0x06083e87        0xa obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .rodata.str1.1
                0x06083e91       0x17 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .rodata        0x06083ea8       0x50 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                0x06083ea8                ShowLogoWin
 .rodata.str1.1
                0x06083ef8       0x45 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                                 0x51 (size before relaxing)
 .rodata.str1.1
                0x06083f3d       0x8d obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 *fill*         0x06083fca        0x2 
 .rodata        0x06083fcc      0x230 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                0x06083fcc                usbDeviceWin
 .rodata.str1.1
                0x060841fc      0x13b obj\Debug\app\task_windows\windows_api.o
                                0x155 (size before relaxing)
 *fill*         0x06084337        0x1 
 .rodata        0x06084338       0x1c obj\Debug\app\task_windows\windows_api.o
 .rodata.str1.1
                0x06084354       0x72 obj\Debug\app\user_config\src\mbedtls_md5.o
                                 0x75 (size before relaxing)
 *fill*         0x060843c6        0x2 
 .rodata        0x060843c8       0x40 obj\Debug\app\user_config\src\mbedtls_md5.o
 .rodata.str1.1
                0x06084408       0x81 obj\Debug\app\user_config\src\user_config_api.o
 *fill*         0x06084489        0x3 
 .rodata        0x0608448c      0x28c obj\Debug\app\user_config\src\user_config_api.o
 .rodata        0x06084718      0x100 obj\Debug\app\user_config\src\user_config_tab.o
                0x06084718                user_cfg_tab
 .rodata        0x06084818       0xd0 ..\lib\libmcu.a(hx330x_auadc.o)
                0x06084818                tbl_micvol
                0x06084858                auadc_samplerate_tab
 .rodata.str1.1
                0x060848e8        0xe ..\lib\libmcu.a(hx330x_csi.o)
 *fill*         0x060848f6        0x2 
 .rodata        0x060848f8      0x154 ..\lib\libmcu.a(hx330x_csi.o)
                0x060848f8                csi_dvp_map_tab
                0x0608490c                csi_dvp_map4
                0x0608494c                csi_dvp_map3
                0x0608498c                csi_dvp_map2
                0x060849cc                csi_dvp_map1
                0x06084a0c                csi_dvp_map0
 .rodata        0x06084a4c       0xac ..\lib\libmcu.a(hx330x_dac.o)
                0x06084a4c                gain
                0x06084a60                eq_coeff
                0x06084a98                hx330x_dacInit_table
 .rodata        0x06084af8      0x210 ..\lib\libmcu.a(hx330x_dma.o)
                0x06084af8                winDis_cfg
                0x06084ba8                winB_cfg
                0x06084c58                winA_cfg
 .rodata        0x06084d08       0xd0 ..\lib\libmcu.a(hx330x_dmauart.o)
                0x06084d08                uart1_IO_MAP_tab
 .rodata        0x06084dd8       0xb8 ..\lib\libmcu.a(hx330x_gpio.o)
 .rodata        0x06084e90      0x4a4 ..\lib\libmcu.a(hx330x_iic.o)
                0x06084e90                iic1_uinit_map_tab
                0x06084eac                iic1_init_map_tab
                0x06084ec8                iic1_uinit_map6
                0x06084ef0                iic1_init_map6
                0x06084f3c                iic1_uinit_map5
                0x06084f64                iic1_init_map5
                0x06084fb0                iic1_uinit_map4
                0x06084fd8                iic1_init_map4
                0x06085024                iic1_uinit_map3
                0x0608504c                iic1_init_map3
                0x06085098                iic1_uinit_map2
                0x060850c0                iic1_init_map2
                0x0608510c                iic1_uinit_map1
                0x06085134                iic1_init_map1
                0x06085180                iic1_uinit_map0
                0x060851a8                iic1_init_map0
                0x060851f4                iic0_init_map_tab
                0x06085204                iic0_init_map3
                0x06085250                iic0_init_map2
                0x0608529c                iic0_init_map1
                0x060852e8                iic0_init_map0
 .rodata        0x06085334       0xf0 ..\lib\libmcu.a(hx330x_int.o)
 .rodata        0x06085424      0x14c ..\lib\libmcu.a(hx330x_isp_tab.o)
                0x06085424                Ratio_of_Evstep
                0x0608552c                GAOS3X3_TAB
                0x06085538                GAOS5X5_TAB
                0x06085554                LOG_TAB
 .rodata        0x06085570       0x14 ..\lib\libmcu.a(hx330x_jpg.o)
 .rodata.str1.1
                0x06085584       0x16 ..\lib\libmcu.a(hx330x_jpg.o)
 *fill*         0x0608559a        0x2 
 .rodata        0x0608559c      0xda0 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                0x06085fdc                c_table_chroma
                0x0608601c                belta_table_chroma
                0x0608605c                alpha_table_chroma
                0x0608609c                c_table_luma
                0x060860dc                belta_table_luma
                0x0608611c                alpha_table_luma
                0x0608615c                bic_coef_tabR
                0x060861fc                bic_coef_tabL
                0x0608629c                bic_coef_tab
 .rodata        0x0608633c      0x11c ..\lib\libmcu.a(hx330x_lcd.o)
 .rodata        0x06086458       0xa0 ..\lib\libmcu.a(hx330x_lcdui.o)
 .rodata        0x060864f8      0x168 ..\lib\libmcu.a(hx330x_misc.o)
 .rodata        0x06086660      0x248 ..\lib\libmcu.a(hx330x_spi1.o)
                0x06086660                SPI1_CS_MAP_tab
                0x06086688                spi1PinCfg_tab
                0x060866a8                SPI1_2LINE_Pos3_tab
                0x060866e8                SPI1_3LINE_Pos3_tab
                0x06086728                SPI1_2LINE_Pos2_tab
                0x06086768                SPI1_3LINE_Pos2_tab
                0x060867a8                SPI1_2LINE_Pos1_tab
                0x060867e8                SPI1_3LINE_Pos1_tab
                0x06086828                SPI1_2LINE_Pos0_tab
                0x06086868                SPI1_3LINE_Pos0_tab
 .rodata        0x060868a8       0x18 ..\lib\libmcu.a(hx330x_sys.o)
 .rodata        0x060868c0      0x100 ..\lib\libmcu.a(hx330x_timer.o)
                0x060868c0                timer3_PWM_IO_MAP_tab
                0x060868f0                timer2_PWM_IO_MAP_tab
                0x06086928                timer1_PWM_IO_MAP_tab
                0x06086968                timer0_PWM_IO_MAP_tab
 .rodata        0x060869c0       0xd8 ..\lib\libmcu.a(hx330x_uart.o)
                0x060869c0                uart0_IO_MAP_tab
 .rodata.cst4   0x06086a98        0x8 ..\lib\libmcu.a(hx330x_usb.o)
 .rodata        0x06086aa0       0x68 ..\lib\libmcu.a(hx330x_emi.o)
                0x06086aa0                hx330x_emiPinConfigSlave_table
                0x06086ac8                hx330x_emiPinConfigMaster_table
 .rodata        0x06086b08       0x44 ..\lib\libisp.a(hal_isp.o)
 .rodata.str1.1
                0x06086b4c       0x12 ..\lib\libisp.a(hal_isp.o)
 .rodata.str1.1
                0x06086b5e      0x12f ..\lib\libjpg.a(hal_jpg.o)
 .rodata.str1.1
                0x06086c8d       0x36 ..\lib\liblcd.a(hal_lcd.o)
 .rodata.str1.1
                0x06086cc3      0x174 ..\lib\liblcd.a(hal_lcdMem.o)
 .rodata.str1.1
                0x06086e37       0x11 ..\lib\liblcd.a(hal_lcdrotate.o)
 .rodata.str1.1
                0x06086e48       0x12 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 *fill*         0x06086e5a        0x2 
 .rodata        0x06086e5c       0x20 ..\lib\libmultimedia.a(api_multimedia.o)
 .rodata.str1.1
                0x06086e7c       0x81 ..\lib\libmultimedia.a(avi_dec.o)
 *fill*         0x06086efd        0x3 
 .rodata        0x06086f00       0x30 ..\lib\libmultimedia.a(avi_dec.o)
                0x06086f00                avi_dec_func
 .rodata.str1.1
                0x06086f30       0x78 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .rodata        0x06086fa8     0xe830 ..\lib\libmultimedia.a(avi_odml_enc.o)
                0x06086fa8                avi_odml_enc_func
                0x06086fd8                avi_odml_header
 .rodata.str1.1
                0x060957d8       0x5f ..\lib\libmultimedia.a(avi_std_enc.o)
 *fill*         0x06095837        0x1 
 .rodata        0x06095838      0x218 ..\lib\libmultimedia.a(avi_std_enc.o)
                0x06095838                avi_std_enc_func
 .rodata.str1.1
                0x06095a50       0x3c ..\lib\libmultimedia.a(wav_dec.o)
 .rodata        0x06095a8c       0x30 ..\lib\libmultimedia.a(wav_dec.o)
                0x06095a8c                wav_dec_func
 .rodata.str1.1
                0x06095abc       0x24 ..\lib\libmultimedia.a(wav_enc.o)
 .rodata        0x06095ae0       0x30 ..\lib\libmultimedia.a(wav_enc.o)
                0x06095ae0                wav_enc_func
 .rodata        0x06095b10       0x2c ..\lib\libmultimedia.a(wav_pcm.o)
                0x06095b10                wav_pcm_head
 .rodata        0x06095b3c      0x100 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                0x06095b3c                __clz_tab

.lcd_resource   0x00000000     0x29bc load address 0x00095e00
 *(.lcd_res.struct)
 .lcd_res.struct
                0x00000000       0xac obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
                0x00000000                __lcd_desc
 *(.lcd_res*)
 .lcd_res       0x000000ac      0x110 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .lcd_res       0x000001bc     0x2800 ..\lib\liblcd.a(lcd_tab.o)
                0x000004bc                lcd_contra_tab
                0x000011bc                lcd_gamma

.sensor_resource
                0x00000000     0x3640 load address 0x000987bc
 *(.sensor_res.header)
 .sensor_res.header
                0x00000000       0x40 obj\Debug\dev\sensor\src\sensor_tab.o
                0x00000000                RES_SensorHeader
                0x00000040                _res_sensor_header_item_start = .
 *(.sensor_res.header.items)
                0x00000040                _res_sensor_header_item_end = .
 *(.sensor_res.isp_tab)
 .sensor_res.isp_tab
                0x00000040     0x3600 obj\Debug\dev\sensor\src\sensor_tab.o
                0x00000040                sensor_rgb_gamma
                0x00001240                sensor_ygamma_tab
 *(.sensor_res.struct)
 *(.sensor_res.init_tab)

.nes_resource   0x00000000        0x0 load address 0x0009be00
 *(.nes_games_tab)

.eh_frame       0x00003640    0x104e0 load address 0x0009bdfc
 *(.eh_frame)
 .eh_frame      0x00003640       0x50 obj\Debug\dev\battery\src\battery_api.o
 .eh_frame      0x00003690       0x80 obj\Debug\dev\dev_api.o
                                 0x94 (size before relaxing)
 .eh_frame      0x00003710       0xa8 obj\Debug\dev\fs\src\diskio.o
                                 0xbc (size before relaxing)
 .eh_frame      0x000037b8      0x914 obj\Debug\dev\fs\src\ff.o
                                0x928 (size before relaxing)
 .eh_frame      0x000040cc       0x54 obj\Debug\dev\fs\src\ffunicode.o
                                 0x68 (size before relaxing)
 .eh_frame      0x00004120      0x2ac obj\Debug\dev\fs\src\fs_api.o
                                0x2c0 (size before relaxing)
 .eh_frame      0x000043cc       0x94 obj\Debug\dev\gsensor\src\gsensor_api.o
                                 0xa8 (size before relaxing)
 .eh_frame      0x00004460       0x80 obj\Debug\dev\gsensor\src\gsensor_da380.o
                                 0x94 (size before relaxing)
 .eh_frame      0x000044e0       0x80 obj\Debug\dev\gsensor\src\gsensor_gma301.o
                                 0x94 (size before relaxing)
 .eh_frame      0x00004560       0x7c obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
                                 0x90 (size before relaxing)
 .eh_frame      0x000045dc       0x3c obj\Debug\dev\ir\src\ir_api.o
                                 0x50 (size before relaxing)
 .eh_frame      0x00004618       0x8c obj\Debug\dev\key\src\key_api.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x000046a4       0x8c obj\Debug\dev\lcd\src\lcd_api.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x00004730       0x38 obj\Debug\dev\led\src\led_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x00004768       0x38 obj\Debug\dev\led_pwm\src\led_pwm_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x000047a0       0xc8 obj\Debug\dev\nvfs\src\nvfs_api.o
                                 0xdc (size before relaxing)
 .eh_frame      0x00004868      0x430 obj\Debug\dev\nvfs\src\nvfs_jpg.o
                                0x444 (size before relaxing)
 .eh_frame      0x00004c98      0x3f8 obj\Debug\dev\sd\src\sd_api.o
                                0x40c (size before relaxing)
 .eh_frame      0x00005090       0xe0 obj\Debug\dev\sensor\src\sensor_api.o
                                 0xf4 (size before relaxing)
 .eh_frame      0x00005170       0x64 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                                 0x78 (size before relaxing)
 .eh_frame      0x000051d4       0x58 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                                 0x6c (size before relaxing)
 .eh_frame      0x0000522c      0x138 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
                                0x14c (size before relaxing)
 .eh_frame      0x00005364       0x78 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                                 0x8c (size before relaxing)
 .eh_frame      0x000053dc      0x110 obj\Debug\dev\usb\dusb\src\dusb_api.o
                                0x124 (size before relaxing)
 .eh_frame      0x000054ec      0x12c obj\Debug\dev\usb\dusb\src\dusb_enum.o
                                0x140 (size before relaxing)
 .eh_frame      0x00005618      0x27c obj\Debug\dev\usb\dusb\src\dusb_msc.o
                                0x290 (size before relaxing)
 .eh_frame      0x00005894      0x12c obj\Debug\dev\usb\dusb\src\dusb_uac.o
                                0x140 (size before relaxing)
 .eh_frame      0x000059c0      0x1ec obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                                0x200 (size before relaxing)
 .eh_frame      0x00005bac      0x1c0 obj\Debug\dev\usb\husb\src\husb_api.o
                                0x1d4 (size before relaxing)
 .eh_frame      0x00005d6c      0x668 obj\Debug\dev\usb\husb\src\husb_enum.o
                                0x67c (size before relaxing)
 .eh_frame      0x000063d4       0x60 obj\Debug\dev\usb\husb\src\husb_hub.o
                                 0x74 (size before relaxing)
 .eh_frame      0x00006434      0x264 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
                                0x278 (size before relaxing)
 .eh_frame      0x00006698      0x248 obj\Debug\dev\usb\husb\src\husb_usensor.o
                                0x25c (size before relaxing)
 .eh_frame      0x000068e0      0x284 obj\Debug\dev\usb\husb\src\husb_uvc.o
                                0x298 (size before relaxing)
 .eh_frame      0x00006b64       0x74 obj\Debug\hal\src\hal_adc.o
                                 0x88 (size before relaxing)
 .eh_frame      0x00006bd8      0x1ec obj\Debug\hal\src\hal_auadc.o
                                0x200 (size before relaxing)
 .eh_frame      0x00006dc4       0xa4 obj\Debug\hal\src\hal_csi.o
                                 0xb8 (size before relaxing)
 .eh_frame      0x00006e68       0xc4 obj\Debug\hal\src\hal_dac.o
                                 0xd8 (size before relaxing)
 .eh_frame      0x00006f2c       0xd8 obj\Debug\hal\src\hal_dmauart.o
                                 0xec (size before relaxing)
 .eh_frame      0x00007004       0x6c obj\Debug\hal\src\hal_gpio.o
                                 0x80 (size before relaxing)
 .eh_frame      0x00007070      0x2c8 obj\Debug\hal\src\hal_iic.o
                                0x2dc (size before relaxing)
 .eh_frame      0x00007338       0x1c obj\Debug\hal\src\hal_int.o
                                 0x30 (size before relaxing)
 .eh_frame      0x00007354      0x474 obj\Debug\hal\src\hal_lcdshow.o
                                0x488 (size before relaxing)
 .eh_frame      0x000077c8       0x68 obj\Debug\hal\src\hal_md.o
                                 0x7c (size before relaxing)
 .eh_frame      0x00007830      0x40c obj\Debug\hal\src\hal_mjpAEncode.o
                                0x420 (size before relaxing)
 .eh_frame      0x00007c3c      0x208 obj\Debug\hal\src\hal_mjpBEncode.o
                                0x21c (size before relaxing)
 .eh_frame      0x00007e44      0x274 obj\Debug\hal\src\hal_mjpDecode.o
                                0x288 (size before relaxing)
 .eh_frame      0x000080b8      0x248 obj\Debug\hal\src\hal_rtc.o
                                0x25c (size before relaxing)
 .eh_frame      0x00008300      0x27c obj\Debug\hal\src\hal_spi.o
                                0x290 (size before relaxing)
 .eh_frame      0x0000857c      0x13c obj\Debug\hal\src\hal_spi1.o
                                0x150 (size before relaxing)
 .eh_frame      0x000086b8       0xe8 obj\Debug\hal\src\hal_stream.o
                                 0xfc (size before relaxing)
 .eh_frame      0x000087a0      0x178 obj\Debug\hal\src\hal_sys.o
                                0x18c (size before relaxing)
 .eh_frame      0x00008918       0x78 obj\Debug\hal\src\hal_timer.o
                                 0x8c (size before relaxing)
 .eh_frame      0x00008990      0x1b0 obj\Debug\hal\src\hal_uart.o
                                0x1c4 (size before relaxing)
 .eh_frame      0x00008b40      0x1fc obj\Debug\hal\src\hal_watermark.o
                                0x210 (size before relaxing)
 .eh_frame      0x00008d3c       0xd4 obj\Debug\mcu\xos\xmsgq.o
                                 0xe8 (size before relaxing)
 .eh_frame      0x00008e10       0x88 obj\Debug\mcu\xos\xos.o
                                 0x9c (size before relaxing)
 .eh_frame      0x00008e98       0x70 obj\Debug\mcu\xos\xwork.o
                                 0x84 (size before relaxing)
 .eh_frame      0x00008f08      0x19c obj\Debug\multimedia\audio\audio_playback.o
                                0x1b0 (size before relaxing)
 .eh_frame      0x000090a4      0x138 obj\Debug\multimedia\audio\audio_record.o
                                0x14c (size before relaxing)
 .eh_frame      0x000091dc       0xb4 obj\Debug\multimedia\image\image_decode.o
                                 0xc8 (size before relaxing)
 .eh_frame      0x00009290       0xa4 obj\Debug\multimedia\image\image_encode.o
                                 0xb8 (size before relaxing)
 .eh_frame      0x00009334       0x20 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                                 0x34 (size before relaxing)
 .eh_frame      0x00009354      0x2b8 obj\Debug\multimedia\video\video_playback.o
                                0x2cc (size before relaxing)
 .eh_frame      0x0000960c      0x230 obj\Debug\multimedia\video\video_record.o
                                0x244 (size before relaxing)
 .eh_frame      0x0000983c      0x3c4 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
                                0x3d8 (size before relaxing)
 .eh_frame      0x00009c00      0x134 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                                0x148 (size before relaxing)
 .eh_frame      0x00009d34       0x60 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
                                 0x74 (size before relaxing)
 .eh_frame      0x00009d94       0xc4 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
                                 0xd8 (size before relaxing)
 .eh_frame      0x00009e58      0x144 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
                                0x158 (size before relaxing)
 .eh_frame      0x00009f9c       0x3c obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
                                 0x50 (size before relaxing)
 .eh_frame      0x00009fd8       0x38 obj\Debug\sys_manage\res_manage\res_manage_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000a010       0x9c obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000a0ac       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a0ec       0x5c obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
                                 0x70 (size before relaxing)
 .eh_frame      0x0000a148       0x44 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
                                 0x58 (size before relaxing)
 .eh_frame      0x0000a18c      0x1c8 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                                0x1dc (size before relaxing)
 .eh_frame      0x0000a354       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a394       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000a3f4      0x358 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
                                0x36c (size before relaxing)
 .eh_frame      0x0000a74c       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a798       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a7e4       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a830       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a870      0x588 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
                                0x59c (size before relaxing)
 .eh_frame      0x0000adf8       0xd8 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000aed0       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000af30       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000af90       0x70 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
                                 0x84 (size before relaxing)
 .eh_frame      0x0000b000       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000b060       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000b0a0       0x80 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
                                 0x94 (size before relaxing)
 .eh_frame      0x0000b120       0x44 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
                                 0x58 (size before relaxing)
 .eh_frame      0x0000b164       0xec obj\Debug\app\app_common\src\app_init.o
                                0x100 (size before relaxing)
 .eh_frame      0x0000b250      0x158 obj\Debug\app\app_common\src\app_lcdshow.o
                                0x16c (size before relaxing)
 .eh_frame      0x0000b3a8       0x1c obj\Debug\app\app_common\src\main.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000b3c4       0x8c obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x0000b450       0x54 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
                                 0x68 (size before relaxing)
 .eh_frame      0x0000b4a4      0x17c obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                                0x190 (size before relaxing)
 .eh_frame      0x0000b620      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000b754      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000b888      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000b9c0      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000baf4      0x2dc obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                                0x2f0 (size before relaxing)
 .eh_frame      0x0000bdd0      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000bf08      0x150 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                                0x164 (size before relaxing)
 .eh_frame      0x0000c058      0x13c obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                                0x150 (size before relaxing)
 .eh_frame      0x0000c194      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000c2cc       0x70 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                                 0x84 (size before relaxing)
 .eh_frame      0x0000c33c       0xc8 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                                 0xdc (size before relaxing)
 .eh_frame      0x0000c404      0x194 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                                0x1a8 (size before relaxing)
 .eh_frame      0x0000c598      0x170 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                                0x184 (size before relaxing)
 .eh_frame      0x0000c708      0x170 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                                0x184 (size before relaxing)
 .eh_frame      0x0000c878       0xe0 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                                 0xf4 (size before relaxing)
 .eh_frame      0x0000c958       0x98 obj\Debug\app\task_windows\msg_api.o
                                 0xac (size before relaxing)
 .eh_frame      0x0000c9f0       0xcc obj\Debug\app\task_windows\task_api.o
                                 0xe0 (size before relaxing)
 .eh_frame      0x0000cabc       0xf4 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
                                0x108 (size before relaxing)
 .eh_frame      0x0000cbb0      0x34c obj\Debug\app\task_windows\task_common\src\task_common.o
                                0x360 (size before relaxing)
 .eh_frame      0x0000cefc       0x1c obj\Debug\app\task_windows\task_common\src\task_common_msg.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000cf18       0x74 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                                 0x88 (size before relaxing)
 .eh_frame      0x0000cf8c      0x200 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                                0x214 (size before relaxing)
 .eh_frame      0x0000d18c      0x180 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                                0x194 (size before relaxing)
 .eh_frame      0x0000d30c      0x36c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                                0x380 (size before relaxing)
 .eh_frame      0x0000d678      0x194 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                                0x1a8 (size before relaxing)
 .eh_frame      0x0000d80c      0x20c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                                0x220 (size before relaxing)
 .eh_frame      0x0000da18       0x1c obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000da34       0x7c obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
                                 0x90 (size before relaxing)
 .eh_frame      0x0000dab0       0xd8 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000db88       0x9c obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000dc24      0x318 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                                0x32c (size before relaxing)
 .eh_frame      0x0000df3c      0x134 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000e070      0x45c obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                                0x470 (size before relaxing)
 .eh_frame      0x0000e4cc       0x84 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                                 0x98 (size before relaxing)
 .eh_frame      0x0000e550       0x9c obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000e5ec       0x38 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000e624       0x54 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                                 0x68 (size before relaxing)
 .eh_frame      0x0000e678       0x58 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                                 0x6c (size before relaxing)
 .eh_frame      0x0000e6d0       0xd8 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000e7a8       0x78 obj\Debug\app\task_windows\windows_api.o
                                 0x8c (size before relaxing)
 .eh_frame      0x0000e820      0x154 obj\Debug\app\user_config\src\mbedtls_md5.o
                                0x168 (size before relaxing)
 .eh_frame      0x0000e974      0x120 obj\Debug\app\user_config\src\user_config_api.o
                                0x134 (size before relaxing)
 .eh_frame      0x0000ea94      0x1d0 ..\lib\libboot.a(boot.o)
                                0x1e4 (size before relaxing)
 .eh_frame      0x0000ec64       0x78 ..\lib\libboot.a(boot_lib.o)
                                 0x8c (size before relaxing)
 .eh_frame      0x0000ecdc       0x68 ..\lib\libmcu.a(hx330x_adc.o)
                                 0x7c (size before relaxing)
 .eh_frame      0x0000ed44      0x110 ..\lib\libmcu.a(hx330x_auadc.o)
                                0x124 (size before relaxing)
 .eh_frame      0x0000ee54      0x4e4 ..\lib\libmcu.a(hx330x_csi.o)
                                0x4f8 (size before relaxing)
 .eh_frame      0x0000f338      0x1d4 ..\lib\libmcu.a(hx330x_dac.o)
                                0x1e8 (size before relaxing)
 .eh_frame      0x0000f50c       0x88 ..\lib\libmcu.a(hx330x_dma.o)
                                 0x9c (size before relaxing)
 .eh_frame      0x0000f594      0x170 ..\lib\libmcu.a(hx330x_dmauart.o)
                                0x184 (size before relaxing)
 .eh_frame      0x0000f704      0x2c8 ..\lib\libmcu.a(hx330x_gpio.o)
                                0x2dc (size before relaxing)
 .eh_frame      0x0000f9cc      0x31c ..\lib\libmcu.a(hx330x_iic.o)
                                0x330 (size before relaxing)
 .eh_frame      0x0000fce8       0x9c ..\lib\libmcu.a(hx330x_int.o)
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000fd84      0x264 ..\lib\libmcu.a(hx330x_isp.o)
                                0x278 (size before relaxing)
 .eh_frame      0x0000ffe8      0x778 ..\lib\libmcu.a(hx330x_jpg.o)
                                0x78c (size before relaxing)
 .eh_frame      0x00010760       0x38 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                                 0x4c (size before relaxing)
 .eh_frame      0x00010798      0x3d4 ..\lib\libmcu.a(hx330x_lcd.o)
                                0x3e8 (size before relaxing)
 .eh_frame      0x00010b6c       0xc4 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                                 0xd8 (size before relaxing)
 .eh_frame      0x00010c30      0x3c8 ..\lib\libmcu.a(hx330x_lcdui.o)
                                0x3dc (size before relaxing)
 .eh_frame      0x00010ff8       0xe8 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                                 0xfc (size before relaxing)
 .eh_frame      0x000110e0       0x80 ..\lib\libmcu.a(hx330x_lcdwin.o)
                                 0x94 (size before relaxing)
 .eh_frame      0x00011160       0x80 ..\lib\libmcu.a(hx330x_md.o)
                                 0x94 (size before relaxing)
 .eh_frame      0x000111e0       0x5c ..\lib\libmcu.a(hx330x_mipi.o)
                                 0x70 (size before relaxing)
 .eh_frame      0x0001123c      0x29c ..\lib\libmcu.a(hx330x_misc.o)
                                0x2b0 (size before relaxing)
 .eh_frame      0x000114d8      0x374 ..\lib\libmcu.a(hx330x_rtc.o)
                                0x388 (size before relaxing)
 .eh_frame      0x0001184c      0x2b4 ..\lib\libmcu.a(hx330x_sd.o)
                                0x2c8 (size before relaxing)
 .eh_frame      0x00011b00      0x108 ..\lib\libmcu.a(hx330x_spi0.o)
                                0x11c (size before relaxing)
 .eh_frame      0x00011c08      0x184 ..\lib\libmcu.a(hx330x_spi1.o)
                                0x198 (size before relaxing)
 .eh_frame      0x00011d8c      0x3d4 ..\lib\libmcu.a(hx330x_sys.o)
                                0x3e8 (size before relaxing)
 .eh_frame      0x00012160      0x188 ..\lib\libmcu.a(hx330x_timer.o)
                                0x19c (size before relaxing)
 .eh_frame      0x000122e8      0x110 ..\lib\libmcu.a(hx330x_tminf.o)
                                0x124 (size before relaxing)
 .eh_frame      0x000123f8       0x78 ..\lib\libmcu.a(hx330x_uart.o)
                                 0x8c (size before relaxing)
 .eh_frame      0x00012470      0x444 ..\lib\libmcu.a(hx330x_usb.o)
                                0x458 (size before relaxing)
 .eh_frame      0x000128b4       0x60 ..\lib\libmcu.a(hx330x_wdt.o)
                                 0x74 (size before relaxing)
 .eh_frame      0x00012914       0xa4 ..\lib\libmcu.a(hx330x_emi.o)
                                 0xb8 (size before relaxing)
 .eh_frame      0x000129b8      0x220 ..\lib\libisp.a(hal_isp.o)
                                0x234 (size before relaxing)
 .eh_frame      0x00012bd8      0x118 ..\lib\libjpg.a(hal_jpg.o)
                                0x12c (size before relaxing)
 .eh_frame      0x00012cf0      0x1c0 ..\lib\liblcd.a(hal_lcd.o)
                                0x1d4 (size before relaxing)
 .eh_frame      0x00012eb0      0x148 ..\lib\liblcd.a(hal_lcdMem.o)
                                0x15c (size before relaxing)
 .eh_frame      0x00012ff8       0x84 ..\lib\liblcd.a(hal_lcdrotate.o)
                                 0x98 (size before relaxing)
 .eh_frame      0x0001307c      0x238 ..\lib\liblcd.a(hal_lcdUi.o)
                                0x24c (size before relaxing)
 .eh_frame      0x000132b4       0x60 ..\lib\liblcd.a(hal_lcdUiLzo.o)
                                 0x74 (size before relaxing)
 .eh_frame      0x00013314       0x58 ..\lib\liblcd.a(lcd_tab.o)
                                 0x6c (size before relaxing)
 .eh_frame      0x0001336c      0x204 ..\lib\libmultimedia.a(api_multimedia.o)
                                0x218 (size before relaxing)
 .eh_frame      0x00013570      0x14c ..\lib\libmultimedia.a(avi_dec.o)
                                0x160 (size before relaxing)
 .eh_frame      0x000136bc      0x130 ..\lib\libmultimedia.a(avi_odml_enc.o)
                                0x144 (size before relaxing)
 .eh_frame      0x000137ec      0x144 ..\lib\libmultimedia.a(avi_std_enc.o)
                                0x158 (size before relaxing)
 .eh_frame      0x00013930       0xb8 ..\lib\libmultimedia.a(wav_dec.o)
                                 0xcc (size before relaxing)
 .eh_frame      0x000139e8       0xa8 ..\lib\libmultimedia.a(wav_enc.o)
                                 0xbc (size before relaxing)
 .eh_frame      0x00013a90       0x38 ..\lib\libmultimedia.a(wav_pcm.o)
                                 0x4c (size before relaxing)
 .eh_frame      0x00013ac8       0x2c D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                                 0x40 (size before relaxing)
 .eh_frame      0x00013af4       0x2c D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                                 0x40 (size before relaxing)

.rela.dyn       0x00013b20        0x0 load address 0x000ac2dc
 .rela.text     0x00013b20        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.data     0x00013b20        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector.kepttext
                0x00013b20        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.rodata   0x00013b20        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.sdram_text
                0x00013b20        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.sensor_res.header
                0x00013b20        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.text.startup
                0x00013b20        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector.text
                0x00013b20        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.bootsec  0x00013b20        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector   0x00013b20        0x0 obj\Debug\dev\battery\src\battery_api.o

.mp3_text       0x00008000        0x0 load address 0x0009be00
                0x00008000                __mp3_text_start = .
 *(.mp3_text)
                0x00008000                . = ALIGN (0x4)

.mp3_code       0x00200000        0x0 load address 0x0009be00
                0x00200000                __mp3_code_start = .
 *(.mp3_code)
                0x00200000                . = ALIGN (0x4)

.mp3_data
 *(.mp3_data)

.nes_code       0x00200000        0x0 load address 0x0009be00
                0x00200000                __nes_code_start = .
 *(.nes_code)
                0x00200000                . = ALIGN (0x4)

.nes_data       0x00200000        0x0
                0x00200000                __nes_data_start = .
 *(.nes_data)
                0x00200000                __nes_data_end = .

.nes_com_text   0x00008000        0x0 load address 0x0009be00
                0x00008000                __nes_text_start = .
 *(.nes_com_text)
                0x00008000                . = ALIGN (0x4)
                0x00000015                _boot_code_len = ((SIZEOF (.boot_code) + 0x1ff) / 0x200)
                0x00000001                _boot_code_sec = (LOADADDR (.boot_code) / 0x200)
                0x02000000                _text_start = _onsdram_start
                0x00000016                _text_sec = (LOADADDR (.on_sdram) / 0x200)
                0x0000004e                _text_len = ((SIZEOF (.on_sdram) + 0x1ff) / 0x200)
                0x00000001                ASSERT (((_sdram_remian_addr - ORIGIN (sdram)) < __sdram_size), No memroy for sdram)
                0x001e3900                __sdram_remain_size = ((ORIGIN (sdram) + __sdram_size) - _sdram_remian_addr)
                0x00001400                __stack_size = 0x1400
                0x00000001                ASSERT ((((ORIGIN (ram_user) + 0x7000) - __sram_end) >= __stack_size), No memroy for stack)
                0x02200000                __bss_end = (_sdram_remian_addr + __sdram_remain_size)
                0x00000000                __mp3_text_len = SIZEOF (.mp3_text)
                0x0009be00                __mp3_text_addr = LOADADDR (.mp3_text)
                0x00000000                __mp3_code_len = SIZEOF (.mp3_code)
                0x0009be00                __mp3_code_addr = LOADADDR (.mp3_code)
                0x00000000                __nes_text_len = SIZEOF (.nes_com_text)
                0x0009be00                __nes_text_addr = LOADADDR (.nes_com_text)
                0x00000000                __nes_code_len = SIZEOF (.nes_code)
                0x0009be00                __nes_code_addr = LOADADDR (.nes_code)
                0x00095e00                _lcd_res_lma = LOADADDR (.lcd_resource)
                0x000029bc                _lcd_res_size = SIZEOF (.lcd_resource)
                0x000987bc                _sensor_resource_start_addr = LOADADDR (.sensor_resource)
                0x00000000                _res_sensor_header_len = (_res_sensor_header_item_end - _res_sensor_header_item_start)
                0x0009be00                _nes_res_lma = LOADADDR (.nes_resource)
                0x00006ffc                PROVIDE (__stack, ((ORIGIN (ram_user) + 0x7000) - 0x4))
LOAD ..\lib\libboot.a
LOAD ..\lib\libmcu.a
LOAD ..\lib\libisp.a
LOAD ..\lib\libjpg.a
LOAD ..\lib\liblcd.a
LOAD ..\lib\libmultimedia.a
LOAD D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a
LOAD D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a
OUTPUT(bin\Debug\hx330x_sdk.exe elf32-or1k)

.comment        0x00000000       0x11
 .comment       0x00000000       0x11 obj\Debug\dev\battery\src\battery_api.o
                                 0x12 (size before relaxing)
 .comment       0x00000011       0x12 obj\Debug\dev\dev_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\diskio.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\ff.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\ffunicode.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\fs_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .comment       0x00000011       0x12 obj\Debug\dev\ir\src\ir_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\key\src\key_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .comment       0x00000011       0x12 obj\Debug\dev\led\src\led_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\nvfs\src\nvfs_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .comment       0x00000011       0x12 obj\Debug\dev\sd\src\sd_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_tab.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_enum.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_hub.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_adc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_auadc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_csi.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_dac.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_dmauart.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_eeprom.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_gpio.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_iic.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_int.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_lcdshow.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_md.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpAEncode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpBEncode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpDecode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_rtc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_spi.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_spi1.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_stream.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_sys.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_timer.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_uart.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_watermark.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_wdt.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xmbox.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xmsgq.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xos.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xwork.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\audio\audio_playback.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\audio\audio_record.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\image\image_decode.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\image\image_encode.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\video\video_playback.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\video\video_record.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\app_init.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\app_lcdshow.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\main.o
 .comment       0x00000011       0x12 obj\Debug\app\resource\user_res.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\msg_api.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_api.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\task_common.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\windows_api.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\mbedtls_md5.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\user_config_api.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\user_config_tab.o
 .comment       0x00000011       0x12 ..\lib\libboot.a(boot.o)
 .comment       0x00000011       0x12 ..\lib\libboot.a(boot_lib.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_adc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_auadc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_csi.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dac.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dma.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dmauart.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_gpio.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_iic.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_int.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_isp.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_jpg.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcd.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdui.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_md.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_mipi.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_misc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_rtc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_sd.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_spi0.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_spi1.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_sys.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_timer.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_tminf.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_uart.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_usb.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_wdt.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_emi.o)
 .comment       0x00000011       0x12 ..\lib\libisp.a(hal_isp.o)
 .comment       0x00000011       0x12 ..\lib\libjpg.a(hal_jpg.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcd.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdMem.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdrotate.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdUi.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(lcd_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(api_multimedia.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_std_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_pcm.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .comment       0x00000011       0x12 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_info     0x00000000     0x13ab
 .debug_info    0x00000000      0x113 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_info    0x00000113      0x131 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_info    0x00000244      0x117 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_info    0x0000035b      0x10e D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_info    0x00000469      0x71e D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_info    0x00000b87      0x76b D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_info    0x000012f2       0xb9 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_abbrev   0x00000000      0x5c4
 .debug_abbrev  0x00000000       0x7f D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_abbrev  0x0000007f       0xab D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_abbrev  0x0000012a       0x9f D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_abbrev  0x000001c9       0x92 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_abbrev  0x0000025b      0x170 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_abbrev  0x000003cb      0x19c D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_abbrev  0x00000567       0x5d D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_loc      0x00000000     0x2bbe
 .debug_loc     0x00000000      0x102 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_loc     0x00000102      0x2ed D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_loc     0x000003ef      0x1ba D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_loc     0x000005a9       0xc7 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_loc     0x00000670     0x1537 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_loc     0x00001ba7     0x1017 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)

.debug_aranges  0x00000000       0xd8
 .debug_aranges
                0x00000000       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_aranges
                0x00000020       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_aranges
                0x00000040       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_aranges
                0x00000060       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_aranges
                0x00000080       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_aranges
                0x000000a0       0x20 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_aranges
                0x000000c0       0x18 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_line     0x00000000      0x849
 .debug_line    0x00000000      0x15d D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_line    0x0000015d      0x164 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_line    0x000002c1      0x16d D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_line    0x0000042e       0xf5 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_line    0x00000523      0x15d D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_line    0x00000680      0x163 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_line    0x000007e3       0x66 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_str      0x00000000      0x4dc
 .debug_str     0x00000000      0x14f D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                                0x188 (size before relaxing)
 .debug_str     0x0000014f       0x7b D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                                0x1af (size before relaxing)
 .debug_str     0x000001ca       0x68 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                                0x19c (size before relaxing)
 .debug_str     0x00000232       0x54 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                                0x1a3 (size before relaxing)
 .debug_str     0x00000286      0x1dc D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                                0x284 (size before relaxing)
 .debug_str     0x00000462        0xa D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                                0x284 (size before relaxing)
 .debug_str     0x0000046c       0x70 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                                0x1cb (size before relaxing)

.debug_frame    0x00000000       0xa0
 .debug_frame   0x00000000       0x28 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_frame   0x00000028       0x28 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_frame   0x00000050       0x28 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_frame   0x00000078       0x28 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)

.debug_ranges   0x00000000      0x320
 .debug_ranges  0x00000000      0x190 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_ranges  0x00000190      0x190 D:\hx330x-gcc\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
