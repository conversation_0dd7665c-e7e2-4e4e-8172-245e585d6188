/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_USB_DEVICE_H
#define  __TASK_USB_DEVICE_H

EXTERN_WINDOW(usbDeviceWindow);
extern msgDealInfor usbDeviceMsgDeal[];
extern sysTask_T taskUSBDevice;
typedef struct USBDEVICE_OP_S
{
	u32 cur_sel_index;
	u32 cur_page_num;
	u32 usb_process_flag;
}USBDEVICE_OP_T;


extern USBDEVICE_OP_T  usbDeviceOp;


#endif
