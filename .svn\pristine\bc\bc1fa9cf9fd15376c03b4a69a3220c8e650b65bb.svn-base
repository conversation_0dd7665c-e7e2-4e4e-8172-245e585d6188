/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_ILI9225G

#define CMD(x)    LCD_CMD_MCU_CMD8(x)
#define DAT(x)    LCD_CMD_MCU_DAT8(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)


LCD_INIT_TAB_BEGIN()

    //************* Start Initial Sequence **********//
    CMD(0x00),CMD(0x01),DAT(0x01),DAT(0x1C), // set SS and NL bit
    CMD(0x00),CMD(0x02),DAT(0x00),DAT(0x00), // set 1 line inversion
    CMD(0x00),CMD(0x03),DAT(0x10),DAT(0x18), // set GRAM write direction and BGR=1.
    CMD(0x00),CMD(0x08),DAT(0x02),DAT(0x02), // set BP and FP
    CMD(0x00),CMD(0x0C),DAT(0x00),DAT(0x00), // RGB interface setting R0Ch=0x0110 for RGB 18Bit and R0Ch=0111for

    CMD(0x00),CMD(0x0F),DAT(0x0d),DAT(0x01), // Set frame rate
    CMD(0x00),CMD(0x20),DAT(0x00),DAT(0x00), // Set GRAM Address
    CMD(0x00),CMD(0x21),DAT(0x00),DAT(0xDB), // Set GRAM Address
    //*************Power On sequence ****************//
    DLY(50),
    CMD(0x00),CMD(0x10),DAT(0x0A),DAT(0x00), // Set SAP,DSTB,STB
    CMD(0x00),CMD(0x11),DAT(0x10),DAT(0x38), // Set APON,PON,AON,VCI1EN,VC
    DLY(50),
    CMD(0x00),CMD(0x12),DAT(0x11),DAT(0x21), // Internal reference voltage= Vci,
    CMD(0x00),CMD(0x13),DAT(0x00),DAT(0x66), // Set GVDD
    CMD(0x00),CMD(0x14),DAT(0x5F),DAT(0x45), // Set VCOMH/VCOML voltage  ¦Ì¨ª¡ã???¦Ì¡Â??¡À¨¨?¨¨ ¦Ì¡ÂD?¡À?¨¢¨¢¦Ì¡Â¡ä¨®¡À?¡ã¦Ì
    //------------------------ Set GRAM area --------------------------------//
    CMD(0x00),CMD(0x30),DAT(0x00),DAT(0x00),
    CMD(0x00),CMD(0x31),DAT(0x00),DAT(0xDB),
    CMD(0x00),CMD(0x32),DAT(0x00),DAT(0x00),


    CMD(0x00),CMD(0x33),DAT(0x00),DAT(0x0),
    CMD(0x00),CMD(0x34),DAT(0x00),DAT(0xDB),

    CMD(0x00),CMD(0x35),DAT(0x00),DAT(0x00),
    CMD(0x00),CMD(0x36),DAT(0x00),DAT(0xAF),


    CMD(0x00),CMD(0x37),DAT(0x00),DAT(0x00),
    CMD(0x00),CMD(0x38),DAT(0x00),DAT(0xDB),
    CMD(0x00),CMD(0x39),DAT(0x00),DAT(0x00),
    // ----------- Adjust the Gamma Curve ----------//
    CMD(0x00),CMD(0x50),DAT(0x04),DAT(0x00),
    CMD(0x00),CMD(0x51),DAT(0x06),DAT(0x0B),
    CMD(0x00),CMD(0x52),DAT(0x0C),DAT(0x0A),
    CMD(0x00),CMD(0x53),DAT(0x01),DAT(0x05),
    CMD(0x00),CMD(0x54),DAT(0x0A),DAT(0x0C),
    CMD(0x00),CMD(0x55),DAT(0x0B),DAT(0x06),
    CMD(0x00),CMD(0x56),DAT(0x00),DAT(0x04),
    CMD(0x00),CMD(0x57),DAT(0x05),DAT(0x01),
    CMD(0x00),CMD(0x58),DAT(0x0E),DAT(0x00),
    CMD(0x00),CMD(0x59),DAT(0x00),DAT(0x0E),
    DLY(50),
    CMD(0x00),CMD(0x07),DAT(0x00),DAT(0x17),//BIT[12]:0--TE OFF,1--TE ON

    CMD(0x00),CMD(0x22),

LCD_INIT_TAB_END()


LCD_DESC_BEGIN()
    .name           = "MCU_iLi9225G",
    .lcd_bus_type   = LCD_IF_GET(),
    .scan_mode      = LCD_DISPLAY_ROTATE_0,
    .te_mode        = LCD_MCU_TE_DISABLE,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div       = LCD_PCLK_DIV(176*220*2*30),
    .clk_per_pixel  = 2,
    .even_order     = LCD_RGB,
    .odd_order      = LCD_RGB,

    .data_mode = LCD_DATA_MODE0_8BIT_RGB565,

    .screen_w       = 220,
    .screen_h       = 176,

    .video_w        = 220,
    .video_h        = 176,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness     = -12,

    .saturation     = LCD_SATURATION_130,

    .contra_index   = 6,

    .gamma_index    = {4, 4, 4},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif




























