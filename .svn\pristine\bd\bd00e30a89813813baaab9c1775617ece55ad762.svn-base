/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT == LCD_RGB_RM68172

#define CMD(x)  LCD_CMD_RGB_DAT((0x20<<8)|(((x)>>8)&0xff)),LCD_CMD_RGB_DAT((0x00<<8)|((x)&0xff))
#define DAT(x)  LCD_CMD_RGB_DAT((0x40<<8)|((x)&0xff))
#define DLY(m)  LCD_CMD_DELAY_MS(m)


LCD_INIT_TAB_BEGIN()

    CMD(0xF000), DAT(0x55),
    CMD(0xF001), DAT(0xAA),
    CMD(0xF002), DAT(0x52),
    CMD(0xF003), DAT(0x08),
    CMD(0xF004), DAT(0x02),
    CMD(0xF600), DAT(0x60),
    CMD(0xF601), DAT(0x40),
    CMD(0xFE00), DAT(0x01),
    CMD(0xFE01), DAT(0x80),
    CMD(0xFE02), DAT(0x09),
    CMD(0xFE03), DAT(0x09),
    DLY(100),
    CMD(0xF000), DAT(0x55),
    CMD(0xF001), DAT(0xAA),
    CMD(0xF002), DAT(0x52),
    CMD(0xF003), DAT(0x08),
    CMD(0xF004), DAT(0x01),
    CMD(0xB000), DAT(0x07),
    CMD(0xB100), DAT(0x07),
    CMD(0xB500), DAT(0x08),
    CMD(0xB600), DAT(0x54),
    CMD(0xB700), DAT(0x44),
    CMD(0xB800), DAT(0x24),
    CMD(0xB900), DAT(0x34),
    CMD(0xBA00), DAT(0x14),
    CMD(0xBC00), DAT(0x00),
    CMD(0xBC01), DAT(0x98),
    CMD(0xBC02), DAT(0x13),
    CMD(0xBD00), DAT(0x00),
    CMD(0xBD01), DAT(0x98),
    CMD(0xBD02), DAT(0x13),
    CMD(0xBE00), DAT(0x00),
    CMD(0xBE01), DAT(0x1A),
    CMD(0xD100), DAT(0x00),
    CMD(0xD101), DAT(0x00),
    CMD(0xD102), DAT(0x00),
    CMD(0xD103), DAT(0x51),
    CMD(0xD104), DAT(0x00),
    CMD(0xD105), DAT(0x7E),
    CMD(0xD106), DAT(0x00),
    CMD(0xD107), DAT(0x9A),
    CMD(0xD108), DAT(0x00),
    CMD(0xD109), DAT(0xB0),
    CMD(0xD10A), DAT(0x00),
    CMD(0xD10B), DAT(0xD2),
    CMD(0xD10C), DAT(0x00),
    CMD(0xD10D), DAT(0xEE),
    CMD(0xD10E), DAT(0x01),
    CMD(0xD10F), DAT(0x1A),
    CMD(0xD110), DAT(0x01),
    CMD(0xD111), DAT(0x3C),
    CMD(0xD112), DAT(0x01),
    CMD(0xD113), DAT(0x71),
    CMD(0xD114), DAT(0x01),
    CMD(0xD115), DAT(0x9C),
    CMD(0xD116), DAT(0x01),
    CMD(0xD117), DAT(0xDF),
    CMD(0xD118), DAT(0x02),
    CMD(0xD119), DAT(0x16),
    CMD(0xD11A), DAT(0x02),
    CMD(0xD11B), DAT(0x18),
    CMD(0xD11C), DAT(0x02),
    CMD(0xD11D), DAT(0x4B),
    CMD(0xD11E), DAT(0x02),
    CMD(0xD11F), DAT(0x80),
    CMD(0xD120), DAT(0x02),
    CMD(0xD121), DAT(0xA1),
    CMD(0xD122), DAT(0x02),
    CMD(0xD123), DAT(0xCD),
    CMD(0xD124), DAT(0x02),
    CMD(0xD125), DAT(0xEB),
    CMD(0xD126), DAT(0x03),
    CMD(0xD127), DAT(0x11),
    CMD(0xD128), DAT(0x03),
    CMD(0xD129), DAT(0x27),
    CMD(0xD12A), DAT(0x03),
    CMD(0xD12B), DAT(0x42),
    CMD(0xD12C), DAT(0x03),
    CMD(0xD12D), DAT(0x4F),
    CMD(0xD12E), DAT(0x03),
    CMD(0xD12F), DAT(0x5A),
    CMD(0xD130), DAT(0x03),
    CMD(0xD131), DAT(0x62),
    CMD(0xD132), DAT(0x03),
    CMD(0xD133), DAT(0xFF),

    CMD(0xD200), DAT(0x00),
    CMD(0xD201), DAT(0x00),
    CMD(0xD202), DAT(0x00),
    CMD(0xD203), DAT(0x51),
    CMD(0xD204), DAT(0x00),
    CMD(0xD205), DAT(0x7E),
    CMD(0xD206), DAT(0x00),
    CMD(0xD207), DAT(0x9A),
    CMD(0xD208), DAT(0x00),
    CMD(0xD209), DAT(0xB0),
    CMD(0xD20A), DAT(0x00),
    CMD(0xD20B), DAT(0xD2),
    CMD(0xD20C), DAT(0x00),
    CMD(0xD20D), DAT(0xEE),
    CMD(0xD20E), DAT(0x01),
    CMD(0xD20F), DAT(0x1A),
    CMD(0xD210), DAT(0x01),
    CMD(0xD211), DAT(0x3C),
    CMD(0xD212), DAT(0x01),
    CMD(0xD213), DAT(0x71),
    CMD(0xD214), DAT(0x01),
    CMD(0xD215), DAT(0x9C),
    CMD(0xD216), DAT(0x01),
    CMD(0xD217), DAT(0xDF),
    CMD(0xD218), DAT(0x02),
    CMD(0xD219), DAT(0x16),
    CMD(0xD21A), DAT(0x02),
    CMD(0xD21B), DAT(0x18),
    CMD(0xD21C), DAT(0x02),
    CMD(0xD21D), DAT(0x4B),
    CMD(0xD21E), DAT(0x02),
    CMD(0xD21F), DAT(0x80),
    CMD(0xD220), DAT(0x02),
    CMD(0xD221), DAT(0xA1),
    CMD(0xD222), DAT(0x02),
    CMD(0xD223), DAT(0xCD),
    CMD(0xD224), DAT(0x02),
    CMD(0xD225), DAT(0xEB),
    CMD(0xD226), DAT(0x03),
    CMD(0xD227), DAT(0x11),
    CMD(0xD228), DAT(0x03),
    CMD(0xD229), DAT(0x27),
    CMD(0xD22A), DAT(0x03),
    CMD(0xD22B), DAT(0x42),
    CMD(0xD22C), DAT(0x03),
    CMD(0xD22D), DAT(0x4F),
    CMD(0xD22E), DAT(0x03),
    CMD(0xD22F), DAT(0x5A),
    CMD(0xD230), DAT(0x03),
    CMD(0xD231), DAT(0x62),
    CMD(0xD232), DAT(0x03),
    CMD(0xD233), DAT(0xFF),

    CMD(0xD300), DAT(0x00),
    CMD(0xD301), DAT(0x00),
    CMD(0xD302), DAT(0x00),
    CMD(0xD303), DAT(0x51),
    CMD(0xD304), DAT(0x00),
    CMD(0xD305), DAT(0x7E),
    CMD(0xD306), DAT(0x00),
    CMD(0xD307), DAT(0x9A),
    CMD(0xD308), DAT(0x00),
    CMD(0xD309), DAT(0xB0),
    CMD(0xD30A), DAT(0x00),
    CMD(0xD30B), DAT(0xD2),
    CMD(0xD30C), DAT(0x00),
    CMD(0xD30D), DAT(0xEE),
    CMD(0xD30E), DAT(0x01),
    CMD(0xD30F), DAT(0x1A),
    CMD(0xD310), DAT(0x01),
    CMD(0xD311), DAT(0x3C),
    CMD(0xD312), DAT(0x01),
    CMD(0xD313), DAT(0x71),
    CMD(0xD314), DAT(0x01),
    CMD(0xD315), DAT(0x9C),
    CMD(0xD316), DAT(0x01),
    CMD(0xD317), DAT(0xDF),
    CMD(0xD318), DAT(0x02),
    CMD(0xD319), DAT(0x16),
    CMD(0xD31A), DAT(0x02),
    CMD(0xD31B), DAT(0x18),
    CMD(0xD31C), DAT(0x02),
    CMD(0xD31D), DAT(0x4B),
    CMD(0xD31E), DAT(0x02),
    CMD(0xD31F), DAT(0x80),
    CMD(0xD320), DAT(0x02),
    CMD(0xD321), DAT(0xA1),
    CMD(0xD322), DAT(0x02),
    CMD(0xD323), DAT(0xCD),
    CMD(0xD324), DAT(0x02),
    CMD(0xD325), DAT(0xEB),
    CMD(0xD326), DAT(0x03),
    CMD(0xD327), DAT(0x11),
    CMD(0xD328), DAT(0x03),
    CMD(0xD329), DAT(0x27),
    CMD(0xD32A), DAT(0x03),
    CMD(0xD32B), DAT(0x42),
    CMD(0xD32C), DAT(0x03),
    CMD(0xD32D), DAT(0x4F),
    CMD(0xD32E), DAT(0x03),
    CMD(0xD32F), DAT(0x5A),
    CMD(0xD330), DAT(0x03),
    CMD(0xD331), DAT(0x62),
    CMD(0xD332), DAT(0x03),
    CMD(0xD333), DAT(0xFF),

    CMD(0xD400), DAT(0x00),
    CMD(0xD401), DAT(0x00),
    CMD(0xD402), DAT(0x00),
    CMD(0xD403), DAT(0x51),
    CMD(0xD404), DAT(0x00),
    CMD(0xD405), DAT(0x7E),
    CMD(0xD406), DAT(0x00),
    CMD(0xD407), DAT(0x9A),
    CMD(0xD408), DAT(0x00),
    CMD(0xD409), DAT(0xB0),
    CMD(0xD40A), DAT(0x00),
    CMD(0xD40B), DAT(0xD2),
    CMD(0xD40C), DAT(0x00),
    CMD(0xD40D), DAT(0xEE),
    CMD(0xD40E), DAT(0x01),
    CMD(0xD40F), DAT(0x1A),
    CMD(0xD410), DAT(0x01),
    CMD(0xD411), DAT(0x3C),
    CMD(0xD412), DAT(0x01),
    CMD(0xD413), DAT(0x71),
    CMD(0xD414), DAT(0x01),
    CMD(0xD415), DAT(0x9C),
    CMD(0xD416), DAT(0x01),
    CMD(0xD417), DAT(0xDF),
    CMD(0xD418), DAT(0x02),
    CMD(0xD419), DAT(0x16),
    CMD(0xD41A), DAT(0x02),
    CMD(0xD41B), DAT(0x18),
    CMD(0xD41C), DAT(0x02),
    CMD(0xD41D), DAT(0x4B),
    CMD(0xD41E), DAT(0x02),
    CMD(0xD41F), DAT(0x80),
    CMD(0xD420), DAT(0x02),
    CMD(0xD421), DAT(0xA1),
    CMD(0xD422), DAT(0x02),
    CMD(0xD423), DAT(0xCD),
    CMD(0xD424), DAT(0x02),
    CMD(0xD425), DAT(0xEB),
    CMD(0xD426), DAT(0x03),
    CMD(0xD427), DAT(0x11),
    CMD(0xD428), DAT(0x03),
    CMD(0xD429), DAT(0x27),
    CMD(0xD42A), DAT(0x03),
    CMD(0xD42B), DAT(0x42),
    CMD(0xD42C), DAT(0x03),
    CMD(0xD42D), DAT(0x4F),
    CMD(0xD42E), DAT(0x03),
    CMD(0xD42F), DAT(0x5A),
    CMD(0xD430), DAT(0x03),
    CMD(0xD431), DAT(0x62),
    CMD(0xD432), DAT(0x03),
    CMD(0xD433), DAT(0xFF),

    CMD(0xD500), DAT(0x00),
    CMD(0xD501), DAT(0x00),
    CMD(0xD502), DAT(0x00),
    CMD(0xD503), DAT(0x51),
    CMD(0xD504), DAT(0x00),
    CMD(0xD505), DAT(0x7E),
    CMD(0xD506), DAT(0x00),
    CMD(0xD507), DAT(0x9A),
    CMD(0xD508), DAT(0x00),
    CMD(0xD509), DAT(0xB0),
    CMD(0xD50A), DAT(0x00),
    CMD(0xD50B), DAT(0xD2),
    CMD(0xD50C), DAT(0x00),
    CMD(0xD50D), DAT(0xEE),
    CMD(0xD50E), DAT(0x01),
    CMD(0xD50F), DAT(0x1A),
    CMD(0xD510), DAT(0x01),
    CMD(0xD511), DAT(0x3C),
    CMD(0xD512), DAT(0x01),
    CMD(0xD513), DAT(0x71),
    CMD(0xD514), DAT(0x01),
    CMD(0xD515), DAT(0x9C),
    CMD(0xD516), DAT(0x01),
    CMD(0xD517), DAT(0xDF),
    CMD(0xD518), DAT(0x02),
    CMD(0xD519), DAT(0x16),
    CMD(0xD51A), DAT(0x02),
    CMD(0xD51B), DAT(0x18),
    CMD(0xD51C), DAT(0x02),
    CMD(0xD51D), DAT(0x4B),
    CMD(0xD51E), DAT(0x02),
    CMD(0xD51F), DAT(0x80),
    CMD(0xD520), DAT(0x02),
    CMD(0xD521), DAT(0xA1),
    CMD(0xD522), DAT(0x02),
    CMD(0xD523), DAT(0xCD),
    CMD(0xD524), DAT(0x02),
    CMD(0xD525), DAT(0xEB),
    CMD(0xD526), DAT(0x03),
    CMD(0xD527), DAT(0x11),
    CMD(0xD528), DAT(0x03),
    CMD(0xD529), DAT(0x27),
    CMD(0xD52A), DAT(0x03),
    CMD(0xD52B), DAT(0x42),
    CMD(0xD52C), DAT(0x03),
    CMD(0xD52D), DAT(0x4F),
    CMD(0xD52E), DAT(0x03),
    CMD(0xD52F), DAT(0x5A),
    CMD(0xD530), DAT(0x03),
    CMD(0xD531), DAT(0x62),
    CMD(0xD532), DAT(0x03),
    CMD(0xD533), DAT(0xFF),

    CMD(0xD600), DAT(0x00),
    CMD(0xD601), DAT(0x00),
    CMD(0xD602), DAT(0x00),
    CMD(0xD603), DAT(0x51),
    CMD(0xD604), DAT(0x00),
    CMD(0xD605), DAT(0x7E),
    CMD(0xD606), DAT(0x00),
    CMD(0xD607), DAT(0x9A),
    CMD(0xD608), DAT(0x00),
    CMD(0xD609), DAT(0xB0),
    CMD(0xD60A), DAT(0x00),
    CMD(0xD60B), DAT(0xD2),
    CMD(0xD60C), DAT(0x00),
    CMD(0xD60D), DAT(0xEE),
    CMD(0xD60E), DAT(0x01),
    CMD(0xD60F), DAT(0x1A),
    CMD(0xD610), DAT(0x01),
    CMD(0xD611), DAT(0x3C),
    CMD(0xD612), DAT(0x01),
    CMD(0xD613), DAT(0x71),
    CMD(0xD614), DAT(0x01),
    CMD(0xD615), DAT(0x9C),
    CMD(0xD616), DAT(0x01),
    CMD(0xD617), DAT(0xDF),
    CMD(0xD618), DAT(0x02),
    CMD(0xD619), DAT(0x16),
    CMD(0xD61A), DAT(0x02),
    CMD(0xD61B), DAT(0x18),
    CMD(0xD61C), DAT(0x02),
    CMD(0xD61D), DAT(0x4B),
    CMD(0xD61E), DAT(0x02),
    CMD(0xD61F), DAT(0x80),
    CMD(0xD620), DAT(0x02),
    CMD(0xD621), DAT(0xA1),
    CMD(0xD622), DAT(0x02),
    CMD(0xD623), DAT(0xCD),
    CMD(0xD624), DAT(0x02),
    CMD(0xD625), DAT(0xEB),
    CMD(0xD626), DAT(0x03),
    CMD(0xD627), DAT(0x11),
    CMD(0xD628), DAT(0x03),
    CMD(0xD629), DAT(0x27),
    CMD(0xD62A), DAT(0x03),
    CMD(0xD62B), DAT(0x42),
    CMD(0xD62C), DAT(0x03),
    CMD(0xD62D), DAT(0x4F),
    CMD(0xD62E), DAT(0x03),
    CMD(0xD62F), DAT(0x5A),
    CMD(0xD630), DAT(0x03),
    CMD(0xD631), DAT(0x62),
    CMD(0xD632), DAT(0x03),
    CMD(0xD633), DAT(0xFF),


    CMD(0xF000), DAT(0x55),
    CMD(0xF001), DAT(0xAA),
    CMD(0xF002), DAT(0x52),
    CMD(0xF003), DAT(0x08),
    CMD(0xF004), DAT(0x03),

    CMD(0xB000), DAT(0x05),
    CMD(0xB001), DAT(0x17),
    CMD(0xB002), DAT(0xF9),
    CMD(0xB003), DAT(0x53),
    CMD(0xB004), DAT(0x53),
    CMD(0xB005), DAT(0x00),
    CMD(0xB006), DAT(0x30),


    CMD(0xB100), DAT(0x05),
    CMD(0xB101), DAT(0x17),
    CMD(0xB102), DAT(0xFB),
    CMD(0xB103), DAT(0x55),
    CMD(0xB104), DAT(0x53),
    CMD(0xB105), DAT(0x00),
    CMD(0xB106), DAT(0x30),

    CMD(0xB200), DAT(0xFC),
    CMD(0xB201), DAT(0xFD),
    CMD(0xB202), DAT(0xFE),
    CMD(0xB203), DAT(0xFF),
    CMD(0xB204), DAT(0xF0),
    CMD(0xB205), DAT(0xED),
    CMD(0xB206), DAT(0x00),
    CMD(0xB207), DAT(0xC4),
    CMD(0xB208), DAT(0x08),

    CMD(0xB300), DAT(0x5B),
    CMD(0xB301), DAT(0x00),
    CMD(0xB302), DAT(0xFC),
    CMD(0xB303), DAT(0x5A),
    CMD(0xB304), DAT(0x5A),
    CMD(0xB305), DAT(0x03),

    CMD(0xB400), DAT(0x00),
    CMD(0xB401), DAT(0x01),
    CMD(0xB402), DAT(0x02),
    CMD(0xB403), DAT(0x03),
    CMD(0xB404), DAT(0x00),
    CMD(0xB405), DAT(0x40),
    CMD(0xB406), DAT(0x04),
    CMD(0xB407), DAT(0x08),
    CMD(0xB408), DAT(0xED),
    CMD(0xB409), DAT(0x00),
    CMD(0xB40A), DAT(0x00),

    CMD(0xB500), DAT(0x40),
    CMD(0xB501), DAT(0x00),
    CMD(0xB502), DAT(0x00),
    CMD(0xB503), DAT(0x80),
    CMD(0xB504), DAT(0x5F),
    CMD(0xB505), DAT(0x5E),
    CMD(0xB506), DAT(0x50),
    CMD(0xB507), DAT(0x50),
    CMD(0xB508), DAT(0x33),
    CMD(0xB509), DAT(0x33),
    CMD(0xB50A), DAT(0x55),

    CMD(0xB600), DAT(0xBC),
    CMD(0xB601), DAT(0x00),
    CMD(0xB602), DAT(0x00),
    CMD(0xB603), DAT(0x00),
    CMD(0xB604), DAT(0x2A),
    CMD(0xB605), DAT(0x80),
    CMD(0xB606), DAT(0x00),

    CMD(0xB700), DAT(0x00),
    CMD(0xB701), DAT(0x00),
    CMD(0xB702), DAT(0x00),
    CMD(0xB703), DAT(0x00),
    CMD(0xB704), DAT(0x00),
    CMD(0xB705), DAT(0x00),
    CMD(0xB706), DAT(0x00),
    CMD(0xB707), DAT(0x00),

    CMD(0xB800), DAT(0x11),
    CMD(0xB801), DAT(0x60),
    CMD(0xB802), DAT(0x00),

    CMD(0xB900), DAT(0x90),

    CMD(0xBA00), DAT(0x44),
    CMD(0xBA01), DAT(0x44),
    CMD(0xBA02), DAT(0x08),
    CMD(0xBA03), DAT(0xAC),
    CMD(0xBA04), DAT(0xE2),
    CMD(0xBA05), DAT(0x64),
    CMD(0xBA06), DAT(0x44),
    CMD(0xBA07), DAT(0x44),
    CMD(0xBA08), DAT(0x44),
    CMD(0xBA09), DAT(0x44),
    CMD(0xBA0A), DAT(0x47),
    CMD(0xBA0B), DAT(0x3F),
    CMD(0xBA0C), DAT(0xDB),
    CMD(0xBA0D), DAT(0x91),
    CMD(0xBA0E), DAT(0x54),
    CMD(0xBA0F), DAT(0x44),

    CMD(0xBB00), DAT(0x44),
    CMD(0xBB01), DAT(0x43),
    CMD(0xBB02), DAT(0x79),
    CMD(0xBB03), DAT(0xFD),
    CMD(0xBB04), DAT(0xB5),
    CMD(0xBB05), DAT(0x14),
    CMD(0xBB06), DAT(0x44),
    CMD(0xBB07), DAT(0x44),
    CMD(0xBB08), DAT(0x44),
    CMD(0xBB09), DAT(0x44),
    CMD(0xBB0A), DAT(0x40),
    CMD(0xBB0B), DAT(0x4A),
    CMD(0xBB0C), DAT(0xCE),
    CMD(0xBB0D), DAT(0x86),
    CMD(0xBB0E), DAT(0x24),
    CMD(0xBB0F), DAT(0x44),

    CMD(0xBC00), DAT(0xE0),
    CMD(0xBC01), DAT(0x1F),
    CMD(0xBC02), DAT(0xF8),
    CMD(0xBC03), DAT(0x07),

    CMD(0xBD00), DAT(0xE0),
    CMD(0xBD01), DAT(0x1F),
    CMD(0xBD02), DAT(0xF8),
    CMD(0xBD03), DAT(0x07),

    CMD(0xF000), DAT(0x55),
    CMD(0xF001), DAT(0xAA),
    CMD(0xF002), DAT(0x52),
    CMD(0xF003), DAT(0x08),
    CMD(0xF004), DAT(0x00),

    CMD(0xB000), DAT(0x00),
    CMD(0xB001), DAT(0x10),

    CMD(0xB100), DAT(0xFC),//倒屏打开
    CMD(0xB101), DAT(0x06),//倒屏打开

    CMD(0xB500), DAT(0x6B),
    CMD(0xBC00), DAT(0x00),

    CMD(0x1100),DAT(0x00),
    DLY(50),
    CMD(0x2900),DAT(0x00),
    DLY(50),

LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    // FPC-LX50FWB4001-V2
    .name = "RM68172",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_24,

    .pclk_div 		= LCD_PCLK_DIV(24000000),
    .clk_per_pixel 	= 1,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .pclk_edge      = LCD_PCLK_EDGE_FALLING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw            = 2,
    .vbp            = 16,
    .vfp            = 20,
    .hlw            = 3,
    .hbp            = 20,
    .hfp            = 20,

    LCD_SPI_DEFAULT(16),

    .data_mode 	= LCD_DATA_MODE0_24BIT_RGB888,

    .screen_w 		= 480,
    .screen_h 		= 852,

    .video_w  		= 852,
    .video_h 	 	= 480,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()
#endif

