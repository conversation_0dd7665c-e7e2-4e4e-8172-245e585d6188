/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_WIDGET_MANAGE_H
#define UI_WIN_WIDGET_MANAGE_H


typedef resID (*getResByID)(u16 id);
typedef struct
{
	uiWidgetObj widget;
	getResByID  getRes;
	u16    minID;
	u16    maxID;
	u16    curID;
	u16    reserve;
}uiWidgetManageObj;

/*******************************************************************************
* Function Name  : uiWidgetManageCreate
* Description    : uiWidgetManageCreate
* Input          : widgetCreateInfor* infor,winHandle parent,uiWinCB cb
* Output         : none                                            
* Return         : winHandle
*******************************************************************************/
winHandle uiWidgetManageCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);

#endif
