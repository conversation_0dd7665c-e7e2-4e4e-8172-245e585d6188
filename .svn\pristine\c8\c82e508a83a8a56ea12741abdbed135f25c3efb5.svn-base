/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_ITEM_MENU_H
#define UI_WIN_ITEM_MENU_H

typedef struct
{
	uiWidgetObj widget;
	ICON_DRAW_T	image;
	ICON_DRAW_T	imageSelect;
	winHandle   hImage;
	winHandle   hStr;
	u16 		select;
	uiColor 	color;
	uiColor 	selectColor;

}uiItemMenuObj;
/*******************************************************************************
* Function Name  : uiItemCreateItemMenu
* Description    : uiItemCreateItemMenu
* Input          : s16 x0,s16 y0,u16 width,u16 height
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiItemCreateItemMenu(s16 x0,s16 y0,u16 width,u16 height,u16 style, uiColor rimColor, u8 round_type);
#endif
