/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuVersionWin.c"
#if UI_SHOW_SMALL_PANEL > 0
/*******************************************************************************
* Function Name  : getVersionResInfor
* Description    : getVersionResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : u32: 
*******************************************************************************/
UNUSED static u32 getVersionResInfor(u32 item,u32* image,u32* str)
{
	return 0;
}
/*******************************************************************************
* Function Name  : versionKeyMsgOk
* Description    : versionKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : versionKeyMsgMenu
* Description    : versionKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : versionKeyMsgMode
* Description    : versionKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : versionKeyMsgMode
* Description    : versionKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]versionOpenWin\n");
	uiItemManageSetRowSum(			winItem(handle,VERSION_TIPS_ID),1,   Rh(32));
	uiItemManageSetColumnSumWithGap(	winItem(handle,VERSION_TIPS_ID),0,2,Rw(50),Rw(12));

	return 0;
}
/*******************************************************************************
* Function Name  : versionCloseWin
* Description    : versionCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]versionCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : versionWinChildClose
* Description    : versionWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int versionWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]versionWinChildClose\n");
	return 0;
}
/*******************************************************************************
* Function Name  : versionTouchWin
* Description    : versionTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int versionTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("versionTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : versionTouchSlideOff
* Description    : versionTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int versionTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;

	if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	return 0;
}

ALIGNED(4) msgDealInfor verisonMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	versionOpenWin},
	{SYS_CLOSE_WINDOW,	versionCloseWin},
	{SYS_CHILE_COLSE,	versionWinChildClose},
	{SYS_TOUCH_WINDOW,  versionTouchWin},
	{SYS_TOUCH_SLIDE_OFF,versionTouchSlideOff},
	{KEY_EVENT_OK,		versionKeyMsgOk},
	{KEY_EVENT_MENU,	versionKeyMsgMenu},
	{KEY_EVENT_MODE,	versionKeyMsgMode},
	{EVENT_MAX,			NULL},
};

WINDOW(versionWindow,verisonMsgDeal,versionWin)

#endif
