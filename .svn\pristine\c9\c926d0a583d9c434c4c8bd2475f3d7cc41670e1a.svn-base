/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	VIDEO_MODE_ID=0,
	VIDEO_REC_TIME_ID,
	VIDEO_RESOLUTION_ID,
	
	VIDEO_SENSOR_RES_ID,
	
	VIDEO_POWERON_TIME_ID,
	VIDEO_BATERRY_ID,

	VIDEO_SYSTIME_ID,
	VIDEO_LED_ID,
	VIDEO_IRLED_ID,
	VIDEO_LOCK_ID,
	VIDEO_SD_ID,
	VIDEO_MIC_ID,
	VIDEO_SCALER_ID,
	VIDEO_FOCUS_ID,
	VIDEO_ROTATE_ID,
	VIDEO_CHACK_SD_ID,

	VIDEO_MAX_ID
};
// char * task_com_sdcCap_str(void);
UNUSED ALIGNED(4) const widgetCreateInfor recordVideoWin[] =
{
	createFrameWin( 						Rx(0),   Ry(0),   Rw(320), Rh(240), R_ID_PALETTE_Transparent, WIN_ABS_POS),

	createImageIcon(VIDEO_MODE_ID,      	Rx(5),   Ry(0),   Rw(30),  Rh(30),  R_ID_ICON_MTRECORD, 	ALIGNMENT_LEFT),
	createStringIcon(VIDEO_REC_TIME_ID,  	Rx(40),  Ry(0),   Rw(80), Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
//	createStringIcon(VIDEO_RESOLUTION_ID,	Rx(120), Ry(0),   Rw(30),  Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
//	createStringIcon(VIDEO_SENSOR_RES_ID,	Rx(150), Ry(0),   Rw(80),  Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	
//	createStringIcon(VIDEO_POWERON_TIME_ID, Rx(230), Ry(0),   Rw(60),  Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_RIGHT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createImageIcon(VIDEO_BATERRY_ID,    	Rx(290), Ry(0),   Rw(30),  Rh(30), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),	
	
	createStringIcon(VIDEO_SYSTIME_ID,      Rx(5),   Ry(210), Rw(285), Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White,	DEFAULT_FONT),
	createImageIcon(VIDEO_LED_ID,        	Rx(235), Ry(0), Rw(30),  Rh(30), 	R_ID_ICON_MTLED3,	ALIGNMENT_CENTER),
	
	createStringIcon(VIDEO_SCALER_ID,		Rx(270),	 Ry(210), Rw(35), Rh(30),	RAM_ID_MAKE(" "),		ALIGNMENT_LEFT, 	R_ID_PALETTE_White, DEFAULT_FONT),

	createImageIcon(VIDEO_IRLED_ID,     	Rx(290), Ry(110), Rw(30),  Rh(30), 	R_ID_ICON_MTIROFF,		ALIGNMENT_CENTER),

//	createImageIcon(VIDEO_LOCK_ID,      	Rx(290), Ry(150), Rw(30),  Rh(30), 	R_ID_ICON_MTLOCK,		ALIGNMENT_CENTER),
	createImageIcon(VIDEO_SD_ID,        	Rx(260), Ry(0), Rw(30),  Rh(30), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
//	createImageIcon(VIDEO_MIC_ID,       	Rx(290), Ry(210), Rw(30),  Rh(30), 	R_ID_ICON_MTMICOFF,		ALIGNMENT_CENTER),
createImageIcon(VIDEO_FOCUS_ID,        Rx(125), Ry(80), Rw(70),  Rh(70), 	R_ID_ICON_MTFOCUS,	ALIGNMENT_CENTER),
createImageIcon(VIDEO_ROTATE_ID,        Rx(150), Ry(0), Rw(30),  Rh(30), 	R_ID_ICON_MTROTATE,	ALIGNMENT_CENTER),
	widgetEnd(),
};
/*******************************************************************************
* Function Name  : videoRemainTimeShow
* Description    : videoRemainTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoRemainTimeShow(winHandle handle)
{
	uiWinSetStrInfor(winItem(handle,VIDEO_REC_TIME_ID),DEFAULT_FONT,ALIGNMENT_LEFT,R_ID_PALETTE_White);
	uiWinSetResid(	winItem(handle,VIDEO_REC_TIME_ID),RAM_ID_MAKE(" "));
//	uiWinSetResid(	winItem(handle,VIDEO_REC_TIME_ID),RAM_ID_MAKE(task_com_rec_remain_time_str()));
}
/*******************************************************************************
* Function Name  : videoRecTimeShow
* Description    : videoRecTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoRecTimeShow(winHandle handle)
{
	uiWinSetStrInfor(winItem(handle,VIDEO_REC_TIME_ID),	DEFAULT_FONT,	ALIGNMENT_LEFT,R_ID_PALETTE_Red);
	uiWinSetResid(	winItem(handle,VIDEO_REC_TIME_ID),	RAM_ID_MAKE(task_com_rec_show_time_str()));
}
/*******************************************************************************
* Function Name  : videoResolutionShow
* Description    : videoResolutionShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoResolutionShow(winHandle handle)
{
	switch(user_config_get(CONFIG_ID_RESOLUTION))
	{
		case R_ID_STR_RES_QVGA: uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),RAM_ID_MAKE("QVGA")); break;
		case R_ID_STR_RES_VGA: uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),RAM_ID_MAKE("VGA")); break;
		case R_ID_STR_RES_HD:  uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),RAM_ID_MAKE("HD")); break;
		case R_ID_STR_RES_FHD: uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),RAM_ID_MAKE("FHD")); break;
		default:			   uiWinSetResid(winItem(handle,VIDEO_RESOLUTION_ID),RAM_ID_MAKE("???")); break;
	}
}
/*******************************************************************************
* Function Name  : videoResolutionShow
* Description    : videoResolutionShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/	
UNUSED static void videoSensorResShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,VIDEO_SENSOR_RES_ID),RAM_ID_MAKE(task_com_sensor_res_str()));
}
/*******************************************************************************
* Function Name  : videoPoweOnTimeShow
* Description    : videoPoweOnTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoPoweOnTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,VIDEO_POWERON_TIME_ID),RAM_ID_MAKE(task_com_powerOnTime_str()));
}
/*******************************************************************************
* Function Name  : videoBaterryShow
* Description    : videoBaterryShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoBaterryShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERY5;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4:
			//case BATTERY_STAT_5:
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetVisible(winItem(handle,VIDEO_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,VIDEO_BATERRY_ID),batid);

}
/*******************************************************************************
* Function Name  : videoSysTimeShow
* Description    : videoSysTimeShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoSysTimeShow(winHandle handle)
{
	uiWinSetResid(winItem(handle,VIDEO_SYSTIME_ID),RAM_ID_MAKE(hal_rtcTime2String(hal_rtcTimeGet())));
}
/*******************************************************************************
* Function Name  : videoMonitorShow
* Description    : videoMonitorShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoIrLedShow(winHandle handle)
{
	if(hardware_setup.ir_led_en)
	{
		if(user_config_get(CONFIG_ID_IR_LED)==R_ID_STR_COM_OFF)
			uiWinSetResid(winItem(handle,VIDEO_IRLED_ID),R_ID_ICON_MTIROFF);
		else
			uiWinSetResid(winItem(handle,VIDEO_IRLED_ID),R_ID_ICON_MTIRON);
	}else
	{
		uiWinSetVisible(winItem(handle,VIDEO_IRLED_ID),0);
	}
}



// UNUSED static void PhotoSdShow(winHandle handle)
// {
// 	uiWinSetResid(winItem(handle,VIDEO_CHACK_SD_ID),RAM_ID_MAKE(task_com_sdcCap_str()));
// }

/*******************************************************************************
* Function Name  : videoLockShow
* Description    : videoLockShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoLockShow(winHandle handle)
{
	if(SysCtrl.dev_stat_gsensorlock)
	{
	    uiWinSetVisible(winItem(handle,VIDEO_LOCK_ID),1);
		uiWinSetResid(winItem(handle,VIDEO_LOCK_ID),R_ID_ICON_MTLOCK);

	}
	else
		uiWinSetVisible(winItem(handle,VIDEO_LOCK_ID),0);
}
/*******************************************************************************
* Function Name  : videoSDShow
* Description    : videoSDShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		uiWinSetResid(winItem(handle,VIDEO_SD_ID),R_ID_ICON_MTSDCNORMAL);
	else
		uiWinSetResid(winItem(handle,VIDEO_SD_ID),R_ID_ICON_MTSDCNULL);
}
/*******************************************************************************
* Function Name  : videoMicShow
* Description    : videoMicShow
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
UNUSED static void videoMicShow(winHandle handle)
{
	if(user_config_get(CONFIG_ID_AUDIOREC)==R_ID_STR_COM_OFF)
		uiWinSetResid(winItem(handle,VIDEO_MIC_ID),R_ID_ICON_MTMICOFF);
	else
		uiWinSetResid(winItem(handle,VIDEO_MIC_ID),R_ID_ICON_MTMICON);
}

UNUSED static void videoScalerShow(winHandle handle)
{

	uiWinSetVisible(winItem(handle,VIDEO_SCALER_ID),1);
	uiWinSetResid(winItem(handle,VIDEO_SCALER_ID),RAM_ID_MAKE(task_com_scaler_str()));//
	
}
UNUSED static void videoScalerHide(winHandle handle)
{
	uiWinSetVisible(winItem(handle,VIDEO_SCALER_ID),0);
}


UNUSED static void videoLedShow(winHandle handle)
{
		resID batid;
		switch(SysCtrl.led_pwm_level)
		{
			case 0: 					 break;
			case 1: batid = R_ID_ICON_MTLED1; break;
			case 2: batid = R_ID_ICON_MTLED2; break;
			case 3: batid = R_ID_ICON_MTLED3; break;
			default:
										 break;
		}

	if(SysCtrl.led_pwm_level)
	{
		uiWinSetVisible(winItem(handle,VIDEO_LED_ID),1);
		uiWinSetResid(winItem(handle,VIDEO_LED_ID),batid);

	}else
	{
		uiWinSetVisible(winItem(handle,VIDEO_LED_ID),0);
	}

}



UNUSED static void recordRotateShow(winHandle handle)
{
	if(SysCtrl.rotate_status)
		uiWinSetVisible(winItem(handle,VIDEO_ROTATE_ID),1);
	else
		uiWinSetVisible(winItem(handle,VIDEO_ROTATE_ID),0);
}