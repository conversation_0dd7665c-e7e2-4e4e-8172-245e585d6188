/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_AUADC_H
    #define HX330X_AUADC_H

//auadc sample rate
#define AUADC_8K                8000L//0x0A
#define AUADC_11K               11025L//0x09
#define AUADC_12K               12000L//0x08
#define AUADC_16K               16000L//0x06
#define AUADC_22K               22050L//0x05
#define AUADC_24K               24000L//0x04
#define AUADC_32K               32000L
#define AUADC_44K               44100L
#define AUADC_48K               48000L

//auadc samplerate              //Set FILT2DN | Set FILT1DN |Set FILT2DN_EN |Set FILT1DN_EN
#define AUADC_SPR_8K            ((3<<21)|(5<<18)|(1<<17)|(1<<16))       //192K /6/4
#define AUADC_SPR_11K           ((3<<21)|(3<<18)|(1<<17)|(1<<16))       //174.47K /4/4  XSFR_AUADCFSBAUDL = 136-1
#define AUADC_SPR_12K           ((3<<21)|(3<<18)|(1<<17)|(1<<16))       //192K /4/4
#define AUADC_SPR_16K           ((1<<21)|(5<<18)|(1<<17)|(1<<16))       //192K/6/2
#define AUADC_SPR_22K           ((1<<21)|(3<<18)|(1<<17)|(1<<16))        //176.47K/4/2   XSFR_AUADCFSBAUDL = 136-1
#define AUADC_SPR_24K           ((1<<21)|(3<<18)|(1<<17)|(1<<16))       //192K/4/2
#define AUADC_SPR_32K           ((0<<21)|(5<<18)|(0<<17)|(1<<16))       //192K/6  /3/2
#define AUADC_SPR_44K           ((0<<21)|(3<<18)|(0<<17)|(1<<16))       //176.47K/4   XSFR_AUADCFSBAUDL = 136-1
#define AUADC_SPR_48K           ((0<<21)|(3<<18)|(0<<17)|(1<<16))       //192K/4
#define AUADC_SPR_MASK          0xFFFF0000

#define MIC_VOLUME_MAX			32
//MIC gain control
//MIC(AUADC)音量分级 16级  暂定这样配置，再根据实际修改
//模拟增益 最大0x0d
#define MIC_AGAIN_N6DB           				0x00 //-6dB
#define MIC_AGAIN_0DB            				0x01 //0dB
#define MIC_AGAIN_6DB            				0x02 //6dB
#define MIC_AGAIN_12DB           				0x03 //12dB
#define MIC_AGAIN_18DB           				0x04 //18dB
#define MIC_AGAIN_24DB           				0x05 //24dB
#define MIC_AGAIN_30DB           				0x06 //30dB
#define MIC_AGAIN_36DB           				0x07 //36dB
#define MIC_AGAIN_42DB           				0x08 //42dB
#define MIC_AGAIN_48DB           				0x09 //48dB
#define MIC_AGAIN_54DB           				0x0a //54dB
#define MIC_AGAIN_60DB           				0x0b //60dB
#define MIC_AGAIN_66DB           				0x0c //66dB
#define MIC_AGAIN_72DB           				0x0d //72dB
//数字增益 最大0x3b				
#define MIC_DGAIN_P0D0DB         				(0x00)
#define MIC_DGAIN_P1D5DB         				(0x0F)
#define MIC_DGAIN_P3D0DB         				(0x1E)
#define MIC_DGAIN_P4D5DB         				(0x2D)
#define MIC_DGAIN_P6D0DB         				(0x3C)
/*******************************************************************************
* Function Name  : hx330x_auadcIRQHandler
* Description    : auadc irq handler
* Input          : 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_auadcIRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_auadcHalfIRQRegister
* Description    : auadc half irq regiser
* Input          :void (*isr)(void) : isr call back
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_auadcHalfIRQRegister(void (*isr)(void));
/*******************************************************************************
* Function Name  : hx330x_auadcEndIRQRegister
* Description    : auadc end irq regiser
* Input          :void (*isr)(void) : isr call back
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_auadcEndIRQRegister(void (*isr)(void));
/*******************************************************************************
* Function Name  : hx330x_auadcEnable
* Description    : auadc enable
* Input          :u8 en : 1-enable,0-disable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_auadcEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_auadcBufferSet
* Description    : auadc set frame buffer
* Input          : u32 addr : addr
				   u32 len   : buffer length
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_auadcBufferSet(u32 addr,u32 len);
/*******************************************************************************
* Function Name  : hx330x_auadcInit
* Description    : auadc intial 
* Input          : src: 0: pll0_out(96M) , 1: sys pll 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_auadcInit(u8 src);
/*******************************************************************************
* Function Name  : hx330x_auadcSetSampleSet
* Description    : auadc set sample set 
* Input          : int sample_rate : sample rate
* Output         : None
* Return         : None
*******************************************************************************/
bool hx330x_auadcSetSampleSet(int sample_rate);
/*******************************************************************************
* Function Name  : hx330x_auadcAGCEnable
* Description    : auadc agc enable
* Input          :u8 en : 1-enable,0-disable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_auadcAGCEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_auadcGainSet
* Description    : auadc agc gain set
* Input          : vol: 0~MIC_VOLUME_MAX
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_auadcGainSet(u32 vol);


/*******************************************************************************
* Function Name  : hx330x_agc_pwr_get
* Description    : hx330x_agc_pwr_get 
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
u16 hx330x_agc_pwr_get(void);









#endif
