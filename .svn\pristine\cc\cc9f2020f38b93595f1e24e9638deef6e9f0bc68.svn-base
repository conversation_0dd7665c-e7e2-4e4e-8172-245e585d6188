/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../hal/inc/hal.h"

/*******************************************************************************
* Function Name : res_GetStringInfor
* Description   :       
* Input         : u32 id,u16* width,u16* height,u8 font  
* Output        : 
* Return        : strnum  
*******************************************************************************/
u32 res_GetStringInfor(u32 id,u16* width,u16* height,u8 font)
{
	if(RES_ID_IS_RAM(id))
	{
		u8* str = RAM_ID_TO_ADDR(id);
		if(str == NULL)
		{
			return 0;
		}
		return res_getAsciiStringSize(str,width,height,font);
	}
	else if(RES_ID_IS_STR(id))
	{
		return res_font_GetString(id,width,height);
	}
	return 0;
}
/*******************************************************************************
* Function Name : res_GetCharInfor
* Description   :       
* Input         : u32 id,u8 num,u16* width,u16* height,u8 font,u8* special 
* Output        : 
* Return        : strnum  
*******************************************************************************/
u8* res_GetCharInfor(u32 id,u8 num,u16* width,u16* height,u8 font,u8* special)
{

	if(width)
		*width=0;
	if(height)
		*height=0;
	if(special)
		*special=0;
	if(RES_ID_IS_RAM(id))
	{
		u8 *str = RAM_ID_TO_ADDR(id);
		if(str==NULL)
			return NULL;
		return (u8*)res_ascii_get(str[num],width,height,font);
	}
	else if(RES_ID_IS_STR(id))
	{
		return (u8*)res_font_GetChar(id, num, width, height,special);
	}
	return NULL;
}

	