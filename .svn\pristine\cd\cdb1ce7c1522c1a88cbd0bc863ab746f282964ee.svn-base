/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : ascii_tab, font num1 :9*16
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"
/*******************************************************************************
* Function Name  : 
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
const unsigned char ascii_num1_32[]= // ' '
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_33[]= // '!'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_35[]= // '#'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x36,0x00,0x36,0x00,0x36,0x00,0xff,0x00,0x36,0x00,
   0x6c,0x00,0x6c,0x00,0xff,0x00,0x6c,0x00,0x6c,0x00,0x6c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_36[]= // '$'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x18,0x00,0x3e,0x00,0x7b,0x00,0x7b,0x00,0x78,0x00,0x38,0x00,
   0x1c,0x00,0x1e,0x00,0x7b,0x00,0x7b,0x00,0x7b,0x00,0x3e,0x00,0x18,0x00,0x18,0x00,
};
const unsigned char ascii_num1_37[]= // '%'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x66,0x00,0xfc,0x00,0xfc,0x00,0xfc,0x00,0xf8,0x00,
   0x7e,0x00,0x3f,0x00,0x3f,0x00,0x3f,0x00,0x6f,0x00,0x66,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_38[]= // '&'
{
   0x09,0x10,
   0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,
   0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,0x7f,0x80,
};
const unsigned char ascii_num1_39[]= // '''
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0xc0,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_40[]= // '('
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x01,0x80,0x03,0x00,0x06,0x00,0x06,0x00,0x0c,0x00,0x0c,0x00,
   0x0c,0x00,0x0c,0x00,0x06,0x00,0x06,0x00,0x03,0x00,0x01,0x80,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_41[]= // ')'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0xc0,0x00,0x60,0x00,0x30,0x00,0x30,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x30,0x00,0x30,0x00,0x60,0x00,0xc0,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_42[]= // '*'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0xff,0x00,0x3c,0x00,
   0x3c,0x00,0xff,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_43[]= // '+'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0xff,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_44[]= // ','
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0xc0,0x00,
};
const unsigned char ascii_num1_45[]= // '-'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_46[]= // '.'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_47[]= // '/'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x01,0x80,0x03,0x00,0x03,0x00,0x06,0x00,0x06,0x00,0x06,0x00,
   0x0c,0x00,0x0c,0x00,0x18,0x00,0x18,0x00,0x30,0x00,0x30,0x00,0x60,0x00,0x00,0x00,
};
const unsigned char ascii_num1_48[]= // '0'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1e,0x00,0x33,0x00,0x61,0x80,0x61,0x80,0x61,0x80,
   0x61,0x80,0x61,0x80,0x61,0x80,0x61,0x80,0x33,0x00,0x1e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_49[]= // '1'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x38,0x00,0x78,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_50[]= // '2'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x36,0x00,0x63,0x00,0x63,0x00,0x03,0x00,
   0x06,0x00,0x0c,0x00,0x18,0x00,0x30,0x00,0x60,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_51[]= // '3'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,0x63,0x00,0x63,0x00,0x03,0x00,0x03,0x00,
   0x0e,0x00,0x03,0x00,0x03,0x00,0x63,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_52[]= // '4'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x07,0x00,0x0f,0x00,0x1b,0x00,0x1b,0x00,
   0x33,0x00,0x63,0x00,0x7f,0x80,0x03,0x00,0x03,0x00,0x03,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_53[]= // '5'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x60,0x00,0x60,0x00,0x7e,0x00,0x63,0x00,
   0x03,0x00,0x03,0x00,0x03,0x00,0x63,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_54[]= // '6'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1e,0x00,0x33,0x00,0x63,0x00,0x60,0x00,0x7e,0x00,
   0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x33,0x00,0x1e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_55[]= // '7'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x03,0x00,0x03,0x00,0x06,0x00,0x06,0x00,
   0x06,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_56[]= // '8'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1e,0x00,0x33,0x00,0x61,0x80,0x61,0x80,0x33,0x00,
   0x1e,0x00,0x33,0x00,0x61,0x80,0x61,0x80,0x33,0x00,0x1e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_57[]= // '9'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x66,0x00,0x63,0x00,0x63,0x00,0x63,0x00,
   0x67,0x00,0x3f,0x00,0x03,0x00,0x63,0x00,0x66,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_58[]= // ':'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_59[]= // ';'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x30,0x00,
};
const unsigned char ascii_num1_60[]= // '<'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x06,0x00,0x0c,0x00,0x18,0x00,0x30,0x00,
   0x60,0x00,0x30,0x00,0x18,0x00,0x0c,0x00,0x06,0x00,0x03,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_61[]= // '='
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0xff,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_62[]= // '>'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x30,0x00,0x18,0x00,0x0c,0x00,0x06,0x00,
   0x03,0x00,0x06,0x00,0x0c,0x00,0x18,0x00,0x30,0x00,0x60,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_63[]= // '?'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,0x63,0x00,0x63,0x00,0x03,0x00,0x06,0x00,
   0x0c,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_64[]= // '@'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x66,0x00,0x7f,0x00,0xff,0x00,0xff,0x00,
   0xff,0x00,0xff,0x00,0xff,0x00,0x7e,0x00,0x60,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};

const unsigned char ascii_num1_65[]= // 'A'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x1c,0x00,0x36,0x00,0x36,0x00,0x36,0x00,
   0x63,0x00,0x63,0x00,0x7f,0x00,0x63,0x00,0xc1,0x80,0xc1,0x80,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_66[]= // 'B'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0xc6,0x00,0xc3,0x00,0xc3,0x00,0xc6,0x00,
   0xfc,0x00,0xc6,0x00,0xc3,0x00,0xc3,0x00,0xc6,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_67[]= // 'C'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x66,0x00,0xc3,0x00,0xc3,0x00,0xc0,0x00,
   0xc0,0x00,0xc0,0x00,0xc3,0x00,0xc3,0x00,0x66,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_68[]= // 'D'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0xc6,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,
   0xc3,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0xc6,0x00,0xfc,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_69[]= // 'E'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,
   0x7e,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_70[]= // 'F'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,
   0xfe,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_71[]= // 'G'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x66,0x00,0xc3,0x00,0xc3,0x00,0xc0,0x00,
   0xcf,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0x67,0x00,0x3f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_72[]= // 'H'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,
   0x7f,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_73[]= // 'I'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_74[]= // 'J'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x03,0x00,0x03,0x00,0x03,0x00,0x03,0x00,
   0x03,0x00,0x03,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_75[]= // 'K'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x61,0x80,0x63,0x00,0x66,0x00,0x6c,0x00,0x78,0x00,
   0x7c,0x00,0x6c,0x00,0x66,0x00,0x63,0x00,0x63,0x00,0x61,0x80,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_76[]= // 'L'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,
   0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xff,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_77[]= // 'M'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,0xc3,0x00,0xe7,0x00,0xe7,0x00,0xe7,0x00,
   0xff,0x00,0xff,0x00,0xff,0x00,0xff,0x00,0xdb,0x00,0xdb,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_78[]= // 'N'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,0x73,0x00,0x73,0x00,0x7b,0x00,0x7b,0x00,
   0x7b,0x00,0x6f,0x00,0x6f,0x00,0x67,0x00,0x67,0x00,0x63,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_79[]= // 'O'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x36,0x00,0x63,0x00,0x63,0x00,0x63,0x00,
   0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x36,0x00,0x1c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_80[]= // 'P'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0xc6,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,
   0xc6,0x00,0xfc,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_81[]= // 'Q'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x1c,0x00,0x36,0x00,0x63,0x00,0x63,0x00,0x63,0x00,
   0x63,0x00,0x63,0x00,0x6f,0x00,0x6f,0x00,0x36,0x00,0x1f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_82[]= // 'R'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xfc,0x00,0xc6,0x00,0xc3,0x00,0xc3,0x00,0xc6,0x00,
   0xfc,0x00,0xcc,0x00,0xcc,0x00,0xc6,0x00,0xc6,0x00,0xc3,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_83[]= // 'S'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,0x66,0x00,0xc3,0x00,0xc0,0x00,0x70,0x00,
   0x1c,0x00,0x06,0x00,0xc3,0x00,0xc3,0x00,0x66,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_84[]= // 'T'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_85[]= // 'U'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,
   0xc3,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0x66,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_86[]= // 'V'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,0xc3,0x00,0x66,0x00,0x66,0x00,0x66,0x00,
   0x66,0x00,0x3c,0x00,0x3c,0x00,0x3c,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_87[]= // 'W'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xdb,0x00,0xdb,0x00,0xdb,0x00,0xff,0x00,0xff,0x00,
   0xff,0x00,0xff,0x00,0x66,0x00,0x66,0x00,0x66,0x00,0x66,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_88[]= // 'X'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,0x66,0x00,0x66,0x00,0x3c,0x00,0x3c,0x00,
   0x18,0x00,0x3c,0x00,0x3c,0x00,0x66,0x00,0x66,0x00,0xc3,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_89[]= // 'Y'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,0x66,0x00,0x66,0x00,0x3c,0x00,0x3c,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_90[]= // 'Z'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x00,0x03,0x00,0x06,0x00,0x0c,0x00,0x0c,0x00,
   0x18,0x00,0x30,0x00,0x60,0x00,0x60,0x00,0xc0,0x00,0xff,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_91[]= // '['
{
   0x09,0x10,
   0x1f,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x1f,0x00,0x00,0x00,
};
const unsigned char ascii_num1_92[]= // '\'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x18,0x00,
   0x18,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x06,0x00,0x06,0x00,0x03,0x00,0x03,0x00,
};
const unsigned char ascii_num1_93[]= // ']'
{
   0x09,0x10,
   0x7c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,
   0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x7c,0x00,0x00,0x00,
};
const unsigned char ascii_num1_94[]= // '^'
{
   0x09,0x10,
   0x00,0x00,0x1c,0x00,0x36,0x00,0x63,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_95[]= // '_'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xff,0x80,
};
const unsigned char ascii_num1_96[]= // '`'
{
   0x09,0x10,
   0x00,0x00,0x38,0x00,0x1c,0x00,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_97[]= // 'a'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,
   0x63,0x00,0x03,0x00,0x3f,0x00,0x63,0x00,0x67,0x00,0x3f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_98[]= // 'b'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7e,0x00,
   0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x73,0x00,0x7e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_99[]= // 'c'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,
   0x63,0x00,0xc0,0x00,0xc0,0x00,0xc0,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_100[]= // 'd'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x03,0x00,0x03,0x00,0x03,0x00,0x3f,0x00,
   0x67,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x67,0x00,0x3f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_101[]= // 'e'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,
   0x66,0x00,0xc3,0x00,0xff,0x00,0xc0,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_102[]= // 'f'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x0f,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x7f,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_103[]= // 'g'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0x00,
   0x66,0x00,0x66,0x00,0x66,0x00,0x3c,0x00,0x60,0x00,0x7e,0x00,0xc3,0x00,0x7e,0x00,
};
const unsigned char ascii_num1_104[]= // 'h'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x7e,0x00,
   0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_105[]= // 'i'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_106[]= // 'j'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x0c,0x00,0x0c,0x00,0x00,0x00,0x00,0x00,0x0c,0x00,
   0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x78,0x00,
};
const unsigned char ascii_num1_107[]= // 'k'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x66,0x00,
   0x6c,0x00,0x78,0x00,0x7c,0x00,0x6c,0x00,0x66,0x00,0x63,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_108[]= // 'l'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_109[]= // 'm'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xfe,0x00,
   0xdb,0x00,0xdb,0x00,0xdb,0x00,0xdb,0x00,0xdb,0x00,0xdb,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_110[]= // 'n'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7e,0x00,
   0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_111[]= // 'o'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3c,0x00,
   0x66,0x00,0xc3,0x00,0xc3,0x00,0xc3,0x00,0x66,0x00,0x3c,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_112[]= // 'p'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7e,0x00,
   0x73,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x73,0x00,0x7e,0x00,0x60,0x00,0x60,0x00,
};
const unsigned char ascii_num1_113[]= // 'q'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3f,0x00,
   0x67,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x67,0x00,0x3f,0x00,0x03,0x00,0x03,0x00,
};
const unsigned char ascii_num1_114[]= // 'r'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x37,0x00,
   0x3c,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_115[]= // 's'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3e,0x00,
   0x63,0x00,0x60,0x00,0x3e,0x00,0x03,0x00,0x63,0x00,0x3e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_116[]= // 't'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x18,0x00,0x7f,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x0f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_117[]= // 'u'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x63,0x00,
   0x63,0x00,0x63,0x00,0x63,0x00,0x63,0x00,0x67,0x00,0x3f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_118[]= // 'v'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,
   0x66,0x00,0x66,0x00,0x66,0x00,0x3c,0x00,0x3c,0x00,0x18,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_119[]= // 'w'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xdb,0x00,
   0xdb,0x00,0xff,0x00,0xff,0x00,0xff,0x00,0x66,0x00,0x66,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_120[]= // 'x'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,
   0x66,0x00,0x3c,0x00,0x18,0x00,0x3c,0x00,0x66,0x00,0xc3,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_121[]= // 'y'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xc3,0x00,
   0x66,0x00,0x66,0x00,0x66,0x00,0x3c,0x00,0x3c,0x00,0x18,0x00,0x18,0x00,0x70,0x00,
};
const unsigned char ascii_num1_122[]= // 'z'
{
   0x09,0x10,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7f,0x00,
   0x06,0x00,0x0c,0x00,0x18,0x00,0x30,0x00,0x60,0x00,0x7f,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_123[]= // '{'
{
   0x09,0x10,
   0x00,0x00,0x0e,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x18,0x00,
   0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0c,0x00,0x0e,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_124[]= // '|'
{
   0x09,0x10,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
   0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,
};
const unsigned char ascii_num1_125[]= // '}'
{
   0x09,0x10,
   0x00,0x00,0x70,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x18,0x00,
   0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x30,0x00,0x70,0x00,0x00,0x00,0x00,0x00,
};
const unsigned char ascii_num1_126[]= // '~'
{
   0x09,0x10,
   0x3b,0x00,0x6f,0x00,0x6e,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
   0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};


ALIGNED(4) const unsigned char * const ascii_num1_table[] =
{
   ascii_num1_32,// ' '
   ascii_num1_33,// '!'
   ascii_num1_35,// '#'
   ascii_num1_36,// '$'
   ascii_num1_37,// '%'
   ascii_num1_38,// '&'
   ascii_num1_39,// '''
   ascii_num1_40,// '('
   ascii_num1_41,// ')'
   ascii_num1_42,// '*'
   ascii_num1_43,// '+'
   ascii_num1_44,// ','
   ascii_num1_45,// '-'
   ascii_num1_46,// '.'
   ascii_num1_47,// '/'
   ascii_num1_48,// '0'
   ascii_num1_49,// '1'
   ascii_num1_50,// '2'
   ascii_num1_51,// '3'
   ascii_num1_52,// '4'
   ascii_num1_53,// '5'
   ascii_num1_54,// '6'
   ascii_num1_55,// '7'
   ascii_num1_56,// '8'
   ascii_num1_57,// '9'
   ascii_num1_58,// ':'
   ascii_num1_59,// ';'
   ascii_num1_60,// '<'
   ascii_num1_61,// '='
   ascii_num1_62,// '>'
   ascii_num1_63,// '?'
   ascii_num1_64,// '@'
   ascii_num1_65,// 'A'
   ascii_num1_66,// 'B'
   ascii_num1_67,// 'C'
   ascii_num1_68,// 'D'
   ascii_num1_69,// 'E'
   ascii_num1_70,// 'F'
   ascii_num1_71,// 'G'
   ascii_num1_72,// 'H'
   ascii_num1_73,// 'I'
   ascii_num1_74,// 'J'
   ascii_num1_75,// 'K'
   ascii_num1_76,// 'L'
   ascii_num1_77,// 'M'
   ascii_num1_78,// 'N'
   ascii_num1_79,// 'O'
   ascii_num1_80,// 'P'
   ascii_num1_81,// 'Q'
   ascii_num1_82,// 'R'
   ascii_num1_83,// 'S'
   ascii_num1_84,// 'T'
   ascii_num1_85,// 'U'
   ascii_num1_86,// 'V'
   ascii_num1_87,// 'W'
   ascii_num1_88,// 'X'
   ascii_num1_89,// 'Y'
   ascii_num1_90,// 'Z'
   ascii_num1_91,// '['
   ascii_num1_92,// '\'
   ascii_num1_93,// ']'
   ascii_num1_94,// '^'
   ascii_num1_95,// '_'
   ascii_num1_96,// '`'
   ascii_num1_97,// 'a'
   ascii_num1_98,// 'b'
   ascii_num1_99,// 'c'
   ascii_num1_100,// 'd'
   ascii_num1_101,// 'e'
   ascii_num1_102,// 'f'
   ascii_num1_103,// 'g'
   ascii_num1_104,// 'h'
   ascii_num1_105,// 'i'
   ascii_num1_106,// 'j'
   ascii_num1_107,// 'k'
   ascii_num1_108,// 'l'
   ascii_num1_109,// 'm'
   ascii_num1_110,// 'n'
   ascii_num1_111,// 'o'
   ascii_num1_112,// 'p'
   ascii_num1_113,// 'q'
   ascii_num1_114,// 'r'
   ascii_num1_115,// 's'
   ascii_num1_116,// 't'
   ascii_num1_117,// 'u'
   ascii_num1_118,// 'v'
   ascii_num1_119,// 'w'
   ascii_num1_120,// 'x'
   ascii_num1_121,// 'y'
   ascii_num1_122,// 'z'
   ascii_num1_123,// '{'
   ascii_num1_124,// '|'
   ascii_num1_125,// '}'
   ascii_num1_126,// '~'
};