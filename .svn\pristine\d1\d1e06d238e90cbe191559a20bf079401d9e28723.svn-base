/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef __MENU_TYPEDEF_H
#define __MENU_TYPEDEF_H

typedef void (*menuItemProc)(winHandle handle,uint32 parameNum,uint32* parame);
#define ARRAY_LEN(a)  (sizeof(a)/sizeof(a[0]))
typedef struct
{
	u32 image;
	u32 str;
}menuOption;

typedef struct
{
	u32 configId;
	u32 image;
	u32 str;
	u32 optionSum;
	menuOption* pOption;
}menuItem;
typedef struct
{
	u32 unselectImage;
	u32 selectImage;
	u32 str;
	u32 itemSum;
	menuItem* pItem;
}menuPage;

typedef struct
{
	u32 curPage;
	u32 pageSum;
	menuPage* pPage;
}menu;

#define MENU_OPTION_START(name)  		 		menuOption  menuOption##name[]= {
#define MENU_OPTION_IMAGE_STR(image,str)		{(u32)image,(u32)str},
#define MENU_OPTION_IMAGE(image)        		{(u32)image,(u32)0},
#define MENU_OPTION_STR(str)             		{(u32)0,    (u32)str},
#define MENU_OPTION_END()           	 		};

#define MENU_ITME_START(name)    				menuItem  menuItem##name[]= {
#define MENU_ITEM_OPTIONS(name,id,image,str)  	{(u32)id, (u32)image, (u32)str,(u32)ARRAY_LEN(menuOption##name),(menuOption*)menuOption##name},
#define MENU_ITEM_NO_ID(name, image,str)      	{(u32)0,  (u32)image, (u32)str,(u32)ARRAY_LEN(menuOption##name),(menuOption*)menuOption##name},
#define MENU_ITEM_PROC(itemProc,image,str)    	{(u32)0,  (u32)image, (u32)str,(u32)0,						  	(menuOption*)itemProc},
#define MENU_ITME_END()             			};

#define MENU_PAGE_START(name)       			menuPage  menuPage##name[]={
#define MENU_PAGE_ITEMS(name,unselectImage,selectImage,str)  {(u32)unselectImage,(u32)selectImage,(u32)str,(u32)ARRAY_LEN(menuItem##name),(menuItem*)menuItem##name},
#define MENU_PAGE_END()             			};

#define MENU_DEFINE(name)   					menu  menu##name = {(u32)0,(u32)ARRAY_LEN(menuPage##name),(menuPage*)menuPage##name};
#define MENU(name)          					(menu##name)

#define EXTERN_MENU(name)						extern menu (menu##name)



#endif
