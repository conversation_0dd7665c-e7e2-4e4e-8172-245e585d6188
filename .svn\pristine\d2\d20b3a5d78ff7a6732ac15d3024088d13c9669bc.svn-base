/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_SD_H
     #define HX330X_SD_H

#define SD_BUS_WIDTH4      		1
#define SD_BUS_WIDTH1      		0



enum{
	SDC_CTL_EN			= (1 << 0),
	SDC_CLK_KEEP		= (1 << 1), //sd clk out when no cmd and data is sending
	SDC_BIT4_MODE		= (1 << 2),
	SDC_OUT_RISING		= (1 << 3), //edge selection for sending data and cmd
	SDC_MORE_8CLK		= (1 << 4), //send 8 sd clk after cmd or data;
	SDC_SAMPLE_CRCSTA	= (1 << 5), //Sample crc according sd internal data status mathine
	SDC_AUTO_STOP		= (1 << 6), 
	SDIO_CP_EN			= (1 << 7), //SDIO MODE,slave interrupt(DAT1) capture and pending enable
	SDC_CMD_IE			= (1 << 8),
	SDC_DAT_IE			= (1 << 9),
	SDIO_CAP_LOW		= (0 << 10),
	SDIO_CAP_HIGH		= (1 << 10),
	SDIO_CAP_RISE		= (2 << 10),
	SDIO_CAP_FALL		= (3 << 10),
	SDC_CMD_DELAY_HALF  = (1 << 12), //SDC CMD Sample delay 1/2 sclk
	SDC_DAT_DELAY_HALF	= (1 << 13), //SDC DAT Sample delay 1/2 sclk
	SDIO_PND			= (1 << 14),
	SDC_DAT_PND			= (1 << 15),
	SDC_DAT_CRC_ERR		= (1 << 16),
	SDC_CMD_PND			= (1 << 17),
	SDC_CMD_CRC_ERR		= (1 << 18),
	SDC_NORPS			= (1 << 19), //when cmd is kick start by “001/011/101/111”,it means no response received after 256 sd clks after cmd
	SDC_DAT0_HIGH		= (1 << 20), //反应DAT0的逻辑电平，当DAT0拉高时表示设备IDLE
	SDC_WCRC_SUCCESS    = (2 << 21), //010:  success;
	SDC_WCRC_ERR		= (5 << 21), //101:  Error;
	SDC_WCRC_FLASHERR	= (7 << 21), //111:  Flash error;
	SDC_CLK_EARLY_MODE	= (1 << 31),	
}SDCON0_T;
enum{
	SDC_CMD_NC_6B		= (1 << 0), //send cmd, receive 6byte response,without check busy;
	SDC_CMD_NC_NB		= (2 << 0), //send cmd,without receive response,without check busy;
	SDC_CMD_NC_17B		= (3 << 0), //send cmd, receive 17byte response,without check busy;
	SDC_CMD_WC_NB		= (4 << 0), //send cmd,without receive response,with check busy;
	SDC_CMD_WC_6B		= (5 << 0), //send cmd, receive 6byte response,with check busy;
	SDC_CMD_WC_17B		= (7 << 0), //send cmd, receive 17byte response,with check busy;
	SDC_DAT_RECIEVE		= (1 << 4), //Receive data
	SDC_DAT_SEND_NC		= (2 << 4), //Send data,without check busy;
	SDC_DAT_SEND_WC		= (3 << 4), //Send data,with check busy;
	SDC_DAT_SFR			= (1 << 6), //0: data src is dma, 1:data src is sfr
	SDIO_IE_CLR			= (1 << 7), //clr sdio interrupt enable
	SDIO_IEPND_CLR		= (1 << 14),//clr sdio interrupt pending;
	SDC_DEPND_CLR		= (1 << 15), //clr data interrupt pending;
	SDC_CEPND_CLR		= (1 << 17), // clr cmd interrupt pending;
}SDCON1_T;

/*******************************************************************************
* Function Name  : hx330x_sd0Init
* Description    : initial sd0 
* Input          : u32 width
				   u32 io_pos
				   u8* buffer_512B
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd0Init(u32 width, u32 io_pos, u8* buffer_512B);
/*******************************************************************************
* Function Name  : hx330x_sd0Uninit
* Description    :uninitial sd0 
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd0Uninit(void);
/*******************************************************************************
* Function Name  : hx330x_sd0SendCmd
* Description    : send command  
* Input          : u8 byCMDIndex : command index
				   u8 byRsp: response type
				   u32 dwArgument: command argument
* Output         : None
* Return         : bool : false : fail
						  true  : success
*******************************************************************************/
bool hx330x_sd0SendCmd(u8 byCMDIndex,u8 byRsp, u32 dwArgument);
/*******************************************************************************
* Function Name  : hx330x_sd0BusSet
* Description    : set sd0 bus width 
* Input          : u8 type : SD_BUS_WIDTH4: 4-bit,SD_BUS_WIDTH1-1bit
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd0BusSet(u8 type);
/*******************************************************************************
* Function Name  : hx330x_sd0Recv
* Description    : sd0 set recv data
* Input          : u32 addr : buffer address
				   u32 len   : length
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd0Recv(u32 addr,u32 len);
/*******************************************************************************
* Function Name  : hx330x_sd0Send
* Description    : sd0 set send data
* Input          : u32 addr : buffer address
				   u32 len   : length
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd0Send(u32 addr,u32 len);
/*******************************************************************************
* Function Name  : hx330x_sd0WaitDAT0
* Description    : sd0 wait data 0 high
* Input          : 
* Output         : None
* Return         : bool : false : fail
						  true  : success
*******************************************************************************/
bool hx330x_sd0WaitDAT0(void);
/*******************************************************************************
* Function Name  : hx330x_sd0WaitPend
* Description    : sd0 wait pending flag
* Input          : 
* Output         : None
* Return         : bool : false : fail
						  true  : success
*******************************************************************************/
bool hx330x_sd0WaitPend(void);
/*******************************************************************************
* Function Name  : hx330x_sd0GetRsp
* Description    : sd0 get rsp resoult
* Input          : 
* Output         : None
* Return         : int rsp resoult
*******************************************************************************/
u32 hx330x_sd0GetRsp(void);
/*******************************************************************************
* Function Name  : hx330x_sd0CRCCheck
* Description    : sd crc check
* Input          : u8 mode : 1-write,0-read
* Output         : None
* Return         : int rsp resoult
*******************************************************************************/
bool hx330x_sd0CRCCheck(u8 mode);
/*******************************************************************************
* Function Name  : hx330x_sd0Buffer
* Description    : get sd0 static buffer
* Input          : 
* Output         : None
* Return         : 
*******************************************************************************/
u8 *hx330x_sd0Buffer(void);

/*******************************************************************************
* Function Name  : hx330x_sd0ClkSet
* Description    : set sd0 clock
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd0ClkSet(u32 clk, u32 highspeed);
/*******************************************************************************
* Function Name  : hx330x_sd0Init
* Description    : initial sd0 
* Input          : u32 width
				   u32 io_pos
				   u8* buffer_512B
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd1Init(u32 width, u32 io_pos, u8* buffer_512B);
/*******************************************************************************
* Function Name  : hx330x_sd1Uninit
* Description    :uninitial sd0 
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd1Uninit(void);
/*******************************************************************************
* Function Name  : hx330x_sd1SendCmd
* Description    : send command  
* Input          : u8 byCMDIndex : command index
				   u8 byRsp: response type
				   u32 dwArgument: command argument
* Output         : None
* Return         : bool : false : fail
						  true  : success
*******************************************************************************/
bool hx330x_sd1SendCmd(u8 byCMDIndex,u8 byRsp, u32 dwArgument);
/*******************************************************************************
* Function Name  : hx330x_sd1BusSet
* Description    : set sd0 bus width 
* Input          : u8 type : SD_BUS_WIDTH4: 4-bit,SD_BUS_WIDTH1-1bit
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd1BusSet(u8 type);
/*******************************************************************************
* Function Name  : hx330x_sd1Recv
* Description    : sd0 set recv data
* Input          : u32 addr : buffer address
				   u32 len   : length
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd1Recv(u32 addr,u32 len);
/*******************************************************************************
* Function Name  : hx330x_sd1Send
* Description    : sd0 set send data
* Input          : u32 addr : buffer address
				   u32 len   : length
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd1Send(u32 addr,u32 len);
/*******************************************************************************
* Function Name  : hx330x_sd1WaitDAT0
* Description    : sd0 wait data 0 high
* Input          : 
* Output         : None
* Return         : bool : false : fail
						  true  : success
*******************************************************************************/
bool hx330x_sd1WaitDAT0(void);
/*******************************************************************************
* Function Name  : hx330x_sd1WaitPend
* Description    : sd0 wait pending flag
* Input          : 
* Output         : None
* Return         : bool : false : fail
						  true  : success
*******************************************************************************/
bool hx330x_sd1WaitPend(void);
/*******************************************************************************
* Function Name  : hx330x_sd1GetRsp
* Description    : sd0 get rsp resoult
* Input          : 
* Output         : None
* Return         : int rsp resoult
*******************************************************************************/
u32 hx330x_sd1GetRsp(void);
/*******************************************************************************
* Function Name  : hx330x_sd1CRCCheck
* Description    : sd crc check
* Input          : u8 mode : 1-write,0-read
* Output         : None
* Return         : int rsp resoult
*******************************************************************************/
bool hx330x_sd1CRCCheck(u8 mode);
/*******************************************************************************
* Function Name  : hx330x_sd1Buffer
* Description    : get sd0 static buffer
* Input          : 
* Output         : None
* Return         : 
*******************************************************************************/
u8 *hx330x_sd1Buffer(void);
/*******************************************************************************
* Function Name  : hx330x_sd1ClkSet
* Description    : set sd0 clock
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_sd1ClkSet(u32 clk, u32 highspeed);
















#endif
