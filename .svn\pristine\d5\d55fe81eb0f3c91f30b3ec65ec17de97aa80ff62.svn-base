/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_LCD_WIN_H
	#define HX330X_LCD_WIN_H

enum win_src_m {
    WIN_SRC_B = 0,
    WIN_SRC_A = 1,
};

/*******************************************************************************
* Function Name  : hx330x_lcdWinABConfig
* Description    : lcd winAB config
* Input          : u8 top_src: top layer source: WIN_SRC_B, WIN_SRC_A,
*                  u16 x : top layer strat-x relative to bottom layer
*                  u16 y : top layer strat-y relative to bottom layer
*                  u16 Bwidth : when top layer is winB
*                  u16 Bheight: when top layer is winB
*                  bool enable: winAB enable
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_lcdWinABConfig(u8 top_src,u16 x,u16 y,u16 Bwidth,u16 Bheight,bool enable);
/*******************************************************************************
* Function Name  : hx330x_lcdWinABEnable
* Description    : lcd WINAB enable
* Input          : bool enable: WINAB enable
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_lcdWinABEnable(bool enable);
/*******************************************************************************
* Function Name  : hx330x_lcdWinReset
* Description    : reset internal counter of WINAB
* Input          : u8 src
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_lcdWinReset(u8 src);
/*******************************************************************************
* Function Name  : hx330x_lcdWinGetTopLayer
* Description    : get source of lcd win top-layer
* Input          : none
* Output         : none
* Return         : 0:WIN_SRC_B;1:WIN_SRC_A
*******************************************************************************/
u8 hx330x_lcdWinGetTopLayer(void);
/*******************************************************************************
* Function Name  : hx330x_lcdWinGetBotLayer
* Description    : get source of lcd win top-layer
* Input          : none
* Output         : none
* Return         : 0:jpeg;1:isp
*******************************************************************************/
u8 hx330x_lcdWinGetBotLayer(void);
#endif
