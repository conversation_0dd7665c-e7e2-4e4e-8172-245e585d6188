/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../../hal/inc/hal.h"

#if HAL_CFG_PQTOOL_EN

SENSOR_API_T* get_sensor_api(void);

ALIGNED(4) static u8 pqbuf[256 + 16];

static void pqtool_reset(void)
{
	hal_sysReset();
}

static void pqtool_iic_write(void)
{
	sensor_iic_write(pqbuf);
}

static void pqtool_iic_read(void)
{
	u32* rslt = (u32 *)&pqbuf[16];
	*rslt = sensor_iic_read(pqbuf);
}

static void pqtool_spiflash_read_id(void)
{
	u32* rslt = (u32 *)&pqbuf[16];
	*rslt = hal_spiFlashReadID();
}

static void pqtool_spiflash_write(void)
{
	u32* arg0 = (u32 *)pqbuf;
	u32* arg1 = (u32 *)(pqbuf + 4);
	u32* arg2 = (u32 *)(pqbuf + 8);
	//				  flash, ram, size
	hal_spiFlashWrite(*arg0, *arg1, *arg2, 1);
}

static void pqtool_spiflash_read(void)
{
	u32* arg0 = (u32 *)pqbuf;
	u32* arg1 = (u32 *)(pqbuf + 4);
	u32* arg2 = (u32 *)(pqbuf + 8);
	//				  flash, ram, size
	hal_spiFlashRead(*arg1, *arg0, *arg2);
}

static void pqtool_spiflash_erase(void)
{
	u32* arg0 = (u32 *)pqbuf;
	hal_spiFlashEraseSector(*arg0, 1);
}

static void pqtool_halfword_memcpy(void)
{
	u32* arg0 = (u32 *)pqbuf;
	u32* arg1 = (u32 *)(pqbuf + 4);
	u32* arg2 = (u32 *)(pqbuf + 8);
	hx330x_halfword_memcpy((u16*)(*arg0), (u16*)(*arg1), *arg2);
}

static void pqtool_word_memcpy(void)
{
	u32* arg0 = (u32 *)pqbuf;
	u32* arg1 = (u32 *)(pqbuf + 4);
	u32* arg2 = (u32 *)(pqbuf + 8);
	hx330x_word_memcpy((u32*)(*arg0), (u32*)(*arg1), *arg2);
}

static void pqtool_mem2sfr(void)
{
	u32* arg0 = (u32 *)pqbuf;
	u32* arg1 = (u32 *)(pqbuf + 4);
	u32* arg2 = (u32 *)(pqbuf + 8);

	u32 dst = (*arg0);
	u32* src = (u32*)(*arg1);
	u32 len = *arg2 / 4;
	hx330x_wdtClear();
	while (len--)
	{
        u32 value = *src;
        asm("l.mtspr\t\t%0,%1,0": : "r" ((unsigned long)dst), "r" ((unsigned long)value));
		// SFR_AT dst = *src;
		src++;
		dst += 4;
	}
}

static void pqtool_sfr2mem(void)
{
	u32* arg0 = (u32 *)pqbuf;
	u32* arg1 = (u32 *)(pqbuf + 4);
	u32* arg2 = (u32 *)(pqbuf + 8);

	u32* dst = (u32*)(*arg0);
	u32 src = *arg1;
	u32 len = *arg2 / 4;
	hx330x_wdtClear();
	while (len--)
	{
		*dst++ = SFR_AT src;
		src += 4;
	}
}

static void pqtool_get_lcd_info(void)
{
#ifdef LCD_TAG_SELECT
#if LCD_TAG_SELECT != LCD_NONE
	hal_lcdPQToolGetInfo((u32 *)&pqbuf[16], 256);
#endif
#endif
}

static void pqtool_start_save_raw(void)
{
	int* rslt = (int *)&pqbuf[16];
	*rslt = hal_csi_save_raw_start((char *)&pqbuf[16]);
}

static void pqtool_get_save_raw_state(void)
{
	int* rslt = (int *)&pqbuf[16];
	*rslt = hal_csi_save_raw_state();
}

/*******************************************************************************
* Function Name  : pqtool_get_sdk_info
* Description    :
* Input          : none
* Output         : none
* Return         : info pointer
*******************************************************************************/
u8* pqtool_get_sdk_info(void)
{
	// 64 words
	INT32U * buffer = (INT32U *)pqbuf;
	buffer[0] = 0x5A000000 | (18 * 4);
	buffer[1] = SEN_RES_FLASH_ADDR(0);
	buffer[2] = (INT32U)pqbuf;
	buffer[3] = (INT32U)get_sensor_api();
	buffer[4] = (INT32U)pqtool_halfword_memcpy;
	buffer[5] = (INT32U)pqtool_word_memcpy;
	buffer[6] = (INT32U)pqtool_mem2sfr;
	buffer[7] = (INT32U)pqtool_sfr2mem;
	buffer[8] = (INT32U)pqtool_reset;
	buffer[9] = (INT32U)pqtool_iic_write;
	buffer[10] = (INT32U)pqtool_iic_read;
	buffer[11] = (INT32U)pqtool_spiflash_write;
	buffer[12] = (INT32U)pqtool_spiflash_read;
	buffer[13] = (INT32U)pqtool_spiflash_erase;
    buffer[14] = (INT32U)pqtool_spiflash_read_id;
    buffer[15] = (INT32U)pqtool_get_lcd_info;
    buffer[16] = (INT32U)pqtool_start_save_raw;
    buffer[17] = (INT32U)pqtool_get_save_raw_state;

	return pqbuf;
}
#endif
