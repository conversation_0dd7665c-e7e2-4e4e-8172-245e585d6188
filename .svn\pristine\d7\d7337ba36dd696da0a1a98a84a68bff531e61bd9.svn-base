<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="hx330x_sdk" />
		<Option pch_mode="2" />
		<Option compiler="hx330x-gcc" />
		<Build>
			<Target title="Debug">
				<Option output="bin/Debug/hx330x_sdk" prefix_auto="1" extension_auto="1" />
				<Option object_output="obj/Debug/" />
				<Option type="1" />
				<Option compiler="hx330x-gcc" />
				<Compiler>
					<Add option="-Os" />
				</Compiler>
				<Linker>
					<Add option="-T../mcu/boot/hx330x.ld" />
					<Add option='-Map=&quot;$(TARGET_OUTPUT_DIR)$(PROJECT_NAME).map&quot;' />
					<Add library="../lib/libboot.a" />
					<Add library="../lib/libmcu.a" />
					<Add library="../lib/libisp.a" />
					<Add library="../lib/libjpg.a" />
					<Add library="../lib/liblcd.a" />
					<Add library="../lib/libmultimedia.a" />
					<Add library="c" />
					<Add library="gcc" />
					<Add directory="../mcu/boot" />
				</Linker>
				<ExtraCommands>
					<Add after="script\build_bin.bat $(TARGET_OUTPUT_FILE)" />
					<Mode after="always" />
				</ExtraCommands>
			</Target>
		</Build>
		<Compiler>
			<Add option="-Wall" />
		</Compiler>
		<Unit filename="../dev/battery/inc/battery_api.h" />
		<Unit filename="../dev/battery/src/battery_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/dev_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/dev_api.h" />
		<Unit filename="../dev/fs/inc/diskio.h" />
		<Unit filename="../dev/fs/inc/ff.h" />
		<Unit filename="../dev/fs/inc/ffconf.h" />
		<Unit filename="../dev/fs/inc/fs_api.h" />
		<Unit filename="../dev/fs/inc/fs_typedef.h" />
		<Unit filename="../dev/fs/src/diskio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/fs/src/ff.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/fs/src/ffunicode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/fs/src/fs_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/gsensor/inc/gsensor_api.h" />
		<Unit filename="../dev/gsensor/src/gsensor_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/gsensor/src/gsensor_da380.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/gsensor/src/gsensor_gma301.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/gsensor/src/gsensor_sc7a30e.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/ir/inc/ir_api.h" />
		<Unit filename="../dev/ir/src/ir_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/key/inc/key_api.h" />
		<Unit filename="../dev/key/src/key_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/inc/lcd_api.h" />
		<Unit filename="../dev/lcd/inc/lcd_typedef.h" />
		<Unit filename="../dev/lcd/src/lcd_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_NT35510HSD.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_SPFD5420.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_hx8352b.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_hx8352c.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_hx8357b.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_ili9225G.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_ili9328.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_ili9335.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_ili9486_T35-H43-86.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_jd9851.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_lgdp4532.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_r61509v.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_st7789.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_mcu_st7789v.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_ILI9806e.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_ILI9806e_4522.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_JLT28060B.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_ili8961.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_ili9342c.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_ota5182.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_otm8019a.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_rm68172.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_st7282.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_st7701FW1601.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_st7701FW1604.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_st7701sLX45FWI4006.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_rgb_st7701s_LX50FWH40149.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/lcd/src/lcd_spi_ili9341.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/led/inc/led_api.h" />
		<Unit filename="../dev/led/src/led_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/led_pwm/inc/led_pwm_api.h" />
		<Unit filename="../dev/led_pwm/src/led_pwm_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/nvfs/inc/nvfs_api.h" />
		<Unit filename="../dev/nvfs/inc/nvfs_jpg.h" />
		<Unit filename="../dev/nvfs/src/nvfs_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/nvfs/src/nvfs_jpg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sd/inc/sd_api.h" />
		<Unit filename="../dev/sd/inc/sd_typedef.h" />
		<Unit filename="../dev/sd/src/sd_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/inc/sensor_api.h" />
		<Unit filename="../dev/sensor/inc/sensor_typedef.h" />
		<Unit filename="../dev/sensor/src/sensor_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_BF3016.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_FPX1002.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_GC1004.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_GC1034.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_GC1054.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_GC1064.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_H42.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_H62.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_H65.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_H7640.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_NT99141.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_OV9710.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_OV9732.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_SC1045.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_SC1243.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_SC1345.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_SP1409.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_720P_SP140A.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_BF2013.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_BF3703.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_BF3a03.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_GC0307.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_GC0308.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_GC0309.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_GC0328.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_HM1055.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_IT03A1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_NT99142.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_OV7670.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_OV7725.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_OV7736.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_SIV100B.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_SIV120B.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_dvp_VGA_SIV121DS.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_720P_GC1054.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_720P_GC1084.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_mipi_720P_OV9714.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/sensor/src/sensor_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/touchpanel/inc/touchpanel_api.h" />
		<Unit filename="../dev/touchpanel/inc/touchpanel_iic.h" />
		<Unit filename="../dev/touchpanel/src/touchpanel_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/touchpanel/src/touchpanel_icnt81.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/touchpanel/src/touchpanel_iic.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/touchpanel/src/touchpanel_ns2009.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/inc/dusb_api.h" />
		<Unit filename="../dev/usb/dusb/inc/dusb_enum.h" />
		<Unit filename="../dev/usb/dusb/inc/dusb_msc.h" />
		<Unit filename="../dev/usb/dusb/inc/dusb_tool_api.h" />
		<Unit filename="../dev/usb/dusb/inc/dusb_uac.h" />
		<Unit filename="../dev/usb/dusb/inc/dusb_uvc.h" />
		<Unit filename="../dev/usb/dusb/src/dusb_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/src/dusb_enum.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/src/dusb_msc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/src/dusb_tool_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/src/dusb_uac.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/dusb/src/dusb_uvc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/husb/inc/husb_api.h" />
		<Unit filename="../dev/usb/husb/inc/husb_enum.h" />
		<Unit filename="../dev/usb/husb/inc/husb_tpbulk.h" />
		<Unit filename="../dev/usb/husb/inc/husb_usensor.h" />
		<Unit filename="../dev/usb/husb/inc/husb_uvc.h" />
		<Unit filename="../dev/usb/husb/src/husb_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/husb/src/husb_enum.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/husb/src/husb_hub.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/husb/src/husb_tpbulk.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/husb/src/husb_usensor.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../dev/usb/husb/src/husb_uvc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/inc/hal.h" />
		<Unit filename="../hal/inc/hal_adc.h" />
		<Unit filename="../hal/inc/hal_auadc.h" />
		<Unit filename="../hal/inc/hal_cfg.h" />
		<Unit filename="../hal/inc/hal_csi.h" />
		<Unit filename="../hal/inc/hal_dac.h" />
		<Unit filename="../hal/inc/hal_eeprom.h" />
		<Unit filename="../hal/inc/hal_gpio.h" />
		<Unit filename="../hal/inc/hal_iic.h" />
		<Unit filename="../hal/inc/hal_int.h" />
		<Unit filename="../hal/inc/hal_isp.h" />
		<Unit filename="../hal/inc/hal_lcd.h" />
		<Unit filename="../hal/inc/hal_lcdframe.h" />
		<Unit filename="../hal/inc/hal_md.h" />
		<Unit filename="../hal/inc/hal_mjpDecode.h" />
		<Unit filename="../hal/inc/hal_mjpEncode.h" />
		<Unit filename="../hal/inc/hal_osd.h" />
		<Unit filename="../hal/inc/hal_osdcmp.h" />
		<Unit filename="../hal/inc/hal_pmu.h" />
		<Unit filename="../hal/inc/hal_rotate.h" />
		<Unit filename="../hal/inc/hal_rtc.h" />
		<Unit filename="../hal/inc/hal_spi.h" />
		<Unit filename="../hal/inc/hal_stream.h" />
		<Unit filename="../hal/inc/hal_sys.h" />
		<Unit filename="../hal/inc/hal_timer.h" />
		<Unit filename="../hal/inc/hal_uart.h" />
		<Unit filename="../hal/inc/hal_watermark.h" />
		<Unit filename="../hal/inc/hal_wdt.h" />
		<Unit filename="../hal/src/hal_adc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_auadc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_csi.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_dac.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_dmauart.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_eeprom.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_gpio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_iic.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_int.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_lcdshow.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_md.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_mjpAEncode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_mjpBEncode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_mjpDecode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_rtc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_spi.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_spi1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_stream.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_sys.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_timer.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_uart.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_watermark.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../hal/src/hal_wdt.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../mcu/boot/hx330x.ld" />
		<Unit filename="../mcu/boot/spi_boot_cfg.S" />
		<Unit filename="../mcu/boot/spi_boot_cfg.h" />
		<Unit filename="../mcu/inc/hx330x.h" />
		<Unit filename="../mcu/inc/hx330x_adc.h" />
		<Unit filename="../mcu/inc/hx330x_auadc.h" />
		<Unit filename="../mcu/inc/hx330x_cfg.h" />
		<Unit filename="../mcu/inc/hx330x_csi.h" />
		<Unit filename="../mcu/inc/hx330x_dac.h" />
		<Unit filename="../mcu/inc/hx330x_dma.h" />
		<Unit filename="../mcu/inc/hx330x_emi.h" />
		<Unit filename="../mcu/inc/hx330x_gpio.h" />
		<Unit filename="../mcu/inc/hx330x_iic.h" />
		<Unit filename="../mcu/inc/hx330x_int.h" />
		<Unit filename="../mcu/inc/hx330x_isp.h" />
		<Unit filename="../mcu/inc/hx330x_jpg.h" />
		<Unit filename="../mcu/inc/hx330x_lcd.h" />
		<Unit filename="../mcu/inc/hx330x_md.h" />
		<Unit filename="../mcu/inc/hx330x_mipi.h" />
		<Unit filename="../mcu/inc/hx330x_misc.h" />
		<Unit filename="../mcu/inc/hx330x_osd.h" />
		<Unit filename="../mcu/inc/hx330x_osdcmp.h" />
		<Unit filename="../mcu/inc/hx330x_pip.h" />
		<Unit filename="../mcu/inc/hx330x_pmu.h" />
		<Unit filename="../mcu/inc/hx330x_rotate.h" />
		<Unit filename="../mcu/inc/hx330x_rtc.h" />
		<Unit filename="../mcu/inc/hx330x_sd.h" />
		<Unit filename="../mcu/inc/hx330x_spi.h" />
		<Unit filename="../mcu/inc/hx330x_spr_defs.h" />
		<Unit filename="../mcu/inc/hx330x_sys.h" />
		<Unit filename="../mcu/inc/hx330x_timer.h" />
		<Unit filename="../mcu/inc/hx330x_tminf.h" />
		<Unit filename="../mcu/inc/hx330x_uart.h" />
		<Unit filename="../mcu/inc/hx330x_usb.h" />
		<Unit filename="../mcu/inc/hx330x_wdt.h" />
		<Unit filename="../mcu/inc/typedef.h" />
		<Unit filename="../mcu/xos/xcfg.h" />
		<Unit filename="../mcu/xos/xdef.h" />
		<Unit filename="../mcu/xos/xmbox.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../mcu/xos/xmbox.h" />
		<Unit filename="../mcu/xos/xmsgq.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../mcu/xos/xmsgq.h" />
		<Unit filename="../mcu/xos/xos.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../mcu/xos/xos.h" />
		<Unit filename="../mcu/xos/xwork.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../mcu/xos/xwork.h" />
		<Unit filename="../multimedia/Library/AVI/inc/avi_api.h" />
		<Unit filename="../multimedia/Library/AVI/inc/avi_typedef.h" />
		<Unit filename="../multimedia/Library/JPG/inc/jpg_api.h" />
		<Unit filename="../multimedia/Library/JPG/inc/jpg_typedef.h" />
		<Unit filename="../multimedia/Library/JPG/src/jpg_enc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/Library/WAV/inc/wav_api.h" />
		<Unit filename="../multimedia/Library/WAV/inc/wav_typedef.h" />
		<Unit filename="../multimedia/Library/api_multimedia.h" />
		<Unit filename="../multimedia/audio/audio_playback.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/audio/audio_playback.h" />
		<Unit filename="../multimedia/audio/audio_record.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/audio/audio_record.h" />
		<Unit filename="../multimedia/image/image_decode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/image/image_decode.h" />
		<Unit filename="../multimedia/image/image_encode.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/image/image_encode.h" />
		<Unit filename="../multimedia/multimedia_api.h" />
		<Unit filename="../multimedia/video/video_playback.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/video/video_playback.h" />
		<Unit filename="../multimedia/video/video_record.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../multimedia/video/video_record.h" />
		<Unit filename="../sys_manage/filelist_manage/inc/file_list_api.h" />
		<Unit filename="../sys_manage/filelist_manage/inc/file_list_typedef.h" />
		<Unit filename="../sys_manage/filelist_manage/src/file_list_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/filelist_manage/src/file_list_manage.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/inc/res_ascii_api.h" />
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_num1_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_num2_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_num3_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_ascii/src/ascii_num4_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_font/inc/res_font_api.h" />
		<Unit filename="../sys_manage/res_manage/res_font/src/res_font_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_icon/inc/res_icon_api.h" />
		<Unit filename="../sys_manage/res_manage/res_icon/src/res_icon_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_image/inc/res_image_api.h" />
		<Unit filename="../sys_manage/res_manage/res_image/src/res_image_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_manage_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_manage_api.h" />
		<Unit filename="../sys_manage/res_manage/res_music/inc/res_music_api.h" />
		<Unit filename="../sys_manage/res_manage/res_music/src/res_music_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/res_manage/res_music/src/res_music_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/sys_manage_api.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinApi.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinButton.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinDialog.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinDraw.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinFrame.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinImageIcon.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinItemManage.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinItemMenu.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinItemMenuEx.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinItemMenuOption.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinLine.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinManage.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinMemManage.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinProgressBar.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinRect.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinStringEx.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinStringIcon.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinTips.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinWidget.h" />
		<Unit filename="../sys_manage/ui_manage/inc/uiWinWidgetManage.h" />
		<Unit filename="../sys_manage/ui_manage/src/uiWinButton.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinCycle.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinDialog.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinDraw.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinFrame.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinImageIcon.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinItemManage.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinItemMenu.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinItemMenuEx.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinItemMenuOption.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinLine.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinManage.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinMemManage.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinProgressBar.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinRect.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinStringEx.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinStringIcon.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinTips.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinWidget.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="../sys_manage/ui_manage/src/uiWinWidgetManage.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app_common/inc/app_api.h" />
		<Unit filename="app_common/inc/app_typedef.h" />
		<Unit filename="app_common/src/app_init.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app_common/src/app_lcdshow.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app_common/src/main.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="resource/user_res.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="resource/user_res.h" />
		<Unit filename="task_windows/menu_windows/inc/menu_api.h" />
		<Unit filename="task_windows/menu_windows/inc/menu_typedef.h" />
		<Unit filename="task_windows/menu_windows/src/mMenuPlayMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/mMenuPlayWin.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/mMenuRecordMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/mMenuRecordWin.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDateTimeMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDateTimeWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDefaultMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDefaultWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDelAllMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDelAllWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDelCurMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuDelCurWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuFormatMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuFormatWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuItemMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuItemWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuLockCurMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuLockCurWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuOptionMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuOptionWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuUnlockAllMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuUnlockAllWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuUnlockCurMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuUnlockCurWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuVersionMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sMenuVersionWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowAsternMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowAsternWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowNoFileMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowNoFileWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowSelfTestMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowSelfTestWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowTips1Msg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowTips1Win.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowTipsMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowTipsWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowTpIconMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/menu_windows/src/sWindowTpIconWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/msg_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/msg_api.h" />
		<Unit filename="task_windows/task_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_api.h" />
		<Unit filename="task_windows/task_common/inc/task_common.h" />
		<Unit filename="task_windows/task_common/src/sys_common_msg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_common/src/task_common.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_common/src/task_common_msg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_audio/inc/taskPlayAudio.h" />
		<Unit filename="task_windows/task_play_audio/src/taskPlayAudio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_audio/src/taskPlayAudioMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_audio/src/taskPlayAudioWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_play_video/inc/taskPlayVideo.h" />
		<Unit filename="task_windows/task_play_video/src/taskPlayVideo.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoMainMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoMainWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoSlideMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoSlideWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoThumbnallMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_play_video/src/taskPlayVideoThumbnallWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_poweroff/inc/taskPoweroff.h" />
		<Unit filename="task_windows/task_poweroff/src/taskPowerOff.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_audio/inc/taskRecordAudio.h" />
		<Unit filename="task_windows/task_record_audio/src/taskRecordAudio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_audio/src/taskRecordAudioMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_audio/src/taskRecordAudioWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_record_photo/inc/taskRecordPhoto.h" />
		<Unit filename="task_windows/task_record_photo/src/taskRecordPhoto.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_photo/src/taskRecordPhotoMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_photo/src/taskRecordPhotoWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_record_video/inc/taskRecordVideo.h" />
		<Unit filename="task_windows/task_record_video/src/taskRecordVideo.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_video/src/taskRecordVideoMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_record_video/src/taskRecordVideoWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_sd_update/inc/taskSdUpdate.h" />
		<Unit filename="task_windows/task_sd_update/src/taskSdUpdate.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_sd_update/src/taskSdUpdateWin.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_show_logo/inc/taskShowLogo.h" />
		<Unit filename="task_windows/task_show_logo/src/taskShowLogo.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_show_logo/src/taskShowLogoMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_show_logo/src/taskShowLogoWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/task_usb_device/inc/taskUsbDevice.h" />
		<Unit filename="task_windows/task_usb_device/src/taskUsbDevice.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_usb_device/src/taskUsbDeviceMsg.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/task_usb_device/src/taskUsbDeviceWin.c">
			<Option compilerVar="CC" />
			<Option compile="0" />
			<Option link="0" />
		</Unit>
		<Unit filename="task_windows/windows_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="task_windows/windows_api.h" />
		<Unit filename="user_config/inc/mbedtls_md5.h" />
		<Unit filename="user_config/inc/user_config_api.h" />
		<Unit filename="user_config/inc/user_config_typedef.h" />
		<Unit filename="user_config/inc/user_hardware_cfg_fgpa.h" />
		<Unit filename="user_config/inc/user_hardware_cfg_hx3303_demo.h" />
		<Unit filename="user_config/inc/user_hardware_cfg_hx3308_demo.h" />
		<Unit filename="user_config/src/mbedtls_md5.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="user_config/src/user_config_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="user_config/src/user_config_tab.c">
			<Option compilerVar="CC" />
		</Unit>
		<Extensions>
			<lib_finder disable_auto="1" />
		</Extensions>
	</Project>
</CodeBlocks_project_file>
