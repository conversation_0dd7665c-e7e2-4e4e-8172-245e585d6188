/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  SD_API_H
    #define  SD_API_H

#include "sd_typedef.h"
#if (CURRENT_CHIP == FPGA)	
#define SD_HS_CLK				36000000L
#else
#if APB_CLK > 96000000L
#define SD_HS_CLK				48000000L
#else
#define SD_HS_CLK				36000000L
#endif

#endif
/*******************************************************************************
* Function Name  : sd_api_init
* Description    : sd_api_init
* Input          : none
* Output         : None
* Return         : 
*******************************************************************************/	
int sd_api_init(u8 bus_width);

/*******************************************************************************
* Function Name  : sd_api_Stop
* Description    : dev layer. sdc stop tranfer
* Input          : none
* Output         : None
* Return         : bool true: ok

*******************************************************************************/
bool sd_api_Stop(void);
/*******************************************************************************
* Function Name  : sd_api_getNextLBA
* Description    : dev layer: sdc next LBA get
* Input          : none
* Output         : None
* Return         : u32
*******************************************************************************/
u32 sd_api_getNextLBA(void);

/*******************************************************************************
* Function Name  : sd_api_Uninit
* Description    : dev layer. uninitial sd
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void sd_api_Uninit(void);

/*******************************************************************************
* Function Name  : sd_api_lock
* Description    : dev layer.lock sdc state
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void sd_api_lock(void);

/*******************************************************************************
* Function Name  : sd_api_unlock
* Description    : dev layer. unlock sdc state
* Input          : none
* Output         : None
* Return         :none
*******************************************************************************/
void sd_api_unlock(void);

/*******************************************************************************
* Function Name  : void sd_api_CardState_Set(u8 state)
* Description    : dev layer. set sdc state
* Input          : state
* Output         : none
* Return         : none
*******************************************************************************/
void sd_api_CardState_Set(u8 state);

/*******************************************************************************
* Function Name  : u8 sd_api_CardState_Get(void)
* Description    : dev layer. get sdc state
* Input          : state
* Output         : none
* Return         : none
*******************************************************************************/
u8 sd_api_CardState_Get(void);

/*******************************************************************************
* Function Name  : sd_api_GetBusWidth()
* Description    : dev layer. get sdc bus width
* Input          : none
* Output         : none
* Return         : sd bus width
*******************************************************************************/
u8 sd_api_GetBusWidth(void);

/*******************************************************************************
* Function Name  : sd_api_Capacity
* Description    : dev layer. get sdc capacity
* Input          : none
* Output         : None
* Return         : sd capacity
*******************************************************************************/
u32 sd_api_Capacity(void);

/*******************************************************************************
* Function Name  : sd_api_Exist
* Description    : dev layer.sd check exist
* Input          : none
* Output         : None
* Return         : bool true: sd exist, false: sd not exist
*******************************************************************************/
bool sd_api_Exist(void);

/*******************************************************************************
* Function Name  : sd_api_Write
* Description    : dev layer.sd write
* Input          : void *pDataBuf : data buffer
				   u32 dwLBA : block index
				   u32 dwLBANum : block num
* Output         : None
* Return         : int 1 : ok
                           0 : fail
*******************************************************************************/
s32 sd_api_Write(void *pDataBuf, u32 dwLBA, u32 dwLBANum);

/*******************************************************************************
* Function Name  : sd_api_speed_debg
* Description    : dev layer. sd write speed debg
* Input          : none
* Output         : None
* Return         : NONE
*******************************************************************************/
void sd_api_speed_debg(void);

/*******************************************************************************
* Function Name  : sd_api_Read
* Description    : dev layer.sd cread
* Input          : void *pDataBuf : data buffer
				   u32 dwLBA : block index
				   u32 dwLBANum : block num
* Output         : None
* Return         : int 1 : ok
                           0 : fail
*******************************************************************************/
s32 sd_api_Read(void *pDataBuf, u32 dwLBA, u32 dwLBANum);
/*******************************************************************************
* Function Name  : dev_sdc_init
* Description    : dev_sdc_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_sdc_init(void);
/*******************************************************************************
* Function Name  : board_sdc_ioctrl
* Description    : board_sdc_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_sdc_ioctrl(INT32U op,INT32U para);


#endif
