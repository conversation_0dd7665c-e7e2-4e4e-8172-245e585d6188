/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/hal.h"
#include "../../app/app_common/inc/app_api.h"


URAM_SECTION  _MJP_TIMEINFO_ 	  tminf_font;



typedef struct HAL_WATERMARK_S
{
	u8 en;
	u8 stat;

	u16 width;
	u16 height;
	u16 x;
	u16 y;
	u16 y_adj;
	u8  info_y;
	u8  info_u;
	u8  info_v;
	u8  info_reverse;
	u8  *ram;
	void (*callback)(void);
}HAL_WATERMARK_T;
typedef struct HAL_WATERMARK_OPT_S{
	s32 open_layer;
	u32 tfont;      // timeinfo font num
}HAL_WATERMARK_OPT;
ALIGNED(4) static HAL_WATERMARK_T hal_watermark_ctrl[MJPEG_TIMEINFO_MAX]=
{
	{
		.width=TIME0_W,
		.height=TIME0_H,
		.ram = tminf_font.win0,
    },
    {
		.width=TIME1_W,
		.height=TIME1_H,
		.ram = tminf_font.win1,
    },
    {
		.width=TIME2_W,
		.height=TIME2_H,
		.ram = tminf_font.win2,
    }
};
ALIGNED(4) static HAL_WATERMARK_OPT hal_watermark_op;
/*******************************************************************************
* Function Name  : hal_watermarkISR
* Description    : hal watermark isr
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_watermarkCallBack(void)
{
	int i;

	for(i=0;i<MJPEG_TIMEINFO_MAX;i++)
	{
		if(hal_watermark_ctrl[i].stat && hal_watermark_ctrl[i].en && hal_watermark_ctrl[i].callback)
			hal_watermark_ctrl[i].callback();
	}
}
/*******************************************************************************
* Function Name  : hal_watermarkInit
* Description    : hal watermark init
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_watermarkInit(void)
{
	int i;
	for(i=0;i<MJPEG_TIMEINFO_MAX;i++)
	{
		hal_watermark_ctrl[i].stat 		= 0;
		hal_watermark_ctrl[i].en 		= 0;
		hal_watermark_ctrl[i].callback 	= NULL;
		hal_watermark_ctrl[i].info_y    = TIME_INF_Y;
		hal_watermark_ctrl[i].info_u    = TIME_INF_U;
		hal_watermark_ctrl[i].info_v    = TIME_INF_V;
		hx330x_mjpA_TimeinfoEnable(i,0);
		hx330x_mjpB_TimeinfoEnable(i,0);
	}
	hal_watermark_op.open_layer = -1;
	hal_watermark_op.tfont		= 0;
	hal_rtcCallBackRegister(hal_watermarkCallBack);
}
/*******************************************************************************
* Function Name  : hal_watermarkClose
* Description    : hal watermark close stream
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
s32 hal_watermarkClose(s32 layer)
{
	if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
	     return -1;

	if(hal_watermark_ctrl[layer].en){
		hx330x_mjpA_TimeinfoEnable(layer,0); // disable mjpeg timeinfo
		hx330x_mjpB_TimeinfoEnable(layer,0); // disable mjpeg timeinfo
	}
	hal_watermark_ctrl[layer].stat = 0;

    return 0;
}
/*******************************************************************************
* Function Name  : hal_watermarkClear
* Description    : hal watermark clear
* Input          :  s32 layer : layer
* Output         : None
* Return         : None
*******************************************************************************/
s32 hal_watermarkClear(s32 layer)
{
	int j;

	if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
	     return -1;
	for(j=0;j<(hal_watermark_ctrl[layer].width*hal_watermark_ctrl[layer].height/8);j++)
			hal_watermark_ctrl[layer].ram[j] = 0;
	return 0;
}
/*******************************************************************************
* Function Name  : hal_watermarkOpen
* Description    : hal watermark open stream
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
s32 hal_watermarkOpen(void)
{
	int i;

	for(i=0;i<MJPEG_TIMEINFO_MAX;i++)
	{
		if(hal_watermark_ctrl[i].stat==0)
		{
			if((hal_watermark_ctrl[i].width == 0)||(hal_watermark_ctrl[i].height==0))  // can not be used
		         continue;
			hal_watermark_ctrl[i].stat 	= 1;
			hal_watermark_ctrl[i].en 	= 0;
			hal_watermark_ctrl[i].callback = NULL;
            hal_watermarkClear(i);

			return i;
		}
	}

	return -1;
}

/*******************************************************************************
* Function Name  : hal_watermarkColor
* Description    : hal watermark color
* Input          : u8 layer : timeinfo layer
* Input		   : u32 y : y-byte
				u32 u : u-bye
				u32 v : v-byte
* Output         : None
* Return         : None
*******************************************************************************/
s32 hal_watermarkColor(s32 layer,u8 y,u8 u,u8 v)
{
	if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
	     return -1;
	hal_watermark_ctrl[layer].info_y    = y;
	hal_watermark_ctrl[layer].info_u    = u;
	hal_watermark_ctrl[layer].info_v    = v;

	return 0;
}
/*******************************************************************************
* Function Name  : hal_watermarkAddr
* Description    : hal watermark addr
* Input          : u8 layer : timeinfo layer
                      void *addr : addr
* Output         : None
* Return         : None
*******************************************************************************/
s32 hal_watermarkAddr(s32 layer,void *addr)
{
	if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
	     return -1;
//	hx330x_mjpA_TimeinfoAddr(layer,(u32)addr);   // can not use user ram

	return -1;
}
/*******************************************************************************
* Function Name  : hal_watermarkSize
* Description    : hal watermark size
* Input          : u8 layer : timeinfo layer
                      u32 width    : width
                      u32 height   : height
* Output         : None
* Return         : None
*******************************************************************************/
s32 hal_watermarkSize(s32 layer,u32 width,u32 height)
{
	if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
	     return -1;

	hal_watermark_ctrl[layer].width  = width;
	hal_watermark_ctrl[layer].height = height;
//    hx330x_mjpA_TimeinfoSize(layer,width,height);

    return 0;
}
/*******************************************************************************
* Function Name  : hal_watermarkPosition
* Description    : hal watermark psotion
* Input          : u8 layer : timeinfo layer
				   s32 x    : x position
				   s32 y   : y posotion
* Output         : None
* Return         : None
*******************************************************************************/
s32 hal_watermarkPosition(s32 layer,s32 x,s32 y)
{


 #if 0
	if((app_taskCurId()  == TASK_RECORD_VIDEO) && (user_config_get(CONFIG_ID_RESOLUTION)))// R_ID_STR_RES_720PCONFIG_ID_RESOLUTION

	{//uiWinSetResid(winItem(handle,ITEM_RESOLUTION_ID),RAM_ID_MAKE("VGA"))
	//user_config_get(CONFIG_ID_RESOLUTION)
		//  if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
		//  return -1;
		hal_watermark_ctrl[layer].x 	= 0;//760;
		hal_watermark_ctrl[layer].y 	= 0;//640;

	}
	else
	{
		if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
	     return -1;
	hal_watermark_ctrl[layer].x 	= x;
	hal_watermark_ctrl[layer].y 	= y;
	//hal_watermark_ctrl[layer].y_adj = y;
//	hx330x_mjpA_TimeinfoPos(layer,x,y);
    return 0;

	}
#endif

	if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
	     return -1;
	hal_watermark_ctrl[layer].x 	= x;
	hal_watermark_ctrl[layer].y 	= y;
// 	//hal_watermark_ctrl[layer].y_adj = y;
// //	hx330x_mjpA_TimeinfoPos(layer,x,y);
    return 0;
}
/*******************************************************************************
* Function Name  : hal_watermarkCallback
* Description    : hal watermark callback register
* Input          : s32 layer : layer
                      void (*callback)(void) : callback
* Output         : None
* Return         : None
*******************************************************************************/
s32 hal_watermarkCallbackRegister(s32 layer,void (*callback)(void))
{
	if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
	     return -1;
    hal_watermark_ctrl[layer].callback = callback;
	return 0;

}
/*******************************************************************************
* Function Name  : hal_watermarkRam
* Description    : hal watermark ram get
* Input          : s32 layer : layer
                      u32 width    : width
                      u32 height   : height
* Output         : None
* Return         : None
*******************************************************************************/
void  *hal_watermarkRam(s32 layer,INT16U *width,INT16U *height)
{
	if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
	     return NULL;
    if((hal_watermark_ctrl[layer].width == 0)||(hal_watermark_ctrl[layer].height==0))
		return NULL;

	if(width)
		*width = hal_watermark_ctrl[layer].width;
	if(height)
		*height = hal_watermark_ctrl[layer].height;
	return (void *)hal_watermark_ctrl[layer].ram;

}
/*******************************************************************************
* Function Name  : hal_watermarkEnable
* Description    : hal watermark enable
* Input          : u8 layer : timeinfo layer
                      u8 en : enable
* Output         : None
* Return         : None
*******************************************************************************/
s32 hal_watermarkEnable(s32 layer,u8 en)
{
	if((layer<0)  || (layer>=MJPEG_TIMEINFO_MAX))
	     return -1;
	if(en)
	{
		if((hal_watermark_ctrl[layer].width == 0)||(hal_watermark_ctrl[layer].height==0))  // this layer can not be used
		    return -1;
		hx330x_mjpA_TimeinfoPos(layer,hal_watermark_ctrl[layer].x,hal_watermark_ctrl[layer].y);
		hx330x_mjpB_TimeinfoPos(layer,hal_watermark_ctrl[layer].x,hal_watermark_ctrl[layer].y_adj);

        hx330x_mjpA_TimeinfoSize(layer,hal_watermark_ctrl[layer].width,hal_watermark_ctrl[layer].height);
		hx330x_mjpB_TimeinfoSize(layer,hal_watermark_ctrl[layer].width,hal_watermark_ctrl[layer].height);

	    hx330x_mjpA_TimeinfoAddr(layer,(u32)hal_watermark_ctrl[layer].ram);
		hx330x_mjpB_TimeinfoAddr(layer,(u32)hal_watermark_ctrl[layer].ram);
		hx330x_mjpA_TimeinfoColor(hal_watermark_ctrl[layer].info_y,hal_watermark_ctrl[layer].info_u,hal_watermark_ctrl[layer].info_v);
		hx330x_mjpB_TimeinfoColor(hal_watermark_ctrl[layer].info_y,hal_watermark_ctrl[layer].info_u,hal_watermark_ctrl[layer].info_v);  // set default color
	}
	hal_watermark_ctrl[layer].en = en;
	hx330x_mjpA_TimeinfoEnable(layer,en);
	hx330x_mjpB_TimeinfoEnable(layer,en);
	return 0;
}
/*******************************************************************************
* Function Name  : hal_watermarkBPos_Adjust
* Description    : hal watermark POS Adjust
* Input          : u8 layer : timeinfo layer
				   u8 en : enable
* Output         : None
* Return         : None
*******************************************************************************/
static INT8U *hal_WartermarkFill(char *str,INT8U *buffer,INT16U width,INT8U font)
{
    int i,j,n;
	INT8U *src,*tar,tw;
	u16 w, h;
    if((str==NULL) || (buffer == NULL))
		return NULL;
	n = 0;
	tw = (width+7)/8;	//width = 40*20/8
	tar = buffer;
    while(str[n])
    {
		src = (INT8U *)res_ascii_get((char)str[n],&w,&h,font);
		if(src != NULL)
		{
			//src = (INT8U *)res_ascii_get((char)' ',&w,&h,font);
			for(i=0;i<h;i++)
			{
				for(j=0;j<(w+7)/8;j++)
				    tar[i*tw+j] = *src++;
			}

		}

		tar+=(w+7)/8;
		n++;
		if(tar>=&buffer[width>>3])
			break;
    }
//	deg_Printf("streamsprintf %d,%d\n",n,font);
	return buffer;
}
/*******************************************************************************
* Function Name  : streamFont
* Description    :
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static INT32U hal_wm_streamFont(INT16U width,INT16U height)
{
	INT8U font;
	u16 h;
	if(width<=640)
		font = RES_FONT_NUM2;
	else if(width<=1280)
		font =  RES_FONT_NUM3;
	else if(width<=1920)
		font =  RES_FONT_NUM4;
	else
		font =  RES_FONT_NUM4;
    res_ascii_get((char)'0',NULL,&h,font);

	return ((h<<8)|font);

}
/*******************************************************************************
* Function Name  : videoRecordWatermark
* Description    : videoRecordWatermark UPDATE, VIDEO_CH_A, register to hal_watermarkCallBack
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static void hal_JPG_WatermarkCallback(void)
{
    INT8U *buffer;
	INT16U w,h;

	char string[20];
	buffer = (INT8U *)hal_watermarkRam(hal_watermark_op.open_layer,&w,&h);

    if(buffer && w && h)
    {
		DATE_TIME_T *rtcTime = hal_rtcTimeGet();
		hal_rtcTime2StringExt(string,rtcTime);
	    string[19] = 0;
	    hal_WartermarkFill(string,buffer,w,hal_watermark_op.tfont);
//		debgbuf(string,20);
    }

}
/*******************************************************************************
* Function Name  : videoRecWartermarkFill
* Description    :
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hal_jpg_watermark_init(void)
{
	if(hal_watermark_op.open_layer)
		hal_watermarkClose(hal_watermark_op.open_layer);
    hal_watermark_op.open_layer = hal_watermarkOpen();
    if(hal_watermark_op.open_layer < 0)
		deg_Printf("watermark open fail.\n");
	else
		hal_watermarkCallbackRegister(hal_watermark_op.open_layer,hal_JPG_WatermarkCallback);
	hal_watermark_op.tfont = 0;
}
/*******************************************************************************
* Function Name  : videoRecWartermarkFill
* Description    :
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hal_jpg_watermark_uinit(void)
{
	hal_watermarkClose(hal_watermark_op.open_layer);
	hal_watermark_op.open_layer = -1;
	hal_watermark_op.tfont		= 0;
}
/*******************************************************************************
* Function Name  : videoRecWartermarkFill
* Description    :
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hal_jpg_watermarkStart(INT16U width,INT16U height,INT8U en)
{
	INT32U value;
    // if(en)
    // {
	// 	value = hal_wm_streamFont(width,height);  //((h<<8)|font);
    //     if(hal_watermark_op.tfont != (value&0xff));//change font,
	// 	    hal_watermarkClear(hal_watermark_op.open_layer);

	// 	hal_watermark_op.tfont = value&0xff;
	// 	//deg_Printf("watermark pos:[%d]\n",height-(value>>8)-20);
	// 	 hal_watermarkPosition(hal_watermark_op.open_layer,16,height-(value>>8)-20);
	// 	hal_JPG_WatermarkCallback(); // update frame
	// 	hal_watermarkEnable(hal_watermark_op.open_layer,1);
    // }
	int user_x;
	int user_y;
    if(en)
    {
		value = hal_wm_streamFont(width,height);  //((h<<8)|font);
        if(hal_watermark_op.tfont != (value&0xff));//change font,
		    hal_watermarkClear(hal_watermark_op.open_layer);
			
		hal_watermark_op.tfont = value&0xff;
		//deg_Printf("watermark pos:[%d]\n",height-(value>>8)-20);
		user_y = height-(value>>8)-20;
		if(width<=640)
		{
			user_x = 320;
			user_y = height-(value>>8)-16;
		}else if(width<=1280)
		{
			user_x = 740;//830
		}else if(width<=1440)
		{
			user_x = 800;
		}else if(width<=1920)
		{
			user_x = 1250;
		}
		
		deg_Printf("width=%d, %d,watermark pos:[%d,%d]\n", width, height, user_x, user_y);
		hal_watermarkPosition(hal_watermark_op.open_layer,user_x,user_y);
		hal_JPG_WatermarkCallback(); // update frame
		hal_watermarkEnable(hal_watermark_op.open_layer,1);
    }
	else
		hal_watermarkEnable(hal_watermark_op.open_layer,0);
}

/*******************************************************************************
* Function Name  : hal_watermarkBPos_Adjust
* Description    : hal watermark POS Adjust
* Input          : u8 layer : timeinfo layer
				   u8 en : enable
* Output         : None
* Return         : None
*******************************************************************************/
void hal_jpgB_watermarkPos_Adjust(u16 height)
{
	if((hal_watermark_op.open_layer<0)  || (hal_watermark_op.open_layer>=MJPEG_TIMEINFO_MAX))
	     return;
	hal_watermark_ctrl[hal_watermark_op.open_layer].y_adj = height - hal_watermark_ctrl[hal_watermark_op.open_layer].height;
	hx330x_mjpB_TimeinfoPos(hal_watermark_op.open_layer,hal_watermark_ctrl[hal_watermark_op.open_layer].x,hal_watermark_ctrl[hal_watermark_op.open_layer].y_adj);
}









