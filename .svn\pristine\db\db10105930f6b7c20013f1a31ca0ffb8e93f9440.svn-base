/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  GSENSOR_API_H
    #define   GSENSOR_API_H

#define G_SENSOR_GMA301_SUPPORT		(1)
#define G_SENSOR_SC7A30E_SUPPORT	(1)
#define G_SENSOR_DA380_SUPPORT		(1)

typedef enum{
	G_IOCTRL_ENABLE = 0,	// enable gsensor
	G_IOCTRL_DISABLE, 		// disable gsensor
	G_IOCTRL_WAKEUPSET,		// wakeup set
	G_IOCTRL_WAKEUPGET,		// wakeup flag get
	G_<PERSON>OCTRL_ACTIVESET,		// active for lock. active level set
	G_IOCTRL_ACTIVEGET,		// active for lock.active level set
	G_IOCTRL_ACTIVECHK,
}GSENSOR_IOCTRL_OP;

typedef enum 
{
	G_LEVEL_OFF	=	0,
	G_LEVEL_LOW,
	G_LEVEL_MIDDLE,
	G_LEVEL_HIGH,

	G_LEVEL_MAX
}GSENSOR_ACTIVE_LEVEL;

typedef enum
{
	G_SENSOR_GMA301	=	0,
	G_SENSOR_SC7A30E,
	G_SENSOR_DA380,

	G_SENSOR_MAX
}G_SENSOR_TYPE_E;

typedef struct G_Sensor_OP_S
{
	char name[12];
	int (*init)(void);

    void (*readxyz)(int *x,int *y,int *z);
	int (*ioctrl)(int op,int para);
}G_Sensor_OP_T;
typedef struct G_Sensor_CTL_S
{
	int activeTime;
	int activeFlag;
}G_Sensor_CTL_T;

extern const G_Sensor_OP_T gma301;
extern const G_Sensor_OP_T sc7a30e;
extern const G_Sensor_OP_T da380;
extern G_Sensor_CTL_T	gsensor_ctl;
/*******************************************************************************
* Function Name  : gsensor_iic_read
* Description    : read gsensor data from iic bus
* Input          : INT8U sid : slave id
                     INT8U addr: read address
                     INT8U *data : data
* Output         : none                                            
* Return         : none
*******************************************************************************/
#define gsensor_iic_read	hal_iic18bitAddrReadData
/*******************************************************************************
* Function Name  : gsensor_iic_write
* Description    : write gsensor data to iic bus
* Input          : INT8U sid : slave id
                     INT8U addr: read address
                     INT8U data : data
* Output         : none                                            
* Return         : none
*******************************************************************************/
#define gsensor_iic_write	hal_iic18bitAddrWriteData
/*******************************************************************************
* Function Name  : gsensor_iic_enable
* Description    : enable iic for gsensor r/w
* Input          :none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void gsensor_iic_enable(void);
/*******************************************************************************
* Function Name  : gsensor_iic_disable
* Description    : disable gsensor r/w iic
* Input          :none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void gsensor_iic_disable(void);

/*******************************************************************************
* Function Name  : gSensorGetName
* Description    : get gsensor name
* Input          :
* Output         : none                                            
* Return         : char *
*******************************************************************************/
char *gSensorGetName(void);
/*******************************************************************************
* Function Name  : dev_gSensor_Init
* Description    : initial gsensor
* Input          : none
* Output         : none                                            
* Return         : wake up state
*******************************************************************************/
int dev_gSensor_Init(void);
/*******************************************************************************
* Function Name  : dev_gSensor_ioctrl
* Description    : dev_gSensor_ioctrl
* Input          : none
* Output         : none                                            
* Return         : wake up state
*******************************************************************************/
int dev_gSensor_ioctrl(INT32U op,INT32U para);
























#endif

