/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_RECORD_VIDEO_H
#define  __TASK_RECORD_VIDEO_H

#define VIDEO_STDAVI_TMP_FILE	"VIDEO.tmp"
//#define VIDEOA_STDAVI_TMP_FILE	"VIDEOA.tmp"
//#define VIDEOB_STDAVI_TMP_FILE	"VIDEOB.tmp"

EXTERN_WINDOW(recordVideoWindow);
extern sysTask_T taskRecordVideo;
/*******************************************************************************
* Function Name  : app_taskRecordVideo_start
* Description    : app_taskRecordVideo_start function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
int app_taskRecordVideo_start(void);
/*******************************************************************************
* Function Name  : app_taskRecordVideo_stop
* Description    : app_taskRecordVideo_stop function.
* Input          : 
* Output         : none                                            
* Return         : int: >=0 stop success
*******************************************************************************/
int app_taskRecordVideo_stop(void);
/*******************************************************************************
* Function Name  : app_taskRecordVideo_start
* Description    : app_taskRecordVideo_start function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
void app_taskRecordVideo_caltime(void);
/*******************************************************************************
* Function Name  : app_taskRecordVideo_Capture
* Description    : app_taskRecordVideo_Capture function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
void app_taskRecordVideo_Capture(u32 cmd);



#endif
