/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HX330X_CSI_H
	#define  HX330X_CSI_H



typedef enum
{
	MCLK_SRC_SYSPLL = 0,
	MCLK_SRC_USBPLL = 1,
}PLLSRC_TYPE;
typedef enum{
	MIPI_DATA_RAW8 		= 0x2A,
	MIPI_DATA_RAW10 	= 0x2B,
}MIPI_VIDEO_DATA_TYPE;
typedef enum
{
	CSI_TYPE_DVP  = (0<<8), 
	CSI_TYPE_MIPI = (1<<8),   
}CSI_INTERFACE;
typedef enum
{
	CSI_TYPE_RAW8=0, // raw8 data 
	CSI_TYPE_RAW10,   // raw10 data 
	CSI_TYPE_RAW12,   // raw12 data 
	CSI_TYPE_YUV422, // YUV422 0-255
	CSI_TYPE_YUVR422,// YUV 422 16-235,16-240
	
	CSI_TYPE_COLOR_BAR = 0xff,// YUV 422 16-235,16-240
}CSI_TYPE_E;

typedef enum
{
	ISP_CTRL_AWB_STAT_CLR= (1 << 0), 
	ISP_CTRL_AE_STAT_CLR = (1 << 1),
	ISP_CTRL_CP_STAT_KS  = (1 << 2),
	ISP_CTRL_AWB_TAB_KS	 = (1 << 16),
	ISP_CTRL_YGAMMA_KS	 = (1 << 17),
	ISP_CTRL_RGAMMA_KS	 = (1 << 18),
	ISP_CTRL_LS_KS		 = (1 << 19),
}ISP_CTRL_E;

typedef enum
{
    CSI_PRIORITY_RGGB=0,
    CSI_PRIORITY_GRBG,
    CSI_PRIORITY_BGGR,
    CSI_PRIORITY_GBRG,

	CSI_PRIORITY_CBY0CRY1=0,
	CSI_PRIORITY_CRY0CBY1,
	CSI_PRIORITY_Y0CBY1CR,
	CSI_PRIORITY_Y0CRY1CB,
}CSI_POLARITY_E;

typedef enum
{
	CSI_MODE_ENABLE         = (1 << 0),
	CSI_MODE_VSYNC          = (1 << 1),
	CSI_MODE_HSYNC          = (1 << 2),
	CSI_MODE_PCLK_INV       = (1 << 3),
	CSI_MODE_MIPI_EN		= (1 << 4),
	CSI_MODE_SAMPLE         = (1 << 5), //1:PCLK rising edge sample, 0: csi clk over sample
	CSI_MODE_FORMAT         = (3 << 6),
	CSI_MODE_PRIORITY       = (3 << 8),
	CSI_MODE_DATA_BW        = (3 << 10),
	CSI_MODE_TEST_MODE      = (15 << 12),
	CSI_MODE_TEST_EN        = (1 << 16),
	CSI_MODE_DVP_PCLK_DIV   = (3 << 17),
	CSI_MODE_DVP_EN         = (1 << 19),
	CSI_MODE_ANA_FIR_MODE   = (15 << 22),
	CSI_MODE_DIG_FIR_MODE   = (3 << 26),
}CSI_MODE_E;


typedef enum
{
	CSI_OUTPUT_UVCSIZE   	= (0x3fff << 0),  //1表示8bytes
	CSI_OUTPUT_UVCDMAPRIO   = (3 << 14),
	CSI_OUTPUT_UVCEN     	= (1 << 16),
	CSI_OUTPUT_RELOAD    	= (1 << 17),
	CSI_OUTPUT_LCDEN     	= (1 << 18),
	CSI_OUTPUT_MJPGEN    	= (1 << 19),
	CSI_OUTPUT_WIFIEN    	= (1 << 20),
	CSI_OUTPUT_LDMAMANEN 	= (1 << 21),
}CSI_OUTPUT_E;
typedef enum
{
	CSI_BURST_4BYTE=0,
	CSI_BURST_8BYTE,
	CSI_BURST_12BYTE,
	CSI_BURST_16BYTE,
	CSI_BURST_20BYTE,
	CSI_BURST_24BYTE,
	CSI_BURST_28BYTE,
	CSI_BURST_32BYTE
}CSI_BURST_E;
typedef enum
{
	CSI_LCDSCALER_1_1=0, // 1/1
	CSI_LCDSCALER_1_2,   // 1/2
	CSI_LCDSCALER_1_3,   // 1/3
	CSI_LCDSCALER_REV
}CSI_LCDSCALER_E;
typedef enum
{
	CSI_UVCDMA_CRY1CBY0=0,
	CSI_UVCDMA_CBY1CRY0,
	CSI_UVCDMA_Y1CRY0CB,
	CSI_UVCDMA_Y1CBY0CR
}CSI_UVCDMA_E;
typedef enum
{
	CSI_JFRA_END_INT=0,   //0
	CSI_LFRA_END_INT,     //1
	CSI_UVC_PACKET_INT,   //2
	CSI_SEN_SIZE_STAT_INT,//3
	CSI_SEN_SIZE_ERR,     //4
	CSI_JBUFF_ERR,        //5
    CSI_LBUFF_ERR,        //6
	CSI_MOTION_DETECT_INT,//7
	CSI_J1BUFF_ERR,       //8
	CSI_J1FRA_END_INT,    //9
	CSI_DVP_FRAME_DONE,   //10
	CSI_ASYC_FIRO_ERR,    //11
	CSI_DVP_MEM_EMPTY,    //12
	CSI_STAT_CP_DONE,     //13
	CSI_WP_DET_INT,       //14
	CSI_AE_HIDT_INT,      //15
	CSI_WDR_STAT_INT,     //16
	CSI_JE_CSI_ERR,       //17
	CSI_JE_LB_OVERFLOW,   //18
	CSI_LINE_SCALER_ERR,  //19
	CSI_UVC_DMA_ERR,      //20	
	CSI_INT_EN = 31
}CSI_INT_E;
typedef enum
{
	CSI_IRQ_JPG_FRAME_END=0,//0 CSI TO JPG FRAME END
	CSI_IRQ_LCD_FRAME_END,  //1 CSI TO LCD FRAME END
	CSI_IRQ_UVC_PACKE_END,  //2 CSI to UVC each packet end 
	CSI_IRQ_SEN_STATE_INT,  //3 
	CSI_IRQ_SEN_STATE_ERR,  //4
	CSI_IRQ_JBF_DMAWR_ERR,  //5 CSI to JPG DMA BUF FULL
	CSI_IRQ_LBF_DMAWR_ERR,  //6 CSI to LCD DMA BUF FULL
	CSI_IRQ_MDT_MOTION_DET, //7
	CSI_IRQ_WIFIBF_ERR,     //8 CSI TO WIFI BUF FULL
	CSI_IRQ_WIFI_FRAME_END ,//9 CSI TO WIFI FRAME END
	CSI_IRQ_ISP,            //10
	CSI_JE_LB_OVERFLOW_ERR, //11 JPG AUTO MODE LINEBUF OVERFLOW   
	CSI_DVP_INPUT_SDRAM_KICK, //12 kick data to dvp input	
	CSI_IRQ_MAX	
}CSI_IRQ_E;

typedef enum
{
	CSI_PASS_MODE   = ((0<<29)|(0<<28)),
	CSI_CROP_MODE   = ((0<<29)|(1<<28)),
	CSI_DIV2_MODE   = ((1<<29)|(0<<28)),
	CSI_CROP_DIV2_MODE = ((1<<29)|(1<<28))
}CSI_CROP_M;
typedef enum
{
	CSI_TEST_WHITE   = 0,
	CSI_TEST_YELLOW,
	CSI_TEST_CYAN,
	CSI_TEST_GREEN,
	CSI_TEST_MAGENTA,
	CSI_TEST_RED,
	CSI_TEST_BLUE,
	CSI_TEST_BLACK,
	CSI_TEST_COLORBARS,
	CSI_TEST_SQUARE,
	CSI_TEST_MAX,
}CSI_TEST_PATTERN_E;
typedef enum
{
	ISP_MODE_BLCEN      = (1 << 0), 
	ISP_MODE_LSCEN      = (1 << 1),
	ISP_MODE_DPCEN      = (1 << 2),
	ISP_MODE_AWBEN      = (1 << 3),
	ISP_MODE_CFAEN      = (1 << 4),
	ISP_MODE_CCMEN      = (1 << 5),
	ISP_MODE_DGAINEN    = (1 << 6),
	ISP_MODE_YGAMMAEN   = (1 << 7),
	ISP_MODE_RGBGAMMAEN = (1 << 8),
	ISP_MODE_CHEN       = (1 << 9),
	ISP_MODE_VDEEN      = (1 << 10),
	ISP_MODE_SHARPEN    = (1 << 11),
	ISP_MODE_CCFEN      = (1 << 12),
	ISP_MODE_SAJEN      = (1 << 13),
	ISP_MODE_AE_STATEN  = (1 << 14),
	ISP_MODE_AWB_STATEN = (1 << 15),
	ISP_MODE_JPEGCROP   = (1 << 16),
	ISP_MODE_LCDCROP    = (1 << 17),
	ISP_MODE_WIFICROP   = (1 << 18),
	ISP_MODE_WIFISDEN	= (1 << 19),
	ISP_MODE_CSI_SAVEEN	= (1 << 20),
	ISP_MODE_JSCALER_SEL= (1 << 21),
	ISP_MODE_LSEN		= (1 << 22),
	ISP_MODE_YUV422_LS_EN = (1 << 23),
	ISP_MODE_JPEGSDEN	= (1 << 24),
	ISP_MODE_YUV422CH_CCM = (1<<30),
	ISP_MODE_YUV422CH_VDE = (2<<30),
}ISP_MODE_E;


/*******************************************************************************
* Function Name  : hx330x_csiIOConfig
* Description    : csi io config set
* Input          :  none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiIOConfig(u32 type);

/*******************************************************************************
* Function Name  : hx330x_csiInit
* Description    : csi initial 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiInit(u32 type);
/*******************************************************************************
* Function Name  : hx330x_mjpA_reset
* Description    : mjpA  module reset
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csi_reset(void);
/*******************************************************************************
* Function Name  : hx330x_csi_fcnt_mnt
* Description    : csi frame debg
* Input          : None
* Output         : None
* Return         : csi frame cnt
*******************************************************************************/
void hx330x_csi_fcnt_mnt(void);

/*******************************************************************************
* Function Name  : hx330x_csiISRRegiser
* Description    : csi isr register
* Input          : u8 type : irq type.CSI_IRQ_E
				   void (*isr)(void) : isr
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiISRRegiser(u8 type,void (*isr)(void));


/*******************************************************************************
* Function Name  : hx330x_SenSizeErr_LCD_callback
* Description    : when sensize err,LCD process flow
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_CSI_SenSizeErr_callback(void);

/*******************************************************************************
* Function Name  : hx330x_SenSizeErr_LCD_callback
* Description    : when sensize err,LCD process flow
* Input          : None
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_CSI_LBFDMAERR_callback(void);

/*******************************************************************************
* Function Name  : hx330x_csiOutputSet
* Description    : csi output mode set
* Input          : u32 mode : mode.CSI_OUTPUT_E
				   u8 en      : 0-disable,1-enable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiOutputSet(u32 mode,u8 en);

/*******************************************************************************
* Function Name  : hx330x_csiMclkSet
* Description    : csi mclk set
* Input          : u32 clk : clock, u32 src:  0- syspll, 1 - usb phy pll
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiMclkSet(u32 clk, u32 src);  //src: 0- syspll, 1 - usb phy pll
/*******************************************************************************
* Function Name  : hx330x_csiSyncSet
* Description    : csi sync set
* Input          : u8 hsync_en : hsync
				   u8 vsync_en : vsync
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiSyncSet(u8 hsync_en,u8 vsync_en);
/*******************************************************************************
* Function Name  : hx330x_csiPrioritySet
* Description    : csi priority set
* Input          : u8 type : type .CSI_POLARITY_E
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiPrioritySet(u32 plo);
/*******************************************************************************
* Function Name  : hx330x_csiTypeSet
* Description    : csi type set
* Input          : u8 type : type .CSI_TYPE_E
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiTypeSet(u32 type);
/*******************************************************************************
* Function Name  :hx330x_csiModeSet
* Description    : csi mode set
* Input          : u32 mode : mode flag
				   u32 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiModeSet(u32 mode,u8 en);
/*******************************************************************************
* Function Name  :hx330x_csiModeGet
* Description    : csi mode get
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
u32 hx330x_csiModeGet(void);
/*******************************************************************************
* Function Name  : hx330x_pclk_fir_Set
* Description    : pclk filter set
* Input          : u8 fir_step : . 0 - disable filter, 1 - enable 2 steps filter,
*                                  2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_pclk_digital_fir_Set(u32 fir_step);
/*******************************************************************************
* Function Name  : hx330x_pclk_analog_Set
* Description    : pclk filter set
* Input          : u8 : fir_step, 4'b0xxx： diable， 4'b1xxx: enable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_pclk_analog_Set(u32 fir_step);
/*******************************************************************************
* Function Name  : hx330x_pclk_inv_Set
* Description    : pclk filter set
* Input          : u8 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_pclk_inv_Set(u8 en);
/*******************************************************************************
* Function Name  : hx330x_csi_clk_tun_Set
* Description    : pclk tun set
* Input          : u8 tun : tun val
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csi_clk_tun_Set(u32 tun);
/*******************************************************************************
* Function Name  : hx330x_csiSizeSet
* Description    : csi input size
* Input          : u16 width : width
				   u16 height: height
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiSizeSet(u16 width,u16 height);
/*******************************************************************************
* Function Name  : hx330x_sen_Image_Size_Set
* Description    : sensor input size
* Input          : u16 width : width
				   u16 height: height
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_sen_Image_Size_Set(u16 width,u16 height);
/*******************************************************************************
* Function Name  : hx330x_csi_in_CropSet
* Description    : csi input crop (sensor input  --> crop --> csi input)
* Input          : u16 width : width
				   u16 height: height
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csi_in_CropSet(u16 w_crop_st,u16 w_crop_ed,u16 h_crop_st,u16 h_crop_ed,u32 crop_mode);
/*******************************************************************************
* Function Name  : hx330x_csiInputAddrSet
* Description    : csi input from sdram addr set
* Input          : u32 addr : input address
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiInputAddrSet(u32 addr);
/*******************************************************************************
* Function Name  : hx330x_csiTestModeSet
* Description    : csi test mode size set
* Input          : u16 width : width
				   u16 height: height
				   u16 hblank : hblank
				   u16 vblank : vblank
				   u16 test_typ :test_typ
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiTestModeSet(u16 width,u16 height,u16 vblank,u16 hblank,u32 test_typ);
/*******************************************************************************
* Function Name  : hx330x_csiDvpClkDivSet
* Description    : csi dvp gennerate clk_div set
* Input          : u32 clk_div_mode : clk_div
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiDvpClkDivSet(u32 clk_div_mode);
/*******************************************************************************
* Function Name  : hx330x_csiINTSet
* Description    : csi interrupt enable set 
* Input          : u32 int_num: CSI_INT_E 
*                  u8 en: 0- diasable, 1 - enable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiINTSet(u32 int_num,u8 en);
/*******************************************************************************
* Function Name  : hx330x_csiEnable
* Description    : csi enable set
* Input          : u8 en : 0-disable,1-enable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_csiLCDScalerDoneCheck
* Description    : csi to lcd linescaler check
* Input          : none
* Output         : none
* Return         : true : scaler done, false: scaler not done
*******************************************************************************/
bool hx330x_csiLCDScalerDoneCheck(void);
/*******************************************************************************
* Function Name  : hx330x_csiToMjpAddrCfg
* Description    : hx330x_csiToMjpAddrCfg
* Input          : u32 mjpeg_y : y pixel address 
				   u32 mjpeg_uv : uv pixel address 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiToMjpAddrCfg(u32 mjpeg_y,u32 mjpeg_uv);
/*******************************************************************************
* Function Name  : hx330x_csiMJPEGFrameSet
* Description    : csi frame set for mjpeg
* Input          : u32 mjpeg_y : y pixel address 
				   u32 mjpeg_uv : uv pixel address 
				   u32 lines     : lines
				   u32 width   : width
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiMJPEGFrameSet(u32 mjpeg_y,u32 mjpeg_uv,u32 lines,u32 width);
/*******************************************************************************
* Function Name  : hx330x_csiWifiFrameSet
* Description    : csi frame set for wifi
* Input          : u32 mjpeg_y : y pixel address 
				   u32 mjpeg_uv : uv pixel address 
				   u32 lines    : lines
				   u32 width   : width
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiWifiFrameSet(u32 mjpeg_y,u32 mjpeg_uv,u32 lines,u32 width);
/*******************************************************************************
* Function Name  : hx330x_csiLCDFrameSet
* Description    : csi set output frame addr.if any of the r address is NULL,do not care it.
* Input          : u32 yaddr : y pixel buffer addr
				   u32 uvaddr: uv pixel buffer addr.
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiLCDFrameSet(u32 yaddr,u32 uvaddr);
/*******************************************************************************
* Function Name  : hx330x_csi_YUVFrameSet
* Description    : csi yuv buffer set for uvc
* Input          : u32 start_addr : only support ram addr
				   u32 size       : yuv buf size, 8bytes unit
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csi_YUVFrameSet(u32 start_addr,u32 size);
/*******************************************************************************
* Function Name  : hx330x_csiMJPEGScaler
* Description    : csi to mjpeg crop and subsample set
* Input          : u16 tar_w : target width
				   u16 tar_h : target height
				   u16 sx : src w crop start pos
				   u16 sy : src h crop start pos
				   u16 ex : src w crop end pos
				   u16 ey : src h crop end pos
* Output         : none
* Return         : false : not support scaler, true: support scaler and cfg
*******************************************************************************/
bool hx330x_csiMJPEGScaler(u16 tar_w,u16 tar_h,u16 sx,u16 sy,u16 ex,u16 ey);
/*******************************************************************************
* Function Name  : hx330x_csiMJPEGScaler
* Description    : csi to mjpeg crop 
* Input          : u16 src_w : src width
				   u16 src_h : src height
				   u16 sx : src w crop start pos
				   u16 sy : src h crop start pos
				   u16 ex : src w crop end pos
				   u16 ey : src h crop end pos
* Output         : none
* Return         : false : not support crop, true: support crop and cfg
*******************************************************************************/
bool hx330x_csiMJPEGCrop(u16 src_w,u16 src_h,u16 sx,u16 sy,u16 ex,u16 ey);
/*******************************************************************************
* Function Name  : hx330x_csiWifiScaler
* Description    : csi to wifi crop and subsample set
* Input          : u16 tar_w : target width
				   u16 tar_h : target height
				   u16 sx : src w crop start pos
				   u16 sy : src h crop start pos
				   u16 ex : src w crop end pos
				   u16 ey : src h crop end pos
* Output         : none
* Return         : false : not support scaler, true : support scaler and cfg
*******************************************************************************/
bool hx330x_csiWifiScaler(u16 tar_w,u16 tar_h,u16 sx,u16 sy,u16 ex,u16 ey);
/*******************************************************************************
* Function Name  : hx330x_csiSetLsawbtooth
* Description    : anti-aliasing on lcd screen
* Input          : u32 *anti_lsawtooth
*                  
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_csiSetLsawbtooth(u32 *anti_lsawtooth);
/*******************************************************************************
* Function Name  : hx330x_csiLCDScaler
* Description    : csi scaler for lcd, use linescaler
* Input          : u16 tar_w : target width
                   u16 tar_h : target height
                   u16 sx    : crop start x position
                   u16 sy    : crop start y position
                   u16 ex    : crop end x position
                   u16 ey    : crop end y position
                   u16 stride: warning:stride >= width
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiLCDScaler(u16 tar_w,u16 tar_h,u16 sx,u16 sy,u16 ex,u16 ey,u16 stride);
/*******************************************************************************
* Function Name  : hx330x_csiMJPEGDmaEnable
* Description    : csi output for mjpeg dma control
* Input          : u8 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiMJPEGDmaEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_csiWifiDmaEnable
* Description    : csi output for wifi dma control
* Input          : u8 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiWifiDmaEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_csiLCDDmaEnable
* Description    : csi output for lcd control enable
* Input          : u8 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiLCDDmaEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_csiLCDDmaKick
* Description    : csi output for lcd control enable(manual mode)
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csiLCDDmaKick(void);
/*******************************************************************************
* Function Name  : hx330x_csi_common_int_set
* Description    : hx330x_csi_only_FrameSet for ezxposure
* Input          : none 
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_csi_common_int_set(void);
/*******************************************************************************
* Function Name  : hx330x_csiIRQHandler
* Description    : csi irq handler
* Input          : None
* Output         : None
* Return         : csi frame cnt
*******************************************************************************/
void hx330x_csiIRQHandler(void);


#endif
