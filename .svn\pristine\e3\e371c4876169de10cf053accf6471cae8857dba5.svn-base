/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"

ALIGNED(4) const u8 device_inquiry_data[50] =
{
	0x00,	// Peripheral Device Type: direct access devices
	0x80,	// Removable: UFD is removable
	0x04,	// ANSI version
	0x02, 	// Response Data Format: compliance with UFI  
	0x2d,   //0x1f,	// Additional Length (Number of unsigned chars following this one): 31, totally 36 unsigned chars
	0x00, 0x00, 0x00,	// reserved
	'G', 'e', 'n', 'e', 'r', 'i', 'c', ' ', 
	'M', 'a', 's', 's', '-', 'S', 't', 'o', 'r', 'a', 'g', 'e', ' ', ' ', ' ', ' ', 
	'3', '.', '1', '0',		
    '.', '1', '.', '0','0',' ',
	'B', 'e', 'a', 'c', 'o', 'n', ' ', ' '
     		
};
#define scsi_bulk_clr_rx()		XSFR_USB20_SIE_EPRX_CTRL0 = DUSB_EPRX_FLUSHFIFO//XSFR_USB20_SIE_EPRX_CTRL0 = 0 //XSFR_USB20_SIE_EPRX_CTRL0 = BIT(4);           //Flush FIFO, Clear RxRdy//XSFR_USB20_SIE_EPRX_CTRL0 = 0;
#define scsi_bulk_clr_rxkick()	XSFR_USB20_SIE_EPRX_CTRL0 = 0 //XSFR_USB20_SIE_EPRX_CTRL0 = BIT(4);  
#define SENSE_ERROR_CODE		0x70
#define	REQUESTDATA_SIZE		0x12
#define INQUIRY_SIZE            sizeof(device_inquiry_data)
#define FORMTCAPACITY_SIZE		0x0C
#define DISKCAPACITY_SIZE		0x08
#define SECTOR_SIZE				0x200
#define MODE_SENSE10_LEN	    0x08


typedef  struct _SENSE_CODE {
	unsigned char	SenseKey;
	unsigned char	ASC;
} SENSE_CODE;

ALIGNED(4) const u8  MscSenseCode[][2] = {
	/*NO_SENSE			*/ 			{0x00, 0x00},
	/*INVALID_FIELD_IN_COMMAND	*/ 	{0x05, 0x24},
	/*NEW_MEDIUM_ARRIVEL		*/ 	{0x06, 0x28},
	/*WRITE_PROTECTED		*/ 		{0x07, 0x27},
	/*MEDIUM_NOT_PRESENT		*/ 	{0x02, 0x3A},
	/*DATA_PHASE_ERROR  */			{0x04, 0x4b},
};

/*******************************************************************************
* Function Name  : dusb_msc_tx
* Description    : dusb_msc_tx
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
SDRAM_TEXT_SECTION
static bool dusb_msc_tx(u32 adr, u16 len)
{
	return hx330x_bulk20_tx(usb_dev_ctl.scsi.epxin&0x7f, adr, len);
}
/*******************************************************************************
* Function Name  : dusb_msc_rx
* Description    : dusb_msc_rx
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
SDRAM_TEXT_SECTION
static bool dusb_msc_rx(u32 adr, u16 len)
{
	return hx330x_bulk20_rx(usb_dev_ctl.scsi.epxout, adr, len);
}
/*******************************************************************************
* Function Name  : dusb_msc_rx_kick
* Description    : dusb_msc_rx_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
UNUSED static void dusb_msc_rx_kick(void)  
{
	XSFR_USB20_SIE_EPS = usb_dev_ctl.scsi.epxout;
	(&XSFR_USB20_EP1_RXADDR)[DEV_RXEP_MASS*2-2]  = (u32)usb_dev_ctl.scsi.prxbuf;
	XSFR_USB20_EPINTLEN = BULK_MAX_SIZE_HS;
	XSFR_USB20_SIE_EPRX_CTRL0 = 0;           //kick
}
/*******************************************************************************
* Function Name  : msc_epx_cfg
* Description    : msc_epx_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void msc_epx_cfg(void)
{
	usb_dev_ctl.scsi.pc_move	 = 0;
	usb_dev_ctl.scsi.epxin       = DEV_TXEP_MASS|0x80;        
	usb_dev_ctl.scsi.epxout      = DEV_RXEP_MASS;    
	usb_dev_ctl.scsi.bstall		 = 0;
	//scsi.pcsw        = (u8*)_USB20_MSC_TXFIFO_;
	//scsi.pcbw        = (u8*)_USB20_MSC_RXFIFO_;
	usb_dev_ctl.scsi.ptxbuf      = (u8*)_USB20_MSC_TXFIFO_;
	usb_dev_ctl.scsi.prxbuf      = (u8*)_USB20_MSC_RXFIFO_;
	
	XSFR_USB20_SIE_EPS 	 = DEV_TXEP_MASS;		
	XSFR_USB20_SIE_TXPKGMAXL 	 = 0;
	XSFR_USB20_SIE_TXPKGMAXH 	 = 0x04;
	XSFR_USB20_SIE_EPTX_CTRL1     = 0;
	XSFR_USB20_SIE_EPTX_CTRL0     = DUSB_EPTX_CLRDATATOG | DUSB_EPTX_FLUSHFIFO;
	XSFR_USB20_SIE_EPS 	 = DEV_RXEP_MASS;	
	XSFR_USB20_SIE_RXPKGMAXL   = 0;
	XSFR_USB20_SIE_RXPKGMAXH   = 0x04;
	XSFR_USB20_SIE_EPRX_CTRL1 	 = 0;
	XSFR_USB20_SIE_EPRX_CTRL0    = DUSB_EPRX_CLRDATATOG | DUSB_EPRX_FLUSHFIFO;
	
	
	(&XSFR_USB20_EP1_TXADDR)[DEV_TXEP_MASS*2-2]  = (u32)_USB20_MSC_TXFIFO_;
    (&XSFR_USB20_EP1_RXADDR)[DEV_RXEP_MASS*2-2]  = (u32)_USB20_MSC_RXFIFO_;	
	scsi_bulk_clr_rxkick();

}
/*******************************************************************************
* Function Name  : ScsiStallAll
* Description    : ScsiStallAll
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void ScsiStallAll(void)
{
	usb_dev_ctl.msc_pipe_stall = 1;
	dusb_stall_ep(usb_dev_ctl.scsi.epxout);
	dusb_stall_ep(usb_dev_ctl.scsi.epxin);
	
	while(usb_dev_ctl.msc_pipe_stall) {
        if(usb_dev_ctl.msc_rx_stall == 0) {
            dusb_stall_ep(usb_dev_ctl.scsi.epxout);
		}
        if(usb_dev_ctl.msc_tx_stall == 0) {
    		dusb_stall_ep(usb_dev_ctl.scsi.epxin);
		}
    }
}
/*******************************************************************************
* Function Name  : ScsiStallIn
* Description    : ScsiStallIn
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool ScsiStallIn(u8 stall)
{
	hx330x_timerTickStart();
    if (stall) 
	{
		//deg_Printf("- stall in\n");
        dusb_stall_ep(usb_dev_ctl.scsi.epxin);
		//deg_Printf("usb_dev_ctl.msc_tx_stall:%d\n",usb_dev_ctl.msc_tx_stall);
        while (usb_dev_ctl.msc_tx_stall)
		{
			hal_wdtClear();
			if(hx330x_timerTickCount() >= hardware_setup.sys_clk*2)
			{
				deg_Printf("- stall in to\n");
				break;
			}		
		}
			
    }
	
	hx330x_timerTickStop();
	return true;

}
/*******************************************************************************
* Function Name  : ScsiStallout
* Description    : ScsiStallout
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool ScsiStallout(u8 stall)
{
	hx330x_timerTickStart();
    if (stall) {
		//deg_Printf("- stall out\n");
        dusb_stall_ep(usb_dev_ctl.scsi.epxout);
		//deg_Printf("usb_dev_ctl.msc_rx_stall:%d\n",usb_dev_ctl.msc_rx_stall);
        while (usb_dev_ctl.msc_rx_stall){
			hal_wdtClear();
			if(hx330x_timerTickCount() >= hardware_setup.sys_clk*2)
			{
				deg_Printf("- stall out to\n");
				break;
			}	
		}
    }
	hx330x_timerTickStop();
	return true;
}
/*******************************************************************************
* Function Name  : dev_online
* Description    : dev_online
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool dev_online(u8 lun)
{
	if(usb_dev_ctl.scsi.disk_online_func)
		return (*usb_dev_ctl.scsi.disk_online_func)();
	return false;

}
/*******************************************************************************
* Function Name  : dev_online
* Description    : dev_online
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static u32 dev_cap(u8 lun)
{
	if(usb_dev_ctl.scsi.disk_cap_func)
		return (*usb_dev_ctl.scsi.disk_cap_func)();
	return 0;
}
/*******************************************************************************
* Function Name  : dev_stop
* Description    : dev_stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void dev_stop(void)
{
	if(usb_dev_ctl.scsi.disk_stop_func)
		(*usb_dev_ctl.scsi.disk_stop_func)();
}


/*******************************************************************************
* Function Name  : dev_busy
* Description    : dev_busy
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void dev_busy(void)
{
	if(usb_dev_ctl.scsi.disk_busy_func)
		(*usb_dev_ctl.scsi.disk_busy_func)();
}


/*******************************************************************************
* Function Name  : dev_free
* Description    : dev_free
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void dev_free(void)
{
	if(usb_dev_ctl.scsi.disk_free_func)
		(*usb_dev_ctl.scsi.disk_free_func)();
}
/*******************************************************************************
* Function Name  : mscSet_Status
* Description    : mscSet_Status
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void mscSet_Status(u8 status)
{
	usb_dev_ctl.scsi.sense = status;
	if(status){
		usb_dev_ctl.scsi.cswsta = 1;	
	}
}
/*******************************************************************************
* Function Name  : Host_In_Data
* Description    : Host_In_Data
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void Host_In_Data(u8 *ptxbuf, u32 len)
{	
	if (!usb_dev_ctl.scsi.bstall) {
		if (usb_dev_ctl.MscCmd.CbwTrxLength > len) {
			usb_dev_ctl.MscCmd.Residue = usb_dev_ctl.MscCmd.CbwTrxLength - len;
			dusb_msc_tx((u32)ptxbuf,len);
			usb_dev_ctl.scsi.bstall = 1;
			
		} else {
			dusb_msc_tx((u32)ptxbuf,usb_dev_ctl.MscCmd.CbwTrxLength);
		}
		mscSet_Status(NO_SENSE);
	}	
	else {
		usb_dev_ctl.MscCmd.Residue = usb_dev_ctl.MscCmd.CbwTrxLength;
	}
}
/*******************************************************************************
* Function Name  : mscCmd_ReadCapacity
* Description    : mscCmd_ReadCapacity
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool mscCmd_ReadCapacity(void)
{
	u32 cap;
	
	if(!dev_online(usb_dev_ctl.MscCmd.CbwLun))
	{
		mscSet_Status(MEDIUM_NOT_PRESENT);
		usb_dev_ctl.scsi.bstall = 1;	
	}
	
	if(!(cap = dev_cap(usb_dev_ctl.MscCmd.CbwLun)))
	{
		mscSet_Status(MEDIUM_NOT_PRESENT);
		usb_dev_ctl.scsi.bstall = 1;	
	}	
	
	cap -= 1;
	usb_dev_ctl.scsi.ptxbuf[0] = (u8)(cap >> 24);
	usb_dev_ctl.scsi.ptxbuf[1] = (u8)(cap >> 16);
	usb_dev_ctl.scsi.ptxbuf[2] = (u8)(cap >> 8);
	usb_dev_ctl.scsi.ptxbuf[3] = (u8)(cap >> 0);

	usb_dev_ctl.scsi.ptxbuf[4] = 0;
	usb_dev_ctl.scsi.ptxbuf[5] = 0;
	usb_dev_ctl.scsi.ptxbuf[6] = 0x02;
	usb_dev_ctl.scsi.ptxbuf[7] = 0x00;
	
	Host_In_Data(usb_dev_ctl.scsi.ptxbuf,8);
	
	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_ReadCapacity
* Description    : mscCmd_ReadCapacity
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool mscCmd_ReadFormatCapacity(void)
{
	u32 cap;

	if(!dev_online(usb_dev_ctl.MscCmd.CbwLun)){
		mscSet_Status(MEDIUM_NOT_PRESENT);
		usb_dev_ctl.scsi.bstall = 1;	
	}
	
	if(!(cap = dev_cap(usb_dev_ctl.MscCmd.CbwLun))){
		mscSet_Status(MEDIUM_NOT_PRESENT);
		usb_dev_ctl.scsi.bstall = 1;	
	}	
	cap -= 1;
	
	usb_dev_ctl.scsi.ptxbuf[0] 	= 0x00;
	usb_dev_ctl.scsi.ptxbuf[1] 	= 0x00;
	usb_dev_ctl.scsi.ptxbuf[2] 	= 0x00;
	usb_dev_ctl.scsi.ptxbuf[3] 	= 0x08;	
	
	usb_dev_ctl.scsi.ptxbuf[4] 	= (u8)(cap >> 24);
	usb_dev_ctl.scsi.ptxbuf[5] 	= (u8)(cap >> 16);
	usb_dev_ctl.scsi.ptxbuf[6] 	= (u8)(cap >> 8);
	usb_dev_ctl.scsi.ptxbuf[7] 	= (u8)(cap >> 0);
		
	usb_dev_ctl.scsi.ptxbuf[8] 	= 0x02;
	usb_dev_ctl.scsi.ptxbuf[9] 	= 0x00;
	usb_dev_ctl.scsi.ptxbuf[10] = 0x02;
	usb_dev_ctl.scsi.ptxbuf[11] = 0x00;
	
	//debg("-bstall:%x\n",scsi.bstall);
	Host_In_Data(usb_dev_ctl.scsi.ptxbuf,12);

	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_RequestSense
* Description    : mscCmd_RequestSense
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool mscCmd_RequestSense(void)
{
	u8 * pSenseCode = (u8*)&MscSenseCode[usb_dev_ctl.scsi.sense];
	
	memset(usb_dev_ctl.scsi.ptxbuf, 0, REQUESTDATA_SIZE);
	
	usb_dev_ctl.scsi.ptxbuf[0] 	= SENSE_ERROR_CODE;		// error code
	usb_dev_ctl.scsi.ptxbuf[2] 	= pSenseCode[0];			// sense key
	usb_dev_ctl.scsi.ptxbuf[7] 	= REQUESTDATA_SIZE - 8;	// Additional Sense data length
	usb_dev_ctl.scsi.ptxbuf[12] = pSenseCode[1];			// Additional Sense Code
	usb_dev_ctl.scsi.ptxbuf[13] = 0;  					//MscStatusCode.ASCQ;Additional Sense Code Qualifier
	
	Host_In_Data(usb_dev_ctl.scsi.ptxbuf,REQUESTDATA_SIZE);

	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_Inquiry
* Description    : mscCmd_Inquiry
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool mscCmd_Inquiry(void)
{
	memcpy(usb_dev_ctl.scsi.ptxbuf, (u8*)device_inquiry_data, INQUIRY_SIZE);
	Host_In_Data(usb_dev_ctl.scsi.ptxbuf,INQUIRY_SIZE);
	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_ModeSense6
* Description    : mscCmd_ModeSense6
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool mscCmd_ModeSense6(void)
{
	if(!dev_online(usb_dev_ctl.MscCmd.CbwLun)){
		mscSet_Status(MEDIUM_NOT_PRESENT);
		usb_dev_ctl.scsi.bstall = 1;	
	}
	#define _dwTxdata_   0x03000000L	
	
	usb_dev_ctl.scsi.ptxbuf[0] = (u8)(_dwTxdata_ >> 24);
	usb_dev_ctl.scsi.ptxbuf[1] = (u8)(_dwTxdata_ >> 16);
	usb_dev_ctl.scsi.ptxbuf[2] = (u8)(_dwTxdata_ >> 8);
	usb_dev_ctl.scsi.ptxbuf[3] = (u8)(_dwTxdata_ >> 0);
	
	Host_In_Data(usb_dev_ctl.scsi.ptxbuf,4);
	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_TestUnitReady
* Description    : mscCmd_TestUnitReady
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool mscCmd_TestUnitReady(void)
{
	dev_free();
	if(!dev_online(usb_dev_ctl.MscCmd.CbwLun)){
		mscSet_Status(MEDIUM_NOT_PRESENT);
	}
	else{
		mscSet_Status(NO_SENSE);			
	}
	
	if(usb_dev_ctl.scsi.pc_move){
		mscSet_Status(MEDIUM_NOT_PRESENT);	
	}
	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_StartStopUnit
* Description    : mscCmd_StartStopUnit
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool mscCmd_StartStopUnit(void)
{
	if(0x0200 == usb_dev_ctl.MscCmd.Address){
		dev_stop();
		usb_dev_ctl.scsi.pc_move = true;
		deg_Printf("-PC DEV moved...\n");
	}
	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_PreventAllowMediumRemoval
* Description    : mscCmd_PreventAllowMediumRemoval
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool mscCmd_PreventAllowMediumRemoval(void)
{
	if((usb_dev_ctl.MscCmd.Address >> 8) == 0x01){
		mscSet_Status(INVALID_FIELD_IN_COMMAND);	
	}
	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_Verify
* Description    : mscCmd_Verify
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool mscCmd_Verify(void)
{
	//LED闪灯
	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_Verify
* Description    : mscCmd_Verify
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
typedef void (*L1_func_type)(u32 *p1,u32 *p2);
typedef void (*L2_func_type) (u32, u32, u32);
/*******************************************************************************
* Function Name  : dusb_WriteToMem
* Description    : dusb_WriteToMem
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_WriteToMem(u8 *buf, u32 len) 
{  //注意4byte对齐
	dusb_msc_rx((u32)buf,len);
	scsi_bulk_clr_rx();
}
/*******************************************************************************
* Function Name  : dusb_ReadFromMem
* Description    : dusb_ReadFromMem
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dusb_ReadFromMem(u8 *buf, u32 len) 
{
	dusb_msc_tx((u32)buf,len);
}
/*******************************************************************************
* Function Name  : rbc_mem_read
* Description    : rbc_mem_read
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void rbc_mem_read(void) 
{

    if(0xffffffff == usb_dev_ctl.MscCmd.DataAddr) 
		usb_dev_ctl.MscCmd.DataAddr = (uint32)usb_dev_ctl.scsi.ptxbuf;
	// 先L2_func再读内存
	if (usb_dev_ctl.MscCmd.Func2 + 1) {
		L2_func_type L2_func = (L2_func_type)usb_dev_ctl.MscCmd.Func2;
        L2_func(usb_dev_ctl.MscCmd.DataAddr, usb_dev_ctl.MscCmd.Residue, usb_dev_ctl.MscCmd.Param);
	}
    dusb_ReadFromMem((uint8_t *)usb_dev_ctl.MscCmd.DataAddr, usb_dev_ctl.MscCmd.Residue);
    //MscCmd.Residue = 0;  
}
/*******************************************************************************
* Function Name  : rbc_mem_write
* Description    : rbc_mem_write
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/        
void rbc_mem_write(void) 
{

    if(0xffffffff == usb_dev_ctl.MscCmd.DataAddr) 
		usb_dev_ctl.MscCmd.DataAddr = (uint32)usb_dev_ctl.scsi.prxbuf;
	//先写内存，再运行
    dusb_WriteToMem((uint8_t *)usb_dev_ctl.MscCmd.DataAddr, usb_dev_ctl.MscCmd.Residue);
    
	if (usb_dev_ctl.MscCmd.Func2 + 1) {
        //debg("\r\nMscCmd.Func2  = %x",MscCmd.Func2 );
		L2_func_type L2_func = (L2_func_type)usb_dev_ctl.MscCmd.Func2;
        L2_func(usb_dev_ctl.MscCmd.DataAddr, usb_dev_ctl.MscCmd.Residue, usb_dev_ctl.MscCmd.Param);
	}
}
/*******************************************************************************
* Function Name  : rbc_mem_rxfunc
* Description    : rbc_mem_rxfunc
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/    
void rbc_mem_rxfunc(void) 
{
	if (0x80 == usb_dev_ctl.MscCmd.CbwFlag)	
		rbc_mem_read ();
	else
		rbc_mem_write ();
}
/*******************************************************************************
* Function Name  : cbw_returnmask
* Description    : cbw_returnmask
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
void sdk_returnmask(void)
{	
	usb_dev_ctl.returnmaskcombo = 0;
	hx330x_wdtEnable(0);//return mask close WDT
	usb_dev_ctl.MscCmd.Residue = 0;
	//sent_csw(); 
	
	hx330x_usb20_uinit();
	hx330x_usb11_uinit();
	
	hx330x_lcdShowWaitDone();
	__LGIE_DIS__();
	__HGIE_DIS__();

	hx330x_intEnable(IRQ_CSI,0);
	hx330x_intEnable(IRQ_AUADC,0);	
	hx330x_intEnable(IRQ_JPGB,0);	
	hx330x_intEnable(IRQ_UART0,0);	
	hx330x_intEnable(IRQ_LCDC,0);	//
	hx330x_intEnable(IRQ_LCDSHOW,0);	    //
	hx330x_intEnable(IRQ_DAC,0);	
	hx330x_intEnable(IRQ_TIMER0,0);	
	hx330x_intEnable(IRQ_TIMER1,0);	
	hx330x_intEnable(IRQ_TIMER2,0);	
	hx330x_intEnable(IRQ_TIMER3,0);	
	hx330x_intEnable(IRQ_GPIO,0);	
	hx330x_intEnable(IRQ_USB20,0);	
	hx330x_intEnable(IRQ_USB11,0);	
	hx330x_intEnable(IRQ_RTC_WDT,0);	

	//lcd back light off
	//hal_gpioEPullSet(GPIO_PA,GPIO_PIN8,GPIO_PULLE_DOWN);
	//hal_gpioWrite(GPIO_PA,GPIO_PIN8,GPIO_LOW);

	//__intvct_adr__(0x100000);
    __intvct_mask();
	//void dcache_close(); 
    //dcache_close();
}
/*******************************************************************************
* Function Name  : cbw_updatartc
* Description    : cbw_updatartc
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
void cbw_updatartc(void)
{
	deg_Printf("updata rtc:%x\n",usb_dev_ctl.MscCmd.Address); 
	hal_rtcSecondSet(usb_dev_ctl.MscCmd.Address);
}
/*******************************************************************************
* Function Name  : mscCmd_ufmod
* Description    : mscCmd_ufmod
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/  
void mscCmd_ufmod(void)
{
    usb_dev_ctl.MscCmd.Residue = usb_dev_ctl.MscCmd.CbwTrxLength;
    L1_func_type l1_func = (L1_func_type)usb_dev_ctl.MscCmd.Func1;
    l1_func((u32 *)&usb_dev_ctl.MscCmd,(u32 *)&usb_dev_ctl.scsi);
    usb_dev_ctl.MscCmd.Residue = 0;
}
/*******************************************************************************
* Function Name  : mscCmd_ufmod
* Description    : mscCmd_ufmod
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/  
bool sent_csw(void)
{	
	usb_dev_ctl.scsi.ptxbuf[0]  = 0x55;
	usb_dev_ctl.scsi.ptxbuf[1]  = 0x53;
	usb_dev_ctl.scsi.ptxbuf[2]  = 0x42;
	usb_dev_ctl.scsi.ptxbuf[3]  = 0x53;
	
	usb_dev_ctl.scsi.ptxbuf[4]  = (u8)(usb_dev_ctl.MscCmd.CbwTag >> 0);
	usb_dev_ctl.scsi.ptxbuf[5]  = (u8)(usb_dev_ctl.MscCmd.CbwTag >> 8);
	usb_dev_ctl.scsi.ptxbuf[6]  = (u8)(usb_dev_ctl.MscCmd.CbwTag >> 16);
	usb_dev_ctl.scsi.ptxbuf[7]  = (u8)(usb_dev_ctl.MscCmd.CbwTag >> 24);

	usb_dev_ctl.scsi.ptxbuf[8]  = (u8)(usb_dev_ctl.MscCmd.Residue >> 0);
	usb_dev_ctl.scsi.ptxbuf[9]  = (u8)(usb_dev_ctl.MscCmd.Residue >> 8);
	usb_dev_ctl.scsi.ptxbuf[10] = (u8)(usb_dev_ctl.MscCmd.Residue >> 16);
	usb_dev_ctl.scsi.ptxbuf[11] = (u8)(usb_dev_ctl.MscCmd.Residue >> 24);

	usb_dev_ctl.scsi.ptxbuf[12] = usb_dev_ctl.scsi.cswsta ? 1 : 0;	
	dusb_msc_tx((u32)usb_dev_ctl.scsi.ptxbuf,13);
	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_Read
* Description    : mscCmd_Read
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/  
bool mscCmd_Read(void)
{
	u32 lba = usb_dev_ctl.MscCmd.Address;
	u32 sec = usb_dev_ctl.MscCmd.Length ;
	usb_dev_ctl.MscCmd.Residue = usb_dev_ctl.MscCmd.CbwTrxLength;
	
	//设备在线？
	if(!dev_online(usb_dev_ctl.MscCmd.CbwLun)){
		mscSet_Status(MEDIUM_NOT_PRESENT);
		usb_dev_ctl.scsi.bstall = 1;
		return false;
	}
	dev_busy();
	do{
		
		if((*usb_dev_ctl.scsi.disk_rd_func)(usb_dev_ctl.scsi.ptxbuf,lba,1)<0)
		{
			return false;	
		}
		if(!dusb_msc_tx((u32)usb_dev_ctl.scsi.ptxbuf,512)){
			return false;	
		}
		usb_dev_ctl.MscCmd.Residue -= 512;
		lba++;
		hal_wdtClear();
	}while(--sec);
	
	return true;
}
/*******************************************************************************
* Function Name  : mscCmd_Write
* Description    : mscCmd_Write
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/  
bool mscCmd_Write(void)
{
	//debg("mscCmd_Write\n");
	//u8 *prxbuf = scsi.prxbuf;
	u32 lba = usb_dev_ctl.MscCmd.Address;
	u32 sec = usb_dev_ctl.MscCmd.Length ;
	usb_dev_ctl.MscCmd.Residue = usb_dev_ctl.MscCmd.CbwTrxLength;
	//deg_Printf("lba:%d,sec:%d\n",lba,sec);
	//设备在线？
	if(!dev_online(usb_dev_ctl.MscCmd.CbwLun)){
		mscSet_Status(MEDIUM_NOT_PRESENT);
		usb_dev_ctl.scsi.bstall = 1;
		return false;
	}
	dev_busy();	
	//ledsta = RECORD;
	do
	{	
		if(dusb_msc_rx((u32)usb_dev_ctl.scsi.prxbuf,512)<0)
			return false;
		if((*usb_dev_ctl.scsi.disk_wr_func)(usb_dev_ctl.scsi.prxbuf,lba,1)<0)
		{
			return false;	
		}
		usb_dev_ctl.MscCmd.Residue -= 512;
		lba++;
		hal_wdtClear();
	}while(--sec);	
	scsi_bulk_clr_rx();           //Flush FIFO, Clear RxRdy(接收下一个包)
	return  true;
}

/*******************************************************************************
* Function Name  : scsi_cmd_analysis
* Description    : scsi_cmd_analysis
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
bool scsi_cmd_analysis(void)
{
	usb_dev_ctl.MscCmd.Residue = 0;
	usb_dev_ctl.scsi.bstall = 0;
	usb_dev_ctl.scsi.cswsta = 0;
	//deg_Printf("MscCmd.CbwFlag:%x,MscCmd.OpCode:%x\n",usb_dev_ctl.MscCmd.CbwFlag,usb_dev_ctl.MscCmd.OpCode);
	//no data 
	if(usb_dev_ctl.MscCmd.CbwTrxLength == 0)
	{
		
		scsi_bulk_clr_rx();
		if (usb_dev_ctl.MscCmd.OpCode == UFI_TEST_UNIT_READY) {
			mscCmd_TestUnitReady(); 
		} else if (usb_dev_ctl.MscCmd.OpCode == UFI_STOP_START_UNIT) {
			mscCmd_StartStopUnit();
		} else if (usb_dev_ctl.MscCmd.OpCode == UFI_PREVENT_ALLOW_MEDIUM_REMOVAL) {
			mscCmd_PreventAllowMediumRemoval();
		} else if (usb_dev_ctl.MscCmd.OpCode == UFI_VERIFY_10) {
			mscCmd_Verify();
		} else if (usb_dev_ctl.MscCmd.OpCode == UFI_USER_UFMODE) {
			mscCmd_ufmod();
		}
		/*else if (usb_dev_ctl.MscCmd.OpCode == UFI_USER_RETURNMASK) {
			//cbw_returnmask();
			usb_dev_ctl.returnmaskcombo = 1;
			//return false;
		}*/
		else if (usb_dev_ctl.MscCmd.OpCode == UFI_USER_UPDATERTC) {
			cbw_updatartc();
		}		
		else {
			mscSet_Status(INVALID_FIELD_IN_COMMAND);
			usb_dev_ctl.scsi.bstall = 1;
		}
		ScsiStallIn(usb_dev_ctl.scsi.bstall);
	}

	//data in
	else if(0x80 == usb_dev_ctl.MscCmd.CbwFlag){
		scsi_bulk_clr_rxkick();
		if(usb_dev_ctl.MscCmd.OpCode == UFI_READ_10) {
			mscCmd_Read();
		} else if(usb_dev_ctl.MscCmd.OpCode == UFI_REQUEST_SENSE) {
			mscCmd_RequestSense();
		} else if (usb_dev_ctl.MscCmd.OpCode == UFI_INQUIRY) {
			mscCmd_Inquiry();
		} else if (usb_dev_ctl.MscCmd.OpCode == UFI_MODE_SENSE_06) {
			mscCmd_ModeSense6();
		} else if (usb_dev_ctl.MscCmd.OpCode == UFI_READ_FORMAT_CAPACITY) {
			mscCmd_ReadFormatCapacity();
		} else if (usb_dev_ctl.MscCmd.OpCode == UFI_READ_CAPACITY) {
			mscCmd_ReadCapacity();
		} else if (usb_dev_ctl.MscCmd.OpCode == UFI_USER_UFMODE) {
			mscCmd_ufmod();
		}
		else{
			usb_dev_ctl.MscCmd.Residue = usb_dev_ctl.MscCmd.CbwTrxLength;
			mscSet_Status(INVALID_FIELD_IN_COMMAND);
			usb_dev_ctl.scsi.bstall = 1;
		}
		ScsiStallIn(usb_dev_ctl.scsi.bstall);
	}
	//data out
	else{
		if ((usb_dev_ctl.MscCmd.OpCode != UFI_WRITE_10 && usb_dev_ctl.MscCmd.OpCode != UFI_USER_UFMODE)) {  
			scsi_bulk_clr_rx();
		}
		
		if (usb_dev_ctl.MscCmd.OpCode == UFI_WRITE_10) {
			mscCmd_Write();
		} else if (usb_dev_ctl.MscCmd.OpCode == UFI_USER_UFMODE) {
			mscCmd_ufmod();
		} else {
			usb_dev_ctl.MscCmd.Residue = usb_dev_ctl.MscCmd.CbwTrxLength;
			mscSet_Status(INVALID_FIELD_IN_COMMAND);
			usb_dev_ctl.scsi.bstall = 1;
		}
		ScsiStallout(usb_dev_ctl.scsi.bstall);
	}
	sent_csw();
	return true;
}

/*******************************************************************************
* Function Name  : scsi_cmd_analysis
* Description    : scsi_cmd_analysis
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
#define __LSB__(a,b,c,d) (((u32)d << 24) | ((u32)c << 16) | ((u32)b << 8) | ((u32)a << 0))
#define __MSB__(a,b,c,d) (((u32)a << 24) | ((u32)b << 16) | ((u32)c << 8) | ((u32)d << 0))
bool get_cbw(void)
{
	u8* prxbuf = usb_dev_ctl.scsi.prxbuf;
	//if(!((XSFR_USB20_SIE_EPXRXCNTH  == 0x00) && (XSFR_USB20_SIE_EPXRXCNTL  == 0x1f)))
	//	return false;
	hx330x_sysDcacheFlush((u32)usb_dev_ctl.scsi.prxbuf,32);	
	//debgbuf(scsi.prxbuf,31);

	
	//decode cbw
	if(__LSB__(prxbuf[0],prxbuf[1],prxbuf[2],prxbuf[3]) != SCSI_CBW_SIG)
	{
		deg_Printf("cbw sig err\n");
		debgbuf(prxbuf,31);
		return false;	
	}	
	//MscCmd.CbwTag
	usb_dev_ctl.MscCmd.CbwTag 		= __LSB__(prxbuf[4],prxbuf[5],prxbuf[6],prxbuf[7]);
	//MscCmd.CbwTrxLength
	usb_dev_ctl.MscCmd.CbwTrxLength = __LSB__(prxbuf[8],prxbuf[9],prxbuf[10],prxbuf[11]);
	//MscCmd.CbwFlag
	usb_dev_ctl.MscCmd.CbwFlag 		= prxbuf[12];
	//Lun
	usb_dev_ctl.MscCmd.CbwLun 		= prxbuf[13];
	//MscCmd.CbwCbLen
	//MscCmd.OpCode
	usb_dev_ctl.MscCmd.OpCode 		= prxbuf[15];
	//MscCmd.SubOpCode
	usb_dev_ctl.MscCmd.SubOpCode 	= prxbuf[16];
	//MscCmd.Address
	usb_dev_ctl.MscCmd.Address 		= __MSB__(prxbuf[17],prxbuf[18],prxbuf[19],prxbuf[20]);
	//MscCmd.SubEx
	usb_dev_ctl.MscCmd.SubEx 		= prxbuf[21];
	//MscCmd.Length
	usb_dev_ctl.MscCmd.Length 		= __MSB__(0,0,prxbuf[22],prxbuf[23]);
	//MscCmd.SubEx1
	usb_dev_ctl.MscCmd.SubEx1 		= prxbuf[24];
	//MscCmd.SubEx2
	usb_dev_ctl.MscCmd.SubEx2 		= __MSB__(prxbuf[25],prxbuf[26],prxbuf[27],prxbuf[28]);
	//MscCmd.SubEx3
	usb_dev_ctl.MscCmd.SubEx3 		= __MSB__(0,0,prxbuf[29],prxbuf[30]);

    usb_dev_ctl.MscCmd.Func1 		= __LSB__(prxbuf[16],prxbuf[17],prxbuf[18],prxbuf[19]);
    usb_dev_ctl.MscCmd.DataAddr 	= __LSB__(prxbuf[20],prxbuf[21],prxbuf[22],prxbuf[23]);
    usb_dev_ctl.MscCmd.Func2 		= __LSB__(prxbuf[24],prxbuf[25],prxbuf[26],prxbuf[27]);
    usb_dev_ctl.MscCmd.Param 		= __MSB__(0,prxbuf[30],prxbuf[29],prxbuf[28]);
	//debg(".OpCode:%x,\n",MscCmd.OpCode);//debgbuf(prxbuf, 31);
	
	
	return true;
}
#undef __LSB__
#undef __MSB__

/*******************************************************************************
* Function Name  : scsi_cmd_analysis
* Description    : scsi_cmd_analysis
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
bool rbc_rec_pkg(void)
{
	hx330x_intCriticalInit();
	hx330x_intCriticalEnter();
	
	XSFR_USB20_SIE_EPS = (usb_dev_ctl.scsi.epxin  & 0x7f);               //选用EP1做为 Mass

	u8 ret = false;
	if(XSFR_USB20_SIE_EPRX_CTRL0 & BIT(0)){
		if(!((XSFR_USB20_SIE_EPXRXCNTH  == 0x00) && (XSFR_USB20_SIE_EPXRXCNTL  == 0x1f)))
			ret =  false;
		else
			ret =  true;
	}	

	hx330x_intCriticalExit();
	return ret;
}
/*******************************************************************************
* Function Name  : rbc_process
* Description    : rbc_process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/ 
bool rbc_process(void)
{
	//if(read_usb_reg(RxCSR1) & BIT(0)){
	if(rbc_rec_pkg()){
		if(get_cbw()){
			return scsi_cmd_analysis();
		}
		else{
			debg("cbw err\n");
			scsi_bulk_clr_rx();
			ScsiStallAll();
		}
	}
	
	return true;
}




















