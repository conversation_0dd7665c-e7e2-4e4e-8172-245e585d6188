/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef HAL_UART_H
#define HAL_UART_H


void uart_PutChar_n(u8 c);
void uart_PutStr(u8 *p_str) ;
void uart_Put_hex(DWORD dwHex, BOOL bMode);
void uart_Put_udec(DWORD dwDec);
void uart_Put_dec(long lDec);
void uart_Printf(const char *pszStr, ...);
void uart_PrintfBuf(void *pBuf, int iDataLen);


#if HAL_CFG_EN_DBG

#define deg(...)              uart_Printf(__VA_ARGS__)
#define deg_Printf(...)       uart_Printf(__VA_ARGS__)
#define deg_PrintfBuf(p,i)    uart_PrintfBuf(p,i)
#define my_printf(...)		  uart_Printf(__VA_ARGS__)
#define debgbuf(p,i)          uart_PrintfBuf(p,i)
#define debgchar(c)		      uart_PutChar_n(c)
#define deg_Register(x)	      uart_Printf(#x"=%X\n",x)
#define debgreg(x)			  uart_Printf(#x"=%X\n",x)
#define MARK()				  uart_Printf("%d\n",__LINE__)
#define ENTER()			      uart_Printf("[ENTER] %s\n",__func__)
#define LEAVE()			      uart_Printf("[EXIT] %s\n",__func__)
#define deg_err(fmt,...)	  uart_Printf("[ERR]:%s,%d  "fmt"",__FUNCTION__,__LINE__,##__VA_ARGS__)
#define deg_wrn(fmt,...)      uart_Printf("[WRN]:%s,%d  "fmt"",__FUNCTION__,__LINE__,##__VA_ARGS__)
#define deg_msg(fmt,...)      uart_Printf(fmt,##__VA_ARGS__)
#else
#define  deg(...)             
#define  deg_Printf(...)      
#define  deg_PrintfBuf(p,i)   
#define  my_printf(...)		   
#define  debgbuf(p,i)         
#define  debgchar(c)			  
#define  deg_Register(x)		
#define  debgreg(x)			   
#define  MARK()				
#define  ENTER()				  
#define  LEAVE()				 
#define deg_err(fmt,...)	  
#define deg_wrn(fmt,...)      
#define deg_msg(fmt,...)      
#endif


/*******************************************************************************
* Function Name  : hal_uartInit
* Description    : hal layer.uart initial
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hal_uartInit(void);
/*******************************************************************************
* Function Name  : hal_uartSendData
* Description    : hal layer.uart  send data
* Input          : u8 data : data
* Output         : None
* Return         : None
*******************************************************************************/
void hal_uartSendData(u8 data);
/*******************************************************************************
* Function Name  : hal_uartRXIsrRegister
* Description    : hal layer.uart  rx ISR register
* Input          : void (*isr)(u8 data) : isr
* Output         : None
* Return         : None
*******************************************************************************/
void hal_uartRXIsrRegister(void (*isr)(u8 data));
/*******************************************************************************
* Function Name  : hal_uartIOShare
* Description    : hal layer.uart io share flag
* Input          : none
* Output         : None
* Return         : u8 : 0->current is uart using,1->other using
*******************************************************************************/
u8 hal_uartIOShare(void);
/*******************************************************************************
* Function Name  : hal_uartPrintString
* Description    : uart printf string
* Input          : 
*                  
* Output         : None
* Return         : None
*******************************************************************************/
void hal_uartPrintString(char *string);
















#endif
