/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_ITEM_MANAGE_H
#define UI_WIN_ITEM_MANAGE_H

#define MAX_ROW_NUM       10
#define MAX_COLUMN_NUM    5
#define MAX_ITEM_NUM      (MAX_ROW_NUM*MAX_COLUMN_NUM)
typedef winHandle (*itemCreateFunc)(s16 x0,s16 y0,u16 width,u16 height, u16 style,uiColor rimColor, u8 round_type);
typedef u32 (*getResInfor)(u32 item,resID* image,resID* str);
typedef u32 (*getResInforEx)(u32 item,resID* image,resID* str,resID* selectImage,resID* selectStr);


typedef struct
{
	u16 itemWidth;
	u16 itemGap;
	u16 firstItemGap;
	u16 itemWSum;
}wItemInfor;

typedef struct
{
	uiWidgetObj 	widget;
	winHandle 		itemHandle[MAX_ITEM_NUM];
	u16 			itemSum;
	u16 			itemHeight;
	u16 			itemGap;
	u16 			firstItemGap;
	wItemInfor 		rowItem[MAX_ROW_NUM];
	getResInfor 	getRes;
	getResInforEx 	getResEx;
	u32 			resSum;
	u32 			currentRes;
	u16             style;
}uiItemManageObj;

/*******************************************************************************
* Function Name  : uiItemManageCreate
* Description    : uiItemManageCreate
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiItemManageCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);
/*******************************************************************************
* Function Name  : uiItemManageSetItemHeight
* Description    : uiItemManageSetItemHeight
* Input          : winHandle hitemManage,u16 itemHeight
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageSetItemHeight(winHandle hitemManage,u16 itemHeight);
/*******************************************************************************
* Function Name  : uiItemManageSetHeightAvgGap
* Description    : uiItemManageSetHeightAvgGap
* Input          : winHandle hitemManage,u16 itemHeight
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageSetHeightAvgGap(winHandle hitemManage,u16 itemHeight);
/*******************************************************************************
* Function Name  : uiItemManageSetHeightNotGap
* Description    : uiItemManageSetHeightNotGap
* Input          : winHandle hitemManage,u16 itemHeight
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageSetHeightNotGap(winHandle hitemManage,u16 itemHeight);
/*******************************************************************************
* Function Name  : uiItemManageSetRowSum
* Description    : uiItemManageSetRowSum
* Input          : winHandle hitemManage,u16 itemSum,u16 itemHeight
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageSetRowSum(winHandle hitemManage,u16 itemSum,u16 itemHeight);
/*******************************************************************************
* Function Name  : uiItemManageSetColumnSumWithGap
* Description    : uiItemManageSetColumnSumWithGap
* Input          : winHandle hitemManage,u8 rowNum,u16 itemSum,u16 itemWidth,u16 gap
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageSetColumnSumWithGap(winHandle hitemManage,u16 rowNum,u16 itemSum,u16 itemWidth,u16 gap);
/*******************************************************************************
* Function Name  : uiItemManageCreateItem
* Description    : uiItemManageCreateItem
* Input          : winHandle hitemManage,itemCreateFunc func,getResInfor getRes,u32 resItemSum
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageCreateItem(winHandle hitemManage,itemCreateFunc func,getResInfor getRes,u32 resItemSum);
/*******************************************************************************
* Function Name  : uiItemManageUpdateRes
* Description    : uiItemManageUpdateRes
* Input          : winHandle hitemManage,u32 resItemSum,u32 curResItem
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageUpdateRes(winHandle hitemManage,u32 resItemSum,u32 curResItem);
/*******************************************************************************
* Function Name  : uiItemManageUpdateAllItem
* Description    : uiItemManageUpdateAllItem
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageUpdateAllItem(winHandle hitemManage);
/*******************************************************************************
* Function Name  : uiItemManageSetResInforFuncEx
* Description    : uiItemManageSetResInforFuncEx
* Input          : winHandle hitemManage,getResInforEx getResEx
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetResInforFuncEx(winHandle hitemManage,getResInforEx getResEx);
/*******************************************************************************
* Function Name  : uiItemManageSetCurItem
* Description    : uiItemManageSetCurItem
* Input          : winHandle hitemManage,u32 itemResNum
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetCurItem(winHandle hitemManage,u32 itemResNum);
/*******************************************************************************
* Function Name  : uiItemManageUpdateCurItem
* Description    : uiItemManageUpdateCurItem
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageUpdateCurItem(winHandle hitemManage);
/*******************************************************************************
* Function Name  : uiItemManageNextItem
* Description    : uiItemManageNextItem
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageNextItem(winHandle hitemManage);
/*******************************************************************************
* Function Name  : uiItemManagePreItem
* Description    : uiItemManagePreItem
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManagePreItem(winHandle hitemManage);
/*******************************************************************************
* Function Name  : uiItemManageNextPage
* Description    : uiItemManageNextPage
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageNextPage(winHandle hitemManage);
/*******************************************************************************
* Function Name  : uiItemManagePrePage
* Description    : uiItemManagePrePage
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManagePrePage(winHandle hitemManage);
/*******************************************************************************
* Function Name  : uiItemManageGetCurrentItem
* Description    : uiItemManageGetCurrentItem
* Input          : winHandle hitemManage
* Output         : none                                            
* Return         : none 
*******************************************************************************/
u32 uiItemManageGetCurrentItem(winHandle hitemManage);
/*******************************************************************************
* Function Name  : uiItemManageSetCharInfor
* Description    : uiItemManageSetCharInfor
* Input          : winHandle hitemManage,charFont font,u8 strAlign,uiColor fontColor
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetCharInfor(winHandle hitemManage,charFont font,u8 strAlign,uiColor fontColor);
/*******************************************************************************
* Function Name  : uiItemManageSetSelectColor
* Description    : uiItemManageSetSelectColor
* Input          : winHandle hitemManage,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetSelectColor(winHandle hitemManage,uiColor color);
/*******************************************************************************
* Function Name  : uiItemManageSetSelectImage
* Description    : uiItemManageSetSelectImage
* Input          : winHandle hitemManage,resID image
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetSelectImage(winHandle hitemManage,resID image);
/*******************************************************************************
* Function Name  : uiItemManageSetUnselectColor
* Description    : uiItemManageSetUnselectColor
* Input          : winHandle hitemManage,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetUnselectColor(winHandle hitemManage,uiColor color);
/*******************************************************************************
* Function Name  : uiItemManageSetUnselectImage
* Description    : uiItemManageSetUnselectImage
* Input          : winHandle hitemManage,resID image
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetUnselectImage(winHandle hitemManage,resID image);
/*******************************************************************************
* Function Name  : uiItemManageGetTouchInfor
* Description    : uiItemManageGetTouchInfor
* Input          : winHandle hitemManage,touchInfor* infor
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageGetTouchInfor(winHandle hitemManage,touchInfor* infor);
/*******************************************************************************
* Function Name  : uiItemManageSetSelectColorEx
* Description    : uiItemManageSetSelectColorEx
* Input          : winHandle hitemManage,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetSelectColorEx(winHandle hitemManage,charFont font,u8 strAlign, uiColor fontcolor, uiColor bgcolor);
/*******************************************************************************
* Function Name  : uiItemManageSetUnselectColorEx
* Description    : uiItemManageSetUnselectColorEx
* Input          : winHandle hitemManage,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiItemManageSetUnselectColorEx(winHandle hitemManage,charFont font,u8 strAlign, uiColor fontcolor, uiColor bgcolor);

#endif
