/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"



typedef struct MENU_CTL_S{
	menu*	currentMenu;
	u32 	menuOpened;
}MENU_CTL_T;
ALIGNED(4) static MENU_CTL_T	menu_ctl = {NULL, 0};
#define currentPage()    	(menu_ctl.currentMenu->pPage[menu_ctl.currentMenu->curPage])
#define getItem(n)   		(currentPage().pItem[n])
#include "sMenuItemWin.c"
/*******************************************************************************
* Function Name  : menuIsOpen
* Description    : menuIsOpen
* Input          : none
* Output         : none
* Return         : u32: 0: close, 1: open
*******************************************************************************/
u32 menuWinIsOpen(void)
{
	return menu_ctl.menuOpened;
}
/*******************************************************************************
* Function Name  : getItemResInfor
* Description    : getItemResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : u32:
*******************************************************************************/
UNUSED static u32 getItemResInfor(u32 item,u32* image,u32* str)
{
	if(image)
		*image 	= getItem(item).image;
	if(str)
		*str	= getItem(item).str;
	return 0 ;
}
/*******************************************************************************
* Function Name  : getItemResInforEx
* Description    : getItemResInforEx
* Input          : u32 item,u32* image,u32* str,u32* selectImage,u32* selectStr
* Output         : none
* Return         : u32:
*******************************************************************************/
static u32 getItemResInforEx(u32 item,u32* image,u32* str,u32* selectImage,u32* selectStr)
{
	u32 config;
	u32 selImg = INVALID_RES_ID;
	u32 selStr = INVALID_RES_ID;
	menuItem* pItem = &getItem(item);
	if(pItem->optionSum > 0)
	{
		if(pItem->configId == 0)
		{
			selStr   = pItem->pOption[0].str;
		}else
		{
			config = user_config_get(pItem->configId);
			if(pItem->optionSum==2 && (config == R_ID_STR_COM_OFF || config == R_ID_STR_COM_ON))
			{
				if(selectImage)
				{
					if(config == R_ID_STR_COM_OFF)
						selImg = R_ID_ICON_MTOFF;
					else
						selImg = R_ID_ICON_MTON;
				}
			}
			else
			{
				selImg = R_ID_ICON_MTMORE;
				selStr = config;
			}
		}
	}

	if(image)
		*image	=	pItem->image;
	if(str)
		*str	=	pItem->str;
	if(selectImage)
		*selectImage = selImg;
	if(selectStr)
		*selectStr = selStr;
	return 0;

}
/*******************************************************************************
* Function Name  : menuItemKeyMsgOk
* Description    : menuItemKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	menuItem* pItem;
	u32 	  config;
	u32 	  keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		pItem = &getItem(uiItemManageGetCurrentItem(winItem(handle,ITEM_SELECT_ID)));
		if(pItem->optionSum == 0)
			((menuItemProc)pItem->pOption)(handle,parameNum,parame);
		else if(pItem->optionSum > 1)
		{
		#if UI_SHOW_SMALL_PANEL == 0// only 2 option and one of them is "OFF" and another is "ON"
			config = user_config_get(pItem->configId);
			if(pItem->optionSum == 2 && (config == R_ID_STR_COM_OFF || config == R_ID_STR_COM_ON))
			{
				if(config==R_ID_STR_COM_OFF)
					user_config_set(pItem->configId,R_ID_STR_COM_ON);
				else
					user_config_set(pItem->configId,R_ID_STR_COM_OFF);
				user_config_cfgSys(pItem->configId);
				user_config_save();

				menuMDShow(handle);
				menuMonitorShow(handle);
				menuMicShow(handle);
				uiItemManageUpdateCurItem(winItem(handle,ITEM_SELECT_ID));
			}
			else
			#endif
				uiOpenWindow(&menuOptionWindow,0,1,pItem);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemKeyMsgUp
* Description    : menuItemKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum==1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,ITEM_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemKeyMsgDown
* Description    : menuItemKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManageNextItem(winItem(handle,ITEM_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemKeyMsgMenu
* Description    : menuItemKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		menu_ctl.currentMenu->curPage++;
		if(menu_ctl.currentMenu->curPage < menu_ctl.currentMenu->pageSum)
		{
			uiWinSetResid(winItem(handle,ITEM_MODE_ID),currentPage().selectImage);
			uiItemManageUpdateRes(winItem(handle,ITEM_SELECT_ID),currentPage().itemSum,0);
		}
		else
			uiWinDestroy(&handle);
	}
	return 0;
}



static int recordPhotoKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);// uiOpenWindow(&menuItemWindow,0,1,&MENU(record));//uiOpenWindow(&menuItemWindow,0,1,&MENU(record));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemKeyMsgMode
* Description    : menuItemKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemSysMsgSD
* Description    : menuItemSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	menuSDShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : menuSysMsgUSB
* Description    : menuSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	menuBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : menuSysMsgBattery
* Description    : menuSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		menuBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemSysMsg1s
* Description    : menuItemSysMsg1s
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemSysMsg1s(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	{

		if(uiWinIsVisible(winItem(handle,ITEM_BATERRY_ID)))
			uiWinSetVisible(winItem(handle,ITEM_BATERRY_ID),0);
		else
		{
			uiWinSetVisible(winItem(handle,ITEM_BATERRY_ID),1);
			uiWinSetResid(winItem(handle,ITEM_BATERRY_ID),R_ID_ICON_MTBATTERY5);
		}
	}
#if UI_SHOW_SMALL_PANEL == 0
	menuPoweOnTimeShow(handle,SysCtrl.powerOnTime);
#endif
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemOpenWin
* Description    : menuItemOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum < 1)
	{
		uiWinDestroy(&handle);
		return 0;
	}
	deg_Printf("[WIN]menuItemOpenWin\n");
	menu_ctl.currentMenu = (menu*)parame[0];
	menu_ctl.currentMenu->curPage = 0;
	uiItemManageSetItemHeight(winItem(handle,ITEM_SELECT_ID),Rh(35));
#if UI_SHOW_SMALL_PANEL > 0
	uiItemManageCreateItem(		winItem(handle,ITEM_SELECT_ID),uiItemCreateItemMenu,NULL,currentPage().itemSum);// no sub menu preview 
	//uiItemManageCreateItem(		winItem(handle,ITEM_SELECT_ID),uiItemCreateMenuOption,getItemResInfor,2);
#else
	uiItemManageCreateItem(		winItem(handle,ITEM_SELECT_ID),uiItemCreateMenuItemEx,NULL,currentPage().itemSum);
	
#endif
	uiItemManageSetResInforFuncEx(winItem(handle,ITEM_SELECT_ID),getItemResInforEx);
#if 1
	uiItemManageSetCharInfor(	winItem(handle,ITEM_SELECT_ID),DEFAULT_FONT,ALIGNMENT_LEFT,R_ID_PALETTE_Black);
	uiItemManageSetSelectColor(	winItem(handle,ITEM_SELECT_ID),R_ID_PALETTE_Gray);
	uiItemManageSetUnselectColor(winItem(handle,ITEM_SELECT_ID),R_ID_PALETTE_White);
#else //选中时图标颜色也改变
	uiItemManageSetSelectColorEx(winItem(handle,ITEM_SELECT_ID),DEFAULT_FONT,ALIGNMENT_LEFT, R_ID_PALETTE_Blue, R_ID_PALETTE_Gray);
	uiItemManageSetUnselectColorEx(winItem(handle,ITEM_SELECT_ID),DEFAULT_FONT,ALIGNMENT_LEFT, R_ID_PALETTE_White, R_ID_PALETTE_TransBlack);
#endif
	uiItemManageNextItem(			winItem(handle,ITEM_SELECT_ID));
	uiWinSetResid(				winItem(handle,ITEM_MODE_ID),currentPage().selectImage);
	menuResolutionShow(handle);
	menuMDShow(handle);
	menuMonitorShow(handle);
	menuIrLedShow(handle);
	menuSDShow(handle);
	menuMicShow(handle);
	menuBaterryShow(handle);
#if UI_SHOW_SMALL_PANEL == 0
	menuPoweOnTimeShow(handle,SysCtrl.powerOnTime);
#endif
	menu_ctl.menuOpened = 1;
	keyLongTypeScanModeSet(1); //no scan longtype
	task_com_LedOnOff_ctrl(0);
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemCloseWin
* Description    : menuItemCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]menuItemCloseWin\n");
	menu_ctl.currentMenu = NULL;
	menu_ctl.menuOpened	 = 0;
	keyLongTypeScanModeSet(0); //no scan longtype
	 XOSTimeDly(100);
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemWinChildClose
* Description    : menuItemWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int
*******************************************************************************/
static int menuItemWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]menuItemWinChildClose\n");
	uiWinSetResid(winItem(handle,ITEM_MODE_ID),currentPage().selectImage);
	menuResolutionShow(handle);
	menuMDShow(handle);
	menuMonitorShow(handle);
	menuIrLedShow(handle);
	menuSDShow(handle);
	menuMicShow(handle);
	menuBaterryShow(handle);
	//uiItemManageUpdateCurItem(winItem(handle,ITEM_SELECT_ID));
	uiItemManageUpdateAllItem(winItem(handle,ITEM_SELECT_ID));
#if UI_SHOW_SMALL_PANEL == 0
	menuPoweOnTimeShow(handle,SysCtrl.powerOnTime);
#endif
	return 0;
}

/*******************************************************************************
* Function Name  : menuItemTouchWin
* Description    : menuItemTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int menuItemTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("menuItemTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == ITEM_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : menuItemTouchSlideOff
* Description    : menuItemTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int menuItemTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;
	//deg_Printf("diretion:%d\n",parame[0]);
	if(parame[0] == TP_DIR_UP)
		uiItemManageNextPage(winItem(handle,ITEM_SELECT_ID));
	else if(parame[0] == TP_DIR_DOWN)
		uiItemManagePrePage(winItem(handle,ITEM_SELECT_ID));	
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	else if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	return 0;
}
ALIGNED(4) msgDealInfor menuItemMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	menuItemOpenWin},
	{SYS_CLOSE_WINDOW,	menuItemCloseWin},
	{SYS_CHILE_COLSE,	menuItemWinChildClose},
	{SYS_TOUCH_WINDOW,  menuItemTouchWin},
	{SYS_TOUCH_SLIDE_OFF,menuItemTouchSlideOff},


	{KEY_EVENT_PHOTO,		menuItemKeyMsgOk},
	{KEY_EVENT_UP,		menuItemKeyMsgDown},//menuItemKeyMsgUp
	{KEY_EVENT_DOWN,	menuItemKeyMsgUp},//menuItemKeyMsgDown

	
	{KEY_EVENT_MENU,	menuItemKeyMsgMenu},
	{KEY_EVENT_POWER,	recordPhotoKeyMsgMenu},
	
	{KEY_EVENT_MODE,	menuItemKeyMsgMode},
	{SYS_EVENT_SDC,		menuItemSysMsgSD},
	{SYS_EVENT_USBDEV,	menuSysMsgUSB},
	{SYS_EVENT_BAT,		menuSysMsgBattery},
	{SYS_EVENT_1S,		menuItemSysMsg1s},
	{EVENT_MAX,			NULL},
};

WINDOW(menuItemWindow,menuItemMsgDeal,menuItemWin)


