/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef USB_HOST_ENUM_H_
#define USB_HOST_ENUM_H_
/*******************************************************************************
* Function Name  : husb20_ep0_cfg
* Description    : husb20_ep0_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb20_ep0_cfg(void);

/*******************************************************************************
* Function Name  : husb11_ep0_cfg
* Description    : husb11_ep0_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb11_ep0_cfg(void);

/*******************************************************************************
* Function Name  : usensor_resolution_select
* Description    : usensor_resolution_select
* Input          : HUSB_HANDLE *pHusb_handle
*                  u8 format:UVC_FORMAT_MJP/UVC_FORMAT_YUV
				   u8 frame_switch: 0 - default frame，1 - 切換frame
* Output         : None
* Return         : true：选择分辨率成功，保存分辨率信息到pHusb_handle->usbsta.usensor_res
*******************************************************************************/
bool usensor_resolution_select(void *handle, u8 format,u8 frame_switch);
/*******************************************************************************
* Function Name  : husb_api_ep0_kick
* Description    : husb_api_ep0_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_ep0_kick(void *handle);

/*******************************************************************************
* Function Name  : husb_api_ep0_process
* Description    : husb_api_ep0_process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_ep0_process(void *handle);
/*******************************************************************************
* Function Name  : husb_api_ep0_uvc_switch_kick
* Description    : husb_api_ep0_uvc_switch_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_ep0_uvc_switch_kick(void* handle);
/*******************************************************************************
* Function Name  : husb_api_ep0_asterncheck_kick
* Description    : husb_api_ep0_asterncheck_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_ep0_asterncheck_kick(void* handle);

/*******************************************************************************
* Function Name  : husb_api_uvcunit_get_kick
* Description    : husb_api_uvcunit_get_kick
* Input          : void* handle
				   u8 uvcunitsel: PU_BACKLIGHT_COMPENSATION_CONTROL ~ PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL
				   u8 request:GET_CUR/GET_MIN/GET_MAX/GET_RES
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_uvcunit_get_kick(void* handle, u8 uvcunitsel, u8 request);
/*******************************************************************************
* Function Name  : husb_api_uvcunit_get_done
* Description    : husb_api_uvcunit_get_done
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u16 husb_api_uvcunit_get_done(void* handle);
/*******************************************************************************
* Function Name  : husb_api_uvcunit_get_kick
* Description    : husb_api_uvcunit_get_kick
* Input          : void* handle
				   u8 uvcunitsel: PU_BACKLIGHT_COMPENSATION_CONTROL ~ PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL
				   u16 val
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_uvcunit_set_kick(void* handle, u8 uvcunitsel, u16 val);
/*******************************************************************************
* Function Name  : husb_api_uvcunit_set_done
* Description    : husb_api_uvcunit_set_done
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_uvcunit_set_done(void* handle);

/*******************************************************************************
* Function Name  : husb_api_ep0_asterncheck_kick
* Description    : husb_api_ep0_asterncheck_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_hub_check_kick(void* handle);


#endif /* USB_HOST_ENUM_H_ */
