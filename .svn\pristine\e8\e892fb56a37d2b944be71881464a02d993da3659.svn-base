/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/

#ifndef  HAL_CFG_H
   #define  HAL_CFG_H




//debug uart output
#define  HAL_CFG_EN_DBG             	1   // debug printf

//mcu moudle function
#define  HAL_CFG_EN_IIC0            	1
#define  HAL_CFG_EN_IIC1            	1





//lcd
#define  HAL_CFG_LCD_BUFFER_NUM     	5  //hal-lcd will define the buffer using by auto check



//adc
#define  HAL_CFG_ADC_BAUDRATE       	1000000UL

// iic
#define  HAL_CFG_IIC0_BAUDRATE      	400000   // 400k
#define  HAL_CFG_IIC1_BAUDRATE      	40000   // 40k

//spi

#define  HAL_CFG_SPI_BAUD_RATE      	SPIFLASH_BAUDRATE

#define  HAL_CFG_SPI_DMA_USE        	1
#define  HAL_CFG_SPI_DMA_THROD      	4096


//auadc buffer size
//#define  HAL_CFG_PCM_BUFFER_SIZE    	8192
#define  HAL_CFG_PCM_BUFFER_NUM     	(PCM_REC_SYNC)// 24 //  16  24



//mjpeg
#define  HAL_CFG_MJPEG_HIGH_QT         0  //1:使用高量化，图像数据量小，但可能会有水波纹

#define  HAL_CFG_MJPEG_BUFFER_NUM		30//HAL_RSV_MJPEG_BUFFER_NUM)// 8  64  (256+32)//AVI_CBUF_SIZE/AVI_SHEET_SIZE  
#define  HAL_CFG_MJPEG_QULITY_AUTO  	1  // auto ajust qulity when mjpeg size is to small or big
#if HAL_CFG_MJPEG_HIGH_QT
   #define  HAL_CFG_MJPEG_VGA_SIZE_MIN     (20*1024)
   #define  HAL_CFG_MJPEG_VGA_SIZE_MAX     (35*1024)
   #define  HAL_CFG_MJPEG_720_SIZE_MIN     (30*1024)
   #define  HAL_CFG_MJPEG_720_SIZE_MAX     (45*1024)
   #define  HAL_CFG_MJPEG_1080_SIZE_MIN    (60*1024)
   #define  HAL_CFG_MJPEG_1080_SIZE_MAX    (75*1024)
#else
   #define  HAL_CFG_MJPEG_VGA_SIZE_MIN     (20*1024)
   #define  HAL_CFG_MJPEG_VGA_SIZE_MAX     (40*1024)
   #define  HAL_CFG_MJPEG_720_SIZE_MIN     (40*1024)
   #define  HAL_CFG_MJPEG_720_SIZE_MAX     (60*1024)
   #define  HAL_CFG_MJPEG_1080_SIZE_MIN    (60*1024)
   #define  HAL_CFG_MJPEG_1080_SIZE_MAX    (80*1024)
#endif




#define  HAL_CFG_OS_TIMER            	TIMER2

#define  HAL_CFG_SYNC_TIMER         	TIMER1   // sync to video play & video record

#if (CURRENT_CHIP == FPGA)
#define  HAL_AVI_FRAMRATE   			25
#else
#define  HAL_AVI_FRAMRATE   			30
#endif

#define  HAL_CFG_PQTOOL_EN              0   // support pqtool



#endif

