/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if SENSOR_DVP_VGA_IT03A1 > 0


SENSOR_INIT_SECTION const u8 IT03A1InitTable[]=
{    
	0x00, 0x00,             // global set : page #0                                                                                          
	0x08, 0x01,	//soft reset                                                                                        
	0x09, 0x02,	//pad output enable, soft power down                                                                
	0x10, 0x44,                                                                                                     
	0x21, 0x00,	//03 //mclk divider 04 for 48Mhz, vh mirror                                                         
	0x30, 0x00,	//vga mode                
	//0x31, HBLANK_BASE >> 8,  //hb [11:8]
	//0x32, HBLANK_BASE & 0xff,//hb [7:0] 
	//0x33, VBLANK_BASE >> 8,  //vb [11:8]
	//0x34, VBLANK_BASE & 0xff,//vb [7:0] 
	0x60, 0x13,	//vsync active low, hsync active high, pclk rising(catch falling)                                   
	0x68, 0x03,	//y-cb-y-cr order                                                                                   
	0x70, 0x91,	//windowing                                                                                                                                                                                       
	0x80, 0x21,	//11 //memory                                                                                       
	0x81, 0x05,	//04                                                                                                                                                                                                                                                                
	0x00, 0x01,             // analog timing : page #1                                                                                          
	0x10, 0x11,                                                                                                     
	0x11, 0x05,                                                                                                     
	0x20, 0xa1,                                                                                                     
	0x30, 0x94,                                                                                                     
	0x31, 0x00,                                                                                                     
	0x40, 0x0e,	//vddio 1.8v 0e, vddio 2.8v 0d                                                                                                                                                                                                                                                                     
	0x00, 0x02,    // pixel timing : page #2                                                                                                 
	0x10, 0x80,                                                                                                     
	0x11, 0x0b,                                                                                                     
	0x12, 0x90,                                                                                                     
	0x20, 0xd2,                                                                                                                                                                                                                                                                                             
	0x00, 0x08,	//ispp pre processing  // ispp : page #08                                                                             
	0x10, 0x03,	//global digital gain control                                                                       
	0x80, 0x01,	//blc control                                                                                       
	0x81, 0x00,	//10 //fixed blc offset    
	0xe8, 0x04,                                                                                                                                                                                                                                                           
	0x00, 0x0a,	//ispp lsc    // ispp : page #0a                                                                                      
	0x10, 0x01,	//lsc control                                                                                       
	0x28, 0x00,                                                                                                     
	0x29, 0x01,                                                                                                     
	0x2a, 0x00,                                                                                                     
	0x2b, 0x00,                                                                                                     
	0x2c, 0x00,                                                                                                     
	0x2d, 0x00,                                                                                                     
	0x30, 0x22,                                                                                                     
	0x31, 0x21,                                                                                                                                                                                                                                                                                     
	0x00, 0x0c,	//ispp dpc     // ispp : page #0c                                                                                     
	0x10, 0x01,	//dpc control                                                                                       
	0x11, 0x11,	//dpc option                                                                                        
	0x18, 0x08,	//dpc threshold                                                                                     
	0x19, 0x04,
	0x80, 0x01,	//nr control                                                                                        
	0x81, 0x98,	//33 //nr option                                                                                    
	0x88, 0x06,	//04 04 //nr threshold                                                                              
	0x89, 0x00,	//08 04                                                                                             
	0x8a, 0x00,	//10 10                                                                                             
	0x8b, 0x05,	//05                                                                                                
	0x8c, 0x05,	//08                                                                                                
	0xe0, 0x10,	//outdoor to normal point                                                                           
	0xe1, 0x30,	//40 //normal to dark point                                                                                                                                                                                                                                                               
	0x00, 0x0e,	//ispp color  // ispp : page #0e                                                                                       
	0x11, 0x68,	//intoption                                                                                         
	0x90, 0x23,	//33 //37 //33 //b3 //a3 //color matrix a                                                           
	0x91, 0x29,	//35 //3f //45 //46 //2b                                                                            
	0x92, 0xd7,	//cf //ca //c9 //c8 //c8                                                                            
	0x93, 0x00,	//fc //f7 //f2 //f2 //0d                                                                            
	0x94, 0x10,	//0e //12 //0b //0a //07                                                                            
	0x95, 0x26,	//32 //32 //33 //34 //33                                                                            
	0x96, 0x0a,	//00 //fc //02 //02 //06                                                                            
	0x97, 0xf3,	//f3 //f1 //f5 //ee //fb                                                                            
	0x98, 0xd7,	//d1 //d1 //d0 //d7 //d7                                                                            
	0x99, 0x36,	//3c //3e //3b //3b //2e                                                                            
	0xa1, 0x03,	//color matrix b                                                                                    
	0xa2, 0x00,                                                                                                     
	0xa3, 0xfd,                                                                                                     
	0xa4, 0xfc,                                                                                                     
	0xa5, 0x00,                                                                                                     
	0xa6, 0x04,                                                                                                     
	0xa7, 0xf0,                                                                                                     
	0xa8, 0x00,                                                                                                     
	0xa9, 0x10,                                                                                                                                                                                                                                                                                    
	0x00, 0x10,	//ispp gamma     // ispp : page #10                                                                                    
	0x10, 0x01,	//gamma control                                                                                     
	0x20, 0x00,                                                                                                     
	0x21, 0x04,	//08                                                                                                
	0x22, 0x0b,	//12                                                                                                
	0x23, 0x24,	//2a                                                                                                
	0x24, 0x49,	//4d                                                                                                
	0x25, 0x66,	//68                                                                                                
	0x26, 0x7c,	//7e                                                                                                
	0x27, 0x8d,	//8f                                                                                                
	0x28, 0x9c,	//9d                                                                                                
	0x29, 0xaa,	//aa                                                                                                
	0x2a, 0xb6,	//b6                                                                                                
	0x2b, 0xcb,	//ca                                                                                                
	0x2c, 0xdd,	//dc                                                                                                
	0x2d, 0xef,	//ef                                                                                                
	0x2e, 0xff,	//ff                                                                                                                                                                                                                                                                               
	0x00, 0x12,	//ispp ee    // ispp : page #12                                                                                        
	0x20, 0x1b,	//18 12 //p edge gain                                                                               
	0x21, 0x20,	//28 1c //n edge gain                                                                               
	0x22, 0x11,	//pn edge coring                                                                                    
	0x23, 0x7f,	//p edge clip                                                                                       
	0x24, 0x7f,	//n edge clip                                                                                       
	0x29, 0x00,	//10 //edge value start                                                                             
	0x2a, 0x8f,	//88                                                                                                
	0x2e, 0x34,	//new edge suppression                                                                              
	0x2f, 0x10,                                                                                                     
	0x50, 0x82,	//82 //h edge control                                                                               
	0x51, 0x80,                                                                                                     
	0x52, 0xc9,                                                                                                     
	0x53, 0x80,                                                                                                     
	0x54, 0x33,                                                                                                     
	0x60, 0x11,	//1b //09 //color cbcr coring                                                                       
	0x61, 0x00,                                                                                                     
	0x80, 0x01,                                                                                                     
	0x8e, 0x32,//new	//30 //color suppression                                                                      
	0x8f, 0x80,                                                                                                     
	0x90, 0x11,	//10 //contrast control                                                                             
	0x91, 0x00,	//08 //brightness control                                                                           
	0x92, 0xf0,	//y clip                                                                                            
	0x93, 0x00,                                                                                                     
	0x98, 0x1a,	//1f //1f //upper b color saturation                                                                
	0x99, 0x1c,	//1f //1f //upper r color saturation                                                                
	0x9a, 0x19,	//10 //1f //lower b color saturation                                                                
	0x9b, 0x1c,	//1f //1f //lower r color saturation                                                                                                                                                                                                                                                          
	0x00, 0x18,	//ispp aeg control   // ispp : page #18                                                                                
	0x10, 0x80,	//aegc control                                                                                      
	0x18, 0x00,	//ae lock range                                                                                     
	0x20, 0x7d,	//step                                                                                              
	0x21, 0x04,	//max shutter                                                                                                                                                                                                      
	0x40, 0x00,	//pixel shutter                                                                                     
	0x41, 0x01,                                                                                                     
	0x50, 0x02,	//analog max gain                                                                                   
	0x51, 0x38,	//max gain                                                                                          
	0x52, 0x08,	//min gain                                                                                          
	0x61, 0x00,	//dark offset                                                                                       
	0x80, 0x80,	//60 //70 //80 //50 //ae target                                                                     
	0x82, 0x80,	//60 //70 //80 //50                                                                                 
	0xa0, 0x00,	//00 //ae weight                                                                                                                                                                                                                                                          
	0x00, 0x1a,	//ispp awb control // ispp : page #1a                                                                                   
	0x10, 0x00,                                                                                                     
	0x20, 0x80,	//80 //7e //cb level                                                                                
	0x21, 0x80,	//81 //7e //80 //cr level                                                                           
	0x30, 0xf0,	//a8 //a8 //ff //ff //cr top                                                                        
	0x31, 0x88,	//98 //00 //00 //80 //cr bottom                                                                     
	0x32, 0xc8,	//c0 //ff //ff //e0 //cb top                                                                        
	0x33, 0x70,	//b0 //b0 //00 //00 //cb bottom                                                                     
	0x40, 0x04,	//47 //8e //23 //outdoor start                                                                      
	0x41, 0x08,	//47 //8e //23 //outdoor end                                                                        
	0x42, 0xa8,	//b2 //outdoor fixed cr gain                                                                        
	0x43, 0xb0,	//aa //outdoor fixed cb gain                                                                        
	0x50, 0x89,	//to high boundary cr                                                                               
	0x51, 0xde,	//to high bounadry cb                                                                               
	0x52, 0x89,	//to low boundary cr                                                                                
	0x53, 0xde,	//to low boundary cb                                                                                
	0x60, 0xea,                                                                                                     
	0x61, 0xb0,                                                                                                     
	0x80, 0xa0,	//white pixel domain                                                                                
	0x81, 0x40,                                                                                                     
	0x82, 0xa0,                                                                                                     
	0x83, 0x40,                                                                                                     
	0x84, 0xa0,                                                                                                     
	0x85, 0x40,                                                                                                     
	0x86, 0xf0,	//c0                                                                                                
	0x87, 0x20,                                                                                                     
	0x88, 0x05,	//                                                                                                  
	0x89, 0x30,                                                                                                     
	0x8d, 0xac,	//                                                                                                  
	0x8e, 0x28,                                                                                                     
	0x8f, 0xff,                                                                                                                                                                                                                                                                                                
	0x00, 0x00,      // operation enable                                                                                                
	0x20, 0x01,                                                                                                                                                                                                                                                                                                                                    
	0x00, 0x1a,	//ispp awb control	                                                                                
	0x60, 0xea,                                                                                                     
	0x61, 0xb0,                                                                                                     
	0x10, 0xb3,
	0x00, 0x00,
	
	SENSOR_TAB_END
};

static void IT03A1_rotate(u32 r)
{
	unsigned char buf[2];
	buf[0] = 0x21;
	buf[1] = 0x00|(r<<0);
	
//	sensor_iic_enable();
//	sensor_iic_info();		
	sensor_iic_write(buf);

//  sensor_iic_disable();		
}

static void IT03A1_hvblank(u32 h,u32 v)
{
	u32 i;
	const u8 t[][2] = {
		{0x31,h >> 8},//HB[11:8]
		{0x32,h & 0xff},//HB[7:0]
		{0x33,v >> 8},//VB[11:8]
		{0x34,v & 0xff},//VB[7:0]
	};
//	sensor_iic_enable();
//	sensor_iic_info();		
	for(i=0;i<4;i++)
		sensor_iic_write((u8 *)&t[i][0]);
//  sensor_iic_disable();			
//	return (h<<16)|v;
}

SENSOR_OP_SECTION const Sensor_Adpt_T it03a1_adpt = 
{
	.typ 				= CSI_TYPE_YUV422| CSI_TYPE_DVP,// csi type: 10; 8	

#if  (CURRENT_CHIP == FPGA)
	.mclk 				= 24000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,  //mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#else
	.mclk 				= 24000000,			//mclk set
	.mclk_src			= MCLK_SRC_SYSPLL,	//mclk src: MCLK_SRC_SYSPLL, MCLK_SRC_USBPLL
	.pclk_dig_fir_step 	= 0,				//pclk digital filter :0 - disable filter, 1 - enable 2 steps filter,2 - enable 3 steps filter, 3 - disable PCLK OUTPUT
	.pclk_ana_fir_step	= 0,				//pclk analog filter :4'b0xxx： diable， 4'b1xxx: enable
	.pclk_inv_en 		= 0,				//pclk invert: 0 - not invert, 1 - invert
	.csi_tun 			= 0,				//csi clk tune: 0x00~0x0f: + 1~ +16steps, 0x10~0x1f: -1 ~ -16 steps
#endif
	//sensor input -> sensor crop -> csi input
	.senPixelw          = 640, 			//sensor input width
	.senPixelh          = 480,				//sensor input height
	.senCropW_St        = 0,				//sensor crop width start
	.senCropW_Ed        = 640,				//sensor crop width end
	.senCropH_St        = 0,				//sensor crop height start
	.senCropH_Ed        = 480,				//sensor crop height end
	.senCropMode        = CSI_PASS_MODE,	//sensor crop mode: CSI_PASS_MODE, CSI_CROP_MODE , CSI_DIV2_MODE, CSI_CROP_DIV2_MODE

	.pixelw 			= 640,				//csi input width
	.pixelh				= 480,				//csi input height
	.hsyn 				= 1,				//1: hsync valid high, 0: hsync valid low
	.vsyn 				= 1,				//1: vsync valid high, 0: vsync valid low
	.colrarray 			= CSI_PRIORITY_Y0CBY1CR,//RAW: CSI_PRIORITY_RGGB, CSI_PRIORITY_GRBG, CSI_PRIORITY_BGGR, CSI_PRIORITY_GBRG
											//YUV422: CSI_PRIORITY_CBY0CRY1, CSI_PRIORITY_CRY0CBY1, CSI_PRIORITY_Y0CBY1CR, CSI_PRIORITY_Y0CRY1CB

	.sensorCore			= SYS_VOL_V1_5,		//VDDSENCORE: SYS_VOL_V1_2 ~ SYS_VOL_V3_3
	.sensorIo			= SYS_VOL_V3_1,		//VDDSENIO: SYS_VOL_V1_2 ~ SYS_VOL_V3_56	

	.mipi_adapt			= {
		.lanes			= 1,			//mipi lane num
		.raw_bit		= CSI_TYPE_RAW10,	//10/8: RAW10/RAW8
		.dphy_pll		= PLL_CLK/5,
		.csi_pclk		= PLL_CLK/8,
		.tclk_settle	= 17,			//TCLK_SETTLE_TIME  = tclk_settle*(1/dphy_pll)
		.tclk_miss		= 4,			//TCLK_MISS_TIME	= tclk_miss*(1/dphy_pll)
		.tclk_prepare	= 2,			//TCLK_PREPARE_TIME = tclk_prepare*(1/dphy_pll)
		.ths_settle		= 2,			//THS_SETTLE_TIME  	= ths_settle*(1/dphy_pll)
		.ths_skip		= 6,			//THS_SKIP_TIME		= ths_skip*(1/dphy_pll)
		.ths_dtermen	= 4,			//THS_DTERMEN_TIME 	= ths_dtermen*(1/dphy_pll)
		.hsa			= 10,				//HSA_TIME			= hsa*(1/csi_pclk)
		.hbp			= 20,				//HBP_TIME			= hbp*(1/csi_pclk)
		.hsd			= 200,				//HSD_TIME			= hsd*(1/csi_pclk)
		.hlines			= 30,
		.vsa_lines		= 3,
		.vbp_lines		= 5,
		.vfp_lines		= 7,
		.vactive_lines	= 0x50
	},
	.rotate_adapt 		= {0},

	.hvb_adapt = {
		.pclk			= 48000000,			//csi pclk input
		.v_len			= 800,				//sensor v_len = height + vblank
		.step_val		= 0,				//auto cal
		.step_max		= 0,				//auto cal
		.down_fps_mode	= 0xff,				//0,1,hvb down_fps; 2: exp down_fps, 0xff: turn off down_fps
#if  (CURRENT_CHIP == FPGA)
		.fps			= 15,				//sensor fps set
#else
		.fps			= 25,				//sensor fps set
#endif
		.frequency		= 0					//0: 50hz, 1: 60hz
	},
	//_ISP_DIS_,_ISP_EN_,  _ISP_AUTO_
	.isp_all_mod =  (_ISP_DIS_  <<_BLC_POS_ | _ISP_DIS_  <<_LSC_POS_  | _ISP_DIS_<<_DDC_POS_   | _ISP_DIS_<<_AWB_POS_  \
					|_ISP_DIS_  <<_CCM_POS_ | _ISP_DIS_<<_AE_POS_   | _ISP_DIS_<<_DGAIN_POS_ | _ISP_DIS_<<_YGAMA_POS_ \
					| _ISP_DIS_<<_RGB_GAMA_POS_ | _ISP_DIS_<<_CH_POS_\
					|_ISP_DIS_<<_VDE_POS_ | _ISP_DIS_<<_EE_POS_   | _ISP_DIS_<<_CFD_POS_    |_ISP_DIS_<<_SAJ_POS_
					|_ISP_YUV422_DIS_ << _YUVMOD_POS_),
	.blc_adapt = {	//when _BLC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.blkl_r		= 0,					//BLC red adjust //signed 10bit
		.blkl_gr	= 0,					//BLC green(red) adjust //signed 10bit
		.blkl_gb	= 0,					//BLC green(blue) adjust //signed 10bit
		.blkl_b		= 0,					//BLC blue adjust //signed 10bit
		.blk_rate 	= {0,2,3,4,5,6,7,8},	//_ISP_AUTO_ use, [AE statistic YLOGA/step_len] to adj BLC para, 8 means 1 rate
		.step_len	= 5,					//_ISP_AUTO_ use
	},
	.ddc_adapt = {	//when _DDC_POS_ set _ISP_EN_ or _ISP_AUTO_
		.hot_num 		= 2,				//亮点：目标点比周围24个点中的(24 - (8- hot_num))个点 都亮，差值 >((h_th_rate*p[2][2])/16 + hot_th)
		.dead_num		= 2,				//暗点：目标点比周围24个点中的(24 - (8-dead_num))个点 都暗，差值 >(d_th_rate*AVG/16 + dead_th), AVG为P[2][2]周围8个点平均值
		.hot_th			= 0,				//亮点：判断亮点的阈值，0~1023
		.dead_th		= 0,				//暗点：判断暗点的阈值，0~1023
		.avg_th			= 16,				//暗点/亮点替换：差值平均值的阈值， 0~255
		.d_th_rate		= {4,4,4,4,4,4,4,4},//_ISP_AUTO_时，根据cur_br获取d_th_rate， default使用 d_th_rate[7] , 16 means 1 rate
		.h_th_rate		= {8,8,8,8,8,8,8,8},//_ISP_AUTO_时，根据cur_br获取 h_th_rate， default使用 h_th_rate[7] , 16 means 1 rate
		.dpc_dn_en		= 1,				//1:开启pre_denoise，滤波系数与坐标距离，像素点差值正相关
		.indx_table		= {2,0,0,0,0,0,0,0},//pre_denoise: 取值范围0~7，配置 dn_idx_table, 值越大，滤波开的越大
		.indx_adapt		= {2,1,1,1,0,0,0,0},//_ISP_AUTO_ use：根据yloga/ddc_step查表获得的值，来调整indx_table 表中的值
		.std_th			= {6,20,30,40,50,80,120}, //差值对比表，对应用于获得indx_table的值
		.std_th_rate	= 0,				//用于调整 std_th ，std_th_rate * avg_val / 16;
		.ddc_step		= 7,				//_ISP_AUTO_ use
		.ddc_class		= 7,				//预留用
	},	
	.awb_adapt = {	////when _AWB_POS_ set _ISP_EN_ or _ISP_AUTO_
		.seg_mode		= 0x03,		//AWBStatistic，取值 0~3，根据Y值划分为 (1 << seg_mode)个统计区域
		.rg_start		= 191,		//AWBStatistic yuv_mod_en = 0使用，rgain (g*256/r)起始范围
		.rgmin			= 191,		//AWBStatistic yuv_mod_en = 0 使用，rgain比较的最小值，当rgain落在[rgmin,rgmax]范围内，则落在统计范围内
		.rgmax			= 485, 		//AWBStatistic yuv_mod_en = 0， rgain比较的最大值 // 256 -> 1 gain  500 /256 =about 1.9 gain
		.weight_in		= 3,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_in_low,bgain_in_high]的统计权重值（+1）
		.weight_mid		= 2,		//AWBStatistic yuv_mod_en = 0，g 在 [bgain_out_low,bgain_out_high]的统计权重值（+1）
		.ymin			= 0x0a,		//AWBStatistic 统计的Y值区域的最小值
		.ymax			= 0xc0,		//AWBStatistic 统计的Y值区域的最大值
		.hb_rate		= 0xff,		//AWB ADJ bgain <256时使用
		.hb_class		= 0x00,		//AWB ADJ 取值范围 0~3 , bgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hb_class))
		.hr_rate		= 0xff,		//AWB ADJ rgain <256时使用
		.hr_class		= 0x00,		//AWB ADJ 取值范围 0~3 , rgain <256时使用，为 0 时不用，th = 1024 - (1 <<(6+hr_class))
		.awb_scene_mod	= 0,		//当前使用的AWB RGB GAIN，用于查表manu_awb_gain[]
		.manu_awb_gain	= { 		//定义不同的AWB GAIN表
		//(bgain << 20) | (ggain<< 10) | (rgain<< 0),
			(400 << 20) | (256<< 10) | (380<< 0), 
			(368 << 20) | (256<< 10) | (350<< 0),
			(465 << 20) | (256<< 10) | (225<< 0),
			(370 << 20) | (256<< 10) | (385<< 0),
			(370 << 20) | (256<< 10) | (385<< 0)
		},
		.yuv_mod_en		= 0,										 //1:base Y, 0: Gray World
		.cb_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},  //AWBStatistic yuv_mod_en = 1, 对应不同的Y分区的ABS(CB)阈值，取值范围 0~127
		.cr_th			= {0x5,0x0a,0x0f,0x14,0x19,0x1e,0x23,0x28},	 //AWBStatistic yuv_mod_en = 1 ,对应不同的Y分区的ABS(CR)阈值，取值范围 0~127 
		.cbcr_th		= {0x8,0x0f,0x16,0x1e,0x24,0x2d,0x34,0x3c},  //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的ABS(CB)+ABS(CR)阈值，取值范围 0~255 
		.ycbcr_th		= 0x0a,										 //AWBStatistic yuv_mod_en = 1,对应不同的Y分区的y阈值(y-ABS(CB)-ABS(CR))，取值范围 0~255 
		.manu_rgain		= 0,										 //manual AWB时记录配置的rgain
		.manu_ggain		= 0,										 //manual AWB时记录配置的ggain	
		.manu_bgain		= 0,										 //manual AWB时记录配置的bgain
		.rgain			= 0,										 //auto AWB时记录配置的rgain
		.ggain			= 0,										 //auto AWB时记录配置的ggain
		.bgain			= 0,										 //auto AWB时记录配置的bgain
		.seg_gain		= {{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0},{0,0,0}}, //AUTO AWB时记录不同Y分区的RGB GAIN值
		.awb_tab		= {	//AWBStatistic yuv_mod_en = 0 用于根据(rgain-rg_start)查表获得目标g值，以16位单位（16*32 = 512）
			178,178,177,175,173,170,167,164,160,156,152,148,144,140,136,129,123,117,113,108,104,100, 94, 90, 87, 81, 78, 78, 76, 76, 76, 73, //bgain_out_high
			178,176,174,171,168,164,160,153,149,146,141,139,134,130,126,121,112,106,102, 96, 92, 88, 86, 83, 81, 77, 75, 74, 73, 72, 73, 74, //bgain_in_high
			178,169,161,150,146,143,138,136,131,125,119,112,113,111,102, 99, 91, 88, 85, 83, 82, 81, 78, 77, 74, 72, 71, 71, 71, 70, 71, 74, //bgain_in_low  
			175,154,143,138,136,130,126,119,116,113,108,106,106, 99, 92, 90, 85, 81, 79, 77, 75, 74, 72, 71, 70, 68, 67, 67, 66, 67, 69, 71 //bgain_out_low
		}		
	},					
	.ccm_adapt = {	//when _CCM_POS_ set _ISP_EN_ or _ISP_AUTO_
		//注意 CCM TAB排列顺序如下，即 竖着看，第一列为调整R， 第二列调整G，第三列调整B
		// RR,  GR, BR,
		// RG,  GG, BG,
		// RB,  GB, BB,
		//R:  (RR*R+RG*G + RB*B)/256 + s41
		//G:  (GR*R+GG*G + GB*B)/256 + s42
		//B:  (BR*R+BG*G + BB*B)/256 + s43
		.ccm	= {	//signed 10bit, -512~511, 
			0x100,	0x000,	0x000,  
			0x000,	0x100,	0x000, 
			0x00,	0x00,	0x100   
		},
		.s41	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s42	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
		.s43	= 0x0c, //signed 7bit,取值范围 -64 ~ 63
	},	
	.ae_adapt = {	//when _AE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.exp_adapt = {	//AE auto adj时使用的参数
			.ylog_cal_fnum	= 4,		//_ISP_AUTO_使用：AE统计的frame num，最大32，计算获得ylog_avg 和yloga
			.exp_tag		= {40,50,60,70,75,80,100,136}, //_ISP_AUTO_使用：根据cur_br查表获得目标ylog
			.exp_ext_mod	= 3,		//_ISP_AUTO_使用：低照度下的最小ylog值：exp_ext_mod*8
			.exp_gain		= 195*256,	//当前exp*gain的值
			.k_br			= 12,		//_ISP_AUTO_使用：用于从ylog换算cur_br的系数，值越大，换算的cur_br越大
			.exp_min		= 4,		//限制最小exp值：当exp_gain比较小时，调整gain
			.gain_max		= 1024*4,	//限制最大gain值：当exp_gain比较大时，调整exp
			.frame_nums		= 2,		//_ISP_AUTO_使用：曝光相关调整的帧数间隔
			.ratio_range	= 16,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar 范围不在[32-ratio_range/2,32 + ratio_range]时，加快调整速度
			.weight_in		= 1,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar <= 32时，使用weight_in系数(即目标照度需要降低时)
			.weight_out		= 4,		//_ISP_AUTO_使用：当 (yloga*32)/ylog_tar > 32时，使用weight_out系数(即目标照度需要提高时)
			.ev_mode		= 0,	    //外部调整整体亮度用：1:在VDE模块调整bright_oft，0：在AE调整中使用
		},
		.hgrm_adapt = { 
			//AE统计配置的参数，AE统计将整幅图划分为5*5的块进行灰度（Y值）统计
			//X[0 - WIN_X0 - WIN_X1 - WIN_X2 - WIN_X3 - WIDTH]
			//Y[0 - WIN_Y0 - WIN_Y1 - WIN_Y2 - WIN_Y3 - HEIGHT]
			.allow_miss_dots	= 256,	//预留用
			.ae_win_x0			= 80,
			.ae_win_x1			= 160,
			.ae_win_x2			= 480,
			.ae_win_x3			= 560,
			.ae_win_y0			= 60,
			.ae_win_y1			= 120,
			.ae_win_y2			= 360,
			.ae_win_y3			= 420,
			.weight_0_7			= 0x44111111,//每4bit 对应每个区域的统计权重，区域 0~7
			.weight_8_15		= 0x114f4114,//每4bit 对应每个区域的统计权重，区域 8~15
			.weight_16_23		= 0x11111444,//每4bit 对应每个区域的统计权重，区域 16~23
			.weight_24			= 0x01,		 //每4bit 对应每个区域的统计权重，区域 24
			.hgrm_centre_weight	= {15,14,13,12,11,10,9,8}, //用于根据cur_br调整中间区域，即区域12的权重值
			.hgrm_gray_weight	= {8,8,9,9,10,10,11,12},   //_ISP_AUTO_使用：根据Y值划分区域调整统计的值
		},
	},		
	.rgbdgain_adapt = { //when _DGAIN_POS_ set _ISP_EN_ or _ISP_AUTO_
		.dgain		= {64,64,64,64,64,64,64,64,64},	//配置寄存器：根据Y值的大小划分8个区域来调整
		.dgain_rate	= {64,64,64,64,64,64,64,64}, 	//_ISP_AUTO_使用：根据cur_br获得调整rate，用于调整dgain[]
	},	
	.ygama_adapt = {	//when _YGAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {5,7,9,11,13,14,15,16}, //根据 tab_num[i]的值来选择sensor_ygamma_tab[tab_num[i]]
		.adpt_num		= {5,5,5,5,5,5,5,5},	  //_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_ygamma_tab[]表
		.gam_num0		= 14,					  //当前使用的gamma表index0, 对应sensor_ygamma_tab[index0]
		.gam_num1		= 14,					  //当前使用的gamma表index1, 对应sensor_ygamma_tab[index1]
		.br_mod			= 0,					  //根据br_mod来从index0和index1表中加权平均获得目标的ygamma值
		.bofst			= 0,					  //ymin值 = bosfst << (10 - 8)
		.lofst			= 0xff,					  //ymax值 = lofst << (10 - 8)
		.pad_num		= 1,					  //配置寄存器用，不为0，微调经过ygamma的RGB值
	},
	.rgbgama_adapt = { //when _RGB_GAMA_POS_ set _ISP_EN_ or _ISP_AUTO_
		.tab_num		= {0,1,2,3,4,5,6,7},	//根据 tab_num[i]的值来选择sensor_rgb_gamma[tab_num[i]] 
		.adpt_num		= {3,2,1,1,1,1,1,1},	//_ISP_AUTO_: 根据cur_br取adpt_num[]值，取的值用于查表tab_num，然后根据查表的值选中对应的sensor_rgb_gamma[]表
		.max_oft		= {16,12,12,8,4,0,0,0}, //_ISP_AUTO_: 根据cur_br查表获得当前的max_oft0值
		.gam_num0		= 3,					//当前使用的gamma表index0, 对应sensor_rgb_gamma[index0]
		.gam_num1		= 3,					//当前使用的gamma表index1, 对应sensor_rgb_gamma[index1]
		.max_oft0		= 0,					//用于加大rgbgamma的值
		.br_mod			= 0,					//根据br_mod来从index0和index1表中加权平均获得目标的rgbgamma值
		.rmin			= 0,					//限制最小r值
		.rmax			= 0xff, 				//限制最大r值
		.gmin			= 0,					//限制最小g值
		.gmax			= 0xff,					//限制最大g值
		.bmin			= 0,					//限制最小b值
		.bmax			= 0xff,					//限制最大b值
		.fog_llimt		= 64,					//_ISP_AUTO_: 根据ylog动态调整的 rmin/gmin/bmin的最大值
		.fog_hlimt		= 224,					//_ISP_AUTO_: 根据ylog动态调整的 rmax/gmax/bmax的最小值
		.fog_dotnum		= 4000,					//_ISP_AUTO_: 亮度统计值的目标值，用于计算获得ylog_low和ylog_high
	},
	.ch_adapt = {	//when _CH_POS_ set _ISP_EN_ or _ISP_AUTO_
		.stage0_en	= 1,//enable r g b
		.stage1_en	= 1,//enable y c m
		.enhence	= {0,1,0,0,0,0},//enhance channel  r g b y c m
		//r: >th1[0] && < th0[0], g: [th0[1],th1[1]], b: [th0[2],th1[2]],
		//y(r+g): [th0[3], th1[3]], c(g+b):[th0[4],th1[4]], m(b+r):[th0[5],th1[5]]
		.th1		= {320,192,320,128,256,384},//you can set hue width
		.th0		= {64,  64,192,  0,128,256},//you can set hue width
		//m_x c_x y_x b_x g_r r_x
		.r_rate		= {14,14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.g_rate		= {0, 14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.b_rate		= {0, 14,14,14,14,14},//[0]~[5]:r,g,b,y,c,m
		.sat		= {4,8,12,16,16,16,16,16,16,16,16,16,16,16,16,16,16}, //根据饱和度S按16划分为16个区域进行调整的rate表
		.rate		= {0,16,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/ch_step查表获得rate，用于调整r_rate，g_rate，b_rate，sat表
		.ch_step	= 6,						//_ISP_AUTO_使用
	},
	.vde_adapt = {	//when _VDE_POS_ set _ISP_EN_ or _ISP_AUTO_
		.contra		= 0x80,	//取值范围0~255，对比度调节系数 (contra-128)/128, 配置为0x80时不调节
		.bright_k	= 0x80, //取值范围0~255，亮度调节系数 (bright_k-128)/128, 配置为0x80时不调节
		.bright_oft	= 0x80, //取值范围0~255，亮度增加值： (bright_oft-128), 配置为0x80时不调节
		.hue		= 0x80, //取值范围0~255，色度（UV）调节系数：配置为0x80时不调节
		.sat		= {60,64,68,78,84,88,88,84,80}, //饱和度调节表（调节UV），根据Y值划分为32间隔的8个区域进行取值，64表示1
		.sat_rate	= {10,10,16,16,16,16,16,16}, //_ISP_AUTO_使用：根据yloga/vde_step选择sat_rate，用于调整sat[]表的值
		.vde_step	= 6,	//_ISP_AUTO_使用
	},
	.ee_adapt = {	//when _EE_POS_ set _ISP_EN_ or _ISP_AUTO_
		//锐化或降噪的差值区间[ee_dn_th-> ee_keep_th-> ee_sharp_th]
		//ee_dn_th = ee_dn_th + ee_th_adp *avg/256;
		//ee_keep_th = ee_dn_th + (1<<ee_dn_slope);
		//ee_sharp_th = ee_keep_th + (1<<ee_sharp_slope);
		.ee_class		= 1,	//预留用	
		.ee_step		= 6,	//_ISP_AUTO_使用：预留ylog 调整用
		.ee_dn_slope	= {1,1,1,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_dn_slope
		.ee_sharp_slope	= {2,2,2,1,1,1,1,1},	//_ISP_AUTO_使用：取值范围0~7，根据cur_br查表获得ee_sharp_slope	
		.ee_th_adp		= {8,8,8,8,8,8,8,8},	//_ISP_AUTO_使用：取值范围0~15，根据cur_br查表获得ee_th_adp	
		.ee_dn_th		= {24,24,24,24,24,24,24,24}, //_ISP_AUTO_使用：取值范围0~63，根据cur_br查表获得ee_dn_th	
		.sharp_class	= {0x7,0x9,0xa,0xa,0xa,0xa,0xa,0xa}, //_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得sharp_class,用于配置 ee_sharp_mask[12] = 32-sharp_class
		.dn_class		= {0,0,0,0,0,0,0,0},	//_ISP_AUTO_使用：取值范围0~31，根据cur_br查表获得dn_class,用于选择不同的dn_mask表，目前固定用0
	},
	.cfd_adapt = {	//when _EE_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		//根据Y值划分区域，
		//(1) Y < ccf_start的区域，mean_en = 1时，进行高斯滤波处理
		//(2) ccf_start < y < ccf_white_ymin, 使用 (ccf_white_ymin - y)/(16<<wclass)为系数调整UV
		//(3) ccf_white_ymin <= y < ymax的区域，直接配置UV 为128
		//(4) y > ymax同时 UV差值大于 th的区域，使用 rate/16 为系数调整UV
		.rate		= 4, 		// UV调整rate，取值范围0~15，
		.ymax		= 0xe0,		// 强光区 ymax配置，取值范围 0~255
		.th			= 0x20, 	// 配置(ABS(U) + ABS(V))阈值，取值范围 0~127
		.wdc_en		= 1, 		// 1：使能(2)(3)区域的调整	
		.wclass		= 1, 		//ccf_start: wymin - (16<<wclass)   reduce saturation
		.wymin		= 0xff, 	//ccf_white_ymin 
		.mean_en	= 1, 		//ccf_mean: 配置为1，使能(1)区域的调整
		.dn_class	= 0,		//选择ccf_cd_mask[9]表，目前固定配置为0
		.ccf_en		= 1,		//配置为1时，使能(4)区域的调整
	},
	.saj_adapt = {	//when _SAJ_POS_ set _ISP_EN_， and _CFD_POS_ set _ISP_EN_ or _ISP_AUTO_ 
		.sat		= {12,12,12,12,12,12,12,13,13,14,14,15,15,16,16,16,16}, //取值范围0~31，饱和度调节率表，色饱和度[0,255]划分为16个区域，
		.sat_rate	= {5,6,7,8,9,10,12,16}, //_ISP_AUTO_使用：根据yloga/saj_step查表用于调节sat[]表, 16为单位
		.saj_step	= 6,		//_ISP_AUTO_使用：
	},
	.md_adapt = {	
		.pixel_th		= 20,
		.num_th			= 20,
		.update_cnt		= 1,
		.win_h_start	= (640/4)*1,	
		.win_h_end		= (640/4)*3,
		.win_v_start	= (480/4)*1,
		.win_v_end		= (480/4)*3,
	}, 

	.p_fun_adapt = {
		.fp_rotate		= IT03A1_rotate,
		.fp_hvblank		= IT03A1_hvblank,
		.fp_exp_gain_wr	= NULL
	},
};
SENSOR_HEADER_ITEM_SECTION const Sensor_Ident_T it03a1_init  =
{
	.sensor_struct_addr   	= (u32 *)&it03a1_adpt,     
	.sensor_struct_size   	= sizeof(Sensor_Adpt_T),
	.sensor_init_tab_adr  	= (u32 *)IT03A1InitTable,     
	.sensor_init_tab_size 	= sizeof(IT03A1InitTable),
	.lsc_tab_adr 			= (u32 *)NULL,     
	.lsc_tab_size 			= 0, 
	.sensor_name	  		= "IT03A1_VGA",
	.w_cmd            		= 0x86,                   
	.r_cmd            		= 0x87,                   
	.addr_num         		= 0x01,                   
	.data_num         		= 0x01,   
	.id               		= 0x63, 
	.id_reg           		= 0x01,                   
};



#endif


