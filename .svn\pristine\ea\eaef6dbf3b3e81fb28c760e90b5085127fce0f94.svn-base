/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_RGB_ILI8961

#define CMD(x)    LCD_CMD_RGB_DAT(x)
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
#if 1
    CMD(0x051e),
    DLY(5),
    <PERSON><PERSON>(0x055e),
    <PERSON><PERSON>(10),
    <PERSON><PERSON>(0x2b00),
    <PERSON><PERSON>(50),

    <PERSON><PERSON>(0x1301),
    <PERSON><PERSON>(0xa462),
    <PERSON><PERSON>(0x0007),
    <PERSON><PERSON>(0x0b81),
    <PERSON><PERSON>(0x01ac),
    <PERSON><PERSON>(0x040b),
    <PERSON><PERSON>(0x2f69),
    <PERSON><PERSON>(0x9500),
    CMD(0xaf08),
    CMD(0x9702),
    CMD(0x9a02),

    CMD(0x1600),
    CMD(0x1714),
    CMD(0x1821),
    CMD(0x1930),
    CMD(0x1a64),
    <PERSON><PERSON>(0x3c34),

    CMD(0x2b01),

    DLY(50),
#else
    CMD(0x055f),
    DLY(5),
    CMD(0x051f),
    DLY(10),
    CMD(0x055f),
    DLY(50),

    CMD(0x2b01),
    CMD(0x000b),             //vcom ac
    CMD(0x019f),            //vcom dc
    CMD(0x040b),
    CMD(0x1604),
    CMD(0x0614),
    CMD(0x0746),

#endif
LCD_INIT_TAB_END()


LCD_DESC_BEGIN()
    .name 			= "RGB_ILI8961",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_0,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_8,

    .pclk_div 		= LCD_PCLK_DIV(24000000),
    .clk_per_pixel 	= 3,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .pclk_edge      = LCD_PCLK_EDGE_FALLING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw 			= 1,
    .vbp 			= 21,
    .vfp 			= 10,

    .hlw 			= 1,
    .hbp 			= 23,
    .hfp 			= 150,

    LCD_SPI_DEFAULT(16),

    .data_mode = LCD_DATA_MODE0_8BIT_RGB888,

    .screen_w 		= 320,
    .screen_h 		= 240,

    .video_w  		= 320,
    .video_h 	 	= 240,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif




























