/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

 





typedef struct File_Node_S
{
	INT16U stat;
	INT16U rev;
	FIL     handle;
}File_Node_T;



ALIGNED(4) SDRAM_SECTION FATFS work_fatfs; 
static GRAM_SECTION File_Node_T 	FSNodePool[FS_CFG_NODE_NUM]; 
FS_EXT_FUNC_T 		fs_exfunc;

/*******************************************************************************
* Function Name  : fs_exfunc_init
* Description    : fs exfunc init
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void fs_exfunc_init(void)
{
	fs_exfunc.getCurTime 	= (void *(*)())hal_rtcTimeGet;
	fs_exfunc.dev_init		= sd_api_init;
	fs_exfunc.dev_sta		= sd_api_Exist;
	fs_exfunc.dev_read		= sd_api_Read;
	fs_exfunc.dev_write		= sd_api_Write;
}
/*******************************************************************************
* Function Name  : fs_nodeinit
* Description    : fs node init
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void fs_nodeinit(void)
{
	memset(FSNodePool,0,sizeof(FSNodePool));
}
/*******************************************************************************
* Function Name  : fs_mount
* Description    : fs mount
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int fs_mount(void)
{	
	memset(&work_fatfs,0,sizeof(FATFS));
//	f_unlink_ext_init(&work_fatfs);
//	f_setcp(932);
	fs_nodeinit();
	if(f_mount(&work_fatfs, "", 1)!=FR_OK)
		return -1;
    switch(work_fatfs.fs_type)
	{
		case FS_FAT16 : deg_Printf("fs : fat16,fat ss = %d\n",work_fatfs.fatbase);break;
		case FS_FAT12 : deg_Printf("fs : fat12,fat ss = %d\n",work_fatfs.fatbase);break;
		case FS_FAT32 : deg_Printf("fs : fat32,fat ss = %d\n",work_fatfs.fatbase);break;
		case FS_EXFAT : deg_Printf("fs : exfat,fat ss = %d\n",work_fatfs.fatbase);break;
		default : deg_Printf("fs : unknow\n");break;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : fs_open
* Description    : fs file open
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int fs_open(const char *name,INT32U op)
{
	FRESULT res;
	int i;

	for(i=0;i<FS_CFG_NODE_NUM;i++)
	{
		if(FSNodePool[i].stat==0)
			break;
	}
	if(i>=FS_CFG_NODE_NUM)
	{
		deg_Printf("node err\n");
		return -1;
    }
	res = f_open(&FSNodePool[i].handle,name,op);
	if(res == FR_OK)
		FSNodePool[i].stat = 1;
	else
	{
		deg_Printf("open file <%s> fail.%d\n",name,res);
		return -1;
	}
	return i;		
}
/*******************************************************************************
* Function Name  : fs_close
* Description    : fs file close
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int fs_close(int fd)
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -1;
	FSNodePool[fd].stat = 0;

	if( f_close(&FSNodePool[fd].handle)==FR_OK)
		return 0;
	else
	    return -1;
}
/*******************************************************************************
* Function Name  : fs_read
* Description    : fs file read
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int fs_read(int fd,void *buff,UINT len)
{
	UINT size = 0;
//	debg("r fd:%d\n",fd);
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -1;
	
	 if(f_read(&FSNodePool[fd].handle,buff,len,&size)!=FR_OK)
	 	  return -1;

	 return size;
}
/*******************************************************************************
* Function Name  : fs_write
* Description    : fs file write
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int fs_write(int fd,const void *buff,UINT len)
{
	UINT size=0,temp;
	FRESULT res;
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -1;
	temp = sd_api_CardState_Get();
	res = f_write(&FSNodePool[fd].handle,buff,len,&size);
	if((res!=FR_OK)||(size!=len))
	{
		deg_Printf("f_write fail.%d,%d,%d\n",res,len,size);
		deg_Printf("sd stat : %d,%d\n",temp,sd_api_CardState_Get());
		return -1;
	}

	return size;
}
/*******************************************************************************
* Function Name  : fs_seek
* Description    : fs file seek
* Input          : opt: FA_CREATE_LINKMAP / 0
* Output         : none
* Return         : none
*******************************************************************************/
FSIZE_t fs_seek(int fd,FSIZE_t offset,INT32U opt)
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -2;
#if FF_USE_FASTSEEK
	if(opt & FA_CREATE_LINKMAP)
	{
		FSNodePool[fd].handle.cltbl = FSNodePool[fd].handle.clb_mm;
		FSNodePool[fd].handle.clb_mm[0] = 0;//FF_FASTSEEK_BLK_SIZE;	
	}
#endif
	
	if(f_lseek(&FSNodePool[fd].handle,offset, opt)!=FR_OK)
		return -3;
	return offset;
}
/*******************************************************************************
* Function Name  : fs_seek
* Description    : fs file seek
* Input          : opt: FA_CREATE_LINKMAP / 0
* Output         : none
* Return         : none
*******************************************************************************/
DWORD* fs_getcltbl(int fd)
{
#if FF_USE_FASTSEEK
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return NULL;
	if(FSNodePool[fd].stat==0)
		return NULL;

	return FSNodePool[fd].handle.cltbl;
	
#endif
	
	return NULL;
}
/*******************************************************************************
* Function Name  : fs_seek
* Description    : fs file seek
* Input          : opt: FA_CREATE_LINKMAP / 0
* Output         : none
* Return         : none
*******************************************************************************/
WORD fs_getclusize(int fd)
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return 0;
	if(FSNodePool[fd].stat==0)
		return 0;
	return FSNodePool[fd].handle.obj.fs->csize;
}
/*******************************************************************************
* Function Name  : fs_mkdir
* Description    : fs file make a dir
* Input          : opt: FA_CREATE_LINKMAP / 0
* Output         : none
* Return         : none
*******************************************************************************/
int fs_mkdir(char *path)
{
	if(f_mkdir(path)!=FR_OK)
		return -1;

	return 0;
}
/*******************************************************************************
* Function Name  : fs_alloc
* Description    : fs file alloc size
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_alloc(int fd,INT32U size,int mode)  // suggest using mode == 1
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -1;

	if(f_expand(&FSNodePool[fd].handle,size,mode)!=FR_OK)
		return -1;
	return 0;
}
/*******************************************************************************
* Function Name  : fs_sync
* Description    : fs file sync
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_sync(int fd)
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -1;
	if(f_sync(&FSNodePool[fd].handle)!=FR_OK)
		return -1;
	return 0;
	
}
/*******************************************************************************
* Function Name  : fs_merge
* Description    : fs file merge fd2 to fd1
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_merge(int fd1,int fd2)
{
	if((fd1<0) || (fd1>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd1].stat==0)
		return -1;

	if((fd2<0) || (fd2>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd2].stat==0)
		return -1;
    if(f_merge(&FSNodePool[fd1].handle,&FSNodePool[fd2].handle)!=FR_OK)
		return -1;
	return 0;
}
/*******************************************************************************
* Function Name  : fs_bound
* Description    : fs file bound fd2 to fd1
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_bound(int fd1,int fd2)
{
	if((fd1<0) || (fd1>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd1].stat==0)
		return -1;

	if((fd2<0) || (fd2>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd2].stat==0)
		return -1;
	if(_f_bound(&FSNodePool[fd1].handle,&FSNodePool[fd2].handle)!=FR_OK)
		return -1;

	return 0;
}
/*******************************************************************************
* Function Name  : fs_getclustersize
* Description    : fs_getclustersize
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
INT32U fs_getclustersize(void)
{
	return (work_fatfs.csize<<9);
}
/*******************************************************************************
* Function Name  : fs_size
* Description    : fs file size get
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
INT32U fs_size(int fd)
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return 0;
	if(FSNodePool[fd].stat==0)
		return 0;
	
	return FSNodePool[fd].handle.obj.objsize;
}
/*******************************************************************************
* Function Name  : fs_size
* Description    : fs file pre size get
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
INT32U fs_pre_size(int fd)
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -1;
	
	return FSNodePool[fd].handle.obj.fsize_prev;	
}
/*******************************************************************************
* Function Name  : fs_tell
* Description    : fs file cur point
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
INT32U fs_tell(int fd)
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -1;
	
	return FSNodePool[fd].handle.fptr;	
}
/*******************************************************************************
* Function Name  : fs_free_size
* Description    : fs_free_size
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
INT32U fs_free_size(void)
{
	FATFS *pfatfs = &work_fatfs;
	u32 free_clst;
	
	if(f_getfree("", &free_clst, &pfatfs)==FR_NO_FILESYSTEM)
		pfatfs->fs_pics = 1;
	else
		pfatfs->fs_pics = 0;
//--------resver	
	if(free_clst>128)
		free_clst -=128;
	else
		free_clst = 0;
	
	return (free_clst*work_fatfs.csize);
}
/*******************************************************************************
* Function Name  : fs_check
* Description    : fs_check
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_check(void)
{
	if(work_fatfs.fs_pics)
		return -1;
	return 0;
}
/*******************************************************************************
* Function Name  : fs_getStartSector
* Description    : fs_getStartSector
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_getStartSector(int fd)
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -1;
	return  clst2sect(FSNodePool[fd].handle.obj.fs,FSNodePool[fd].handle.obj.sclust);
}

/*******************************************************************************
* Function Name  : fs_getClustStartSector
* Description    : fs_getClustStartSector
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
SDRAM_TEXT_SECTION
int fs_getClustStartSector(int fd, u32 clst)
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -1;
	return  clst2sect(FSNodePool[fd].handle.obj.fs,clst);
}
/*******************************************************************************
* Function Name  : fs_ftime
* Description    : fs_ftime
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
u32 fs_ftime(int fd)
{
	if((fd<0) || (fd>=FS_CFG_NODE_NUM))
		return -1;
	if(FSNodePool[fd].stat==0)
		return -1;
	return f_ftime(&FSNodePool[fd].handle);
	
}
