/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

typedef struct TP_IIC_CTRL_S{
	u8 slaveId;
	u8 addr_num;
}TP_IIC_CTRL_T;
ALIGNED(4) static TP_IIC_CTRL_T tp_iic_ctrl;




#define tp_iic_sda_get()		hx330x_gpioDataGet(hardware_setup.tp_iic_sda_ch, hardware_setup.tp_iic_sda_pin)
#define tp_iic_sda_set(data)	hx330x_gpioDataSet(hardware_setup.tp_iic_sda_ch, hardware_setup.tp_iic_sda_pin,data)
#define tp_iic_scl_set(data)	hx330x_gpioDataSet(hardware_setup.tp_iic_scl_ch, hardware_setup.tp_iic_scl_pin,data)
#define tp_iic_sdaOut()			hx330x_gpioDirSet(hardware_setup.tp_iic_sda_ch, hardware_setup.tp_iic_sda_pin,GPIO_OUTPUT)	
#define tp_iic_sdaIn()			hx330x_gpioDirSet(hardware_setup.tp_iic_sda_ch, hardware_setup.tp_iic_sda_pin,GPIO_INPUT)		

/*******************************************************************************
* Function Name  : tp_iic_delay
* Description	 : tp_iic_delay
* Input		     : None
* Output		 : None
* Return		 : none
*******************************************************************************/
static void tp_iic_delay(void)
{
	int dtime = hardware_setup.tp_iic_delay;
	while(dtime--)
	{
		//hx330x_wdtClear();
		asm("l.nop");
	}
}
/*******************************************************************************
* Function Name  : tp_iic_init
* Description	 : tp_iic_init
* Input		     : None
* Output		 : None
* Return		 : none
*******************************************************************************/
static void tp_iic_start(void)
{
	tp_iic_sdaOut();
	tp_iic_sda_set(1);
	tp_iic_scl_set(1);
	tp_iic_delay();
	tp_iic_sda_set(0);
	tp_iic_delay();
	tp_iic_scl_set(0);
}
 
static void tp_iic_stop(void)
{
	tp_iic_sdaOut();
	tp_iic_scl_set(0);
	tp_iic_sda_set(0);
	tp_iic_delay();
	tp_iic_scl_set(1);
	tp_iic_sda_set(1);
	tp_iic_delay();
}
static u8 tp_iic_wait_ack(void)//0:ACK 1: NACK
{
	u8 time=0;
	tp_iic_sdaIn();
	tp_iic_sda_set(1);
	tp_iic_delay();
	tp_iic_scl_set(1);
	tp_iic_delay();
	while(tp_iic_sda_get())
	{
	  	time++;
		if(time > 250)
		{
			tp_iic_stop();
			return 1;
		}
		hal_wdtClear();
	}
	tp_iic_scl_set(0);
	return 0;

}
static void tp_iic_ack(void)
{
	tp_iic_scl_set(0);
	tp_iic_sdaOut();
	tp_iic_sda_set(0);
	tp_iic_delay();
	tp_iic_scl_set(1);
	tp_iic_delay();
	tp_iic_scl_set(0);
}
static void tp_iic_nack(void)
{
	tp_iic_scl_set(0);
	tp_iic_sdaOut();
	tp_iic_sda_set(1);
	tp_iic_delay();
	tp_iic_scl_set(1);
	tp_iic_delay();
	tp_iic_scl_set(0);
}
static void tp_iic_sendbyte(u8 w)
{
	u8 i;
	tp_iic_sdaOut();
	tp_iic_scl_set(0);
	for(i=0;i<8;i++)
	{
		tp_iic_sda_set((w&0x80)>>7);//iicsda=(w&0x80)>>7;
		w<<=1;
		tp_iic_delay();
		tp_iic_scl_set(1);
		tp_iic_delay();
		tp_iic_scl_set(0);
		hal_wdtClear();
	}
	tp_iic_delay();
}
static u8 tp_iic_readbyte(u8 ack)
{
	unsigned char i=0,r=0;
	tp_iic_sdaIn();
	for(i=0;i<8;i++)
	{
		tp_iic_scl_set(0);
		tp_iic_delay();
		tp_iic_scl_set(1);
		r<<=1;
		if(tp_iic_sda_get())
			r++;
		tp_iic_delay();
		hal_wdtClear();
	}
	if(ack)
		tp_iic_ack();
	else
		tp_iic_nack();
	return r;
}
/*******************************************************************************
* Function Name  : tp_iic_init
* Description	 : tp_iic_init
* Input		     : None
* Output		 : None
* Return		 : none
*******************************************************************************/
void tp_iic_init(void)
{
	tp_iic_sda_set(1);
	tp_iic_scl_set(1);
	hal_gpioInit(hardware_setup.tp_iic_sda_ch,hardware_setup.tp_iic_sda_pin,GPIO_OUTPUT,GPIO_PULL_UP);
	hal_gpioInit(hardware_setup.tp_iic_scl_ch, hardware_setup.tp_iic_scl_pin,GPIO_OUTPUT,GPIO_PULL_UP);
}
/*******************************************************************************
* Function Name  : tp_iic_config
* Description	 : tp_iic_config
* Input		     : u8 slaveId, u8 addr_num
* Output		 : None
* Return		 : none
*******************************************************************************/
void tp_iic_config(u8 slaveId, u8 addr_num)
{
	tp_iic_ctrl.slaveId  = slaveId;
	tp_iic_ctrl.addr_num = addr_num;
}
/*******************************************************************************
* Function Name  : tp_iic_write
* Description	 : tp_iic_write
* Input		     : u32 addr, u8* buf, u8 len
* Output		 : None
* Return		 : none
*******************************************************************************/
int tp_iic_write(u32 addr, u8* buf, u8 len)
{
	u8 i;
	if(len == 0)
		return -1;
	tp_iic_start();

	tp_iic_sendbyte(tp_iic_ctrl.slaveId);  // send slaveid
	if(tp_iic_wait_ack())             // recv ack singal
	{
		deg_Printf("[TP IIC] write id ack err\n");
		return -2;
	}
	for(i = tp_iic_ctrl.addr_num; i > 0; i--)
	{
		tp_iic_sendbyte((addr >> ((i-1)*8)) & 0xff);     // send address
		if(tp_iic_wait_ack())             // recv ack singal
		{
			deg_Printf("[TP IIC] write addr ack err\n");
			return -3;
		}
	}
	while( (len--) > 0 )
	{
	 	tp_iic_sendbyte(*buf++);
		if(tp_iic_wait_ack())
		{
			deg_Printf("[TP IIC] write data ack err\n");
			return -4;
		}
	}
	tp_iic_stop();
	return 0;

}
/*******************************************************************************
* Function Name  : tp_iic_read
* Description	 : tp_iic_read
* Input		     : u32 addr, u8* buf, u8 len
* Output		 : None
* Return		 : none
*******************************************************************************/
int tp_iic_read(u32 addr, u8* buf, u8 len)
{
	u8 i;
	if(len == 0)
		return -1;
	tp_iic_start();

	tp_iic_sendbyte(tp_iic_ctrl.slaveId);  // send slaveid
	if(tp_iic_wait_ack())             // recv ack singal
	{
		deg_Printf("[TP IIC] read id ack err\n");
		return -2;
	}
	for(i = tp_iic_ctrl.addr_num; i > 0; i--)
	{
		tp_iic_sendbyte((addr >> ((i-1)*8)) & 0xff);     // send address
		if(tp_iic_wait_ack())             // recv ack singal
		{
			deg_Printf("[TP IIC] read addr ack err\n");
			return -3;
		}
	}
	tp_iic_stop();                 // stop
	tp_iic_start();				 // send Start singal
	tp_iic_sendbyte(tp_iic_ctrl.slaveId|1);  // send slaveid
	if(tp_iic_wait_ack())             // recv ack singal
	{
		deg_Printf("[TP IIC] read addr1 ack err\n");
		return -4;
	}
	while( (len--) > 1 )
	{
		*buf++ = tp_iic_readbyte(1);
	}
	*buf++ = tp_iic_readbyte(0);
	tp_iic_stop();
	return 0;
}
