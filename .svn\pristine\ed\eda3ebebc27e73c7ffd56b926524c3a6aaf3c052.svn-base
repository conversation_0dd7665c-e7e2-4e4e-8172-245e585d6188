/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HAL_LCD_UI_H
    #define HAL_LCD_UI_H

/*******************************************************************************
* Function Name  : hal_uiRotateBufMalloc
* Description    : get free ui rotate(input) buffer
* Input          : None
* Output         : None
* Return         : NULL is fail
*******************************************************************************/
lcdshow_frame_t * hal_uiRotateBufMalloc(void);
/*******************************************************************************
* Function Name  : hal_uiLzoBufMalloc
* Description    : get free uilzo (input) buffer
* Input          : None
* Output         : None
* Return         : NULL is fail
*******************************************************************************/
lcdshow_frame_t * hal_uiLzoBufMalloc(void);
/*******************************************************************************
* Function Name  : hal_uiDrawBufMalloc
* Description    : get free ui draw (input) buffer
* Input          : None
* Output         : None
* Return         : NULL is fail
*******************************************************************************/
lcdshow_frame_t * hal_uiDrawBufMalloc(u8 layer);
/*******************************************************************************
* Function Name  : hal_uiBuffFree
* Description    : set a ui buff as free
* Input          : p_buffer 
* Output         : None
* Return         : None
*******************************************************************************/
void hal_uiBuffFree(lcdshow_frame_t * p_buffer);
/*******************************************************************************
* Function Name  : hal_lcdUiKick
* Description    : add buffer to queue
* Input          : lcdshow_frame_t * p_ui_buffer
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdUiKick(lcdshow_frame_t * p_ui_buffer);
/*******************************************************************************
* Function Name  : hal_lcdUiKick
* Description    : add buffer to queue
* Input          : lcdshow_frame_t * p_ui_buffer
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdUiKickWait(lcdshow_frame_t * p_ui_buffer);
/*******************************************************************************
* Function Name  : hal_lcdUiInit
* Description    : lcd ui  initial
* Input          : layer : layer id(UI_LAYER0, UI_LAYER1)
				   u16 width : width
				   u16 height: height
				   u16 x : x
				   u16 y: y
				   u32 palette : palette addr
                   u8 rotate_mode
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_lcdUiInit(u8 layer,u16 width,u16 height,s16 x,s16 y,u32 palette,u8 rotate_mode);
/*******************************************************************************
* Function Name  : hal_lcdUiEnable
* Description    : lcd ui set enable
* Input          : layer : layer id(UI_LAYER0, UI_LAYER1)
				   u8 en: 0-disable,1-enable
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdUiEnable(u8 layer,u8 en);
/*******************************************************************************
* Function Name  : hal_lcdUiSetAddr
* Description    : lcd ui set addr
* Input          : layer : layer id(UI_LAYER0, UI_LAYER1)
				   u32 addr : addr
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdUiSetAddr(u8 layer,u32 addr);
/*******************************************************************************
* Function Name  : hal_lcdUiSetBuffer
* Description    : lcd ui set addr
* Input          : layer : UI_LAYER0,UI_LAYER1
                   p_buffer : 
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdUiSetBuffer(u8 layer,lcdshow_frame_t * p_buffer);
/*******************************************************************************
* Function Name  : hal_lcdUiSetBuffer
* Description    : lcd ui set addr
* Input          : layer : UI_LAYER0,UI_LAYER1
                   p_buffer : 
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdUiSetBufferWaitDone(u8 layer);
/*******************************************************************************
* Function Name  : hal_lcdUiSetBuffer
* Description    : lcd ui set addr
* Input          : layer : UI_LAYER0,UI_LAYER1
                   p_buffer : 
* Output         : None
* Return         : None
*******************************************************************************/
int hal_lcdUiBuffFlush(void);
/*******************************************************************************
* Function Name  : hal_lcdUiSetPosition
* Description    : lcd ui set position
* Input          : layer : UI_LAYER0,UI_LAYER1
				   u16 x : x
				   u16 y: y
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdUiSetPosition(u8 layer,u16 x,u16 y);
/*******************************************************************************
* Function Name  : hal_lcdUiSetPalette
* Description    : lcd ui set palette
* Input          : layer : UI_LAYER0,UI_LAYER1
				   u32 addr : addr
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdUiSetPalette(u8 layer,u32 addr);
/*******************************************************************************
* Function Name  : hal_lcdUiSetSize
* Description    : lcd ui set size
* Input          : layer : UI_LAYER0,UI_LAYER1
				   u16 width : width
				   u16 height: height
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdUiSetSize(u8 layer,u16 width,u16 height);
/*******************************************************************************
* Function Name  : hal_lcdUiSetAlpha
* Description    : lcd ui set alpha
* Input          : layer : UI_LAYER0,UI_LAYER1
				   u8 type : ALPHA_NORMAL,ALPHA_GLOBAL
				   u8 value : gblobal alpha value
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdUiSetAlpha(u8 layer,u8 type,u8 value);
/*******************************************************************************
* Function Name  : hal_lcdUiResolutionGet
* Description    : hardware layer ,get lcd ui width and height
* Input          : layer : UI_LAYER0,UI_LAYER1
				   u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : bool true: success
						false: fail
*******************************************************************************/
bool hal_lcdUiResolutionGet(u8 layer,u16 *width,u16 *height);
/*******************************************************************************
* Function Name  : hal_lcdVideo_CCM_cfg
* Description    : set lcd video ccm
* Input          : u32 *p_ccm
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdVideo_CCM_cfg(u32 *p_ccm);

/*******************************************************************************
* Function Name  : hal_lcdVideo_SAJ_cfg
* Description    : set lcd  saj
* Input          : u32 *p_saj
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdVideo_SAJ_cfg(u8 *p_saj);

#endif

