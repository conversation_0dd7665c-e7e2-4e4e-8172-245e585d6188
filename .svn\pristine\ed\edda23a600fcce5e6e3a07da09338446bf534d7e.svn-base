/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef USB_HOST_HUB_H_
#define USB_HOST_HUB_H_
/*******************************************************************************
* Function Name  : husb_hub_init
* Description    : husb_hub_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_hub_init(void *handle);

/*******************************************************************************
* Function Name  : husb_hub_uinit
* Description    : husb_hub_uinit
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_hub_uinit(void *handle);

#endif /* USB_HOST_TPBULK_H_ */

