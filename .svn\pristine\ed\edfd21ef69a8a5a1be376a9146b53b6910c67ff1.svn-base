/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  AUDIO_RECORD_H
    #define  AUDIO_RECORD_H



typedef struct AUIDO_REC_ARG_S
{
	WAV_PARA_T 	wav_arg;
	INT32U  looprec; 
	INT32U 	looptime;
	int (*callback)(INT32U cmd,INT32U para);
}AUIDO_REC_ARG_T;
typedef struct AUDIO_RARG_S
{
	INT8U  src_type;  // fs , rawdata
	INT8U  tar_type;  // wav{alow,ulow,adpcm,pcm},mp3,...
	INT8U  channel;   // 1,2
	INT8U  looprec; 

	INT32U looptime;
	INT32U samplerate;

	
	
}AUDIO_RARG_T;


enum
{
	AUDIO_CMD_NULL=0,
	AUDIO_CMD_START,
	AUDIO_CMD_STOP,
	AUDIO_CMD_FAIL,

	AUDIO_CMD_MAX
};

#if DBG_AUIDO_REC_EN
    #define  AUDIOREC_DBG   		deg_Printf
#else
    #define  AUDIOREC_DBG(...)
#endif




/*******************************************************************************
* Function Name  : audioRecordInit
* Description    : initial audio record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioRecordInit(AUIDO_REC_ARG_T *arg);
/*******************************************************************************
* Function Name  : audioRecordUninit
* Description    : uninitial audio record 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void audioRecordUninit(void);
/*******************************************************************************
* Function Name  : audioRecordStart
* Description    : start audio record
* Input          : AUDIO_RECORD_ARG_T *arg : audio record argument
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioRecordStart(void);
/*******************************************************************************
* Function Name  : audioRecordStop
* Description    : stop audio record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioRecordStop(INT8U error);
/*******************************************************************************
* Function Name  : audioRecordPuase
* Description    : pause audio record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioRecordPuase(void);
/*******************************************************************************
* Function Name  : audioRecordResume
* Description    : resume audio record 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioRecordResume(void);
/*******************************************************************************
* Function Name  : audioRecordGetStatus
* Description    : get audio record 
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int audioRecordGetStatus(void);
/*******************************************************************************
* Function Name  : audioRecordSetStatus
* Description    : Set audio record 
* Input          : stat
* Output         : none
* Return         : none
                      
*******************************************************************************/
void audioRecordSetStatus(u16 stat);

/*******************************************************************************
* Function Name  : audioRecordGetTime
* Description    : get audio record time
* Input          : none
* Output         : none
* Return         : int : ms
                      
*******************************************************************************/
int audioRecordGetTime(void);
/*******************************************************************************
* Function Name  : audioRecordService
* Description    : audio record service
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int audioRecordService(void);
















#endif
