/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_FRAME_H
#define UI_WIN_FRAME_H

#define WIGET_HANDLE_MAX_NUM    20
typedef struct
{
	uiWinObj win;
	uiWinCB  cb;	//dialog cb
	winHandle widgetHandle[WIGET_HANDLE_MAX_NUM];
	void	*prvate; //pointer WINDOWS_T
}uiFrameWinObj;
/*******************************************************************************
* Function Name  : uiFrameWinCreate
* Description    : uiFrameWinCreate
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiFrameWinCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb);
#endif
