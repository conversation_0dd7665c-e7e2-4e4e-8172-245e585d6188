/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../hal/inc/hal.h"

#if  MEDIA_AUDIO_DECODE_EN > 0


#define DAC_VOLUME_MAKE(x)          ((x*0x7fff)/100)


typedef struct Audio_Ctrl_S
{
	INT8U  	stat;	   //MEDIA_STAT_STOP / MEDIA_STAT_PLAY / MEDIA_STAT_PAUSE
	INT8U  	dacstat;   //PLAY_DACSTA_STOP/PLAY_DACSTA_START/PLAY_DACSTA_WAITSTOP // 0 :stop,1:start,2:dac auto stop,3:decode auto stop	
	INT8U  	prepause;
	INT8U  	rev;
	
    INT8U  	curIdx;
	INT8U  	nextIdx;
	INT16U 	datasize;
	
	INT32U 	framesize;
	
	MSG_T 	audsIStack[AUDIO_CFG_BUFFER_NUM];
	MSG_T	audsBStack[AUDIO_CFG_BUFFER_NUM];
	
	XMsg 	*idleQ;
	XMsg 	*busyQ;
	
	WAV_PARA_T arg;

	INT8U 	*audiobuffer;

}Audio_Ctrl_T;

ALIGNED(4) static Audio_Ctrl_T audioCtrl;


/*******************************************************************************
* Function Name  : audioPlaybackISR
* Description	 : audio play back dac callback
* Input 		 : int flag
* Output		 : none 										   
* Return		 : none
*******************************************************************************/
static void audioPlaybackISR(int flag)
{
	INT8U err;
	int idx,size;

	//deg_Printf("[FLAG:%x]\n",flag);
	if(audioCtrl.arg.src_type == MEDIA_SRC_RAM)
	{
		if(flag & DAC_INT_EMPTY)
		   audioPlaybackStop();
		return ;
	}

	if(flag & DAC_INT_PEND)
	{
		if(audioCtrl.nextIdx != 0xff)
			hal_dacFlush(audioCtrl.framesize);
		if(audioCtrl.curIdx != 0xff)
		{
			XMsgQPost(audioCtrl.idleQ,(MSG_T *)(INT32)audioCtrl.curIdx);
		}
		audioCtrl.curIdx = audioCtrl.nextIdx;
		audioCtrl.nextIdx = 0xff;
		return ;
	}
	if(audioCtrl.prepause)
	{
		if((flag&DAC_INT_EMPTY) == 0)
			return ;
		if(audioCtrl.curIdx!=0xff)
			XMsgQPost(audioCtrl.idleQ,(MSG_T *)(INT32)audioCtrl.curIdx);
		audioCtrl.curIdx = 0xff;
		hal_dacPlayStop();
		audioCtrl.dacstat 	= PLAY_DACSTA_STOP;
		audioCtrl.stat 		= MEDIA_STAT_PAUSE;
	    sd_api_unlock();
		return ;
	}
	
	if(flag & DAC_INT_HALF)
	{
		if(audioCtrl.nextIdx==0xff)
		{
			size = (int)XMsgQPend(audioCtrl.busyQ,&err); 
			if(err == X_ERR_NONE)
			{
				idx = size&0xff;
	            size = size>>8;
				audioCtrl.framesize = size;
				hal_dacSetBuffer((INT32U)(audioCtrl.audiobuffer+AUDIO_CFG_BUFFER_SIZE*idx),size);
				audioCtrl.nextIdx = idx;
			}
		}
	}
	else if(flag&DAC_INT_EMPTY)
	{
		if(audioCtrl.dacstat == PLAY_DACSTA_WAITSTOP)
			audioPlaybackStop();
		else
		{
			hal_dacPlayStop();
		    audioCtrl.dacstat = PLAY_DACSTA_STOP;
		}
	}
	
    if(audioCtrl.curIdx != 0xff)  // current buffer back to idle q
    {
		idx = audioCtrl.curIdx;
		audioCtrl.curIdx = 0xff;
		XMsgQPost(audioCtrl.idleQ,(MSG_T *)idx);
    }
}
/*******************************************************************************
* Function Name  : audioPlaybackInit
* Description    : initial audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackInit(void)
{
	memset(&audioCtrl, 0, sizeof(Audio_Ctrl_T));
	audioCtrl.arg.media_ch	= -1;
	audioCtrl.arg.fd		= -1;
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioPlaybackInit
* Description    : initial audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
static int audioPlaybackMemInit(void)
{
	int i;
	audioCtrl.idleQ 		= XMsgQCreate(audioCtrl.audsIStack,AUDIO_CFG_BUFFER_NUM);
	audioCtrl.busyQ 		= XMsgQCreate(audioCtrl.audsBStack,AUDIO_CFG_BUFFER_NUM);
	audioCtrl.audiobuffer  = (INT8U *)hal_sysMemMalloc(AUDIO_CFG_BUFFER_NUM*AUDIO_CFG_BUFFER_SIZE);//shareMemMalloc(AUDIO_CFG_BUFFER_NUM*AUDIO_CFG_BUFFER_SIZE);//audioCache;
	audioCtrl.arg.cachelen = AUDIO_CACHE_SIZE;
	audioCtrl.arg.cachebuf = (INT8U *)hal_sysMemMalloc(audioCtrl.arg.cachelen);//shareMemMalloc(AUDIO_RING_SIZE);//aviIdx1Cache;
	if(audioCtrl.arg.cachebuf == NULL || audioCtrl.audiobuffer == NULL)
	{
		AUDIO_PLY_DBG("[AUDIO PLY]: malloc mem fail.\n");
		return STATUS_FAIL;
	}
	XMsgQFlush(audioCtrl.idleQ);
	XMsgQFlush(audioCtrl.busyQ);
	for(i=0;i<AUDIO_CFG_BUFFER_NUM;i++)
	{
		XMsgQPost(audioCtrl.idleQ,(MSG_T *)i);
	}	
	return STATUS_OK;
	
}
/*******************************************************************************
* Function Name  : audioPlaybackInit
* Description    : initial audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
static void audioPlaybackMemUinit(void)
{
	if(audioCtrl.arg.cachebuf)
		hal_sysMemFree(audioCtrl.arg.cachebuf);
	if(audioCtrl.audiobuffer)
		hal_sysMemFree(audioCtrl.audiobuffer);
	audioCtrl.arg.cachebuf = NULL;
	audioCtrl.arg.cachelen = 0;
	audioCtrl.audiobuffer = NULL;
	XMsgQDestory(audioCtrl.idleQ);
	XMsgQDestory(audioCtrl.busyQ);
//    audac_play_callback_register(NULL);
	audioCtrl.idleQ = NULL;
	audioCtrl.busyQ = NULL;	
}
/*******************************************************************************
* Function Name  : audioPlaybackUninit
* Description    : uninitial audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackUninit(void)
{
	if(audioCtrl.stat != MEDIA_STAT_STOP)
		audioPlaybackStop();


	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioPlaybackStart
* Description    : Start audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackParse(WAV_PARA_T *arg)
{
	int ret;
	if(arg == NULL || arg->type != MEDIA_WAV)
	{
		AUDIO_PLY_DBG("[AUDIO PLY]: arg err\n");
		return STATUS_FAIL;
	}
	//if(audioCtrl.stat != MEDIA_STAT_STOP)
	//{
	//	AUDIO_PLY_DBG("[AUDIO PLY]: state error %d\n",audioCtrl.stat);
	//	return STATUS_FAIL;
	//}
	//if(audioCtrl.arg.media_ch >= 0)
	//	api_multimedia_uninit(audioCtrl.media_ch);	
	arg->media_ch = api_multimedia_init(MEDIA_DECODE, arg->type);
	if(arg->media_ch < 0)
	{
		AUDIO_PLY_DBG("[AUDIO PLY]:api init fail.\n");
		return STATUS_FAIL;
	}
	ret = api_multimedia_start(arg->media_ch, (void*)arg);
	if(ret < 0)
	{
		AUDIO_PLY_DBG("[AUDIO PLY]:start fail. %d\n",ret);
		api_multimedia_uninit(arg->media_ch);
		return STATUS_FAIL;
	}	
	ret =  api_multimedia_getArg(arg->media_ch, (void*)arg);
	if(ret < 0)
	{
		AUDIO_PLY_DBG("[AUDIO PLY]:get ARG fail.\n");
		api_multimedia_uninit(arg->media_ch);
		return STATUS_FAIL;
	}		
	if(arg->src_type == MEDIA_SRC_RAM)
	{
		api_multimedia_uninit(arg->media_ch);
		arg->media_ch = -1;
	}
	return STATUS_OK;
}

/*******************************************************************************
* Function Name  : audioPlaybackStart
* Description    : Start audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackStart(WAV_PARA_T *arg)
{
	int ret;
	if(arg->src_type != MEDIA_SRC_RAM)
	{
		if(audioPlaybackMemInit() != STATUS_OK)
		{
			fs_close(arg->fd);
			return STATUS_FAIL;
		}
		audioCtrl.arg.ch_out   = arg->ch_out;
		audioCtrl.arg.volume   = arg->volume;
		audioCtrl.arg.type	   = arg->type;
		audioCtrl.arg.src_type = arg->src_type;
		audioCtrl.arg.fd	   = arg->fd;
		audioCtrl.curIdx 	   = 0xff;
		audioCtrl.nextIdx 	   = 0xff;	
		hal_dacCallBackRegister(audioPlaybackISR);
		if(audioPlaybackParse(&audioCtrl.arg) < 0)
		{
			audioPlaybackStop();
			return STATUS_FAIL;
		}
		if(audioCtrl.busyQ == NULL || audioCtrl.idleQ == NULL)  // audio playback is not initialed
		{
			audioPlaybackStop();
			return STATUS_FAIL;
		}

		hal_dacSetVolume(audioCtrl.arg.volume);	
		if(audioCtrl.arg.src_type == MEDIA_SRC_FS)
			sd_api_lock();
		audioCtrl.dacstat 	= PLAY_DACSTA_STOP;	
	}else
	{
		if(arg == NULL || arg->type != MEDIA_WAV || arg->datasize == 0)
		{
			if(arg == NULL)
				deg_Printf("arg null\n");
			else
				deg_Printf("arg type:%x, datasize:%x\n",arg->type, arg->datasize);
			AUDIO_PLY_DBG("[AUDIO PLY]:arg fail.\n");
			return STATUS_FAIL;
		}
		//memcpy((void*)&audioCtrl.arg,(void*)arg,sizeof(WAV_PARA_T));
		audioCtrl.arg.src_type = arg->src_type;
		audioCtrl.curIdx 	= 0xff;
		audioCtrl.nextIdx 	= 0xff;	
		hal_dacCallBackRegister(audioPlaybackISR);
		hal_dacSetVolume(arg->volume);
		ret = hal_dacPlayStart(arg->samplerate,(INT32U)arg->dataoffset,arg->datasize);
		if(ret < 0)
		{
			AUDIO_PLY_DBG("[AUDIO PLY]:start fail.\n");
			audioPlaybackStop();
			return STATUS_FAIL;
		}
		audioCtrl.dacstat = PLAY_DACSTA_START;
	}
	audioCtrl.stat 		= MEDIA_STAT_PLAY;
	AUDIO_PLY_DBG("[AUDIO PLY]:start\n");
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioPlaybackStop
* Description    : Stop audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackStop(void)
{
		audioCtrl.prepause = 0;
		hal_dacPlayStop();
		hal_dacCallBackRegister(NULL);
		if(audioCtrl.arg.src_type != MEDIA_SRC_RAM)
		{
			if(audioCtrl.arg.media_ch >= 0)
			{
				api_multimedia_uninit(audioCtrl.arg.media_ch);
				audioCtrl.arg.media_ch	= -1;
			}
			if(audioCtrl.arg.src_type == MEDIA_SRC_FS)
			{
				fs_close(audioCtrl.arg.fd);
				audioCtrl.arg.fd = -1;
				sd_api_unlock();
			}
			audioPlaybackMemUinit();
		
		}
		audioCtrl.dacstat 	= PLAY_DACSTA_STOP;
		audioCtrl.curIdx 	= 0xff;
		audioCtrl.stat 		= MEDIA_STAT_STOP;
		
		AUDIO_PLY_DBG("[AUDIO PLY]:stop!\n");
		return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioPlaybackPause
* Description    : Puase audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackPause(void)
{
//	int idx;
	if(audioCtrl.stat == MEDIA_STAT_PLAY)
	{
		if(audioCtrl.dacstat != PLAY_DACSTA_STOP)
		{
			audioCtrl.prepause = 1;
		}
	}
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioPlaybackPause
* Description    : Puase audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackFirstPause(void)
{
//	int idx;
	if(audioCtrl.stat == MEDIA_STAT_PLAY)
	{
		audioCtrl.stat = MEDIA_STAT_PAUSE;
		audioCtrl.dacstat = PLAY_DACSTA_STOP;
		sd_api_unlock();
		AUDIO_PLY_DBG("[AUDIO PLY]:FIRST PAUSE!\n");
	}
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioPlaybackResume
* Description    : Resume audio Playback 
* Input          : none
* Output         : none
* Return         : int 0 : success
                      <0 : fail
*******************************************************************************/
int audioPlaybackResume(void)
{
	if(audioCtrl.stat == MEDIA_STAT_PAUSE)
	{
		hal_dacCallBackRegister(audioPlaybackISR);
		audioCtrl.stat = MEDIA_STAT_PLAY;
		audioCtrl.prepause = 0;
		if(audioCtrl.arg.src_type == MEDIA_SRC_FS)
			sd_api_lock();
	}
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioPlaybackGetStatus
* Description    : get audio Playback 
* Input          : none
* Output         : none
* Return         : int 
                      
*******************************************************************************/
int audioPlaybackGetStatus(void)
{
	return audioCtrl.stat;
}
/*******************************************************************************
* Function Name  : videoPlaybackGetTime
* Description    : get video Playback time
* Input          : INT32U *total : total time  ms
                     INT32U *curr : current time ms
* Output         : INT32U *total : total time
                     INT32U *curr : current time
* Return         : int 
                      
*******************************************************************************/
int audioPlaybackGetTime(INT32U *total,INT32U *curr)
{
	if(api_multimedia_gettime(audioCtrl.arg.media_ch, (int *)total, (int *)curr) < 0)
	{
		if(total)
			*total = 0;
		if(curr)
			*curr = 0;
		return STATUS_FAIL;
	}
	
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioPlaybackSetVolume
* Description    :set audio Playback volume
* Input          : INT8U volume : 0-100
* Return         : int 
                      
*******************************************************************************/
int audioPlaybackSetVolume(INT8U volume)
{
	if(volume>100)
		volume = 100;
	audioCtrl.arg.volume = volume;
	if(audioCtrl.stat == MEDIA_STAT_PLAY)
        //audac_volume_set(DAC_VOLUME_MAKE(volume));
        hal_dacSetVolume(volume);
	return STATUS_OK;
}
/*******************************************************************************
* Function Name  : audioPlaybackGetVolume
* Description    : get audio Playback volume
* Input          : 
* Return         : INT8U volume : 0-100 
                      
*******************************************************************************/
int audioPlaybackGetVolume(void)
{
	return audioCtrl.arg.volume;
}
/*******************************************************************************
* Function Name  : audioPlaybackService
* Description    : audio play back services
* Input          : 
* Output         : 
* Return         :  
                      
*******************************************************************************/
void audioPlaybackService(void)
{
	INT8U idx,err;
	INT32S ret,size;

	if(audioCtrl.stat != MEDIA_STAT_PLAY || audioCtrl.dacstat == PLAY_DACSTA_WAITSTOP||audioCtrl.arg.src_type == MEDIA_SRC_RAM) //decode end or error,wait audac end & stop
	{
		return ;
	}
//----------get idle buffer--------------
	size = (int)XMsgQPend(audioCtrl.idleQ,&err);
	if(err!= X_ERR_NONE) // no buffer
		return;
    
	idx = size&0xff;
	//if(audioCtrl.framesize!=0)
    // 	size = audioCtrl.framesize;
	//else
	size = AUDIO_CFG_BUFFER_SIZE;
//---------decode---------------------
	ret = api_multimedia_decodeframe(audioCtrl.arg.media_ch, (void *)(audioCtrl.audiobuffer+AUDIO_CFG_BUFFER_SIZE*idx), NULL, (int *)&size,0);
	if(ret < 0)
	{
		deg_Printf("[AUDIO PLY]: decode fail %d\n",ret);
		XMsgQPost(audioCtrl.idleQ,(MSG_T *)(idx&0xff)); // back to queue
		if(audioCtrl.curIdx == 0xff)
		    audioPlaybackStop();
		else
			audioCtrl.dacstat = PLAY_DACSTA_WAITSTOP;
		return;
	}
//--------play--------------    
    if(audioCtrl.dacstat == PLAY_DACSTA_STOP) // first frame
    {
		//deg_Printf("[AUDIO PLY]: first frame,%d\n",audioCtrl.stat);
		//audioCtrl.framesize = size;
		//audioCtrl.datasize  = size;

		hal_dacPlayStart(audioCtrl.arg.samplerate,(INT32U)(audioCtrl.audiobuffer+AUDIO_CFG_BUFFER_SIZE*idx),size);

		audioCtrl.curIdx 	= idx;	
		audioCtrl.dacstat 	= PLAY_DACSTA_START;
		audioPlaybackResume();
    }
	else//--------send to play queue
	{
		size = (size<<8)|idx;
	    XMsgQPost(audioCtrl.busyQ,(MSG_T *)size);
	}
	//ringService();	
}


#endif
