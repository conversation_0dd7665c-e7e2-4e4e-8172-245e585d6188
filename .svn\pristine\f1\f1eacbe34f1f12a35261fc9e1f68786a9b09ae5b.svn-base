/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

/*******************************************************************************
* Function Name  : sysComKeyMsgAll
* Description    : sysComKeyMsgAll: OK/UP/DOWN/MENU/MODE
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComKeyMsgAll(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_auto_poweroff(1);
		task_com_sreen_check(SREEN_RESET_AUTOOFF);
		if(SysCtrl.poweroffTime != 0) //reset auto poweroff
			SysCtrl.poweroffTime = FUN_AUTOPOWEROFF_TIME;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysComKeyMsgPower
* Description    : sysComKeyMsgPower
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_auto_poweroff(1);
		if(SysCtrl.poweroffTime != 0) //reset auto poweroff
			SysCtrl.poweroffTime = FUN_AUTOPOWEROFF_TIME;
		if(SysCtrl.dev_fd_ir >= 0)
		{
			task_com_sreen_check(SREEN_RESET_AUTOOFF);
			if(user_config_get(CONFIG_ID_IR_LED) != R_ID_STR_IR_AUTO) 
			{
				u32 para	= 0; 
				if(dev_ioctrl(SysCtrl.dev_fd_ir,DEV_IR_READ,(u32)&para) >= 0)
				{
					if(para == 0)
					{
						task_com_ir_set(1);	//ir manual on
						user_config_set(CONFIG_ID_IR_LED,R_ID_STR_COM_ON);
					}else
					{
						task_com_ir_set(0);	//ir manual on
						user_config_set(CONFIG_ID_IR_LED,R_ID_STR_COM_OFF);
					}
				}
			}
		}
// 		else
// 		{
// 			if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
// 			{
// 				husb_api_usensor_switch_res_kick(0, 1);
// 				task_com_tips_show(TIPS_COM_WAITING_2S);
// 			}


// //			if(SysCtrl.dev_stat_lcd)//LCD Sreen on and not astern mode
// //			{
// //				task_com_sreen_check(SREEN_SET_OFF);
// //			}else
// //			{
// //				task_com_sreen_check(SREEN_SET_ON);
// //			}
// 		}
		deg_Printf("[SYS] deal:powerkey\n");
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysComKeyMsgPowerOff
* Description    : sysComKeyMsgPowerOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComKeyMsgPowerOff(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_taskStart(TASK_POWER_OFF,0);
		deg_Printf("[SYS] deal:power off\n");
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysComSysMsgUSB
* Description    : sysComSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	u32 usbdev_state = USBDEV_STAT_MAX;
	if(parameNum == 1)
		usbdev_state = parame[0];
	if(usbdev_state >= USBDEV_STAT_MAX)
		return 0;	
	task_com_sreen_check(SREEN_RESET_AUTOOFF);
	if(usbdev_state != USBDEV_STAT_NULL)  // DC IN
	{
		task_com_auto_poweroff(1);
		SysCtrl.poweroffTime = 0;
		if(SysCtrl.dev_husb_stat == USBHOST_STAT_OUT/* && app_taskCurId() != TASK_PLAY_MP3 && app_taskCurId() != TASK_NES_GAME*/)
			task_com_usbhost_set(USBHOST_STAT_NULL);
	}
	else //USBDEV_STAT_NULL
	{
		if(SysCtrl.poweroffTime == 0)
			SysCtrl.poweroffTime = FUN_AUTOPOWEROFF_TIME;
		if(FUN_AUTOPOWEROFF_TIME)
			uiOpenWindow(&tips1Window,0,2,TIPS_NO_DC_POWEROFF,FUN_AUTOPOWEROFF_TIME);


		
		if(SysCtrl.dev_husb_stat != USBHOST_STAT_SHOW && (app_taskCurId()  == TASK_USB_DEVICE))
			app_taskStart(TASK_RECORD_PHOTO,0);
	
		task_com_USB_CS_DM_DP_status_select(1);
	}	
	if(usbdev_state == USBDEV_STAT_PC) 
		app_taskStart(TASK_USB_DEVICE,0);
	return 0;
}
/*******************************************************************************
* Function Name  : sysComSysMsgSD
* Description    : sysComSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{      
	int ret;
	u32 sd_state = SDC_STAT_MAX;
	if(parameNum == 1)
		sd_state = parame[0];
	if(sd_state >= SDC_STAT_MAX)
		return 0;
	task_com_sreen_check(SREEN_RESET_AUTOOFF);
	task_com_auto_poweroff(1);
	if(SysCtrl.poweroffTime != 0) //reset auto poweroff
		SysCtrl.poweroffTime = FUN_AUTOPOWEROFF_TIME;
	if(sd_state == SDC_STAT_NORMAL)
	{
		ret = fs_open((const char *)"SELFTEST.bin",FA_READ);
		if(ret>=0)
		{
			fs_close(ret);
			uiOpenWindow(&selfTestWindow,0,0);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysComSysMsgBattery
* Description    : sysComSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	u32 bat_state = BATTERY_STAT_MAX;
	if(parameNum == 1)
		bat_state = parame[0];
	if(bat_state >= BATTERY_STAT_MAX)
		return 0;	
	if(bat_state <= BATTERY_STAT_2)
	{
		/*task_com_usbhost_set(USBHOST_STAT_OUT);
		if(husb_api_usensor_tran_sta())
		{
			husb_api_usensor_detech();
			deg_Printf("[SYS] BAT detach Usensor\n");
			
		}*/
		task_com_tips_show(TIPS_TYPE_POWER);
	}
	if(bat_state <= BATTERY_STAT_1)
	{
		if(SysCtrl.poweroffTime == 0) //auto poweroff
			SysCtrl.poweroffTime = FUN_AUTOPOWEROFF_TIME;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : sysComSysMsg1S
* Description    : sysComSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	static u8 lowpower_times_cnt = 0;
	if(SysCtrl.poweroffTime)
	{
		SysCtrl.poweroffTime--;
		if(SysCtrl.poweroffTime==0)
		{
			deg_Printf("sys deal: auto power off\n");
			app_taskStart(TASK_POWER_OFF,0);
		}
	}
	if(SysCtrl.dev_dusb_stat >= USBDEV_STAT_DCIN)
	{
		SysCtrl.low_power_tips = 0;
		lowpower_times_cnt = 0;
	}	
	if(SysCtrl.low_power_tips)
	{
		lowpower_times_cnt++;
		if(lowpower_times_cnt%10 == 0)
		{
			task_com_tips_show(TIPS_TYPE_POWER);	
		}
		if(lowpower_times_cnt>60)
		{
			deg_Printf("sys deal: battery low power off\n");
			app_taskStart(TASK_POWER_OFF,0);

		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : taskComMsgUSBHost
* Description    : taskComMsgUSBHost
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int sysComSysMsgUSBHOST(winHandle handle,u32 parameNum,u32* parame)
{
	u32 usbhost_state = USBHOST_STAT_MAX;
	if(parameNum == 1)
		usbhost_state = parame[0];
	if(usbhost_state >= USBHOST_STAT_MAX)
		return 0;
	if(app_taskCurId() != TASK_RECORD_VIDEO && app_taskCurId() != TASK_RECORD_PHOTO)
	{
		return 0;
	}
	switch(usbhost_state)
	{	
		case USBHOST_STAT_NULL:
			SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINA;
			//app_lcdVideoShowScaler_cfg(0);
			app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
			SysCtrl.dev_stat_power &= ~POWERON_FLAG_WAIT;
			break;
		case USBHOST_STAT_SHOW:
			if(husb_api_usensor_res_type_is_mjp())
			{
				SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINB;
			}else
			{
				SysCtrl.lcdshow_win_mode = LCDSHOW_ONLYWINA;
			}
			//app_lcdVideoShowScaler_cfg(0);
			app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
			SysCtrl.dev_stat_power &= ~POWERON_FLAG_WAIT;
			break;
		default: break;
	}
	return 0;
}
ALIGNED(4) const msgDealInfor sysComMsgDeal[]=
{
	{KEY_EVENT_OK,		sysComKeyMsgAll},
	{KEY_EVENT_UP,		sysComKeyMsgAll},
	{KEY_EVENT_DOWN,	sysComKeyMsgAll},
	{KEY_EVENT_MENU,	sysComKeyMsgAll},
	{KEY_EVENT_MODE,	sysComKeyMsgAll},
	{KEY_EVENT_POWER,	sysComKeyMsgPower},
	{KEY_EVENT_POWEROFF,sysComKeyMsgPowerOff},
	{SYS_EVENT_USBDEV,	sysComSysMsgUSB},
	{SYS_EVENT_SDC,		sysComSysMsgSD},
	{SYS_EVENT_BAT,		sysComSysMsgBattery},
	{SYS_EVENT_1S,		sysComSysMsg1S},
	{SYS_EVENT_USBHOST,	sysComSysMsgUSBHOST},
	{EVENT_MAX,NULL},
};




