/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"


/*******************************************************************************
* Function Name  : taskShowLogoOpen
* Description    : taskShowLogoOpen
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskShowLogoOpen(uint32 arg)
{
	//task_com_usb_dev_out(1);
	//dusb_api_Uninit();
	//app_lcdCsiVideoShowStop();
	//hal_csiEnable(0);

	

	uiOpenWindow(&ShowLogoWindow, 0, 0);	
	
}
/*******************************************************************************
* Function Name  : taskShowLogoClose
* Description    : taskShowLogoClose
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskShowLogoClose(uint32 arg)
{
	task_com_sreen_check(SREEN_RESET_AUTOOFF);
	//task_com_usb_dev_out(0);
	task_com_auto_poweroff(1);
	//app_logo_show(0,1,1); 
	hal_lcdUiEnable(UI_LAYER0,1);
	//hal_gpioWrite(USER_SENSOR_POWER_CH, USER_SENSOR_POWER_PIN,GPIO_HIGH);
}
/*******************************************************************************
* Function Name  : taskShowLogoService
* Description    : taskShowLogoService
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskShowLogoService(uint32 arg)
{
	
}

ALIGNED(4) sysTask_T taskShowLogo =
{
	"Show Logo",
	0,
	taskShowLogoOpen,
	taskShowLogoClose,
	NULL,//taskShowLogoService,
};
