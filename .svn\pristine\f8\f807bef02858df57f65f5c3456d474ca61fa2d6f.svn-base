/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiWidgetManageProc
* Description    : uiWidgetManageProc
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void uiWidgetManageProc(uiWinMsg* msg)
{
	winHandle hCurWin,hParentWin;
	uiWidgetManageObj* pwidgetManage;
	U16 id;
	if(uiWidgetProc(msg))
		return;
	pwidgetManage 	= (uiWidgetManageObj*)uiHandleToPtr(msg->curWin);
	hParentWin 		= pwidgetManage->widget.win.parent;
	switch(msg->id)
	{
		case MSG_WIDGET_NEXT:
			uiWinSendMsgId(uiDialogItem(hParentWin,pwidgetManage->curID),MSG_WIN_UNSELECT);
			if(pwidgetManage->curID < pwidgetManage->maxID)
				pwidgetManage->curID++;
			else
				pwidgetManage->curID = pwidgetManage->minID;
			hCurWin = uiDialogItem(hParentWin,pwidgetManage->curID);
			if(pwidgetManage->getRes)
				uiWinSetResid(hCurWin,pwidgetManage->getRes(pwidgetManage->curID));
			uiWinSendMsgId(hCurWin,MSG_WIN_SELECT);
			return ;
		case MSG_WIDGET_PREV:
			uiWinSendMsgId(uiDialogItem(hParentWin,pwidgetManage->curID),MSG_WIN_UNSELECT);	
			if(pwidgetManage->curID > pwidgetManage->minID)
				pwidgetManage->curID--;
			else
				pwidgetManage->curID = pwidgetManage->maxID;
			hCurWin = uiDialogItem(hParentWin,pwidgetManage->curID);
			if(pwidgetManage->getRes)
				uiWinSetResid(hCurWin,pwidgetManage->getRes(pwidgetManage->curID));
			uiWinSendMsgId(hCurWin,MSG_WIN_SELECT);
			return ;
		case MSG_WIDGET_SELECT:
			id = msg->para.v;
			if(id == pwidgetManage->curID)
				return;
			if(id < pwidgetManage->minID || id > pwidgetManage->maxID)
				return;
			uiWinSendMsgId(uiDialogItem(hParentWin,pwidgetManage->curID),MSG_WIN_UNSELECT);	
			pwidgetManage->curID = id;
			hCurWin = uiDialogItem(hParentWin,pwidgetManage->curID);
			if(pwidgetManage->getRes)
				uiWinSetResid(hCurWin,pwidgetManage->getRes(pwidgetManage->curID));
			uiWinSendMsgId(hCurWin,MSG_WIN_SELECT);
			return;
		case MSG_WIDGET_CUR:
			msg->para.v = pwidgetManage->curID;
			return;
		case MSG_WIDGET_CHANGE_ALL_RESID:
			if(pwidgetManage->getRes == NULL)
				return;
			for(id = pwidgetManage->minID;id <= pwidgetManage->maxID;id++)
			{
				uiWinSetResid(uiDialogItem(hParentWin,id),pwidgetManage->getRes(id));
			}
			return;
		case MSG_WIN_UPDATE_RESID:
			uiWinSetResid(uiDialogItem(hParentWin,pwidgetManage->curID),pwidgetManage->getRes(pwidgetManage->curID));
			return;
		case MSG_WIN_SELECT_INFOR_EX:
		case MSG_WIN_UNSELECT_INFOR_EX:
			for(id = pwidgetManage->minID;id <= pwidgetManage->maxID;id++)
			{
				uiWinSendMsg(uiDialogItem(hParentWin,id),msg);
			}
			return;
	}
	uiWinDefaultProc(msg);
}

/*******************************************************************************
* Function Name  : uiWidgetManageCreate
* Description    : uiWidgetManageCreate
* Input          : widgetCreateInfor* infor,winHandle parent,uiWinCB cb
* Output         : none                                            
* Return         : winHandle
*******************************************************************************/
winHandle uiWidgetManageCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle hwidgetManage;
	uiWidgetManageObj* pwidgetManage;
	
	hwidgetManage =	uiWinCreate(0,0,1,1,parent,uiWidgetManageProc,sizeof(uiWidgetManageObj),WIN_WIDGET|WIN_TOUCH_SUPPORT|infor->style);
	if(hwidgetManage != INVALID_HANDLE)
	{
		pwidgetManage		  = (uiWidgetManageObj*)uiHandleToPtr(hwidgetManage);
		pwidgetManage->getRes = infor->prvate;
		pwidgetManage->curID  = INVALID_WIDGET_ID;
		pwidgetManage->minID  = infor->width;
		pwidgetManage->maxID  = infor->height;
		
		uiWidgetSetId(hwidgetManage,infor->id);
	}
	return hwidgetManage;
}