/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HAL_MD_H
#define HAL_MD_H


/*******************************************************************************
* Function Name  : hal_mdInit
* Description    : hal layer .motion dectetion initial
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
void hal_mdInit(MD_Adapt *pMD);
/*******************************************************************************
* Function Name  : hal_dacCallback
* Description    : hal layer .motion dectetion enable  set
* Input          : u8 en : 1-enable,0-disable
* Output         : None
* Return         : none
*******************************************************************************/
void hal_mdEnable(u8 en);


/*******************************************************************************
* Function Name  : hal_mdEnable_check
* Description    : hal layer .motion dectetion enable  check
* Input          : None
* Output         : None
* Return         : Bool : 1 :md enable , 0 : md disable
*******************************************************************************/
#define hal_mdEnable_check 		hx330x_mdEnable_check


/*******************************************************************************
* Function Name  : hal_mdCheck
* Description    : hal layer .motion dectetion check md event
* Input          : 
* Output         : None
* Return         : u32 : 0 :no md event happend
                             >0: md event happend
*******************************************************************************/
u32 hal_mdCheck(void);





#endif
