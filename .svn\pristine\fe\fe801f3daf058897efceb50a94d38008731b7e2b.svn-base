/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  FS_API_H
    #define  FS_API_H

#include "ffconf.h"
#include "ff.h"
#include "fs_typedef.h"
#include "diskio.h"
#include "../../../hal/inc/hal.h"
#define  FS_CFG_NODE_NUM           2   // support max open file number at the same time




typedef struct FS_EXT_FUNC_S{
	void *(*getCurTime)(void);
	int (*dev_init)(u8 bus);
	bool (*dev_sta)(void);
	s32 (*dev_read)(void *pDataBuf, u32 dwLBA, u32 dwLBANum);
	s32 (*dev_write)(void *pDataBuf, u32 dwLBA, u32 dwLBANum);
}FS_EXT_FUNC_T;

extern FS_EXT_FUNC_T 		fs_exfunc;

/*******************************************************************************
* Function Name  : fs_exfunc_init
* Description    : fs exfunc init
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void fs_exfunc_init(void);
/*******************************************************************************
* Function Name  : fs_nodeinit
* Description    : fs node init
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void fs_nodeinit(void);
/*******************************************************************************
* Function Name  : fs_mount
* Description    : fs mount
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int fs_mount(void);
/*******************************************************************************
* Function Name  : fs_open
* Description    : fs file open
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int fs_open(const char *name,INT32U op);
/*******************************************************************************
* Function Name  : fs_close
* Description    : fs file close
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int fs_close(int fd);
/*******************************************************************************
* Function Name  : fs_read
* Description    : fs file read
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int fs_read(int fd,void *buff,UINT len);
/*******************************************************************************
* Function Name  : fs_seek
* Description    : fs file seek
* Input          : opt: FA_CREATE_LINKMAP / 0
* Output         : none
* Return         : none
*******************************************************************************/
DWORD* fs_getcltbl(int fd);
/*******************************************************************************
* Function Name  : fs_seek
* Description    : fs file seek
* Input          : opt: FA_CREATE_LINKMAP / 0
* Output         : none
* Return         : none
*******************************************************************************/
WORD fs_getclusize(int fd);
/*******************************************************************************
* Function Name  : fs_write
* Description    : fs file write
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int fs_write(int fd,const void *buff,UINT len);
/*******************************************************************************
* Function Name  : fs_seek
* Description    : fs file seek
* Input          : opt: FA_CREATE_LINKMAP / 0
* Output         : none
* Return         : none
*******************************************************************************/
FSIZE_t fs_seek(int fd,FSIZE_t offset,INT32U opt);
/*******************************************************************************
* Function Name  : fs_mkdir
* Description    : fs file make a dir
* Input          : opt: FA_CREATE_LINKMAP / 0
* Output         : none
* Return         : none
*******************************************************************************/
int fs_mkdir(char *path);
/*******************************************************************************
* Function Name  : fs_alloc
* Description    : fs file alloc size
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_alloc(int fd,INT32U size,int mode);  // suggest using mode == 1
/*******************************************************************************
* Function Name  : fs_sync
* Description    : fs file sync
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_sync(int fd);
/*******************************************************************************
* Function Name  : fs_merge
* Description    : fs file merge fd2 to fd1
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_merge(int fd1,int fd2);
/*******************************************************************************
* Function Name  : fs_bound
* Description    : fs file bound fd2 to fd1
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_bound(int fd1,int fd2);
/*******************************************************************************
* Function Name  : fs_getclustersize
* Description    : fs_getclustersize
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
INT32U fs_getclustersize(void);
/*******************************************************************************
* Function Name  : fs_size
* Description    : fs file size get
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
INT32U fs_size(int fd);
/*******************************************************************************
* Function Name  : fs_size
* Description    : fs file pre size get
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
INT32U fs_pre_size(int fd);
/*******************************************************************************
* Function Name  : fs_tell
* Description    : fs file cur point
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
INT32U fs_tell(int fd);

/*******************************************************************************
* Function Name  : fs_free_size
* Description    : fs_free_size
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
INT32U fs_free_size(void);

/*******************************************************************************
* Function Name  : fs_check
* Description    : fs_check
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_check(void);

/*******************************************************************************
* Function Name  : fs_getStartSector
* Description    : fs_getStartSector
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
int fs_getStartSector(int fd);
/*******************************************************************************
* Function Name  : fs_getClustStartSector
* Description    : fs_getClustStartSector
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
//SDRAM_TEXT_SECTION
int fs_getClustStartSector(int fd, u32 clst);
/*******************************************************************************
* Function Name  : fs_ftime
* Description    : fs_ftime
* Input          : 
* Output         : none
* Return         : none
*******************************************************************************/
u32 fs_ftime(int fd);
#endif

