/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/app_api.h"

ALIGNED(4) static LCDSHOW_CTRL_T  app_lcdshow_ctrl;


/*******************************************************************************
* Function Name  : app_lcdVideoShowScaler_cfg
* Description    : set lcd show crop scaler(from csi img)
* Input          : int step: 0: not crop, > 0: crop plus, < 0: crop minus
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdVideoShowScaler_cfg(int step, u8 scaler_type)
{
    static u8 crop_index = 0;
	static u16 sx,ex,sy,ey;
	//deg_Printf("app_lcdVideoShowScaler_cfg:%d, %d\n",step, scaler_type);
	u16 video_ratio_w, video_ratio_h; //csi scaler min, video scaler max
	u16 csi_w, csi_h; //csi scaler max


	u8  scaler_cfg = 0; //1: SCALER CSI , 2 SCALER VIDEO

	if(step == 0 && scaler_type == VIDEO_SCALER_NONE)
	{
		crop_index 	= 0;
		if(app_lcdshow_ctrl.video_layer_en)
		{
			hal_lcdSetRatio(hardware_setup.lcd_ratio_mode, 1);
		}else
		{
			hal_lcdSetRatio(0, 0);
			crop_index = 1;
		}

	}else if(step > 0)
	{
		crop_index++;
		if(app_lcdshow_ctrl.video_layer_en && crop_index == 1)
		{
			u16 video_w, video_h;
			hal_lcdGetVideoRatioResolution(&video_ratio_w, &video_ratio_h);
			hal_lcdGetVideoResolution(&video_w, &video_h);
			if(hardware_setup.lcd_ratio_mode == 0 || (video_ratio_w == video_w && video_ratio_h == video_h))
			{
				crop_index++;
			}else
			{
				hal_lcdSetRatio(0,1);
			}
		}
	}else if(step < 0)
	{
		if(crop_index >= 1)
		{
			crop_index--;
			if(app_lcdshow_ctrl.video_layer_en && crop_index == 0)
			{
				hal_lcdSetRatio(hardware_setup.lcd_ratio_mode,1);
			}
		}
	}
	hal_lcdGetVideoRatioResolution(&video_ratio_w, &video_ratio_h);

	if(app_lcdshow_ctrl.video_layer_en) //预览画面
	{
		hal_SensorResolutionGet(&csi_w,&csi_h);
		if(husb_api_usensor_tran_sta())
		{
			if(husb_api_usensor_res_type_is_yuv())
			{
				scaler_cfg = 1; //CSI SCALER
				husb_api_usensor_res_get(&csi_w,&csi_h);
			}
			else
			{
				scaler_cfg = 3; //scaler video
			}
		}else
		{
			scaler_cfg = 1; //CSI SCALER
			hal_SensorResolutionGet(&csi_w,&csi_h);
		}

	}else //回放界面
	{
		scaler_cfg = 2; //scaler video
	}
	//hx330x_lcdShowWaitDone();
	if(crop_index <= 1)
	{
		if(scaler_cfg != 2)
		{
			hal_lcdSetCsiScaler(0,0,csi_w,csi_h);
		}
		hal_lcdSetVideoScaler(0,0,video_ratio_w,video_ratio_h);
		if(scaler_cfg & 2)
		{		
			sx = 0; sy = 0; ex = video_ratio_w; ey = video_ratio_h;	
		}else
		{
			sx = 0; sy = 0; ex = csi_w; ey = csi_h;
		}	

	}else
	{
		int scaler_step_w = (LCDSHOW_SCALER_STEP *  video_ratio_w / 100) & ~3;
		int scaler_step_h = (LCDSHOW_SCALER_STEP *  video_ratio_h / 100) & ~3;
		u16 scaler_w_max,scaler_w_min;
		u16 scaler_h_max,scaler_h_min;
		scaler_w_min = (video_ratio_w*(LCDSHOW_SCALER_MIN)/100) &~1;
		scaler_h_min = (video_ratio_h*(LCDSHOW_SCALER_MIN)/100) &~1;
		if(scaler_cfg & 2) //video scaler
		{
			scaler_w_max = video_ratio_w;
			scaler_h_max = video_ratio_h;
		}else
		{
			scaler_w_max = csi_w;
			scaler_h_max = csi_h;
		}
		scaler_type = hal_lcdVideoScalerTypeAdj(scaler_type);
		int sx_temp,ex_temp,sy_temp,ey_temp;
		sx_temp = sx; sy_temp = sy; ex_temp = ex; ey_temp = ey;
		switch(scaler_type)
		{
			case VIDEO_SCALER_CENTER:
				if(step > 0)
				{
					if( ex - sx - scaler_step_w < scaler_w_min)
					{
						scaler_step_w = ((ex - sx - scaler_w_min)/2) & ~1;
					}else
					{
						scaler_step_w = scaler_step_w /2;
					}
					sx_temp += scaler_step_w; ex_temp -= scaler_step_w;
					if( ey - sy - scaler_step_h < scaler_h_min)
					{
						scaler_step_h = ((ey - sy - scaler_h_min)/2) & ~1;
					}else
					{
						scaler_step_h = scaler_step_h /2;
					}
					sy_temp += scaler_step_h; ey_temp -= scaler_step_h;
				}else if(step < 0)
				{
					scaler_step_w = scaler_step_w/2;
					scaler_step_h = scaler_step_h/2;
					sx_temp = (sx < scaler_step_w) ? 0 :(sx - scaler_step_w);
					ex_temp = (scaler_w_max < (ex + scaler_step_w)) ? scaler_w_max :(ex + scaler_step_w);
					sy_temp = (sy < scaler_step_h) ? 0 :(sy - scaler_step_h);
					ey_temp = (scaler_h_max < (ey + scaler_step_h)) ? scaler_h_max :(ey + scaler_step_h);
				}

				break;
			case VIDEO_SCALER_LEFT:

				if(sx_temp >= scaler_step_w)
				{
					sx_temp -= scaler_step_w; ex_temp -= scaler_step_w;
				}else
				{
					sx_temp = 0; ex_temp = ex - sx;
				}
				break;
			case VIDEO_SCALER_RIGHT:
				if( (ex_temp + scaler_step_w) < scaler_w_max)
				{
					sx_temp += scaler_step_w; ex_temp += scaler_step_w;
				}else
				{
					sx_temp = scaler_w_max - (ex - sx); ex_temp = scaler_w_max;
				}
				break;
			case VIDEO_SCALER_UP:
				if(sy_temp >= scaler_step_h)
				{
					sy_temp -= scaler_step_h; ey_temp -= scaler_step_h;
				}else
				{
					sy_temp = 0; ey_temp = ey - sy;
				}
				break;
			case VIDEO_SCALER_DOWN:
				if( (ey_temp + scaler_step_h) < scaler_h_max)
				{
					sy_temp += scaler_step_h; ey_temp += scaler_step_h;
				}else
				{
					sy_temp = scaler_h_max - (ey - sy); ey_temp = scaler_h_max;
				}
				break;
			default:
				break;
		}

		if(sx_temp != sx || sy_temp != sy || ex_temp != ex || ey_temp != ey)
		{
			sx = sx_temp;
			sy = sy_temp;
			ex = ex_temp;
			ey = ey_temp;

			if(scaler_cfg & 2) //video scaler
			{
				hal_lcdSetVideoScaler(sx,sy,ex,ey);
			}else
			{
				scaler_w_max = ex - sx;
				scaler_h_max = ey - sy;
				if(scaler_w_max >= video_ratio_w && scaler_h_max >= video_ratio_h) //just csi scaler
				{
					hal_lcdSetCsiScaler(sx,sy,ex,ey);
					hal_lcdSetVideoScaler(0,0,video_ratio_w,video_ratio_h);
				}else
				{
					if(scaler_w_max < video_ratio_w)
					{
						if(sx_temp + video_ratio_w > csi_w)
						{
							sx_temp = csi_w - video_ratio_w;
							ex_temp = csi_w;
						}else
						{
							ex_temp = sx_temp + video_ratio_w;
						}
					}
					if(scaler_h_max < video_ratio_h)
					{
						if(sy_temp + video_ratio_h > csi_h)
						{
							sy_temp = csi_h - video_ratio_h;
							ey_temp = csi_h;
						}else
						{
							ey_temp = sy_temp + video_ratio_h;
						}
					}

					hal_lcdSetCsiScaler(sx_temp,sy_temp,ex_temp,ey_temp);
					if(scaler_w_max < video_ratio_w)
					{
						ex_temp = ex - sx_temp;
						sx_temp = sx - sx_temp;
					}else
					{
						ex_temp = video_ratio_w;
						sx_temp = 0;
					}
					if(scaler_h_max < video_ratio_h)
					{
						ey_temp = ey - sy_temp;
						sy_temp = sy - sy_temp;
					}else
					{
						ey_temp = video_ratio_h;
						sy_temp = 0;
					}
					hal_lcdSetVideoScaler(sx_temp, sy_temp, ex_temp, ey_temp);
				}
			}
		}else
		{
			if(step != 0 && scaler_type != VIDEO_SCALER_NONE)
			{
				if(step > 0) 		crop_index--;
				else if(step < 0) 	crop_index++;
			}
		}

	}
	SysCtrl.lcd_scaler_level = (crop_index-1)<0?0:(crop_index-1);

	deg_Printf("[CSI scaler]: [%d, %d] [%d %d %d %d]\n", scaler_cfg,crop_index, sx,sy,ex,ey);
	return crop_index;
}

/*******************************************************************************
* Function Name  : app_lcdVideoShowRotate_cfg
* Description    : user cfg display video rotate
* Input          : step: 0 user config default, > 0: add 90 degree, <0 : minus 90 degree
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdVideoShowRotate_cfg(int step)
{
	static u8 scan_mode = 0;

    if(step == 0)
    {
        scan_mode = hardware_setup.lcd_video_rotate_mode;
    }
	u8 rotate = scan_mode & LCD_DISPLAY_ROTATE_MASK;
	if(step > 0)
	{
		rotate = (rotate >= 2)?0:(1 +1);
	}else if(step < 0)
	{
		rotate = (rotate == 0)?LCD_DISPLAY_ROTATE_MASK:(rotate -1);
	}
	scan_mode = (scan_mode &~LCD_DISPLAY_ROTATE_MASK)|rotate;

	if(hal_lcdVideoSetRotate(scan_mode) < 0)
		return -1;
	if(app_lcdshow_ctrl.video_layer_en)
	{
		hal_lcdCsiShowStop();

	}
	//hx330x_lcdShowWaitDone();
	lcdshow_frame_t * p_lcd_buffer = NULL;
	do{
		p_lcd_buffer = hal_lcdVideoIdleFrameMalloc();
	}while(p_lcd_buffer == NULL);
	hal_lcdVideoRotateUpdata(p_lcd_buffer); //ROTATE UPDATA, ratio updata buf change
	hal_dispframeFree(p_lcd_buffer);
	if(app_lcdshow_ctrl.video_layer_en)
	{
		if(!husb_api_usensor_tran_sta()  || (husb_api_usensor_tran_sta() && husb_api_usensor_res_type_is_mjp()) )
		{
			hal_csi_input_switch(0, 0);
		}
	}

	//app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
	if(app_lcdshow_ctrl.video_layer_en)
	{
		hal_lcdCsiShowStart();
		if(SysCtrl.lcdshow_win_mode == LCDSHOW_ONLYWINB)
		{
			u16 video_w, video_h, csi_w, csi_h;
			hal_lcdGetVideoRatioResolution(&video_w,&video_h);
			hal_SensorResolutionGet(&csi_w,&csi_h);
			video_w = hx330x_min(csi_w, video_w);
			video_h = hx330x_min(csi_h, video_h);
    		hal_lcdWinEnablePreSet(1);
			hal_lcdSetWINAB(LCDWIN_A,LCDWIN_BOT_LAYER,0,0,video_w,video_h,WINAB_EN);
			hal_lcdSetWINAB(LCDWIN_B,LCDWIN_TOP_LAYER,0,0,video_w,video_h,WINAB_EN);
		}
	}
	return 0;
}



int app_lcdVideoShowMirro_cfg(int step)
{
	static u8 scan_mode = 0;

    if(step == 0)
    {
        scan_mode = hardware_setup.lcd_video_rotate_mode;
    }
	u8 mirro = scan_mode & LCD_DISPLAY_MIRROR_MASK;
	if(step > 0)
	{
		//rotate = (rotate >= LCD_DISPLAY_ROTATE_MASK)?0:(rotate +1);
		mirro = (mirro == 0)?0x30:0;
	}else if(step < 0)
	{
		//rotate = (rotate == 0)?LCD_DISPLAY_ROTATE_MASK:(rotate -1);
		mirro = (mirro == 0)?0x30:0;
	}
	
	scan_mode = (scan_mode &~LCD_DISPLAY_MIRROR_MASK)|mirro;
	deg_Printf("mirro=%d,scan_mode=%d\r\n",mirro,scan_mode);

	if(hal_lcdVideoSetRotate(scan_mode) < 0)
		return -1;
	if(app_lcdshow_ctrl.video_layer_en)
	{
		hal_lcdCsiShowStop();

	}
	//hx330x_lcdShowWaitDone();
	lcdshow_frame_t * p_lcd_buffer = NULL;
	do{
		p_lcd_buffer = hal_lcdVideoIdleFrameMalloc();
	}while(p_lcd_buffer == NULL);
	hal_lcdVideoRotateUpdata(p_lcd_buffer); //ROTATE UPDATA, ratio updata buf change
	hal_dispframeFree(p_lcd_buffer);
	if(app_lcdshow_ctrl.video_layer_en)
	{
		if(!husb_api_usensor_tran_sta()  || (husb_api_usensor_tran_sta() && husb_api_usensor_res_type_is_mjp()) )
		{
			hal_csi_input_switch(0, 0);
		}
	}

	//app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
	if(app_lcdshow_ctrl.video_layer_en)
	{
		hal_lcdCsiShowStart();
		if(SysCtrl.lcdshow_win_mode == LCDSHOW_ONLYWINB)
		{
			u16 video_w, video_h, csi_w, csi_h;
			hal_lcdGetVideoRatioResolution(&video_w,&video_h);
			hal_SensorResolutionGet(&csi_w,&csi_h);
			video_w = hx330x_min(csi_w, video_w);
			video_h = hx330x_min(csi_h, video_h);
    		hal_lcdWinEnablePreSet(1);
			hal_lcdSetWINAB(LCDWIN_A,LCDWIN_BOT_LAYER,0,0,video_w,video_h,WINAB_EN);
			hal_lcdSetWINAB(LCDWIN_B,LCDWIN_TOP_LAYER,0,0,video_w,video_h,WINAB_EN);
		}
	}
	return 0;
}

/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStart
* Description    : lcd video show start for showing csi, will reset scaler
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdCsiVideoShowStart(void)
{
	if(app_lcdshow_ctrl.video_layer_en)
		return 0;
	app_lcdshow_ctrl.video_layer_en = 1;
	if(app_lcdVideoShowRotate_cfg(0) < 0)
	{
		hal_lcdSetRatio(hardware_setup.lcd_ratio_mode,app_lcdshow_ctrl.video_layer_en);
		app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
		hal_lcdCsiShowStart();
	}

	return 0;
}
/*******************************************************************************
* Function Name  : app_lcdCsiVideoLayerEnGet
* Description    : lcd video show start for showing csi, will reset scaler
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdCsiVideoLayerEnGet(void)
{
	return app_lcdshow_ctrl.video_layer_en;
}
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStop
* Description    : lcd video csi show stop
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void app_lcdCsiVideoShowStop(void)
{
	if(app_lcdshow_ctrl.video_layer_en == 0)
		return;
	app_lcdshow_ctrl.video_layer_en = 0;
	hal_lcdCsiShowStop();
	if(app_lcdVideoShowRotate_cfg(0) < 0)
	{
		hal_lcdSetRatio(hardware_setup.lcd_ratio_mode,app_lcdshow_ctrl.video_layer_en);
		app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
	}
}

/*******************************************************************************
* Function Name  : app_lcdVideoIdleFrameGet
* Description    : lcd video show idle frame get
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void* app_lcdVideoIdleFrameGet(void)
{
    return (void *) hal_lcdVideoIdleFrameMalloc();
}

/*******************************************************************************
* Function Name  : app_lcdUiShowInit
* Description    : lcd ui show init
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
int app_lcdUiShowInit(void)
{
	if(app_lcdshow_ctrl.ui_layer_en != 0)
		return 0;
	u16	ui_w, ui_h, sreen_w,sreen_h;
	u16 pos_x, pos_y;
	if(hal_lcdGetUiResolution(&ui_w,&ui_h) < 0)
		return -2;
	if(hal_lcdGetUiPosition(&pos_x,&pos_y) < 0)
			return -3;
	if(hal_lcdGetSreenResolution(&sreen_w,&sreen_h) < 0)
		return -4;
    u32 palette 	= (u32)hal_sysMemMalloc(256 * 4);
	if(palette == 0)
		return -6;
    INT32 scanmode = hal_lcdUiScanModeGet();
    // load palette
	if(res_iconGetPalette(R_ID_BIN_PALETTE, (u8*)palette) <= 0)
	{
		hal_sysMemFree((void *)palette);
		palette = 0;
	}
    hal_uiLzoInit();
	hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, pos_x,pos_y,	palette,scanmode & 0x03);
#if 0
	switch(scanmode & 0x03)
	{
		case LCD_DISPLAY_ROTATE_0:		hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, pos_x,		         pos_y,	               palette,	scanmode & 0x03); 	break;
		case LCD_DISPLAY_ROTATE_90:		hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, pos_y,	             sreen_w-pos_x-ui_w,   palette,	scanmode & 0x03);	break;
		case LCD_DISPLAY_ROTATE_180:	hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, sreen_w-pos_x-ui_w,   sreen_h-pos_y-ui_h,   palette,	scanmode & 0x03);	break;
		case LCD_DISPLAY_ROTATE_270:	hal_lcdUiInit(UI_LAYER0,ui_w,ui_h, sreen_h-pos_y-ui_h,   pos_x,	               palette,	scanmode & 0x03);	break;
		default: 				hal_sysMemFree((void *)palette); return -5;//
	}
#endif
    hal_lcdUiEnable(UI_LAYER0,1);
    hal_sysMemFree((void *)palette);
	app_lcdshow_ctrl.ui_layer_en = 1;
	return 0;
}
/*******************************************************************************
* Function Name  : app_lcdCsiVideoShowStop
* Description    : lcd video csi show stop
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void app_lcdUiShowUinit(void)
{
	if(app_lcdshow_ctrl.ui_layer_en)
		hal_lcdUiEnable(UI_LAYER0,0);
	app_lcdshow_ctrl.ui_layer_en = 0;
}
/*******************************************************************************
* Function Name  : app_lcdUiDrawIdleFrameGet
* Description    : lcd ui draw idle frame get
* Input          : none
* Output         : none
* Return         : 0
*******************************************************************************/
void* app_lcdUiDrawIdleFrameGet(void)
{
    return (void *) hal_uiDrawBufMalloc(UI_LAYER0);
}
/*******************************************************************************
* Function Name  : app_lcdShowWinModeCfg
* Description    : config lcdshow WINAB mode
* Input          : INT8U mode : mode index
* Output         : none
* Return         : int 0:success
*******************************************************************************/
int app_lcdShowWinModeCfg(INT8U mode)
{
	static u32 winMode = LCDSHOW_WINBMAX;
	static u32 csi_src_save = 2;
	u16 csi_w,csi_h;
	u32 csi_src;
    u16 video_w,video_h;

	deg_Printf("app_lcdShowWinModeCfg:%d\n",mode);
	if(husb_api_usensor_tran_sta() && husb_api_usensor_res_type_is_yuv())
	{
		csi_src = 1;
	}else{
		csi_src = 0;
	}
	if(winMode == mode && csi_src_save == csi_src)
	{
		return 0;
	}
	winMode = mode;
	csi_src_save = csi_src;
	switch(mode)
	{
    	case LCDSHOW_ONLYWINA:
			hal_csiEnable(1);
			hal_lcdCsiShowStop();
			hal_csi_input_switch(csi_src, 0);
			app_lcdshow_ctrl.video_layer_en = 0;
			app_lcdCsiVideoShowStart();

			if(csi_src)
			{
				husb_api_usensor_res_get(&csi_w,&csi_h);
			}else
			{
				hal_SensorResolutionGet(&csi_w,&csi_h);
			}
			hal_lcdGetVideoRatioResolution(&video_w,&video_h);
			video_w = hx330x_min(csi_w, video_w);
			video_h = hx330x_min(csi_h, video_h);
			//deg_Printf("WIN:%d, %d\n", video_w, video_h);

			hal_lcdWinEnablePreSet(1);
			hal_lcdSetWINAB(LCDWIN_A,LCDWIN_TOP_LAYER,0,0,video_w,video_h,WINAB_EN);
			if(husb_api_usensor_tran_sta() && husb_api_usensor_res_type_is_mjp())
			{
				hal_lcdSetWINAB(LCDWIN_B,LCDWIN_BOT_LAYER,0,0,video_w,video_h,WINAB_EN);
			}else{
				hal_lcdSetWINAB(LCDWIN_B,LCDWIN_BOT_LAYER,0,0,video_w,video_h,WINAB_DIS);
			}
			hx330x_dmaNocWinA();
			
			return 0;
    	case LCDSHOW_ONLYWINB:
			hal_csiEnable(1);
			if(!husb_api_usensor_tran_sta() || !husb_api_usensor_res_type_is_mjp())
				return 0;
			hal_lcdCsiShowStop();
			hal_csi_input_switch(0, 0);
			app_lcdshow_ctrl.video_layer_en = 0;
			app_lcdCsiVideoShowStart();
			hal_lcdGetVideoRatioResolution(&video_w,&video_h);
			hal_SensorResolutionGet(&csi_w,&csi_h);
			video_w = hx330x_min(csi_w, video_w);
			video_h = hx330x_min(csi_h, video_h);
    		hal_lcdWinEnablePreSet(1);
			hal_lcdSetWINAB(LCDWIN_A,LCDWIN_BOT_LAYER,0,0,video_w,video_h,WINAB_EN);
			hal_lcdSetWINAB(LCDWIN_B,LCDWIN_TOP_LAYER,0,0,video_w,video_h,WINAB_EN);
			hx330x_dmaNocWinB();
			
			return 0;
		case LCDSHOW_WIN_DISABLE: 
			hal_csiEnable(0); 
			app_lcdCsiVideoShowStop(); 
			hal_lcdSetWinEnable(0); 
			hx330x_dmaNocWinDis(); 
			return 0;
		default:	return -1;
	}

}




















