Archive member included to satisfy reference by file (symbol)

..\lib\libboot.a(boot.o)      obj\Debug\dev\battery\src\battery_api.o (boot_vddrtcCalculate)
..\lib\libboot.a(boot_loader.o)
                              obj\Debug\mcu\boot\spi_boot_cfg.o (.bootsect)
..\lib\libboot.a(reset.o)     obj\Debug\mcu\boot\spi_boot_cfg.o (_start)
..\lib\libboot.a(boot_lib.o)  ..\lib\libboot.a(boot_loader.o) (boot_sdram_init)
..\lib\libmcu.a(hx330x_adc.o)
                              obj\Debug\hal\src\hal_adc.o (hx330x_adcEnable)
..\lib\libmcu.a(hx330x_auadc.o)
                              obj\Debug\hal\src\hal_auadc.o (hx330x_auadcHalfIRQRegister)
..\lib\libmcu.a(hx330x_csi.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_csiInit)
..\lib\libmcu.a(hx330x_dac.o)
                              obj\Debug\hal\src\hal_dac.o (hx330x_dacTypeCfg)
..\lib\libmcu.a(hx330x_dma.o)
                              obj\Debug\app\app_common\src\app_lcdshow.o (hx330x_dmaNocWinA)
..\lib\libmcu.a(hx330x_dmauart.o)
                              obj\Debug\hal\src\hal_dmauart.o (hx330x_DmaUart_con_cfg)
..\lib\libmcu.a(hx330x_gpio.o)
                              ..\lib\libmcu.a(hx330x_csi.o) (hx330x_gpioSFRSet)
..\lib\libmcu.a(hx330x_iic.o)
                              obj\Debug\hal\src\hal_iic.o (hx330x_iic0Init)
..\lib\libmcu.a(hx330x_int.o)
                              ..\lib\libboot.a(reset.o) (fast_isr)
..\lib\libmcu.a(hx330x_isp.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_isp_mask_tab_cfg)
..\lib\libmcu.a(hx330x_isp_tab.o)
                              ..\lib\libmcu.a(hx330x_isp.o) (GAOS3X3_TAB)
..\lib\libmcu.a(hx330x_jpg.o)
                              obj\Debug\hal\src\hal_lcdshow.o (hx330x_mjpA_EncodeISRRegister)
..\lib\libmcu.a(hx330x_jpg_tab.o)
                              ..\lib\libmcu.a(hx330x_jpg.o) (hx330x_mjpA_table_init)
..\lib\libmcu.a(hx330x_lcd.o)
                              obj\Debug\dev\lcd\src\lcd_api.o (hx330x_lcdReset)
..\lib\libmcu.a(hx330x_lcdrotate.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_rotateIRQHandler)
..\lib\libmcu.a(hx330x_lcdui.o)
                              obj\Debug\dev\usb\dusb\src\dusb_msc.o (hx330x_lcdShowWaitDone)
..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_uiLzoIRQHandler)
..\lib\libmcu.a(hx330x_lcdwin.o)
                              obj\Debug\hal\src\hal_lcdshow.o (hx330x_lcdWinABConfig)
..\lib\libmcu.a(hx330x_md.o)  obj\Debug\hal\src\hal_md.o (hx330x_mdEnable)
..\lib\libmcu.a(hx330x_mipi.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_MipiCSIUinit)
..\lib\libmcu.a(hx330x_misc.o)
                              obj\Debug\dev\touchpanel\src\touchpanel_api.o (hx330x_abs)
..\lib\libmcu.a(hx330x_rtc.o)
                              obj\Debug\hal\src\hal_rtc.o (hx330x_rtcRamRead)
..\lib\libmcu.a(hx330x_sd.o)  obj\Debug\dev\sd\src\sd_api.o (hx330x_sd0Init)
..\lib\libmcu.a(hx330x_spi0.o)
                              obj\Debug\hal\src\hal_spi.o (hx330x_spi0ManualInit)
..\lib\libmcu.a(hx330x_spi1.o)
                              obj\Debug\hal\src\hal_spi1.o (hx330x_spi1_CS_Config)
..\lib\libmcu.a(hx330x_sys.o)
                              obj\Debug\dev\gsensor\src\gsensor_da380.o (hx330x_sysCpuMsDelay)
..\lib\libmcu.a(hx330x_timer.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_timer0IRQHandler)
..\lib\libmcu.a(hx330x_tminf.o)
                              obj\Debug\hal\src\hal_watermark.o (hx330x_mjpA_TimeinfoEnable)
..\lib\libmcu.a(hx330x_uart.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_uart0IRQHandler)
..\lib\libmcu.a(hx330x_usb.o)
                              obj\Debug\dev\usb\dusb\src\dusb_api.o (hx330x_usb20_CallbackRegister)
..\lib\libmcu.a(hx330x_wdt.o)
                              obj\Debug\dev\usb\dusb\src\dusb_msc.o (hx330x_wdtEnable)
..\lib\libmcu.a(hx330x_emi.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_emiIRQHandler)
..\lib\libisp.a(hal_isp.o)    obj\Debug\dev\sensor\src\sensor_api.o (hal_sensor_fps_adpt)
..\lib\libjpg.a(hal_jpg.o)    obj\Debug\hal\src\hal_mjpAEncode.o (hal_mjp_enle_init)
..\lib\liblcd.a(hal_lcd.o)    obj\Debug\hal\src\hal_lcdshow.o (lcd_show_ctrl)
..\lib\liblcd.a(hal_lcdMem.o)
                              ..\lib\liblcd.a(hal_lcd.o) (hal_lcdAddrCalculate)
..\lib\liblcd.a(hal_lcdrotate.o)
                              ..\lib\liblcd.a(hal_lcd.o) (hal_rotateInit)
..\lib\liblcd.a(hal_lcdUi.o)  obj\Debug\app\app_common\src\app_lcdshow.o (hal_uiDrawBufMalloc)
..\lib\liblcd.a(hal_lcdUiLzo.o)
                              ..\lib\liblcd.a(hal_lcdUi.o) (hal_uiLzokick)
..\lib\liblcd.a(lcd_tab.o)    obj\Debug\dev\lcd\src\lcd_api.o (hal_lcdParaLoad)
..\lib\libmultimedia.a(api_multimedia.o)
                              obj\Debug\multimedia\audio\audio_playback.o (api_multimedia_init)
..\lib\libmultimedia.a(avi_dec.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_dec_func)
..\lib\libmultimedia.a(avi_odml_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_odml_enc_func)
..\lib\libmultimedia.a(avi_std_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_std_enc_func)
..\lib\libmultimedia.a(wav_dec.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (wav_dec_func)
..\lib\libmultimedia.a(wav_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (wav_enc_func)
..\lib\libmultimedia.a(wav_pcm.o)
                              ..\lib\libmultimedia.a(wav_enc.o) (pcm_encode)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                              obj\Debug\app\user_config\src\mbedtls_md5.o (memcmp)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                              obj\Debug\dev\sd\src\sd_api.o (memcpy)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                              obj\Debug\dev\fs\src\fs_api.o (memset)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                              obj\Debug\dev\gsensor\src\gsensor_api.o (strcpy)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                              obj\Debug\dev\fs\src\ff.o (__udivdi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                              obj\Debug\dev\fs\src\ff.o (__umoddi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
                              E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__udivsi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
                              E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__umodsi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                              E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__clz_tab)

Allocating common symbols
Common symbol       size              file

flash_success       0x1               obj\Debug\app\app_common\src\main.o
WAV_TYPE_E          0x4               obj\Debug\dev\battery\src\battery_api.o
SysCtrl             0x130             obj\Debug\app\app_common\src\app_init.o
gsensor_ctl         0x8               obj\Debug\dev\gsensor\src\gsensor_api.o
RGB_GMMA_Tab        0x300             ..\lib\libmcu.a(hx330x_isp.o)
ui_draw_ctrl        0x10              obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
usb_dev_ctl         0x29c             obj\Debug\dev\usb\dusb\src\dusb_api.o
rc128k_div          0x4               ..\lib\libmcu.a(hx330x_rtc.o)
husb_ctl            0x1484            obj\Debug\dev\usb\husb\src\husb_api.o
rtcSecondISR        0x4               ..\lib\libmcu.a(hx330x_rtc.o)
task_play_audio_stat
                    0x4               obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
lcdshow_frame_op    0x1e8             ..\lib\liblcd.a(hal_lcdMem.o)
sd_update_op        0x64              obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
mediaVideoCtl       0x74              obj\Debug\multimedia\video\video_record.o
Y_GMA_Tab           0x200             ..\lib\libmcu.a(hx330x_isp.o)
rtcAlamISR          0x4               ..\lib\libmcu.a(hx330x_rtc.o)
usensor_handle      0x4               obj\Debug\dev\usb\husb\src\husb_usensor.o
USB_CH              0x4               obj\Debug\dev\battery\src\battery_api.o
UVC_CACHE_STA       0x4               obj\Debug\dev\battery\src\battery_api.o
fs_exfunc           0x14              obj\Debug\dev\fs\src\fs_api.o
SDCON0_T            0x4               obj\Debug\dev\battery\src\battery_api.o
usbDeviceOp         0xc               obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
XOSNesting          0x4               obj\Debug\mcu\xos\xos.o
smph_dmacopy        0x4               ..\lib\libmcu.a(hx330x_sys.o)
mainTaskOp          0x48              obj\Debug\app\task_windows\task_main\src\taskMain.o
SDCON1_T            0x4               obj\Debug\dev\battery\src\battery_api.o
tp_api_t            0x20              obj\Debug\dev\touchpanel\src\touchpanel_api.o
hx330x_lcdISR       0x14              ..\lib\libmcu.a(hx330x_lcd.o)
dev_key_tab         0x78              obj\Debug\dev\key\src\key_api.o
hx330x_timerISR     0x10              ..\lib\libmcu.a(hx330x_timer.o)
uhub_handle         0x4               obj\Debug\dev\usb\husb\src\husb_hub.o
UVC_FSTACK_STA      0x4               obj\Debug\dev\battery\src\battery_api.o
rc128k_rtc_cnt      0x4               ..\lib\libmcu.a(hx330x_rtc.o)
lcd_show_ctrl       0x84              ..\lib\liblcd.a(hal_lcd.o)
recordPhotoOp       0x20              obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
rc128k_timer_cnt    0x4               ..\lib\libmcu.a(hx330x_rtc.o)
playVideoOp         0x44              obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
CHANNEL_EXCHANGE_E  0x4               obj\Debug\dev\battery\src\battery_api.o

Memory Configuration

Name             Origin             Length             Attributes
boot             0x01fffc00         0x00000200
ram_boot         0x00000000         0x00006c00
ram_user         0x00000000         0x00007000
usb_ram          0x00008000         0x00008000
line_ram         0x00200000         0x00010000
mp3_text         0x00008000         0x00008000
mp3_ram          0x00200000         0x00010000
nes_text         0x00008000         0x00008000
nes_ram          0x00200000         0x00010000
sdram            0x02000000         0x00800000
flash            0x06000000         0x00800000
exsdram          0x00000000         0x00800000
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD obj\Debug\dev\battery\src\battery_api.o
LOAD obj\Debug\dev\dev_api.o
LOAD obj\Debug\dev\fs\src\diskio.o
LOAD obj\Debug\dev\fs\src\ff.o
LOAD obj\Debug\dev\fs\src\ffunicode.o
LOAD obj\Debug\dev\fs\src\fs_api.o
LOAD obj\Debug\dev\gsensor\src\gsensor_api.o
LOAD obj\Debug\dev\gsensor\src\gsensor_da380.o
LOAD obj\Debug\dev\gsensor\src\gsensor_gma301.o
LOAD obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
LOAD obj\Debug\dev\ir\src\ir_api.o
LOAD obj\Debug\dev\key\src\key_api.o
LOAD obj\Debug\dev\lcd\src\lcd_api.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
LOAD obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
LOAD obj\Debug\dev\led\src\led_api.o
LOAD obj\Debug\dev\led_pwm\src\led_pwm_api.o
LOAD obj\Debug\dev\nvfs\src\nvfs_api.o
LOAD obj\Debug\dev\nvfs\src\nvfs_jpg.o
LOAD obj\Debug\dev\sd\src\sd_api.o
LOAD obj\Debug\dev\sensor\src\sensor_api.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
LOAD obj\Debug\dev\sensor\src\sensor_tab.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_api.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_iic.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_api.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_enum.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_msc.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_uac.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_uvc.o
LOAD obj\Debug\dev\usb\husb\src\husb_api.o
LOAD obj\Debug\dev\usb\husb\src\husb_enum.o
LOAD obj\Debug\dev\usb\husb\src\husb_hub.o
LOAD obj\Debug\dev\usb\husb\src\husb_tpbulk.o
LOAD obj\Debug\dev\usb\husb\src\husb_usensor.o
LOAD obj\Debug\dev\usb\husb\src\husb_uvc.o
LOAD obj\Debug\hal\src\hal_adc.o
LOAD obj\Debug\hal\src\hal_auadc.o
LOAD obj\Debug\hal\src\hal_csi.o
LOAD obj\Debug\hal\src\hal_dac.o
LOAD obj\Debug\hal\src\hal_dmauart.o
LOAD obj\Debug\hal\src\hal_eeprom.o
LOAD obj\Debug\hal\src\hal_gpio.o
LOAD obj\Debug\hal\src\hal_iic.o
LOAD obj\Debug\hal\src\hal_int.o
LOAD obj\Debug\hal\src\hal_lcdshow.o
LOAD obj\Debug\hal\src\hal_md.o
LOAD obj\Debug\hal\src\hal_mjpAEncode.o
LOAD obj\Debug\hal\src\hal_mjpBEncode.o
LOAD obj\Debug\hal\src\hal_mjpDecode.o
LOAD obj\Debug\hal\src\hal_rtc.o
LOAD obj\Debug\hal\src\hal_spi.o
LOAD obj\Debug\hal\src\hal_spi1.o
LOAD obj\Debug\hal\src\hal_stream.o
LOAD obj\Debug\hal\src\hal_sys.o
LOAD obj\Debug\hal\src\hal_timer.o
LOAD obj\Debug\hal\src\hal_uart.o
LOAD obj\Debug\hal\src\hal_watermark.o
LOAD obj\Debug\hal\src\hal_wdt.o
LOAD obj\Debug\mcu\boot\spi_boot_cfg.o
LOAD obj\Debug\mcu\xos\xmbox.o
LOAD obj\Debug\mcu\xos\xmsgq.o
LOAD obj\Debug\mcu\xos\xos.o
LOAD obj\Debug\mcu\xos\xwork.o
LOAD obj\Debug\multimedia\audio\audio_playback.o
LOAD obj\Debug\multimedia\audio\audio_record.o
LOAD obj\Debug\multimedia\image\image_decode.o
LOAD obj\Debug\multimedia\image\image_encode.o
LOAD obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
LOAD obj\Debug\multimedia\video\video_playback.o
LOAD obj\Debug\multimedia\video\video_record.o
LOAD obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
LOAD obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
LOAD obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
LOAD obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
LOAD obj\Debug\sys_manage\res_manage\res_manage_api.o
LOAD obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
LOAD obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
LOAD obj\Debug\app\app_common\src\app_init.o
LOAD obj\Debug\app\app_common\src\app_lcdshow.o
LOAD obj\Debug\app\app_common\src\main.o
LOAD obj\Debug\app\resource\user_res.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
LOAD obj\Debug\app\task_windows\msg_api.o
LOAD obj\Debug\app\task_windows\task_api.o
LOAD obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
LOAD obj\Debug\app\task_windows\task_common\src\task_common.o
LOAD obj\Debug\app\task_windows\task_common\src\task_common_msg.o
LOAD obj\Debug\app\task_windows\task_main\src\taskMain.o
LOAD obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
LOAD obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
LOAD obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
LOAD obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
LOAD obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
LOAD obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
LOAD obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
LOAD obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
LOAD obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
LOAD obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
LOAD obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
LOAD obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
LOAD obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
LOAD obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
LOAD obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
LOAD obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
LOAD obj\Debug\app\task_windows\windows_api.o
LOAD obj\Debug\app\user_config\src\mbedtls_md5.o
LOAD obj\Debug\app\user_config\src\user_config_api.o
LOAD obj\Debug\app\user_config\src\user_config_tab.o
                0x00200000                __sdram_size = (boot_sdram_size == 0x1)?0x800000:0x200000

.bootsec        0x01fffc00      0x200 load address 0x00000000
 *(.bootsec)
 .bootsec       0x01fffc00      0x200 ..\lib\libboot.a(boot_loader.o)
                0x01fffc10                hex
                0x01fffc10                .hex
                0x01fffd1c                .bootsect

.boot_code      0x00000000     0x29a8 load address 0x00000200
                0x00000000                _boot_vma = .
 *(.vector)
 .vector        0x00000000      0x380 ..\lib\libboot.a(reset.o)
                0x000002a0                _start
                0x000002ec                _step_in
                0x00000330                _step_out
 *(.vector.kepttext)
 .vector.kepttext
                0x00000380       0x6c obj\Debug\dev\dev_api.o
                0x00000380                exception_lowpower_io_cfg
 .vector.kepttext
                0x000003ec      0x44c ..\lib\libboot.a(boot.o)
                0x000004d8                boot_vddrtcCalculate
                0x00000504                boot_putchar
                0x000005b0                boot_uart_puts
                0x00000604                exception
                0x0000081c                exception_trigger
 .vector.kepttext
                0x00000838       0x20 ..\lib\libboot.a(boot_lib.o)
                0x00000838                boot_getChipSN
 .vector.kepttext
                0x00000858      0x210 ..\lib\libmcu.a(hx330x_gpio.o)
                0x00000858                exception_gpioDataSet
                0x000008e4                hx330x_gpioDataGet
                0x00000968                hx330x_gpioCommonDataGet
                0x000009b0                exception_io1d1_softstart_clr
 .vector.kepttext
                0x00000a68      0x304 ..\lib\libmcu.a(hx330x_rtc.o)
                0x00000a68                hx330x_rtcWriteByte
                0x00000ab8                hx330x_rtcReadByte
                0x00000b1c                hx330x_rtcDataRead
                0x00000ba4                hx330x_rtcDataWrite
                0x00000c2c                hx330x_WKI1WakeupEnable
 .vector.kepttext
                0x00000d6c      0x1f0 ..\lib\libmcu.a(hx330x_sys.o)
                0x00000d6c                hx330x_sysCpuMsDelay
                0x00000dc0                hx330x_sysCpuNopDelay
                0x00000dfc                hx330x_bytes_memcpy
                0x00000e6c                hx330x_bytes_memset
                0x00000ec4                hx330x_bytes_cmp
 .vector.kepttext
                0x00000f5c       0x74 ..\lib\libmcu.a(hx330x_uart.o)
                0x00000f5c                hx330x_uart0SendByte
 .vector.kepttext
                0x00000fd0       0x84 ..\lib\libmcu.a(hx330x_wdt.o)
                0x00000fd0                hx330x_wdtEnable
                0x00001004                hx330x_wdtClear
                0x00001038                hx330x_wdtReset
 *(.vector.keptdata)
 .vector.keptdata
                0x00001054      0x148 obj\Debug\app\user_config\src\user_config_tab.o
                0x00001054                hardware_setup
 .vector.keptdata
                0x0000119c       0x3c ..\lib\libboot.a(boot.o)
                0x0000119c                vbg_param
 .vector.keptdata
                0x000011d8      0x100 ..\lib\libboot.a(reset.o)
                0x000011d8                _step_data
 .vector.keptdata
                0x000012d8        0x4 ..\lib\libboot.a(boot_lib.o)
 .vector.keptdata
                0x000012dc        0x4 ..\lib\libmcu.a(hx330x_gpio.o)
 .vector.keptdata
                0x000012e0        0x4 ..\lib\libmcu.a(hx330x_spi0.o)
                0x000012e0                spi_auto_mode
                0x000012e4                . = ALIGN (0x4)
                0x000012e4                _boot_kept_vma = .
 *(.vector.text)
 .vector.text   0x000012e4      0x8f4 ..\lib\libboot.a(boot.o)
                0x00001554                boot_vbg_pre_init
                0x000015ec                boot_vbg_back_init
                0x00001634                exception_init
                0x0000166c                bool_pll_init
                0x00001ae4                boot_clktun_check
                0x00001b70                boot_clktun_save
 .vector.text   0x00001bd8      0xbbc ..\lib\libboot.a(boot_lib.o)
                0x00001dc0                boot_sdram_init
 *(.vector.data)
 .vector.data   0x00002794        0x4 ..\lib\libboot.a(boot.o)
                0x00002794                vbg_adc
 .vector.data   0x00002798      0x210 ..\lib\libboot.a(boot_lib.o)
                0x00002798                tune_values
                0x00002918                tune_by
                0x00002924                tune_tab_2_clk
                0x00002944                tune_tab_1_clk
                0x00002964                tuning_test_addr
                0x00002984                tuning_test_data
                0x000029a4                SDRTUN2_CON

.ram            0x00000000     0x36d0
                0x000012e4                . = _boot_kept_vma
 *fill*         0x00000000     0x12e4 
                0x000012e4                __sram_start = .
 *(.sram_usb11fifo)
 .sram_usb11fifo
                0x000012e4      0x96c obj\Debug\hal\src\hal_sys.o
                0x000012e4                usb11_fifo
 *(.sram_comm)
 .sram_comm     0x00001c50      0xd60 obj\Debug\dev\fs\src\fs_api.o
 .sram_comm     0x000029b0      0x200 obj\Debug\dev\sd\src\sd_api.o
                0x000029b0                sd0RamBuffer
 .sram_comm     0x00002bb0       0x40 obj\Debug\hal\src\hal_dmauart.o
                0x00002bb0                dmauart_fifo
 .sram_comm     0x00002bf0      0x400 obj\Debug\hal\src\hal_mjpDecode.o
 .sram_comm     0x00002ff0       0x60 obj\Debug\mcu\xos\xmsgq.o
 .sram_comm     0x00003050      0x400 ..\lib\libmcu.a(hx330x_jpg.o)
                0x00003050                jpg_dri_tab
 .sram_comm     0x00003450      0x280 ..\lib\libisp.a(hal_isp.o)
                0x000036d0                __sram_end = .

.usb_ram        0x00008000     0x3630
                0x00008000                __ufifo_start = .
 *(.uram_usb20fifo)
 .uram_usb20fifo
                0x00008000     0x1d30 obj\Debug\hal\src\hal_sys.o
                0x00008000                usb20_fifo
 *(.uram_comm)
 .uram_comm     0x00009d30     0x1900 obj\Debug\hal\src\hal_watermark.o
                0x00009d30                tminf_font
                0x0000b630                __ufifo_end = .

.line_ram       0x00200000        0x0
                0x00200000                __line_start = .
 *(.lram_comm)
                0x00200000                __line_end = .

.on_sdram       0x02000000     0x9c18 load address 0x00002c00
                0x02000000                _onsdram_start = .
 *(.sdram_text)
 .sdram_text    0x02000000       0x3c obj\Debug\dev\fs\src\ff.o
                0x02000000                clst2sect
 .sdram_text    0x0200003c       0x58 obj\Debug\dev\fs\src\fs_api.o
                0x0200003c                fs_getClustStartSector
 .sdram_text    0x02000094      0x174 obj\Debug\dev\nvfs\src\nvfs_jpg.o
                0x02000094                nv_jpg_write_by_linkmap
 .sdram_text    0x02000208      0x4f8 obj\Debug\dev\sd\src\sd_api.o
                0x020002a8                sd_api_Stop
                0x02000538                sd_api_Exist
                0x02000560                sd_api_Write
                0x02000640                sd_api_Read
 .sdram_text    0x02000700       0x3c obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .sdram_text    0x0200073c      0x260 obj\Debug\dev\usb\husb\src\husb_hub.o
 .sdram_text    0x0200099c     0x110c obj\Debug\dev\usb\husb\src\husb_uvc.o
                0x02001890                huvc_cache_dcd_down
                0x0200194c                husb_uvc_frame_read
 .sdram_text    0x02001aa8       0x3c obj\Debug\hal\src\hal_adc.o
                0x02001aa8                hal_adcGetChannel
 .sdram_text    0x02001ae4       0x48 obj\Debug\hal\src\hal_dac.o
 .sdram_text    0x02001b2c      0x104 obj\Debug\hal\src\hal_gpio.o
                0x02001b2c                hal_gpioInit
                0x02001bd4                hal_gpioEPullSet
 .sdram_text    0x02001c30      0x12c obj\Debug\hal\src\hal_lcdshow.o
                0x02001c30                hal_lcd_enc_kick
 .sdram_text    0x02001d5c      0x9f8 obj\Debug\hal\src\hal_spi.o
                0x02001d8c                hal_spiUpdata_led_show
                0x02001e44                hal_spiUpdata_led_show_init
                0x02001ec4                hal_spiUpdata_led_show_uinit
                0x02001f1c                hal_spiManualInit
                0x02001f48                hal_spiAutoModeInit
                0x02001f78                hal_spiModeSwitch
                0x02001ff4                hal_spiFlashReadID
                0x02002064                hal_spiFlashWriteEnable
                0x02002098                hal_spiFlashWait
                0x02002128                hal_spiFlashReadPage
                0x02002180                hal_spiFlashRead
                0x0200220c                hal_spiFlashWritePage
                0x020022a0                hal_spiFlashWrite
                0x02002380                hal_spiFlashWriteInManual
                0x02002414                hal_spiFlashEraseSector
                0x020024b8                hal_spiFlashEraseBlock
                0x02002538                hal_spiFlashEraseChip
                0x02002574                hal_spiFlashReadUniqueID
                0x020025fc                hal_spiFlashReadOTP
                0x020026a8                hal_spiFlashReadManual
 .sdram_text    0x02002754      0x460 obj\Debug\hal\src\hal_stream.o
                0x02002754                hal_streamMalloc
                0x0200286c                hal_streamIn
                0x0200294c                hal_streamOut
                0x02002a48                hal_streamOutNext
                0x02002b10                hal_streamfree
 .sdram_text    0x02002bb4       0x24 obj\Debug\hal\src\hal_timer.o
                0x02002bb4                hal_timerPWMStop
 .sdram_text    0x02002bd8      0x158 obj\Debug\hal\src\hal_uart.o
                0x02002bd8                uart_Printf
 .sdram_text    0x02002d30      0xc34 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02002d30                taskSdGetClst
                0x02002da4                taskSdReadBuf
                0x02002ee0                taskSdUpdateProcess
 .sdram_text    0x02003964      0x330 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                0x02003964                taskSdUpdateDrawStartAddrCal
                0x02003a3c                taskSdUpdateDrawAddrReCal
                0x02003ba8                taskSdUpdate_uiProgress
 .sdram_text    0x02003c94      0x11c ..\lib\libmcu.a(hx330x_adc.o)
                0x02003c94                hx330x_adcEnable
                0x02003cf0                hx330x_adcSetBaudrate
                0x02003d24                hx330x_adcConverStart
                0x02003d48                hx330x_adcRead
 .sdram_text    0x02003db0       0x90 ..\lib\libmcu.a(hx330x_auadc.o)
                0x02003db0                hx330x_auadcIRQHandler
 .sdram_text    0x02003e40      0x170 ..\lib\libmcu.a(hx330x_csi.o)
                0x02003e40                hx330x_csiIRQHandler
 .sdram_text    0x02003fb0      0x1e8 ..\lib\libmcu.a(hx330x_dac.o)
                0x02003fb0                hx330x_dacIRQHandler
                0x02004084                hx330x_dacBufferSet
                0x020040a4                hx330x_dacBufferFlush
                0x020040c4                hx330x_dacStart
                0x02004140                hx330x_check_dacstop
 .sdram_text    0x02004198       0xa4 ..\lib\libmcu.a(hx330x_dmauart.o)
                0x02004198                hx330x_uart1IRQHandler
 .sdram_text    0x0200423c      0x584 ..\lib\libmcu.a(hx330x_gpio.o)
                0x0200423c                hx330x_gpioDirSet
                0x020042e0                hx330x_gpioPullSet
                0x020043a0                hx330x_gpioPinPollSet
                0x020043ec                hx330x_gpioDrvSet
                0x02004478                hx330x_gpioDataSet
                0x02004504                hx330x_gpioPinDataSet
                0x02004550                hx330x_gpioMapSet
                0x020045dc                hx330x_gpioDigitalSet
                0x02004668                hx330x_gpioLedPull
                0x02004728                hx330x_gpioIRQHandler
 .sdram_text    0x020047c0      0x230 ..\lib\libmcu.a(hx330x_int.o)
                0x020047c0                hx330x_intHandler
                0x020047d4                fast_isr
                0x020048ac                slow_isr
                0x020049a8                hx330x_intEnable
 .sdram_text    0x020049f0      0x1a4 ..\lib\libmcu.a(hx330x_jpg.o)
                0x020049f0                hx330x_mjpA_EncodeLoadAddrGet
                0x02004a0c                hx330x_mjpA_EncodeStartAddrGet
                0x02004a28                hx330x_mjpA_EncodeDriTabGet
                0x02004a44                hx330x_mjpA_IRQHandler
                0x02004a90                hx330x_mjpB_IRQHandler
 .sdram_text    0x02004b94      0x17c ..\lib\libmcu.a(hx330x_lcd.o)
                0x02004b94                hx330x_lcdKick
                0x02004bb8                hx330x_lcdIRQHandler
 .sdram_text    0x02004d10       0x50 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                0x02004d10                hx330x_rotateIRQHandler
 .sdram_text    0x02004d60      0x470 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x02004d60                hx330x_lcdShowWaitDone
                0x02004dac                hx330x_lcdShowKick
                0x02004dd0                hx330x_lcdShowIRQHandler
                0x02004e30                hx330x_lcdVideoSetScanMode
                0x02004ec4                hx330x_lcdVideoSetAddr
                0x02004ee0                hx330x_lcdVideoSetStride
                0x02004f04                hx330x_lcdvideoSetPosition
                0x02004f28                hx330x_lcdUiEnable
                0x02004f7c                hx330x_lcdUiSetAddr
                0x02005030                hx330x_lcdVideoUpScaler_cfg
                0x02005128                hx330x_lcdVideoUpScalerSoftRotate_cfg
 .sdram_text    0x020051d0       0x4c ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                0x020051d0                hx330x_uiLzoIRQHandler
 .sdram_text    0x0200521c       0x60 ..\lib\libmcu.a(hx330x_misc.o)
                0x0200521c                hx330x_min
                0x02005240                hx330x_data_check
 .sdram_text    0x0200527c      0x224 ..\lib\libmcu.a(hx330x_rtc.o)
                0x0200527c                hx330x_rtcIRQHandler
                0x02005348                hx330x_WKOEnable
                0x020053c4                hx330x_WKI1WakeupTriger
                0x02005414                hx330x_WKI0WakeupTriger
                0x02005464                hx330x_WakeUpCleanPending
 .sdram_text    0x020054a0      0x554 ..\lib\libmcu.a(hx330x_sd.o)
                0x020054a0                hx330x_sd0SendCmd
                0x0200556c                hx330x_sd0Recv
                0x020055c8                hx330x_sd0Send
                0x02005600                hx330x_sd0WaitDAT0
                0x020056a0                hx330x_sd0WaitPend
                0x02005728                hx330x_sd0GetRsp
                0x02005744                hx330x_sd0CRCCheck
                0x020057a8                hx330x_sd1SendCmd
                0x02005874                hx330x_sd1Recv
                0x020058d0                hx330x_sd1Send
                0x02005908                hx330x_sd1WaitPend
                0x02005990                hx330x_sd1CRCCheck
 .sdram_text    0x020059f4      0x678 ..\lib\libmcu.a(hx330x_spi0.o)
                0x020059f4                hx330x_spi0PinConfig
                0x02005a74                hx330x_spi0ManualInit
                0x02005ba0                hx330x_spi0SendByte
                0x02005bf4                hx330x_spi0RecvByte
                0x02005c48                hx330x_spi0Send
                0x02005d00                hx330x_spi0Recv
                0x02005dc4                hx330x_spi0CS0Config
                0x02005e08                hx330x_spi0AutoModeInit
                0x02005ff4                hx330x_spi0ExitAutoMode
 .sdram_text    0x0200606c       0x64 ..\lib\libmcu.a(hx330x_spi1.o)
                0x0200606c                hx330x_spi1DMAIRQHandler
 .sdram_text    0x020060d0      0x63c ..\lib\libmcu.a(hx330x_sys.o)
                0x020060d0                table_init_sfr
                0x02006150                hx330x_sysDcacheWback
                0x020061ec                hx330x_sysDcacheFlush
                0x02006288                hx330x_sysClkSet
                0x020062e4                hx330x_mcpy0_sdram2gram
                0x020063c0                hx330x_mcpy0_sdram2gram_nocache
                0x02006468                hx330x_mcpy1_sdram2gram_nocache_waitdone
                0x020064dc                hx330x_mcpy1_sdram2gram_nocache_kick
                0x0200658c                hx330x_mcpy1_sdram2gram
                0x02006658                hx330x_mcpy1_sdram2gram_nocache
 .sdram_text    0x0200670c      0x1fc ..\lib\libmcu.a(hx330x_timer.o)
                0x0200670c                hx330x_timer0IRQHandler
                0x02006750                hx330x_timer1IRQHandler
                0x02006794                hx330x_timer2IRQHandler
                0x020067d8                hx330x_timer3IRQHandler
                0x0200681c                hx330x_timerTickStart
                0x02006850                hx330x_timerTickStop
                0x0200686c                hx330x_timerTickCount
                0x02006888                hx330x_timerPWMStop
 .sdram_text    0x02006908       0x64 ..\lib\libmcu.a(hx330x_uart.o)
                0x02006908                hx330x_uart0IRQHandler
 .sdram_text    0x0200696c      0x76c ..\lib\libmcu.a(hx330x_usb.o)
                0x020069f4                hx330x_usb20_Func_Call
                0x02006a20                hx330x_bulk20_tx
                0x02006be4                hx330x_bulk20_rx
                0x02006d64                hx330x_usb20DevIRQHanlder
                0x02006e9c                hx330x_usb20_hostIRQHanlder
                0x02006fa4                hx330x_usb11_Func_Call
                0x02006fd0                hx330x_usb11_hostIRQHanlder
 .sdram_text    0x020070d8       0x5c ..\lib\libmcu.a(hx330x_emi.o)
                0x020070d8                hx330x_emiIRQHandler
 .sdram_text    0x02007134       0x68 ..\lib\libjpg.a(hal_jpg.o)
                0x02007134                hal_mjp_enle_tab_get
 .sdram_text    0x0200719c      0x358 ..\lib\liblcd.a(hal_lcd.o)
 .sdram_text    0x020074f4      0x228 ..\lib\liblcd.a(hal_lcdMem.o)
                0x020074f4                hal_lcdAddrCalculate
                0x020076b8                hal_dispframeFree
 .sdram_text    0x0200771c      0x18c ..\lib\liblcd.a(hal_lcdUi.o)
                0x0200771c                hal_uiBuffFree
                0x02007744                hal_lcdUiEnable
                0x0200779c                hal_lcdUiBuffFlush
 *(.sdram_code)
 .sdram_code    0x020078a8      0x110 ..\lib\libmcu.a(hx330x_spi0.o)
                0x020078a8                spi0PinCfg_tab
                0x020078b8                SPI0_4_LINE_tab
                0x020078f8                SPI0_2_LINE1_tab
                0x02007938                SPI0_2_LINE0_tab
                0x02007978                SPI0_1_LINE_tab
 *(.sdram_data)
 *(.data*)
 .data          0x020079b8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .data          0x020079b8      0x120 obj\Debug\dev\dev_api.o
                0x020079b8                dev_node
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\diskio.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\ff.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\ffunicode.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\fs_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .data          0x02007ad8        0x0 obj\Debug\dev\ir\src\ir_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\key\src\key_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .data          0x02007ad8        0x0 obj\Debug\dev\led\src\led_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\nvfs\src\nvfs_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sd\src\sd_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .data          0x02007ad8        0x4 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .data          0x02007adc        0x0 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .data          0x02007adc      0x208 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x02007adc                dusb_com_cfgdsc
                0x02007cc4                dusb_msc_cfgdsc
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_api.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_enum.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_hub.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_adc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_auadc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_csi.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_dac.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_dmauart.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_eeprom.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_gpio.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_iic.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_int.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_lcdshow.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_md.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpAEncode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpBEncode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpDecode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_rtc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_spi.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_spi1.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_stream.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_sys.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_timer.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_uart.o
 .data          0x02007ce4       0x48 obj\Debug\hal\src\hal_watermark.o
 .data          0x02007d2c        0x0 obj\Debug\hal\src\hal_wdt.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xmbox.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xmsgq.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xos.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xwork.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\audio\audio_playback.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\audio\audio_record.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\image\image_decode.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\image\image_encode.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\video\video_playback.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\video\video_record.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .data          0x02007d2c        0x0 obj\Debug\app\app_common\src\app_init.o
 .data          0x02007d2c        0x8 obj\Debug\app\app_common\src\app_lcdshow.o
 .data          0x02007d34        0x0 obj\Debug\app\app_common\src\main.o
 .data          0x02007d34      0xb7c obj\Debug\app\resource\user_res.o
                0x02007d34                User_Icon_Table
                0x02008154                User_String_Table
 .data          0x020088b0        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .data          0x020088b0       0x48 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
                0x020088b0                menuplayBack
                0x020088bc                menuPageplayBack
                0x020088d0                menuItemplayBack
 .data          0x020088f8        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .data          0x020088f8      0x2a0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                0x020088f8                menurecord
                0x02008904                menuPagerecord
                0x02008918                menuItemrecord
                0x020089b8                menuOptionversion
                0x020089c0                menuOptionscreenSave
                0x020089e0                menuOptionirLed
                0x020089f8                menuOptionfrequency
                0x02008a08                menuOptionlanguage
                0x02008a78                menuOptionautoPowerOff
                0x02008a90                menuOptionkeySound
                0x02008aa0                menuOptiongsensor
                0x02008ac0                menuOptiontimeStamp
                0x02008ad0                menuOptionparking
                0x02008ae0                menuOptionaudio
                0x02008af0                menuOptionmd
                0x02008b00                menuOptionev
                0x02008b28                menuOptionawb
                0x02008b50                menuOptionloopRecord
                0x02008b70                menuOptionphotoResolution
                0x02008b88                menuOptionvideoResolution
 .data          0x02008b98       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                0x02008b98                dateTimeWindow
                0x02008ba8                dateTimeMsgDeal
 .data          0x02008c08       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                0x02008c08                defaultWindow
                0x02008c18                defaultMsgDeal
 .data          0x02008c70       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                0x02008c70                delAllWindow
                0x02008c80                delAllMsgDeal
 .data          0x02008cd8       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                0x02008cd8                delCurWindow
                0x02008ce8                delCurMsgDeal
 .data          0x02008d40       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                0x02008d40                formatWindow
                0x02008d50                formatMsgDeal
 .data          0x02008da8       0x98 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x02008db0                menuItemWindow
                0x02008dc0                menuItemMsgDeal
 .data          0x02008e40       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                0x02008e40                lockCurWindow
                0x02008e50                lockCurMsgDeal
 .data          0x02008ea8       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                0x02008ea8                menuOptionWindow
                0x02008eb8                menuOptionMsgDeal
 .data          0x02008f18       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                0x02008f18                unlockAllWindow
                0x02008f28                unlockAllMsgDeal
 .data          0x02008f80       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                0x02008f80                unlockCurWindow
                0x02008f90                unlockCurMsgDeal
 .data          0x02008fe8        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .data          0x02008fe8       0x38 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                0x02008fe8                asternWindow
                0x02008ff8                asternMsgDeal
 .data          0x02009020       0x68 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                0x02009020                noFileWindow
                0x02009030                noFileMsgDeal
 .data          0x02009088       0x98 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                0x02009088                selfTestWindow
                0x02009098                selfTestMsgDeal
 .data          0x02009120       0xcc obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                0x02009120                tips1Window
                0x02009130                tips1MsgDeal
 .data          0x020091ec       0xcc obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                0x020091ec                tipsWindow
                0x020091fc                tipsMsgDeal
 .data          0x020092b8       0x5c obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                0x020092b8                takephotoiconWindow
                0x020092c8                TpIconMsgDeal
 .data          0x02009314        0x0 obj\Debug\app\task_windows\msg_api.o
 .data          0x02009314        0x0 obj\Debug\app\task_windows\task_api.o
 .data          0x02009314        0x0 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .data          0x02009314       0x4c obj\Debug\app\task_windows\task_common\src\task_common.o
 .data          0x02009360        0x0 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .data          0x02009360       0x14 obj\Debug\app\task_windows\task_main\src\taskMain.o
                0x02009360                taskMain
 .data          0x02009374       0x60 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
                0x02009374                mainWindow
                0x02009384                mainMsgDeal
 .data          0x020093d4       0x14 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x020093d4                taskPlayAudio
 .data          0x020093e8       0x80 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                0x020093e8                playAudioWindow
                0x020093f8                playAudioMsgDeal
 .data          0x02009468       0x14 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x02009468                taskPlayVideo
 .data          0x0200947c       0xf8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                0x020094a4                playVideoMainWindow
                0x020094b4                playVideoMainMsgDeal
 .data          0x02009574       0x9c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                0x02009580                playVideoSlideWindow
                0x02009590                playVideoSlideMsgDeal
 .data          0x02009610       0x88 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                0x02009610                playVideoThumbnallWindow
                0x02009620                playVideoThumbnallMsgDeal
 .data          0x02009698       0x14 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
                0x02009698                taskPowerOff
 .data          0x020096ac       0x14 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
                0x020096ac                taskRecordAudio
 .data          0x020096c0       0x50 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                0x020096c0                RecordAudioWindow
                0x020096d0                recordAudioMsgDeal
 .data          0x02009710       0x14 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x02009710                taskRecordPhoto
 .data          0x02009724      0x268 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                0x02009724                recordPhotoWindow
                0x02009734                photoEncodeMsgDeal
                0x020097fc                recordPhotoWin
 .data          0x0200998c       0x14 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                0x0200998c                taskRecordVideo
 .data          0x020099a0       0xf8 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                0x020099a0                recordVideoWindow
                0x020099b0                recordVideoMsgDeal
 .data          0x02009a98       0x14 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02009a98                taskSDUpdate
 .data          0x02009aac        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .data          0x02009aac       0x14 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
                0x02009aac                taskShowLogo
 .data          0x02009ac0       0x30 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                0x02009ac0                ShowLogoWindow
                0x02009ad0                ShowLogoMsgDeal
 .data          0x02009af0       0x14 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                0x02009af0                taskUSBDevice
 .data          0x02009b04       0xb8 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                0x02009b04                usbDeviceWindow
                0x02009b14                usbDeviceMsgDeal
 .data          0x02009bbc        0x0 obj\Debug\app\task_windows\windows_api.o
 .data          0x02009bbc        0x8 obj\Debug\app\user_config\src\mbedtls_md5.o
                0x02009bbc                MY_KEY
 .data          0x02009bc4        0x0 obj\Debug\app\user_config\src\user_config_api.o
 .data          0x02009bc4        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .data          0x02009bc4        0x0 ..\lib\libboot.a(boot.o)
 .data          0x02009bc4        0x0 ..\lib\libboot.a(boot_loader.o)
 .data          0x02009bc4        0x0 ..\lib\libboot.a(reset.o)
 .data          0x02009bc4        0x0 ..\lib\libboot.a(boot_lib.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_auadc.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_csi.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_dac.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_dma.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_dmauart.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_gpio.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_iic.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_int.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_isp.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .data          0x02009bc4        0x0 ..\lib\libmcu.a(hx330x_jpg.o)
 .data          0x02009bc4        0x1 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 *fill*         0x02009bc5        0x3 
 .data          0x02009bc8        0x4 ..\lib\libmcu.a(hx330x_lcd.o)
 .data          0x02009bcc        0x0 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .data          0x02009bcc        0x8 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x02009bcc                video_scanmode_tab
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_md.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_mipi.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_misc.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_rtc.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_sd.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_spi1.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_sys.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_timer.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_tminf.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_uart.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_usb.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_wdt.o)
 .data          0x02009bd4        0x0 ..\lib\libmcu.a(hx330x_emi.o)
 .data          0x02009bd4        0x4 ..\lib\libisp.a(hal_isp.o)
 .data          0x02009bd8        0x0 ..\lib\libjpg.a(hal_jpg.o)
 .data          0x02009bd8       0x40 ..\lib\liblcd.a(hal_lcd.o)
 .data          0x02009c18        0x0 ..\lib\liblcd.a(hal_lcdMem.o)
 .data          0x02009c18        0x0 ..\lib\liblcd.a(hal_lcdrotate.o)
 .data          0x02009c18        0x0 ..\lib\liblcd.a(hal_lcdUi.o)
 .data          0x02009c18        0x0 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .data          0x02009c18        0x0 ..\lib\liblcd.a(lcd_tab.o)
 .data          0x02009c18        0x0 ..\lib\libmultimedia.a(api_multimedia.o)
 .data          0x02009c18        0x0 ..\lib\libmultimedia.a(avi_dec.o)
 .data          0x02009c18        0x0 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .data          0x02009c18        0x0 ..\lib\libmultimedia.a(avi_std_enc.o)
 .data          0x02009c18        0x0 ..\lib\libmultimedia.a(wav_dec.o)
 .data          0x02009c18        0x0 ..\lib\libmultimedia.a(wav_enc.o)
 .data          0x02009c18        0x0 ..\lib\libmultimedia.a(wav_pcm.o)
 .data          0x02009c18        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .data          0x02009c18        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .data          0x02009c18        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .data          0x02009c18        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .data          0x02009c18        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .data          0x02009c18        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .data          0x02009c18        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
 .data          0x02009c18        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
 .data          0x02009c18        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.bss            0x02009c18    0x12b6c load address 0x0000c818
                0x02009c18                __bss_start = .
 *(.bss*)
 .bss           0x02009c18        0x8 obj\Debug\dev\battery\src\battery_api.o
 .bss           0x02009c20        0x8 obj\Debug\dev\dev_api.o
 .bss           0x02009c28        0x0 obj\Debug\dev\fs\src\diskio.o
 .bss           0x02009c28      0x468 obj\Debug\dev\fs\src\ff.o
 .bss           0x0200a090        0x0 obj\Debug\dev\fs\src\ffunicode.o
 .bss           0x0200a090        0x0 obj\Debug\dev\fs\src\fs_api.o
 .bss           0x0200a090        0x4 obj\Debug\dev\gsensor\src\gsensor_api.o
 .bss           0x0200a094        0x0 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .bss           0x0200a094        0x0 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .bss           0x0200a094        0x0 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .bss           0x0200a094        0x4 obj\Debug\dev\ir\src\ir_api.o
 .bss           0x0200a098       0x1c obj\Debug\dev\key\src\key_api.o
 .bss           0x0200a0b4       0xbc obj\Debug\dev\lcd\src\lcd_api.o
                0x0200a0b4                lcd_saj_nocolor
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .bss           0x0200a170        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .bss           0x0200a170        0x4 obj\Debug\dev\led\src\led_api.o
 .bss           0x0200a174        0x4 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .bss           0x0200a178       0x18 obj\Debug\dev\nvfs\src\nvfs_api.o
 .bss           0x0200a190       0x10 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .bss           0x0200a1a0       0x2c obj\Debug\dev\sd\src\sd_api.o
                0x0200a1a0                hal_sdc_speed
 .bss           0x0200a1cc     0x1218 obj\Debug\dev\sensor\src\sensor_api.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .bss           0x0200b3e4        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .bss           0x0200b3e4        0x8 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .bss           0x0200b3ec        0x0 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .bss           0x0200b3ec        0x4 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .bss           0x0200b3f0       0x1c obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .bss           0x0200b40c        0x4 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .bss           0x0200b410        0x0 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .bss           0x0200b410        0x0 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .bss           0x0200b410        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .bss           0x0200b410        0x0 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .bss           0x0200b410        0x2 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 *fill*         0x0200b412        0x2 
 .bss           0x0200b414        0xc obj\Debug\dev\usb\husb\src\husb_api.o
 .bss           0x0200b420        0x0 obj\Debug\dev\usb\husb\src\husb_enum.o
 .bss           0x0200b420        0x0 obj\Debug\dev\usb\husb\src\husb_hub.o
 .bss           0x0200b420        0x0 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .bss           0x0200b420        0x4 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .bss           0x0200b424        0x0 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .bss           0x0200b424        0x0 obj\Debug\hal\src\hal_adc.o
 .bss           0x0200b424       0xc8 obj\Debug\hal\src\hal_auadc.o
                0x0200b424                auadccnt
 .bss           0x0200b4ec        0x4 obj\Debug\hal\src\hal_csi.o
 .bss           0x0200b4f0        0x8 obj\Debug\hal\src\hal_dac.o
 .bss           0x0200b4f8       0x50 obj\Debug\hal\src\hal_dmauart.o
 .bss           0x0200b548        0x0 obj\Debug\hal\src\hal_eeprom.o
 .bss           0x0200b548        0x0 obj\Debug\hal\src\hal_gpio.o
 .bss           0x0200b548        0x1 obj\Debug\hal\src\hal_iic.o
 .bss           0x0200b549        0x0 obj\Debug\hal\src\hal_int.o
 *fill*         0x0200b549        0x3 
 .bss           0x0200b54c        0x8 obj\Debug\hal\src\hal_lcdshow.o
 .bss           0x0200b554        0x4 obj\Debug\hal\src\hal_md.o
 .bss           0x0200b558      0x3b4 obj\Debug\hal\src\hal_mjpAEncode.o
 .bss           0x0200b90c      0x3a0 obj\Debug\hal\src\hal_mjpBEncode.o
 .bss           0x0200bcac       0x74 obj\Debug\hal\src\hal_mjpDecode.o
 .bss           0x0200bd20       0x34 obj\Debug\hal\src\hal_rtc.o
 .bss           0x0200bd54        0x8 obj\Debug\hal\src\hal_spi.o
                0x0200bd54                spi_updata_led
 .bss           0x0200bd5c       0x10 obj\Debug\hal\src\hal_spi1.o
 .bss           0x0200bd6c        0x0 obj\Debug\hal\src\hal_stream.o
 .bss           0x0200bd6c      0x50c obj\Debug\hal\src\hal_sys.o
 .bss           0x0200c278        0x0 obj\Debug\hal\src\hal_timer.o
 .bss           0x0200c278        0x8 obj\Debug\hal\src\hal_uart.o
 .bss           0x0200c280        0x8 obj\Debug\hal\src\hal_watermark.o
 .bss           0x0200c288        0x0 obj\Debug\hal\src\hal_wdt.o
 .bss           0x0200c288        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .bss           0x0200c288        0x0 obj\Debug\mcu\xos\xmbox.o
 .bss           0x0200c288        0x0 obj\Debug\mcu\xos\xmsgq.o
 .bss           0x0200c288        0x8 obj\Debug\mcu\xos\xos.o
 .bss           0x0200c290       0x80 obj\Debug\mcu\xos\xwork.o
 .bss           0x0200c310       0x60 obj\Debug\multimedia\audio\audio_playback.o
 .bss           0x0200c370       0x48 obj\Debug\multimedia\audio\audio_record.o
 .bss           0x0200c3b8        0x0 obj\Debug\multimedia\image\image_decode.o
 .bss           0x0200c3b8        0x0 obj\Debug\multimedia\image\image_encode.o
 .bss           0x0200c3b8        0x0 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .bss           0x0200c3b8      0x1c8 obj\Debug\multimedia\video\video_playback.o
 .bss           0x0200c580        0x0 obj\Debug\multimedia\video\video_record.o
 .bss           0x0200c580        0xc obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .bss           0x0200c58c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .bss           0x0200c58c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .bss           0x0200c58c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .bss           0x0200c58c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .bss           0x0200c58c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .bss           0x0200c58c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .bss           0x0200c58c      0x530 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .bss           0x0200cabc       0x94 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .bss           0x0200cb50        0x0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .bss           0x0200cb50        0x0 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .bss           0x0200cb50       0x60 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .bss           0x0200cbb0        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .bss           0x0200cbb0     0x5068 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .bss           0x02011c18        0x4 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .bss           0x02011c1c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .bss           0x02011c1c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .bss           0x02011c1c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .bss           0x02011c1c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .bss           0x02011c1c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .bss           0x02011c1c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .bss           0x02011c1c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .bss           0x02011c1c       0x50 obj\Debug\app\app_common\src\app_init.o
 .bss           0x02011c6c       0x10 obj\Debug\app\app_common\src\app_lcdshow.o
 .bss           0x02011c7c        0x0 obj\Debug\app\app_common\src\main.o
 .bss           0x02011c7c        0x0 obj\Debug\app\resource\user_res.o
 .bss           0x02011c7c        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .bss           0x02011c7c        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .bss           0x02011c7c        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .bss           0x02011c7c        0x8 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                0x02011c7c                menuOptionmemory
 .bss           0x02011c84       0x48 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .bss           0x02011ccc        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .bss           0x02011ccc        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .bss           0x02011ccc        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .bss           0x02011ccc        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .bss           0x02011ccc        0x8 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
 .bss           0x02011cd4        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .bss           0x02011cd4        0x4 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .bss           0x02011cd8        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .bss           0x02011cd8        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .bss           0x02011cd8        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .bss           0x02011cd8        0x0 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .bss           0x02011cd8        0x0 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .bss           0x02011cd8        0xc obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .bss           0x02011ce4        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .bss           0x02011ce8        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .bss           0x02011cec        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .bss           0x02011cf0      0x138 obj\Debug\app\task_windows\msg_api.o
 .bss           0x02011e28       0x40 obj\Debug\app\task_windows\task_api.o
 .bss           0x02011e68        0x1 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 *fill*         0x02011e69        0x3 
 .bss           0x02011e6c       0x3c obj\Debug\app\task_windows\task_common\src\task_common.o
 .bss           0x02011ea8        0x0 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .bss           0x02011ea8        0x0 obj\Debug\app\task_windows\task_main\src\taskMain.o
 .bss           0x02011ea8        0x0 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
 .bss           0x02011ea8        0x0 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .bss           0x02011ea8        0x4 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .bss           0x02011eac        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .bss           0x02011eac        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .bss           0x02011eac        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .bss           0x02011eac        0xc obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .bss           0x02011eb8        0x0 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .bss           0x02011eb8        0x0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .bss           0x02011eb8        0x0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .bss           0x02011eb8        0x0 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .bss           0x02011eb8        0x8 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .bss           0x02011ec0        0x0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
 .bss           0x02011ec0        0x8 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .bss           0x02011ec8        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .bss           0x02011ec8        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .bss           0x02011ec8        0x0 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .bss           0x02011ec8        0x1 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .bss           0x02011ec9        0x0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .bss           0x02011ec9        0x0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .bss           0x02011ec9        0x0 obj\Debug\app\task_windows\windows_api.o
 .bss           0x02011ec9        0x0 obj\Debug\app\user_config\src\mbedtls_md5.o
 *fill*         0x02011ec9        0x3 
 .bss           0x02011ecc      0x20c obj\Debug\app\user_config\src\user_config_api.o
 .bss           0x020120d8        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .bss           0x020120d8        0x0 ..\lib\libboot.a(boot.o)
 .bss           0x020120d8        0x0 ..\lib\libboot.a(boot_loader.o)
 .bss           0x020120d8        0x0 ..\lib\libboot.a(reset.o)
 .bss           0x020120d8        0x0 ..\lib\libboot.a(boot_lib.o)
 .bss           0x020120d8        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .bss           0x020120d8        0x8 ..\lib\libmcu.a(hx330x_auadc.o)
 .bss           0x020120e0       0x3c ..\lib\libmcu.a(hx330x_csi.o)
 .bss           0x0201211c        0x4 ..\lib\libmcu.a(hx330x_dac.o)
 .bss           0x02012120        0x0 ..\lib\libmcu.a(hx330x_dma.o)
 .bss           0x02012120        0x4 ..\lib\libmcu.a(hx330x_dmauart.o)
 .bss           0x02012124       0x6c ..\lib\libmcu.a(hx330x_gpio.o)
 .bss           0x02012190        0x0 ..\lib\libmcu.a(hx330x_iic.o)
 .bss           0x02012190        0x0 ..\lib\libmcu.a(hx330x_int.o)
 .bss           0x02012190        0xc ..\lib\libmcu.a(hx330x_isp.o)
                0x02012190                isp_ccf_dn_tab
                0x02012194                isp_ee_dn_tab
                0x02012198                isp_ee_sharp_tab
 .bss           0x0201219c        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .bss           0x0201219c       0x54 ..\lib\libmcu.a(hx330x_jpg.o)
                0x0201219c                mjpBEncAvgSize
                0x020121a0                mjpAEncAvgSize
 .bss           0x020121f0        0x0 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 .bss           0x020121f0        0x0 ..\lib\libmcu.a(hx330x_lcd.o)
 .bss           0x020121f0        0x4 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .bss           0x020121f4        0x8 ..\lib\libmcu.a(hx330x_lcdui.o)
 .bss           0x020121fc        0x4 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .bss           0x02012200        0x0 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .bss           0x02012200        0x0 ..\lib\libmcu.a(hx330x_md.o)
 .bss           0x02012200        0x0 ..\lib\libmcu.a(hx330x_mipi.o)
 .bss           0x02012200        0x0 ..\lib\libmcu.a(hx330x_misc.o)
 .bss           0x02012200        0x4 ..\lib\libmcu.a(hx330x_rtc.o)
                0x02012200                rtcAlarmFlag
 .bss           0x02012204       0x10 ..\lib\libmcu.a(hx330x_sd.o)
 .bss           0x02012214        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .bss           0x02012214        0x4 ..\lib\libmcu.a(hx330x_spi1.o)
 .bss           0x02012218        0x4 ..\lib\libmcu.a(hx330x_sys.o)
                0x02012218                mcp1_lock
 .bss           0x0201221c        0x0 ..\lib\libmcu.a(hx330x_timer.o)
 .bss           0x0201221c       0x28 ..\lib\libmcu.a(hx330x_tminf.o)
 .bss           0x02012244        0x4 ..\lib\libmcu.a(hx330x_uart.o)
 .bss           0x02012248       0x7c ..\lib\libmcu.a(hx330x_usb.o)
 .bss           0x020122c4        0x0 ..\lib\libmcu.a(hx330x_wdt.o)
 .bss           0x020122c4        0x4 ..\lib\libmcu.a(hx330x_emi.o)
 .bss           0x020122c8       0xe0 ..\lib\libisp.a(hal_isp.o)
 .bss           0x020123a8       0x4c ..\lib\libjpg.a(hal_jpg.o)
 .bss           0x020123f4       0x16 ..\lib\liblcd.a(hal_lcd.o)
 .bss           0x0201240a        0x0 ..\lib\liblcd.a(hal_lcdMem.o)
 *fill*         0x0201240a        0x2 
 .bss           0x0201240c       0x28 ..\lib\liblcd.a(hal_lcdrotate.o)
 .bss           0x02012434       0x28 ..\lib\liblcd.a(hal_lcdUi.o)
 .bss           0x0201245c        0x8 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .bss           0x02012464        0x0 ..\lib\liblcd.a(lcd_tab.o)
 .bss           0x02012464       0x60 ..\lib\libmultimedia.a(api_multimedia.o)
 .bss           0x020124c4        0x0 ..\lib\libmultimedia.a(avi_dec.o)
 .bss           0x020124c4        0x0 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .bss           0x020124c4        0x0 ..\lib\libmultimedia.a(avi_std_enc.o)
 .bss           0x020124c4        0x0 ..\lib\libmultimedia.a(wav_dec.o)
 .bss           0x020124c4        0x0 ..\lib\libmultimedia.a(wav_enc.o)
 .bss           0x020124c4        0x0 ..\lib\libmultimedia.a(wav_pcm.o)
 .bss           0x020124c4        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .bss           0x020124c4        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .bss           0x020124c4        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .bss           0x020124c4        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .bss           0x020124c4        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .bss           0x020124c4        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .bss           0x020124c4        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
 .bss           0x020124c4        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
 .bss           0x020124c4        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
 *(COMMON)
 COMMON         0x020124c4       0x1c obj\Debug\dev\battery\src\battery_api.o
                0x020124c4                WAV_TYPE_E
                0x020124c8                USB_CH
                0x020124cc                UVC_CACHE_STA
                0x020124d0                SDCON0_T
                0x020124d4                SDCON1_T
                0x020124d8                UVC_FSTACK_STA
                0x020124dc                CHANNEL_EXCHANGE_E
 COMMON         0x020124e0       0x14 obj\Debug\dev\fs\src\fs_api.o
                0x020124e0                fs_exfunc
 COMMON         0x020124f4        0x8 obj\Debug\dev\gsensor\src\gsensor_api.o
                0x020124f4                gsensor_ctl
 COMMON         0x020124fc       0x78 obj\Debug\dev\key\src\key_api.o
                0x020124fc                dev_key_tab
 COMMON         0x02012574       0x20 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                0x02012574                tp_api_t
 COMMON         0x02012594      0x29c obj\Debug\dev\usb\dusb\src\dusb_api.o
                0x02012594                usb_dev_ctl
 COMMON         0x02012830     0x1484 obj\Debug\dev\usb\husb\src\husb_api.o
                0x02012830                husb_ctl
 COMMON         0x02013cb4        0x4 obj\Debug\dev\usb\husb\src\husb_hub.o
                0x02013cb4                uhub_handle
 COMMON         0x02013cb8        0x4 obj\Debug\dev\usb\husb\src\husb_usensor.o
                0x02013cb8                usensor_handle
 COMMON         0x02013cbc        0x4 obj\Debug\mcu\xos\xos.o
                0x02013cbc                XOSNesting
 COMMON         0x02013cc0       0x74 obj\Debug\multimedia\video\video_record.o
                0x02013cc0                mediaVideoCtl
 COMMON         0x02013d34       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                0x02013d34                ui_draw_ctrl
 COMMON         0x02013d44      0x130 obj\Debug\app\app_common\src\app_init.o
                0x02013d44                SysCtrl
 COMMON         0x02013e74        0x1 obj\Debug\app\app_common\src\main.o
                0x02013e74                flash_success
 *fill*         0x02013e75        0x3 
 COMMON         0x02013e78       0x48 obj\Debug\app\task_windows\task_main\src\taskMain.o
                0x02013e78                mainTaskOp
 COMMON         0x02013ec0        0x4 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x02013ec0                task_play_audio_stat
 COMMON         0x02013ec4       0x44 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x02013ec4                playVideoOp
 COMMON         0x02013f08       0x20 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x02013f08                recordPhotoOp
 COMMON         0x02013f28       0x64 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02013f28                sd_update_op
 COMMON         0x02013f8c        0xc obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                0x02013f8c                usbDeviceOp
 COMMON         0x02013f98      0x500 ..\lib\libmcu.a(hx330x_isp.o)
                0x02013f98                RGB_GMMA_Tab
                0x02014298                Y_GMA_Tab
 COMMON         0x02014498       0x14 ..\lib\libmcu.a(hx330x_lcd.o)
                0x02014498                hx330x_lcdISR
 COMMON         0x020144ac       0x14 ..\lib\libmcu.a(hx330x_rtc.o)
                0x020144ac                rc128k_div
                0x020144b0                rtcSecondISR
                0x020144b4                rtcAlamISR
                0x020144b8                rc128k_rtc_cnt
                0x020144bc                rc128k_timer_cnt
 COMMON         0x020144c0        0x4 ..\lib\libmcu.a(hx330x_sys.o)
                0x020144c0                smph_dmacopy
 COMMON         0x020144c4       0x10 ..\lib\libmcu.a(hx330x_timer.o)
                0x020144c4                hx330x_timerISR
 COMMON         0x020144d4       0x84 ..\lib\liblcd.a(hal_lcd.o)
                0x020144d4                lcd_show_ctrl
 COMMON         0x02014558      0x1e8 ..\lib\liblcd.a(hal_lcdMem.o)
                0x02014558                lcdshow_frame_op
 *(.big_buffer*)
 *(._sdram_buf_)
 ._sdram_buf_   0x02014740     0x8044 obj\Debug\dev\fs\src\fs_api.o
                0x02014740                work_fatfs
                0x0201c7c0                _sdram_remian_addr = (ALIGN (0x40) + 0x0)
                0x0000ca00                _text_lma = (((LOADADDR (.on_sdram) + SIZEOF (.on_sdram)) + 0x1ff) & 0xfffffe00)
                0x0600ca00                _text_vma = (ORIGIN (flash) + _text_lma)

.text           0x0600ca00    0x8a870 load address 0x0000ca00
 *(.text*)
 .text          0x0600ca00      0x244 obj\Debug\dev\battery\src\battery_api.o
                0x0600ca00                dev_battery_ioctrl
                0x0600cb84                dev_battery_init
 .text          0x0600cc44      0x254 obj\Debug\dev\dev_api.o
                0x0600cc44                dev_api_node_init
                0x0600cd68                dev_open
                0x0600ce28                dev_ioctrl
 .text          0x0600ce98      0x244 obj\Debug\dev\fs\src\diskio.o
                0x0600ce98                get_fattime
                0x0600cf18                disk_status
                0x0600cf68                disk_initialize
                0x0600cfbc                disk_read
                0x0600d018                disk_write
                0x0600d074                disk_ioctl
 .text          0x0600d0dc     0x8084 obj\Debug\dev\fs\src\ff.o
                0x06010ec4                f_mount
                0x06010f74                f_open
                0x060114c4                f_read
                0x060117fc                f_write
                0x06011b8c                f_sync
                0x06011df0                f_close
                0x06011e3c                f_ftime
                0x06011e98                f_lseek
                0x0601261c                f_opendir
                0x06012774                f_closedir
                0x060127b0                f_readdir
                0x06012854                f_findnext
                0x060128f8                f_findfirst
                0x0601294c                f_stat
                0x060129e0                f_getfree
                0x06012d5c                f_truncate
                0x06012eb4                f_unlink
                0x06013068                f_mkdir
                0x06013358                f_rename
                0x0601363c                f_chmod
                0x06013724                f_utime
                0x06013808                f_expand
                0x06013aec                f_mkfs
                0x06014de4                FEX_getlink_clust
                0x06014e1c                f_merge
                0x06014fcc                _f_bound
 .text.unlikely
                0x06015160       0x30 obj\Debug\dev\fs\src\ff.o
 .text          0x06015190      0x204 obj\Debug\dev\fs\src\ffunicode.o
                0x06015190                ff_uni2oem
                0x06015208                ff_oem2uni
                0x06015280                ff_wtoupper
 .text          0x06015394      0xa2c obj\Debug\dev\fs\src\fs_api.o
                0x06015394                fs_exfunc_init
                0x060153ec                fs_nodeinit
                0x0601541c                fs_mount
                0x06015518                fs_open
                0x060155e8                fs_close
                0x06015660                fs_read
                0x060156d8                fs_write
                0x060157c8                fs_seek
                0x06015894                fs_getcltbl
                0x060158e4                fs_getclusize
                0x06015934                fs_mkdir
                0x06015960                fs_alloc
                0x060159d4                fs_sync
                0x06015a3c                fs_merge
                0x06015ac8                fs_bound
                0x06015b54                fs_getclustersize
                0x06015b78                fs_size
                0x06015bc4                fs_pre_size
                0x06015c10                fs_tell
                0x06015c5c                fs_free_size
                0x06015ce0                fs_check
                0x06015d08                fs_getStartSector
                0x06015d64                fs_ftime
 .text          0x06015dc0      0x36c obj\Debug\dev\gsensor\src\gsensor_api.o
                0x06015dc0                gsensor_iic_enable
                0x06015de4                gsensor_iic_disable
                0x06015e04                gSensorGetName
                0x06015e38                dev_gSensor_Init
                0x06015f58                dev_gSensor_ioctrl
 .text          0x0601612c      0x550 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .text          0x0601667c      0x80c obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .text          0x06016e88      0x4fc obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .text          0x06017384      0x12c obj\Debug\dev\ir\src\ir_api.o
                0x06017384                dev_ir_init
                0x060173cc                dev_ir_ioctrl
 .text          0x060174b0      0x624 obj\Debug\dev\key\src\key_api.o
                0x060174b0                dev_key_init
                0x06017604                dev_key_ioctrl
                0x06017a70                getKeyADCvalue
                0x06017a90                getKeyCurEvent
                0x06017ab0                keyLongTypeScanModeSet
 .text          0x06017ad4      0x318 obj\Debug\dev\lcd\src\lcd_api.o
                0x06017ad4                lcd_initTab_config
                0x06017be4                LcdGetName
                0x06017c00                dev_lcd_init
                0x06017cb0                dev_lcd_ioctrl
                0x06017dcc                dev_lcd_nocolor_status
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .text          0x06017dec        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .text          0x06017dec      0x104 obj\Debug\dev\led\src\led_api.o
                0x06017dec                dev_led_init
                0x06017e04                dev_led_ioctrl
 .text          0x06017ef0       0xf0 obj\Debug\dev\led_pwm\src\led_pwm_api.o
                0x06017ef0                dev_led_pwm_init
                0x06017f08                dev_led_pwm_ioctrl
 .text          0x06017fe0      0x30c obj\Debug\dev\nvfs\src\nvfs_api.o
                0x06017fe0                nv_port_read
                0x06018000                nv_init
                0x06018130                nv_uninit
                0x06018148                nv_configAddr
                0x060181c0                nv_open
                0x0601823c                nv_size
                0x060182bc                nv_read
 .text          0x060182ec     0x1f0c obj\Debug\dev\nvfs\src\nvfs_jpg.o
                0x06018b74                nv_dir_read
                0x06018cb8                nv_dir_readfirst
                0x06018d28                nv_dir_readnext
                0x06018d7c                nv_jpg_ex_force_init
                0x06018d9c                nv_jpg_ex_init
                0x06018e78                nv_jpg_formart_init
                0x06019048                nv_jpg_init
                0x06019288                nv_jpg_uinit
                0x060192f0                nv_jpg_format
                0x06019380                nv_jpg_open
                0x060196b8                nv_jpg_change_lock
                0x06019794                nv_jpg_close
                0x0601986c                nv_jpgfile_read
                0x06019a6c                nv_jpgfile_seek
                0x06019c88                nv_jpgfile_delete
                0x06019d80                nv_jpgfile_size
                0x06019dc0                nvjpg_free_size
                0x06019df4                nv_jpgfile_write
                0x0601a1c8                nvjpg_free_dir
 .text          0x0601a1f8      0xc28 obj\Debug\dev\sd\src\sd_api.o
                0x0601a98c                sd_api_init
                0x0601abd8                sd_api_getNextLBA
                0x0601abf8                sd_api_Uninit
                0x0601ac18                sd_api_lock
                0x0601ac58                sd_api_unlock
                0x0601ac84                sd_api_CardState_Set
                0x0601aca4                sd_api_CardState_Get
                0x0601acc4                sd_api_GetBusWidth
                0x0601ace4                sd_api_Capacity
                0x0601ad04                sd_api_speed_debg
                0x0601ad50                dev_sdc_init
                0x0601ad80                dev_sdc_ioctrl
 .text          0x0601ae20      0x708 obj\Debug\dev\sensor\src\sensor_api.o
                0x0601ae20                sensor_iic_write
                0x0601ae8c                sensor_iic_read
                0x0601af0c                sensor_rgbgamma_tab_load
                0x0601afd4                sensor_ygamma_tab_load
                0x0601b09c                sensor_lsc_tab_load
                0x0601b0ec                SensorGetName
                0x0601b108                dev_sensor_init
                0x0601b120                dev_sensor_ioctrl
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .text          0x0601b528        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .text          0x0601b528      0x504 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                0x0601b588                dev_touchpanel_Init
                0x0601b724                dev_touchpanel_ioctrl
 .text          0x0601ba2c      0x1ec obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                0x0601ba2c                tp_icnt81_getPoint
 .text          0x0601bc18      0x74c obj\Debug\dev\touchpanel\src\touchpanel_iic.o
                0x0601c05c                tp_iic_init
                0x0601c0d0                tp_iic_config
                0x0601c0f4                tp_iic_write
                0x0601c204                tp_iic_read
 .text          0x0601c364      0x374 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                0x0601c364                tp_ns2009_Match
                0x0601c408                tp_ns2009_getPoint
 .text          0x0601c6d8      0x3f4 obj\Debug\dev\usb\dusb\src\dusb_api.o
                0x0601c730                dusb_api_online
                0x0601c750                dusb_api_Init
                0x0601c84c                dusb_api_Uninit
                0x0601c88c                dusb_api_offline
                0x0601c8c4                dusb_api_Process
                0x0601c934                dev_dusb_init
                0x0601c970                dev_dusb_ioctrl
 .text          0x0601cacc      0xb50 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x0601cc74                dusb_stall_ep
                0x0601cd00                dusb_ep0_tx
                0x0601cd7c                dusb_ep0_recieve_set
                0x0601cdb4                dusb_ep0_process
                0x0601d508                dusb_ep0_cfg
                0x0601d55c                dusb_cfg_reg
 .text          0x0601d61c     0x1150 obj\Debug\dev\usb\dusb\src\dusb_msc.o
                0x0601d794                msc_epx_cfg
                0x0601d830                dusb_WriteToMem
                0x0601d870                dusb_ReadFromMem
                0x0601d894                rbc_mem_read
                0x0601d904                rbc_mem_write
                0x0601d97c                rbc_mem_rxfunc
                0x0601d9b8                sdk_returnmask
                0x0601daf4                cbw_updatartc
                0x0601db3c                mscCmd_ufmod
                0x0601db88                sent_csw
                0x0601dc64                mscCmd_Read
                0x0601dd64                mscCmd_Write
                0x0601de64                scsi_cmd_analysis
                0x0601e3c0                get_cbw
                0x0601e60c                rbc_rec_pkg
                0x0601e6ac                rbc_process
 .text          0x0601e76c        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .text          0x0601e76c      0x5a4 obj\Debug\dev\usb\dusb\src\dusb_uac.o
                0x0601e76c                uac_set_volume
                0x0601e7b8                uac_set_mute
                0x0601e800                uac_isr_process
                0x0601e920                uac_epx_cfg
                0x0601e994                uac_get_volume
                0x0601ea24                uac_unit_ctl_hal
                0x0601eaf8                UacHandleToStreaming
                0x0601eb94                uac_start
                0x0601ec58                UacReceiveSetSamplingFreqCallback
                0x0601ecb4                uac_stop
 .text          0x0601ed10      0x8f0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                0x0601ed10                uvc_pic_callback
                0x0601ed38                unitsel_set
                0x0601ed74                uvc_video_probe_control_callback
                0x0601eda8                uvc_still_probe_control_callback
                0x0601eddc                uvc_epx_cfg
                0x0601ef30                uvc_unit_ctl_hal
                0x0601efd8                uvc_video_probe_control
                0x0601f020                uvc_still_probe_control
                0x0601f068                uvc_still_trigger_control
                0x0601f09c                uvc_probe_ctl_hal
                0x0601f118                uvc_start
                0x0601f1bc                uvc_stop
                0x0601f220                uvc_is_start
                0x0601f240                uvc_pic_sanp
                0x0601f260                uvc_header_fill
                0x0601f348                uvc_isr_process
                0x0601f4f4                uvc_process
 .text          0x0601f600      0x8f0 obj\Debug\dev\usb\husb\src\husb_api.o
                0x0601f600                husb_api_u20_remove
                0x0601f68c                husb_api_u11_remove
                0x0601f928                husb_api_handle_get
                0x0601f958                husb_api_init
                0x0601fab4                husb_api_devicesta
                0x0601fae8                husb_api_devicesta_set
                0x0601fb3c                husb_api_msc_try_tran
                0x0601fb78                dev_husb_io_power_set
                0x0601fc60                dev_husb_init
                0x0601fd24                dev_husb_ioctrl
 .text          0x0601fef0     0x2970 obj\Debug\dev\usb\husb\src\husb_enum.o
                0x060203b4                husb_hub_clrport_feature_ack
                0x06020414                husb_hub_set_port_devaddr_ack
                0x060205c0                husb_get_pcommit_cs100_ack
                0x060206a4                husb_uvcunit_ack
                0x06020db8                husb_setport_feature_ack
                0x06020de0                husb_hub_port_reset_ack
                0x06020e08                husb_get_max_lun_ack
                0x06020e3c                husb_set_intfs_uvc_1_ack
                0x06020e60                husb_hub_inf_ack
                0x06021014                husb_hub_getport_status_ack
                0x060210b4                husb_set_pcommit_cs200_ack
                0x060210f0                husb20_ep0_cfg
                0x06021110                husb11_ep0_cfg
                0x06021130                usensor_resolution_select
                0x0602145c                husb_all_get_cfg_desc_ack
                0x06021fec                husb_astern_check
                0x06022028                husb_astern_ack
                0x060220ac                husb_api_ep0_kick
                0x0602218c                husb_api_ep0_process
                0x060222d4                husb_api_ep0_uvc_switch_kick
                0x06022310                husb_api_ep0_asterncheck_kick
                0x0602235c                husb_api_uvcunit_get_kick
                0x060224f0                husb_api_uvcunit_get_done
                0x060225a4                husb_api_uvcunit_set_kick
                0x06022740                husb_api_uvcunit_set_done
                0x060227e8                husb_api_hub_check_kick
 .text          0x06022860      0x230 obj\Debug\dev\usb\husb\src\husb_hub.o
                0x06022860                husb_hub_init
                0x06022a04                husb_hub_uinit
 .text          0x06022a90     0x1138 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
                0x06022a90                udisk_cap
                0x06022ab4                udisk_online
                0x06022ae4                husb_msc_init
                0x06022c70                epbulk20_send_dat
                0x06022e34                epbulk20_recieve_dat
                0x06022fa4                epbulk11_send_dat
                0x06023144                epbulk11_recieve_dat
                0x060232a8                epbulk_send_dat
                0x060232f8                epbulk_receive_dat
                0x06023348                cbw_init
                0x06023394                rbc_read_lba
                0x06023498                rbc_write_lba
                0x0602358c                spc_inquiry
                0x06023640                spc_test_unit_rdy
                0x060236e0                spc_request_sense
                0x06023794                rbc_read_capacity
                0x06023924                spc_StartStopUnit
                0x060239ac                enum_mass_dev
 .text          0x06023bc8      0x7d4 obj\Debug\dev\usb\husb\src\husb_usensor.o
                0x06023bc8                husb_api_handle_reg
                0x06023be8                husb_api_usensor_tran_sta
                0x06023c20                husb_api_usensor_atech_sta
                0x06023c58                husb_api_usensor_res_get
                0x06023ca0                husb_api_usensor_res_type_is_mjp
                0x06023cdc                husb_api_usensor_res_type_is_yuv
                0x06023d18                husb_api_astern_set
                0x06023d78                husb_api_astern_get
                0x06023db0                husb_api_detech_check
                0x06023de8                husb_api_usensor_linkingLcd
                0x06023e14                husb_api_usensor_relinkLcd_reg
                0x06023e40                husb_api_usensor_dcdown
                0x06023e78                husb_api_usensor_detech
                0x06023eec                husb_api_usensor_asterncheck
                0x06023f20                husb_api_usensor_asternset
                0x06023f7c                husb_api_usensor_frame_read
                0x06023fe8                husb_api_usensor_csi_kick
                0x06024058                husb_api_usensor_switch_res_kick
                0x060241e4                husb_api_usensor_notran_check
                0x060242c0                husb_api_usensor_stop_fill
                0x060242f0                husb_api_usensor_uvcunit_get
                0x0602434c                husb_api_usensor_uvcunit_set
 .text          0x0602439c      0x740 obj\Debug\dev\usb\husb\src\husb_uvc.o
                0x060245c0                husb_uvc_atech_codec
                0x06024690                husb_uvc_dcd_cache_get
                0x060246b4                husb_uvc_init
                0x060248d4                husb_uvc_linking
                0x06024998                husb_uvc_relink_register
                0x060249c8                husb_uvc_detech
                0x06024a10                husb_uvc_stop_fill
 .text          0x06024adc       0xcc obj\Debug\hal\src\hal_adc.o
                0x06024adc                hal_adcInit
                0x06024b1c                hal_adcRead
                0x06024b50                hal_adcSetChannel
 .text          0x06024ba8      0x784 obj\Debug\hal\src\hal_auadc.o
                0x06024e60                hal_auadc_stamp_out
                0x06024e80                hal_auadc_stamp_next
                0x06024ea0                hal_auadcInit
                0x06024ee0                hal_auadcMemInit
                0x06024f90                hal_auadc_pcmsize_get
                0x06024fb0                hal_auadcMemUninit
                0x0602500c                hal_auadc_cnt
                0x06025038                hal_auadcmutebuf_get
                0x06025058                hal_auadcStart
                0x060251f8                hal_auadcBufferGet
                0x06025264                hal_auadcBufferRelease
                0x0602528c                hal_auadcStop
                0x060252cc                hal_adcBuffer_prefull
                0x0602530c                hal_adc_volume_set
 .text          0x0602532c      0x4fc obj\Debug\hal\src\hal_csi.o
                0x0602562c                hal_csi_init
                0x06025650                hal_csi_input_switch
                0x06025784                hal_csi_test_fps_adj
 .text          0x06025828      0x1f0 obj\Debug\hal\src\hal_dac.o
                0x06025828                hal_dacInit
                0x0602588c                hal_dacPlayInit
                0x06025914                hal_dacPlayStart
                0x06025970                hal_dacPlayStop
                0x06025998                hal_dacSetVolume
                0x060259f8                hal_dacCallBackRegister
 .text          0x06025a18      0x42c obj\Debug\hal\src\hal_dmauart.o
                0x06025a18                hal_dmaUartRxOverWait
                0x06025aac                hal_dmaUartRxDataOut
                0x06025b48                hal_dmaUartIRQHandler
                0x06025bd0                hal_dmauartInit
                0x06025c78                hal_dmauartTxDma
                0x06025cd8                hal_dmauartTxDmaKick
                0x06025d30                hal_dmauartTest
 .text          0x06025e44        0x0 obj\Debug\hal\src\hal_eeprom.o
 .text          0x06025e44       0xa8 obj\Debug\hal\src\hal_gpio.o
                0x06025e44                hal_gpioInit_io1d1
 .text          0x06025eec      0xa50 obj\Debug\hal\src\hal_iic.o
                0x06025eec                hal_iic0Init
                0x06025f1c                hal_iic0Uninit
                0x06025f3c                hal_iic08bitAddrWriteData
                0x06025fa8                hal_iic08bitAddrReadData
                0x06026030                hal_iic08bitAddrWrite
                0x060260c0                hal_iic08bitAddrRead
                0x06026160                hal_iic016bitAddrWriteData
                0x060261d8                hal_iic016bitAddrReadData
                0x0602626c                hal_iic016bitAddrWrite
                0x06026308                hal_iic016bitAddrRead
                0x060263b4                hal_iic1IOShare
                0x060263e4                hal_iic1IOShareCheck
                0x0602642c                hal_iic1Init
                0x0602646c                hal_iic1Uninit
                0x060264a4                hal_iic18bitAddrWriteData
                0x06026514                hal_iic18bitAddrReadData
                0x060265a0                hal_iic18bitAddrWrite
                0x06026634                hal_iic18bitAddrRead
                0x060266d8                hal_iic116bitAddrWriteData
                0x06026754                hal_iic116bitAddrReadData
                0x060267ec                hal_iic116bitAddrWrite
                0x0602688c                hal_iic116bitAddrRead
 .text          0x0602693c       0x34 obj\Debug\hal\src\hal_int.o
                0x0602693c                hal_intInit
 .text          0x06026970     0x18cc obj\Debug\hal\src\hal_lcdshow.o
                0x06026a04                hal_lcdSetCsiScaler
                0x06026ab8                hal_lcdSetVideoScaler
                0x06026b28                hal_lcd_scaler_done_check
                0x06026b80                hal_lcdVideoSetRotate
                0x06026c78                hal_lcdUiSetRotate
                0x06026cd8                hal_lcdSetRatio
                0x06026f14                hal_lcdSetBufYUV
                0x06026fa8                hal_lcdSetBufYUV_2
                0x0602702c                hal_lcdSetWINAB
                0x06027100                hal_lcdWinEnablePreSet
                0x06027120                hal_lcdSetWinEnable
                0x06027158                lcd_struct_get
                0x06027178                hal_lcdLCMPowerOff
                0x060271fc                hal_lcd_decwin_done
                0x06027508                hal_lcd_encwin_done
                0x060275b8                hal_lcd_frame_enc_func_register
                0x06027608                hal_CSI_lcdFrameEndCallback
                0x06027a50                hal_lcd_fps_debg
                0x06027a98                hal_lcdGetSreenResolution
                0x06027b20                hal_lcdGetUiResolution
                0x06027b7c                hal_lcdGetUiPosition
                0x06027bd8                hal_lcdUiScanModeGet
                0x06027c08                hal_lcdGetVideoRatioResolution
                0x06027c64                hal_lcdSetVideoRatioResolution
                0x06027c9c                hal_lcdGetVideoRatioPos
                0x06027cf8                hal_lcdGetVideoResolution
                0x06027d54                hal_lcdGetVideoPos
                0x06027db0                hal_lcdVideoScanModeGet
                0x06027de0                hal_lcdVideoScalerTypeAdj
                0x06027e98                hal_lcd_enc_start
                0x06027f2c                hal_lcd_enc_stop
                0x06027f98                hal_lcd_enc_checkdone
                0x06027fc4                hal_lcd_enc_frame_get
                0x06028004                hal_lcd_enc_frame_res_get
                0x06028048                hal_lcd_pause_set
                0x0602809c                hal_lcd_pause_sta_get
                0x060280ec                hal_lcdSetGamma
 .text          0x0602823c      0x154 obj\Debug\hal\src\hal_md.o
                0x06028264                hal_mdInit
                0x06028310                hal_mdEnable
                0x06028358                hal_mdCheck
 .text          0x06028390     0x1dc0 obj\Debug\hal\src\hal_mjpAEncode.o
                0x06028454                hal_mjpA_Start
                0x06028484                hal_mjpA_Restart
                0x060286ec                hal_mjpA_Sizecalculate
                0x06028718                hal_mjpA_EncState
                0x06028d50                hal_jA_fcnt_mnt
                0x06028da4                hal_mjpA_Linebuf_nocolor_change
                0x06029024                hal_mjpAEncodePhotoResumePKG
                0x060290e4                hal_mjpAEncodePhotoResumeLLPKG
                0x06029194                hal_mjpAEncodePhotoResumeRam
                0x06029290                hal_mjpA_EncodeInit
                0x060292ec                hal_mjpA_LineBuf_get
                0x06029318                hal_mjpA_src_res_get
                0x0602934c                hal_mjpA_buf_MenInit
                0x060293fc                hal_mjpA_linebufUninit
                0x06029444                hal_mjpA_MemUninit
                0x06029494                hal_mjpA_EncodeUninit
                0x06029520                hal_mjpA_EncVideo_Start
                0x060298ac                hal_mjpA_EncPhoto_Start
                0x06029c40                hal_mjpA_EncPhotoLcd_Start
                0x06029ef0                hal_mjpA_photo_encode_mode
                0x06029f10                hal_mjpA_RawBufferfree
                0x06029f38                hal_mjpA_RawBufferGet
                0x0602a034                hal_mjpA_RkgBufferGet
                0x0602a0d8                hal_mjpA_Buffer_prefull
                0x0602a114                hal_mjpA_Buffer_halffull
 .text          0x0602a150      0x85c obj\Debug\hal\src\hal_mjpBEncode.o
                0x0602a150                hal_mjpBEncodeKickManual
                0x0602a248                hal_mjpB_Sizecalculate
                0x0602a270                hal_jB_fcnt_mnt
                0x0602a2c4                hal_mjpBEnc_state
                0x0602a2f0                hal_mjpBEncodeDoneCfg
                0x0602a314                hal_mjpBEncodeDoneManual
                0x0602a424                hal_mjpB_LineBuf_MenInit
                0x0602a4c0                hal_mjpB_LineBuf_cfg
                0x0602a4e4                hal_mjpB_buf_MenInit
                0x0602a59c                hal_mjpB_MemUninit
                0x0602a5fc                hal_mjpB_usb_resolution_set
                0x0602a620                hal_mjpB_DecodeODMA1En
                0x0602a664                hal_mjpB_Enc_Start
                0x0602a7dc                hal_mjpB_Enc_Stop
                0x0602a810                hal_mjpB_RawBufferfree
                0x0602a838                hal_mjpB_RawBufferGet
                0x0602a934                hal_mjpB_Buffer_prefull
                0x0602a970                hal_mjpB_Buffer_halffull
 .text          0x0602a9ac      0xe50 obj\Debug\hal\src\hal_mjpDecode.o
                0x0602a9ac                hal_mjpHeaderParse
                0x0602b05c                hal_mjpDecodeIsYUV422
                0x0602b07c                hal_mjpDecodeGetResolution
                0x0602b0c0                hal_mjpDecodeSetResolution
                0x0602b0e4                hal_mjpDecodeBusyCheck
                0x0602b104                hal_mjpDecodeErrorCheck
                0x0602b124                hal_mjpDecodeStop
                0x0602b144                hal_mjpDecodeReset
                0x0602b170                hal_mjpDecodePicture
                0x0602b200                hal_mjpegDecodePicture_noisr
                0x0602b28c                hal_mjpegDecodePicture_packet
                0x0602b33c                hal_mjpDecodeParse
                0x0602b36c                hal_mjpDecodeOneFrame
                0x0602b3e4                hal_mjpDecodeOneFrame_Ext
                0x0602b47c                hal_mjpDecodeRestart_Ext
                0x0602b4e8                hal_mjpDecodeOneFrame_Fast
                0x0602b5e4                hal_mjpDecodeMiniSize
                0x0602b768                hal_mjpDecodeODma1Cfg
                0x0602b78c                hal_BackRecDecodeStatusCheck
 .text          0x0602b7fc      0xc80 obj\Debug\hal\src\hal_rtc.o
                0x0602b828                hal_rtcCallBackRegister
                0x0602b888                hal_rtcCallBackRelease
                0x0602b8c4                hal_rtcUninit
                0x0602b900                hal_rtcTimeGet
                0x0602b91c                hal_rtcTimeGetExt
                0x0602b950                hal_rtcSecondGet
                0x0602b970                hal_rtcTime2String
                0x0602bc24                hal_rtcTime2StringExt
                0x0602bc88                hal_rtcLeapYear
                0x0602bea8                hal_rtcTime
                0x0602bfbc                hal_rtcInit
                0x0602c0fc                hal_rtcSecondSet
                0x0602c138                hal_rtcValue
                0x0602c248                hal_rtcTimeSet
                0x0602c2ec                hal_rtcAlarmSet
                0x0602c37c                hal_rtcAlarmSetExt
                0x0602c420                hal_rtcAlarmStatusGet
                0x0602c45c                hal_rtcTrimCallBack
 .text          0x0602c47c        0x0 obj\Debug\hal\src\hal_spi.o
 .text          0x0602c47c      0x504 obj\Debug\hal\src\hal_spi1.o
                0x0602c47c                hal_spi1DmaCallback
                0x0602c510                hal_spi1DmaDoneCheck
                0x0602c590                hal_spi1Init
                0x0602c5e8                hal_spi1SendByte
                0x0602c60c                hal_spi1RecvByte
                0x0602c62c                hal_spi1SendDmaKick
                0x0602c6c4                hal_spi1SendDma
                0x0602c71c                hal_spi1RecvDmaKick
                0x0602c7b8                hal_spi1RecvDma
                0x0602c810                hal_spi1_test
 .text          0x0602c980      0x21c obj\Debug\hal\src\hal_stream.o
                0x0602c980                hal_streamInit
                0x0602cadc                hal_streamMallocDrop
                0x0602cb84                hal_stream_size
 .text          0x0602cb9c      0x9ac obj\Debug\hal\src\hal_sys.o
                0x0602ce98                hal_sysMemPrint
                0x0602cfa4                hal_sysMemMalloc
                0x0602d0bc                hal_sysMemMallocLast
                0x0602d1e8                hal_sysMemFree
                0x0602d3c4                hal_sysMemRemain
                0x0602d41c                hal_sysInit
 .text          0x0602d548       0xe0 obj\Debug\hal\src\hal_timer.o
                0x0602d548                hal_timerEnable
                0x0602d580                hal_timerTickEnable
                0x0602d5b4                hal_timerPWMStart
 .text          0x0602d628      0x540 obj\Debug\hal\src\hal_uart.o
                0x0602d68c                hal_uartIOShare
                0x0602d6c8                hal_uartIOShareCheck
                0x0602d714                hal_uartInit
                0x0602d76c                hal_uartSendData
                0x0602d790                hal_uartRXIsrRegister
                0x0602d7b0                uart_PutChar_n
                0x0602d7e4                uart_PutStr
                0x0602d824                uart_Put_hex
                0x0602d950                uart_Put_udec
                0x0602d9e4                uart_Put_dec
                0x0602da8c                uart_PrintfBuf
                0x0602db24                hal_uartPrintString
 .text          0x0602db68      0xa04 obj\Debug\hal\src\hal_watermark.o
                0x0602dbd0                hal_watermarkInit
                0x0602dc80                hal_watermarkClose
                0x0602dd14                hal_watermarkClear
                0x0602dd94                hal_watermarkOpen
                0x0602de34                hal_watermarkColor
                0x0602de94                hal_watermarkAddr
                0x0602deac                hal_watermarkSize
                0x0602def8                hal_watermarkPosition
                0x0602df44                hal_watermarkCallbackRegister
                0x0602df8c                hal_watermarkRam
                0x0602e184                hal_watermarkEnable
                0x0602e2d4                hal_jpg_watermark_init
                0x0602e364                hal_jpg_watermark_uinit
                0x0602e3a8                hal_jpg_watermarkStart
                0x0602e4fc                hal_jpgB_watermarkPos_Adjust
 .text          0x0602e56c        0x0 obj\Debug\hal\src\hal_wdt.o
 .text          0x0602e56c        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .text          0x0602e56c        0x0 obj\Debug\mcu\xos\xmbox.o
 .text          0x0602e56c      0x2e4 obj\Debug\mcu\xos\xmsgq.o
                0x0602e56c                XMsgQInit
                0x0602e5ac                XMsgQCreate
                0x0602e63c                XMsgQDestory
                0x0602e674                XMsgQFlush
                0x0602e6b4                XMsgQPost
                0x0602e72c                XMsgQPostFront
                0x0602e7a8                XMsgQPend
                0x0602e838                XMsgQCheck
 .text          0x0602e850      0x14c obj\Debug\mcu\xos\xos.o
                0x0602e850                XOSInit
                0x0602e88c                XOSTickService
                0x0602e8fc                XOSTimeGet
                0x0602e91c                XOSTimeDly
                0x0602e97c                XOSRandom
 .text          0x0602e99c      0x168 obj\Debug\mcu\xos\xwork.o
                0x0602e99c                XWorkInit
                0x0602e9dc                XWorkCreate
                0x0602ea68                XWorkDestory
                0x0602ea98                XWorkService
 .text          0x0602eb04      0x998 obj\Debug\multimedia\audio\audio_playback.o
                0x0602eb04                audioPlaybackInit
                0x0602eb50                audioPlaybackParse
                0x0602ec40                audioPlaybackStop
                0x0602ed48                audioPlaybackUninit
                0x0602ed80                audioPlaybackStart
                0x0602f150                audioPlaybackPause
                0x0602f18c                audioPlaybackFirstPause
                0x0602f1e8                audioPlaybackResume
                0x0602f250                audioPlaybackGetStatus
                0x0602f270                audioPlaybackGetTime
                0x0602f2ec                audioPlaybackSetVolume
                0x0602f340                audioPlaybackGetVolume
                0x0602f360                audioPlaybackService
 .text          0x0602f49c      0x528 obj\Debug\multimedia\audio\audio_record.o
                0x0602f4d0                audioRecordInit
                0x0602f598                audioRecordUninit
                0x0602f5ec                audioRecordStop
                0x0602f698                audioRecordStart
                0x0602f7d4                audioRecordPuase
                0x0602f804                audioRecordResume
                0x0602f834                audioRecordGetStatus
                0x0602f854                audioRecordSetStatus
                0x0602f874                audioRecordGetTime
                0x0602f894                audioRecordService
 .text          0x0602f9c4      0xbf4 obj\Debug\multimedia\image\image_decode.o
                0x0602f9c4                imageDecodeSubCheck
                0x0602fb40                imageDecodeStart
                0x06030108                imageDecodeSpiStart
                0x06030318                imageDecodeGetResolution
                0x0603033c                imageDecodeDirect
 .text          0x060305b8      0x9c8 obj\Debug\multimedia\image\image_encode.o
                0x060305b8                imageEncodeInit
                0x060305e4                imageEncodeUninit
                0x06030608                imageEncodeStart
                0x06030c78                imageLcdEncodeStart
                0x06030d64                imageEncodeToSpi
 .text          0x06030f80      0x158 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                0x06030f80                jpg_encode
 .text          0x060310d8     0x19a0 obj\Debug\multimedia\video\video_playback.o
                0x0603120c                videoPlaybackClear
                0x06031270                videoPlaybackInit
                0x0603134c                videoPlaybackDecodeWait
                0x06031398                videoPlaybackStart
                0x060317b0                videoPlaybackStop
                0x060318e8                videoPlaybackUninit
                0x06031b5c                videoPlaybackPause
                0x06031be0                videoPlaybackResume
                0x06031c44                videoPlaybackGetStatus
                0x06031c64                videoPlaybackGetSpeed
                0x06031c84                videoPlaybackSetVolume
                0x06031cd4                videoPlaybackGetVolume
                0x06031cf4                videoPlaybackGetTime
                0x06031d2c                videoPlaybabkGetArg
                0x06031d48                videoGetFirstFrame
                0x06031f24                videoDecodeFirstFrame
                0x06032038                videoPlaybackService
                0x060328ec                videoPlaybackFastForward
                0x060329b4                videoPlaybackFastBackward
 .text          0x06032a78      0xed8 obj\Debug\multimedia\video\video_record.o
                0x06032a78                videoRecordInit
                0x06032b38                videoRecordUninit
                0x06032b84                videoRecordFileStart
                0x06032cf4                videoRecordFileStop
                0x06032dbc                videoRecordFileError
                0x06032e24                videoRecordError
                0x06032e74                videoRecordStart
                0x06032f90                videoRecordGetTimeSec
                0x06032fd4                videoRecordGetStatus
                0x06032ff4                videoRecordJunkSync
                0x06033030                videoRecordStop
                0x060330a8                videoRecordRestart
                0x060331a4                videoRecordFrameProcess
                0x06033680                videoRecordService
                0x060337b4                videoRecordCmdSet
                0x06033860                videoRecordSizePreSec
                0x060338cc                videoRecordTakePhotoCfg
                0x060338f0                videoRecordTakePhotoStatus
                0x06033910                videoRecordSetPhotoStatus
                0x06033930                videoRecordTakePhotoFd
 .text          0x06033950     0x19f0 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
                0x06033a44                filelist_listFlush
                0x06033b48                filelist_api_Init
                0x06033b74                filelist_api_nodecreate
                0x06033edc                filelist_api_nodedestory
                0x06033ff8                filelist_api_scan
                0x060341c8                filenode_api_CountGet
                0x060341f8                filelist_api_CountGet
                0x06034238                filelist_api_MaxCountGet
                0x06034268                filelist_GetFileNameByIndex
                0x060342e4                filelist_GetFileFullNameByIndex
                0x06034448                filelist_GetFileShortNameByIndex
                0x06034568                filelist_GetFileIndexByIndex
                0x06034614                filelist_findFirstFileName
                0x06034694                filelist_findFileNameByFname
                0x06034768                filelist_delFileByIndex
                0x06034880                filelist_delFileByFname
                0x06034938                filelist_listDelAll
                0x06034aac                filelist_createNewFileFullName
                0x06034b14                filelist_createNewFileFullNameByFname
                0x06034b54                filenode_addFileByFname
                0x06034b9c                filenode_filefullnameLock
                0x06034c70                filenode_filefullnameUnlock
                0x06034d44                filenode_fnameLockByIndex
                0x06034dbc                filenode_fnameUnlockByIndex
                0x06034e38                filelist_fnameChecklockByIndex
                0x06034ec4                filenode_parentdir_get
                0x06034f6c                filelist_GetLrcFileFullNameByIndex
                0x06035014                filelist_SpaceCheck
 .text          0x06035340      0xedc obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                0x06035340                filelist_NameChangeSufType
                0x060353a0                filenode_fname_check
                0x060353dc                filenode_fname_createNew
                0x06035540                filenode_filename_CreateByFname
                0x06035890                filenode_filefullname_CreateByFname
                0x0603590c                filenode_AddFileByFname
                0x06035ac8                filenode_Scan
                0x0603607c                filenode_api_findfirst
                0x0603615c                filenode_api_findByFname
 .text          0x0603621c      0x200 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
                0x0603621c                res_ascii_get
                0x06036304                res_getAsciiCharSize
                0x0603633c                res_getAsciiStringSize
 .text          0x0603641c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .text          0x0603641c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .text          0x0603641c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .text          0x0603641c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .text          0x0603641c      0x65c obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
                0x0603641c                res_font_Init
                0x0603654c                res_font_SetLanguage
                0x0603661c                res_font_GetString
                0x06036734                res_font_GetChar
                0x060368fc                res_font_StringTableInit
                0x06036988                res_font_GetAddrAndSize
 .text          0x06036a78      0x738 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
                0x06036b10                res_iconInit
                0x06036c10                res_iconBuffInit
                0x06036c7c                res_iconGetSizeByIndex
                0x06036d30                res_iconGetAddrByIndex
                0x06036db0                res_icon_GetAddrAndSize
                0x06036e7c                res_icon_GetTColor
                0x06036eb0                res_iconBuffTimeUpdate
                0x06036ef4                res_iconGetData
                0x0603712c                res_iconGetPalette
 .text          0x060371b0      0x324 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
                0x060371b0                res_image_show
                0x060372c8                res_image_show1
                0x060373fc                res_image_decode
 .text          0x060374d4      0x118 obj\Debug\sys_manage\res_manage\res_manage_api.o
                0x060374d4                res_GetStringInfor
                0x0603753c                res_GetCharInfor
 .text          0x060375ec      0x368 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
                0x060375ec                res_music_end
                0x06037644                res_music_start
                0x0603778c                res_keysound_init
                0x060378c0                res_keysound_play
                0x060378f4                res_keysound_stop
 .text          0x06037954        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .text          0x06037954      0x1c8 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
                0x06037a20                uiButtonCreate
 .text          0x06037b1c      0x2f8 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
                0x06037c18                uiCycleCreateDirect
                0x06037ca4                uiCycleCreate
 .text          0x06037e14      0x1ec obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
                0x06037e14                uiDialogCreate
                0x06037f48                uiDialogItem
 .text          0x06038000     0x19cc obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                0x06038590                uiWinDrawInit
                0x060385e8                uiWinDrawUpdate
                0x06038644                uiWinDrawLine
                0x06038944                uiWinDrawRect
                0x06038b88                uiWinDrawRoundRectWithRim
                0x06038f2c                uiWinDrawPoint
                0x06038fcc                uiWinDrawCircle
                0x06039160                uiWinDrawIcon
                0x0603944c                uiWinDrawString
 .text          0x060399cc      0x29c obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
                0x06039b50                uiFrameWinCreate
 .text          0x06039c68      0x3b0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
                0x06039e5c                uiImageIconCreateDirect
                0x06039f3c                uiImageIconCreate
 .text          0x0603a018     0x18c0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
                0x0603a018                uiItemManageCreate
                0x0603a108                uiItemManageSetItemHeight
                0x0603a2a8                uiItemManageSetHeightAvgGap
                0x0603a428                uiItemManageSetHeightNotGap
                0x0603a564                uiItemManageSetRowSum
                0x0603a6cc                uiItemManageSetColumnSumWithGap
                0x0603a84c                uiItemManageCreateItem
                0x0603a9f8                uiItemManageSetResInforFuncEx
                0x0603aa58                uiItemManageSetCurItem
                0x0603ad38                uiItemManageUpdateRes
                0x0603adf0                uiItemManageUpdateAllItem
                0x0603ae54                uiItemManageUpdateCurItem
                0x0603aff8                uiItemManageNextItem
                0x0603b068                uiItemManagePreItem
                0x0603b0d8                uiItemManageNextPage
                0x0603b15c                uiItemManagePrePage
                0x0603b1e4                uiItemManageGetCurrentItem
                0x0603b248                uiItemManageSetCharInfor
                0x0603b300                uiItemManageSetSelectColor
                0x0603b398                uiItemManageSetSelectImage
                0x0603b430                uiItemManageSetUnselectColor
                0x0603b4c8                uiItemManageSetUnselectImage
                0x0603b560                uiItemManageGetTouchInfor
                0x0603b758                uiItemManageSetSelectColorEx
                0x0603b818                uiItemManageSetUnselectColorEx
 .text          0x0603b8d8      0x444 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
                0x0603bb70                uiItemCreateItemMenu
 .text          0x0603bd1c      0x5c4 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
                0x0603c0cc                uiItemCreateMenuItemEx
 .text          0x0603c2e0      0x390 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
                0x0603c4f8                uiItemCreateMenuOption
 .text          0x0603c670      0x238 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
                0x0603c708                uiLineCreate
 .text          0x0603c8a8     0x1ccc obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
                0x0603c8a8                uiWinSendMsg
                0x0603c8e8                uiWinSendMsgId
                0x0603c910                uiWinSendMsgToParent
                0x0603c940                uiWinSendMsgIdToParent
                0x0603c968                uiWinOverlapCmp
                0x0603c9cc                uiWinInsideCmp
                0x0603ca30                uiWinStringExRowCal
                0x0603ca98                uiWinStringExGetByRow
                0x0603cafc                uiWinStringExGetNext
                0x0603cb60                uiWinInterSection
                0x0603cbe0                uiWinHasInvalidRect
                0x0603cc7c                uiWinFreeInvalidRect
                0x0603cf38                uiWinSetbgColor
                0x0603cf6c                uiWinSetCycleRadius
                0x0603cfa4                uiWinSetRoundRectRadius
                0x0603cfdc                uiWinSetfgColor
                0x0603d010                uiWinSetVisible
                0x0603d094                uiWinIsVisible
                0x0603d0d0                uiWinSetResid
                0x0603d100                uiWinSetItemSelResid
                0x0603d130                uiWinUpdateResId
                0x0603d154                uiWinUpdateAllResId
                0x0603d1b8                uiWinSetStrInfor
                0x0603d204                uiResInforInit
                0x0603d250                uiWinSetSelectInfor
                0x0603d280                uiWinSetUnselectInfor
                0x0603d2b0                uiWinGetResSum
                0x0603d2f0                uiWinSetResSum
                0x0603d320                uiWinSetResidByNum
                0x0603d370                uiWinSetPorgressRate
                0x0603d3a0                uiWinParentRedraw
                0x0603d410                uiWinGetRelativePos
                0x0603d4a0                uiWinGetPos
                0x0603d4e4                uiWinUpdateInvalid
                0x0603d55c                uiWinSetProgressRate
                0x0603d58c                uiWinSetName
                0x0603d5ac                uiWinGetCurrent
                0x0603d5cc                uiWinDefaultProc
                0x0603d974                uiWinCreate
                0x0603de44                uiWinDestroy
                0x0603dff0                uiWinGetTouchInfor
                0x0603e020                uiWinSetTouchInfor
                0x0603e050                uiWinTouchProcess
                0x0603e258                uiWinDrawProcess
                0x0603e36c                uiWinDestroyDeskTopChildWin
                0x0603e3e0                uiWinInit
                0x0603e4f8                uiWinUninit
 .text          0x0603e574      0x380 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
                0x0603e574                uiWinHeapInit
                0x0603e5b4                uiWinHeapMemInfo
                0x0603e630                uiWinHeapMalloc
                0x0603e700                uiWinHeapFree
                0x0603e7b0                uiMemPoolCreate
                0x0603e830                uiMemPoolGet
                0x0603e878                uiMemPoolPut
                0x0603e8b4                uiMemPoolInfo
 .text          0x0603e8f4      0x2e4 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
                0x0603ea94                uiProgressBarCreateDirect
                0x0603eb30                uiProgressBarCreate
 .text          0x0603ebd8      0x2dc obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
                0x0603ed3c                uiRectCreateDirect
                0x0603edc8                uiRectCreate
 .text          0x0603eeb4      0x678 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
                0x0603f240                uiStringExCreateDirect
                0x0603f33c                uiStringExCreate
 .text          0x0603f52c      0x3fc obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
                0x0603f720                uiStringIconCreateDirect
                0x0603f824                uiStringIconCreate
 .text          0x0603f928      0x250 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
                0x0603faac                uiTipsCreate
 .text          0x0603fb78      0x120 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
                0x0603fb78                uiWidgetProc
                0x0603fbc4                uiWidgetSetType
                0x0603fbf8                uiWidgetGetType
                0x0603fc2c                uiWidgetGetId
                0x0603fc64                uiWidgetSetId
 .text          0x0603fc98      0x310 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
                0x0603ff08                uiWidgetManageCreate
 .text          0x0603ffa8      0x8fc obj\Debug\app\app_common\src\app_init.o
                0x0603ffa8                app_logo_show
                0x06040040                app_version_get
                0x06040074                app_draw_init
                0x060400a4                app_uninit
                0x06040274                app_init
                0x060407f4                app_sendDrawUIMsg
                0x06040828                app_draw_Service
                0x06040870                app_systemService
 .text          0x060408a4     0x107c obj\Debug\app\app_common\src\app_lcdshow.o
                0x060408a4                app_lcdVideoShowScaler_cfg
                0x06041010                app_lcdVideoShowRotate_cfg
                0x060411e8                app_lcdVideoShowMirro_cfg
                0x060413bc                app_lcdCsiVideoShowStart
                0x0604143c                app_lcdCsiVideoLayerEnGet
                0x0604145c                app_lcdCsiVideoShowStop
                0x060414d0                app_lcdVideoIdleFrameGet
                0x060414f0                app_lcdUiShowInit
                0x0604161c                app_lcdUiShowUinit
                0x0604166c                app_lcdUiDrawIdleFrameGet
                0x06041690                app_lcdShowWinModeCfg
 .text          0x06041920        0x0 obj\Debug\app\app_common\src\main.o
 .text.startup  0x06041920       0x5c obj\Debug\app\app_common\src\main.o
                0x06041920                main
 .text          0x0604197c        0x0 obj\Debug\app\resource\user_res.o
 .text          0x0604197c       0xf0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
                0x0604197c                menuProcDelCur
                0x060419ac                menuProcDelAll
                0x060419dc                menuProcLockCur
                0x06041a0c                menuProcUnlockCur
                0x06041a3c                menuProcUnlockAll
 .text          0x06041a6c        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .text          0x06041a6c       0x90 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
                0x06041a6c                menuProcDateTime
                0x06041a9c                menuProcFormat
                0x06041acc                menuProcDefault
 .text          0x06041afc        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
 .text          0x06041afc      0x970 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .text          0x0604246c      0x450 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .text          0x060428bc      0x518 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .text          0x06042dd4      0x58c obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .text          0x06043360      0x548 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .text          0x060438a8      0xef0 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x06044778                menuWinIsOpen
 .text          0x06044798      0x554 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .text          0x06044cec      0x4f8 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .text          0x060451e4      0x588 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .text          0x0604576c      0x564 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .text          0x06045cd0        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .text          0x06045cd0      0x118 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .text          0x06045de8      0x1f8 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .text          0x06045fe0      0xad4 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .text          0x06046ab4      0x434 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .text          0x06046ee8      0x44c obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .text          0x06047334      0x1a8 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .text          0x060474dc      0x368 obj\Debug\app\task_windows\msg_api.o
                0x060474dc                taskmsgFuncRegister
                0x06047594                sysMsgFuncRegister
                0x06047610                app_msgDeal
                0x0604774c                app_msgDealByType
                0x060477e8                app_msgDealByInfor
 .text          0x06047844      0x4bc obj\Debug\app\task_windows\task_api.o
                0x060478ec                app_taskInit
                0x060479bc                app_taskCurId
                0x060479dc                app_taskStart
                0x06047aa8                app_taskChange
                0x06047b24                app_task_rec_Change
                0x06047ba0                app_taskService
 .text          0x06047d00      0x550 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .text          0x06048250     0x2154 obj\Debug\app\task_windows\task_common\src\task_common.o
                0x06048250                task_com_para_init
                0x060482c0                task_com_usbhost_set
                0x06048528                task_com_usb_dev_out
                0x0604856c                task_com_spijpg_Init
                0x06048580                task_com_sdlist_scan
                0x06048594                task_com_sdc_stat_set
                0x0604868c                task_com_lcdbk_set
                0x06048700                task_com_sreen_check
                0x060487bc                task_com_auto_poweroff
                0x06048870                task_com_keysound_play
                0x060488bc                task_com_sound_wait_end
                0x060488f0                task_com_sdc_freesize_check
                0x060489a8                task_com_fs_scan
                0x06048ae4                task_com_sdc_freesize_modify
                0x06048b78                task_com_ir_set
                0x06048bdc                task_com_powerOnTime_str
                0x06048c70                task_com_sensor_res_str
                0x06048d44                task_com_rec_show_time_str
                0x06048e04                task_com_rec_remain_time_str
                0x06048ec8                task_com_play_time_str
                0x060490a8                task_com_tips_show
                0x06049298                task_com_LedPwm_ctrl
                0x060493d0                task_com_LedOnOff_ctrl
                0x06049520                task_com_scaler_str
                0x0604955c                task_com_USB_CS_DM_DP_status_select
                0x0604986c                task_com_service
 .text          0x0604a3a4       0x44 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .text          0x0604a3e8      0xffc obj\Debug\app\task_windows\task_main\src\taskMain.o
                0x0604a550                taskMainWinInit
                0x0604a580                taskMainWinChangeInit
                0x0604a610                taskMainWinChangeUint
                0x0604a6b4                taskMainWinChangeKick
                0x0604a980                taskWinCenterProcess
                0x0604ad80                taskWinHorProcess
                0x0604b098                taskWinVorProcess
                0x0604b3d0                taskWinChangeProcess
 .text          0x0604b3e4      0x3ec obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
                0x0604b784                taskMainCurIdCfg
 .text          0x0604b7d0      0x2c0 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x0604b934                app_taskPlayAudio_start
 .text          0x0604ba90      0xa34 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .text          0x0604c4c4     0x1464 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x0604c6b0                taskPlayVideoThumbnallDrawImage
                0x0604c8b8                taskPlayVideoSlideOpen
                0x0604ca08                taskPlayVideoSlideClose
                0x0604ca60                taskPlayVideoSlidePause
                0x0604caa4                taskPlayVideoSlideStart
                0x0604cbf4                taskPlayVideoMainStart
                0x0604d8cc                taskPlayVideoMainScalerCfg
 .text          0x0604d928     0x158c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .text          0x0604eeb4      0x3c8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .text          0x0604f27c      0xb10 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .text          0x0604fd8c       0x4c obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .text          0x0604fdd8      0x364 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .text          0x0605013c      0x20c obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .text          0x06050348      0x748 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x06050430                taskRecordPhotoRemainCal
                0x06050588                app_taskRecordPhoto_callback
                0x060508c0                taskRecordPhotoProcess
 .text          0x06050a90      0xc40 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .text          0x060516d0      0xcf0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                0x06051eb8                taskRecordvideoTimeCount1S
                0x06051f88                app_taskRecordVideo_caltime
                0x06051fc0                app_taskRecordVideo_Capture
                0x06052174                app_taskRecordVideo_start
                0x06052238                app_taskRecordVideo_stop
 .text          0x060523c0      0xf4c obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .text          0x0605330c       0x20 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .text          0x0605332c       0xd8 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                0x0605332c                taskSdUpdate_uiInit
 .text          0x06053404       0x68 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .text          0x0605346c      0x120 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .text          0x0605358c      0x1bc obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .text          0x06053748      0x1ac obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .text          0x060538f4      0x3a0 obj\Debug\app\task_windows\windows_api.o
                0x06053aec                uiParentDealMsg
                0x06053b1c                uiOpenWindow
                0x06053c64                windowIsOpen
 .text          0x06053c94     0x1488 obj\Debug\app\user_config\src\mbedtls_md5.o
                0x06053c94                mbedtls_md5_init
                0x06053cbc                mbedtls_md5_free
                0x06053cfc                mbedtls_md5_clone
                0x06053d20                mbedtls_md5_starts
                0x06053d70                mbedtls_md5_process
                0x06054d50                mbedtls_md5_update
                0x06054d7c                mbedtls_md5_finish
                0x06054eb8                mbedtls_md5
                0x06054f2c                check_uid_entryption
                0x060550fc                spi_flash_check_md5
 .text          0x0605511c      0x4e8 obj\Debug\app\user_config\src\user_config_api.o
                0x0605511c                user_config_set
                0x06055154                user_config_get
                0x06055190                user_config_save
                0x06055238                userConfig_Reset
                0x060552a0                userConfig_Init
                0x06055388                userConfigInitial
                0x060553a8                user_configValue2Int
                0x06055410                user_config_Language
                0x06055478                user_config_cfgSys
                0x060555c8                user_config_cfgSysAll
 .text          0x06055604        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .text          0x06055604        0x0 ..\lib\libboot.a(boot.o)
 .text          0x06055604        0x0 ..\lib\libboot.a(boot_loader.o)
 .text          0x06055604        0x0 ..\lib\libboot.a(reset.o)
 .text          0x06055604        0x0 ..\lib\libboot.a(boot_lib.o)
 .text          0x06055604        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .text          0x06055604      0x420 ..\lib\libmcu.a(hx330x_auadc.o)
                0x06055604                hx330x_auadcHalfIRQRegister
                0x06055624                hx330x_auadcEndIRQRegister
                0x06055644                hx330x_auadcEnable
                0x060556fc                hx330x_auadcAGCEnable
                0x0605578c                hx330x_auadcGainSet
                0x06055824                hx330x_auadcBufferSet
                0x06055884                hx330x_auadcInit
                0x06055974                hx330x_auadcSetSampleSet
                0x06055a04                hx330x_agc_pwr_get
 .text          0x06055a24     0x125c ..\lib\libmcu.a(hx330x_csi.o)
                0x06055a24                hx330x_CSI_LBFDMAERR_callback
                0x06055aa8                hx330x_CSI_SenSizeErr_callback
                0x06055b10                hx330x_csiIOConfig
                0x06055bac                hx330x_csi_reset
                0x06055bf0                hx330x_csiInit
                0x06055c3c                hx330x_csi_fcnt_mnt
                0x06055c68                hx330x_csiISRRegiser
                0x06055ca4                hx330x_csiOutputSet
                0x06055ce0                hx330x_csiMclkSet
                0x06055dbc                hx330x_csiSyncSet
                0x06055e20                hx330x_csiPrioritySet
                0x06055e5c                hx330x_csiTypeSet
                0x06055ee0                hx330x_csiModeSet
                0x06055f1c                hx330x_csiModeGet
                0x06055f38                hx330x_pclk_digital_fir_Set
                0x06055f9c                hx330x_pclk_analog_Set
                0x06055fec                hx330x_pclk_inv_Set
                0x06056030                hx330x_csi_clk_tun_Set
                0x06056088                hx330x_csiSizeSet
                0x060560c0                hx330x_sen_Image_Size_Set
                0x060560f8                hx330x_csi_in_CropSet
                0x0605617c                hx330x_csiInputAddrSet
                0x06056194                hx330x_csiTestModeSet
                0x060561f4                hx330x_csiDvpClkDivSet
                0x06056234                hx330x_csiINTSet
                0x06056270                hx330x_csiEnable
                0x06056300                hx330x_csiLCDScalerDoneCheck
                0x06056328                hx330x_csiToMjpAddrCfg
                0x06056344                hx330x_csiMJPEGFrameSet
                0x060563c8                hx330x_csiWifiFrameSet
                0x06056420                hx330x_csiLCDFrameSet
                0x0605644c                hx330x_csi_YUVFrameSet
                0x060564ac                hx330x_csiMJPEGScaler
                0x06056630                hx330x_csiMJPEGCrop
                0x06056730                hx330x_csiWifiScaler
                0x060568bc                hx330x_csiSetLsawbtooth
                0x060568f0                hx330x_csiLCDScaler
                0x06056a04                hx330x_csiMJPEGDmaEnable
                0x06056aa4                hx330x_csiWifiDmaEnable
                0x06056b44                hx330x_csiLCDDmaEnable
                0x06056bf8                hx330x_csiLCDDmaKick
                0x06056c3c                hx330x_csi_common_int_set
 .text          0x06056c80      0x4b0 ..\lib\libmcu.a(hx330x_dac.o)
                0x06056cc0                hx330x_dacTypeCfg
                0x06056d04                hx330x_dacSampleRateSet
                0x06056d74                hx330x_dacEnable
                0x06056df8                hx330x_dacVolumeSet
                0x06056e70                hx330x_dacReset
                0x06056e94                hx330x_dacStop
                0x06056ec4                hx330x_dacISRRegister
                0x06056ee4                hx330x_dacHPSet
                0x06056f78                eq_coeff_init
                0x06056fe8                eq_gain_init
                0x06057058                hx330x_dacInit
 .text          0x06057130      0x13c ..\lib\libmcu.a(hx330x_dma.o)
                0x06057130                hx330x_dmaNocCfg
                0x060571a4                hx330x_dmaNocWinA
                0x060571cc                hx330x_dmaNocWinB
                0x060571f4                hx330x_dmaNocWinDis
                0x0605721c                hx330x_dmaChannelEnable
 .text          0x0605726c      0x41c ..\lib\libmcu.a(hx330x_dmauart.o)
                0x0605726c                hx330x_DmaUart_sta_cfg
                0x060572ac                hx330x_DmaUart_con_cfg
                0x060572ec                hx330x_DmaUartIOCfg
                0x06057444                hx330x_DmaUartInit
                0x060574b4                hx330x_DmaUart_CallbackRegister
                0x060574d4                hx330x_dmauart_sendbyte
                0x06057504                hx330x_dmauart_sendDmakick
                0x06057538                hx330x_dmauart_sendDma
                0x060575b4                hx330x_dmauart_recvAutoDmakick
                0x06057600                hx330x_dmauart_recvBytekick
                0x06057634                hx330x_dmauart_rxOutAdrGet
                0x06057650                hx330x_dmauart_rxCntGet
                0x0605766c                hx330x_dmauart_rxFifoOut
 .text          0x06057688      0x8e8 ..\lib\libmcu.a(hx330x_gpio.o)
                0x06057688                hx330x_gpioSFRSet
                0x060578cc                hx330x_gpioSFRGet
                0x06057a88                hx330x_gpioHystersisSet
                0x06057b14                hx330x_GPIO_FUNC
                0x06057b80                hx330x_gpioCommonConfig
                0x06057c14                hx330x_gpioLedInit
                0x06057ca4                hx330x_gpioINTCheck
                0x06057cdc                hx330x_gpioINTClear
                0x06057d00                hx330x_gpioINTInit
                0x06057e1c                hx330x_io1d1_softstart
                0x06057ed8                hx330x_io1d1_pd_enable
 .text          0x06057f70      0xbd0 ..\lib\libmcu.a(hx330x_iic.o)
                0x06058040                soft_iic0_sdaout
                0x0605808c                soft_iic0_sdain
                0x060580d8                soft_iic0_sda_set
                0x0605810c                soft_iic0_scl_set
                0x060581c0                soft_iic0_sda_get
                0x060581f0                soft_iic0_init
                0x06058220                soft_iic0_start
                0x060582a0                soft_iic0_stop
                0x06058320                soft_iic0_wait_ack
                0x060583e8                hx330x_iic0Init
                0x06058490                hx330x_iic0Uninit
                0x060584d4                hx330x_iic0Start
                0x06058514                hx330x_iic0Stop
                0x06058598                hx330x_iic0RecvACK
                0x060585ec                hx330x_iic0SendACK
                0x06058634                hx330x_iic0SendByte
                0x06058754                hx330x_iic0RecvByte
                0x06058884                hx330x_iic1Init
                0x0605891c                hx330x_iic1Uninit
                0x06058994                hx330x_iic1Start
                0x060589b0                hx330x_iic1Stop
                0x06058a10                hx330x_iic1RecvACK
                0x06058a38                hx330x_iic1SendACK
                0x06058a5c                hx330x_iic1SendByte
                0x06058acc                hx330x_iic1RecvByte
 .text          0x06058b40      0x18c ..\lib\libmcu.a(hx330x_int.o)
                0x06058b40                hx330x_int_priority
                0x06058b88                hx330x_intInit
 .text          0x06058ccc     0x15d4 ..\lib\libmcu.a(hx330x_isp.o)
                0x06058ccc                hx330x_isp_mask_tab_cfg
                0x06058d04                hx330x_ispModeSet
                0x06058d40                hx330x_isp_BLC_cfg
                0x06058d8c                hx330x_isp_LSC_cfg
                0x06058dc4                hx330x_isp_DDC_cfg
                0x06058f3c                hx330x_isp_AWB_GAIN_adj
                0x06058f70                hx330x_isp_whtpnt_stat_cfg
                0x060591dc                hx330x_isp_CCM_cfg
                0x060592a0                hx330x_isp_RGB_DGAIN_adj
                0x06059330                hx330x_isp_hist_stat_cfg
                0x060593b8                hx330x_isp_YGAMMA_cfg
                0x060594e8                hx330x_isp_ylog_ygamma_cal
                0x0605952c                hx330x_isp_RGBGAMMA_cfg
                0x060596bc                hx330x_isp_CH_cfg
                0x06059a14                hx330x_isp_VDE_cfg
                0x06059ae8                hx330x_isp_EE_cfg
                0x06059eb4                hx330x_isp_CCF_cfg
                0x0605a000                hx330x_isp_SAJ_cfg
                0x0605a118                hx330x_isp_kick_stat
                0x0605a148                hx330x_isp_stat_en
                0x0605a18c                hx330x_isp_stat_cp_kick_st
                0x0605a1b0                hx330x_isp_stat_cp_done
                0x0605a1e0                hx330x_isp_model_cfg
 .text          0x0605a2a0        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .text          0x0605a2a0     0x19b8 ..\lib\libmcu.a(hx330x_jpg.o)
                0x0605a2a0                hx330x_mjpA_EncodeISRRegister
                0x0605a2c0                hx330x_MJPA_EncodeLcdPreRegister
                0x0605a2e0                hx330x_MJPA_EncodeLcdPre_Func_call
                0x0605a314                hx330x_MJPA_EncodeLcdPre_Func_Check
                0x0605a340                hx330x_MJPA_EncodeLcdBackRegister
                0x0605a360                hx330x_MJPA_EncodeLcdBack_Func_call
                0x0605a394                hx330x_MJPA_EncodeLcdBack_Func_Check
                0x0605a3c0                hx330x_mjpB_EncodeISRRegister
                0x0605a3e0                hx330x_mjpA_isr_check
                0x0605a40c                hx330x_mjpB_DecodeISRRegister
                0x0605a42c                hx330x_mjpB_Encode_StartFunc_Check
                0x0605a458                hx330x_mjpB_Encode_StartFunc_Reg
                0x0605a478                hx330x_mjpB_Encode_StartFunc_call
                0x0605a4ac                hx330x_mjpA_Encode_StartFunc_Check
                0x0605a4d8                hx330x_mjpA_Encode_StartFunc_Reg
                0x0605a4f8                hx330x_mjpA_Encode_StartFunc_call
                0x0605a52c                hx330x_mjpA_reset
                0x0605a570                hx330x_mjpB_reset
                0x0605a5c0                hx330x_mjpA_EncodeSizeSet
                0x0605a668                hx330x_mjpA_EncodeSizeSet2
                0x0605a7f0                hx330x_mjpA_EncodeQuilitySet
                0x0605a830                hx330x_mjpA_EncodeInfoSet
                0x0605a870                hx330x_mjpA_EncodeBufferSet
                0x0605a8bc                hx330x_mjpA_Encode_inlinebuf_init
                0x0605a914                hx330x_mjpA_Encode_manual_on
                0x0605a974                hx330x_mjpA_Encode_manual_stop
                0x0605a9a0                hx330x_mjpA_EncodeEnable
                0x0605aa1c                hx330x_mjpA_EncodeQadj
                0x0605aae4                hx330x_mjpA_EncodeInit
                0x0605ab8c                hx330x_mjpA_EncodeDriModeSet
                0x0605abf4                hx330x_cal_jASize
                0x0605ac24                hx330x_mjpA_Encode_check
                0x0605adc0                hx330x_mjpA_Flag_Clr
                0x0605add8                hx330x_mjpB_EncodeQuilitySet
                0x0605ae18                hx330x_mjpB_EncodeQadj
                0x0605aee0                hx330x_mjpB_Encodeinit
                0x0605b064                hx330x_cal_jBSize
                0x0605b094                hx330x_mjpB_Encode_inlinebuf_init
                0x0605b0c0                hx330x_mjpB_Encode_output_init
                0x0605b0ec                hx330x_mjpB_Encode_manual_stop
                0x0605b154                hx330x_mjpB_Encode_manual_start
                0x0605b1a0                hx330x_mjpB_EncodeLoadAddrGet
                0x0605b1bc                hx330x_mjpB_as_Encode
                0x0605b1e8                hx330x_mjpB_DecodeScalerCal
                0x0605b2b8                hx330x_mjpB_DecodeSetSize
                0x0605b4ec                hx330x_mjpB_DecodeOutputSet
                0x0605b508                hx330x_mjpB_DecodeInputSet
                0x0605b54c                hx330x_mjpB_DecodeInputResume
                0x0605b584                hx330x_mjpB_DecodeDriSet
                0x0605b59c                hx330x_mjpB_DecodeCompressSet
                0x0605b5b4                hx330x_mjpB_DecodeInitTable
                0x0605b5e4                hx330x_mjpB_yuvfmt_set
                0x0605b630                hx330x_mjpB_DecodeInit
                0x0605b788                hx330x_mjpB_DecodeDCTimeSet
                0x0605b838                hx330x_mjpB_DecodeEnable
                0x0605b8a8                hx330x_mjpB_DecodeKick
                0x0605b8f0                hx330x_mjpB_DecodeStop
                0x0605b92c                hx330x_mjpB_DecodeQDTCfg
                0x0605b980                hx330x_mjpB_DecodeBusyCheck
                0x0605b9b4                hx330x_mjpB_DecodeCheck
                0x0605b9e4                hx330x_mjpB_DecodeODma1Cfg
                0x0605ba94                hx330x_mjpB_DecodePacket_check
                0x0605bb58                hx330x_mjpB_Decode_InResume
                0x0605bb7c                hx330x_mjpB_Decode_check
 .text          0x0605bc58      0x1c8 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                0x0605bc58                hx330x_mjpA_table_init
                0x0605bd30                hx330x_mjpB_table_init
 .text          0x0605be20     0x10a0 ..\lib\libmcu.a(hx330x_lcd.o)
                0x0605be20                hx330x_lcdReset
                0x0605be34                hx330x_lcdSPIMode
                0x0605be60                hx330x_lcdSPIInit
                0x0605bf80                hx330x_lcdSPIUninit
                0x0605bfdc                hx330x_lcdSPISendData
                0x0605c150                hx330x_lcdMcuSendCmd
                0x0605c1c4                hx330x_lcdMcuSendData
                0x0605c220                hx330x_lcdMcuSendCmd16
                0x0605c2b4                hx330x_lcdMcuSendData16
                0x0605c348                hx330x_lcdInit
                0x0605c394                hx330x_lcdIRQEnable
                0x0605c3dc                hx330x_lcdPreLineSet
                0x0605c3f8                hx330x_lcdSignalSet
                0x0605c490                hx330x_lcdBusWidth
                0x0605c4e4                hx330x_lcdBusEnable
                0x0605c534                hx330x_lcdClkSet
                0x0605c550                hx330x_lcdSyncSet
                0x0605c574                hx330x_lcdDESignalSet
                0x0605c598                hx330x_lcdPositionSet
                0x0605c5b4                hx330x_lcdResolutionSet
                0x0605c5d0                hx330x_lcdWindowSizeSet
                0x0605c5ec                hx330x_lcdDataModeSet
                0x0605c630                hx330x_lcdClkNumberSet
                0x0605c648                hx330x_lcdEndLineSet
                0x0605c664                hx330x_lcdPanelMode
                0x0605c6ac                hx330x_lcdEnable
                0x0605c710                hx330x_lcdTeMode
                0x0605c758                hx330x_lcdTeCheck
                0x0605c7d4                hx330x_lcdISRRegister
                0x0605c818                hx330x_lcdRGBTimimgInit
                0x0605ca68                hx330x_lcdMCUTimimgInit
                0x0605cc3c                hx330x_lcdRGBIOConfig
                0x0605cd9c                hx330x_lcdMCUIOConfig
 .text          0x0605cec0      0x238 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                0x0605cec0                hx330x_rotateISRRegiser
                0x0605cee0                hx330x_rotateCheckBusy
                0x0605cf00                hx330x_rotateReset
                0x0605cf44                hx330x_rotateStart
                0x0605d064                hx330x_rotateWaitFrameDone
                0x0605d0c8                hx330x_rotateGetSrcYAddr
 .text          0x0605d0f8      0x874 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x0605d0f8                hx330x_checkLcdShowStatus
                0x0605d11c                hx330x_lcdShowISRRegister
                0x0605d13c                hx330x_lcdshowInit
                0x0605d1b8                hx330x_lcdshowSetCritical
                0x0605d1fc                hx330x_lcdSetVideoBgColor
                0x0605d230                hx330x_lcdSetVideoBSC
                0x0605d27c                hx330x_lcdSetVideoBrightness
                0x0605d2d4                hx330x_lcdVideoSetRgbWidth
                0x0605d340                hx330x_lcdVideoSetScalePara
                0x0605d38c                hx330x_lcdVideoSetScaleLine
                0x0605d3f4                hx330x_lcdVideoGetYAddr
                0x0605d424                hx330x_lcdVideoGetUVAddr
                0x0605d454                hx330x_lcdVideoSetSize
                0x0605d48c                hx330x_lcdvideoMemcpy
                0x0605d4ec                hx330x_lcdvideoEnable
                0x0605d530                hx330x_lcdVideoSetGAMA
                0x0605d5dc                hx330x_lcdVideo_CCM_cfg
                0x0605d6c4                hx330x_lcdVideo_SAJ_cfg
                0x0605d730                hx330x_lcdvideoGammaEnable
                0x0605d774                hx330x_lcdUiSetSize
                0x0605d7c0                hx330x_lcdUiSetPosition
                0x0605d804                hx330x_lcdUiSetPalette
                0x0605d868                hx330x_UiGetAddr
                0x0605d8b8                hx330x_lcdUiSetAlpha
                0x0605d91c                hx330x_lcdUiLzoSoftCreate
 .text          0x0605d96c      0x32c ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                0x0605d96c                hx330x_uiLzoISRRegiser
                0x0605d98c                hx330x_uiLzoCheckBusy
                0x0605d9b0                hx330x_uiLzoReset
                0x0605d9f4                hx330x_uiLzoGetOutSize
                0x0605da14                hx330x_uiLzoWaiDone
                0x0605dabc                hx330x_uiLzoStart
                0x0605dbbc                hx330x_uiLzoKick
 .text          0x0605dc98      0x14c ..\lib\libmcu.a(hx330x_lcdwin.o)
                0x0605dc98                hx330x_lcdWinABConfig
                0x0605dd14                hx330x_lcdWinABEnable
                0x0605dd58                hx330x_lcdWinReset
                0x0605dd98                hx330x_lcdWinGetTopLayer
                0x0605ddbc                hx330x_lcdWinGetBotLayer
 .text          0x0605dde4      0x118 ..\lib\libmcu.a(hx330x_md.o)
                0x0605dde4                hx330x_mdEnable
                0x0605de4c                hx330x_mdEnable_check
                0x0605de6c                hx330x_mdInit
                0x0605deb4                hx330x_mdXPos
                0x0605ded8                hx330x_mdYPos
 .text          0x0605defc      0x304 ..\lib\libmcu.a(hx330x_mipi.o)
                0x0605defc                hx330x_mipiClkCfg
                0x0605df50                hx330x_MipiCSIUinit
                0x0605dfb0                hx330x_MipiCSIInit
 .text          0x0605e200      0x860 ..\lib\libmcu.a(hx330x_misc.o)
                0x0605e200                hx330x_sin
                0x0605e248                hx330x_cos
                0x0605e26c                hx330x_abs
                0x0605e28c                hx330x_dif_abs
                0x0605e2b0                hx330x_max
                0x0605e2d4                hx330x_clip
                0x0605e304                hx330x_str_cpy
                0x0605e368                hx330x_str_ncpy
                0x0605e3e0                hx330x_str_char
                0x0605e428                hx330x_str_cmp
                0x0605e4ac                hx330x_str_ncmp
                0x0605e554                hx330x_str_len
                0x0605e590                hx330x_str_cat
                0x0605e600                hx330x_str_seek
                0x0605e66c                hx330x_strTransform
                0x0605e6c0                hx330x_dec_num2str
                0x0605e700                hx330x_char2hex
                0x0605e754                hx330x_str2num
                0x0605e7ac                hx330x_hex2str
                0x0605e824                hx330x_num2str
                0x0605e888                hx330x_num2str_cnt
                0x0605e914                hx330x_CountToString
                0x0605ea00                hx330x_str_noftcpy
                0x0605ea28                hx330x_greatest_divisor
 .text          0x0605ea60      0xd00 ..\lib\libmcu.a(hx330x_rtc.o)
                0x0605ea60                hx330x_rtcRamRead
                0x0605eafc                hx330x_rtcRamWrite
                0x0605eb98                hx330x_rtcSecondTrim
                0x0605ed2c                hx330x_rtcInit
                0x0605ef7c                hx330x_rtc128K_div_cfg
                0x0605f008                hx330x_rtc128K_trim
                0x0605f0b8                hx330x_rtcSencodEnable
                0x0605f134                hx330x_rtcAlamEnable
                0x0605f21c                hx330x_rtcAlamSet
                0x0605f264                hx330x_rtcGet
                0x0605f2b8                hx330x_rtc_alarm_weakup_reset
                0x0605f340                hx330x_rtcSet
                0x0605f388                hx330x_VDDGSENEnable
                0x0605f3f8                hx330x_WKI0InputEnable
                0x0605f468                hx330x_WKI1InputEnable
                0x0605f4d8                hx330x_WKI1Read
                0x0605f4fc                hx330x_WKI0Read
                0x0605f520                hx330x_WKI0WakeupEnable
                0x0605f660                hx330x_rtcBatDecEnable
                0x0605f6d0                hx330x_rtcSenHVEnable
                0x0605f740                hx330x_rtcAlarmWakeUpFlag
 .text          0x0605f760      0x788 ..\lib\libmcu.a(hx330x_sd.o)
                0x0605f760                hx330x_sd0Init
                0x0605f8b4                hx330x_sd0Uninit
                0x0605f988                hx330x_sd0BusSet
                0x0605f9cc                hx330x_sd0Buffer
                0x0605f9ec                hx330x_sd0ClkSet
                0x0605fa84                hx330x_sd1Init
                0x0605fc2c                hx330x_sd1Uninit
                0x0605fd30                hx330x_sd1BusSet
                0x0605fd74                hx330x_sd1WaitDAT0
                0x0605fe14                hx330x_sd1GetRsp
                0x0605fe30                hx330x_sd1Buffer
                0x0605fe50                hx330x_sd1ClkSet
 .text          0x0605fee8        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .text          0x0605fee8      0x4ac ..\lib\libmcu.a(hx330x_spi1.o)
                0x0605fee8                hx330x_spi1_pin_config
                0x0605ff68                hx330x_spi1_CS_Config
                0x0605ffc4                hx330x_spi1Init
                0x06060074                hx330x_spi1DMAIRQ_CallbackRegister
                0x06060094                hx330x_spi1SendByte
                0x06060114                hx330x_spi1RecvByte
                0x0606015c                hx330x_sp1RecvDmaKick
                0x060601b4                hx330x_spi1DmaDoneCheck
                0x060601d8                hx330x_sp1SendDmaKick
                0x06060234                hx330x_sp1RecvDma
                0x060602d0                hx330x_sp1SendDma
                0x06060350                hx330x_sp1Enable
 .text          0x06060394      0x8bc ..\lib\libmcu.a(hx330x_sys.o)
                0x06060394                hx330x_word_memcpy
                0x06060404                hx330x_halfword_memcpy
                0x06060474                hx330x_word_memset
                0x060604d0                hx330x_mtsfr_memcpy
                0x06060534                hx330x_mfsfr_memcpy
                0x06060598                table_init_data
                0x06060618                hx330x_sysDcacheInit
                0x0606065c                hx330x_sysIcacheInit
                0x060606a0                hx330x_sysDcacheInvalid
                0x06060738                hx330x_sysSRAMClear
                0x0606078c                hx330x_sysBSSClear
                0x060607f0                hx330x_sysLDOSet
                0x060609a0                hx330x_sysReset
                0x060609e0                hx330x_mcpy0_llp
                0x06060a70                hx330x_sysInit
                0x06060b9c                hx330x_sysUninit
 .text          0x06060c50      0x658 ..\lib\libmcu.a(hx330x_timer.o)
                0x06060c50                hx330x_timerISRRegister
                0x06060c94                hx330x_timerStart
                0x06060de0                hx330x_timerStop
                0x06060eb0                hx330x_timerEnable
                0x06060f3c                hx330x_timerDisable
                0x06060fe0                hx330x_timerPWMStart
 .text          0x060612a8      0x444 ..\lib\libmcu.a(hx330x_tminf.o)
                0x060612a8                hx330x_mjpA_TimeinfoEnable
                0x06061300                hx330x_mjpB_TimeinfoEnable
                0x0606136c                hx330x_mjpA_TimeinfoColor
                0x060613b0                hx330x_mjpB_TimeinfoColor
                0x06061404                hx330x_mjpA_TimeinfoSize
                0x06061460                hx330x_mjpB_TimeinfoSize
                0x060614e8                hx330x_mjpA_TimeinfoPos
                0x06061544                hx330x_mjpB_TimeinfoPos
                0x060615cc                hx330x_mjpA_TimeinfoAddr
                0x06061610                hx330x_mjpB_TimeinfoAddr
                0x06061680                hx330x_recfg_mjpb_tminf
 .text          0x060616ec      0x224 ..\lib\libmcu.a(hx330x_uart.o)
                0x060616ec                hx330x_uart0IOCfg
                0x06061870                hx330x_uart0Init
 .text          0x06061910      0xe10 ..\lib\libmcu.a(hx330x_usb.o)
                0x06061910                hx330x_usb20_CallbackInit
                0x06061940                hx330x_usb20_CallbackRegister
                0x0606197c                hx330x_usb20_eptx_register
                0x06061a14                hx330x_usb20_eprx_register
                0x06061aa4                hx330x_USB20_EPTX_Flush
                0x06061ad0                hx330x_usb20_HighSpeed
                0x06061afc                hx330x_iso20_tx
                0x06061c54                hx330x_iso20_tx_kick
                0x06061d10                hx330x_usb20_dev_reset
                0x06061d84                hx330x_usb20_host_speed_connect
                0x06061e24                hx330x_usb20_host_reset
                0x06061e8c                hx330x_usb20_dev_init
                0x06061fe8                hx330x_usb20_host_init
                0x060621cc                hx330x_usb20_uinit
                0x06062244                get_u16softcnt
                0x06062264                hx330x_usb11_CallbackInit
                0x06062294                hx330x_usb11_CallbackRegister
                0x060622d0                hx330x_usb11_host_init
                0x060623c4                hx330x_usb11_host_eprx_register
                0x06062470                hx330x_usb11_host_reset
                0x060624c8                hx330x_usb11_host_speed_connect
                0x06062510                hx330x_usb11_uinit
                0x060625d8                hx330x_usb20_dev_check_init
                0x060625f4                hx330x_usb20_host_check_init
                0x06062610                hx330x_usb11_host_check_init
                0x0606262c                hx330x_usb20_device_check
                0x06062660                hx330x_usb20_host_check
                0x060626f4                hx330x_usb11_host_check
 .text          0x06062720       0x14 ..\lib\libmcu.a(hx330x_wdt.o)
                0x06062720                hx330x_wdtTimeSet
 .text          0x06062734      0x184 ..\lib\libmcu.a(hx330x_emi.o)
                0x06062734                hx330x_emiInit
                0x060627e4                hx330x_emiISRRegister
                0x06062818                hx330x_emiKick
                0x06062874                hx330x_emiCheckBusy
                0x06062894                hx330x_emiCheckRXError
 .text          0x060628b8     0x1c34 ..\lib\libisp.a(hal_isp.o)
                0x06062be0                hal_sensor_fps_adpt
                0x06062cc0                hal_isp_process
                0x06063c24                hal_sensor_awb_scene_set
                0x06063d14                hal_sensor_EV_set
                0x06063e2c                hal_isp_br_get
                0x06063e4c                hal_ispService
                0x06063f2c                hal_isp_init
                0x060642cc                hal_SensorRegister
                0x06064364                hal_SensorApiGet
                0x06064384                hal_SensorResolutionGet
                0x060643e0                hal_SensorRotate
                0x0606444c                hal_isplog_cnt
                0x0606449c                hal_isp_cur_yloga
 .text          0x060644ec      0xd64 ..\lib\libjpg.a(hal_jpg.o)
                0x0606461c                hal_mjp_enle_init
                0x0606464c                hal_mjp_enle_unit
                0x060646f0                hal_mjp_enle_check
                0x06064ca0                hal_mjp_enle_manual_kickstart
                0x06064e50                hal_mjp_enle_buf_mdf
                0x06064fc4                hal_mjp_enle_manual_done
 .text          0x06065250      0xe58 ..\lib\liblcd.a(hal_lcd.o)
                0x060652a4                hal_lcdWinUpdata
                0x060653a0                hal_lcdVideoRotateUpdata
                0x060654e8                hal_lcdCsiShowStop
                0x06065568                hal_lcdVideoShowFrameGet
                0x060655d8                hal_lcdVideoIdleFrameMalloc
                0x0606561c                hal_lcdCsiShowStart
                0x06065784                hal_lcdVideoSetFrameWait
                0x060657c8                hal_lcdVideoSetFrame
                0x060658ac                hal_lcdBrightnessGet
                0x060658dc                hal_lcdRegister
                0x06065fa4                hal_lcdFrameEndCallBackRegister
                0x06065fec                hal_lcd_send_cmd
 .text          0x060660a8      0xe88 ..\lib\liblcd.a(hal_lcdMem.o)
                0x06066178                hal_lcdframes_vr_init
                0x060662f8                hal_lcdframes_vd_init
                0x06066488                hal_dispframePrintf
                0x060664d8                hal_dispframeInit
                0x06066c68                hal_dispframeUinit
                0x06066cf4                hal_dispframeMalloc
                0x06066dbc                hal_lcdVideoFrameFlush
 .text          0x06066f30      0x384 ..\lib\liblcd.a(hal_lcdrotate.o)
                0x060670b8                hal_rotateInit
                0x0606710c                hal_rotateAdd
 .text          0x060672b4      0x718 ..\lib\liblcd.a(hal_lcdUi.o)
                0x060672b4                hal_uiRotateBufMalloc
                0x060672d8                hal_uiLzoBufMalloc
                0x060672fc                hal_uiDrawBufMalloc
                0x0606736c                hal_lcdUiKickWait
                0x060673c8                hal_lcdUiKick
                0x06067454                hal_lcdUiSetAddr
                0x060674c4                hal_lcdUiSetBuffer
                0x06067558                hal_lcdUiSetBufferWaitDone
                0x060675ac                hal_lcdUiInit
                0x06067748                hal_lcdUiSetPosition
                0x060677c4                hal_lcdUiSetPalette
                0x06067818                hal_lcdUiSetSize
                0x06067894                hal_lcdUiSetAlpha
                0x06067910                hal_lcdUiResolutionGet
                0x0606797c                hal_lcdVideo_CCM_cfg
                0x060679a4                hal_lcdVideo_SAJ_cfg
 .text          0x060679cc      0x1a8 ..\lib\liblcd.a(hal_lcdUiLzo.o)
                0x06067a68                hal_uiLzokick
                0x06067b4c                hal_uiLzoInit
 .text          0x06067b74      0x1ec ..\lib\liblcd.a(lcd_tab.o)
                0x06067b74                hal_lcdParaLoad
                0x06067c38                hal_lcdSetLsawbtooth
                0x06067cec                hal_lcdPQToolGetInfo
 .text          0x06067d60      0xa70 ..\lib\libmultimedia.a(api_multimedia.o)
                0x06067f18                api_multimedia_cache_init
                0x06068014                api_multimedia_cachePreRead
                0x060680a4                api_multimedia_cacheRead
                0x06068120                api_multimedia_init
                0x06068270                api_multimedia_uninit
                0x060682f4                api_multimedia_start
                0x06068370                api_multimedia_end
                0x060683ec                api_multimedia_service
                0x06068468                api_multimedia_addjunk
                0x060684e4                api_multimedia_encodeframe
                0x06068564                api_multimedia_decodeframe
                0x060685e0                api_multimedia_gettime
                0x0606865c                api_multimedia_getsta
                0x060686d8                api_multimedia_decodefast
                0x06068754                api_multimedia_getArg
 .text          0x060687d0     0x1440 ..\lib\libmultimedia.a(avi_dec.o)
 .text          0x06069c10      0xed4 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .text          0x0606aae4      0xaa4 ..\lib\libmultimedia.a(avi_std_enc.o)
                0x0606b13c                avi_stdEncidxEnd
                0x0606b230                avi_stdEncEnd
 .text          0x0606b588      0x958 ..\lib\libmultimedia.a(wav_dec.o)
 .text          0x0606bee0      0x414 ..\lib\libmultimedia.a(wav_enc.o)
                0x0606bee0                wav_EncEnd
 .text          0x0606c2f4      0x1c8 ..\lib\libmultimedia.a(wav_pcm.o)
                0x0606c2f4                pcm_encode
                0x0606c3a4                pcm_decode
 .text          0x0606c4bc       0xb0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                0x0606c4bc                memcmp
 .text          0x0606c56c      0x158 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                0x0606c56c                memcpy
 .text          0x0606c6c4      0x15c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                0x0606c6c4                memset
 .text          0x0606c820       0xc0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                0x0606c820                strcpy
 .text          0x0606c8e0      0x7c8 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                0x0606c8e0                __udivdi3
 .text          0x0606d0a8      0x7c8 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                0x0606d0a8                __umoddi3
 .text          0x0606d870       0xf4 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
                0x0606d870                __udivsi3
                0x0606d870                __udivsi3_internal
 .text          0x0606d964       0x1c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
                0x0606d964                __umodsi3
 .text          0x0606d980        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
 *(.rodata*)
 .rodata.str1.1
                0x0606d980       0x15 obj\Debug\dev\battery\src\battery_api.o
 *fill*         0x0606d995        0x3 
 .rodata        0x0606d998        0xc obj\Debug\dev\battery\src\battery_api.o
 .rodata.str1.1
                0x0606d9a4       0x49 obj\Debug\dev\dev_api.o
 .rodata.str1.1
                0x0606d9ed       0x65 obj\Debug\dev\fs\src\ff.o
                                 0x69 (size before relaxing)
 *fill*         0x0606da52        0x2 
 .rodata.cst4   0x0606da54        0x4 obj\Debug\dev\fs\src\ff.o
 .rodata        0x0606da58       0xb0 obj\Debug\dev\fs\src\ff.o
 .rodata        0x0606db08      0x3d4 obj\Debug\dev\fs\src\ffunicode.o
 .rodata.str1.1
                0x0606dedc       0xb7 obj\Debug\dev\fs\src\fs_api.o
                                 0xb8 (size before relaxing)
 .rodata.str1.1
                0x0606df93       0x7a obj\Debug\dev\gsensor\src\gsensor_api.o
 *fill*         0x0606e00d        0x3 
 .rodata        0x0606e010        0xc obj\Debug\dev\gsensor\src\gsensor_api.o
 .rodata        0x0606e01c       0x38 obj\Debug\dev\gsensor\src\gsensor_da380.o
                0x0606e034                da380
 .rodata        0x0606e054       0x38 obj\Debug\dev\gsensor\src\gsensor_gma301.o
                0x0606e06c                gma301
 .rodata        0x0606e08c       0x38 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
                0x0606e0a4                sc7a30e
 .rodata.str1.1
                0x0606e0c4       0x16 obj\Debug\dev\lcd\src\lcd_api.o
 .rodata.str1.1
                0x0606e0da       0x1d obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .rodata.str1.1
                0x0606e0f7       0x6a obj\Debug\dev\nvfs\src\nvfs_api.o
 .rodata.str1.1
                0x0606e161       0x8b obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .rodata.str1.1
                0x0606e1ec      0x143 obj\Debug\dev\sd\src\sd_api.o
 *fill*         0x0606e32f        0x1 
 .rodata        0x0606e330      0x160 obj\Debug\dev\sd\src\sd_api.o
                0x0606e330                sd_ident_tab
 .rodata.str1.1
                0x0606e490       0xc3 obj\Debug\dev\sensor\src\sensor_api.o
 *fill*         0x0606e553        0x1 
 .rodata        0x0606e554       0x44 obj\Debug\dev\sensor\src\sensor_api.o
                0x0606e554                user_ccf_dn_tab
                0x0606e560                user_ee_dn_tab
                0x0606e57c                user_ee_sharp_tab
 .rodata        0x0606e598      0x758 obj\Debug\dev\sensor\src\sensor_tab.o
                0x0606e598                test_img_init
                0x0606e5dc                test_img_adpt
 .rodata.str1.1
                0x0606ecf0       0x57 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .rodata.str1.1
                0x0606ed47       0x47 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 *fill*         0x0606ed8e        0x2 
 .rodata        0x0606ed90       0x1c obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                0x0606ed90                tp_icnt81
 .rodata.str1.1
                0x0606edac       0xa8 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .rodata.str1.1
                0x0606ee54       0x13 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                                 0x17 (size before relaxing)
 *fill*         0x0606ee67        0x1 
 .rodata        0x0606ee68       0x2c obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                0x0606ee78                tp_ns2009
 .rodata.str1.1
                0x0606ee94       0x50 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .rodata        0x0606eee4      0x100 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x0606ef30                StringDescTbl
                0x0606ef48                DEV_QAULIFIER_DESC_DATA
                0x0606ef54                UsbStrDescSerialNumber
                0x0606ef74                UsbStrDescProduct_1
                0x0606ef8c                UsbStrDescProduct_0
                0x0606efa4                UsbStrDescManufacturer
                0x0606efb4                UsbLanguageID
                0x0606efb8                dusb_com_devdsc
                0x0606efcc                dusb_msc_devdsc
                0x0606efe0                SDK_CHIP_INF
 .rodata.str1.1
                0x0606efe4       0x4d obj\Debug\dev\usb\dusb\src\dusb_msc.o
 *fill*         0x0606f031        0x3 
 .rodata        0x0606f034       0x40 obj\Debug\dev\usb\dusb\src\dusb_msc.o
                0x0606f034                MscSenseCode
                0x0606f040                device_inquiry_data
 .rodata.str1.1
                0x0606f074        0xc obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .rodata        0x0606f080        0x4 obj\Debug\dev\usb\dusb\src\dusb_uac.o
                0x0606f080                uac_vol_tbl
 .rodata        0x0606f084      0x1e0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                0x0606f098                uvc_ctl_tab
                0x0606f1d8                unit_callback
                0x0606f228                vc_still_probe_commit_desc
                0x0606f234                vc_probe_commit_desc
                0x0606f250                uvc_res_tab
 .rodata.str1.1
                0x0606f264       0x69 obj\Debug\dev\usb\husb\src\husb_api.o
 .rodata.str1.1
                0x0606f2cd      0x5e4 obj\Debug\dev\usb\husb\src\husb_enum.o
                                0x5e6 (size before relaxing)
 *fill*         0x0606f8b1        0x3 
 .rodata        0x0606f8b4      0x2c0 obj\Debug\dev\usb\husb\src\husb_enum.o
                0x0606f8e4                husb_uvcunit_set_hcdtrb
                0x0606f904                husb_uvcunit_get_hcdtrb
                0x0606f924                husb_astern_hcdtrb
                0x0606f944                husb_uvc_switch_hcdtrb
                0x0606f9c4                husb_enum_hcdtrb
 .rodata.str1.1
                0x0606fb74       0xa8 obj\Debug\dev\usb\husb\src\husb_hub.o
 .rodata.str1.1
                0x0606fc1c      0x271 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .rodata.str1.1
                0x0606fe8d       0x17 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .rodata.str1.1
                0x0606fea4      0x17b obj\Debug\dev\usb\husb\src\husb_uvc.o
                                0x198 (size before relaxing)
 .rodata.str1.1
                0x0607001f       0xc1 obj\Debug\hal\src\hal_auadc.o
 .rodata.str1.1
                0x060700e0       0x8c obj\Debug\hal\src\hal_csi.o
 .rodata        0x0607016c       0x2c obj\Debug\hal\src\hal_dac.o
 .rodata.str1.1
                0x06070198       0x6b obj\Debug\hal\src\hal_dmauart.o
 .rodata.str1.1
                0x06070203       0x8d obj\Debug\hal\src\hal_lcdshow.o
                                 0x93 (size before relaxing)
 .rodata        0x06070290       0x14 obj\Debug\hal\src\hal_lcdshow.o
 .rodata.str1.1
                0x060702a4      0x193 obj\Debug\hal\src\hal_mjpAEncode.o
                                0x195 (size before relaxing)
 *fill*         0x06070437        0x1 
 .rodata        0x06070438        0xc obj\Debug\hal\src\hal_mjpAEncode.o
                0x06070438                mjpegQualityTable
 .rodata.str1.1
                0x06070444       0xbb obj\Debug\hal\src\hal_mjpBEncode.o
 .rodata.str1.1
                0x060704ff       0x47 obj\Debug\hal\src\hal_mjpDecode.o
 *fill*         0x06070546        0x2 
 .rodata        0x06070548        0x8 obj\Debug\hal\src\hal_mjpDecode.o
 .rodata.str1.1
                0x06070550        0xa obj\Debug\hal\src\hal_rtc.o
 *fill*         0x0607055a        0x2 
 .rodata        0x0607055c        0xc obj\Debug\hal\src\hal_rtc.o
                0x0607055c                monDaysTable
 .rodata.str1.1
                0x06070568       0xdf obj\Debug\hal\src\hal_spi1.o
 .rodata.str1.1
                0x06070647       0xc7 obj\Debug\hal\src\hal_sys.o
 .rodata.str1.1
                0x0607070e       0x14 obj\Debug\hal\src\hal_timer.o
 .rodata.str1.1
                0x06070722       0x3a obj\Debug\hal\src\hal_watermark.o
 .rodata.str1.1
                0x0607075c      0x116 obj\Debug\multimedia\audio\audio_playback.o
 .rodata.str1.1
                0x06070872       0x98 obj\Debug\multimedia\audio\audio_record.o
 .rodata.str1.1
                0x0607090a       0xc5 obj\Debug\multimedia\image\image_decode.o
 .rodata.str1.1
                0x060709cf      0x1a4 obj\Debug\multimedia\image\image_encode.o
 *fill*         0x06070b73        0x1 
 .rodata        0x06070b74      0x2dc obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                0x06070b74                exif_head
 .rodata.str1.1
                0x06070e50      0x323 obj\Debug\multimedia\video\video_playback.o
                                0x327 (size before relaxing)
 *fill*         0x06071173        0x1 
 .rodata        0x06071174        0x8 obj\Debug\multimedia\video\video_playback.o
 .rodata.str1.1
                0x0607117c      0x31e obj\Debug\multimedia\video\video_record.o
 .rodata.str1.1
                0x0607149a      0x1a6 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .rodata.str1.1
                0x06071640       0x95 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                                 0xb1 (size before relaxing)
 *fill*         0x060716d5        0x3 
 .rodata        0x060716d8      0xeb0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
                0x060716d8                ascii_num1_table
                0x06071850                ascii_num1_126
                0x06071874                ascii_num1_125
                0x06071898                ascii_num1_124
                0x060718bc                ascii_num1_123
                0x060718e0                ascii_num1_122
                0x06071904                ascii_num1_121
                0x06071928                ascii_num1_120
                0x0607194c                ascii_num1_119
                0x06071970                ascii_num1_118
                0x06071994                ascii_num1_117
                0x060719b8                ascii_num1_116
                0x060719dc                ascii_num1_115
                0x06071a00                ascii_num1_114
                0x06071a24                ascii_num1_113
                0x06071a48                ascii_num1_112
                0x06071a6c                ascii_num1_111
                0x06071a90                ascii_num1_110
                0x06071ab4                ascii_num1_109
                0x06071ad8                ascii_num1_108
                0x06071afc                ascii_num1_107
                0x06071b20                ascii_num1_106
                0x06071b44                ascii_num1_105
                0x06071b68                ascii_num1_104
                0x06071b8c                ascii_num1_103
                0x06071bb0                ascii_num1_102
                0x06071bd4                ascii_num1_101
                0x06071bf8                ascii_num1_100
                0x06071c1c                ascii_num1_99
                0x06071c40                ascii_num1_98
                0x06071c64                ascii_num1_97
                0x06071c88                ascii_num1_96
                0x06071cac                ascii_num1_95
                0x06071cd0                ascii_num1_94
                0x06071cf4                ascii_num1_93
                0x06071d18                ascii_num1_92
                0x06071d3c                ascii_num1_91
                0x06071d60                ascii_num1_90
                0x06071d84                ascii_num1_89
                0x06071da8                ascii_num1_88
                0x06071dcc                ascii_num1_87
                0x06071df0                ascii_num1_86
                0x06071e14                ascii_num1_85
                0x06071e38                ascii_num1_84
                0x06071e5c                ascii_num1_83
                0x06071e80                ascii_num1_82
                0x06071ea4                ascii_num1_81
                0x06071ec8                ascii_num1_80
                0x06071eec                ascii_num1_79
                0x06071f10                ascii_num1_78
                0x06071f34                ascii_num1_77
                0x06071f58                ascii_num1_76
                0x06071f7c                ascii_num1_75
                0x06071fa0                ascii_num1_74
                0x06071fc4                ascii_num1_73
                0x06071fe8                ascii_num1_72
                0x0607200c                ascii_num1_71
                0x06072030                ascii_num1_70
                0x06072054                ascii_num1_69
                0x06072078                ascii_num1_68
                0x0607209c                ascii_num1_67
                0x060720c0                ascii_num1_66
                0x060720e4                ascii_num1_65
                0x06072108                ascii_num1_64
                0x0607212c                ascii_num1_63
                0x06072150                ascii_num1_62
                0x06072174                ascii_num1_61
                0x06072198                ascii_num1_60
                0x060721bc                ascii_num1_59
                0x060721e0                ascii_num1_58
                0x06072204                ascii_num1_57
                0x06072228                ascii_num1_56
                0x0607224c                ascii_num1_55
                0x06072270                ascii_num1_54
                0x06072294                ascii_num1_53
                0x060722b8                ascii_num1_52
                0x060722dc                ascii_num1_51
                0x06072300                ascii_num1_50
                0x06072324                ascii_num1_49
                0x06072348                ascii_num1_48
                0x0607236c                ascii_num1_47
                0x06072390                ascii_num1_46
                0x060723b4                ascii_num1_45
                0x060723d8                ascii_num1_44
                0x060723fc                ascii_num1_43
                0x06072420                ascii_num1_42
                0x06072444                ascii_num1_41
                0x06072468                ascii_num1_40
                0x0607248c                ascii_num1_39
                0x060724b0                ascii_num1_38
                0x060724d4                ascii_num1_37
                0x060724f8                ascii_num1_36
                0x0607251c                ascii_num1_35
                0x06072540                ascii_num1_33
                0x06072564                ascii_num1_32
 .rodata        0x06072588     0x1a70 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
                0x06072588                ascii_num2_table
                0x06072700                ascii_num2_126
                0x06072744                ascii_num2_125
                0x06072788                ascii_num2_124
                0x060727cc                ascii_num2_123
                0x06072810                ascii_num2_122
                0x06072854                ascii_num2_121
                0x06072898                ascii_num2_120
                0x060728dc                ascii_num2_119
                0x06072920                ascii_num2_118
                0x06072964                ascii_num2_117
                0x060729a8                ascii_num2_116
                0x060729ec                ascii_num2_115
                0x06072a30                ascii_num2_114
                0x06072a74                ascii_num2_113
                0x06072ab8                ascii_num2_112
                0x06072afc                ascii_num2_111
                0x06072b40                ascii_num2_110
                0x06072b84                ascii_num2_109
                0x06072bc8                ascii_num2_108
                0x06072c0c                ascii_num2_107
                0x06072c50                ascii_num2_106
                0x06072c94                ascii_num2_105
                0x06072cd8                ascii_num2_104
                0x06072d1c                ascii_num2_103
                0x06072d60                ascii_num2_102
                0x06072da4                ascii_num2_101
                0x06072de8                ascii_num2_100
                0x06072e2c                ascii_num2_99
                0x06072e70                ascii_num2_98
                0x06072eb4                ascii_num2_97
                0x06072ef8                ascii_num2_96
                0x06072f3c                ascii_num2_95
                0x06072f80                ascii_num2_94
                0x06072fc4                ascii_num2_93
                0x06073008                ascii_num2_92
                0x0607304c                ascii_num2_91
                0x06073090                ascii_num2_90
                0x060730d4                ascii_num2_89
                0x06073118                ascii_num2_88
                0x0607315c                ascii_num2_87
                0x060731a0                ascii_num2_86
                0x060731e4                ascii_num2_85
                0x06073228                ascii_num2_84
                0x0607326c                ascii_num2_83
                0x060732b0                ascii_num2_82
                0x060732f4                ascii_num2_81
                0x06073338                ascii_num2_80
                0x0607337c                ascii_num2_79
                0x060733c0                ascii_num2_78
                0x06073404                ascii_num2_77
                0x06073448                ascii_num2_76
                0x0607348c                ascii_num2_75
                0x060734d0                ascii_num2_74
                0x06073514                ascii_num2_73
                0x06073558                ascii_num2_72
                0x0607359c                ascii_num2_71
                0x060735e0                ascii_num2_70
                0x06073624                ascii_num2_69
                0x06073668                ascii_num2_68
                0x060736ac                ascii_num2_67
                0x060736f0                ascii_num2_66
                0x06073734                ascii_num2_65
                0x06073778                ascii_num2_64
                0x060737bc                ascii_num2_63
                0x06073800                ascii_num2_62
                0x06073844                ascii_num2_61
                0x06073888                ascii_num2_60
                0x060738cc                ascii_num2_59
                0x06073910                ascii_num2_58
                0x06073954                ascii_num2_57
                0x06073998                ascii_num2_56
                0x060739dc                ascii_num2_55
                0x06073a20                ascii_num2_54
                0x06073a64                ascii_num2_53
                0x06073aa8                ascii_num2_52
                0x06073aec                ascii_num2_51
                0x06073b30                ascii_num2_50
                0x06073b74                ascii_num2_49
                0x06073bb8                ascii_num2_48
                0x06073bfc                ascii_num2_47
                0x06073c40                ascii_num2_46
                0x06073c84                ascii_num2_45
                0x06073cc8                ascii_num2_44
                0x06073d0c                ascii_num2_43
                0x06073d50                ascii_num2_42
                0x06073d94                ascii_num2_41
                0x06073dd8                ascii_num2_40
                0x06073e1c                ascii_num2_39
                0x06073e60                ascii_num2_38
                0x06073ea4                ascii_num2_37
                0x06073ee8                ascii_num2_36
                0x06073f2c                ascii_num2_35
                0x06073f70                ascii_num2_33
                0x06073fb4                ascii_num2_32
 .rodata        0x06073ff8     0x5430 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
                0x06073ff8                ascii_num3_table
                0x06074170                ascii_num3_126
                0x06074274                ascii_num3_125
                0x060742f8                ascii_num3_124
                0x0607437c                ascii_num3_123
                0x06074400                ascii_num3_122
                0x060744c4                ascii_num3_121
                0x06074588                ascii_num3_120
                0x0607464c                ascii_num3_119
                0x06074750                ascii_num3_118
                0x06074814                ascii_num3_117
                0x06074918                ascii_num3_116
                0x0607499c                ascii_num3_115
                0x06074a60                ascii_num3_114
                0x06074ae4                ascii_num3_113
                0x06074be8                ascii_num3_112
                0x06074cec                ascii_num3_111
                0x06074df0                ascii_num3_110
                0x06074ef4                ascii_num3_109
                0x06075038                ascii_num3_108
                0x060750bc                ascii_num3_107
                0x06075180                ascii_num3_106
                0x06075204                ascii_num3_105
                0x06075288                ascii_num3_104
                0x0607538c                ascii_num3_103
                0x06075490                ascii_num3_102
                0x06075514                ascii_num3_101
                0x06075618                ascii_num3_100
                0x0607571c                ascii_num3_99
                0x060757e0                ascii_num3_98
                0x060758e4                ascii_num3_97
                0x060759e8                ascii_num3_96
                0x06075a6c                ascii_num3_95
                0x06075b30                ascii_num3_94
                0x06075bf4                ascii_num3_93
                0x06075c78                ascii_num3_92
                0x06075cfc                ascii_num3_91
                0x06075d80                ascii_num3_90
                0x06075e84                ascii_num3_89
                0x06075f88                ascii_num3_88
                0x0607608c                ascii_num3_87
                0x06076210                ascii_num3_86
                0x06076314                ascii_num3_85
                0x06076418                ascii_num3_84
                0x0607651c                ascii_num3_83
                0x06076620                ascii_num3_82
                0x06076724                ascii_num3_81
                0x06076868                ascii_num3_80
                0x0607696c                ascii_num3_79
                0x06076ab0                ascii_num3_78
                0x06076bb4                ascii_num3_77
                0x06076cf8                ascii_num3_76
                0x06076dfc                ascii_num3_75
                0x06076f00                ascii_num3_74
                0x06076fc4                ascii_num3_73
                0x06077048                ascii_num3_72
                0x0607714c                ascii_num3_71
                0x06077290                ascii_num3_70
                0x06077394                ascii_num3_69
                0x06077498                ascii_num3_68
                0x0607759c                ascii_num3_67
                0x060776a0                ascii_num3_66
                0x060777a4                ascii_num3_65
                0x060778a8                ascii_num3_64
                0x06077a2c                ascii_num3_63
                0x06077b30                ascii_num3_62
                0x06077c34                ascii_num3_61
                0x06077d38                ascii_num3_60
                0x06077e3c                ascii_num3_59
                0x06077ec0                ascii_num3_58
                0x06077f44                ascii_num3_57
                0x06078048                ascii_num3_56
                0x0607814c                ascii_num3_55
                0x06078250                ascii_num3_54
                0x06078354                ascii_num3_53
                0x06078458                ascii_num3_52
                0x0607855c                ascii_num3_51
                0x06078660                ascii_num3_50
                0x06078764                ascii_num3_49
                0x06078868                ascii_num3_48
                0x0607896c                ascii_num3_47
                0x060789f0                ascii_num3_46
                0x06078a74                ascii_num3_45
                0x06078af8                ascii_num3_44
                0x06078b7c                ascii_num3_43
                0x06078c80                ascii_num3_42
                0x06078d44                ascii_num3_41
                0x06078dc8                ascii_num3_40
                0x06078e4c                ascii_num3_39
                0x06078ed0                ascii_num3_38
                0x06078fd4                ascii_num3_37
                0x06079118                ascii_num3_36
                0x0607921c                ascii_num3_35
                0x06079320                ascii_num3_33
                0x060793a4                ascii_num3_32
 .rodata        0x06079428     0x7870 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
                0x06079428                ascii_num4_table
                0x060795a0                ascii_num4_126
                0x060796e4                ascii_num4_125
                0x06079828                ascii_num4_124
                0x0607996c                ascii_num4_123
                0x06079ab0                ascii_num4_122
                0x06079bf4                ascii_num4_121
                0x06079d38                ascii_num4_120
                0x06079e7c                ascii_num4_119
                0x06079fc0                ascii_num4_118
                0x0607a104                ascii_num4_117
                0x0607a248                ascii_num4_116
                0x0607a38c                ascii_num4_115
                0x0607a4d0                ascii_num4_114
                0x0607a614                ascii_num4_113
                0x0607a758                ascii_num4_112
                0x0607a89c                ascii_num4_111
                0x0607a9e0                ascii_num4_110
                0x0607ab24                ascii_num4_109
                0x0607ac68                ascii_num4_108
                0x0607adac                ascii_num4_107
                0x0607aef0                ascii_num4_106
                0x0607b034                ascii_num4_105
                0x0607b178                ascii_num4_104
                0x0607b2bc                ascii_num4_103
                0x0607b400                ascii_num4_102
                0x0607b544                ascii_num4_101
                0x0607b688                ascii_num4_100
                0x0607b7cc                ascii_num4_99
                0x0607b910                ascii_num4_98
                0x0607ba54                ascii_num4_97
                0x0607bb98                ascii_num4_96
                0x0607bcdc                ascii_num4_95
                0x0607be20                ascii_num4_94
                0x0607bf64                ascii_num4_93
                0x0607c0a8                ascii_num4_92
                0x0607c1ec                ascii_num4_91
                0x0607c330                ascii_num4_90
                0x0607c474                ascii_num4_89
                0x0607c5b8                ascii_num4_88
                0x0607c6fc                ascii_num4_87
                0x0607c840                ascii_num4_86
                0x0607c984                ascii_num4_85
                0x0607cac8                ascii_num4_84
                0x0607cc0c                ascii_num4_83
                0x0607cd50                ascii_num4_82
                0x0607ce94                ascii_num4_81
                0x0607cfd8                ascii_num4_80
                0x0607d11c                ascii_num4_79
                0x0607d260                ascii_num4_78
                0x0607d3a4                ascii_num4_77
                0x0607d4e8                ascii_num4_76
                0x0607d62c                ascii_num4_75
                0x0607d770                ascii_num4_74
                0x0607d8b4                ascii_num4_73
                0x0607d9f8                ascii_num4_72
                0x0607db3c                ascii_num4_71
                0x0607dc80                ascii_num4_70
                0x0607ddc4                ascii_num4_69
                0x0607df08                ascii_num4_68
                0x0607e04c                ascii_num4_67
                0x0607e190                ascii_num4_66
                0x0607e2d4                ascii_num4_65
                0x0607e418                ascii_num4_64
                0x0607e55c                ascii_num4_63
                0x0607e6a0                ascii_num4_62
                0x0607e7e4                ascii_num4_61
                0x0607e928                ascii_num4_60
                0x0607ea6c                ascii_num4_59
                0x0607ebb0                ascii_num4_58
                0x0607ecf4                ascii_num4_57
                0x0607ee38                ascii_num4_56
                0x0607ef7c                ascii_num4_55
                0x0607f0c0                ascii_num4_54
                0x0607f204                ascii_num4_53
                0x0607f348                ascii_num4_52
                0x0607f48c                ascii_num4_51
                0x0607f5d0                ascii_num4_50
                0x0607f714                ascii_num4_49
                0x0607f858                ascii_num4_48
                0x0607f99c                ascii_num4_47
                0x0607fae0                ascii_num4_46
                0x0607fc24                ascii_num4_45
                0x0607fd68                ascii_num4_44
                0x0607feac                ascii_num4_43
                0x0607fff0                ascii_num4_42
                0x06080134                ascii_num4_41
                0x06080278                ascii_num4_40
                0x060803bc                ascii_num4_39
                0x06080500                ascii_num4_38
                0x06080644                ascii_num4_37
                0x06080788                ascii_num4_36
                0x060808cc                ascii_num4_35
                0x06080a10                ascii_num4_33
                0x06080b54                ascii_num4_32
 .rodata.str1.1
                0x06080c98       0xae obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .rodata.str1.1
                0x06080d46       0x5c obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .rodata.str1.1
                0x06080da2       0x3a obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .rodata.str1.1
                0x06080ddc       0x27 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 *fill*         0x06080e03        0x1 
 .rodata        0x06080e04      0x71c obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
                0x06080e04                res_key_music
 .rodata.str1.1
                0x06081520       0x13 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .rodata.str1.1
                0x06081533       0x2e obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 *fill*         0x06081561        0x3 
 .rodata        0x06081564       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .rodata.str1.1
                0x06081574       0x2f obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 *fill*         0x060815a3        0x1 
 .rodata        0x060815a4       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .rodata        0x060815b4       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .rodata.str1.1
                0x06081620       0x17 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 *fill*         0x06081637        0x1 
 .rodata        0x06081638       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .rodata        0x060816a4       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .rodata        0x06081710       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .rodata.str1.1
                0x0608177c      0x198 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .rodata        0x06081914       0x1c obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .rodata.str1.1
                0x06081930       0x5b obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 *fill*         0x0608198b        0x1 
 .rodata        0x0608198c       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .rodata        0x060819f8       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .rodata        0x06081a64       0x64 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .rodata.str1.1
                0x06081ac8      0x121 obj\Debug\app\app_common\src\app_init.o
                                0x128 (size before relaxing)
 .rodata.str1.1
                0x06081be9       0x58 obj\Debug\app\app_common\src\app_lcdshow.o
 *fill*         0x06081c41        0x3 
 .rodata        0x06081c44       0x14 obj\Debug\app\app_common\src\app_lcdshow.o
 .rodata.str1.1
                0x06081c58       0x8a obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                                 0x8e (size before relaxing)
 *fill*         0x06081ce2        0x2 
 .rodata        0x06081ce4      0x140 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                0x06081d0c                dateTimeWin
 .rodata.str1.1
                0x06081e24       0x46 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 *fill*         0x06081e6a        0x2 
 .rodata        0x06081e6c      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                0x06081e6c                defaultWin
 .rodata.str1.1
                0x06081f84       0x43 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 *fill*         0x06081fc7        0x1 
 .rodata        0x06081fc8      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                0x06081fc8                delAllWin
 .rodata.str1.1
                0x060820e0       0x5e obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 *fill*         0x0608213e        0x2 
 .rodata        0x06082140      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                0x06082140                delCurWin
 .rodata.str1.1
                0x06082258       0x6c obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                                 0x6d (size before relaxing)
 .rodata        0x060822c4      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                0x060822c4                formatWin
 .rodata.str1.1
                0x060823dc       0x56 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                                 0x5d (size before relaxing)
 *fill*         0x06082432        0x2 
 .rodata        0x06082434       0xd8 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x06082444                menuItemWin
 .rodata.str1.1
                0x0608250c       0x46 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                                 0x65 (size before relaxing)
 *fill*         0x06082552        0x2 
 .rodata        0x06082554       0x78 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                0x06082554                lockCurWin
 .rodata.str1.1
                0x060825cc       0x4f obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                                 0x51 (size before relaxing)
 *fill*         0x0608261b        0x1 
 .rodata        0x0608261c       0xc8 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                0x0608261c                menuOptionWin
 .rodata.str1.1
                0x060826e4       0x5f obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                                 0x6d (size before relaxing)
 *fill*         0x06082743        0x1 
 .rodata        0x06082744       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                0x06082744                unlockAllWin
 .rodata.str1.1
                0x060827e4       0x4c obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                                 0x6d (size before relaxing)
 .rodata        0x06082830       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                0x06082830                unlockCurWin
 .rodata.str1.1
                0x060828d0       0x5d obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 *fill*         0x0608292d        0x3 
 .rodata        0x06082930      0x348 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                0x06082930                asternWin
 .rodata.str1.1
                0x06082c78       0x43 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 *fill*         0x06082cbb        0x1 
 .rodata        0x06082cbc       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                0x06082cbc                noFileWin
 .rodata.str1.1
                0x06082d34      0x11a obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                                0x17b (size before relaxing)
 *fill*         0x06082e4e        0x2 
 .rodata        0x06082e50      0x4e8 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                0x06082ed8                selfTestWin
 .rodata.str1.1
                0x06083338       0x40 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                                 0x42 (size before relaxing)
 .rodata        0x06083378       0xa0 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                0x06083378                tips1Win
 .rodata.str1.1
                0x06083418       0x46 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                                 0x48 (size before relaxing)
 *fill*         0x0608345e        0x2 
 .rodata        0x06083460       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                0x06083460                tipsWin
 .rodata.str1.1
                0x060834d8       0x43 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 *fill*         0x0608351b        0x1 
 .rodata        0x0608351c       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                0x0608351c                TpIconWin
 .rodata.str1.1
                0x06083594      0x148 obj\Debug\app\task_windows\task_api.o
 .rodata        0x060836dc       0x28 obj\Debug\app\task_windows\task_api.o
 .rodata.str1.1
                0x06083704       0x73 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 *fill*         0x06083777        0x1 
 .rodata        0x06083778       0x68 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
                0x06083778                sysComMsgDeal
 .rodata.str1.1
                0x060837e0      0x25a obj\Debug\app\task_windows\task_common\src\task_common.o
                                0x266 (size before relaxing)
 *fill*         0x06083a3a        0x2 
 .rodata        0x06083a3c       0x90 obj\Debug\app\task_windows\task_common\src\task_common.o
 .rodata.str1.1
                0x06083acc       0x15 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 *fill*         0x06083ae1        0x3 
 .rodata        0x06083ae4       0x10 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
                0x06083ae4                taskComMsgDeal
 .rodata.str1.1
                0x06083af4       0x6a obj\Debug\app\task_windows\task_main\src\taskMain.o
 .rodata.str1.1
                0x06083b5e       0x3d obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
 *fill*         0x06083b9b        0x1 
 .rodata        0x06083b9c       0x70 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
                0x06083b9c                mainMenuTab
                0x06083bbc                mainWin
 .rodata.str1.1
                0x06083c0c       0x72 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .rodata.str1.1
                0x06083c7e       0x63 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                                 0x65 (size before relaxing)
 *fill*         0x06083ce1        0x3 
 .rodata        0x06083ce4      0x128 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                0x06083cf4                playAudioWin
 .rodata.str1.1
                0x06083e0c      0x211 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .rodata.str1.1
                0x0608401d      0x124 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                                0x156 (size before relaxing)
 *fill*         0x06084141        0x3 
 .rodata        0x06084144      0x218 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                0x06084154                playVideoMainWin
 .rodata.str1.1
                0x0608435c       0x5b obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                                 0x5d (size before relaxing)
 *fill*         0x060843b7        0x1 
 .rodata        0x060843b8       0xc8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                0x060843b8                playVideoSlideWin
 .rodata.str1.1
                0x06084480       0x8d obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                                 0x97 (size before relaxing)
 *fill*         0x0608450d        0x3 
 .rodata        0x06084510      0x4d4 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                0x06084510                playVideoThumbnallWin
 .rodata.str1.1
                0x060849e4        0xa obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .rodata.str1.1
                0x060849ee       0xb1 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .rodata.str1.1
                0x06084a9f        0x9 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                                 0x16 (size before relaxing)
 .rodata        0x06084aa8       0xa0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                0x06084aa8                RecordAudioWin
 .rodata.str1.1
                0x06084b48      0x17d obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .rodata.str1.1
                0x06084cc5       0xe9 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                                0x122 (size before relaxing)
 *fill*         0x06084dae        0x2 
 .rodata        0x06084db0       0x10 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .rodata.str1.1
                0x06084dc0      0x315 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                                0x321 (size before relaxing)
 .rodata.str1.1
                0x060850d5       0xaf obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                                 0xdf (size before relaxing)
 .rodata        0x06085184      0x1f0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                0x06085194                recordVideoWin
 .rodata.str1.1
                0x06085374      0x147 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                                0x159 (size before relaxing)
 .rodata.str1.1
                0x060854bb        0xa obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .rodata.str1.1
                0x060854c5       0x17 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .rodata        0x060854dc       0x50 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                0x060854dc                ShowLogoWin
 .rodata.str1.1
                0x0608552c       0x45 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                                 0x51 (size before relaxing)
 .rodata.str1.1
                0x06085571       0x8d obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 *fill*         0x060855fe        0x2 
 .rodata        0x06085600      0x230 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                0x06085600                usbDeviceWin
 .rodata.str1.1
                0x06085830      0x13b obj\Debug\app\task_windows\windows_api.o
                                0x155 (size before relaxing)
 *fill*         0x0608596b        0x1 
 .rodata        0x0608596c       0x1c obj\Debug\app\task_windows\windows_api.o
 .rodata.str1.1
                0x06085988       0x72 obj\Debug\app\user_config\src\mbedtls_md5.o
                                 0x75 (size before relaxing)
 *fill*         0x060859fa        0x2 
 .rodata        0x060859fc       0x40 obj\Debug\app\user_config\src\mbedtls_md5.o
 .rodata.str1.1
                0x06085a3c       0x81 obj\Debug\app\user_config\src\user_config_api.o
 *fill*         0x06085abd        0x3 
 .rodata        0x06085ac0      0x28c obj\Debug\app\user_config\src\user_config_api.o
 .rodata        0x06085d4c      0x100 obj\Debug\app\user_config\src\user_config_tab.o
                0x06085d4c                user_cfg_tab
 .rodata        0x06085e4c       0xd0 ..\lib\libmcu.a(hx330x_auadc.o)
                0x06085e4c                tbl_micvol
                0x06085e8c                auadc_samplerate_tab
 .rodata.str1.1
                0x06085f1c        0xe ..\lib\libmcu.a(hx330x_csi.o)
 *fill*         0x06085f2a        0x2 
 .rodata        0x06085f2c      0x154 ..\lib\libmcu.a(hx330x_csi.o)
                0x06085f2c                csi_dvp_map_tab
                0x06085f40                csi_dvp_map4
                0x06085f80                csi_dvp_map3
                0x06085fc0                csi_dvp_map2
                0x06086000                csi_dvp_map1
                0x06086040                csi_dvp_map0
 .rodata        0x06086080       0xac ..\lib\libmcu.a(hx330x_dac.o)
                0x06086080                gain
                0x06086094                eq_coeff
                0x060860cc                hx330x_dacInit_table
 .rodata        0x0608612c      0x210 ..\lib\libmcu.a(hx330x_dma.o)
                0x0608612c                winDis_cfg
                0x060861dc                winB_cfg
                0x0608628c                winA_cfg
 .rodata        0x0608633c       0xd0 ..\lib\libmcu.a(hx330x_dmauart.o)
                0x0608633c                uart1_IO_MAP_tab
 .rodata        0x0608640c       0xb8 ..\lib\libmcu.a(hx330x_gpio.o)
 .rodata        0x060864c4      0x4a4 ..\lib\libmcu.a(hx330x_iic.o)
                0x060864c4                iic1_uinit_map_tab
                0x060864e0                iic1_init_map_tab
                0x060864fc                iic1_uinit_map6
                0x06086524                iic1_init_map6
                0x06086570                iic1_uinit_map5
                0x06086598                iic1_init_map5
                0x060865e4                iic1_uinit_map4
                0x0608660c                iic1_init_map4
                0x06086658                iic1_uinit_map3
                0x06086680                iic1_init_map3
                0x060866cc                iic1_uinit_map2
                0x060866f4                iic1_init_map2
                0x06086740                iic1_uinit_map1
                0x06086768                iic1_init_map1
                0x060867b4                iic1_uinit_map0
                0x060867dc                iic1_init_map0
                0x06086828                iic0_init_map_tab
                0x06086838                iic0_init_map3
                0x06086884                iic0_init_map2
                0x060868d0                iic0_init_map1
                0x0608691c                iic0_init_map0
 .rodata        0x06086968       0xf0 ..\lib\libmcu.a(hx330x_int.o)
 .rodata        0x06086a58      0x14c ..\lib\libmcu.a(hx330x_isp_tab.o)
                0x06086a58                Ratio_of_Evstep
                0x06086b60                GAOS3X3_TAB
                0x06086b6c                GAOS5X5_TAB
                0x06086b88                LOG_TAB
 .rodata        0x06086ba4       0x14 ..\lib\libmcu.a(hx330x_jpg.o)
 .rodata.str1.1
                0x06086bb8       0x16 ..\lib\libmcu.a(hx330x_jpg.o)
 *fill*         0x06086bce        0x2 
 .rodata        0x06086bd0      0xda0 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                0x06087610                c_table_chroma
                0x06087650                belta_table_chroma
                0x06087690                alpha_table_chroma
                0x060876d0                c_table_luma
                0x06087710                belta_table_luma
                0x06087750                alpha_table_luma
                0x06087790                bic_coef_tabR
                0x06087830                bic_coef_tabL
                0x060878d0                bic_coef_tab
 .rodata        0x06087970      0x11c ..\lib\libmcu.a(hx330x_lcd.o)
 .rodata        0x06087a8c       0xa0 ..\lib\libmcu.a(hx330x_lcdui.o)
 .rodata        0x06087b2c      0x168 ..\lib\libmcu.a(hx330x_misc.o)
 .rodata        0x06087c94      0x248 ..\lib\libmcu.a(hx330x_spi1.o)
                0x06087c94                SPI1_CS_MAP_tab
                0x06087cbc                spi1PinCfg_tab
                0x06087cdc                SPI1_2LINE_Pos3_tab
                0x06087d1c                SPI1_3LINE_Pos3_tab
                0x06087d5c                SPI1_2LINE_Pos2_tab
                0x06087d9c                SPI1_3LINE_Pos2_tab
                0x06087ddc                SPI1_2LINE_Pos1_tab
                0x06087e1c                SPI1_3LINE_Pos1_tab
                0x06087e5c                SPI1_2LINE_Pos0_tab
                0x06087e9c                SPI1_3LINE_Pos0_tab
 .rodata        0x06087edc       0x18 ..\lib\libmcu.a(hx330x_sys.o)
 .rodata        0x06087ef4      0x100 ..\lib\libmcu.a(hx330x_timer.o)
                0x06087ef4                timer3_PWM_IO_MAP_tab
                0x06087f24                timer2_PWM_IO_MAP_tab
                0x06087f5c                timer1_PWM_IO_MAP_tab
                0x06087f9c                timer0_PWM_IO_MAP_tab
 .rodata        0x06087ff4       0xd8 ..\lib\libmcu.a(hx330x_uart.o)
                0x06087ff4                uart0_IO_MAP_tab
 .rodata.cst4   0x060880cc        0x8 ..\lib\libmcu.a(hx330x_usb.o)
 .rodata        0x060880d4       0x68 ..\lib\libmcu.a(hx330x_emi.o)
                0x060880d4                hx330x_emiPinConfigSlave_table
                0x060880fc                hx330x_emiPinConfigMaster_table
 .rodata        0x0608813c       0x44 ..\lib\libisp.a(hal_isp.o)
 .rodata.str1.1
                0x06088180       0x12 ..\lib\libisp.a(hal_isp.o)
 .rodata.str1.1
                0x06088192      0x12f ..\lib\libjpg.a(hal_jpg.o)
 .rodata.str1.1
                0x060882c1       0x36 ..\lib\liblcd.a(hal_lcd.o)
 .rodata.str1.1
                0x060882f7      0x174 ..\lib\liblcd.a(hal_lcdMem.o)
 .rodata.str1.1
                0x0608846b       0x11 ..\lib\liblcd.a(hal_lcdrotate.o)
 .rodata.str1.1
                0x0608847c       0x12 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 *fill*         0x0608848e        0x2 
 .rodata        0x06088490       0x20 ..\lib\libmultimedia.a(api_multimedia.o)
 .rodata.str1.1
                0x060884b0       0x81 ..\lib\libmultimedia.a(avi_dec.o)
 *fill*         0x06088531        0x3 
 .rodata        0x06088534       0x30 ..\lib\libmultimedia.a(avi_dec.o)
                0x06088534                avi_dec_func
 .rodata.str1.1
                0x06088564       0x78 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .rodata        0x060885dc     0xe830 ..\lib\libmultimedia.a(avi_odml_enc.o)
                0x060885dc                avi_odml_enc_func
                0x0608860c                avi_odml_header
 .rodata.str1.1
                0x06096e0c       0x5f ..\lib\libmultimedia.a(avi_std_enc.o)
 *fill*         0x06096e6b        0x1 
 .rodata        0x06096e6c      0x218 ..\lib\libmultimedia.a(avi_std_enc.o)
                0x06096e6c                avi_std_enc_func
 .rodata.str1.1
                0x06097084       0x3c ..\lib\libmultimedia.a(wav_dec.o)
 .rodata        0x060970c0       0x30 ..\lib\libmultimedia.a(wav_dec.o)
                0x060970c0                wav_dec_func
 .rodata.str1.1
                0x060970f0       0x24 ..\lib\libmultimedia.a(wav_enc.o)
 .rodata        0x06097114       0x30 ..\lib\libmultimedia.a(wav_enc.o)
                0x06097114                wav_enc_func
 .rodata        0x06097144       0x2c ..\lib\libmultimedia.a(wav_pcm.o)
                0x06097144                wav_pcm_head
 .rodata        0x06097170      0x100 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                0x06097170                __clz_tab

.lcd_resource   0x00000000     0x29bc load address 0x00097400
 *(.lcd_res.struct)
 .lcd_res.struct
                0x00000000       0xac obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
                0x00000000                __lcd_desc
 *(.lcd_res*)
 .lcd_res       0x000000ac      0x110 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .lcd_res       0x000001bc     0x2800 ..\lib\liblcd.a(lcd_tab.o)
                0x000004bc                lcd_contra_tab
                0x000011bc                lcd_gamma

.sensor_resource
                0x00000000     0x3640 load address 0x00099dbc
 *(.sensor_res.header)
 .sensor_res.header
                0x00000000       0x40 obj\Debug\dev\sensor\src\sensor_tab.o
                0x00000000                RES_SensorHeader
                0x00000040                _res_sensor_header_item_start = .
 *(.sensor_res.header.items)
                0x00000040                _res_sensor_header_item_end = .
 *(.sensor_res.isp_tab)
 .sensor_res.isp_tab
                0x00000040     0x3600 obj\Debug\dev\sensor\src\sensor_tab.o
                0x00000040                sensor_rgb_gamma
                0x00001240                sensor_ygamma_tab
 *(.sensor_res.struct)
 *(.sensor_res.init_tab)

.nes_resource   0x00000000        0x0 load address 0x0009d400
 *(.nes_games_tab)

.eh_frame       0x00003640    0x107a8 load address 0x0009d3fc
 *(.eh_frame)
 .eh_frame      0x00003640       0x50 obj\Debug\dev\battery\src\battery_api.o
 .eh_frame      0x00003690       0x80 obj\Debug\dev\dev_api.o
                                 0x94 (size before relaxing)
 .eh_frame      0x00003710       0xa8 obj\Debug\dev\fs\src\diskio.o
                                 0xbc (size before relaxing)
 .eh_frame      0x000037b8      0x914 obj\Debug\dev\fs\src\ff.o
                                0x928 (size before relaxing)
 .eh_frame      0x000040cc       0x54 obj\Debug\dev\fs\src\ffunicode.o
                                 0x68 (size before relaxing)
 .eh_frame      0x00004120      0x2ac obj\Debug\dev\fs\src\fs_api.o
                                0x2c0 (size before relaxing)
 .eh_frame      0x000043cc       0x94 obj\Debug\dev\gsensor\src\gsensor_api.o
                                 0xa8 (size before relaxing)
 .eh_frame      0x00004460       0x80 obj\Debug\dev\gsensor\src\gsensor_da380.o
                                 0x94 (size before relaxing)
 .eh_frame      0x000044e0       0x80 obj\Debug\dev\gsensor\src\gsensor_gma301.o
                                 0x94 (size before relaxing)
 .eh_frame      0x00004560       0x7c obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
                                 0x90 (size before relaxing)
 .eh_frame      0x000045dc       0x3c obj\Debug\dev\ir\src\ir_api.o
                                 0x50 (size before relaxing)
 .eh_frame      0x00004618       0x8c obj\Debug\dev\key\src\key_api.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x000046a4       0x8c obj\Debug\dev\lcd\src\lcd_api.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x00004730       0x38 obj\Debug\dev\led\src\led_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x00004768       0x38 obj\Debug\dev\led_pwm\src\led_pwm_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x000047a0       0xc8 obj\Debug\dev\nvfs\src\nvfs_api.o
                                 0xdc (size before relaxing)
 .eh_frame      0x00004868      0x430 obj\Debug\dev\nvfs\src\nvfs_jpg.o
                                0x444 (size before relaxing)
 .eh_frame      0x00004c98      0x3f8 obj\Debug\dev\sd\src\sd_api.o
                                0x40c (size before relaxing)
 .eh_frame      0x00005090       0xe0 obj\Debug\dev\sensor\src\sensor_api.o
                                 0xf4 (size before relaxing)
 .eh_frame      0x00005170       0x64 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                                 0x78 (size before relaxing)
 .eh_frame      0x000051d4       0x58 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                                 0x6c (size before relaxing)
 .eh_frame      0x0000522c      0x138 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
                                0x14c (size before relaxing)
 .eh_frame      0x00005364       0x78 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                                 0x8c (size before relaxing)
 .eh_frame      0x000053dc      0x110 obj\Debug\dev\usb\dusb\src\dusb_api.o
                                0x124 (size before relaxing)
 .eh_frame      0x000054ec      0x12c obj\Debug\dev\usb\dusb\src\dusb_enum.o
                                0x140 (size before relaxing)
 .eh_frame      0x00005618      0x27c obj\Debug\dev\usb\dusb\src\dusb_msc.o
                                0x290 (size before relaxing)
 .eh_frame      0x00005894      0x12c obj\Debug\dev\usb\dusb\src\dusb_uac.o
                                0x140 (size before relaxing)
 .eh_frame      0x000059c0      0x1ec obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                                0x200 (size before relaxing)
 .eh_frame      0x00005bac      0x1c0 obj\Debug\dev\usb\husb\src\husb_api.o
                                0x1d4 (size before relaxing)
 .eh_frame      0x00005d6c      0x668 obj\Debug\dev\usb\husb\src\husb_enum.o
                                0x67c (size before relaxing)
 .eh_frame      0x000063d4       0x60 obj\Debug\dev\usb\husb\src\husb_hub.o
                                 0x74 (size before relaxing)
 .eh_frame      0x00006434      0x264 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
                                0x278 (size before relaxing)
 .eh_frame      0x00006698      0x248 obj\Debug\dev\usb\husb\src\husb_usensor.o
                                0x25c (size before relaxing)
 .eh_frame      0x000068e0      0x284 obj\Debug\dev\usb\husb\src\husb_uvc.o
                                0x298 (size before relaxing)
 .eh_frame      0x00006b64       0x74 obj\Debug\hal\src\hal_adc.o
                                 0x88 (size before relaxing)
 .eh_frame      0x00006bd8      0x1ec obj\Debug\hal\src\hal_auadc.o
                                0x200 (size before relaxing)
 .eh_frame      0x00006dc4       0xa4 obj\Debug\hal\src\hal_csi.o
                                 0xb8 (size before relaxing)
 .eh_frame      0x00006e68       0xc4 obj\Debug\hal\src\hal_dac.o
                                 0xd8 (size before relaxing)
 .eh_frame      0x00006f2c       0xd8 obj\Debug\hal\src\hal_dmauart.o
                                 0xec (size before relaxing)
 .eh_frame      0x00007004       0x6c obj\Debug\hal\src\hal_gpio.o
                                 0x80 (size before relaxing)
 .eh_frame      0x00007070      0x2c8 obj\Debug\hal\src\hal_iic.o
                                0x2dc (size before relaxing)
 .eh_frame      0x00007338       0x1c obj\Debug\hal\src\hal_int.o
                                 0x30 (size before relaxing)
 .eh_frame      0x00007354      0x474 obj\Debug\hal\src\hal_lcdshow.o
                                0x488 (size before relaxing)
 .eh_frame      0x000077c8       0x68 obj\Debug\hal\src\hal_md.o
                                 0x7c (size before relaxing)
 .eh_frame      0x00007830      0x40c obj\Debug\hal\src\hal_mjpAEncode.o
                                0x420 (size before relaxing)
 .eh_frame      0x00007c3c      0x208 obj\Debug\hal\src\hal_mjpBEncode.o
                                0x21c (size before relaxing)
 .eh_frame      0x00007e44      0x274 obj\Debug\hal\src\hal_mjpDecode.o
                                0x288 (size before relaxing)
 .eh_frame      0x000080b8      0x248 obj\Debug\hal\src\hal_rtc.o
                                0x25c (size before relaxing)
 .eh_frame      0x00008300      0x27c obj\Debug\hal\src\hal_spi.o
                                0x290 (size before relaxing)
 .eh_frame      0x0000857c      0x13c obj\Debug\hal\src\hal_spi1.o
                                0x150 (size before relaxing)
 .eh_frame      0x000086b8       0xe8 obj\Debug\hal\src\hal_stream.o
                                 0xfc (size before relaxing)
 .eh_frame      0x000087a0      0x178 obj\Debug\hal\src\hal_sys.o
                                0x18c (size before relaxing)
 .eh_frame      0x00008918       0x78 obj\Debug\hal\src\hal_timer.o
                                 0x8c (size before relaxing)
 .eh_frame      0x00008990      0x1b0 obj\Debug\hal\src\hal_uart.o
                                0x1c4 (size before relaxing)
 .eh_frame      0x00008b40      0x1fc obj\Debug\hal\src\hal_watermark.o
                                0x210 (size before relaxing)
 .eh_frame      0x00008d3c       0xd4 obj\Debug\mcu\xos\xmsgq.o
                                 0xe8 (size before relaxing)
 .eh_frame      0x00008e10       0x88 obj\Debug\mcu\xos\xos.o
                                 0x9c (size before relaxing)
 .eh_frame      0x00008e98       0x70 obj\Debug\mcu\xos\xwork.o
                                 0x84 (size before relaxing)
 .eh_frame      0x00008f08      0x19c obj\Debug\multimedia\audio\audio_playback.o
                                0x1b0 (size before relaxing)
 .eh_frame      0x000090a4      0x138 obj\Debug\multimedia\audio\audio_record.o
                                0x14c (size before relaxing)
 .eh_frame      0x000091dc       0xb4 obj\Debug\multimedia\image\image_decode.o
                                 0xc8 (size before relaxing)
 .eh_frame      0x00009290       0xa4 obj\Debug\multimedia\image\image_encode.o
                                 0xb8 (size before relaxing)
 .eh_frame      0x00009334       0x20 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                                 0x34 (size before relaxing)
 .eh_frame      0x00009354      0x2b8 obj\Debug\multimedia\video\video_playback.o
                                0x2cc (size before relaxing)
 .eh_frame      0x0000960c      0x230 obj\Debug\multimedia\video\video_record.o
                                0x244 (size before relaxing)
 .eh_frame      0x0000983c      0x3c4 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
                                0x3d8 (size before relaxing)
 .eh_frame      0x00009c00      0x134 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                                0x148 (size before relaxing)
 .eh_frame      0x00009d34       0x60 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
                                 0x74 (size before relaxing)
 .eh_frame      0x00009d94       0xc4 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
                                 0xd8 (size before relaxing)
 .eh_frame      0x00009e58      0x144 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
                                0x158 (size before relaxing)
 .eh_frame      0x00009f9c       0x58 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
                                 0x6c (size before relaxing)
 .eh_frame      0x00009ff4       0x38 obj\Debug\sys_manage\res_manage\res_manage_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000a02c       0x9c obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000a0c8       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a108       0x5c obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
                                 0x70 (size before relaxing)
 .eh_frame      0x0000a164       0x44 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
                                 0x58 (size before relaxing)
 .eh_frame      0x0000a1a8      0x1c8 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                                0x1dc (size before relaxing)
 .eh_frame      0x0000a370       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a3b0       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000a410      0x358 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
                                0x36c (size before relaxing)
 .eh_frame      0x0000a768       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a7b4       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a800       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a84c       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a88c      0x588 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
                                0x59c (size before relaxing)
 .eh_frame      0x0000ae14       0xd8 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000aeec       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000af4c       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000afac       0x70 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
                                 0x84 (size before relaxing)
 .eh_frame      0x0000b01c       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000b07c       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000b0bc       0x80 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
                                 0x94 (size before relaxing)
 .eh_frame      0x0000b13c       0x44 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
                                 0x58 (size before relaxing)
 .eh_frame      0x0000b180       0xec obj\Debug\app\app_common\src\app_init.o
                                0x100 (size before relaxing)
 .eh_frame      0x0000b26c      0x158 obj\Debug\app\app_common\src\app_lcdshow.o
                                0x16c (size before relaxing)
 .eh_frame      0x0000b3c4       0x1c obj\Debug\app\app_common\src\main.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000b3e0       0x8c obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x0000b46c       0x54 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
                                 0x68 (size before relaxing)
 .eh_frame      0x0000b4c0      0x17c obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                                0x190 (size before relaxing)
 .eh_frame      0x0000b63c      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000b770      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000b8a4      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000b9dc      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000bb10      0x2dc obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                                0x2f0 (size before relaxing)
 .eh_frame      0x0000bdec      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000bf24      0x150 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                                0x164 (size before relaxing)
 .eh_frame      0x0000c074      0x13c obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                                0x150 (size before relaxing)
 .eh_frame      0x0000c1b0      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000c2e8       0x70 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                                 0x84 (size before relaxing)
 .eh_frame      0x0000c358       0xc8 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                                 0xdc (size before relaxing)
 .eh_frame      0x0000c420      0x194 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                                0x1a8 (size before relaxing)
 .eh_frame      0x0000c5b4      0x170 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                                0x184 (size before relaxing)
 .eh_frame      0x0000c724      0x170 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                                0x184 (size before relaxing)
 .eh_frame      0x0000c894       0xe0 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                                 0xf4 (size before relaxing)
 .eh_frame      0x0000c974       0x98 obj\Debug\app\task_windows\msg_api.o
                                 0xac (size before relaxing)
 .eh_frame      0x0000ca0c       0xcc obj\Debug\app\task_windows\task_api.o
                                 0xe0 (size before relaxing)
 .eh_frame      0x0000cad8       0xf4 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
                                0x108 (size before relaxing)
 .eh_frame      0x0000cbcc      0x34c obj\Debug\app\task_windows\task_common\src\task_common.o
                                0x360 (size before relaxing)
 .eh_frame      0x0000cf18       0x1c obj\Debug\app\task_windows\task_common\src\task_common_msg.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000cf34      0x170 obj\Debug\app\task_windows\task_main\src\taskMain.o
                                0x184 (size before relaxing)
 .eh_frame      0x0000d0a4      0x130 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
                                0x144 (size before relaxing)
 .eh_frame      0x0000d1d4       0x74 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                                 0x88 (size before relaxing)
 .eh_frame      0x0000d248      0x200 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                                0x214 (size before relaxing)
 .eh_frame      0x0000d448      0x180 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                                0x194 (size before relaxing)
 .eh_frame      0x0000d5c8      0x388 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                                0x39c (size before relaxing)
 .eh_frame      0x0000d950      0x194 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                                0x1a8 (size before relaxing)
 .eh_frame      0x0000dae4      0x20c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                                0x220 (size before relaxing)
 .eh_frame      0x0000dcf0       0x1c obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000dd0c       0x7c obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
                                 0x90 (size before relaxing)
 .eh_frame      0x0000dd88       0xd8 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000de60       0x9c obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000defc      0x314 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                                0x328 (size before relaxing)
 .eh_frame      0x0000e210      0x134 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000e344      0x450 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                                0x464 (size before relaxing)
 .eh_frame      0x0000e794       0x84 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                                 0x98 (size before relaxing)
 .eh_frame      0x0000e818       0x9c obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000e8b4       0x38 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000e8ec       0x54 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                                 0x68 (size before relaxing)
 .eh_frame      0x0000e940       0x58 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                                 0x6c (size before relaxing)
 .eh_frame      0x0000e998       0xd8 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000ea70       0x78 obj\Debug\app\task_windows\windows_api.o
                                 0x8c (size before relaxing)
 .eh_frame      0x0000eae8      0x154 obj\Debug\app\user_config\src\mbedtls_md5.o
                                0x168 (size before relaxing)
 .eh_frame      0x0000ec3c      0x120 obj\Debug\app\user_config\src\user_config_api.o
                                0x134 (size before relaxing)
 .eh_frame      0x0000ed5c      0x1d0 ..\lib\libboot.a(boot.o)
                                0x1e4 (size before relaxing)
 .eh_frame      0x0000ef2c       0x78 ..\lib\libboot.a(boot_lib.o)
                                 0x8c (size before relaxing)
 .eh_frame      0x0000efa4       0x68 ..\lib\libmcu.a(hx330x_adc.o)
                                 0x7c (size before relaxing)
 .eh_frame      0x0000f00c      0x110 ..\lib\libmcu.a(hx330x_auadc.o)
                                0x124 (size before relaxing)
 .eh_frame      0x0000f11c      0x4e4 ..\lib\libmcu.a(hx330x_csi.o)
                                0x4f8 (size before relaxing)
 .eh_frame      0x0000f600      0x1d4 ..\lib\libmcu.a(hx330x_dac.o)
                                0x1e8 (size before relaxing)
 .eh_frame      0x0000f7d4       0x88 ..\lib\libmcu.a(hx330x_dma.o)
                                 0x9c (size before relaxing)
 .eh_frame      0x0000f85c      0x170 ..\lib\libmcu.a(hx330x_dmauart.o)
                                0x184 (size before relaxing)
 .eh_frame      0x0000f9cc      0x2c8 ..\lib\libmcu.a(hx330x_gpio.o)
                                0x2dc (size before relaxing)
 .eh_frame      0x0000fc94      0x31c ..\lib\libmcu.a(hx330x_iic.o)
                                0x330 (size before relaxing)
 .eh_frame      0x0000ffb0       0x9c ..\lib\libmcu.a(hx330x_int.o)
                                 0xb0 (size before relaxing)
 .eh_frame      0x0001004c      0x264 ..\lib\libmcu.a(hx330x_isp.o)
                                0x278 (size before relaxing)
 .eh_frame      0x000102b0      0x778 ..\lib\libmcu.a(hx330x_jpg.o)
                                0x78c (size before relaxing)
 .eh_frame      0x00010a28       0x38 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                                 0x4c (size before relaxing)
 .eh_frame      0x00010a60      0x3d4 ..\lib\libmcu.a(hx330x_lcd.o)
                                0x3e8 (size before relaxing)
 .eh_frame      0x00010e34       0xc4 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                                 0xd8 (size before relaxing)
 .eh_frame      0x00010ef8      0x3c8 ..\lib\libmcu.a(hx330x_lcdui.o)
                                0x3dc (size before relaxing)
 .eh_frame      0x000112c0       0xe8 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                                 0xfc (size before relaxing)
 .eh_frame      0x000113a8       0x80 ..\lib\libmcu.a(hx330x_lcdwin.o)
                                 0x94 (size before relaxing)
 .eh_frame      0x00011428       0x80 ..\lib\libmcu.a(hx330x_md.o)
                                 0x94 (size before relaxing)
 .eh_frame      0x000114a8       0x5c ..\lib\libmcu.a(hx330x_mipi.o)
                                 0x70 (size before relaxing)
 .eh_frame      0x00011504      0x29c ..\lib\libmcu.a(hx330x_misc.o)
                                0x2b0 (size before relaxing)
 .eh_frame      0x000117a0      0x374 ..\lib\libmcu.a(hx330x_rtc.o)
                                0x388 (size before relaxing)
 .eh_frame      0x00011b14      0x2b4 ..\lib\libmcu.a(hx330x_sd.o)
                                0x2c8 (size before relaxing)
 .eh_frame      0x00011dc8      0x108 ..\lib\libmcu.a(hx330x_spi0.o)
                                0x11c (size before relaxing)
 .eh_frame      0x00011ed0      0x184 ..\lib\libmcu.a(hx330x_spi1.o)
                                0x198 (size before relaxing)
 .eh_frame      0x00012054      0x3d4 ..\lib\libmcu.a(hx330x_sys.o)
                                0x3e8 (size before relaxing)
 .eh_frame      0x00012428      0x188 ..\lib\libmcu.a(hx330x_timer.o)
                                0x19c (size before relaxing)
 .eh_frame      0x000125b0      0x110 ..\lib\libmcu.a(hx330x_tminf.o)
                                0x124 (size before relaxing)
 .eh_frame      0x000126c0       0x78 ..\lib\libmcu.a(hx330x_uart.o)
                                 0x8c (size before relaxing)
 .eh_frame      0x00012738      0x444 ..\lib\libmcu.a(hx330x_usb.o)
                                0x458 (size before relaxing)
 .eh_frame      0x00012b7c       0x60 ..\lib\libmcu.a(hx330x_wdt.o)
                                 0x74 (size before relaxing)
 .eh_frame      0x00012bdc       0xa4 ..\lib\libmcu.a(hx330x_emi.o)
                                 0xb8 (size before relaxing)
 .eh_frame      0x00012c80      0x220 ..\lib\libisp.a(hal_isp.o)
                                0x234 (size before relaxing)
 .eh_frame      0x00012ea0      0x118 ..\lib\libjpg.a(hal_jpg.o)
                                0x12c (size before relaxing)
 .eh_frame      0x00012fb8      0x1c0 ..\lib\liblcd.a(hal_lcd.o)
                                0x1d4 (size before relaxing)
 .eh_frame      0x00013178      0x148 ..\lib\liblcd.a(hal_lcdMem.o)
                                0x15c (size before relaxing)
 .eh_frame      0x000132c0       0x84 ..\lib\liblcd.a(hal_lcdrotate.o)
                                 0x98 (size before relaxing)
 .eh_frame      0x00013344      0x238 ..\lib\liblcd.a(hal_lcdUi.o)
                                0x24c (size before relaxing)
 .eh_frame      0x0001357c       0x60 ..\lib\liblcd.a(hal_lcdUiLzo.o)
                                 0x74 (size before relaxing)
 .eh_frame      0x000135dc       0x58 ..\lib\liblcd.a(lcd_tab.o)
                                 0x6c (size before relaxing)
 .eh_frame      0x00013634      0x204 ..\lib\libmultimedia.a(api_multimedia.o)
                                0x218 (size before relaxing)
 .eh_frame      0x00013838      0x14c ..\lib\libmultimedia.a(avi_dec.o)
                                0x160 (size before relaxing)
 .eh_frame      0x00013984      0x130 ..\lib\libmultimedia.a(avi_odml_enc.o)
                                0x144 (size before relaxing)
 .eh_frame      0x00013ab4      0x144 ..\lib\libmultimedia.a(avi_std_enc.o)
                                0x158 (size before relaxing)
 .eh_frame      0x00013bf8       0xb8 ..\lib\libmultimedia.a(wav_dec.o)
                                 0xcc (size before relaxing)
 .eh_frame      0x00013cb0       0xa8 ..\lib\libmultimedia.a(wav_enc.o)
                                 0xbc (size before relaxing)
 .eh_frame      0x00013d58       0x38 ..\lib\libmultimedia.a(wav_pcm.o)
                                 0x4c (size before relaxing)
 .eh_frame      0x00013d90       0x2c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                                 0x40 (size before relaxing)
 .eh_frame      0x00013dbc       0x2c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                                 0x40 (size before relaxing)

.rela.dyn       0x00013de8        0x0 load address 0x000adba4
 .rela.text     0x00013de8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.data     0x00013de8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector.kepttext
                0x00013de8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.rodata   0x00013de8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.sdram_text
                0x00013de8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.sensor_res.header
                0x00013de8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.text.startup
                0x00013de8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector.text
                0x00013de8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.bootsec  0x00013de8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector   0x00013de8        0x0 obj\Debug\dev\battery\src\battery_api.o

.mp3_text       0x00008000        0x0 load address 0x0009d400
                0x00008000                __mp3_text_start = .
 *(.mp3_text)
                0x00008000                . = ALIGN (0x4)

.mp3_code       0x00200000        0x0 load address 0x0009d400
                0x00200000                __mp3_code_start = .
 *(.mp3_code)
                0x00200000                . = ALIGN (0x4)

.mp3_data
 *(.mp3_data)

.nes_code       0x00200000        0x0 load address 0x0009d400
                0x00200000                __nes_code_start = .
 *(.nes_code)
                0x00200000                . = ALIGN (0x4)

.nes_data       0x00200000        0x0
                0x00200000                __nes_data_start = .
 *(.nes_data)
                0x00200000                __nes_data_end = .

.nes_com_text   0x00008000        0x0 load address 0x0009d400
                0x00008000                __nes_text_start = .
 *(.nes_com_text)
                0x00008000                . = ALIGN (0x4)
                0x00000015                _boot_code_len = ((SIZEOF (.boot_code) + 0x1ff) / 0x200)
                0x00000001                _boot_code_sec = (LOADADDR (.boot_code) / 0x200)
                0x02000000                _text_start = _onsdram_start
                0x00000016                _text_sec = (LOADADDR (.on_sdram) / 0x200)
                0x0000004f                _text_len = ((SIZEOF (.on_sdram) + 0x1ff) / 0x200)
                0x00000001                ASSERT (((_sdram_remian_addr - ORIGIN (sdram)) < __sdram_size), No memroy for sdram)
                0x001e3840                __sdram_remain_size = ((ORIGIN (sdram) + __sdram_size) - _sdram_remian_addr)
                0x00001400                __stack_size = 0x1400
                0x00000001                ASSERT ((((ORIGIN (ram_user) + 0x7000) - __sram_end) >= __stack_size), No memroy for stack)
                0x02200000                __bss_end = (_sdram_remian_addr + __sdram_remain_size)
                0x00000000                __mp3_text_len = SIZEOF (.mp3_text)
                0x0009d400                __mp3_text_addr = LOADADDR (.mp3_text)
                0x00000000                __mp3_code_len = SIZEOF (.mp3_code)
                0x0009d400                __mp3_code_addr = LOADADDR (.mp3_code)
                0x00000000                __nes_text_len = SIZEOF (.nes_com_text)
                0x0009d400                __nes_text_addr = LOADADDR (.nes_com_text)
                0x00000000                __nes_code_len = SIZEOF (.nes_code)
                0x0009d400                __nes_code_addr = LOADADDR (.nes_code)
                0x00097400                _lcd_res_lma = LOADADDR (.lcd_resource)
                0x000029bc                _lcd_res_size = SIZEOF (.lcd_resource)
                0x00099dbc                _sensor_resource_start_addr = LOADADDR (.sensor_resource)
                0x00000000                _res_sensor_header_len = (_res_sensor_header_item_end - _res_sensor_header_item_start)
                0x0009d400                _nes_res_lma = LOADADDR (.nes_resource)
                0x00006ffc                PROVIDE (__stack, ((ORIGIN (ram_user) + 0x7000) - 0x4))
LOAD ..\lib\libboot.a
LOAD ..\lib\libmcu.a
LOAD ..\lib\libisp.a
LOAD ..\lib\libjpg.a
LOAD ..\lib\liblcd.a
LOAD ..\lib\libmultimedia.a
LOAD E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a
LOAD E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a
OUTPUT(bin\Debug\hx330x_sdk.exe elf32-or1k)

.comment        0x00000000       0x11
 .comment       0x00000000       0x11 obj\Debug\dev\battery\src\battery_api.o
                                 0x12 (size before relaxing)
 .comment       0x00000011       0x12 obj\Debug\dev\dev_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\diskio.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\ff.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\ffunicode.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\fs_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .comment       0x00000011       0x12 obj\Debug\dev\ir\src\ir_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\key\src\key_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .comment       0x00000011       0x12 obj\Debug\dev\led\src\led_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\nvfs\src\nvfs_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .comment       0x00000011       0x12 obj\Debug\dev\sd\src\sd_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_tab.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_enum.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_hub.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_adc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_auadc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_csi.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_dac.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_dmauart.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_eeprom.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_gpio.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_iic.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_int.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_lcdshow.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_md.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpAEncode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpBEncode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpDecode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_rtc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_spi.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_spi1.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_stream.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_sys.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_timer.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_uart.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_watermark.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_wdt.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xmbox.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xmsgq.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xos.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xwork.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\audio\audio_playback.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\audio\audio_record.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\image\image_decode.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\image\image_encode.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\video\video_playback.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\video\video_record.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\app_init.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\app_lcdshow.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\main.o
 .comment       0x00000011       0x12 obj\Debug\app\resource\user_res.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\msg_api.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_api.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\task_common.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_main\src\taskMain.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\windows_api.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\mbedtls_md5.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\user_config_api.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\user_config_tab.o
 .comment       0x00000011       0x12 ..\lib\libboot.a(boot.o)
 .comment       0x00000011       0x12 ..\lib\libboot.a(boot_lib.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_adc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_auadc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_csi.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dac.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dma.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dmauart.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_gpio.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_iic.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_int.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_isp.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_jpg.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcd.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdui.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_md.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_mipi.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_misc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_rtc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_sd.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_spi0.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_spi1.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_sys.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_timer.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_tminf.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_uart.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_usb.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_wdt.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_emi.o)
 .comment       0x00000011       0x12 ..\lib\libisp.a(hal_isp.o)
 .comment       0x00000011       0x12 ..\lib\libjpg.a(hal_jpg.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcd.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdMem.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdrotate.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdUi.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(lcd_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(api_multimedia.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_std_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_pcm.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_info     0x00000000     0x13ab
 .debug_info    0x00000000      0x113 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_info    0x00000113      0x131 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_info    0x00000244      0x117 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_info    0x0000035b      0x10e E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_info    0x00000469      0x71e E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_info    0x00000b87      0x76b E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_info    0x000012f2       0xb9 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_abbrev   0x00000000      0x5c4
 .debug_abbrev  0x00000000       0x7f E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_abbrev  0x0000007f       0xab E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_abbrev  0x0000012a       0x9f E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_abbrev  0x000001c9       0x92 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_abbrev  0x0000025b      0x170 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_abbrev  0x000003cb      0x19c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_abbrev  0x00000567       0x5d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_loc      0x00000000     0x2bbe
 .debug_loc     0x00000000      0x102 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_loc     0x00000102      0x2ed E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_loc     0x000003ef      0x1ba E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_loc     0x000005a9       0xc7 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_loc     0x00000670     0x1537 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_loc     0x00001ba7     0x1017 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)

.debug_aranges  0x00000000       0xd8
 .debug_aranges
                0x00000000       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_aranges
                0x00000020       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_aranges
                0x00000040       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_aranges
                0x00000060       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_aranges
                0x00000080       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_aranges
                0x000000a0       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_aranges
                0x000000c0       0x18 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_line     0x00000000      0x849
 .debug_line    0x00000000      0x15d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_line    0x0000015d      0x164 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_line    0x000002c1      0x16d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_line    0x0000042e       0xf5 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_line    0x00000523      0x15d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_line    0x00000680      0x163 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_line    0x000007e3       0x66 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_str      0x00000000      0x4dc
 .debug_str     0x00000000      0x14f E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                                0x188 (size before relaxing)
 .debug_str     0x0000014f       0x7b E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                                0x1af (size before relaxing)
 .debug_str     0x000001ca       0x68 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                                0x19c (size before relaxing)
 .debug_str     0x00000232       0x54 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                                0x1a3 (size before relaxing)
 .debug_str     0x00000286      0x1dc E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                                0x284 (size before relaxing)
 .debug_str     0x00000462        0xa E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                                0x284 (size before relaxing)
 .debug_str     0x0000046c       0x70 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                                0x1cb (size before relaxing)

.debug_frame    0x00000000       0xa0
 .debug_frame   0x00000000       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_frame   0x00000028       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_frame   0x00000050       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_frame   0x00000078       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)

.debug_ranges   0x00000000      0x320
 .debug_ranges  0x00000000      0x190 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_ranges  0x00000190      0x190 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
