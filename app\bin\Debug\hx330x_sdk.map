Archive member included to satisfy reference by file (symbol)

..\lib\libboot.a(boot.o)      obj\Debug\dev\battery\src\battery_api.o (boot_vddrtcCalculate)
..\lib\libboot.a(boot_loader.o)
                              obj\Debug\mcu\boot\spi_boot_cfg.o (.bootsect)
..\lib\libboot.a(reset.o)     obj\Debug\mcu\boot\spi_boot_cfg.o (_start)
..\lib\libboot.a(boot_lib.o)  ..\lib\libboot.a(boot_loader.o) (boot_sdram_init)
..\lib\libmcu.a(hx330x_adc.o)
                              obj\Debug\hal\src\hal_adc.o (hx330x_adcEnable)
..\lib\libmcu.a(hx330x_auadc.o)
                              obj\Debug\hal\src\hal_auadc.o (hx330x_auadcHalfIRQRegister)
..\lib\libmcu.a(hx330x_csi.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_csiInit)
..\lib\libmcu.a(hx330x_dac.o)
                              obj\Debug\hal\src\hal_dac.o (hx330x_dacTypeCfg)
..\lib\libmcu.a(hx330x_dma.o)
                              obj\Debug\app\app_common\src\app_lcdshow.o (hx330x_dmaNocWinA)
..\lib\libmcu.a(hx330x_dmauart.o)
                              obj\Debug\hal\src\hal_dmauart.o (hx330x_DmaUart_con_cfg)
..\lib\libmcu.a(hx330x_gpio.o)
                              ..\lib\libmcu.a(hx330x_csi.o) (hx330x_gpioSFRSet)
..\lib\libmcu.a(hx330x_iic.o)
                              obj\Debug\hal\src\hal_iic.o (hx330x_iic0Init)
..\lib\libmcu.a(hx330x_int.o)
                              ..\lib\libboot.a(reset.o) (fast_isr)
..\lib\libmcu.a(hx330x_isp.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_isp_mask_tab_cfg)
..\lib\libmcu.a(hx330x_isp_tab.o)
                              ..\lib\libmcu.a(hx330x_isp.o) (GAOS3X3_TAB)
..\lib\libmcu.a(hx330x_jpg.o)
                              obj\Debug\hal\src\hal_lcdshow.o (hx330x_mjpA_EncodeISRRegister)
..\lib\libmcu.a(hx330x_jpg_tab.o)
                              ..\lib\libmcu.a(hx330x_jpg.o) (hx330x_mjpA_table_init)
..\lib\libmcu.a(hx330x_lcd.o)
                              obj\Debug\dev\lcd\src\lcd_api.o (hx330x_lcdReset)
..\lib\libmcu.a(hx330x_lcdrotate.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_rotateIRQHandler)
..\lib\libmcu.a(hx330x_lcdui.o)
                              obj\Debug\dev\usb\dusb\src\dusb_msc.o (hx330x_lcdShowWaitDone)
..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_uiLzoIRQHandler)
..\lib\libmcu.a(hx330x_lcdwin.o)
                              obj\Debug\hal\src\hal_lcdshow.o (hx330x_lcdWinABConfig)
..\lib\libmcu.a(hx330x_md.o)  obj\Debug\hal\src\hal_md.o (hx330x_mdEnable)
..\lib\libmcu.a(hx330x_mipi.o)
                              obj\Debug\dev\sensor\src\sensor_api.o (hx330x_MipiCSIUinit)
..\lib\libmcu.a(hx330x_misc.o)
                              obj\Debug\dev\touchpanel\src\touchpanel_api.o (hx330x_abs)
..\lib\libmcu.a(hx330x_rtc.o)
                              obj\Debug\hal\src\hal_rtc.o (hx330x_rtcRamRead)
..\lib\libmcu.a(hx330x_sd.o)  obj\Debug\dev\sd\src\sd_api.o (hx330x_sd0Init)
..\lib\libmcu.a(hx330x_spi0.o)
                              obj\Debug\hal\src\hal_spi.o (hx330x_spi0ManualInit)
..\lib\libmcu.a(hx330x_spi1.o)
                              obj\Debug\hal\src\hal_spi1.o (hx330x_spi1_CS_Config)
..\lib\libmcu.a(hx330x_sys.o)
                              obj\Debug\dev\gsensor\src\gsensor_da380.o (hx330x_sysCpuMsDelay)
..\lib\libmcu.a(hx330x_timer.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_timer0IRQHandler)
..\lib\libmcu.a(hx330x_tminf.o)
                              obj\Debug\hal\src\hal_watermark.o (hx330x_mjpA_TimeinfoEnable)
..\lib\libmcu.a(hx330x_uart.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_uart0IRQHandler)
..\lib\libmcu.a(hx330x_usb.o)
                              obj\Debug\dev\usb\dusb\src\dusb_api.o (hx330x_usb20_CallbackRegister)
..\lib\libmcu.a(hx330x_wdt.o)
                              obj\Debug\dev\usb\dusb\src\dusb_msc.o (hx330x_wdtEnable)
..\lib\libmcu.a(hx330x_emi.o)
                              ..\lib\libmcu.a(hx330x_int.o) (hx330x_emiIRQHandler)
..\lib\libisp.a(hal_isp.o)    obj\Debug\dev\sensor\src\sensor_api.o (hal_sensor_fps_adpt)
..\lib\libjpg.a(hal_jpg.o)    obj\Debug\hal\src\hal_mjpAEncode.o (hal_mjp_enle_init)
..\lib\liblcd.a(hal_lcd.o)    obj\Debug\hal\src\hal_lcdshow.o (lcd_show_ctrl)
..\lib\liblcd.a(hal_lcdMem.o)
                              ..\lib\liblcd.a(hal_lcd.o) (hal_lcdAddrCalculate)
..\lib\liblcd.a(hal_lcdrotate.o)
                              ..\lib\liblcd.a(hal_lcd.o) (hal_rotateInit)
..\lib\liblcd.a(hal_lcdUi.o)  obj\Debug\app\app_common\src\app_lcdshow.o (hal_uiDrawBufMalloc)
..\lib\liblcd.a(hal_lcdUiLzo.o)
                              ..\lib\liblcd.a(hal_lcdUi.o) (hal_uiLzokick)
..\lib\liblcd.a(lcd_tab.o)    obj\Debug\dev\lcd\src\lcd_api.o (hal_lcdParaLoad)
..\lib\libmultimedia.a(api_multimedia.o)
                              obj\Debug\multimedia\audio\audio_playback.o (api_multimedia_init)
..\lib\libmultimedia.a(avi_dec.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_dec_func)
..\lib\libmultimedia.a(avi_odml_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_odml_enc_func)
..\lib\libmultimedia.a(avi_std_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (avi_std_enc_func)
..\lib\libmultimedia.a(wav_dec.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (wav_dec_func)
..\lib\libmultimedia.a(wav_enc.o)
                              ..\lib\libmultimedia.a(api_multimedia.o) (wav_enc_func)
..\lib\libmultimedia.a(wav_pcm.o)
                              ..\lib\libmultimedia.a(wav_enc.o) (pcm_encode)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                              obj\Debug\app\user_config\src\mbedtls_md5.o (memcmp)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                              obj\Debug\dev\sd\src\sd_api.o (memcpy)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                              obj\Debug\dev\fs\src\fs_api.o (memset)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                              obj\Debug\dev\gsensor\src\gsensor_api.o (strcpy)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                              obj\Debug\dev\fs\src\ff.o (__udivdi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                              obj\Debug\dev\fs\src\ff.o (__umoddi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
                              E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__udivsi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
                              E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__umodsi3)
E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                              E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o) (__clz_tab)

Allocating common symbols
Common symbol       size              file

flash_success       0x1               obj\Debug\app\app_common\src\main.o
WAV_TYPE_E          0x4               obj\Debug\dev\battery\src\battery_api.o
SysCtrl             0x130             obj\Debug\app\app_common\src\app_init.o
gsensor_ctl         0x8               obj\Debug\dev\gsensor\src\gsensor_api.o
RGB_GMMA_Tab        0x300             ..\lib\libmcu.a(hx330x_isp.o)
ui_draw_ctrl        0x10              obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
usb_dev_ctl         0x29c             obj\Debug\dev\usb\dusb\src\dusb_api.o
rc128k_div          0x4               ..\lib\libmcu.a(hx330x_rtc.o)
husb_ctl            0x1484            obj\Debug\dev\usb\husb\src\husb_api.o
rtcSecondISR        0x4               ..\lib\libmcu.a(hx330x_rtc.o)
task_play_audio_stat
                    0x4               obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
lcdshow_frame_op    0x1e8             ..\lib\liblcd.a(hal_lcdMem.o)
sd_update_op        0x64              obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
mediaVideoCtl       0x74              obj\Debug\multimedia\video\video_record.o
Y_GMA_Tab           0x200             ..\lib\libmcu.a(hx330x_isp.o)
rtcAlamISR          0x4               ..\lib\libmcu.a(hx330x_rtc.o)
usensor_handle      0x4               obj\Debug\dev\usb\husb\src\husb_usensor.o
USB_CH              0x4               obj\Debug\dev\battery\src\battery_api.o
UVC_CACHE_STA       0x4               obj\Debug\dev\battery\src\battery_api.o
fs_exfunc           0x14              obj\Debug\dev\fs\src\fs_api.o
SDCON0_T            0x4               obj\Debug\dev\battery\src\battery_api.o
usbDeviceOp         0xc               obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
XOSNesting          0x4               obj\Debug\mcu\xos\xos.o
smph_dmacopy        0x4               ..\lib\libmcu.a(hx330x_sys.o)
mainTaskOp          0x48              obj\Debug\app\task_windows\task_main\src\taskMain.o
SDCON1_T            0x4               obj\Debug\dev\battery\src\battery_api.o
tp_api_t            0x20              obj\Debug\dev\touchpanel\src\touchpanel_api.o
hx330x_lcdISR       0x14              ..\lib\libmcu.a(hx330x_lcd.o)
dev_key_tab         0x78              obj\Debug\dev\key\src\key_api.o
hx330x_timerISR     0x10              ..\lib\libmcu.a(hx330x_timer.o)
uhub_handle         0x4               obj\Debug\dev\usb\husb\src\husb_hub.o
UVC_FSTACK_STA      0x4               obj\Debug\dev\battery\src\battery_api.o
rc128k_rtc_cnt      0x4               ..\lib\libmcu.a(hx330x_rtc.o)
lcd_show_ctrl       0x84              ..\lib\liblcd.a(hal_lcd.o)
recordPhotoOp       0x20              obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
rc128k_timer_cnt    0x4               ..\lib\libmcu.a(hx330x_rtc.o)
playVideoOp         0x44              obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
CHANNEL_EXCHANGE_E  0x4               obj\Debug\dev\battery\src\battery_api.o

Memory Configuration

Name             Origin             Length             Attributes
boot             0x01fffc00         0x00000200
ram_boot         0x00000000         0x00006c00
ram_user         0x00000000         0x00007000
usb_ram          0x00008000         0x00008000
line_ram         0x00200000         0x00010000
mp3_text         0x00008000         0x00008000
mp3_ram          0x00200000         0x00010000
nes_text         0x00008000         0x00008000
nes_ram          0x00200000         0x00010000
sdram            0x02000000         0x00800000
flash            0x06000000         0x00800000
exsdram          0x00000000         0x00800000
*default*        0x00000000         0xffffffff

Linker script and memory map

LOAD obj\Debug\dev\battery\src\battery_api.o
LOAD obj\Debug\dev\dev_api.o
LOAD obj\Debug\dev\fs\src\diskio.o
LOAD obj\Debug\dev\fs\src\ff.o
LOAD obj\Debug\dev\fs\src\ffunicode.o
LOAD obj\Debug\dev\fs\src\fs_api.o
LOAD obj\Debug\dev\gsensor\src\gsensor_api.o
LOAD obj\Debug\dev\gsensor\src\gsensor_da380.o
LOAD obj\Debug\dev\gsensor\src\gsensor_gma301.o
LOAD obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
LOAD obj\Debug\dev\ir\src\ir_api.o
LOAD obj\Debug\dev\key\src\key_api.o
LOAD obj\Debug\dev\lcd\src\lcd_api.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
LOAD obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
LOAD obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
LOAD obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
LOAD obj\Debug\dev\led\src\led_api.o
LOAD obj\Debug\dev\led_pwm\src\led_pwm_api.o
LOAD obj\Debug\dev\nvfs\src\nvfs_api.o
LOAD obj\Debug\dev\nvfs\src\nvfs_jpg.o
LOAD obj\Debug\dev\sd\src\sd_api.o
LOAD obj\Debug\dev\sensor\src\sensor_api.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
LOAD obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
LOAD obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
LOAD obj\Debug\dev\sensor\src\sensor_tab.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_api.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_iic.o
LOAD obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_api.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_enum.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_msc.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_uac.o
LOAD obj\Debug\dev\usb\dusb\src\dusb_uvc.o
LOAD obj\Debug\dev\usb\husb\src\husb_api.o
LOAD obj\Debug\dev\usb\husb\src\husb_enum.o
LOAD obj\Debug\dev\usb\husb\src\husb_hub.o
LOAD obj\Debug\dev\usb\husb\src\husb_tpbulk.o
LOAD obj\Debug\dev\usb\husb\src\husb_usensor.o
LOAD obj\Debug\dev\usb\husb\src\husb_uvc.o
LOAD obj\Debug\hal\src\hal_adc.o
LOAD obj\Debug\hal\src\hal_auadc.o
LOAD obj\Debug\hal\src\hal_csi.o
LOAD obj\Debug\hal\src\hal_dac.o
LOAD obj\Debug\hal\src\hal_dmauart.o
LOAD obj\Debug\hal\src\hal_eeprom.o
LOAD obj\Debug\hal\src\hal_gpio.o
LOAD obj\Debug\hal\src\hal_iic.o
LOAD obj\Debug\hal\src\hal_int.o
LOAD obj\Debug\hal\src\hal_lcdshow.o
LOAD obj\Debug\hal\src\hal_md.o
LOAD obj\Debug\hal\src\hal_mjpAEncode.o
LOAD obj\Debug\hal\src\hal_mjpBEncode.o
LOAD obj\Debug\hal\src\hal_mjpDecode.o
LOAD obj\Debug\hal\src\hal_rtc.o
LOAD obj\Debug\hal\src\hal_spi.o
LOAD obj\Debug\hal\src\hal_spi1.o
LOAD obj\Debug\hal\src\hal_stream.o
LOAD obj\Debug\hal\src\hal_sys.o
LOAD obj\Debug\hal\src\hal_timer.o
LOAD obj\Debug\hal\src\hal_uart.o
LOAD obj\Debug\hal\src\hal_watermark.o
LOAD obj\Debug\hal\src\hal_wdt.o
LOAD obj\Debug\mcu\boot\spi_boot_cfg.o
LOAD obj\Debug\mcu\xos\xmbox.o
LOAD obj\Debug\mcu\xos\xmsgq.o
LOAD obj\Debug\mcu\xos\xos.o
LOAD obj\Debug\mcu\xos\xwork.o
LOAD obj\Debug\multimedia\audio\audio_playback.o
LOAD obj\Debug\multimedia\audio\audio_record.o
LOAD obj\Debug\multimedia\image\image_decode.o
LOAD obj\Debug\multimedia\image\image_encode.o
LOAD obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
LOAD obj\Debug\multimedia\video\video_playback.o
LOAD obj\Debug\multimedia\video\video_record.o
LOAD obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
LOAD obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
LOAD obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
LOAD obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
LOAD obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
LOAD obj\Debug\sys_manage\res_manage\res_manage_api.o
LOAD obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
LOAD obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
LOAD obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
LOAD obj\Debug\app\app_common\src\app_init.o
LOAD obj\Debug\app\app_common\src\app_lcdshow.o
LOAD obj\Debug\app\app_common\src\main.o
LOAD obj\Debug\app\resource\user_res.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
LOAD obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
LOAD obj\Debug\app\task_windows\msg_api.o
LOAD obj\Debug\app\task_windows\task_api.o
LOAD obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
LOAD obj\Debug\app\task_windows\task_common\src\task_common.o
LOAD obj\Debug\app\task_windows\task_common\src\task_common_msg.o
LOAD obj\Debug\app\task_windows\task_main\src\taskMain.o
LOAD obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
LOAD obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
LOAD obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
LOAD obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
LOAD obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
LOAD obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
LOAD obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
LOAD obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
LOAD obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
LOAD obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
LOAD obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
LOAD obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
LOAD obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
LOAD obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
LOAD obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
LOAD obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
LOAD obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
LOAD obj\Debug\app\task_windows\windows_api.o
LOAD obj\Debug\app\user_config\src\mbedtls_md5.o
LOAD obj\Debug\app\user_config\src\user_config_api.o
LOAD obj\Debug\app\user_config\src\user_config_tab.o
                0x00200000                __sdram_size = (boot_sdram_size == 0x1)?0x800000:0x200000

.bootsec        0x01fffc00      0x200 load address 0x00000000
 *(.bootsec)
 .bootsec       0x01fffc00      0x200 ..\lib\libboot.a(boot_loader.o)
                0x01fffc10                hex
                0x01fffc10                .hex
                0x01fffd1c                .bootsect

.boot_code      0x00000000     0x29a8 load address 0x00000200
                0x00000000                _boot_vma = .
 *(.vector)
 .vector        0x00000000      0x380 ..\lib\libboot.a(reset.o)
                0x000002a0                _start
                0x000002ec                _step_in
                0x00000330                _step_out
 *(.vector.kepttext)
 .vector.kepttext
                0x00000380       0x6c obj\Debug\dev\dev_api.o
                0x00000380                exception_lowpower_io_cfg
 .vector.kepttext
                0x000003ec      0x44c ..\lib\libboot.a(boot.o)
                0x000004d8                boot_vddrtcCalculate
                0x00000504                boot_putchar
                0x000005b0                boot_uart_puts
                0x00000604                exception
                0x0000081c                exception_trigger
 .vector.kepttext
                0x00000838       0x20 ..\lib\libboot.a(boot_lib.o)
                0x00000838                boot_getChipSN
 .vector.kepttext
                0x00000858      0x210 ..\lib\libmcu.a(hx330x_gpio.o)
                0x00000858                exception_gpioDataSet
                0x000008e4                hx330x_gpioDataGet
                0x00000968                hx330x_gpioCommonDataGet
                0x000009b0                exception_io1d1_softstart_clr
 .vector.kepttext
                0x00000a68      0x304 ..\lib\libmcu.a(hx330x_rtc.o)
                0x00000a68                hx330x_rtcWriteByte
                0x00000ab8                hx330x_rtcReadByte
                0x00000b1c                hx330x_rtcDataRead
                0x00000ba4                hx330x_rtcDataWrite
                0x00000c2c                hx330x_WKI1WakeupEnable
 .vector.kepttext
                0x00000d6c      0x1f0 ..\lib\libmcu.a(hx330x_sys.o)
                0x00000d6c                hx330x_sysCpuMsDelay
                0x00000dc0                hx330x_sysCpuNopDelay
                0x00000dfc                hx330x_bytes_memcpy
                0x00000e6c                hx330x_bytes_memset
                0x00000ec4                hx330x_bytes_cmp
 .vector.kepttext
                0x00000f5c       0x74 ..\lib\libmcu.a(hx330x_uart.o)
                0x00000f5c                hx330x_uart0SendByte
 .vector.kepttext
                0x00000fd0       0x84 ..\lib\libmcu.a(hx330x_wdt.o)
                0x00000fd0                hx330x_wdtEnable
                0x00001004                hx330x_wdtClear
                0x00001038                hx330x_wdtReset
 *(.vector.keptdata)
 .vector.keptdata
                0x00001054      0x148 obj\Debug\app\user_config\src\user_config_tab.o
                0x00001054                hardware_setup
 .vector.keptdata
                0x0000119c       0x3c ..\lib\libboot.a(boot.o)
                0x0000119c                vbg_param
 .vector.keptdata
                0x000011d8      0x100 ..\lib\libboot.a(reset.o)
                0x000011d8                _step_data
 .vector.keptdata
                0x000012d8        0x4 ..\lib\libboot.a(boot_lib.o)
 .vector.keptdata
                0x000012dc        0x4 ..\lib\libmcu.a(hx330x_gpio.o)
 .vector.keptdata
                0x000012e0        0x4 ..\lib\libmcu.a(hx330x_spi0.o)
                0x000012e0                spi_auto_mode
                0x000012e4                . = ALIGN (0x4)
                0x000012e4                _boot_kept_vma = .
 *(.vector.text)
 .vector.text   0x000012e4      0x8f4 ..\lib\libboot.a(boot.o)
                0x00001554                boot_vbg_pre_init
                0x000015ec                boot_vbg_back_init
                0x00001634                exception_init
                0x0000166c                bool_pll_init
                0x00001ae4                boot_clktun_check
                0x00001b70                boot_clktun_save
 .vector.text   0x00001bd8      0xbbc ..\lib\libboot.a(boot_lib.o)
                0x00001dc0                boot_sdram_init
 *(.vector.data)
 .vector.data   0x00002794        0x4 ..\lib\libboot.a(boot.o)
                0x00002794                vbg_adc
 .vector.data   0x00002798      0x210 ..\lib\libboot.a(boot_lib.o)
                0x00002798                tune_values
                0x00002918                tune_by
                0x00002924                tune_tab_2_clk
                0x00002944                tune_tab_1_clk
                0x00002964                tuning_test_addr
                0x00002984                tuning_test_data
                0x000029a4                SDRTUN2_CON

.ram            0x00000000     0x36d0
                0x000012e4                . = _boot_kept_vma
 *fill*         0x00000000     0x12e4 
                0x000012e4                __sram_start = .
 *(.sram_usb11fifo)
 .sram_usb11fifo
                0x000012e4      0x96c obj\Debug\hal\src\hal_sys.o
                0x000012e4                usb11_fifo
 *(.sram_comm)
 .sram_comm     0x00001c50      0xd60 obj\Debug\dev\fs\src\fs_api.o
 .sram_comm     0x000029b0      0x200 obj\Debug\dev\sd\src\sd_api.o
                0x000029b0                sd0RamBuffer
 .sram_comm     0x00002bb0       0x40 obj\Debug\hal\src\hal_dmauart.o
                0x00002bb0                dmauart_fifo
 .sram_comm     0x00002bf0      0x400 obj\Debug\hal\src\hal_mjpDecode.o
 .sram_comm     0x00002ff0       0x60 obj\Debug\mcu\xos\xmsgq.o
 .sram_comm     0x00003050      0x400 ..\lib\libmcu.a(hx330x_jpg.o)
                0x00003050                jpg_dri_tab
 .sram_comm     0x00003450      0x280 ..\lib\libisp.a(hal_isp.o)
                0x000036d0                __sram_end = .

.usb_ram        0x00008000     0x3630
                0x00008000                __ufifo_start = .
 *(.uram_usb20fifo)
 .uram_usb20fifo
                0x00008000     0x1d30 obj\Debug\hal\src\hal_sys.o
                0x00008000                usb20_fifo
 *(.uram_comm)
 .uram_comm     0x00009d30     0x1900 obj\Debug\hal\src\hal_watermark.o
                0x00009d30                tminf_font
                0x0000b630                __ufifo_end = .

.line_ram       0x00200000        0x0
                0x00200000                __line_start = .
 *(.lram_comm)
                0x00200000                __line_end = .

.on_sdram       0x02000000     0x9bf0 load address 0x00002c00
                0x02000000                _onsdram_start = .
 *(.sdram_text)
 .sdram_text    0x02000000       0x3c obj\Debug\dev\fs\src\ff.o
                0x02000000                clst2sect
 .sdram_text    0x0200003c       0x58 obj\Debug\dev\fs\src\fs_api.o
                0x0200003c                fs_getClustStartSector
 .sdram_text    0x02000094      0x174 obj\Debug\dev\nvfs\src\nvfs_jpg.o
                0x02000094                nv_jpg_write_by_linkmap
 .sdram_text    0x02000208      0x4f8 obj\Debug\dev\sd\src\sd_api.o
                0x020002a8                sd_api_Stop
                0x02000538                sd_api_Exist
                0x02000560                sd_api_Write
                0x02000640                sd_api_Read
 .sdram_text    0x02000700       0x3c obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .sdram_text    0x0200073c      0x260 obj\Debug\dev\usb\husb\src\husb_hub.o
 .sdram_text    0x0200099c     0x110c obj\Debug\dev\usb\husb\src\husb_uvc.o
                0x02001890                huvc_cache_dcd_down
                0x0200194c                husb_uvc_frame_read
 .sdram_text    0x02001aa8       0x3c obj\Debug\hal\src\hal_adc.o
                0x02001aa8                hal_adcGetChannel
 .sdram_text    0x02001ae4       0x48 obj\Debug\hal\src\hal_dac.o
 .sdram_text    0x02001b2c      0x104 obj\Debug\hal\src\hal_gpio.o
                0x02001b2c                hal_gpioInit
                0x02001bd4                hal_gpioEPullSet
 .sdram_text    0x02001c30      0x12c obj\Debug\hal\src\hal_lcdshow.o
                0x02001c30                hal_lcd_enc_kick
 .sdram_text    0x02001d5c      0x9f8 obj\Debug\hal\src\hal_spi.o
                0x02001d8c                hal_spiUpdata_led_show
                0x02001e44                hal_spiUpdata_led_show_init
                0x02001ec4                hal_spiUpdata_led_show_uinit
                0x02001f1c                hal_spiManualInit
                0x02001f48                hal_spiAutoModeInit
                0x02001f78                hal_spiModeSwitch
                0x02001ff4                hal_spiFlashReadID
                0x02002064                hal_spiFlashWriteEnable
                0x02002098                hal_spiFlashWait
                0x02002128                hal_spiFlashReadPage
                0x02002180                hal_spiFlashRead
                0x0200220c                hal_spiFlashWritePage
                0x020022a0                hal_spiFlashWrite
                0x02002380                hal_spiFlashWriteInManual
                0x02002414                hal_spiFlashEraseSector
                0x020024b8                hal_spiFlashEraseBlock
                0x02002538                hal_spiFlashEraseChip
                0x02002574                hal_spiFlashReadUniqueID
                0x020025fc                hal_spiFlashReadOTP
                0x020026a8                hal_spiFlashReadManual
 .sdram_text    0x02002754      0x460 obj\Debug\hal\src\hal_stream.o
                0x02002754                hal_streamMalloc
                0x0200286c                hal_streamIn
                0x0200294c                hal_streamOut
                0x02002a48                hal_streamOutNext
                0x02002b10                hal_streamfree
 .sdram_text    0x02002bb4       0x24 obj\Debug\hal\src\hal_timer.o
                0x02002bb4                hal_timerPWMStop
 .sdram_text    0x02002bd8      0x158 obj\Debug\hal\src\hal_uart.o
                0x02002bd8                uart_Printf
 .sdram_text    0x02002d30      0xc34 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02002d30                taskSdGetClst
                0x02002da4                taskSdReadBuf
                0x02002ee0                taskSdUpdateProcess
 .sdram_text    0x02003964      0x330 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                0x02003964                taskSdUpdateDrawStartAddrCal
                0x02003a3c                taskSdUpdateDrawAddrReCal
                0x02003ba8                taskSdUpdate_uiProgress
 .sdram_text    0x02003c94      0x11c ..\lib\libmcu.a(hx330x_adc.o)
                0x02003c94                hx330x_adcEnable
                0x02003cf0                hx330x_adcSetBaudrate
                0x02003d24                hx330x_adcConverStart
                0x02003d48                hx330x_adcRead
 .sdram_text    0x02003db0       0x90 ..\lib\libmcu.a(hx330x_auadc.o)
                0x02003db0                hx330x_auadcIRQHandler
 .sdram_text    0x02003e40      0x170 ..\lib\libmcu.a(hx330x_csi.o)
                0x02003e40                hx330x_csiIRQHandler
 .sdram_text    0x02003fb0      0x1e8 ..\lib\libmcu.a(hx330x_dac.o)
                0x02003fb0                hx330x_dacIRQHandler
                0x02004084                hx330x_dacBufferSet
                0x020040a4                hx330x_dacBufferFlush
                0x020040c4                hx330x_dacStart
                0x02004140                hx330x_check_dacstop
 .sdram_text    0x02004198       0xa4 ..\lib\libmcu.a(hx330x_dmauart.o)
                0x02004198                hx330x_uart1IRQHandler
 .sdram_text    0x0200423c      0x584 ..\lib\libmcu.a(hx330x_gpio.o)
                0x0200423c                hx330x_gpioDirSet
                0x020042e0                hx330x_gpioPullSet
                0x020043a0                hx330x_gpioPinPollSet
                0x020043ec                hx330x_gpioDrvSet
                0x02004478                hx330x_gpioDataSet
                0x02004504                hx330x_gpioPinDataSet
                0x02004550                hx330x_gpioMapSet
                0x020045dc                hx330x_gpioDigitalSet
                0x02004668                hx330x_gpioLedPull
                0x02004728                hx330x_gpioIRQHandler
 .sdram_text    0x020047c0      0x230 ..\lib\libmcu.a(hx330x_int.o)
                0x020047c0                hx330x_intHandler
                0x020047d4                fast_isr
                0x020048ac                slow_isr
                0x020049a8                hx330x_intEnable
 .sdram_text    0x020049f0      0x1a4 ..\lib\libmcu.a(hx330x_jpg.o)
                0x020049f0                hx330x_mjpA_EncodeLoadAddrGet
                0x02004a0c                hx330x_mjpA_EncodeStartAddrGet
                0x02004a28                hx330x_mjpA_EncodeDriTabGet
                0x02004a44                hx330x_mjpA_IRQHandler
                0x02004a90                hx330x_mjpB_IRQHandler
 .sdram_text    0x02004b94      0x17c ..\lib\libmcu.a(hx330x_lcd.o)
                0x02004b94                hx330x_lcdKick
                0x02004bb8                hx330x_lcdIRQHandler
 .sdram_text    0x02004d10       0x50 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                0x02004d10                hx330x_rotateIRQHandler
 .sdram_text    0x02004d60      0x470 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x02004d60                hx330x_lcdShowWaitDone
                0x02004dac                hx330x_lcdShowKick
                0x02004dd0                hx330x_lcdShowIRQHandler
                0x02004e30                hx330x_lcdVideoSetScanMode
                0x02004ec4                hx330x_lcdVideoSetAddr
                0x02004ee0                hx330x_lcdVideoSetStride
                0x02004f04                hx330x_lcdvideoSetPosition
                0x02004f28                hx330x_lcdUiEnable
                0x02004f7c                hx330x_lcdUiSetAddr
                0x02005030                hx330x_lcdVideoUpScaler_cfg
                0x02005128                hx330x_lcdVideoUpScalerSoftRotate_cfg
 .sdram_text    0x020051d0       0x4c ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                0x020051d0                hx330x_uiLzoIRQHandler
 .sdram_text    0x0200521c       0x60 ..\lib\libmcu.a(hx330x_misc.o)
                0x0200521c                hx330x_min
                0x02005240                hx330x_data_check
 .sdram_text    0x0200527c      0x224 ..\lib\libmcu.a(hx330x_rtc.o)
                0x0200527c                hx330x_rtcIRQHandler
                0x02005348                hx330x_WKOEnable
                0x020053c4                hx330x_WKI1WakeupTriger
                0x02005414                hx330x_WKI0WakeupTriger
                0x02005464                hx330x_WakeUpCleanPending
 .sdram_text    0x020054a0      0x554 ..\lib\libmcu.a(hx330x_sd.o)
                0x020054a0                hx330x_sd0SendCmd
                0x0200556c                hx330x_sd0Recv
                0x020055c8                hx330x_sd0Send
                0x02005600                hx330x_sd0WaitDAT0
                0x020056a0                hx330x_sd0WaitPend
                0x02005728                hx330x_sd0GetRsp
                0x02005744                hx330x_sd0CRCCheck
                0x020057a8                hx330x_sd1SendCmd
                0x02005874                hx330x_sd1Recv
                0x020058d0                hx330x_sd1Send
                0x02005908                hx330x_sd1WaitPend
                0x02005990                hx330x_sd1CRCCheck
 .sdram_text    0x020059f4      0x678 ..\lib\libmcu.a(hx330x_spi0.o)
                0x020059f4                hx330x_spi0PinConfig
                0x02005a74                hx330x_spi0ManualInit
                0x02005ba0                hx330x_spi0SendByte
                0x02005bf4                hx330x_spi0RecvByte
                0x02005c48                hx330x_spi0Send
                0x02005d00                hx330x_spi0Recv
                0x02005dc4                hx330x_spi0CS0Config
                0x02005e08                hx330x_spi0AutoModeInit
                0x02005ff4                hx330x_spi0ExitAutoMode
 .sdram_text    0x0200606c       0x64 ..\lib\libmcu.a(hx330x_spi1.o)
                0x0200606c                hx330x_spi1DMAIRQHandler
 .sdram_text    0x020060d0      0x63c ..\lib\libmcu.a(hx330x_sys.o)
                0x020060d0                table_init_sfr
                0x02006150                hx330x_sysDcacheWback
                0x020061ec                hx330x_sysDcacheFlush
                0x02006288                hx330x_sysClkSet
                0x020062e4                hx330x_mcpy0_sdram2gram
                0x020063c0                hx330x_mcpy0_sdram2gram_nocache
                0x02006468                hx330x_mcpy1_sdram2gram_nocache_waitdone
                0x020064dc                hx330x_mcpy1_sdram2gram_nocache_kick
                0x0200658c                hx330x_mcpy1_sdram2gram
                0x02006658                hx330x_mcpy1_sdram2gram_nocache
 .sdram_text    0x0200670c      0x1fc ..\lib\libmcu.a(hx330x_timer.o)
                0x0200670c                hx330x_timer0IRQHandler
                0x02006750                hx330x_timer1IRQHandler
                0x02006794                hx330x_timer2IRQHandler
                0x020067d8                hx330x_timer3IRQHandler
                0x0200681c                hx330x_timerTickStart
                0x02006850                hx330x_timerTickStop
                0x0200686c                hx330x_timerTickCount
                0x02006888                hx330x_timerPWMStop
 .sdram_text    0x02006908       0x64 ..\lib\libmcu.a(hx330x_uart.o)
                0x02006908                hx330x_uart0IRQHandler
 .sdram_text    0x0200696c      0x76c ..\lib\libmcu.a(hx330x_usb.o)
                0x020069f4                hx330x_usb20_Func_Call
                0x02006a20                hx330x_bulk20_tx
                0x02006be4                hx330x_bulk20_rx
                0x02006d64                hx330x_usb20DevIRQHanlder
                0x02006e9c                hx330x_usb20_hostIRQHanlder
                0x02006fa4                hx330x_usb11_Func_Call
                0x02006fd0                hx330x_usb11_hostIRQHanlder
 .sdram_text    0x020070d8       0x5c ..\lib\libmcu.a(hx330x_emi.o)
                0x020070d8                hx330x_emiIRQHandler
 .sdram_text    0x02007134       0x68 ..\lib\libjpg.a(hal_jpg.o)
                0x02007134                hal_mjp_enle_tab_get
 .sdram_text    0x0200719c      0x358 ..\lib\liblcd.a(hal_lcd.o)
 .sdram_text    0x020074f4      0x228 ..\lib\liblcd.a(hal_lcdMem.o)
                0x020074f4                hal_lcdAddrCalculate
                0x020076b8                hal_dispframeFree
 .sdram_text    0x0200771c      0x18c ..\lib\liblcd.a(hal_lcdUi.o)
                0x0200771c                hal_uiBuffFree
                0x02007744                hal_lcdUiEnable
                0x0200779c                hal_lcdUiBuffFlush
 *(.sdram_code)
 .sdram_code    0x020078a8      0x110 ..\lib\libmcu.a(hx330x_spi0.o)
                0x020078a8                spi0PinCfg_tab
                0x020078b8                SPI0_4_LINE_tab
                0x020078f8                SPI0_2_LINE1_tab
                0x02007938                SPI0_2_LINE0_tab
                0x02007978                SPI0_1_LINE_tab
 *(.sdram_data)
 *(.data*)
 .data          0x020079b8        0x0 obj\Debug\dev\battery\src\battery_api.o
 .data          0x020079b8      0x120 obj\Debug\dev\dev_api.o
                0x020079b8                dev_node
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\diskio.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\ff.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\ffunicode.o
 .data          0x02007ad8        0x0 obj\Debug\dev\fs\src\fs_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .data          0x02007ad8        0x0 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .data          0x02007ad8        0x0 obj\Debug\dev\ir\src\ir_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\key\src\key_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .data          0x02007ad8        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .data          0x02007ad8        0x0 obj\Debug\dev\led\src\led_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\nvfs\src\nvfs_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sd\src\sd_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_api.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .data          0x02007ad8        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .data          0x02007ad8        0x4 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .data          0x02007adc        0x0 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .data          0x02007adc        0x0 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .data          0x02007adc      0x208 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x02007adc                dusb_com_cfgdsc
                0x02007cc4                dusb_msc_cfgdsc
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_api.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_enum.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_hub.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .data          0x02007ce4        0x0 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_adc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_auadc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_csi.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_dac.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_dmauart.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_eeprom.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_gpio.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_iic.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_int.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_lcdshow.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_md.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpAEncode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpBEncode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_mjpDecode.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_rtc.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_spi.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_spi1.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_stream.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_sys.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_timer.o
 .data          0x02007ce4        0x0 obj\Debug\hal\src\hal_uart.o
 .data          0x02007ce4       0x48 obj\Debug\hal\src\hal_watermark.o
 .data          0x02007d2c        0x0 obj\Debug\hal\src\hal_wdt.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xmbox.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xmsgq.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xos.o
 .data          0x02007d2c        0x0 obj\Debug\mcu\xos\xwork.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\audio\audio_playback.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\audio\audio_record.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\image\image_decode.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\image\image_encode.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\video\video_playback.o
 .data          0x02007d2c        0x0 obj\Debug\multimedia\video\video_record.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .data          0x02007d2c        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .data          0x02007d2c        0x0 obj\Debug\app\app_common\src\app_init.o
 .data          0x02007d2c        0x8 obj\Debug\app\app_common\src\app_lcdshow.o
 .data          0x02007d34        0x0 obj\Debug\app\app_common\src\main.o
 .data          0x02007d34      0xb7c obj\Debug\app\resource\user_res.o
                0x02007d34                User_Icon_Table
                0x02008154                User_String_Table
 .data          0x020088b0        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .data          0x020088b0       0x48 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
                0x020088b0                menuplayBack
                0x020088bc                menuPageplayBack
                0x020088d0                menuItemplayBack
 .data          0x020088f8        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .data          0x020088f8      0x2a0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                0x020088f8                menurecord
                0x02008904                menuPagerecord
                0x02008918                menuItemrecord
                0x020089b8                menuOptionversion
                0x020089c0                menuOptionscreenSave
                0x020089e0                menuOptionirLed
                0x020089f8                menuOptionfrequency
                0x02008a08                menuOptionlanguage
                0x02008a78                menuOptionautoPowerOff
                0x02008a90                menuOptionkeySound
                0x02008aa0                menuOptiongsensor
                0x02008ac0                menuOptiontimeStamp
                0x02008ad0                menuOptionparking
                0x02008ae0                menuOptionaudio
                0x02008af0                menuOptionmd
                0x02008b00                menuOptionev
                0x02008b28                menuOptionawb
                0x02008b50                menuOptionloopRecord
                0x02008b70                menuOptionphotoResolution
                0x02008b88                menuOptionvideoResolution
 .data          0x02008b98       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                0x02008b98                dateTimeWindow
                0x02008ba8                dateTimeMsgDeal
 .data          0x02008c08       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                0x02008c08                defaultWindow
                0x02008c18                defaultMsgDeal
 .data          0x02008c70       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                0x02008c70                delAllWindow
                0x02008c80                delAllMsgDeal
 .data          0x02008cd8       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                0x02008cd8                delCurWindow
                0x02008ce8                delCurMsgDeal
 .data          0x02008d40       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                0x02008d40                formatWindow
                0x02008d50                formatMsgDeal
 .data          0x02008da8       0x98 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x02008db0                menuItemWindow
                0x02008dc0                menuItemMsgDeal
 .data          0x02008e40       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                0x02008e40                lockCurWindow
                0x02008e50                lockCurMsgDeal
 .data          0x02008ea8       0x70 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                0x02008ea8                menuOptionWindow
                0x02008eb8                menuOptionMsgDeal
 .data          0x02008f18       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                0x02008f18                unlockAllWindow
                0x02008f28                unlockAllMsgDeal
 .data          0x02008f80       0x68 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                0x02008f80                unlockCurWindow
                0x02008f90                unlockCurMsgDeal
 .data          0x02008fe8        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .data          0x02008fe8       0x38 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                0x02008fe8                asternWindow
                0x02008ff8                asternMsgDeal
 .data          0x02009020       0x68 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                0x02009020                noFileWindow
                0x02009030                noFileMsgDeal
 .data          0x02009088       0x98 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                0x02009088                selfTestWindow
                0x02009098                selfTestMsgDeal
 .data          0x02009120       0xcc obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                0x02009120                tips1Window
                0x02009130                tips1MsgDeal
 .data          0x020091ec       0xcc obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                0x020091ec                tipsWindow
                0x020091fc                tipsMsgDeal
 .data          0x020092b8       0x5c obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                0x020092b8                takephotoiconWindow
                0x020092c8                TpIconMsgDeal
 .data          0x02009314        0x0 obj\Debug\app\task_windows\msg_api.o
 .data          0x02009314        0x0 obj\Debug\app\task_windows\task_api.o
 .data          0x02009314        0x0 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .data          0x02009314       0x4c obj\Debug\app\task_windows\task_common\src\task_common.o
 .data          0x02009360        0x0 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .data          0x02009360       0x14 obj\Debug\app\task_windows\task_main\src\taskMain.o
                0x02009360                taskMain
 .data          0x02009374       0x50 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
                0x02009374                mainWindow
                0x02009384                mainMsgDeal
 .data          0x020093c4       0x14 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x020093c4                taskPlayAudio
 .data          0x020093d8       0x80 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                0x020093d8                playAudioWindow
                0x020093e8                playAudioMsgDeal
 .data          0x02009458       0x14 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x02009458                taskPlayVideo
 .data          0x0200946c       0xf8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                0x02009494                playVideoMainWindow
                0x020094a4                playVideoMainMsgDeal
 .data          0x02009564       0x9c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                0x02009570                playVideoSlideWindow
                0x02009580                playVideoSlideMsgDeal
 .data          0x02009600       0x88 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                0x02009600                playVideoThumbnallWindow
                0x02009610                playVideoThumbnallMsgDeal
 .data          0x02009688       0x14 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
                0x02009688                taskPowerOff
 .data          0x0200969c       0x14 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
                0x0200969c                taskRecordAudio
 .data          0x020096b0       0x50 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                0x020096b0                RecordAudioWindow
                0x020096c0                recordAudioMsgDeal
 .data          0x02009700       0x14 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x02009700                taskRecordPhoto
 .data          0x02009714      0x260 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                0x02009714                recordPhotoWindow
                0x02009724                photoEncodeMsgDeal
                0x020097e4                recordPhotoWin
 .data          0x02009974       0x14 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                0x02009974                taskRecordVideo
 .data          0x02009988       0xe8 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                0x02009988                recordVideoWindow
                0x02009998                recordVideoMsgDeal
 .data          0x02009a70       0x14 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02009a70                taskSDUpdate
 .data          0x02009a84        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .data          0x02009a84       0x14 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
                0x02009a84                taskShowLogo
 .data          0x02009a98       0x30 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                0x02009a98                ShowLogoWindow
                0x02009aa8                ShowLogoMsgDeal
 .data          0x02009ac8       0x14 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                0x02009ac8                taskUSBDevice
 .data          0x02009adc       0xb8 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                0x02009adc                usbDeviceWindow
                0x02009aec                usbDeviceMsgDeal
 .data          0x02009b94        0x0 obj\Debug\app\task_windows\windows_api.o
 .data          0x02009b94        0x8 obj\Debug\app\user_config\src\mbedtls_md5.o
                0x02009b94                MY_KEY
 .data          0x02009b9c        0x0 obj\Debug\app\user_config\src\user_config_api.o
 .data          0x02009b9c        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .data          0x02009b9c        0x0 ..\lib\libboot.a(boot.o)
 .data          0x02009b9c        0x0 ..\lib\libboot.a(boot_loader.o)
 .data          0x02009b9c        0x0 ..\lib\libboot.a(reset.o)
 .data          0x02009b9c        0x0 ..\lib\libboot.a(boot_lib.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_auadc.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_csi.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_dac.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_dma.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_dmauart.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_gpio.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_iic.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_int.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_isp.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .data          0x02009b9c        0x0 ..\lib\libmcu.a(hx330x_jpg.o)
 .data          0x02009b9c        0x1 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 *fill*         0x02009b9d        0x3 
 .data          0x02009ba0        0x4 ..\lib\libmcu.a(hx330x_lcd.o)
 .data          0x02009ba4        0x0 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .data          0x02009ba4        0x8 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x02009ba4                video_scanmode_tab
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_md.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_mipi.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_misc.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_rtc.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_sd.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_spi1.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_sys.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_timer.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_tminf.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_uart.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_usb.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_wdt.o)
 .data          0x02009bac        0x0 ..\lib\libmcu.a(hx330x_emi.o)
 .data          0x02009bac        0x4 ..\lib\libisp.a(hal_isp.o)
 .data          0x02009bb0        0x0 ..\lib\libjpg.a(hal_jpg.o)
 .data          0x02009bb0       0x40 ..\lib\liblcd.a(hal_lcd.o)
 .data          0x02009bf0        0x0 ..\lib\liblcd.a(hal_lcdMem.o)
 .data          0x02009bf0        0x0 ..\lib\liblcd.a(hal_lcdrotate.o)
 .data          0x02009bf0        0x0 ..\lib\liblcd.a(hal_lcdUi.o)
 .data          0x02009bf0        0x0 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .data          0x02009bf0        0x0 ..\lib\liblcd.a(lcd_tab.o)
 .data          0x02009bf0        0x0 ..\lib\libmultimedia.a(api_multimedia.o)
 .data          0x02009bf0        0x0 ..\lib\libmultimedia.a(avi_dec.o)
 .data          0x02009bf0        0x0 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .data          0x02009bf0        0x0 ..\lib\libmultimedia.a(avi_std_enc.o)
 .data          0x02009bf0        0x0 ..\lib\libmultimedia.a(wav_dec.o)
 .data          0x02009bf0        0x0 ..\lib\libmultimedia.a(wav_enc.o)
 .data          0x02009bf0        0x0 ..\lib\libmultimedia.a(wav_pcm.o)
 .data          0x02009bf0        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .data          0x02009bf0        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .data          0x02009bf0        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .data          0x02009bf0        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .data          0x02009bf0        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .data          0x02009bf0        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .data          0x02009bf0        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
 .data          0x02009bf0        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
 .data          0x02009bf0        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.bss            0x02009bf0    0x12b64 load address 0x0000c7f0
                0x02009bf0                __bss_start = .
 *(.bss*)
 .bss           0x02009bf0        0x8 obj\Debug\dev\battery\src\battery_api.o
 .bss           0x02009bf8        0x8 obj\Debug\dev\dev_api.o
 .bss           0x02009c00        0x0 obj\Debug\dev\fs\src\diskio.o
 .bss           0x02009c00      0x468 obj\Debug\dev\fs\src\ff.o
 .bss           0x0200a068        0x0 obj\Debug\dev\fs\src\ffunicode.o
 .bss           0x0200a068        0x0 obj\Debug\dev\fs\src\fs_api.o
 .bss           0x0200a068        0x4 obj\Debug\dev\gsensor\src\gsensor_api.o
 .bss           0x0200a06c        0x0 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .bss           0x0200a06c        0x0 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .bss           0x0200a06c        0x0 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .bss           0x0200a06c        0x4 obj\Debug\dev\ir\src\ir_api.o
 .bss           0x0200a070       0x1c obj\Debug\dev\key\src\key_api.o
 .bss           0x0200a08c       0xbc obj\Debug\dev\lcd\src\lcd_api.o
                0x0200a08c                lcd_saj_nocolor
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .bss           0x0200a148        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .bss           0x0200a148        0x4 obj\Debug\dev\led\src\led_api.o
 .bss           0x0200a14c        0x4 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .bss           0x0200a150       0x18 obj\Debug\dev\nvfs\src\nvfs_api.o
 .bss           0x0200a168       0x10 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .bss           0x0200a178       0x2c obj\Debug\dev\sd\src\sd_api.o
                0x0200a178                hal_sdc_speed
 .bss           0x0200a1a4     0x1218 obj\Debug\dev\sensor\src\sensor_api.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .bss           0x0200b3bc        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .bss           0x0200b3bc        0x8 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .bss           0x0200b3c4        0x0 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .bss           0x0200b3c4        0x4 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .bss           0x0200b3c8       0x1c obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .bss           0x0200b3e4        0x4 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .bss           0x0200b3e8        0x0 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .bss           0x0200b3e8        0x0 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .bss           0x0200b3e8        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .bss           0x0200b3e8        0x0 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .bss           0x0200b3e8        0x2 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 *fill*         0x0200b3ea        0x2 
 .bss           0x0200b3ec        0xc obj\Debug\dev\usb\husb\src\husb_api.o
 .bss           0x0200b3f8        0x0 obj\Debug\dev\usb\husb\src\husb_enum.o
 .bss           0x0200b3f8        0x0 obj\Debug\dev\usb\husb\src\husb_hub.o
 .bss           0x0200b3f8        0x0 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .bss           0x0200b3f8        0x4 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .bss           0x0200b3fc        0x0 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .bss           0x0200b3fc        0x0 obj\Debug\hal\src\hal_adc.o
 .bss           0x0200b3fc       0xc8 obj\Debug\hal\src\hal_auadc.o
                0x0200b3fc                auadccnt
 .bss           0x0200b4c4        0x4 obj\Debug\hal\src\hal_csi.o
 .bss           0x0200b4c8        0x8 obj\Debug\hal\src\hal_dac.o
 .bss           0x0200b4d0       0x50 obj\Debug\hal\src\hal_dmauart.o
 .bss           0x0200b520        0x0 obj\Debug\hal\src\hal_eeprom.o
 .bss           0x0200b520        0x0 obj\Debug\hal\src\hal_gpio.o
 .bss           0x0200b520        0x1 obj\Debug\hal\src\hal_iic.o
 .bss           0x0200b521        0x0 obj\Debug\hal\src\hal_int.o
 *fill*         0x0200b521        0x3 
 .bss           0x0200b524        0x8 obj\Debug\hal\src\hal_lcdshow.o
 .bss           0x0200b52c        0x4 obj\Debug\hal\src\hal_md.o
 .bss           0x0200b530      0x3b4 obj\Debug\hal\src\hal_mjpAEncode.o
 .bss           0x0200b8e4      0x3a0 obj\Debug\hal\src\hal_mjpBEncode.o
 .bss           0x0200bc84       0x74 obj\Debug\hal\src\hal_mjpDecode.o
 .bss           0x0200bcf8       0x34 obj\Debug\hal\src\hal_rtc.o
 .bss           0x0200bd2c        0x8 obj\Debug\hal\src\hal_spi.o
                0x0200bd2c                spi_updata_led
 .bss           0x0200bd34       0x10 obj\Debug\hal\src\hal_spi1.o
 .bss           0x0200bd44        0x0 obj\Debug\hal\src\hal_stream.o
 .bss           0x0200bd44      0x50c obj\Debug\hal\src\hal_sys.o
 .bss           0x0200c250        0x0 obj\Debug\hal\src\hal_timer.o
 .bss           0x0200c250        0x8 obj\Debug\hal\src\hal_uart.o
 .bss           0x0200c258        0x8 obj\Debug\hal\src\hal_watermark.o
 .bss           0x0200c260        0x0 obj\Debug\hal\src\hal_wdt.o
 .bss           0x0200c260        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .bss           0x0200c260        0x0 obj\Debug\mcu\xos\xmbox.o
 .bss           0x0200c260        0x0 obj\Debug\mcu\xos\xmsgq.o
 .bss           0x0200c260        0x8 obj\Debug\mcu\xos\xos.o
 .bss           0x0200c268       0x80 obj\Debug\mcu\xos\xwork.o
 .bss           0x0200c2e8       0x60 obj\Debug\multimedia\audio\audio_playback.o
 .bss           0x0200c348       0x48 obj\Debug\multimedia\audio\audio_record.o
 .bss           0x0200c390        0x0 obj\Debug\multimedia\image\image_decode.o
 .bss           0x0200c390        0x0 obj\Debug\multimedia\image\image_encode.o
 .bss           0x0200c390        0x0 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .bss           0x0200c390      0x1c8 obj\Debug\multimedia\video\video_playback.o
 .bss           0x0200c558        0x0 obj\Debug\multimedia\video\video_record.o
 .bss           0x0200c558        0xc obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .bss           0x0200c564        0x0 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .bss           0x0200c564        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .bss           0x0200c564        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .bss           0x0200c564        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .bss           0x0200c564        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .bss           0x0200c564        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .bss           0x0200c564      0x530 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .bss           0x0200ca94       0x94 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .bss           0x0200cb28        0x0 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .bss           0x0200cb28        0x0 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .bss           0x0200cb28       0x60 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .bss           0x0200cb88        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .bss           0x0200cb88     0x5068 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .bss           0x02011bf0        0x4 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .bss           0x02011bf4        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .bss           0x02011bf4        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .bss           0x02011bf4        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .bss           0x02011bf4        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .bss           0x02011bf4        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .bss           0x02011bf4        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .bss           0x02011bf4        0x0 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .bss           0x02011bf4       0x50 obj\Debug\app\app_common\src\app_init.o
 .bss           0x02011c44       0x10 obj\Debug\app\app_common\src\app_lcdshow.o
 .bss           0x02011c54        0x0 obj\Debug\app\app_common\src\main.o
 .bss           0x02011c54        0x0 obj\Debug\app\resource\user_res.o
 .bss           0x02011c54        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .bss           0x02011c54        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .bss           0x02011c54        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .bss           0x02011c54        0x8 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
                0x02011c54                menuOptionmemory
 .bss           0x02011c5c       0x48 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .bss           0x02011ca4        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .bss           0x02011ca4        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .bss           0x02011ca4        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .bss           0x02011ca4        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .bss           0x02011ca4        0x8 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
 .bss           0x02011cac        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .bss           0x02011cac        0x4 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .bss           0x02011cb0        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .bss           0x02011cb0        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .bss           0x02011cb0        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .bss           0x02011cb0        0x0 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .bss           0x02011cb0        0x0 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .bss           0x02011cb0        0xc obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .bss           0x02011cbc        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .bss           0x02011cc0        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .bss           0x02011cc4        0x4 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .bss           0x02011cc8      0x130 obj\Debug\app\task_windows\msg_api.o
 .bss           0x02011df8       0x40 obj\Debug\app\task_windows\task_api.o
 .bss           0x02011e38        0x1 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 *fill*         0x02011e39        0x3 
 .bss           0x02011e3c       0x3c obj\Debug\app\task_windows\task_common\src\task_common.o
 .bss           0x02011e78        0x0 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .bss           0x02011e78        0x0 obj\Debug\app\task_windows\task_main\src\taskMain.o
 .bss           0x02011e78        0x0 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
 .bss           0x02011e78        0x0 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .bss           0x02011e78        0x4 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .bss           0x02011e7c        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .bss           0x02011e7c        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .bss           0x02011e7c        0x0 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .bss           0x02011e7c        0xc obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .bss           0x02011e88        0x0 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .bss           0x02011e88        0x0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .bss           0x02011e88        0x0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .bss           0x02011e88        0x0 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .bss           0x02011e88        0x8 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .bss           0x02011e90        0x0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
 .bss           0x02011e90        0x8 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .bss           0x02011e98        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .bss           0x02011e98        0x0 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .bss           0x02011e98        0x0 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .bss           0x02011e98        0x1 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .bss           0x02011e99        0x0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .bss           0x02011e99        0x0 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .bss           0x02011e99        0x0 obj\Debug\app\task_windows\windows_api.o
 .bss           0x02011e99        0x0 obj\Debug\app\user_config\src\mbedtls_md5.o
 *fill*         0x02011e99        0x3 
 .bss           0x02011e9c      0x20c obj\Debug\app\user_config\src\user_config_api.o
 .bss           0x020120a8        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .bss           0x020120a8        0x0 ..\lib\libboot.a(boot.o)
 .bss           0x020120a8        0x0 ..\lib\libboot.a(boot_loader.o)
 .bss           0x020120a8        0x0 ..\lib\libboot.a(reset.o)
 .bss           0x020120a8        0x0 ..\lib\libboot.a(boot_lib.o)
 .bss           0x020120a8        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .bss           0x020120a8        0x8 ..\lib\libmcu.a(hx330x_auadc.o)
 .bss           0x020120b0       0x3c ..\lib\libmcu.a(hx330x_csi.o)
 .bss           0x020120ec        0x4 ..\lib\libmcu.a(hx330x_dac.o)
 .bss           0x020120f0        0x0 ..\lib\libmcu.a(hx330x_dma.o)
 .bss           0x020120f0        0x4 ..\lib\libmcu.a(hx330x_dmauart.o)
 .bss           0x020120f4       0x6c ..\lib\libmcu.a(hx330x_gpio.o)
 .bss           0x02012160        0x0 ..\lib\libmcu.a(hx330x_iic.o)
 .bss           0x02012160        0x0 ..\lib\libmcu.a(hx330x_int.o)
 .bss           0x02012160        0xc ..\lib\libmcu.a(hx330x_isp.o)
                0x02012160                isp_ccf_dn_tab
                0x02012164                isp_ee_dn_tab
                0x02012168                isp_ee_sharp_tab
 .bss           0x0201216c        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .bss           0x0201216c       0x54 ..\lib\libmcu.a(hx330x_jpg.o)
                0x0201216c                mjpBEncAvgSize
                0x02012170                mjpAEncAvgSize
 .bss           0x020121c0        0x0 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 .bss           0x020121c0        0x0 ..\lib\libmcu.a(hx330x_lcd.o)
 .bss           0x020121c0        0x4 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .bss           0x020121c4        0x8 ..\lib\libmcu.a(hx330x_lcdui.o)
 .bss           0x020121cc        0x4 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .bss           0x020121d0        0x0 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .bss           0x020121d0        0x0 ..\lib\libmcu.a(hx330x_md.o)
 .bss           0x020121d0        0x0 ..\lib\libmcu.a(hx330x_mipi.o)
 .bss           0x020121d0        0x0 ..\lib\libmcu.a(hx330x_misc.o)
 .bss           0x020121d0        0x4 ..\lib\libmcu.a(hx330x_rtc.o)
                0x020121d0                rtcAlarmFlag
 .bss           0x020121d4       0x10 ..\lib\libmcu.a(hx330x_sd.o)
 .bss           0x020121e4        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .bss           0x020121e4        0x4 ..\lib\libmcu.a(hx330x_spi1.o)
 .bss           0x020121e8        0x4 ..\lib\libmcu.a(hx330x_sys.o)
                0x020121e8                mcp1_lock
 .bss           0x020121ec        0x0 ..\lib\libmcu.a(hx330x_timer.o)
 .bss           0x020121ec       0x28 ..\lib\libmcu.a(hx330x_tminf.o)
 .bss           0x02012214        0x4 ..\lib\libmcu.a(hx330x_uart.o)
 .bss           0x02012218       0x7c ..\lib\libmcu.a(hx330x_usb.o)
 .bss           0x02012294        0x0 ..\lib\libmcu.a(hx330x_wdt.o)
 .bss           0x02012294        0x4 ..\lib\libmcu.a(hx330x_emi.o)
 .bss           0x02012298       0xe0 ..\lib\libisp.a(hal_isp.o)
 .bss           0x02012378       0x4c ..\lib\libjpg.a(hal_jpg.o)
 .bss           0x020123c4       0x16 ..\lib\liblcd.a(hal_lcd.o)
 .bss           0x020123da        0x0 ..\lib\liblcd.a(hal_lcdMem.o)
 *fill*         0x020123da        0x2 
 .bss           0x020123dc       0x28 ..\lib\liblcd.a(hal_lcdrotate.o)
 .bss           0x02012404       0x28 ..\lib\liblcd.a(hal_lcdUi.o)
 .bss           0x0201242c        0x8 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .bss           0x02012434        0x0 ..\lib\liblcd.a(lcd_tab.o)
 .bss           0x02012434       0x60 ..\lib\libmultimedia.a(api_multimedia.o)
 .bss           0x02012494        0x0 ..\lib\libmultimedia.a(avi_dec.o)
 .bss           0x02012494        0x0 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .bss           0x02012494        0x0 ..\lib\libmultimedia.a(avi_std_enc.o)
 .bss           0x02012494        0x0 ..\lib\libmultimedia.a(wav_dec.o)
 .bss           0x02012494        0x0 ..\lib\libmultimedia.a(wav_enc.o)
 .bss           0x02012494        0x0 ..\lib\libmultimedia.a(wav_pcm.o)
 .bss           0x02012494        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .bss           0x02012494        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .bss           0x02012494        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .bss           0x02012494        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .bss           0x02012494        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .bss           0x02012494        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .bss           0x02012494        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
 .bss           0x02012494        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
 .bss           0x02012494        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
 *(COMMON)
 COMMON         0x02012494       0x1c obj\Debug\dev\battery\src\battery_api.o
                0x02012494                WAV_TYPE_E
                0x02012498                USB_CH
                0x0201249c                UVC_CACHE_STA
                0x020124a0                SDCON0_T
                0x020124a4                SDCON1_T
                0x020124a8                UVC_FSTACK_STA
                0x020124ac                CHANNEL_EXCHANGE_E
 COMMON         0x020124b0       0x14 obj\Debug\dev\fs\src\fs_api.o
                0x020124b0                fs_exfunc
 COMMON         0x020124c4        0x8 obj\Debug\dev\gsensor\src\gsensor_api.o
                0x020124c4                gsensor_ctl
 COMMON         0x020124cc       0x78 obj\Debug\dev\key\src\key_api.o
                0x020124cc                dev_key_tab
 COMMON         0x02012544       0x20 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                0x02012544                tp_api_t
 COMMON         0x02012564      0x29c obj\Debug\dev\usb\dusb\src\dusb_api.o
                0x02012564                usb_dev_ctl
 COMMON         0x02012800     0x1484 obj\Debug\dev\usb\husb\src\husb_api.o
                0x02012800                husb_ctl
 COMMON         0x02013c84        0x4 obj\Debug\dev\usb\husb\src\husb_hub.o
                0x02013c84                uhub_handle
 COMMON         0x02013c88        0x4 obj\Debug\dev\usb\husb\src\husb_usensor.o
                0x02013c88                usensor_handle
 COMMON         0x02013c8c        0x4 obj\Debug\mcu\xos\xos.o
                0x02013c8c                XOSNesting
 COMMON         0x02013c90       0x74 obj\Debug\multimedia\video\video_record.o
                0x02013c90                mediaVideoCtl
 COMMON         0x02013d04       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                0x02013d04                ui_draw_ctrl
 COMMON         0x02013d14      0x130 obj\Debug\app\app_common\src\app_init.o
                0x02013d14                SysCtrl
 COMMON         0x02013e44        0x1 obj\Debug\app\app_common\src\main.o
                0x02013e44                flash_success
 *fill*         0x02013e45        0x3 
 COMMON         0x02013e48       0x48 obj\Debug\app\task_windows\task_main\src\taskMain.o
                0x02013e48                mainTaskOp
 COMMON         0x02013e90        0x4 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x02013e90                task_play_audio_stat
 COMMON         0x02013e94       0x44 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x02013e94                playVideoOp
 COMMON         0x02013ed8       0x20 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x02013ed8                recordPhotoOp
 COMMON         0x02013ef8       0x64 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                0x02013ef8                sd_update_op
 COMMON         0x02013f5c        0xc obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                0x02013f5c                usbDeviceOp
 COMMON         0x02013f68      0x500 ..\lib\libmcu.a(hx330x_isp.o)
                0x02013f68                RGB_GMMA_Tab
                0x02014268                Y_GMA_Tab
 COMMON         0x02014468       0x14 ..\lib\libmcu.a(hx330x_lcd.o)
                0x02014468                hx330x_lcdISR
 COMMON         0x0201447c       0x14 ..\lib\libmcu.a(hx330x_rtc.o)
                0x0201447c                rc128k_div
                0x02014480                rtcSecondISR
                0x02014484                rtcAlamISR
                0x02014488                rc128k_rtc_cnt
                0x0201448c                rc128k_timer_cnt
 COMMON         0x02014490        0x4 ..\lib\libmcu.a(hx330x_sys.o)
                0x02014490                smph_dmacopy
 COMMON         0x02014494       0x10 ..\lib\libmcu.a(hx330x_timer.o)
                0x02014494                hx330x_timerISR
 COMMON         0x020144a4       0x84 ..\lib\liblcd.a(hal_lcd.o)
                0x020144a4                lcd_show_ctrl
 COMMON         0x02014528      0x1e8 ..\lib\liblcd.a(hal_lcdMem.o)
                0x02014528                lcdshow_frame_op
 *(.big_buffer*)
 *(._sdram_buf_)
 ._sdram_buf_   0x02014710     0x8044 obj\Debug\dev\fs\src\fs_api.o
                0x02014710                work_fatfs
                0x0201c780                _sdram_remian_addr = (ALIGN (0x40) + 0x0)
                0x0000c800                _text_lma = (((LOADADDR (.on_sdram) + SIZEOF (.on_sdram)) + 0x1ff) & 0xfffffe00)
                0x0600c800                _text_vma = (ORIGIN (flash) + _text_lma)

.text           0x0600c800    0x8a72c load address 0x0000c800
 *(.text*)
 .text          0x0600c800      0x244 obj\Debug\dev\battery\src\battery_api.o
                0x0600c800                dev_battery_ioctrl
                0x0600c984                dev_battery_init
 .text          0x0600ca44      0x254 obj\Debug\dev\dev_api.o
                0x0600ca44                dev_api_node_init
                0x0600cb68                dev_open
                0x0600cc28                dev_ioctrl
 .text          0x0600cc98      0x244 obj\Debug\dev\fs\src\diskio.o
                0x0600cc98                get_fattime
                0x0600cd18                disk_status
                0x0600cd68                disk_initialize
                0x0600cdbc                disk_read
                0x0600ce18                disk_write
                0x0600ce74                disk_ioctl
 .text          0x0600cedc     0x8084 obj\Debug\dev\fs\src\ff.o
                0x06010cc4                f_mount
                0x06010d74                f_open
                0x060112c4                f_read
                0x060115fc                f_write
                0x0601198c                f_sync
                0x06011bf0                f_close
                0x06011c3c                f_ftime
                0x06011c98                f_lseek
                0x0601241c                f_opendir
                0x06012574                f_closedir
                0x060125b0                f_readdir
                0x06012654                f_findnext
                0x060126f8                f_findfirst
                0x0601274c                f_stat
                0x060127e0                f_getfree
                0x06012b5c                f_truncate
                0x06012cb4                f_unlink
                0x06012e68                f_mkdir
                0x06013158                f_rename
                0x0601343c                f_chmod
                0x06013524                f_utime
                0x06013608                f_expand
                0x060138ec                f_mkfs
                0x06014be4                FEX_getlink_clust
                0x06014c1c                f_merge
                0x06014dcc                _f_bound
 .text.unlikely
                0x06014f60       0x30 obj\Debug\dev\fs\src\ff.o
 .text          0x06014f90      0x204 obj\Debug\dev\fs\src\ffunicode.o
                0x06014f90                ff_uni2oem
                0x06015008                ff_oem2uni
                0x06015080                ff_wtoupper
 .text          0x06015194      0xa2c obj\Debug\dev\fs\src\fs_api.o
                0x06015194                fs_exfunc_init
                0x060151ec                fs_nodeinit
                0x0601521c                fs_mount
                0x06015318                fs_open
                0x060153e8                fs_close
                0x06015460                fs_read
                0x060154d8                fs_write
                0x060155c8                fs_seek
                0x06015694                fs_getcltbl
                0x060156e4                fs_getclusize
                0x06015734                fs_mkdir
                0x06015760                fs_alloc
                0x060157d4                fs_sync
                0x0601583c                fs_merge
                0x060158c8                fs_bound
                0x06015954                fs_getclustersize
                0x06015978                fs_size
                0x060159c4                fs_pre_size
                0x06015a10                fs_tell
                0x06015a5c                fs_free_size
                0x06015ae0                fs_check
                0x06015b08                fs_getStartSector
                0x06015b64                fs_ftime
 .text          0x06015bc0      0x36c obj\Debug\dev\gsensor\src\gsensor_api.o
                0x06015bc0                gsensor_iic_enable
                0x06015be4                gsensor_iic_disable
                0x06015c04                gSensorGetName
                0x06015c38                dev_gSensor_Init
                0x06015d58                dev_gSensor_ioctrl
 .text          0x06015f2c      0x550 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .text          0x0601647c      0x80c obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .text          0x06016c88      0x4fc obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .text          0x06017184      0x12c obj\Debug\dev\ir\src\ir_api.o
                0x06017184                dev_ir_init
                0x060171cc                dev_ir_ioctrl
 .text          0x060172b0      0x624 obj\Debug\dev\key\src\key_api.o
                0x060172b0                dev_key_init
                0x06017404                dev_key_ioctrl
                0x06017870                getKeyADCvalue
                0x06017890                getKeyCurEvent
                0x060178b0                keyLongTypeScanModeSet
 .text          0x060178d4      0x318 obj\Debug\dev\lcd\src\lcd_api.o
                0x060178d4                lcd_initTab_config
                0x060179e4                LcdGetName
                0x06017a00                dev_lcd_init
                0x06017ab0                dev_lcd_ioctrl
                0x06017bcc                dev_lcd_nocolor_status
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .text          0x06017bec        0x0 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .text          0x06017bec      0x104 obj\Debug\dev\led\src\led_api.o
                0x06017bec                dev_led_init
                0x06017c04                dev_led_ioctrl
 .text          0x06017cf0       0xf0 obj\Debug\dev\led_pwm\src\led_pwm_api.o
                0x06017cf0                dev_led_pwm_init
                0x06017d08                dev_led_pwm_ioctrl
 .text          0x06017de0      0x30c obj\Debug\dev\nvfs\src\nvfs_api.o
                0x06017de0                nv_port_read
                0x06017e00                nv_init
                0x06017f30                nv_uninit
                0x06017f48                nv_configAddr
                0x06017fc0                nv_open
                0x0601803c                nv_size
                0x060180bc                nv_read
 .text          0x060180ec     0x1f0c obj\Debug\dev\nvfs\src\nvfs_jpg.o
                0x06018974                nv_dir_read
                0x06018ab8                nv_dir_readfirst
                0x06018b28                nv_dir_readnext
                0x06018b7c                nv_jpg_ex_force_init
                0x06018b9c                nv_jpg_ex_init
                0x06018c78                nv_jpg_formart_init
                0x06018e48                nv_jpg_init
                0x06019088                nv_jpg_uinit
                0x060190f0                nv_jpg_format
                0x06019180                nv_jpg_open
                0x060194b8                nv_jpg_change_lock
                0x06019594                nv_jpg_close
                0x0601966c                nv_jpgfile_read
                0x0601986c                nv_jpgfile_seek
                0x06019a88                nv_jpgfile_delete
                0x06019b80                nv_jpgfile_size
                0x06019bc0                nvjpg_free_size
                0x06019bf4                nv_jpgfile_write
                0x06019fc8                nvjpg_free_dir
 .text          0x06019ff8      0xc28 obj\Debug\dev\sd\src\sd_api.o
                0x0601a78c                sd_api_init
                0x0601a9d8                sd_api_getNextLBA
                0x0601a9f8                sd_api_Uninit
                0x0601aa18                sd_api_lock
                0x0601aa58                sd_api_unlock
                0x0601aa84                sd_api_CardState_Set
                0x0601aaa4                sd_api_CardState_Get
                0x0601aac4                sd_api_GetBusWidth
                0x0601aae4                sd_api_Capacity
                0x0601ab04                sd_api_speed_debg
                0x0601ab50                dev_sdc_init
                0x0601ab80                dev_sdc_ioctrl
 .text          0x0601ac20      0x708 obj\Debug\dev\sensor\src\sensor_api.o
                0x0601ac20                sensor_iic_write
                0x0601ac8c                sensor_iic_read
                0x0601ad0c                sensor_rgbgamma_tab_load
                0x0601add4                sensor_ygamma_tab_load
                0x0601ae9c                sensor_lsc_tab_load
                0x0601aeec                SensorGetName
                0x0601af08                dev_sensor_init
                0x0601af20                dev_sensor_ioctrl
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .text          0x0601b328        0x0 obj\Debug\dev\sensor\src\sensor_tab.o
 .text          0x0601b328      0x504 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                0x0601b388                dev_touchpanel_Init
                0x0601b524                dev_touchpanel_ioctrl
 .text          0x0601b82c      0x1ec obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                0x0601b82c                tp_icnt81_getPoint
 .text          0x0601ba18      0x74c obj\Debug\dev\touchpanel\src\touchpanel_iic.o
                0x0601be5c                tp_iic_init
                0x0601bed0                tp_iic_config
                0x0601bef4                tp_iic_write
                0x0601c004                tp_iic_read
 .text          0x0601c164      0x374 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                0x0601c164                tp_ns2009_Match
                0x0601c208                tp_ns2009_getPoint
 .text          0x0601c4d8      0x3f4 obj\Debug\dev\usb\dusb\src\dusb_api.o
                0x0601c530                dusb_api_online
                0x0601c550                dusb_api_Init
                0x0601c64c                dusb_api_Uninit
                0x0601c68c                dusb_api_offline
                0x0601c6c4                dusb_api_Process
                0x0601c734                dev_dusb_init
                0x0601c770                dev_dusb_ioctrl
 .text          0x0601c8cc      0xb50 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x0601ca74                dusb_stall_ep
                0x0601cb00                dusb_ep0_tx
                0x0601cb7c                dusb_ep0_recieve_set
                0x0601cbb4                dusb_ep0_process
                0x0601d308                dusb_ep0_cfg
                0x0601d35c                dusb_cfg_reg
 .text          0x0601d41c     0x1150 obj\Debug\dev\usb\dusb\src\dusb_msc.o
                0x0601d594                msc_epx_cfg
                0x0601d630                dusb_WriteToMem
                0x0601d670                dusb_ReadFromMem
                0x0601d694                rbc_mem_read
                0x0601d704                rbc_mem_write
                0x0601d77c                rbc_mem_rxfunc
                0x0601d7b8                sdk_returnmask
                0x0601d8f4                cbw_updatartc
                0x0601d93c                mscCmd_ufmod
                0x0601d988                sent_csw
                0x0601da64                mscCmd_Read
                0x0601db64                mscCmd_Write
                0x0601dc64                scsi_cmd_analysis
                0x0601e1c0                get_cbw
                0x0601e40c                rbc_rec_pkg
                0x0601e4ac                rbc_process
 .text          0x0601e56c        0x0 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .text          0x0601e56c      0x5a4 obj\Debug\dev\usb\dusb\src\dusb_uac.o
                0x0601e56c                uac_set_volume
                0x0601e5b8                uac_set_mute
                0x0601e600                uac_isr_process
                0x0601e720                uac_epx_cfg
                0x0601e794                uac_get_volume
                0x0601e824                uac_unit_ctl_hal
                0x0601e8f8                UacHandleToStreaming
                0x0601e994                uac_start
                0x0601ea58                UacReceiveSetSamplingFreqCallback
                0x0601eab4                uac_stop
 .text          0x0601eb10      0x8f0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                0x0601eb10                uvc_pic_callback
                0x0601eb38                unitsel_set
                0x0601eb74                uvc_video_probe_control_callback
                0x0601eba8                uvc_still_probe_control_callback
                0x0601ebdc                uvc_epx_cfg
                0x0601ed30                uvc_unit_ctl_hal
                0x0601edd8                uvc_video_probe_control
                0x0601ee20                uvc_still_probe_control
                0x0601ee68                uvc_still_trigger_control
                0x0601ee9c                uvc_probe_ctl_hal
                0x0601ef18                uvc_start
                0x0601efbc                uvc_stop
                0x0601f020                uvc_is_start
                0x0601f040                uvc_pic_sanp
                0x0601f060                uvc_header_fill
                0x0601f148                uvc_isr_process
                0x0601f2f4                uvc_process
 .text          0x0601f400      0x8f0 obj\Debug\dev\usb\husb\src\husb_api.o
                0x0601f400                husb_api_u20_remove
                0x0601f48c                husb_api_u11_remove
                0x0601f728                husb_api_handle_get
                0x0601f758                husb_api_init
                0x0601f8b4                husb_api_devicesta
                0x0601f8e8                husb_api_devicesta_set
                0x0601f93c                husb_api_msc_try_tran
                0x0601f978                dev_husb_io_power_set
                0x0601fa60                dev_husb_init
                0x0601fb24                dev_husb_ioctrl
 .text          0x0601fcf0     0x2970 obj\Debug\dev\usb\husb\src\husb_enum.o
                0x060201b4                husb_hub_clrport_feature_ack
                0x06020214                husb_hub_set_port_devaddr_ack
                0x060203c0                husb_get_pcommit_cs100_ack
                0x060204a4                husb_uvcunit_ack
                0x06020bb8                husb_setport_feature_ack
                0x06020be0                husb_hub_port_reset_ack
                0x06020c08                husb_get_max_lun_ack
                0x06020c3c                husb_set_intfs_uvc_1_ack
                0x06020c60                husb_hub_inf_ack
                0x06020e14                husb_hub_getport_status_ack
                0x06020eb4                husb_set_pcommit_cs200_ack
                0x06020ef0                husb20_ep0_cfg
                0x06020f10                husb11_ep0_cfg
                0x06020f30                usensor_resolution_select
                0x0602125c                husb_all_get_cfg_desc_ack
                0x06021dec                husb_astern_check
                0x06021e28                husb_astern_ack
                0x06021eac                husb_api_ep0_kick
                0x06021f8c                husb_api_ep0_process
                0x060220d4                husb_api_ep0_uvc_switch_kick
                0x06022110                husb_api_ep0_asterncheck_kick
                0x0602215c                husb_api_uvcunit_get_kick
                0x060222f0                husb_api_uvcunit_get_done
                0x060223a4                husb_api_uvcunit_set_kick
                0x06022540                husb_api_uvcunit_set_done
                0x060225e8                husb_api_hub_check_kick
 .text          0x06022660      0x230 obj\Debug\dev\usb\husb\src\husb_hub.o
                0x06022660                husb_hub_init
                0x06022804                husb_hub_uinit
 .text          0x06022890     0x1138 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
                0x06022890                udisk_cap
                0x060228b4                udisk_online
                0x060228e4                husb_msc_init
                0x06022a70                epbulk20_send_dat
                0x06022c34                epbulk20_recieve_dat
                0x06022da4                epbulk11_send_dat
                0x06022f44                epbulk11_recieve_dat
                0x060230a8                epbulk_send_dat
                0x060230f8                epbulk_receive_dat
                0x06023148                cbw_init
                0x06023194                rbc_read_lba
                0x06023298                rbc_write_lba
                0x0602338c                spc_inquiry
                0x06023440                spc_test_unit_rdy
                0x060234e0                spc_request_sense
                0x06023594                rbc_read_capacity
                0x06023724                spc_StartStopUnit
                0x060237ac                enum_mass_dev
 .text          0x060239c8      0x7d4 obj\Debug\dev\usb\husb\src\husb_usensor.o
                0x060239c8                husb_api_handle_reg
                0x060239e8                husb_api_usensor_tran_sta
                0x06023a20                husb_api_usensor_atech_sta
                0x06023a58                husb_api_usensor_res_get
                0x06023aa0                husb_api_usensor_res_type_is_mjp
                0x06023adc                husb_api_usensor_res_type_is_yuv
                0x06023b18                husb_api_astern_set
                0x06023b78                husb_api_astern_get
                0x06023bb0                husb_api_detech_check
                0x06023be8                husb_api_usensor_linkingLcd
                0x06023c14                husb_api_usensor_relinkLcd_reg
                0x06023c40                husb_api_usensor_dcdown
                0x06023c78                husb_api_usensor_detech
                0x06023cec                husb_api_usensor_asterncheck
                0x06023d20                husb_api_usensor_asternset
                0x06023d7c                husb_api_usensor_frame_read
                0x06023de8                husb_api_usensor_csi_kick
                0x06023e58                husb_api_usensor_switch_res_kick
                0x06023fe4                husb_api_usensor_notran_check
                0x060240c0                husb_api_usensor_stop_fill
                0x060240f0                husb_api_usensor_uvcunit_get
                0x0602414c                husb_api_usensor_uvcunit_set
 .text          0x0602419c      0x740 obj\Debug\dev\usb\husb\src\husb_uvc.o
                0x060243c0                husb_uvc_atech_codec
                0x06024490                husb_uvc_dcd_cache_get
                0x060244b4                husb_uvc_init
                0x060246d4                husb_uvc_linking
                0x06024798                husb_uvc_relink_register
                0x060247c8                husb_uvc_detech
                0x06024810                husb_uvc_stop_fill
 .text          0x060248dc       0xcc obj\Debug\hal\src\hal_adc.o
                0x060248dc                hal_adcInit
                0x0602491c                hal_adcRead
                0x06024950                hal_adcSetChannel
 .text          0x060249a8      0x784 obj\Debug\hal\src\hal_auadc.o
                0x06024c60                hal_auadc_stamp_out
                0x06024c80                hal_auadc_stamp_next
                0x06024ca0                hal_auadcInit
                0x06024ce0                hal_auadcMemInit
                0x06024d90                hal_auadc_pcmsize_get
                0x06024db0                hal_auadcMemUninit
                0x06024e0c                hal_auadc_cnt
                0x06024e38                hal_auadcmutebuf_get
                0x06024e58                hal_auadcStart
                0x06024ff8                hal_auadcBufferGet
                0x06025064                hal_auadcBufferRelease
                0x0602508c                hal_auadcStop
                0x060250cc                hal_adcBuffer_prefull
                0x0602510c                hal_adc_volume_set
 .text          0x0602512c      0x4fc obj\Debug\hal\src\hal_csi.o
                0x0602542c                hal_csi_init
                0x06025450                hal_csi_input_switch
                0x06025584                hal_csi_test_fps_adj
 .text          0x06025628      0x1f0 obj\Debug\hal\src\hal_dac.o
                0x06025628                hal_dacInit
                0x0602568c                hal_dacPlayInit
                0x06025714                hal_dacPlayStart
                0x06025770                hal_dacPlayStop
                0x06025798                hal_dacSetVolume
                0x060257f8                hal_dacCallBackRegister
 .text          0x06025818      0x42c obj\Debug\hal\src\hal_dmauart.o
                0x06025818                hal_dmaUartRxOverWait
                0x060258ac                hal_dmaUartRxDataOut
                0x06025948                hal_dmaUartIRQHandler
                0x060259d0                hal_dmauartInit
                0x06025a78                hal_dmauartTxDma
                0x06025ad8                hal_dmauartTxDmaKick
                0x06025b30                hal_dmauartTest
 .text          0x06025c44        0x0 obj\Debug\hal\src\hal_eeprom.o
 .text          0x06025c44       0xa8 obj\Debug\hal\src\hal_gpio.o
                0x06025c44                hal_gpioInit_io1d1
 .text          0x06025cec      0xa50 obj\Debug\hal\src\hal_iic.o
                0x06025cec                hal_iic0Init
                0x06025d1c                hal_iic0Uninit
                0x06025d3c                hal_iic08bitAddrWriteData
                0x06025da8                hal_iic08bitAddrReadData
                0x06025e30                hal_iic08bitAddrWrite
                0x06025ec0                hal_iic08bitAddrRead
                0x06025f60                hal_iic016bitAddrWriteData
                0x06025fd8                hal_iic016bitAddrReadData
                0x0602606c                hal_iic016bitAddrWrite
                0x06026108                hal_iic016bitAddrRead
                0x060261b4                hal_iic1IOShare
                0x060261e4                hal_iic1IOShareCheck
                0x0602622c                hal_iic1Init
                0x0602626c                hal_iic1Uninit
                0x060262a4                hal_iic18bitAddrWriteData
                0x06026314                hal_iic18bitAddrReadData
                0x060263a0                hal_iic18bitAddrWrite
                0x06026434                hal_iic18bitAddrRead
                0x060264d8                hal_iic116bitAddrWriteData
                0x06026554                hal_iic116bitAddrReadData
                0x060265ec                hal_iic116bitAddrWrite
                0x0602668c                hal_iic116bitAddrRead
 .text          0x0602673c       0x34 obj\Debug\hal\src\hal_int.o
                0x0602673c                hal_intInit
 .text          0x06026770     0x18cc obj\Debug\hal\src\hal_lcdshow.o
                0x06026804                hal_lcdSetCsiScaler
                0x060268b8                hal_lcdSetVideoScaler
                0x06026928                hal_lcd_scaler_done_check
                0x06026980                hal_lcdVideoSetRotate
                0x06026a78                hal_lcdUiSetRotate
                0x06026ad8                hal_lcdSetRatio
                0x06026d14                hal_lcdSetBufYUV
                0x06026da8                hal_lcdSetBufYUV_2
                0x06026e2c                hal_lcdSetWINAB
                0x06026f00                hal_lcdWinEnablePreSet
                0x06026f20                hal_lcdSetWinEnable
                0x06026f58                lcd_struct_get
                0x06026f78                hal_lcdLCMPowerOff
                0x06026ffc                hal_lcd_decwin_done
                0x06027308                hal_lcd_encwin_done
                0x060273b8                hal_lcd_frame_enc_func_register
                0x06027408                hal_CSI_lcdFrameEndCallback
                0x06027850                hal_lcd_fps_debg
                0x06027898                hal_lcdGetSreenResolution
                0x06027920                hal_lcdGetUiResolution
                0x0602797c                hal_lcdGetUiPosition
                0x060279d8                hal_lcdUiScanModeGet
                0x06027a08                hal_lcdGetVideoRatioResolution
                0x06027a64                hal_lcdSetVideoRatioResolution
                0x06027a9c                hal_lcdGetVideoRatioPos
                0x06027af8                hal_lcdGetVideoResolution
                0x06027b54                hal_lcdGetVideoPos
                0x06027bb0                hal_lcdVideoScanModeGet
                0x06027be0                hal_lcdVideoScalerTypeAdj
                0x06027c98                hal_lcd_enc_start
                0x06027d2c                hal_lcd_enc_stop
                0x06027d98                hal_lcd_enc_checkdone
                0x06027dc4                hal_lcd_enc_frame_get
                0x06027e04                hal_lcd_enc_frame_res_get
                0x06027e48                hal_lcd_pause_set
                0x06027e9c                hal_lcd_pause_sta_get
                0x06027eec                hal_lcdSetGamma
 .text          0x0602803c      0x154 obj\Debug\hal\src\hal_md.o
                0x06028064                hal_mdInit
                0x06028110                hal_mdEnable
                0x06028158                hal_mdCheck
 .text          0x06028190     0x1dc0 obj\Debug\hal\src\hal_mjpAEncode.o
                0x06028254                hal_mjpA_Start
                0x06028284                hal_mjpA_Restart
                0x060284ec                hal_mjpA_Sizecalculate
                0x06028518                hal_mjpA_EncState
                0x06028b50                hal_jA_fcnt_mnt
                0x06028ba4                hal_mjpA_Linebuf_nocolor_change
                0x06028e24                hal_mjpAEncodePhotoResumePKG
                0x06028ee4                hal_mjpAEncodePhotoResumeLLPKG
                0x06028f94                hal_mjpAEncodePhotoResumeRam
                0x06029090                hal_mjpA_EncodeInit
                0x060290ec                hal_mjpA_LineBuf_get
                0x06029118                hal_mjpA_src_res_get
                0x0602914c                hal_mjpA_buf_MenInit
                0x060291fc                hal_mjpA_linebufUninit
                0x06029244                hal_mjpA_MemUninit
                0x06029294                hal_mjpA_EncodeUninit
                0x06029320                hal_mjpA_EncVideo_Start
                0x060296ac                hal_mjpA_EncPhoto_Start
                0x06029a40                hal_mjpA_EncPhotoLcd_Start
                0x06029cf0                hal_mjpA_photo_encode_mode
                0x06029d10                hal_mjpA_RawBufferfree
                0x06029d38                hal_mjpA_RawBufferGet
                0x06029e34                hal_mjpA_RkgBufferGet
                0x06029ed8                hal_mjpA_Buffer_prefull
                0x06029f14                hal_mjpA_Buffer_halffull
 .text          0x06029f50      0x85c obj\Debug\hal\src\hal_mjpBEncode.o
                0x06029f50                hal_mjpBEncodeKickManual
                0x0602a048                hal_mjpB_Sizecalculate
                0x0602a070                hal_jB_fcnt_mnt
                0x0602a0c4                hal_mjpBEnc_state
                0x0602a0f0                hal_mjpBEncodeDoneCfg
                0x0602a114                hal_mjpBEncodeDoneManual
                0x0602a224                hal_mjpB_LineBuf_MenInit
                0x0602a2c0                hal_mjpB_LineBuf_cfg
                0x0602a2e4                hal_mjpB_buf_MenInit
                0x0602a39c                hal_mjpB_MemUninit
                0x0602a3fc                hal_mjpB_usb_resolution_set
                0x0602a420                hal_mjpB_DecodeODMA1En
                0x0602a464                hal_mjpB_Enc_Start
                0x0602a5dc                hal_mjpB_Enc_Stop
                0x0602a610                hal_mjpB_RawBufferfree
                0x0602a638                hal_mjpB_RawBufferGet
                0x0602a734                hal_mjpB_Buffer_prefull
                0x0602a770                hal_mjpB_Buffer_halffull
 .text          0x0602a7ac      0xe50 obj\Debug\hal\src\hal_mjpDecode.o
                0x0602a7ac                hal_mjpHeaderParse
                0x0602ae5c                hal_mjpDecodeIsYUV422
                0x0602ae7c                hal_mjpDecodeGetResolution
                0x0602aec0                hal_mjpDecodeSetResolution
                0x0602aee4                hal_mjpDecodeBusyCheck
                0x0602af04                hal_mjpDecodeErrorCheck
                0x0602af24                hal_mjpDecodeStop
                0x0602af44                hal_mjpDecodeReset
                0x0602af70                hal_mjpDecodePicture
                0x0602b000                hal_mjpegDecodePicture_noisr
                0x0602b08c                hal_mjpegDecodePicture_packet
                0x0602b13c                hal_mjpDecodeParse
                0x0602b16c                hal_mjpDecodeOneFrame
                0x0602b1e4                hal_mjpDecodeOneFrame_Ext
                0x0602b27c                hal_mjpDecodeRestart_Ext
                0x0602b2e8                hal_mjpDecodeOneFrame_Fast
                0x0602b3e4                hal_mjpDecodeMiniSize
                0x0602b568                hal_mjpDecodeODma1Cfg
                0x0602b58c                hal_BackRecDecodeStatusCheck
 .text          0x0602b5fc      0xc80 obj\Debug\hal\src\hal_rtc.o
                0x0602b628                hal_rtcCallBackRegister
                0x0602b688                hal_rtcCallBackRelease
                0x0602b6c4                hal_rtcUninit
                0x0602b700                hal_rtcTimeGet
                0x0602b71c                hal_rtcTimeGetExt
                0x0602b750                hal_rtcSecondGet
                0x0602b770                hal_rtcTime2String
                0x0602ba24                hal_rtcTime2StringExt
                0x0602ba88                hal_rtcLeapYear
                0x0602bca8                hal_rtcTime
                0x0602bdbc                hal_rtcInit
                0x0602befc                hal_rtcSecondSet
                0x0602bf38                hal_rtcValue
                0x0602c048                hal_rtcTimeSet
                0x0602c0ec                hal_rtcAlarmSet
                0x0602c17c                hal_rtcAlarmSetExt
                0x0602c220                hal_rtcAlarmStatusGet
                0x0602c25c                hal_rtcTrimCallBack
 .text          0x0602c27c        0x0 obj\Debug\hal\src\hal_spi.o
 .text          0x0602c27c      0x504 obj\Debug\hal\src\hal_spi1.o
                0x0602c27c                hal_spi1DmaCallback
                0x0602c310                hal_spi1DmaDoneCheck
                0x0602c390                hal_spi1Init
                0x0602c3e8                hal_spi1SendByte
                0x0602c40c                hal_spi1RecvByte
                0x0602c42c                hal_spi1SendDmaKick
                0x0602c4c4                hal_spi1SendDma
                0x0602c51c                hal_spi1RecvDmaKick
                0x0602c5b8                hal_spi1RecvDma
                0x0602c610                hal_spi1_test
 .text          0x0602c780      0x21c obj\Debug\hal\src\hal_stream.o
                0x0602c780                hal_streamInit
                0x0602c8dc                hal_streamMallocDrop
                0x0602c984                hal_stream_size
 .text          0x0602c99c      0x9ac obj\Debug\hal\src\hal_sys.o
                0x0602cc98                hal_sysMemPrint
                0x0602cda4                hal_sysMemMalloc
                0x0602cebc                hal_sysMemMallocLast
                0x0602cfe8                hal_sysMemFree
                0x0602d1c4                hal_sysMemRemain
                0x0602d21c                hal_sysInit
 .text          0x0602d348       0xe0 obj\Debug\hal\src\hal_timer.o
                0x0602d348                hal_timerEnable
                0x0602d380                hal_timerTickEnable
                0x0602d3b4                hal_timerPWMStart
 .text          0x0602d428      0x540 obj\Debug\hal\src\hal_uart.o
                0x0602d48c                hal_uartIOShare
                0x0602d4c8                hal_uartIOShareCheck
                0x0602d514                hal_uartInit
                0x0602d56c                hal_uartSendData
                0x0602d590                hal_uartRXIsrRegister
                0x0602d5b0                uart_PutChar_n
                0x0602d5e4                uart_PutStr
                0x0602d624                uart_Put_hex
                0x0602d750                uart_Put_udec
                0x0602d7e4                uart_Put_dec
                0x0602d88c                uart_PrintfBuf
                0x0602d924                hal_uartPrintString
 .text          0x0602d968      0xa04 obj\Debug\hal\src\hal_watermark.o
                0x0602d9d0                hal_watermarkInit
                0x0602da80                hal_watermarkClose
                0x0602db14                hal_watermarkClear
                0x0602db94                hal_watermarkOpen
                0x0602dc34                hal_watermarkColor
                0x0602dc94                hal_watermarkAddr
                0x0602dcac                hal_watermarkSize
                0x0602dcf8                hal_watermarkPosition
                0x0602dd44                hal_watermarkCallbackRegister
                0x0602dd8c                hal_watermarkRam
                0x0602df84                hal_watermarkEnable
                0x0602e0d4                hal_jpg_watermark_init
                0x0602e164                hal_jpg_watermark_uinit
                0x0602e1a8                hal_jpg_watermarkStart
                0x0602e2fc                hal_jpgB_watermarkPos_Adjust
 .text          0x0602e36c        0x0 obj\Debug\hal\src\hal_wdt.o
 .text          0x0602e36c        0x0 obj\Debug\mcu\boot\spi_boot_cfg.o
 .text          0x0602e36c        0x0 obj\Debug\mcu\xos\xmbox.o
 .text          0x0602e36c      0x2e4 obj\Debug\mcu\xos\xmsgq.o
                0x0602e36c                XMsgQInit
                0x0602e3ac                XMsgQCreate
                0x0602e43c                XMsgQDestory
                0x0602e474                XMsgQFlush
                0x0602e4b4                XMsgQPost
                0x0602e52c                XMsgQPostFront
                0x0602e5a8                XMsgQPend
                0x0602e638                XMsgQCheck
 .text          0x0602e650      0x14c obj\Debug\mcu\xos\xos.o
                0x0602e650                XOSInit
                0x0602e68c                XOSTickService
                0x0602e6fc                XOSTimeGet
                0x0602e71c                XOSTimeDly
                0x0602e77c                XOSRandom
 .text          0x0602e79c      0x168 obj\Debug\mcu\xos\xwork.o
                0x0602e79c                XWorkInit
                0x0602e7dc                XWorkCreate
                0x0602e868                XWorkDestory
                0x0602e898                XWorkService
 .text          0x0602e904      0x998 obj\Debug\multimedia\audio\audio_playback.o
                0x0602e904                audioPlaybackInit
                0x0602e950                audioPlaybackParse
                0x0602ea40                audioPlaybackStop
                0x0602eb48                audioPlaybackUninit
                0x0602eb80                audioPlaybackStart
                0x0602ef50                audioPlaybackPause
                0x0602ef8c                audioPlaybackFirstPause
                0x0602efe8                audioPlaybackResume
                0x0602f050                audioPlaybackGetStatus
                0x0602f070                audioPlaybackGetTime
                0x0602f0ec                audioPlaybackSetVolume
                0x0602f140                audioPlaybackGetVolume
                0x0602f160                audioPlaybackService
 .text          0x0602f29c      0x528 obj\Debug\multimedia\audio\audio_record.o
                0x0602f2d0                audioRecordInit
                0x0602f398                audioRecordUninit
                0x0602f3ec                audioRecordStop
                0x0602f498                audioRecordStart
                0x0602f5d4                audioRecordPuase
                0x0602f604                audioRecordResume
                0x0602f634                audioRecordGetStatus
                0x0602f654                audioRecordSetStatus
                0x0602f674                audioRecordGetTime
                0x0602f694                audioRecordService
 .text          0x0602f7c4      0xbf4 obj\Debug\multimedia\image\image_decode.o
                0x0602f7c4                imageDecodeSubCheck
                0x0602f940                imageDecodeStart
                0x0602ff08                imageDecodeSpiStart
                0x06030118                imageDecodeGetResolution
                0x0603013c                imageDecodeDirect
 .text          0x060303b8      0x9c8 obj\Debug\multimedia\image\image_encode.o
                0x060303b8                imageEncodeInit
                0x060303e4                imageEncodeUninit
                0x06030408                imageEncodeStart
                0x06030a78                imageLcdEncodeStart
                0x06030b64                imageEncodeToSpi
 .text          0x06030d80      0x158 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                0x06030d80                jpg_encode
 .text          0x06030ed8     0x19a0 obj\Debug\multimedia\video\video_playback.o
                0x0603100c                videoPlaybackClear
                0x06031070                videoPlaybackInit
                0x0603114c                videoPlaybackDecodeWait
                0x06031198                videoPlaybackStart
                0x060315b0                videoPlaybackStop
                0x060316e8                videoPlaybackUninit
                0x0603195c                videoPlaybackPause
                0x060319e0                videoPlaybackResume
                0x06031a44                videoPlaybackGetStatus
                0x06031a64                videoPlaybackGetSpeed
                0x06031a84                videoPlaybackSetVolume
                0x06031ad4                videoPlaybackGetVolume
                0x06031af4                videoPlaybackGetTime
                0x06031b2c                videoPlaybabkGetArg
                0x06031b48                videoGetFirstFrame
                0x06031d24                videoDecodeFirstFrame
                0x06031e38                videoPlaybackService
                0x060326ec                videoPlaybackFastForward
                0x060327b4                videoPlaybackFastBackward
 .text          0x06032878      0xed8 obj\Debug\multimedia\video\video_record.o
                0x06032878                videoRecordInit
                0x06032938                videoRecordUninit
                0x06032984                videoRecordFileStart
                0x06032af4                videoRecordFileStop
                0x06032bbc                videoRecordFileError
                0x06032c24                videoRecordError
                0x06032c74                videoRecordStart
                0x06032d90                videoRecordGetTimeSec
                0x06032dd4                videoRecordGetStatus
                0x06032df4                videoRecordJunkSync
                0x06032e30                videoRecordStop
                0x06032ea8                videoRecordRestart
                0x06032fa4                videoRecordFrameProcess
                0x06033480                videoRecordService
                0x060335b4                videoRecordCmdSet
                0x06033660                videoRecordSizePreSec
                0x060336cc                videoRecordTakePhotoCfg
                0x060336f0                videoRecordTakePhotoStatus
                0x06033710                videoRecordSetPhotoStatus
                0x06033730                videoRecordTakePhotoFd
 .text          0x06033750     0x19f0 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
                0x06033844                filelist_listFlush
                0x06033948                filelist_api_Init
                0x06033974                filelist_api_nodecreate
                0x06033cdc                filelist_api_nodedestory
                0x06033df8                filelist_api_scan
                0x06033fc8                filenode_api_CountGet
                0x06033ff8                filelist_api_CountGet
                0x06034038                filelist_api_MaxCountGet
                0x06034068                filelist_GetFileNameByIndex
                0x060340e4                filelist_GetFileFullNameByIndex
                0x06034248                filelist_GetFileShortNameByIndex
                0x06034368                filelist_GetFileIndexByIndex
                0x06034414                filelist_findFirstFileName
                0x06034494                filelist_findFileNameByFname
                0x06034568                filelist_delFileByIndex
                0x06034680                filelist_delFileByFname
                0x06034738                filelist_listDelAll
                0x060348ac                filelist_createNewFileFullName
                0x06034914                filelist_createNewFileFullNameByFname
                0x06034954                filenode_addFileByFname
                0x0603499c                filenode_filefullnameLock
                0x06034a70                filenode_filefullnameUnlock
                0x06034b44                filenode_fnameLockByIndex
                0x06034bbc                filenode_fnameUnlockByIndex
                0x06034c38                filelist_fnameChecklockByIndex
                0x06034cc4                filenode_parentdir_get
                0x06034d6c                filelist_GetLrcFileFullNameByIndex
                0x06034e14                filelist_SpaceCheck
 .text          0x06035140      0xedc obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                0x06035140                filelist_NameChangeSufType
                0x060351a0                filenode_fname_check
                0x060351dc                filenode_fname_createNew
                0x06035340                filenode_filename_CreateByFname
                0x06035690                filenode_filefullname_CreateByFname
                0x0603570c                filenode_AddFileByFname
                0x060358c8                filenode_Scan
                0x06035e7c                filenode_api_findfirst
                0x06035f5c                filenode_api_findByFname
 .text          0x0603601c      0x200 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
                0x0603601c                res_ascii_get
                0x06036104                res_getAsciiCharSize
                0x0603613c                res_getAsciiStringSize
 .text          0x0603621c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .text          0x0603621c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .text          0x0603621c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .text          0x0603621c        0x0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .text          0x0603621c      0x65c obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
                0x0603621c                res_font_Init
                0x0603634c                res_font_SetLanguage
                0x0603641c                res_font_GetString
                0x06036534                res_font_GetChar
                0x060366fc                res_font_StringTableInit
                0x06036788                res_font_GetAddrAndSize
 .text          0x06036878      0x738 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
                0x06036910                res_iconInit
                0x06036a10                res_iconBuffInit
                0x06036a7c                res_iconGetSizeByIndex
                0x06036b30                res_iconGetAddrByIndex
                0x06036bb0                res_icon_GetAddrAndSize
                0x06036c7c                res_icon_GetTColor
                0x06036cb0                res_iconBuffTimeUpdate
                0x06036cf4                res_iconGetData
                0x06036f2c                res_iconGetPalette
 .text          0x06036fb0      0x324 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
                0x06036fb0                res_image_show
                0x060370c8                res_image_show1
                0x060371fc                res_image_decode
 .text          0x060372d4      0x118 obj\Debug\sys_manage\res_manage\res_manage_api.o
                0x060372d4                res_GetStringInfor
                0x0603733c                res_GetCharInfor
 .text          0x060373ec      0x368 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
                0x060373ec                res_music_end
                0x06037444                res_music_start
                0x0603758c                res_keysound_init
                0x060376c0                res_keysound_play
                0x060376f4                res_keysound_stop
 .text          0x06037754        0x0 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .text          0x06037754      0x1c8 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
                0x06037820                uiButtonCreate
 .text          0x0603791c      0x2f8 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
                0x06037a18                uiCycleCreateDirect
                0x06037aa4                uiCycleCreate
 .text          0x06037c14      0x1ec obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
                0x06037c14                uiDialogCreate
                0x06037d48                uiDialogItem
 .text          0x06037e00     0x19cc obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                0x06038390                uiWinDrawInit
                0x060383e8                uiWinDrawUpdate
                0x06038444                uiWinDrawLine
                0x06038744                uiWinDrawRect
                0x06038988                uiWinDrawRoundRectWithRim
                0x06038d2c                uiWinDrawPoint
                0x06038dcc                uiWinDrawCircle
                0x06038f60                uiWinDrawIcon
                0x0603924c                uiWinDrawString
 .text          0x060397cc      0x29c obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
                0x06039950                uiFrameWinCreate
 .text          0x06039a68      0x3b0 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
                0x06039c5c                uiImageIconCreateDirect
                0x06039d3c                uiImageIconCreate
 .text          0x06039e18     0x18c0 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
                0x06039e18                uiItemManageCreate
                0x06039f08                uiItemManageSetItemHeight
                0x0603a0a8                uiItemManageSetHeightAvgGap
                0x0603a228                uiItemManageSetHeightNotGap
                0x0603a364                uiItemManageSetRowSum
                0x0603a4cc                uiItemManageSetColumnSumWithGap
                0x0603a64c                uiItemManageCreateItem
                0x0603a7f8                uiItemManageSetResInforFuncEx
                0x0603a858                uiItemManageSetCurItem
                0x0603ab38                uiItemManageUpdateRes
                0x0603abf0                uiItemManageUpdateAllItem
                0x0603ac54                uiItemManageUpdateCurItem
                0x0603adf8                uiItemManageNextItem
                0x0603ae68                uiItemManagePreItem
                0x0603aed8                uiItemManageNextPage
                0x0603af5c                uiItemManagePrePage
                0x0603afe4                uiItemManageGetCurrentItem
                0x0603b048                uiItemManageSetCharInfor
                0x0603b100                uiItemManageSetSelectColor
                0x0603b198                uiItemManageSetSelectImage
                0x0603b230                uiItemManageSetUnselectColor
                0x0603b2c8                uiItemManageSetUnselectImage
                0x0603b360                uiItemManageGetTouchInfor
                0x0603b558                uiItemManageSetSelectColorEx
                0x0603b618                uiItemManageSetUnselectColorEx
 .text          0x0603b6d8      0x444 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
                0x0603b970                uiItemCreateItemMenu
 .text          0x0603bb1c      0x5c4 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
                0x0603becc                uiItemCreateMenuItemEx
 .text          0x0603c0e0      0x390 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
                0x0603c2f8                uiItemCreateMenuOption
 .text          0x0603c470      0x238 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
                0x0603c508                uiLineCreate
 .text          0x0603c6a8     0x1ccc obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
                0x0603c6a8                uiWinSendMsg
                0x0603c6e8                uiWinSendMsgId
                0x0603c710                uiWinSendMsgToParent
                0x0603c740                uiWinSendMsgIdToParent
                0x0603c768                uiWinOverlapCmp
                0x0603c7cc                uiWinInsideCmp
                0x0603c830                uiWinStringExRowCal
                0x0603c898                uiWinStringExGetByRow
                0x0603c8fc                uiWinStringExGetNext
                0x0603c960                uiWinInterSection
                0x0603c9e0                uiWinHasInvalidRect
                0x0603ca7c                uiWinFreeInvalidRect
                0x0603cd38                uiWinSetbgColor
                0x0603cd6c                uiWinSetCycleRadius
                0x0603cda4                uiWinSetRoundRectRadius
                0x0603cddc                uiWinSetfgColor
                0x0603ce10                uiWinSetVisible
                0x0603ce94                uiWinIsVisible
                0x0603ced0                uiWinSetResid
                0x0603cf00                uiWinSetItemSelResid
                0x0603cf30                uiWinUpdateResId
                0x0603cf54                uiWinUpdateAllResId
                0x0603cfb8                uiWinSetStrInfor
                0x0603d004                uiResInforInit
                0x0603d050                uiWinSetSelectInfor
                0x0603d080                uiWinSetUnselectInfor
                0x0603d0b0                uiWinGetResSum
                0x0603d0f0                uiWinSetResSum
                0x0603d120                uiWinSetResidByNum
                0x0603d170                uiWinSetPorgressRate
                0x0603d1a0                uiWinParentRedraw
                0x0603d210                uiWinGetRelativePos
                0x0603d2a0                uiWinGetPos
                0x0603d2e4                uiWinUpdateInvalid
                0x0603d35c                uiWinSetProgressRate
                0x0603d38c                uiWinSetName
                0x0603d3ac                uiWinGetCurrent
                0x0603d3cc                uiWinDefaultProc
                0x0603d774                uiWinCreate
                0x0603dc44                uiWinDestroy
                0x0603ddf0                uiWinGetTouchInfor
                0x0603de20                uiWinSetTouchInfor
                0x0603de50                uiWinTouchProcess
                0x0603e058                uiWinDrawProcess
                0x0603e16c                uiWinDestroyDeskTopChildWin
                0x0603e1e0                uiWinInit
                0x0603e2f8                uiWinUninit
 .text          0x0603e374      0x380 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
                0x0603e374                uiWinHeapInit
                0x0603e3b4                uiWinHeapMemInfo
                0x0603e430                uiWinHeapMalloc
                0x0603e500                uiWinHeapFree
                0x0603e5b0                uiMemPoolCreate
                0x0603e630                uiMemPoolGet
                0x0603e678                uiMemPoolPut
                0x0603e6b4                uiMemPoolInfo
 .text          0x0603e6f4      0x2e4 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
                0x0603e894                uiProgressBarCreateDirect
                0x0603e930                uiProgressBarCreate
 .text          0x0603e9d8      0x2dc obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
                0x0603eb3c                uiRectCreateDirect
                0x0603ebc8                uiRectCreate
 .text          0x0603ecb4      0x678 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
                0x0603f040                uiStringExCreateDirect
                0x0603f13c                uiStringExCreate
 .text          0x0603f32c      0x3fc obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
                0x0603f520                uiStringIconCreateDirect
                0x0603f624                uiStringIconCreate
 .text          0x0603f728      0x250 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
                0x0603f8ac                uiTipsCreate
 .text          0x0603f978      0x120 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
                0x0603f978                uiWidgetProc
                0x0603f9c4                uiWidgetSetType
                0x0603f9f8                uiWidgetGetType
                0x0603fa2c                uiWidgetGetId
                0x0603fa64                uiWidgetSetId
 .text          0x0603fa98      0x310 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
                0x0603fd08                uiWidgetManageCreate
 .text          0x0603fda8      0x8fc obj\Debug\app\app_common\src\app_init.o
                0x0603fda8                app_logo_show
                0x0603fe40                app_version_get
                0x0603fe74                app_draw_init
                0x0603fea4                app_uninit
                0x06040074                app_init
                0x060405f4                app_sendDrawUIMsg
                0x06040628                app_draw_Service
                0x06040670                app_systemService
 .text          0x060406a4     0x107c obj\Debug\app\app_common\src\app_lcdshow.o
                0x060406a4                app_lcdVideoShowScaler_cfg
                0x06040e10                app_lcdVideoShowRotate_cfg
                0x06040fe8                app_lcdVideoShowMirro_cfg
                0x060411bc                app_lcdCsiVideoShowStart
                0x0604123c                app_lcdCsiVideoLayerEnGet
                0x0604125c                app_lcdCsiVideoShowStop
                0x060412d0                app_lcdVideoIdleFrameGet
                0x060412f0                app_lcdUiShowInit
                0x0604141c                app_lcdUiShowUinit
                0x0604146c                app_lcdUiDrawIdleFrameGet
                0x06041490                app_lcdShowWinModeCfg
 .text          0x06041720        0x0 obj\Debug\app\app_common\src\main.o
 .text.startup  0x06041720       0x5c obj\Debug\app\app_common\src\main.o
                0x06041720                main
 .text          0x0604177c        0x0 obj\Debug\app\resource\user_res.o
 .text          0x0604177c       0xf0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
                0x0604177c                menuProcDelCur
                0x060417ac                menuProcDelAll
                0x060417dc                menuProcLockCur
                0x0604180c                menuProcUnlockCur
                0x0604183c                menuProcUnlockAll
 .text          0x0604186c        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .text          0x0604186c       0x90 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
                0x0604186c                menuProcDateTime
                0x0604189c                menuProcFormat
                0x060418cc                menuProcDefault
 .text          0x060418fc        0x0 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
 .text          0x060418fc      0x970 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .text          0x0604226c      0x450 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .text          0x060426bc      0x518 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .text          0x06042bd4      0x58c obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .text          0x06043160      0x548 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .text          0x060436a8      0xef0 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x06044578                menuWinIsOpen
 .text          0x06044598      0x554 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .text          0x06044aec      0x4f8 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .text          0x06044fe4      0x588 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .text          0x0604556c      0x564 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .text          0x06045ad0        0x0 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .text          0x06045ad0      0x118 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .text          0x06045be8      0x1f8 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .text          0x06045de0      0xad4 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .text          0x060468b4      0x434 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .text          0x06046ce8      0x44c obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .text          0x06047134      0x1a8 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .text          0x060472dc      0x368 obj\Debug\app\task_windows\msg_api.o
                0x060472dc                taskmsgFuncRegister
                0x06047394                sysMsgFuncRegister
                0x06047410                app_msgDeal
                0x0604754c                app_msgDealByType
                0x060475e8                app_msgDealByInfor
 .text          0x06047644      0x4bc obj\Debug\app\task_windows\task_api.o
                0x060476ec                app_taskInit
                0x060477bc                app_taskCurId
                0x060477dc                app_taskStart
                0x060478a8                app_taskChange
                0x06047924                app_task_rec_Change
                0x060479a0                app_taskService
 .text          0x06047b00      0x550 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .text          0x06048050     0x2154 obj\Debug\app\task_windows\task_common\src\task_common.o
                0x06048050                task_com_para_init
                0x060480c0                task_com_usbhost_set
                0x06048328                task_com_usb_dev_out
                0x0604836c                task_com_spijpg_Init
                0x06048380                task_com_sdlist_scan
                0x06048394                task_com_sdc_stat_set
                0x0604848c                task_com_lcdbk_set
                0x06048500                task_com_sreen_check
                0x060485bc                task_com_auto_poweroff
                0x06048670                task_com_keysound_play
                0x060486bc                task_com_sound_wait_end
                0x060486f0                task_com_sdc_freesize_check
                0x060487a8                task_com_fs_scan
                0x060488e4                task_com_sdc_freesize_modify
                0x06048978                task_com_ir_set
                0x060489dc                task_com_powerOnTime_str
                0x06048a70                task_com_sensor_res_str
                0x06048b44                task_com_rec_show_time_str
                0x06048c04                task_com_rec_remain_time_str
                0x06048cc8                task_com_play_time_str
                0x06048ea8                task_com_tips_show
                0x06049098                task_com_LedPwm_ctrl
                0x060491d0                task_com_LedOnOff_ctrl
                0x06049320                task_com_scaler_str
                0x0604935c                task_com_USB_CS_DM_DP_status_select
                0x0604966c                task_com_service
 .text          0x0604a1a4       0x44 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .text          0x0604a1e8      0xffc obj\Debug\app\task_windows\task_main\src\taskMain.o
                0x0604a350                taskMainWinInit
                0x0604a380                taskMainWinChangeInit
                0x0604a410                taskMainWinChangeUint
                0x0604a4b4                taskMainWinChangeKick
                0x0604a780                taskWinCenterProcess
                0x0604ab80                taskWinHorProcess
                0x0604ae98                taskWinVorProcess
                0x0604b1d0                taskWinChangeProcess
 .text          0x0604b1e4      0x378 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
                0x0604b510                taskMainCurIdCfg
 .text          0x0604b55c      0x2c0 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                0x0604b6c0                app_taskPlayAudio_start
 .text          0x0604b81c      0xa34 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .text          0x0604c250     0x1464 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                0x0604c43c                taskPlayVideoThumbnallDrawImage
                0x0604c644                taskPlayVideoSlideOpen
                0x0604c794                taskPlayVideoSlideClose
                0x0604c7ec                taskPlayVideoSlidePause
                0x0604c830                taskPlayVideoSlideStart
                0x0604c980                taskPlayVideoMainStart
                0x0604d658                taskPlayVideoMainScalerCfg
 .text          0x0604d6b4     0x1550 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .text          0x0604ec04      0x3c8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .text          0x0604efcc      0xb10 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .text          0x0604fadc       0x4c obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .text          0x0604fb28      0x364 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .text          0x0604fe8c      0x20c obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .text          0x06050098      0x748 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                0x06050180                taskRecordPhotoRemainCal
                0x060502d8                app_taskRecordPhoto_callback
                0x06050610                taskRecordPhotoProcess
 .text          0x060507e0      0xc00 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .text          0x060513e0      0xcf0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                0x06051bc8                taskRecordvideoTimeCount1S
                0x06051c98                app_taskRecordVideo_caltime
                0x06051cd0                app_taskRecordVideo_Capture
                0x06051e84                app_taskRecordVideo_start
                0x06051f48                app_taskRecordVideo_stop
 .text          0x060520d0      0xef8 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .text          0x06052fc8       0x20 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .text          0x06052fe8       0xd8 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                0x06052fe8                taskSdUpdate_uiInit
 .text          0x060530c0       0x68 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .text          0x06053128      0x120 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .text          0x06053248      0x1bc obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .text          0x06053404      0x1ac obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .text          0x060535b0      0x3a0 obj\Debug\app\task_windows\windows_api.o
                0x060537a8                uiParentDealMsg
                0x060537d8                uiOpenWindow
                0x06053920                windowIsOpen
 .text          0x06053950     0x1488 obj\Debug\app\user_config\src\mbedtls_md5.o
                0x06053950                mbedtls_md5_init
                0x06053978                mbedtls_md5_free
                0x060539b8                mbedtls_md5_clone
                0x060539dc                mbedtls_md5_starts
                0x06053a2c                mbedtls_md5_process
                0x06054a0c                mbedtls_md5_update
                0x06054a38                mbedtls_md5_finish
                0x06054b74                mbedtls_md5
                0x06054be8                check_uid_entryption
                0x06054db8                spi_flash_check_md5
 .text          0x06054dd8      0x4e8 obj\Debug\app\user_config\src\user_config_api.o
                0x06054dd8                user_config_set
                0x06054e10                user_config_get
                0x06054e4c                user_config_save
                0x06054ef4                userConfig_Reset
                0x06054f5c                userConfig_Init
                0x06055044                userConfigInitial
                0x06055064                user_configValue2Int
                0x060550cc                user_config_Language
                0x06055134                user_config_cfgSys
                0x06055284                user_config_cfgSysAll
 .text          0x060552c0        0x0 obj\Debug\app\user_config\src\user_config_tab.o
 .text          0x060552c0        0x0 ..\lib\libboot.a(boot.o)
 .text          0x060552c0        0x0 ..\lib\libboot.a(boot_loader.o)
 .text          0x060552c0        0x0 ..\lib\libboot.a(reset.o)
 .text          0x060552c0        0x0 ..\lib\libboot.a(boot_lib.o)
 .text          0x060552c0        0x0 ..\lib\libmcu.a(hx330x_adc.o)
 .text          0x060552c0      0x420 ..\lib\libmcu.a(hx330x_auadc.o)
                0x060552c0                hx330x_auadcHalfIRQRegister
                0x060552e0                hx330x_auadcEndIRQRegister
                0x06055300                hx330x_auadcEnable
                0x060553b8                hx330x_auadcAGCEnable
                0x06055448                hx330x_auadcGainSet
                0x060554e0                hx330x_auadcBufferSet
                0x06055540                hx330x_auadcInit
                0x06055630                hx330x_auadcSetSampleSet
                0x060556c0                hx330x_agc_pwr_get
 .text          0x060556e0     0x125c ..\lib\libmcu.a(hx330x_csi.o)
                0x060556e0                hx330x_CSI_LBFDMAERR_callback
                0x06055764                hx330x_CSI_SenSizeErr_callback
                0x060557cc                hx330x_csiIOConfig
                0x06055868                hx330x_csi_reset
                0x060558ac                hx330x_csiInit
                0x060558f8                hx330x_csi_fcnt_mnt
                0x06055924                hx330x_csiISRRegiser
                0x06055960                hx330x_csiOutputSet
                0x0605599c                hx330x_csiMclkSet
                0x06055a78                hx330x_csiSyncSet
                0x06055adc                hx330x_csiPrioritySet
                0x06055b18                hx330x_csiTypeSet
                0x06055b9c                hx330x_csiModeSet
                0x06055bd8                hx330x_csiModeGet
                0x06055bf4                hx330x_pclk_digital_fir_Set
                0x06055c58                hx330x_pclk_analog_Set
                0x06055ca8                hx330x_pclk_inv_Set
                0x06055cec                hx330x_csi_clk_tun_Set
                0x06055d44                hx330x_csiSizeSet
                0x06055d7c                hx330x_sen_Image_Size_Set
                0x06055db4                hx330x_csi_in_CropSet
                0x06055e38                hx330x_csiInputAddrSet
                0x06055e50                hx330x_csiTestModeSet
                0x06055eb0                hx330x_csiDvpClkDivSet
                0x06055ef0                hx330x_csiINTSet
                0x06055f2c                hx330x_csiEnable
                0x06055fbc                hx330x_csiLCDScalerDoneCheck
                0x06055fe4                hx330x_csiToMjpAddrCfg
                0x06056000                hx330x_csiMJPEGFrameSet
                0x06056084                hx330x_csiWifiFrameSet
                0x060560dc                hx330x_csiLCDFrameSet
                0x06056108                hx330x_csi_YUVFrameSet
                0x06056168                hx330x_csiMJPEGScaler
                0x060562ec                hx330x_csiMJPEGCrop
                0x060563ec                hx330x_csiWifiScaler
                0x06056578                hx330x_csiSetLsawbtooth
                0x060565ac                hx330x_csiLCDScaler
                0x060566c0                hx330x_csiMJPEGDmaEnable
                0x06056760                hx330x_csiWifiDmaEnable
                0x06056800                hx330x_csiLCDDmaEnable
                0x060568b4                hx330x_csiLCDDmaKick
                0x060568f8                hx330x_csi_common_int_set
 .text          0x0605693c      0x4b0 ..\lib\libmcu.a(hx330x_dac.o)
                0x0605697c                hx330x_dacTypeCfg
                0x060569c0                hx330x_dacSampleRateSet
                0x06056a30                hx330x_dacEnable
                0x06056ab4                hx330x_dacVolumeSet
                0x06056b2c                hx330x_dacReset
                0x06056b50                hx330x_dacStop
                0x06056b80                hx330x_dacISRRegister
                0x06056ba0                hx330x_dacHPSet
                0x06056c34                eq_coeff_init
                0x06056ca4                eq_gain_init
                0x06056d14                hx330x_dacInit
 .text          0x06056dec      0x13c ..\lib\libmcu.a(hx330x_dma.o)
                0x06056dec                hx330x_dmaNocCfg
                0x06056e60                hx330x_dmaNocWinA
                0x06056e88                hx330x_dmaNocWinB
                0x06056eb0                hx330x_dmaNocWinDis
                0x06056ed8                hx330x_dmaChannelEnable
 .text          0x06056f28      0x41c ..\lib\libmcu.a(hx330x_dmauart.o)
                0x06056f28                hx330x_DmaUart_sta_cfg
                0x06056f68                hx330x_DmaUart_con_cfg
                0x06056fa8                hx330x_DmaUartIOCfg
                0x06057100                hx330x_DmaUartInit
                0x06057170                hx330x_DmaUart_CallbackRegister
                0x06057190                hx330x_dmauart_sendbyte
                0x060571c0                hx330x_dmauart_sendDmakick
                0x060571f4                hx330x_dmauart_sendDma
                0x06057270                hx330x_dmauart_recvAutoDmakick
                0x060572bc                hx330x_dmauart_recvBytekick
                0x060572f0                hx330x_dmauart_rxOutAdrGet
                0x0605730c                hx330x_dmauart_rxCntGet
                0x06057328                hx330x_dmauart_rxFifoOut
 .text          0x06057344      0x8e8 ..\lib\libmcu.a(hx330x_gpio.o)
                0x06057344                hx330x_gpioSFRSet
                0x06057588                hx330x_gpioSFRGet
                0x06057744                hx330x_gpioHystersisSet
                0x060577d0                hx330x_GPIO_FUNC
                0x0605783c                hx330x_gpioCommonConfig
                0x060578d0                hx330x_gpioLedInit
                0x06057960                hx330x_gpioINTCheck
                0x06057998                hx330x_gpioINTClear
                0x060579bc                hx330x_gpioINTInit
                0x06057ad8                hx330x_io1d1_softstart
                0x06057b94                hx330x_io1d1_pd_enable
 .text          0x06057c2c      0xbd0 ..\lib\libmcu.a(hx330x_iic.o)
                0x06057cfc                soft_iic0_sdaout
                0x06057d48                soft_iic0_sdain
                0x06057d94                soft_iic0_sda_set
                0x06057dc8                soft_iic0_scl_set
                0x06057e7c                soft_iic0_sda_get
                0x06057eac                soft_iic0_init
                0x06057edc                soft_iic0_start
                0x06057f5c                soft_iic0_stop
                0x06057fdc                soft_iic0_wait_ack
                0x060580a4                hx330x_iic0Init
                0x0605814c                hx330x_iic0Uninit
                0x06058190                hx330x_iic0Start
                0x060581d0                hx330x_iic0Stop
                0x06058254                hx330x_iic0RecvACK
                0x060582a8                hx330x_iic0SendACK
                0x060582f0                hx330x_iic0SendByte
                0x06058410                hx330x_iic0RecvByte
                0x06058540                hx330x_iic1Init
                0x060585d8                hx330x_iic1Uninit
                0x06058650                hx330x_iic1Start
                0x0605866c                hx330x_iic1Stop
                0x060586cc                hx330x_iic1RecvACK
                0x060586f4                hx330x_iic1SendACK
                0x06058718                hx330x_iic1SendByte
                0x06058788                hx330x_iic1RecvByte
 .text          0x060587fc      0x18c ..\lib\libmcu.a(hx330x_int.o)
                0x060587fc                hx330x_int_priority
                0x06058844                hx330x_intInit
 .text          0x06058988     0x15d4 ..\lib\libmcu.a(hx330x_isp.o)
                0x06058988                hx330x_isp_mask_tab_cfg
                0x060589c0                hx330x_ispModeSet
                0x060589fc                hx330x_isp_BLC_cfg
                0x06058a48                hx330x_isp_LSC_cfg
                0x06058a80                hx330x_isp_DDC_cfg
                0x06058bf8                hx330x_isp_AWB_GAIN_adj
                0x06058c2c                hx330x_isp_whtpnt_stat_cfg
                0x06058e98                hx330x_isp_CCM_cfg
                0x06058f5c                hx330x_isp_RGB_DGAIN_adj
                0x06058fec                hx330x_isp_hist_stat_cfg
                0x06059074                hx330x_isp_YGAMMA_cfg
                0x060591a4                hx330x_isp_ylog_ygamma_cal
                0x060591e8                hx330x_isp_RGBGAMMA_cfg
                0x06059378                hx330x_isp_CH_cfg
                0x060596d0                hx330x_isp_VDE_cfg
                0x060597a4                hx330x_isp_EE_cfg
                0x06059b70                hx330x_isp_CCF_cfg
                0x06059cbc                hx330x_isp_SAJ_cfg
                0x06059dd4                hx330x_isp_kick_stat
                0x06059e04                hx330x_isp_stat_en
                0x06059e48                hx330x_isp_stat_cp_kick_st
                0x06059e6c                hx330x_isp_stat_cp_done
                0x06059e9c                hx330x_isp_model_cfg
 .text          0x06059f5c        0x0 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .text          0x06059f5c     0x19b8 ..\lib\libmcu.a(hx330x_jpg.o)
                0x06059f5c                hx330x_mjpA_EncodeISRRegister
                0x06059f7c                hx330x_MJPA_EncodeLcdPreRegister
                0x06059f9c                hx330x_MJPA_EncodeLcdPre_Func_call
                0x06059fd0                hx330x_MJPA_EncodeLcdPre_Func_Check
                0x06059ffc                hx330x_MJPA_EncodeLcdBackRegister
                0x0605a01c                hx330x_MJPA_EncodeLcdBack_Func_call
                0x0605a050                hx330x_MJPA_EncodeLcdBack_Func_Check
                0x0605a07c                hx330x_mjpB_EncodeISRRegister
                0x0605a09c                hx330x_mjpA_isr_check
                0x0605a0c8                hx330x_mjpB_DecodeISRRegister
                0x0605a0e8                hx330x_mjpB_Encode_StartFunc_Check
                0x0605a114                hx330x_mjpB_Encode_StartFunc_Reg
                0x0605a134                hx330x_mjpB_Encode_StartFunc_call
                0x0605a168                hx330x_mjpA_Encode_StartFunc_Check
                0x0605a194                hx330x_mjpA_Encode_StartFunc_Reg
                0x0605a1b4                hx330x_mjpA_Encode_StartFunc_call
                0x0605a1e8                hx330x_mjpA_reset
                0x0605a22c                hx330x_mjpB_reset
                0x0605a27c                hx330x_mjpA_EncodeSizeSet
                0x0605a324                hx330x_mjpA_EncodeSizeSet2
                0x0605a4ac                hx330x_mjpA_EncodeQuilitySet
                0x0605a4ec                hx330x_mjpA_EncodeInfoSet
                0x0605a52c                hx330x_mjpA_EncodeBufferSet
                0x0605a578                hx330x_mjpA_Encode_inlinebuf_init
                0x0605a5d0                hx330x_mjpA_Encode_manual_on
                0x0605a630                hx330x_mjpA_Encode_manual_stop
                0x0605a65c                hx330x_mjpA_EncodeEnable
                0x0605a6d8                hx330x_mjpA_EncodeQadj
                0x0605a7a0                hx330x_mjpA_EncodeInit
                0x0605a848                hx330x_mjpA_EncodeDriModeSet
                0x0605a8b0                hx330x_cal_jASize
                0x0605a8e0                hx330x_mjpA_Encode_check
                0x0605aa7c                hx330x_mjpA_Flag_Clr
                0x0605aa94                hx330x_mjpB_EncodeQuilitySet
                0x0605aad4                hx330x_mjpB_EncodeQadj
                0x0605ab9c                hx330x_mjpB_Encodeinit
                0x0605ad20                hx330x_cal_jBSize
                0x0605ad50                hx330x_mjpB_Encode_inlinebuf_init
                0x0605ad7c                hx330x_mjpB_Encode_output_init
                0x0605ada8                hx330x_mjpB_Encode_manual_stop
                0x0605ae10                hx330x_mjpB_Encode_manual_start
                0x0605ae5c                hx330x_mjpB_EncodeLoadAddrGet
                0x0605ae78                hx330x_mjpB_as_Encode
                0x0605aea4                hx330x_mjpB_DecodeScalerCal
                0x0605af74                hx330x_mjpB_DecodeSetSize
                0x0605b1a8                hx330x_mjpB_DecodeOutputSet
                0x0605b1c4                hx330x_mjpB_DecodeInputSet
                0x0605b208                hx330x_mjpB_DecodeInputResume
                0x0605b240                hx330x_mjpB_DecodeDriSet
                0x0605b258                hx330x_mjpB_DecodeCompressSet
                0x0605b270                hx330x_mjpB_DecodeInitTable
                0x0605b2a0                hx330x_mjpB_yuvfmt_set
                0x0605b2ec                hx330x_mjpB_DecodeInit
                0x0605b444                hx330x_mjpB_DecodeDCTimeSet
                0x0605b4f4                hx330x_mjpB_DecodeEnable
                0x0605b564                hx330x_mjpB_DecodeKick
                0x0605b5ac                hx330x_mjpB_DecodeStop
                0x0605b5e8                hx330x_mjpB_DecodeQDTCfg
                0x0605b63c                hx330x_mjpB_DecodeBusyCheck
                0x0605b670                hx330x_mjpB_DecodeCheck
                0x0605b6a0                hx330x_mjpB_DecodeODma1Cfg
                0x0605b750                hx330x_mjpB_DecodePacket_check
                0x0605b814                hx330x_mjpB_Decode_InResume
                0x0605b838                hx330x_mjpB_Decode_check
 .text          0x0605b914      0x1c8 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                0x0605b914                hx330x_mjpA_table_init
                0x0605b9ec                hx330x_mjpB_table_init
 .text          0x0605badc     0x10a0 ..\lib\libmcu.a(hx330x_lcd.o)
                0x0605badc                hx330x_lcdReset
                0x0605baf0                hx330x_lcdSPIMode
                0x0605bb1c                hx330x_lcdSPIInit
                0x0605bc3c                hx330x_lcdSPIUninit
                0x0605bc98                hx330x_lcdSPISendData
                0x0605be0c                hx330x_lcdMcuSendCmd
                0x0605be80                hx330x_lcdMcuSendData
                0x0605bedc                hx330x_lcdMcuSendCmd16
                0x0605bf70                hx330x_lcdMcuSendData16
                0x0605c004                hx330x_lcdInit
                0x0605c050                hx330x_lcdIRQEnable
                0x0605c098                hx330x_lcdPreLineSet
                0x0605c0b4                hx330x_lcdSignalSet
                0x0605c14c                hx330x_lcdBusWidth
                0x0605c1a0                hx330x_lcdBusEnable
                0x0605c1f0                hx330x_lcdClkSet
                0x0605c20c                hx330x_lcdSyncSet
                0x0605c230                hx330x_lcdDESignalSet
                0x0605c254                hx330x_lcdPositionSet
                0x0605c270                hx330x_lcdResolutionSet
                0x0605c28c                hx330x_lcdWindowSizeSet
                0x0605c2a8                hx330x_lcdDataModeSet
                0x0605c2ec                hx330x_lcdClkNumberSet
                0x0605c304                hx330x_lcdEndLineSet
                0x0605c320                hx330x_lcdPanelMode
                0x0605c368                hx330x_lcdEnable
                0x0605c3cc                hx330x_lcdTeMode
                0x0605c414                hx330x_lcdTeCheck
                0x0605c490                hx330x_lcdISRRegister
                0x0605c4d4                hx330x_lcdRGBTimimgInit
                0x0605c724                hx330x_lcdMCUTimimgInit
                0x0605c8f8                hx330x_lcdRGBIOConfig
                0x0605ca58                hx330x_lcdMCUIOConfig
 .text          0x0605cb7c      0x238 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                0x0605cb7c                hx330x_rotateISRRegiser
                0x0605cb9c                hx330x_rotateCheckBusy
                0x0605cbbc                hx330x_rotateReset
                0x0605cc00                hx330x_rotateStart
                0x0605cd20                hx330x_rotateWaitFrameDone
                0x0605cd84                hx330x_rotateGetSrcYAddr
 .text          0x0605cdb4      0x874 ..\lib\libmcu.a(hx330x_lcdui.o)
                0x0605cdb4                hx330x_checkLcdShowStatus
                0x0605cdd8                hx330x_lcdShowISRRegister
                0x0605cdf8                hx330x_lcdshowInit
                0x0605ce74                hx330x_lcdshowSetCritical
                0x0605ceb8                hx330x_lcdSetVideoBgColor
                0x0605ceec                hx330x_lcdSetVideoBSC
                0x0605cf38                hx330x_lcdSetVideoBrightness
                0x0605cf90                hx330x_lcdVideoSetRgbWidth
                0x0605cffc                hx330x_lcdVideoSetScalePara
                0x0605d048                hx330x_lcdVideoSetScaleLine
                0x0605d0b0                hx330x_lcdVideoGetYAddr
                0x0605d0e0                hx330x_lcdVideoGetUVAddr
                0x0605d110                hx330x_lcdVideoSetSize
                0x0605d148                hx330x_lcdvideoMemcpy
                0x0605d1a8                hx330x_lcdvideoEnable
                0x0605d1ec                hx330x_lcdVideoSetGAMA
                0x0605d298                hx330x_lcdVideo_CCM_cfg
                0x0605d380                hx330x_lcdVideo_SAJ_cfg
                0x0605d3ec                hx330x_lcdvideoGammaEnable
                0x0605d430                hx330x_lcdUiSetSize
                0x0605d47c                hx330x_lcdUiSetPosition
                0x0605d4c0                hx330x_lcdUiSetPalette
                0x0605d524                hx330x_UiGetAddr
                0x0605d574                hx330x_lcdUiSetAlpha
                0x0605d5d8                hx330x_lcdUiLzoSoftCreate
 .text          0x0605d628      0x32c ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                0x0605d628                hx330x_uiLzoISRRegiser
                0x0605d648                hx330x_uiLzoCheckBusy
                0x0605d66c                hx330x_uiLzoReset
                0x0605d6b0                hx330x_uiLzoGetOutSize
                0x0605d6d0                hx330x_uiLzoWaiDone
                0x0605d778                hx330x_uiLzoStart
                0x0605d878                hx330x_uiLzoKick
 .text          0x0605d954      0x14c ..\lib\libmcu.a(hx330x_lcdwin.o)
                0x0605d954                hx330x_lcdWinABConfig
                0x0605d9d0                hx330x_lcdWinABEnable
                0x0605da14                hx330x_lcdWinReset
                0x0605da54                hx330x_lcdWinGetTopLayer
                0x0605da78                hx330x_lcdWinGetBotLayer
 .text          0x0605daa0      0x118 ..\lib\libmcu.a(hx330x_md.o)
                0x0605daa0                hx330x_mdEnable
                0x0605db08                hx330x_mdEnable_check
                0x0605db28                hx330x_mdInit
                0x0605db70                hx330x_mdXPos
                0x0605db94                hx330x_mdYPos
 .text          0x0605dbb8      0x304 ..\lib\libmcu.a(hx330x_mipi.o)
                0x0605dbb8                hx330x_mipiClkCfg
                0x0605dc0c                hx330x_MipiCSIUinit
                0x0605dc6c                hx330x_MipiCSIInit
 .text          0x0605debc      0x860 ..\lib\libmcu.a(hx330x_misc.o)
                0x0605debc                hx330x_sin
                0x0605df04                hx330x_cos
                0x0605df28                hx330x_abs
                0x0605df48                hx330x_dif_abs
                0x0605df6c                hx330x_max
                0x0605df90                hx330x_clip
                0x0605dfc0                hx330x_str_cpy
                0x0605e024                hx330x_str_ncpy
                0x0605e09c                hx330x_str_char
                0x0605e0e4                hx330x_str_cmp
                0x0605e168                hx330x_str_ncmp
                0x0605e210                hx330x_str_len
                0x0605e24c                hx330x_str_cat
                0x0605e2bc                hx330x_str_seek
                0x0605e328                hx330x_strTransform
                0x0605e37c                hx330x_dec_num2str
                0x0605e3bc                hx330x_char2hex
                0x0605e410                hx330x_str2num
                0x0605e468                hx330x_hex2str
                0x0605e4e0                hx330x_num2str
                0x0605e544                hx330x_num2str_cnt
                0x0605e5d0                hx330x_CountToString
                0x0605e6bc                hx330x_str_noftcpy
                0x0605e6e4                hx330x_greatest_divisor
 .text          0x0605e71c      0xd00 ..\lib\libmcu.a(hx330x_rtc.o)
                0x0605e71c                hx330x_rtcRamRead
                0x0605e7b8                hx330x_rtcRamWrite
                0x0605e854                hx330x_rtcSecondTrim
                0x0605e9e8                hx330x_rtcInit
                0x0605ec38                hx330x_rtc128K_div_cfg
                0x0605ecc4                hx330x_rtc128K_trim
                0x0605ed74                hx330x_rtcSencodEnable
                0x0605edf0                hx330x_rtcAlamEnable
                0x0605eed8                hx330x_rtcAlamSet
                0x0605ef20                hx330x_rtcGet
                0x0605ef74                hx330x_rtc_alarm_weakup_reset
                0x0605effc                hx330x_rtcSet
                0x0605f044                hx330x_VDDGSENEnable
                0x0605f0b4                hx330x_WKI0InputEnable
                0x0605f124                hx330x_WKI1InputEnable
                0x0605f194                hx330x_WKI1Read
                0x0605f1b8                hx330x_WKI0Read
                0x0605f1dc                hx330x_WKI0WakeupEnable
                0x0605f31c                hx330x_rtcBatDecEnable
                0x0605f38c                hx330x_rtcSenHVEnable
                0x0605f3fc                hx330x_rtcAlarmWakeUpFlag
 .text          0x0605f41c      0x788 ..\lib\libmcu.a(hx330x_sd.o)
                0x0605f41c                hx330x_sd0Init
                0x0605f570                hx330x_sd0Uninit
                0x0605f644                hx330x_sd0BusSet
                0x0605f688                hx330x_sd0Buffer
                0x0605f6a8                hx330x_sd0ClkSet
                0x0605f740                hx330x_sd1Init
                0x0605f8e8                hx330x_sd1Uninit
                0x0605f9ec                hx330x_sd1BusSet
                0x0605fa30                hx330x_sd1WaitDAT0
                0x0605fad0                hx330x_sd1GetRsp
                0x0605faec                hx330x_sd1Buffer
                0x0605fb0c                hx330x_sd1ClkSet
 .text          0x0605fba4        0x0 ..\lib\libmcu.a(hx330x_spi0.o)
 .text          0x0605fba4      0x4ac ..\lib\libmcu.a(hx330x_spi1.o)
                0x0605fba4                hx330x_spi1_pin_config
                0x0605fc24                hx330x_spi1_CS_Config
                0x0605fc80                hx330x_spi1Init
                0x0605fd30                hx330x_spi1DMAIRQ_CallbackRegister
                0x0605fd50                hx330x_spi1SendByte
                0x0605fdd0                hx330x_spi1RecvByte
                0x0605fe18                hx330x_sp1RecvDmaKick
                0x0605fe70                hx330x_spi1DmaDoneCheck
                0x0605fe94                hx330x_sp1SendDmaKick
                0x0605fef0                hx330x_sp1RecvDma
                0x0605ff8c                hx330x_sp1SendDma
                0x0606000c                hx330x_sp1Enable
 .text          0x06060050      0x8bc ..\lib\libmcu.a(hx330x_sys.o)
                0x06060050                hx330x_word_memcpy
                0x060600c0                hx330x_halfword_memcpy
                0x06060130                hx330x_word_memset
                0x0606018c                hx330x_mtsfr_memcpy
                0x060601f0                hx330x_mfsfr_memcpy
                0x06060254                table_init_data
                0x060602d4                hx330x_sysDcacheInit
                0x06060318                hx330x_sysIcacheInit
                0x0606035c                hx330x_sysDcacheInvalid
                0x060603f4                hx330x_sysSRAMClear
                0x06060448                hx330x_sysBSSClear
                0x060604ac                hx330x_sysLDOSet
                0x0606065c                hx330x_sysReset
                0x0606069c                hx330x_mcpy0_llp
                0x0606072c                hx330x_sysInit
                0x06060858                hx330x_sysUninit
 .text          0x0606090c      0x658 ..\lib\libmcu.a(hx330x_timer.o)
                0x0606090c                hx330x_timerISRRegister
                0x06060950                hx330x_timerStart
                0x06060a9c                hx330x_timerStop
                0x06060b6c                hx330x_timerEnable
                0x06060bf8                hx330x_timerDisable
                0x06060c9c                hx330x_timerPWMStart
 .text          0x06060f64      0x444 ..\lib\libmcu.a(hx330x_tminf.o)
                0x06060f64                hx330x_mjpA_TimeinfoEnable
                0x06060fbc                hx330x_mjpB_TimeinfoEnable
                0x06061028                hx330x_mjpA_TimeinfoColor
                0x0606106c                hx330x_mjpB_TimeinfoColor
                0x060610c0                hx330x_mjpA_TimeinfoSize
                0x0606111c                hx330x_mjpB_TimeinfoSize
                0x060611a4                hx330x_mjpA_TimeinfoPos
                0x06061200                hx330x_mjpB_TimeinfoPos
                0x06061288                hx330x_mjpA_TimeinfoAddr
                0x060612cc                hx330x_mjpB_TimeinfoAddr
                0x0606133c                hx330x_recfg_mjpb_tminf
 .text          0x060613a8      0x224 ..\lib\libmcu.a(hx330x_uart.o)
                0x060613a8                hx330x_uart0IOCfg
                0x0606152c                hx330x_uart0Init
 .text          0x060615cc      0xe10 ..\lib\libmcu.a(hx330x_usb.o)
                0x060615cc                hx330x_usb20_CallbackInit
                0x060615fc                hx330x_usb20_CallbackRegister
                0x06061638                hx330x_usb20_eptx_register
                0x060616d0                hx330x_usb20_eprx_register
                0x06061760                hx330x_USB20_EPTX_Flush
                0x0606178c                hx330x_usb20_HighSpeed
                0x060617b8                hx330x_iso20_tx
                0x06061910                hx330x_iso20_tx_kick
                0x060619cc                hx330x_usb20_dev_reset
                0x06061a40                hx330x_usb20_host_speed_connect
                0x06061ae0                hx330x_usb20_host_reset
                0x06061b48                hx330x_usb20_dev_init
                0x06061ca4                hx330x_usb20_host_init
                0x06061e88                hx330x_usb20_uinit
                0x06061f00                get_u16softcnt
                0x06061f20                hx330x_usb11_CallbackInit
                0x06061f50                hx330x_usb11_CallbackRegister
                0x06061f8c                hx330x_usb11_host_init
                0x06062080                hx330x_usb11_host_eprx_register
                0x0606212c                hx330x_usb11_host_reset
                0x06062184                hx330x_usb11_host_speed_connect
                0x060621cc                hx330x_usb11_uinit
                0x06062294                hx330x_usb20_dev_check_init
                0x060622b0                hx330x_usb20_host_check_init
                0x060622cc                hx330x_usb11_host_check_init
                0x060622e8                hx330x_usb20_device_check
                0x0606231c                hx330x_usb20_host_check
                0x060623b0                hx330x_usb11_host_check
 .text          0x060623dc       0x14 ..\lib\libmcu.a(hx330x_wdt.o)
                0x060623dc                hx330x_wdtTimeSet
 .text          0x060623f0      0x184 ..\lib\libmcu.a(hx330x_emi.o)
                0x060623f0                hx330x_emiInit
                0x060624a0                hx330x_emiISRRegister
                0x060624d4                hx330x_emiKick
                0x06062530                hx330x_emiCheckBusy
                0x06062550                hx330x_emiCheckRXError
 .text          0x06062574     0x1c34 ..\lib\libisp.a(hal_isp.o)
                0x0606289c                hal_sensor_fps_adpt
                0x0606297c                hal_isp_process
                0x060638e0                hal_sensor_awb_scene_set
                0x060639d0                hal_sensor_EV_set
                0x06063ae8                hal_isp_br_get
                0x06063b08                hal_ispService
                0x06063be8                hal_isp_init
                0x06063f88                hal_SensorRegister
                0x06064020                hal_SensorApiGet
                0x06064040                hal_SensorResolutionGet
                0x0606409c                hal_SensorRotate
                0x06064108                hal_isplog_cnt
                0x06064158                hal_isp_cur_yloga
 .text          0x060641a8      0xd64 ..\lib\libjpg.a(hal_jpg.o)
                0x060642d8                hal_mjp_enle_init
                0x06064308                hal_mjp_enle_unit
                0x060643ac                hal_mjp_enle_check
                0x0606495c                hal_mjp_enle_manual_kickstart
                0x06064b0c                hal_mjp_enle_buf_mdf
                0x06064c80                hal_mjp_enle_manual_done
 .text          0x06064f0c      0xe58 ..\lib\liblcd.a(hal_lcd.o)
                0x06064f60                hal_lcdWinUpdata
                0x0606505c                hal_lcdVideoRotateUpdata
                0x060651a4                hal_lcdCsiShowStop
                0x06065224                hal_lcdVideoShowFrameGet
                0x06065294                hal_lcdVideoIdleFrameMalloc
                0x060652d8                hal_lcdCsiShowStart
                0x06065440                hal_lcdVideoSetFrameWait
                0x06065484                hal_lcdVideoSetFrame
                0x06065568                hal_lcdBrightnessGet
                0x06065598                hal_lcdRegister
                0x06065c60                hal_lcdFrameEndCallBackRegister
                0x06065ca8                hal_lcd_send_cmd
 .text          0x06065d64      0xe88 ..\lib\liblcd.a(hal_lcdMem.o)
                0x06065e34                hal_lcdframes_vr_init
                0x06065fb4                hal_lcdframes_vd_init
                0x06066144                hal_dispframePrintf
                0x06066194                hal_dispframeInit
                0x06066924                hal_dispframeUinit
                0x060669b0                hal_dispframeMalloc
                0x06066a78                hal_lcdVideoFrameFlush
 .text          0x06066bec      0x384 ..\lib\liblcd.a(hal_lcdrotate.o)
                0x06066d74                hal_rotateInit
                0x06066dc8                hal_rotateAdd
 .text          0x06066f70      0x718 ..\lib\liblcd.a(hal_lcdUi.o)
                0x06066f70                hal_uiRotateBufMalloc
                0x06066f94                hal_uiLzoBufMalloc
                0x06066fb8                hal_uiDrawBufMalloc
                0x06067028                hal_lcdUiKickWait
                0x06067084                hal_lcdUiKick
                0x06067110                hal_lcdUiSetAddr
                0x06067180                hal_lcdUiSetBuffer
                0x06067214                hal_lcdUiSetBufferWaitDone
                0x06067268                hal_lcdUiInit
                0x06067404                hal_lcdUiSetPosition
                0x06067480                hal_lcdUiSetPalette
                0x060674d4                hal_lcdUiSetSize
                0x06067550                hal_lcdUiSetAlpha
                0x060675cc                hal_lcdUiResolutionGet
                0x06067638                hal_lcdVideo_CCM_cfg
                0x06067660                hal_lcdVideo_SAJ_cfg
 .text          0x06067688      0x1a8 ..\lib\liblcd.a(hal_lcdUiLzo.o)
                0x06067724                hal_uiLzokick
                0x06067808                hal_uiLzoInit
 .text          0x06067830      0x1ec ..\lib\liblcd.a(lcd_tab.o)
                0x06067830                hal_lcdParaLoad
                0x060678f4                hal_lcdSetLsawbtooth
                0x060679a8                hal_lcdPQToolGetInfo
 .text          0x06067a1c      0xa70 ..\lib\libmultimedia.a(api_multimedia.o)
                0x06067bd4                api_multimedia_cache_init
                0x06067cd0                api_multimedia_cachePreRead
                0x06067d60                api_multimedia_cacheRead
                0x06067ddc                api_multimedia_init
                0x06067f2c                api_multimedia_uninit
                0x06067fb0                api_multimedia_start
                0x0606802c                api_multimedia_end
                0x060680a8                api_multimedia_service
                0x06068124                api_multimedia_addjunk
                0x060681a0                api_multimedia_encodeframe
                0x06068220                api_multimedia_decodeframe
                0x0606829c                api_multimedia_gettime
                0x06068318                api_multimedia_getsta
                0x06068394                api_multimedia_decodefast
                0x06068410                api_multimedia_getArg
 .text          0x0606848c     0x1440 ..\lib\libmultimedia.a(avi_dec.o)
 .text          0x060698cc      0xed4 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .text          0x0606a7a0      0xaa4 ..\lib\libmultimedia.a(avi_std_enc.o)
                0x0606adf8                avi_stdEncidxEnd
                0x0606aeec                avi_stdEncEnd
 .text          0x0606b244      0x958 ..\lib\libmultimedia.a(wav_dec.o)
 .text          0x0606bb9c      0x414 ..\lib\libmultimedia.a(wav_enc.o)
                0x0606bb9c                wav_EncEnd
 .text          0x0606bfb0      0x1c8 ..\lib\libmultimedia.a(wav_pcm.o)
                0x0606bfb0                pcm_encode
                0x0606c060                pcm_decode
 .text          0x0606c178       0xb0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                0x0606c178                memcmp
 .text          0x0606c228      0x158 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                0x0606c228                memcpy
 .text          0x0606c380      0x15c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                0x0606c380                memset
 .text          0x0606c4dc       0xc0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                0x0606c4dc                strcpy
 .text          0x0606c59c      0x7c8 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                0x0606c59c                __udivdi3
 .text          0x0606cd64      0x7c8 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                0x0606cd64                __umoddi3
 .text          0x0606d52c       0xf4 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__udivsi3.o)
                0x0606d52c                __udivsi3
                0x0606d52c                __udivsi3_internal
 .text          0x0606d620       0x1c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(__umodsi3.o)
                0x0606d620                __umodsi3
 .text          0x0606d63c        0x0 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
 *(.rodata*)
 .rodata.str1.1
                0x0606d63c       0x15 obj\Debug\dev\battery\src\battery_api.o
 *fill*         0x0606d651        0x3 
 .rodata        0x0606d654        0xc obj\Debug\dev\battery\src\battery_api.o
 .rodata.str1.1
                0x0606d660       0x49 obj\Debug\dev\dev_api.o
 .rodata.str1.1
                0x0606d6a9       0x65 obj\Debug\dev\fs\src\ff.o
                                 0x69 (size before relaxing)
 *fill*         0x0606d70e        0x2 
 .rodata.cst4   0x0606d710        0x4 obj\Debug\dev\fs\src\ff.o
 .rodata        0x0606d714       0xb0 obj\Debug\dev\fs\src\ff.o
 .rodata        0x0606d7c4      0x3d4 obj\Debug\dev\fs\src\ffunicode.o
 .rodata.str1.1
                0x0606db98       0xb7 obj\Debug\dev\fs\src\fs_api.o
                                 0xb8 (size before relaxing)
 .rodata.str1.1
                0x0606dc4f       0x7a obj\Debug\dev\gsensor\src\gsensor_api.o
 *fill*         0x0606dcc9        0x3 
 .rodata        0x0606dccc        0xc obj\Debug\dev\gsensor\src\gsensor_api.o
 .rodata        0x0606dcd8       0x38 obj\Debug\dev\gsensor\src\gsensor_da380.o
                0x0606dcf0                da380
 .rodata        0x0606dd10       0x38 obj\Debug\dev\gsensor\src\gsensor_gma301.o
                0x0606dd28                gma301
 .rodata        0x0606dd48       0x38 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
                0x0606dd60                sc7a30e
 .rodata.str1.1
                0x0606dd80       0x16 obj\Debug\dev\lcd\src\lcd_api.o
 .rodata.str1.1
                0x0606dd96       0x1d obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .rodata.str1.1
                0x0606ddb3       0x6a obj\Debug\dev\nvfs\src\nvfs_api.o
 .rodata.str1.1
                0x0606de1d       0x8b obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .rodata.str1.1
                0x0606dea8      0x143 obj\Debug\dev\sd\src\sd_api.o
 *fill*         0x0606dfeb        0x1 
 .rodata        0x0606dfec      0x160 obj\Debug\dev\sd\src\sd_api.o
                0x0606dfec                sd_ident_tab
 .rodata.str1.1
                0x0606e14c       0xc3 obj\Debug\dev\sensor\src\sensor_api.o
 *fill*         0x0606e20f        0x1 
 .rodata        0x0606e210       0x44 obj\Debug\dev\sensor\src\sensor_api.o
                0x0606e210                user_ccf_dn_tab
                0x0606e21c                user_ee_dn_tab
                0x0606e238                user_ee_sharp_tab
 .rodata        0x0606e254      0x758 obj\Debug\dev\sensor\src\sensor_tab.o
                0x0606e254                test_img_init
                0x0606e298                test_img_adpt
 .rodata.str1.1
                0x0606e9ac       0x57 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .rodata.str1.1
                0x0606ea03       0x47 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 *fill*         0x0606ea4a        0x2 
 .rodata        0x0606ea4c       0x1c obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                0x0606ea4c                tp_icnt81
 .rodata.str1.1
                0x0606ea68       0xa8 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .rodata.str1.1
                0x0606eb10       0x13 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                                 0x17 (size before relaxing)
 *fill*         0x0606eb23        0x1 
 .rodata        0x0606eb24       0x2c obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                0x0606eb34                tp_ns2009
 .rodata.str1.1
                0x0606eb50       0x50 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .rodata        0x0606eba0      0x100 obj\Debug\dev\usb\dusb\src\dusb_enum.o
                0x0606ebec                StringDescTbl
                0x0606ec04                DEV_QAULIFIER_DESC_DATA
                0x0606ec10                UsbStrDescSerialNumber
                0x0606ec30                UsbStrDescProduct_1
                0x0606ec48                UsbStrDescProduct_0
                0x0606ec60                UsbStrDescManufacturer
                0x0606ec70                UsbLanguageID
                0x0606ec74                dusb_com_devdsc
                0x0606ec88                dusb_msc_devdsc
                0x0606ec9c                SDK_CHIP_INF
 .rodata.str1.1
                0x0606eca0       0x4d obj\Debug\dev\usb\dusb\src\dusb_msc.o
 *fill*         0x0606eced        0x3 
 .rodata        0x0606ecf0       0x40 obj\Debug\dev\usb\dusb\src\dusb_msc.o
                0x0606ecf0                MscSenseCode
                0x0606ecfc                device_inquiry_data
 .rodata.str1.1
                0x0606ed30        0xc obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .rodata        0x0606ed3c        0x4 obj\Debug\dev\usb\dusb\src\dusb_uac.o
                0x0606ed3c                uac_vol_tbl
 .rodata        0x0606ed40      0x1e0 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                0x0606ed54                uvc_ctl_tab
                0x0606ee94                unit_callback
                0x0606eee4                vc_still_probe_commit_desc
                0x0606eef0                vc_probe_commit_desc
                0x0606ef0c                uvc_res_tab
 .rodata.str1.1
                0x0606ef20       0x69 obj\Debug\dev\usb\husb\src\husb_api.o
 .rodata.str1.1
                0x0606ef89      0x5e4 obj\Debug\dev\usb\husb\src\husb_enum.o
                                0x5e6 (size before relaxing)
 *fill*         0x0606f56d        0x3 
 .rodata        0x0606f570      0x2c0 obj\Debug\dev\usb\husb\src\husb_enum.o
                0x0606f5a0                husb_uvcunit_set_hcdtrb
                0x0606f5c0                husb_uvcunit_get_hcdtrb
                0x0606f5e0                husb_astern_hcdtrb
                0x0606f600                husb_uvc_switch_hcdtrb
                0x0606f680                husb_enum_hcdtrb
 .rodata.str1.1
                0x0606f830       0xa8 obj\Debug\dev\usb\husb\src\husb_hub.o
 .rodata.str1.1
                0x0606f8d8      0x271 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .rodata.str1.1
                0x0606fb49       0x17 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .rodata.str1.1
                0x0606fb60      0x17b obj\Debug\dev\usb\husb\src\husb_uvc.o
                                0x198 (size before relaxing)
 .rodata.str1.1
                0x0606fcdb       0xc1 obj\Debug\hal\src\hal_auadc.o
 .rodata.str1.1
                0x0606fd9c       0x8c obj\Debug\hal\src\hal_csi.o
 .rodata        0x0606fe28       0x2c obj\Debug\hal\src\hal_dac.o
 .rodata.str1.1
                0x0606fe54       0x6b obj\Debug\hal\src\hal_dmauart.o
 .rodata.str1.1
                0x0606febf       0x8d obj\Debug\hal\src\hal_lcdshow.o
                                 0x93 (size before relaxing)
 .rodata        0x0606ff4c       0x14 obj\Debug\hal\src\hal_lcdshow.o
 .rodata.str1.1
                0x0606ff60      0x193 obj\Debug\hal\src\hal_mjpAEncode.o
                                0x195 (size before relaxing)
 *fill*         0x060700f3        0x1 
 .rodata        0x060700f4        0xc obj\Debug\hal\src\hal_mjpAEncode.o
                0x060700f4                mjpegQualityTable
 .rodata.str1.1
                0x06070100       0xbb obj\Debug\hal\src\hal_mjpBEncode.o
 .rodata.str1.1
                0x060701bb       0x47 obj\Debug\hal\src\hal_mjpDecode.o
 *fill*         0x06070202        0x2 
 .rodata        0x06070204        0x8 obj\Debug\hal\src\hal_mjpDecode.o
 .rodata.str1.1
                0x0607020c        0xa obj\Debug\hal\src\hal_rtc.o
 *fill*         0x06070216        0x2 
 .rodata        0x06070218        0xc obj\Debug\hal\src\hal_rtc.o
                0x06070218                monDaysTable
 .rodata.str1.1
                0x06070224       0xdf obj\Debug\hal\src\hal_spi1.o
 .rodata.str1.1
                0x06070303       0xc7 obj\Debug\hal\src\hal_sys.o
 .rodata.str1.1
                0x060703ca       0x14 obj\Debug\hal\src\hal_timer.o
 .rodata.str1.1
                0x060703de       0x3a obj\Debug\hal\src\hal_watermark.o
 .rodata.str1.1
                0x06070418      0x116 obj\Debug\multimedia\audio\audio_playback.o
 .rodata.str1.1
                0x0607052e       0x98 obj\Debug\multimedia\audio\audio_record.o
 .rodata.str1.1
                0x060705c6       0xc5 obj\Debug\multimedia\image\image_decode.o
 .rodata.str1.1
                0x0607068b      0x1a4 obj\Debug\multimedia\image\image_encode.o
 *fill*         0x0607082f        0x1 
 .rodata        0x06070830      0x2dc obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                0x06070830                exif_head
 .rodata.str1.1
                0x06070b0c      0x323 obj\Debug\multimedia\video\video_playback.o
                                0x327 (size before relaxing)
 *fill*         0x06070e2f        0x1 
 .rodata        0x06070e30        0x8 obj\Debug\multimedia\video\video_playback.o
 .rodata.str1.1
                0x06070e38      0x31e obj\Debug\multimedia\video\video_record.o
 .rodata.str1.1
                0x06071156      0x1a6 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .rodata.str1.1
                0x060712fc       0x95 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                                 0xb1 (size before relaxing)
 *fill*         0x06071391        0x3 
 .rodata        0x06071394      0xeb0 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
                0x06071394                ascii_num1_table
                0x0607150c                ascii_num1_126
                0x06071530                ascii_num1_125
                0x06071554                ascii_num1_124
                0x06071578                ascii_num1_123
                0x0607159c                ascii_num1_122
                0x060715c0                ascii_num1_121
                0x060715e4                ascii_num1_120
                0x06071608                ascii_num1_119
                0x0607162c                ascii_num1_118
                0x06071650                ascii_num1_117
                0x06071674                ascii_num1_116
                0x06071698                ascii_num1_115
                0x060716bc                ascii_num1_114
                0x060716e0                ascii_num1_113
                0x06071704                ascii_num1_112
                0x06071728                ascii_num1_111
                0x0607174c                ascii_num1_110
                0x06071770                ascii_num1_109
                0x06071794                ascii_num1_108
                0x060717b8                ascii_num1_107
                0x060717dc                ascii_num1_106
                0x06071800                ascii_num1_105
                0x06071824                ascii_num1_104
                0x06071848                ascii_num1_103
                0x0607186c                ascii_num1_102
                0x06071890                ascii_num1_101
                0x060718b4                ascii_num1_100
                0x060718d8                ascii_num1_99
                0x060718fc                ascii_num1_98
                0x06071920                ascii_num1_97
                0x06071944                ascii_num1_96
                0x06071968                ascii_num1_95
                0x0607198c                ascii_num1_94
                0x060719b0                ascii_num1_93
                0x060719d4                ascii_num1_92
                0x060719f8                ascii_num1_91
                0x06071a1c                ascii_num1_90
                0x06071a40                ascii_num1_89
                0x06071a64                ascii_num1_88
                0x06071a88                ascii_num1_87
                0x06071aac                ascii_num1_86
                0x06071ad0                ascii_num1_85
                0x06071af4                ascii_num1_84
                0x06071b18                ascii_num1_83
                0x06071b3c                ascii_num1_82
                0x06071b60                ascii_num1_81
                0x06071b84                ascii_num1_80
                0x06071ba8                ascii_num1_79
                0x06071bcc                ascii_num1_78
                0x06071bf0                ascii_num1_77
                0x06071c14                ascii_num1_76
                0x06071c38                ascii_num1_75
                0x06071c5c                ascii_num1_74
                0x06071c80                ascii_num1_73
                0x06071ca4                ascii_num1_72
                0x06071cc8                ascii_num1_71
                0x06071cec                ascii_num1_70
                0x06071d10                ascii_num1_69
                0x06071d34                ascii_num1_68
                0x06071d58                ascii_num1_67
                0x06071d7c                ascii_num1_66
                0x06071da0                ascii_num1_65
                0x06071dc4                ascii_num1_64
                0x06071de8                ascii_num1_63
                0x06071e0c                ascii_num1_62
                0x06071e30                ascii_num1_61
                0x06071e54                ascii_num1_60
                0x06071e78                ascii_num1_59
                0x06071e9c                ascii_num1_58
                0x06071ec0                ascii_num1_57
                0x06071ee4                ascii_num1_56
                0x06071f08                ascii_num1_55
                0x06071f2c                ascii_num1_54
                0x06071f50                ascii_num1_53
                0x06071f74                ascii_num1_52
                0x06071f98                ascii_num1_51
                0x06071fbc                ascii_num1_50
                0x06071fe0                ascii_num1_49
                0x06072004                ascii_num1_48
                0x06072028                ascii_num1_47
                0x0607204c                ascii_num1_46
                0x06072070                ascii_num1_45
                0x06072094                ascii_num1_44
                0x060720b8                ascii_num1_43
                0x060720dc                ascii_num1_42
                0x06072100                ascii_num1_41
                0x06072124                ascii_num1_40
                0x06072148                ascii_num1_39
                0x0607216c                ascii_num1_38
                0x06072190                ascii_num1_37
                0x060721b4                ascii_num1_36
                0x060721d8                ascii_num1_35
                0x060721fc                ascii_num1_33
                0x06072220                ascii_num1_32
 .rodata        0x06072244     0x1a70 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
                0x06072244                ascii_num2_table
                0x060723bc                ascii_num2_126
                0x06072400                ascii_num2_125
                0x06072444                ascii_num2_124
                0x06072488                ascii_num2_123
                0x060724cc                ascii_num2_122
                0x06072510                ascii_num2_121
                0x06072554                ascii_num2_120
                0x06072598                ascii_num2_119
                0x060725dc                ascii_num2_118
                0x06072620                ascii_num2_117
                0x06072664                ascii_num2_116
                0x060726a8                ascii_num2_115
                0x060726ec                ascii_num2_114
                0x06072730                ascii_num2_113
                0x06072774                ascii_num2_112
                0x060727b8                ascii_num2_111
                0x060727fc                ascii_num2_110
                0x06072840                ascii_num2_109
                0x06072884                ascii_num2_108
                0x060728c8                ascii_num2_107
                0x0607290c                ascii_num2_106
                0x06072950                ascii_num2_105
                0x06072994                ascii_num2_104
                0x060729d8                ascii_num2_103
                0x06072a1c                ascii_num2_102
                0x06072a60                ascii_num2_101
                0x06072aa4                ascii_num2_100
                0x06072ae8                ascii_num2_99
                0x06072b2c                ascii_num2_98
                0x06072b70                ascii_num2_97
                0x06072bb4                ascii_num2_96
                0x06072bf8                ascii_num2_95
                0x06072c3c                ascii_num2_94
                0x06072c80                ascii_num2_93
                0x06072cc4                ascii_num2_92
                0x06072d08                ascii_num2_91
                0x06072d4c                ascii_num2_90
                0x06072d90                ascii_num2_89
                0x06072dd4                ascii_num2_88
                0x06072e18                ascii_num2_87
                0x06072e5c                ascii_num2_86
                0x06072ea0                ascii_num2_85
                0x06072ee4                ascii_num2_84
                0x06072f28                ascii_num2_83
                0x06072f6c                ascii_num2_82
                0x06072fb0                ascii_num2_81
                0x06072ff4                ascii_num2_80
                0x06073038                ascii_num2_79
                0x0607307c                ascii_num2_78
                0x060730c0                ascii_num2_77
                0x06073104                ascii_num2_76
                0x06073148                ascii_num2_75
                0x0607318c                ascii_num2_74
                0x060731d0                ascii_num2_73
                0x06073214                ascii_num2_72
                0x06073258                ascii_num2_71
                0x0607329c                ascii_num2_70
                0x060732e0                ascii_num2_69
                0x06073324                ascii_num2_68
                0x06073368                ascii_num2_67
                0x060733ac                ascii_num2_66
                0x060733f0                ascii_num2_65
                0x06073434                ascii_num2_64
                0x06073478                ascii_num2_63
                0x060734bc                ascii_num2_62
                0x06073500                ascii_num2_61
                0x06073544                ascii_num2_60
                0x06073588                ascii_num2_59
                0x060735cc                ascii_num2_58
                0x06073610                ascii_num2_57
                0x06073654                ascii_num2_56
                0x06073698                ascii_num2_55
                0x060736dc                ascii_num2_54
                0x06073720                ascii_num2_53
                0x06073764                ascii_num2_52
                0x060737a8                ascii_num2_51
                0x060737ec                ascii_num2_50
                0x06073830                ascii_num2_49
                0x06073874                ascii_num2_48
                0x060738b8                ascii_num2_47
                0x060738fc                ascii_num2_46
                0x06073940                ascii_num2_45
                0x06073984                ascii_num2_44
                0x060739c8                ascii_num2_43
                0x06073a0c                ascii_num2_42
                0x06073a50                ascii_num2_41
                0x06073a94                ascii_num2_40
                0x06073ad8                ascii_num2_39
                0x06073b1c                ascii_num2_38
                0x06073b60                ascii_num2_37
                0x06073ba4                ascii_num2_36
                0x06073be8                ascii_num2_35
                0x06073c2c                ascii_num2_33
                0x06073c70                ascii_num2_32
 .rodata        0x06073cb4     0x5430 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
                0x06073cb4                ascii_num3_table
                0x06073e2c                ascii_num3_126
                0x06073f30                ascii_num3_125
                0x06073fb4                ascii_num3_124
                0x06074038                ascii_num3_123
                0x060740bc                ascii_num3_122
                0x06074180                ascii_num3_121
                0x06074244                ascii_num3_120
                0x06074308                ascii_num3_119
                0x0607440c                ascii_num3_118
                0x060744d0                ascii_num3_117
                0x060745d4                ascii_num3_116
                0x06074658                ascii_num3_115
                0x0607471c                ascii_num3_114
                0x060747a0                ascii_num3_113
                0x060748a4                ascii_num3_112
                0x060749a8                ascii_num3_111
                0x06074aac                ascii_num3_110
                0x06074bb0                ascii_num3_109
                0x06074cf4                ascii_num3_108
                0x06074d78                ascii_num3_107
                0x06074e3c                ascii_num3_106
                0x06074ec0                ascii_num3_105
                0x06074f44                ascii_num3_104
                0x06075048                ascii_num3_103
                0x0607514c                ascii_num3_102
                0x060751d0                ascii_num3_101
                0x060752d4                ascii_num3_100
                0x060753d8                ascii_num3_99
                0x0607549c                ascii_num3_98
                0x060755a0                ascii_num3_97
                0x060756a4                ascii_num3_96
                0x06075728                ascii_num3_95
                0x060757ec                ascii_num3_94
                0x060758b0                ascii_num3_93
                0x06075934                ascii_num3_92
                0x060759b8                ascii_num3_91
                0x06075a3c                ascii_num3_90
                0x06075b40                ascii_num3_89
                0x06075c44                ascii_num3_88
                0x06075d48                ascii_num3_87
                0x06075ecc                ascii_num3_86
                0x06075fd0                ascii_num3_85
                0x060760d4                ascii_num3_84
                0x060761d8                ascii_num3_83
                0x060762dc                ascii_num3_82
                0x060763e0                ascii_num3_81
                0x06076524                ascii_num3_80
                0x06076628                ascii_num3_79
                0x0607676c                ascii_num3_78
                0x06076870                ascii_num3_77
                0x060769b4                ascii_num3_76
                0x06076ab8                ascii_num3_75
                0x06076bbc                ascii_num3_74
                0x06076c80                ascii_num3_73
                0x06076d04                ascii_num3_72
                0x06076e08                ascii_num3_71
                0x06076f4c                ascii_num3_70
                0x06077050                ascii_num3_69
                0x06077154                ascii_num3_68
                0x06077258                ascii_num3_67
                0x0607735c                ascii_num3_66
                0x06077460                ascii_num3_65
                0x06077564                ascii_num3_64
                0x060776e8                ascii_num3_63
                0x060777ec                ascii_num3_62
                0x060778f0                ascii_num3_61
                0x060779f4                ascii_num3_60
                0x06077af8                ascii_num3_59
                0x06077b7c                ascii_num3_58
                0x06077c00                ascii_num3_57
                0x06077d04                ascii_num3_56
                0x06077e08                ascii_num3_55
                0x06077f0c                ascii_num3_54
                0x06078010                ascii_num3_53
                0x06078114                ascii_num3_52
                0x06078218                ascii_num3_51
                0x0607831c                ascii_num3_50
                0x06078420                ascii_num3_49
                0x06078524                ascii_num3_48
                0x06078628                ascii_num3_47
                0x060786ac                ascii_num3_46
                0x06078730                ascii_num3_45
                0x060787b4                ascii_num3_44
                0x06078838                ascii_num3_43
                0x0607893c                ascii_num3_42
                0x06078a00                ascii_num3_41
                0x06078a84                ascii_num3_40
                0x06078b08                ascii_num3_39
                0x06078b8c                ascii_num3_38
                0x06078c90                ascii_num3_37
                0x06078dd4                ascii_num3_36
                0x06078ed8                ascii_num3_35
                0x06078fdc                ascii_num3_33
                0x06079060                ascii_num3_32
 .rodata        0x060790e4     0x7870 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
                0x060790e4                ascii_num4_table
                0x0607925c                ascii_num4_126
                0x060793a0                ascii_num4_125
                0x060794e4                ascii_num4_124
                0x06079628                ascii_num4_123
                0x0607976c                ascii_num4_122
                0x060798b0                ascii_num4_121
                0x060799f4                ascii_num4_120
                0x06079b38                ascii_num4_119
                0x06079c7c                ascii_num4_118
                0x06079dc0                ascii_num4_117
                0x06079f04                ascii_num4_116
                0x0607a048                ascii_num4_115
                0x0607a18c                ascii_num4_114
                0x0607a2d0                ascii_num4_113
                0x0607a414                ascii_num4_112
                0x0607a558                ascii_num4_111
                0x0607a69c                ascii_num4_110
                0x0607a7e0                ascii_num4_109
                0x0607a924                ascii_num4_108
                0x0607aa68                ascii_num4_107
                0x0607abac                ascii_num4_106
                0x0607acf0                ascii_num4_105
                0x0607ae34                ascii_num4_104
                0x0607af78                ascii_num4_103
                0x0607b0bc                ascii_num4_102
                0x0607b200                ascii_num4_101
                0x0607b344                ascii_num4_100
                0x0607b488                ascii_num4_99
                0x0607b5cc                ascii_num4_98
                0x0607b710                ascii_num4_97
                0x0607b854                ascii_num4_96
                0x0607b998                ascii_num4_95
                0x0607badc                ascii_num4_94
                0x0607bc20                ascii_num4_93
                0x0607bd64                ascii_num4_92
                0x0607bea8                ascii_num4_91
                0x0607bfec                ascii_num4_90
                0x0607c130                ascii_num4_89
                0x0607c274                ascii_num4_88
                0x0607c3b8                ascii_num4_87
                0x0607c4fc                ascii_num4_86
                0x0607c640                ascii_num4_85
                0x0607c784                ascii_num4_84
                0x0607c8c8                ascii_num4_83
                0x0607ca0c                ascii_num4_82
                0x0607cb50                ascii_num4_81
                0x0607cc94                ascii_num4_80
                0x0607cdd8                ascii_num4_79
                0x0607cf1c                ascii_num4_78
                0x0607d060                ascii_num4_77
                0x0607d1a4                ascii_num4_76
                0x0607d2e8                ascii_num4_75
                0x0607d42c                ascii_num4_74
                0x0607d570                ascii_num4_73
                0x0607d6b4                ascii_num4_72
                0x0607d7f8                ascii_num4_71
                0x0607d93c                ascii_num4_70
                0x0607da80                ascii_num4_69
                0x0607dbc4                ascii_num4_68
                0x0607dd08                ascii_num4_67
                0x0607de4c                ascii_num4_66
                0x0607df90                ascii_num4_65
                0x0607e0d4                ascii_num4_64
                0x0607e218                ascii_num4_63
                0x0607e35c                ascii_num4_62
                0x0607e4a0                ascii_num4_61
                0x0607e5e4                ascii_num4_60
                0x0607e728                ascii_num4_59
                0x0607e86c                ascii_num4_58
                0x0607e9b0                ascii_num4_57
                0x0607eaf4                ascii_num4_56
                0x0607ec38                ascii_num4_55
                0x0607ed7c                ascii_num4_54
                0x0607eec0                ascii_num4_53
                0x0607f004                ascii_num4_52
                0x0607f148                ascii_num4_51
                0x0607f28c                ascii_num4_50
                0x0607f3d0                ascii_num4_49
                0x0607f514                ascii_num4_48
                0x0607f658                ascii_num4_47
                0x0607f79c                ascii_num4_46
                0x0607f8e0                ascii_num4_45
                0x0607fa24                ascii_num4_44
                0x0607fb68                ascii_num4_43
                0x0607fcac                ascii_num4_42
                0x0607fdf0                ascii_num4_41
                0x0607ff34                ascii_num4_40
                0x06080078                ascii_num4_39
                0x060801bc                ascii_num4_38
                0x06080300                ascii_num4_37
                0x06080444                ascii_num4_36
                0x06080588                ascii_num4_35
                0x060806cc                ascii_num4_33
                0x06080810                ascii_num4_32
 .rodata.str1.1
                0x06080954       0xae obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .rodata.str1.1
                0x06080a02       0x5c obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .rodata.str1.1
                0x06080a5e       0x3a obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .rodata.str1.1
                0x06080a98       0x27 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 *fill*         0x06080abf        0x1 
 .rodata        0x06080ac0      0x71c obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
                0x06080ac0                res_key_music
 .rodata.str1.1
                0x060811dc       0x13 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .rodata.str1.1
                0x060811ef       0x2e obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 *fill*         0x0608121d        0x3 
 .rodata        0x06081220       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .rodata.str1.1
                0x06081230       0x2f obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 *fill*         0x0608125f        0x1 
 .rodata        0x06081260       0x10 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .rodata        0x06081270       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .rodata.str1.1
                0x060812dc       0x17 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 *fill*         0x060812f3        0x1 
 .rodata        0x060812f4       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .rodata        0x06081360       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .rodata        0x060813cc       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .rodata.str1.1
                0x06081438      0x198 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .rodata        0x060815d0       0x1c obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .rodata.str1.1
                0x060815ec       0x5b obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 *fill*         0x06081647        0x1 
 .rodata        0x06081648       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .rodata        0x060816b4       0x6c obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .rodata        0x06081720       0x64 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .rodata.str1.1
                0x06081784      0x121 obj\Debug\app\app_common\src\app_init.o
                                0x128 (size before relaxing)
 .rodata.str1.1
                0x060818a5       0x58 obj\Debug\app\app_common\src\app_lcdshow.o
 *fill*         0x060818fd        0x3 
 .rodata        0x06081900       0x14 obj\Debug\app\app_common\src\app_lcdshow.o
 .rodata.str1.1
                0x06081914       0x8a obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                                 0x8e (size before relaxing)
 *fill*         0x0608199e        0x2 
 .rodata        0x060819a0      0x140 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                0x060819c8                dateTimeWin
 .rodata.str1.1
                0x06081ae0       0x46 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 *fill*         0x06081b26        0x2 
 .rodata        0x06081b28      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                0x06081b28                defaultWin
 .rodata.str1.1
                0x06081c40       0x43 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 *fill*         0x06081c83        0x1 
 .rodata        0x06081c84      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                0x06081c84                delAllWin
 .rodata.str1.1
                0x06081d9c       0x5e obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 *fill*         0x06081dfa        0x2 
 .rodata        0x06081dfc      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                0x06081dfc                delCurWin
 .rodata.str1.1
                0x06081f14       0x6c obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                                 0x6d (size before relaxing)
 .rodata        0x06081f80      0x118 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                0x06081f80                formatWin
 .rodata.str1.1
                0x06082098       0x56 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                                 0x5d (size before relaxing)
 *fill*         0x060820ee        0x2 
 .rodata        0x060820f0       0xd8 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                0x06082100                menuItemWin
 .rodata.str1.1
                0x060821c8       0x46 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                                 0x65 (size before relaxing)
 *fill*         0x0608220e        0x2 
 .rodata        0x06082210       0x78 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                0x06082210                lockCurWin
 .rodata.str1.1
                0x06082288       0x4f obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                                 0x51 (size before relaxing)
 *fill*         0x060822d7        0x1 
 .rodata        0x060822d8       0xc8 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                0x060822d8                menuOptionWin
 .rodata.str1.1
                0x060823a0       0x5f obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                                 0x6d (size before relaxing)
 *fill*         0x060823ff        0x1 
 .rodata        0x06082400       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                0x06082400                unlockAllWin
 .rodata.str1.1
                0x060824a0       0x4c obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                                 0x6d (size before relaxing)
 .rodata        0x060824ec       0xa0 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                0x060824ec                unlockCurWin
 .rodata.str1.1
                0x0608258c       0x5d obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 *fill*         0x060825e9        0x3 
 .rodata        0x060825ec      0x348 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                0x060825ec                asternWin
 .rodata.str1.1
                0x06082934       0x43 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 *fill*         0x06082977        0x1 
 .rodata        0x06082978       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                0x06082978                noFileWin
 .rodata.str1.1
                0x060829f0      0x11a obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                                0x17b (size before relaxing)
 *fill*         0x06082b0a        0x2 
 .rodata        0x06082b0c      0x4e8 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                0x06082b94                selfTestWin
 .rodata.str1.1
                0x06082ff4       0x40 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                                 0x42 (size before relaxing)
 .rodata        0x06083034       0xa0 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                0x06083034                tips1Win
 .rodata.str1.1
                0x060830d4       0x46 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                                 0x48 (size before relaxing)
 *fill*         0x0608311a        0x2 
 .rodata        0x0608311c       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                0x0608311c                tipsWin
 .rodata.str1.1
                0x06083194       0x43 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 *fill*         0x060831d7        0x1 
 .rodata        0x060831d8       0x78 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                0x060831d8                TpIconWin
 .rodata.str1.1
                0x06083250      0x148 obj\Debug\app\task_windows\task_api.o
 .rodata        0x06083398       0x28 obj\Debug\app\task_windows\task_api.o
 .rodata.str1.1
                0x060833c0       0x73 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 *fill*         0x06083433        0x1 
 .rodata        0x06083434       0x68 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
                0x06083434                sysComMsgDeal
 .rodata.str1.1
                0x0608349c      0x25a obj\Debug\app\task_windows\task_common\src\task_common.o
                                0x266 (size before relaxing)
 *fill*         0x060836f6        0x2 
 .rodata        0x060836f8       0x90 obj\Debug\app\task_windows\task_common\src\task_common.o
 .rodata.str1.1
                0x06083788       0x15 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 *fill*         0x0608379d        0x3 
 .rodata        0x060837a0       0x10 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
                0x060837a0                taskComMsgDeal
 .rodata.str1.1
                0x060837b0       0x6a obj\Debug\app\task_windows\task_main\src\taskMain.o
 .rodata.str1.1
                0x0608381a       0x3d obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
 *fill*         0x06083857        0x1 
 .rodata        0x06083858       0x70 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
                0x06083858                mainMenuTab
                0x06083878                mainWin
 .rodata.str1.1
                0x060838c8       0x72 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .rodata.str1.1
                0x0608393a       0x63 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                                 0x65 (size before relaxing)
 *fill*         0x0608399d        0x3 
 .rodata        0x060839a0      0x128 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                0x060839b0                playAudioWin
 .rodata.str1.1
                0x06083ac8      0x211 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .rodata.str1.1
                0x06083cd9      0x124 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                                0x156 (size before relaxing)
 *fill*         0x06083dfd        0x3 
 .rodata        0x06083e00      0x218 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                0x06083e10                playVideoMainWin
 .rodata.str1.1
                0x06084018       0x5b obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                                 0x5d (size before relaxing)
 *fill*         0x06084073        0x1 
 .rodata        0x06084074       0xc8 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                0x06084074                playVideoSlideWin
 .rodata.str1.1
                0x0608413c       0x8d obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                                 0x97 (size before relaxing)
 *fill*         0x060841c9        0x3 
 .rodata        0x060841cc      0x4d4 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                0x060841cc                playVideoThumbnallWin
 .rodata.str1.1
                0x060846a0        0xa obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .rodata.str1.1
                0x060846aa       0xb1 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .rodata.str1.1
                0x0608475b        0x9 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                                 0x16 (size before relaxing)
 .rodata        0x06084764       0xa0 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                0x06084764                RecordAudioWin
 .rodata.str1.1
                0x06084804      0x17d obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .rodata.str1.1
                0x06084981       0xe9 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                                0x122 (size before relaxing)
 *fill*         0x06084a6a        0x2 
 .rodata        0x06084a6c       0x10 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .rodata.str1.1
                0x06084a7c      0x315 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                                0x321 (size before relaxing)
 .rodata.str1.1
                0x06084d91       0xaf obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                                 0xdf (size before relaxing)
 .rodata        0x06084e40      0x1f0 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                0x06084e50                recordVideoWin
 .rodata.str1.1
                0x06085030      0x147 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                                0x159 (size before relaxing)
 .rodata.str1.1
                0x06085177        0xa obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .rodata.str1.1
                0x06085181       0x17 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .rodata        0x06085198       0x50 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                0x06085198                ShowLogoWin
 .rodata.str1.1
                0x060851e8       0x45 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                                 0x51 (size before relaxing)
 .rodata.str1.1
                0x0608522d       0x8d obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 *fill*         0x060852ba        0x2 
 .rodata        0x060852bc      0x230 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                0x060852bc                usbDeviceWin
 .rodata.str1.1
                0x060854ec      0x13b obj\Debug\app\task_windows\windows_api.o
                                0x155 (size before relaxing)
 *fill*         0x06085627        0x1 
 .rodata        0x06085628       0x1c obj\Debug\app\task_windows\windows_api.o
 .rodata.str1.1
                0x06085644       0x72 obj\Debug\app\user_config\src\mbedtls_md5.o
                                 0x75 (size before relaxing)
 *fill*         0x060856b6        0x2 
 .rodata        0x060856b8       0x40 obj\Debug\app\user_config\src\mbedtls_md5.o
 .rodata.str1.1
                0x060856f8       0x81 obj\Debug\app\user_config\src\user_config_api.o
 *fill*         0x06085779        0x3 
 .rodata        0x0608577c      0x28c obj\Debug\app\user_config\src\user_config_api.o
 .rodata        0x06085a08      0x100 obj\Debug\app\user_config\src\user_config_tab.o
                0x06085a08                user_cfg_tab
 .rodata        0x06085b08       0xd0 ..\lib\libmcu.a(hx330x_auadc.o)
                0x06085b08                tbl_micvol
                0x06085b48                auadc_samplerate_tab
 .rodata.str1.1
                0x06085bd8        0xe ..\lib\libmcu.a(hx330x_csi.o)
 *fill*         0x06085be6        0x2 
 .rodata        0x06085be8      0x154 ..\lib\libmcu.a(hx330x_csi.o)
                0x06085be8                csi_dvp_map_tab
                0x06085bfc                csi_dvp_map4
                0x06085c3c                csi_dvp_map3
                0x06085c7c                csi_dvp_map2
                0x06085cbc                csi_dvp_map1
                0x06085cfc                csi_dvp_map0
 .rodata        0x06085d3c       0xac ..\lib\libmcu.a(hx330x_dac.o)
                0x06085d3c                gain
                0x06085d50                eq_coeff
                0x06085d88                hx330x_dacInit_table
 .rodata        0x06085de8      0x210 ..\lib\libmcu.a(hx330x_dma.o)
                0x06085de8                winDis_cfg
                0x06085e98                winB_cfg
                0x06085f48                winA_cfg
 .rodata        0x06085ff8       0xd0 ..\lib\libmcu.a(hx330x_dmauart.o)
                0x06085ff8                uart1_IO_MAP_tab
 .rodata        0x060860c8       0xb8 ..\lib\libmcu.a(hx330x_gpio.o)
 .rodata        0x06086180      0x4a4 ..\lib\libmcu.a(hx330x_iic.o)
                0x06086180                iic1_uinit_map_tab
                0x0608619c                iic1_init_map_tab
                0x060861b8                iic1_uinit_map6
                0x060861e0                iic1_init_map6
                0x0608622c                iic1_uinit_map5
                0x06086254                iic1_init_map5
                0x060862a0                iic1_uinit_map4
                0x060862c8                iic1_init_map4
                0x06086314                iic1_uinit_map3
                0x0608633c                iic1_init_map3
                0x06086388                iic1_uinit_map2
                0x060863b0                iic1_init_map2
                0x060863fc                iic1_uinit_map1
                0x06086424                iic1_init_map1
                0x06086470                iic1_uinit_map0
                0x06086498                iic1_init_map0
                0x060864e4                iic0_init_map_tab
                0x060864f4                iic0_init_map3
                0x06086540                iic0_init_map2
                0x0608658c                iic0_init_map1
                0x060865d8                iic0_init_map0
 .rodata        0x06086624       0xf0 ..\lib\libmcu.a(hx330x_int.o)
 .rodata        0x06086714      0x14c ..\lib\libmcu.a(hx330x_isp_tab.o)
                0x06086714                Ratio_of_Evstep
                0x0608681c                GAOS3X3_TAB
                0x06086828                GAOS5X5_TAB
                0x06086844                LOG_TAB
 .rodata        0x06086860       0x14 ..\lib\libmcu.a(hx330x_jpg.o)
 .rodata.str1.1
                0x06086874       0x16 ..\lib\libmcu.a(hx330x_jpg.o)
 *fill*         0x0608688a        0x2 
 .rodata        0x0608688c      0xda0 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                0x060872cc                c_table_chroma
                0x0608730c                belta_table_chroma
                0x0608734c                alpha_table_chroma
                0x0608738c                c_table_luma
                0x060873cc                belta_table_luma
                0x0608740c                alpha_table_luma
                0x0608744c                bic_coef_tabR
                0x060874ec                bic_coef_tabL
                0x0608758c                bic_coef_tab
 .rodata        0x0608762c      0x11c ..\lib\libmcu.a(hx330x_lcd.o)
 .rodata        0x06087748       0xa0 ..\lib\libmcu.a(hx330x_lcdui.o)
 .rodata        0x060877e8      0x168 ..\lib\libmcu.a(hx330x_misc.o)
 .rodata        0x06087950      0x248 ..\lib\libmcu.a(hx330x_spi1.o)
                0x06087950                SPI1_CS_MAP_tab
                0x06087978                spi1PinCfg_tab
                0x06087998                SPI1_2LINE_Pos3_tab
                0x060879d8                SPI1_3LINE_Pos3_tab
                0x06087a18                SPI1_2LINE_Pos2_tab
                0x06087a58                SPI1_3LINE_Pos2_tab
                0x06087a98                SPI1_2LINE_Pos1_tab
                0x06087ad8                SPI1_3LINE_Pos1_tab
                0x06087b18                SPI1_2LINE_Pos0_tab
                0x06087b58                SPI1_3LINE_Pos0_tab
 .rodata        0x06087b98       0x18 ..\lib\libmcu.a(hx330x_sys.o)
 .rodata        0x06087bb0      0x100 ..\lib\libmcu.a(hx330x_timer.o)
                0x06087bb0                timer3_PWM_IO_MAP_tab
                0x06087be0                timer2_PWM_IO_MAP_tab
                0x06087c18                timer1_PWM_IO_MAP_tab
                0x06087c58                timer0_PWM_IO_MAP_tab
 .rodata        0x06087cb0       0xd8 ..\lib\libmcu.a(hx330x_uart.o)
                0x06087cb0                uart0_IO_MAP_tab
 .rodata.cst4   0x06087d88        0x8 ..\lib\libmcu.a(hx330x_usb.o)
 .rodata        0x06087d90       0x68 ..\lib\libmcu.a(hx330x_emi.o)
                0x06087d90                hx330x_emiPinConfigSlave_table
                0x06087db8                hx330x_emiPinConfigMaster_table
 .rodata        0x06087df8       0x44 ..\lib\libisp.a(hal_isp.o)
 .rodata.str1.1
                0x06087e3c       0x12 ..\lib\libisp.a(hal_isp.o)
 .rodata.str1.1
                0x06087e4e      0x12f ..\lib\libjpg.a(hal_jpg.o)
 .rodata.str1.1
                0x06087f7d       0x36 ..\lib\liblcd.a(hal_lcd.o)
 .rodata.str1.1
                0x06087fb3      0x174 ..\lib\liblcd.a(hal_lcdMem.o)
 .rodata.str1.1
                0x06088127       0x11 ..\lib\liblcd.a(hal_lcdrotate.o)
 .rodata.str1.1
                0x06088138       0x12 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 *fill*         0x0608814a        0x2 
 .rodata        0x0608814c       0x20 ..\lib\libmultimedia.a(api_multimedia.o)
 .rodata.str1.1
                0x0608816c       0x81 ..\lib\libmultimedia.a(avi_dec.o)
 *fill*         0x060881ed        0x3 
 .rodata        0x060881f0       0x30 ..\lib\libmultimedia.a(avi_dec.o)
                0x060881f0                avi_dec_func
 .rodata.str1.1
                0x06088220       0x78 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .rodata        0x06088298     0xe830 ..\lib\libmultimedia.a(avi_odml_enc.o)
                0x06088298                avi_odml_enc_func
                0x060882c8                avi_odml_header
 .rodata.str1.1
                0x06096ac8       0x5f ..\lib\libmultimedia.a(avi_std_enc.o)
 *fill*         0x06096b27        0x1 
 .rodata        0x06096b28      0x218 ..\lib\libmultimedia.a(avi_std_enc.o)
                0x06096b28                avi_std_enc_func
 .rodata.str1.1
                0x06096d40       0x3c ..\lib\libmultimedia.a(wav_dec.o)
 .rodata        0x06096d7c       0x30 ..\lib\libmultimedia.a(wav_dec.o)
                0x06096d7c                wav_dec_func
 .rodata.str1.1
                0x06096dac       0x24 ..\lib\libmultimedia.a(wav_enc.o)
 .rodata        0x06096dd0       0x30 ..\lib\libmultimedia.a(wav_enc.o)
                0x06096dd0                wav_enc_func
 .rodata        0x06096e00       0x2c ..\lib\libmultimedia.a(wav_pcm.o)
                0x06096e00                wav_pcm_head
 .rodata        0x06096e2c      0x100 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                0x06096e2c                __clz_tab

.lcd_resource   0x00000000     0x29bc load address 0x00097000
 *(.lcd_res.struct)
 .lcd_res.struct
                0x00000000       0xac obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
                0x00000000                __lcd_desc
 *(.lcd_res*)
 .lcd_res       0x000000ac      0x110 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .lcd_res       0x000001bc     0x2800 ..\lib\liblcd.a(lcd_tab.o)
                0x000004bc                lcd_contra_tab
                0x000011bc                lcd_gamma

.sensor_resource
                0x00000000     0x3640 load address 0x000999bc
 *(.sensor_res.header)
 .sensor_res.header
                0x00000000       0x40 obj\Debug\dev\sensor\src\sensor_tab.o
                0x00000000                RES_SensorHeader
                0x00000040                _res_sensor_header_item_start = .
 *(.sensor_res.header.items)
                0x00000040                _res_sensor_header_item_end = .
 *(.sensor_res.isp_tab)
 .sensor_res.isp_tab
                0x00000040     0x3600 obj\Debug\dev\sensor\src\sensor_tab.o
                0x00000040                sensor_rgb_gamma
                0x00001240                sensor_ygamma_tab
 *(.sensor_res.struct)
 *(.sensor_res.init_tab)

.nes_resource   0x00000000        0x0 load address 0x0009d000
 *(.nes_games_tab)

.eh_frame       0x00003640    0x10704 load address 0x0009cffc
 *(.eh_frame)
 .eh_frame      0x00003640       0x50 obj\Debug\dev\battery\src\battery_api.o
 .eh_frame      0x00003690       0x80 obj\Debug\dev\dev_api.o
                                 0x94 (size before relaxing)
 .eh_frame      0x00003710       0xa8 obj\Debug\dev\fs\src\diskio.o
                                 0xbc (size before relaxing)
 .eh_frame      0x000037b8      0x914 obj\Debug\dev\fs\src\ff.o
                                0x928 (size before relaxing)
 .eh_frame      0x000040cc       0x54 obj\Debug\dev\fs\src\ffunicode.o
                                 0x68 (size before relaxing)
 .eh_frame      0x00004120      0x2ac obj\Debug\dev\fs\src\fs_api.o
                                0x2c0 (size before relaxing)
 .eh_frame      0x000043cc       0x94 obj\Debug\dev\gsensor\src\gsensor_api.o
                                 0xa8 (size before relaxing)
 .eh_frame      0x00004460       0x80 obj\Debug\dev\gsensor\src\gsensor_da380.o
                                 0x94 (size before relaxing)
 .eh_frame      0x000044e0       0x80 obj\Debug\dev\gsensor\src\gsensor_gma301.o
                                 0x94 (size before relaxing)
 .eh_frame      0x00004560       0x7c obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
                                 0x90 (size before relaxing)
 .eh_frame      0x000045dc       0x3c obj\Debug\dev\ir\src\ir_api.o
                                 0x50 (size before relaxing)
 .eh_frame      0x00004618       0x8c obj\Debug\dev\key\src\key_api.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x000046a4       0x8c obj\Debug\dev\lcd\src\lcd_api.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x00004730       0x38 obj\Debug\dev\led\src\led_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x00004768       0x38 obj\Debug\dev\led_pwm\src\led_pwm_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x000047a0       0xc8 obj\Debug\dev\nvfs\src\nvfs_api.o
                                 0xdc (size before relaxing)
 .eh_frame      0x00004868      0x430 obj\Debug\dev\nvfs\src\nvfs_jpg.o
                                0x444 (size before relaxing)
 .eh_frame      0x00004c98      0x3f8 obj\Debug\dev\sd\src\sd_api.o
                                0x40c (size before relaxing)
 .eh_frame      0x00005090       0xe0 obj\Debug\dev\sensor\src\sensor_api.o
                                 0xf4 (size before relaxing)
 .eh_frame      0x00005170       0x64 obj\Debug\dev\touchpanel\src\touchpanel_api.o
                                 0x78 (size before relaxing)
 .eh_frame      0x000051d4       0x58 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
                                 0x6c (size before relaxing)
 .eh_frame      0x0000522c      0x138 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
                                0x14c (size before relaxing)
 .eh_frame      0x00005364       0x78 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
                                 0x8c (size before relaxing)
 .eh_frame      0x000053dc      0x110 obj\Debug\dev\usb\dusb\src\dusb_api.o
                                0x124 (size before relaxing)
 .eh_frame      0x000054ec      0x12c obj\Debug\dev\usb\dusb\src\dusb_enum.o
                                0x140 (size before relaxing)
 .eh_frame      0x00005618      0x27c obj\Debug\dev\usb\dusb\src\dusb_msc.o
                                0x290 (size before relaxing)
 .eh_frame      0x00005894      0x12c obj\Debug\dev\usb\dusb\src\dusb_uac.o
                                0x140 (size before relaxing)
 .eh_frame      0x000059c0      0x1ec obj\Debug\dev\usb\dusb\src\dusb_uvc.o
                                0x200 (size before relaxing)
 .eh_frame      0x00005bac      0x1c0 obj\Debug\dev\usb\husb\src\husb_api.o
                                0x1d4 (size before relaxing)
 .eh_frame      0x00005d6c      0x668 obj\Debug\dev\usb\husb\src\husb_enum.o
                                0x67c (size before relaxing)
 .eh_frame      0x000063d4       0x60 obj\Debug\dev\usb\husb\src\husb_hub.o
                                 0x74 (size before relaxing)
 .eh_frame      0x00006434      0x264 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
                                0x278 (size before relaxing)
 .eh_frame      0x00006698      0x248 obj\Debug\dev\usb\husb\src\husb_usensor.o
                                0x25c (size before relaxing)
 .eh_frame      0x000068e0      0x284 obj\Debug\dev\usb\husb\src\husb_uvc.o
                                0x298 (size before relaxing)
 .eh_frame      0x00006b64       0x74 obj\Debug\hal\src\hal_adc.o
                                 0x88 (size before relaxing)
 .eh_frame      0x00006bd8      0x1ec obj\Debug\hal\src\hal_auadc.o
                                0x200 (size before relaxing)
 .eh_frame      0x00006dc4       0xa4 obj\Debug\hal\src\hal_csi.o
                                 0xb8 (size before relaxing)
 .eh_frame      0x00006e68       0xc4 obj\Debug\hal\src\hal_dac.o
                                 0xd8 (size before relaxing)
 .eh_frame      0x00006f2c       0xd8 obj\Debug\hal\src\hal_dmauart.o
                                 0xec (size before relaxing)
 .eh_frame      0x00007004       0x6c obj\Debug\hal\src\hal_gpio.o
                                 0x80 (size before relaxing)
 .eh_frame      0x00007070      0x2c8 obj\Debug\hal\src\hal_iic.o
                                0x2dc (size before relaxing)
 .eh_frame      0x00007338       0x1c obj\Debug\hal\src\hal_int.o
                                 0x30 (size before relaxing)
 .eh_frame      0x00007354      0x474 obj\Debug\hal\src\hal_lcdshow.o
                                0x488 (size before relaxing)
 .eh_frame      0x000077c8       0x68 obj\Debug\hal\src\hal_md.o
                                 0x7c (size before relaxing)
 .eh_frame      0x00007830      0x40c obj\Debug\hal\src\hal_mjpAEncode.o
                                0x420 (size before relaxing)
 .eh_frame      0x00007c3c      0x208 obj\Debug\hal\src\hal_mjpBEncode.o
                                0x21c (size before relaxing)
 .eh_frame      0x00007e44      0x274 obj\Debug\hal\src\hal_mjpDecode.o
                                0x288 (size before relaxing)
 .eh_frame      0x000080b8      0x248 obj\Debug\hal\src\hal_rtc.o
                                0x25c (size before relaxing)
 .eh_frame      0x00008300      0x27c obj\Debug\hal\src\hal_spi.o
                                0x290 (size before relaxing)
 .eh_frame      0x0000857c      0x13c obj\Debug\hal\src\hal_spi1.o
                                0x150 (size before relaxing)
 .eh_frame      0x000086b8       0xe8 obj\Debug\hal\src\hal_stream.o
                                 0xfc (size before relaxing)
 .eh_frame      0x000087a0      0x178 obj\Debug\hal\src\hal_sys.o
                                0x18c (size before relaxing)
 .eh_frame      0x00008918       0x78 obj\Debug\hal\src\hal_timer.o
                                 0x8c (size before relaxing)
 .eh_frame      0x00008990      0x1b0 obj\Debug\hal\src\hal_uart.o
                                0x1c4 (size before relaxing)
 .eh_frame      0x00008b40      0x1fc obj\Debug\hal\src\hal_watermark.o
                                0x210 (size before relaxing)
 .eh_frame      0x00008d3c       0xd4 obj\Debug\mcu\xos\xmsgq.o
                                 0xe8 (size before relaxing)
 .eh_frame      0x00008e10       0x88 obj\Debug\mcu\xos\xos.o
                                 0x9c (size before relaxing)
 .eh_frame      0x00008e98       0x70 obj\Debug\mcu\xos\xwork.o
                                 0x84 (size before relaxing)
 .eh_frame      0x00008f08      0x19c obj\Debug\multimedia\audio\audio_playback.o
                                0x1b0 (size before relaxing)
 .eh_frame      0x000090a4      0x138 obj\Debug\multimedia\audio\audio_record.o
                                0x14c (size before relaxing)
 .eh_frame      0x000091dc       0xb4 obj\Debug\multimedia\image\image_decode.o
                                 0xc8 (size before relaxing)
 .eh_frame      0x00009290       0xa4 obj\Debug\multimedia\image\image_encode.o
                                 0xb8 (size before relaxing)
 .eh_frame      0x00009334       0x20 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
                                 0x34 (size before relaxing)
 .eh_frame      0x00009354      0x2b8 obj\Debug\multimedia\video\video_playback.o
                                0x2cc (size before relaxing)
 .eh_frame      0x0000960c      0x230 obj\Debug\multimedia\video\video_record.o
                                0x244 (size before relaxing)
 .eh_frame      0x0000983c      0x3c4 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
                                0x3d8 (size before relaxing)
 .eh_frame      0x00009c00      0x134 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
                                0x148 (size before relaxing)
 .eh_frame      0x00009d34       0x60 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
                                 0x74 (size before relaxing)
 .eh_frame      0x00009d94       0xc4 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
                                 0xd8 (size before relaxing)
 .eh_frame      0x00009e58      0x144 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
                                0x158 (size before relaxing)
 .eh_frame      0x00009f9c       0x58 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
                                 0x6c (size before relaxing)
 .eh_frame      0x00009ff4       0x38 obj\Debug\sys_manage\res_manage\res_manage_api.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000a02c       0x9c obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000a0c8       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a108       0x5c obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
                                 0x70 (size before relaxing)
 .eh_frame      0x0000a164       0x44 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
                                 0x58 (size before relaxing)
 .eh_frame      0x0000a1a8      0x1c8 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
                                0x1dc (size before relaxing)
 .eh_frame      0x0000a370       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a3b0       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000a410      0x358 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
                                0x36c (size before relaxing)
 .eh_frame      0x0000a768       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a7b4       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a800       0x4c obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
                                 0x60 (size before relaxing)
 .eh_frame      0x0000a84c       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000a88c      0x588 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
                                0x59c (size before relaxing)
 .eh_frame      0x0000ae14       0xd8 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000aeec       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000af4c       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000afac       0x70 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
                                 0x84 (size before relaxing)
 .eh_frame      0x0000b01c       0x60 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
                                 0x74 (size before relaxing)
 .eh_frame      0x0000b07c       0x40 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
                                 0x54 (size before relaxing)
 .eh_frame      0x0000b0bc       0x80 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
                                 0x94 (size before relaxing)
 .eh_frame      0x0000b13c       0x44 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
                                 0x58 (size before relaxing)
 .eh_frame      0x0000b180       0xec obj\Debug\app\app_common\src\app_init.o
                                0x100 (size before relaxing)
 .eh_frame      0x0000b26c      0x158 obj\Debug\app\app_common\src\app_lcdshow.o
                                0x16c (size before relaxing)
 .eh_frame      0x0000b3c4       0x1c obj\Debug\app\app_common\src\main.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000b3e0       0x8c obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
                                 0xa0 (size before relaxing)
 .eh_frame      0x0000b46c       0x54 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
                                 0x68 (size before relaxing)
 .eh_frame      0x0000b4c0      0x17c obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
                                0x190 (size before relaxing)
 .eh_frame      0x0000b63c      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000b770      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000b8a4      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000b9dc      0x134 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000bb10      0x2dc obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
                                0x2f0 (size before relaxing)
 .eh_frame      0x0000bdec      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000bf24      0x150 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
                                0x164 (size before relaxing)
 .eh_frame      0x0000c074      0x13c obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
                                0x150 (size before relaxing)
 .eh_frame      0x0000c1b0      0x138 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
                                0x14c (size before relaxing)
 .eh_frame      0x0000c2e8       0x70 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
                                 0x84 (size before relaxing)
 .eh_frame      0x0000c358       0xc8 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
                                 0xdc (size before relaxing)
 .eh_frame      0x0000c420      0x194 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
                                0x1a8 (size before relaxing)
 .eh_frame      0x0000c5b4      0x170 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
                                0x184 (size before relaxing)
 .eh_frame      0x0000c724      0x170 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
                                0x184 (size before relaxing)
 .eh_frame      0x0000c894       0xe0 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
                                 0xf4 (size before relaxing)
 .eh_frame      0x0000c974       0x98 obj\Debug\app\task_windows\msg_api.o
                                 0xac (size before relaxing)
 .eh_frame      0x0000ca0c       0xcc obj\Debug\app\task_windows\task_api.o
                                 0xe0 (size before relaxing)
 .eh_frame      0x0000cad8       0xf4 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
                                0x108 (size before relaxing)
 .eh_frame      0x0000cbcc      0x34c obj\Debug\app\task_windows\task_common\src\task_common.o
                                0x360 (size before relaxing)
 .eh_frame      0x0000cf18       0x1c obj\Debug\app\task_windows\task_common\src\task_common_msg.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000cf34      0x170 obj\Debug\app\task_windows\task_main\src\taskMain.o
                                0x184 (size before relaxing)
 .eh_frame      0x0000d0a4       0xf8 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
                                0x10c (size before relaxing)
 .eh_frame      0x0000d19c       0x74 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
                                 0x88 (size before relaxing)
 .eh_frame      0x0000d210      0x200 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
                                0x214 (size before relaxing)
 .eh_frame      0x0000d410      0x180 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
                                0x194 (size before relaxing)
 .eh_frame      0x0000d590      0x36c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
                                0x380 (size before relaxing)
 .eh_frame      0x0000d8fc      0x194 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
                                0x1a8 (size before relaxing)
 .eh_frame      0x0000da90      0x20c obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
                                0x220 (size before relaxing)
 .eh_frame      0x0000dc9c       0x1c obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
                                 0x30 (size before relaxing)
 .eh_frame      0x0000dcb8       0x7c obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
                                 0x90 (size before relaxing)
 .eh_frame      0x0000dd34       0xd8 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000de0c       0x9c obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000dea8      0x2f8 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
                                0x30c (size before relaxing)
 .eh_frame      0x0000e1a0      0x134 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
                                0x148 (size before relaxing)
 .eh_frame      0x0000e2d4      0x41c obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
                                0x430 (size before relaxing)
 .eh_frame      0x0000e6f0       0x84 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
                                 0x98 (size before relaxing)
 .eh_frame      0x0000e774       0x9c obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000e810       0x38 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
                                 0x4c (size before relaxing)
 .eh_frame      0x0000e848       0x54 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
                                 0x68 (size before relaxing)
 .eh_frame      0x0000e89c       0x58 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
                                 0x6c (size before relaxing)
 .eh_frame      0x0000e8f4       0xd8 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
                                 0xec (size before relaxing)
 .eh_frame      0x0000e9cc       0x78 obj\Debug\app\task_windows\windows_api.o
                                 0x8c (size before relaxing)
 .eh_frame      0x0000ea44      0x154 obj\Debug\app\user_config\src\mbedtls_md5.o
                                0x168 (size before relaxing)
 .eh_frame      0x0000eb98      0x120 obj\Debug\app\user_config\src\user_config_api.o
                                0x134 (size before relaxing)
 .eh_frame      0x0000ecb8      0x1d0 ..\lib\libboot.a(boot.o)
                                0x1e4 (size before relaxing)
 .eh_frame      0x0000ee88       0x78 ..\lib\libboot.a(boot_lib.o)
                                 0x8c (size before relaxing)
 .eh_frame      0x0000ef00       0x68 ..\lib\libmcu.a(hx330x_adc.o)
                                 0x7c (size before relaxing)
 .eh_frame      0x0000ef68      0x110 ..\lib\libmcu.a(hx330x_auadc.o)
                                0x124 (size before relaxing)
 .eh_frame      0x0000f078      0x4e4 ..\lib\libmcu.a(hx330x_csi.o)
                                0x4f8 (size before relaxing)
 .eh_frame      0x0000f55c      0x1d4 ..\lib\libmcu.a(hx330x_dac.o)
                                0x1e8 (size before relaxing)
 .eh_frame      0x0000f730       0x88 ..\lib\libmcu.a(hx330x_dma.o)
                                 0x9c (size before relaxing)
 .eh_frame      0x0000f7b8      0x170 ..\lib\libmcu.a(hx330x_dmauart.o)
                                0x184 (size before relaxing)
 .eh_frame      0x0000f928      0x2c8 ..\lib\libmcu.a(hx330x_gpio.o)
                                0x2dc (size before relaxing)
 .eh_frame      0x0000fbf0      0x31c ..\lib\libmcu.a(hx330x_iic.o)
                                0x330 (size before relaxing)
 .eh_frame      0x0000ff0c       0x9c ..\lib\libmcu.a(hx330x_int.o)
                                 0xb0 (size before relaxing)
 .eh_frame      0x0000ffa8      0x264 ..\lib\libmcu.a(hx330x_isp.o)
                                0x278 (size before relaxing)
 .eh_frame      0x0001020c      0x778 ..\lib\libmcu.a(hx330x_jpg.o)
                                0x78c (size before relaxing)
 .eh_frame      0x00010984       0x38 ..\lib\libmcu.a(hx330x_jpg_tab.o)
                                 0x4c (size before relaxing)
 .eh_frame      0x000109bc      0x3d4 ..\lib\libmcu.a(hx330x_lcd.o)
                                0x3e8 (size before relaxing)
 .eh_frame      0x00010d90       0xc4 ..\lib\libmcu.a(hx330x_lcdrotate.o)
                                 0xd8 (size before relaxing)
 .eh_frame      0x00010e54      0x3c8 ..\lib\libmcu.a(hx330x_lcdui.o)
                                0x3dc (size before relaxing)
 .eh_frame      0x0001121c       0xe8 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
                                 0xfc (size before relaxing)
 .eh_frame      0x00011304       0x80 ..\lib\libmcu.a(hx330x_lcdwin.o)
                                 0x94 (size before relaxing)
 .eh_frame      0x00011384       0x80 ..\lib\libmcu.a(hx330x_md.o)
                                 0x94 (size before relaxing)
 .eh_frame      0x00011404       0x5c ..\lib\libmcu.a(hx330x_mipi.o)
                                 0x70 (size before relaxing)
 .eh_frame      0x00011460      0x29c ..\lib\libmcu.a(hx330x_misc.o)
                                0x2b0 (size before relaxing)
 .eh_frame      0x000116fc      0x374 ..\lib\libmcu.a(hx330x_rtc.o)
                                0x388 (size before relaxing)
 .eh_frame      0x00011a70      0x2b4 ..\lib\libmcu.a(hx330x_sd.o)
                                0x2c8 (size before relaxing)
 .eh_frame      0x00011d24      0x108 ..\lib\libmcu.a(hx330x_spi0.o)
                                0x11c (size before relaxing)
 .eh_frame      0x00011e2c      0x184 ..\lib\libmcu.a(hx330x_spi1.o)
                                0x198 (size before relaxing)
 .eh_frame      0x00011fb0      0x3d4 ..\lib\libmcu.a(hx330x_sys.o)
                                0x3e8 (size before relaxing)
 .eh_frame      0x00012384      0x188 ..\lib\libmcu.a(hx330x_timer.o)
                                0x19c (size before relaxing)
 .eh_frame      0x0001250c      0x110 ..\lib\libmcu.a(hx330x_tminf.o)
                                0x124 (size before relaxing)
 .eh_frame      0x0001261c       0x78 ..\lib\libmcu.a(hx330x_uart.o)
                                 0x8c (size before relaxing)
 .eh_frame      0x00012694      0x444 ..\lib\libmcu.a(hx330x_usb.o)
                                0x458 (size before relaxing)
 .eh_frame      0x00012ad8       0x60 ..\lib\libmcu.a(hx330x_wdt.o)
                                 0x74 (size before relaxing)
 .eh_frame      0x00012b38       0xa4 ..\lib\libmcu.a(hx330x_emi.o)
                                 0xb8 (size before relaxing)
 .eh_frame      0x00012bdc      0x220 ..\lib\libisp.a(hal_isp.o)
                                0x234 (size before relaxing)
 .eh_frame      0x00012dfc      0x118 ..\lib\libjpg.a(hal_jpg.o)
                                0x12c (size before relaxing)
 .eh_frame      0x00012f14      0x1c0 ..\lib\liblcd.a(hal_lcd.o)
                                0x1d4 (size before relaxing)
 .eh_frame      0x000130d4      0x148 ..\lib\liblcd.a(hal_lcdMem.o)
                                0x15c (size before relaxing)
 .eh_frame      0x0001321c       0x84 ..\lib\liblcd.a(hal_lcdrotate.o)
                                 0x98 (size before relaxing)
 .eh_frame      0x000132a0      0x238 ..\lib\liblcd.a(hal_lcdUi.o)
                                0x24c (size before relaxing)
 .eh_frame      0x000134d8       0x60 ..\lib\liblcd.a(hal_lcdUiLzo.o)
                                 0x74 (size before relaxing)
 .eh_frame      0x00013538       0x58 ..\lib\liblcd.a(lcd_tab.o)
                                 0x6c (size before relaxing)
 .eh_frame      0x00013590      0x204 ..\lib\libmultimedia.a(api_multimedia.o)
                                0x218 (size before relaxing)
 .eh_frame      0x00013794      0x14c ..\lib\libmultimedia.a(avi_dec.o)
                                0x160 (size before relaxing)
 .eh_frame      0x000138e0      0x130 ..\lib\libmultimedia.a(avi_odml_enc.o)
                                0x144 (size before relaxing)
 .eh_frame      0x00013a10      0x144 ..\lib\libmultimedia.a(avi_std_enc.o)
                                0x158 (size before relaxing)
 .eh_frame      0x00013b54       0xb8 ..\lib\libmultimedia.a(wav_dec.o)
                                 0xcc (size before relaxing)
 .eh_frame      0x00013c0c       0xa8 ..\lib\libmultimedia.a(wav_enc.o)
                                 0xbc (size before relaxing)
 .eh_frame      0x00013cb4       0x38 ..\lib\libmultimedia.a(wav_pcm.o)
                                 0x4c (size before relaxing)
 .eh_frame      0x00013cec       0x2c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                                 0x40 (size before relaxing)
 .eh_frame      0x00013d18       0x2c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                                 0x40 (size before relaxing)

.rela.dyn       0x00013d44        0x0 load address 0x000ad700
 .rela.text     0x00013d44        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.data     0x00013d44        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector.kepttext
                0x00013d44        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.rodata   0x00013d44        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.sdram_text
                0x00013d44        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.sensor_res.header
                0x00013d44        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.text.startup
                0x00013d44        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector.text
                0x00013d44        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.bootsec  0x00013d44        0x0 obj\Debug\dev\battery\src\battery_api.o
 .rela.vector   0x00013d44        0x0 obj\Debug\dev\battery\src\battery_api.o

.mp3_text       0x00008000        0x0 load address 0x0009d000
                0x00008000                __mp3_text_start = .
 *(.mp3_text)
                0x00008000                . = ALIGN (0x4)

.mp3_code       0x00200000        0x0 load address 0x0009d000
                0x00200000                __mp3_code_start = .
 *(.mp3_code)
                0x00200000                . = ALIGN (0x4)

.mp3_data
 *(.mp3_data)

.nes_code       0x00200000        0x0 load address 0x0009d000
                0x00200000                __nes_code_start = .
 *(.nes_code)
                0x00200000                . = ALIGN (0x4)

.nes_data       0x00200000        0x0
                0x00200000                __nes_data_start = .
 *(.nes_data)
                0x00200000                __nes_data_end = .

.nes_com_text   0x00008000        0x0 load address 0x0009d000
                0x00008000                __nes_text_start = .
 *(.nes_com_text)
                0x00008000                . = ALIGN (0x4)
                0x00000015                _boot_code_len = ((SIZEOF (.boot_code) + 0x1ff) / 0x200)
                0x00000001                _boot_code_sec = (LOADADDR (.boot_code) / 0x200)
                0x02000000                _text_start = _onsdram_start
                0x00000016                _text_sec = (LOADADDR (.on_sdram) / 0x200)
                0x0000004e                _text_len = ((SIZEOF (.on_sdram) + 0x1ff) / 0x200)
                0x00000001                ASSERT (((_sdram_remian_addr - ORIGIN (sdram)) < __sdram_size), No memroy for sdram)
                0x001e3880                __sdram_remain_size = ((ORIGIN (sdram) + __sdram_size) - _sdram_remian_addr)
                0x00001400                __stack_size = 0x1400
                0x00000001                ASSERT ((((ORIGIN (ram_user) + 0x7000) - __sram_end) >= __stack_size), No memroy for stack)
                0x02200000                __bss_end = (_sdram_remian_addr + __sdram_remain_size)
                0x00000000                __mp3_text_len = SIZEOF (.mp3_text)
                0x0009d000                __mp3_text_addr = LOADADDR (.mp3_text)
                0x00000000                __mp3_code_len = SIZEOF (.mp3_code)
                0x0009d000                __mp3_code_addr = LOADADDR (.mp3_code)
                0x00000000                __nes_text_len = SIZEOF (.nes_com_text)
                0x0009d000                __nes_text_addr = LOADADDR (.nes_com_text)
                0x00000000                __nes_code_len = SIZEOF (.nes_code)
                0x0009d000                __nes_code_addr = LOADADDR (.nes_code)
                0x00097000                _lcd_res_lma = LOADADDR (.lcd_resource)
                0x000029bc                _lcd_res_size = SIZEOF (.lcd_resource)
                0x000999bc                _sensor_resource_start_addr = LOADADDR (.sensor_resource)
                0x00000000                _res_sensor_header_len = (_res_sensor_header_item_end - _res_sensor_header_item_start)
                0x0009d000                _nes_res_lma = LOADADDR (.nes_resource)
                0x00006ffc                PROVIDE (__stack, ((ORIGIN (ram_user) + 0x7000) - 0x4))
LOAD ..\lib\libboot.a
LOAD ..\lib\libmcu.a
LOAD ..\lib\libisp.a
LOAD ..\lib\libjpg.a
LOAD ..\lib\liblcd.a
LOAD ..\lib\libmultimedia.a
LOAD E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a
LOAD E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a
OUTPUT(bin\Debug\hx330x_sdk.exe elf32-or1k)

.comment        0x00000000       0x11
 .comment       0x00000000       0x11 obj\Debug\dev\battery\src\battery_api.o
                                 0x12 (size before relaxing)
 .comment       0x00000011       0x12 obj\Debug\dev\dev_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\diskio.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\ff.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\ffunicode.o
 .comment       0x00000011       0x12 obj\Debug\dev\fs\src\fs_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_da380.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_gma301.o
 .comment       0x00000011       0x12 obj\Debug\dev\gsensor\src\gsensor_sc7a30e.o
 .comment       0x00000011       0x12 obj\Debug\dev\ir\src\ir_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\key\src\key_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8352b.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8352c.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_hx8357b.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9225G.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9328.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9335.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_ili9486_T35-H43-86.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_jd9851.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_lgdp4532.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_NT35510HSD.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_r61509v.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_SPFD5420.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_st7789.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_mcu_st7789v.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ili8961.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ili9342c.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ILI9806e_4522.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_JLT28060B.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_ota5182.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_otm8019a.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_rm68172.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7282.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1601.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701FW1604.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701s_LX50FWH40149.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_rgb_st7701sLX45FWI4006.o
 .comment       0x00000011       0x12 obj\Debug\dev\lcd\src\lcd_spi_ili9341.o
 .comment       0x00000011       0x12 obj\Debug\dev\led\src\led_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\led_pwm\src\led_pwm_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\nvfs\src\nvfs_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\nvfs\src\nvfs_jpg.o
 .comment       0x00000011       0x12 obj\Debug\dev\sd\src\sd_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_BF3016.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_FPX1002.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1004.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1034.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1054.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_GC1064.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H42.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H62.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H65.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_H7640.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_NT99141.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9710.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_OV9732.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1045.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1243.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SC1345.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP1409.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_720P_SP140A.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF2013.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3703.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_BF3a03.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0307.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0308.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0309.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_GC0328.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_HM1055.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_IT03A1.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_NT99142.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7670.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7725.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_OV7736.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV100B.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV120B.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_dvp_VGA_SIV121DS.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1054.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_GC1084.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_mipi_720P_OV9714.o
 .comment       0x00000011       0x12 obj\Debug\dev\sensor\src\sensor_tab.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_icnt81.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_iic.o
 .comment       0x00000011       0x12 obj\Debug\dev\touchpanel\src\touchpanel_ns2009.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_enum.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_msc.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_tool_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_uac.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\dusb\src\dusb_uvc.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_api.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_enum.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_hub.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_tpbulk.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_usensor.o
 .comment       0x00000011       0x12 obj\Debug\dev\usb\husb\src\husb_uvc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_adc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_auadc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_csi.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_dac.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_dmauart.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_eeprom.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_gpio.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_iic.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_int.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_lcdshow.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_md.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpAEncode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpBEncode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_mjpDecode.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_rtc.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_spi.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_spi1.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_stream.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_sys.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_timer.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_uart.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_watermark.o
 .comment       0x00000011       0x12 obj\Debug\hal\src\hal_wdt.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xmbox.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xmsgq.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xos.o
 .comment       0x00000011       0x12 obj\Debug\mcu\xos\xwork.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\audio\audio_playback.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\audio\audio_record.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\image\image_decode.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\image\image_encode.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\Library\JPG\src\jpg_enc.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\video\video_playback.o
 .comment       0x00000011       0x12 obj\Debug\multimedia\video\video_record.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\filelist_manage\src\file_list_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\filelist_manage\src\file_list_manage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num1_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num2_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num3_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_ascii\src\ascii_num4_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_font\src\res_font_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_icon\src\res_icon_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_image\src\res_image_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_manage_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_music\src\res_music_api.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\res_manage\res_music\src\res_music_tab.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinButton.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinCycle.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinDialog.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinDraw.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinFrame.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinImageIcon.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenu.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuEx.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinItemMenuOption.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinLine.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinMemManage.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinProgressBar.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinRect.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinStringEx.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinStringIcon.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinTips.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinWidget.o
 .comment       0x00000011       0x12 obj\Debug\sys_manage\ui_manage\src\uiWinWidgetManage.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\app_init.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\app_lcdshow.o
 .comment       0x00000011       0x12 obj\Debug\app\app_common\src\main.o
 .comment       0x00000011       0x12 obj\Debug\app\resource\user_res.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuPlayWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\mMenuRecordWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDateTimeMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDefaultMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDelAllMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuDelCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuFormatMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuItemMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuLockCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuOptionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockAllMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuUnlockCurMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sMenuVersionMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowAsternMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowNoFileMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowSelfTestMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTips1Msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTipsMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\menu_windows\src\sWindowTpIconMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\msg_api.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_api.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\sys_common_msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\task_common.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_common\src\task_common_msg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_main\src\taskMain.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_main\src\taskMainMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudio.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_audio\src\taskPlayAudioMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoMainMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoSlideMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_play_video\src\taskPlayVideoThumbnallMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_poweroff\src\taskPowerOff.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudio.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_audio\src\taskRecordAudioMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhoto.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_photo\src\taskRecordPhotoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_record_video\src\taskRecordVideoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdate.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_sd_update\src\taskSdUpdateWin.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogo.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_show_logo\src\taskShowLogoMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDevice.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\task_usb_device\src\taskUsbDeviceMsg.o
 .comment       0x00000011       0x12 obj\Debug\app\task_windows\windows_api.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\mbedtls_md5.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\user_config_api.o
 .comment       0x00000011       0x12 obj\Debug\app\user_config\src\user_config_tab.o
 .comment       0x00000011       0x12 ..\lib\libboot.a(boot.o)
 .comment       0x00000011       0x12 ..\lib\libboot.a(boot_lib.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_adc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_auadc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_csi.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dac.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dma.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_dmauart.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_gpio.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_iic.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_int.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_isp.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_isp_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_jpg.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_jpg_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcd.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdrotate.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdui.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdUiLzo.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_lcdwin.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_md.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_mipi.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_misc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_rtc.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_sd.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_spi0.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_spi1.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_sys.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_timer.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_tminf.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_uart.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_usb.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_wdt.o)
 .comment       0x00000011       0x12 ..\lib\libmcu.a(hx330x_emi.o)
 .comment       0x00000011       0x12 ..\lib\libisp.a(hal_isp.o)
 .comment       0x00000011       0x12 ..\lib\libjpg.a(hal_jpg.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcd.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdMem.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdrotate.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdUi.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(hal_lcdUiLzo.o)
 .comment       0x00000011       0x12 ..\lib\liblcd.a(lcd_tab.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(api_multimedia.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_odml_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(avi_std_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_dec.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_enc.o)
 .comment       0x00000011       0x12 ..\lib\libmultimedia.a(wav_pcm.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .comment       0x00000011       0x12 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_info     0x00000000     0x13ab
 .debug_info    0x00000000      0x113 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_info    0x00000113      0x131 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_info    0x00000244      0x117 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_info    0x0000035b      0x10e E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_info    0x00000469      0x71e E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_info    0x00000b87      0x76b E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_info    0x000012f2       0xb9 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_abbrev   0x00000000      0x5c4
 .debug_abbrev  0x00000000       0x7f E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_abbrev  0x0000007f       0xab E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_abbrev  0x0000012a       0x9f E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_abbrev  0x000001c9       0x92 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_abbrev  0x0000025b      0x170 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_abbrev  0x000003cb      0x19c E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_abbrev  0x00000567       0x5d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_loc      0x00000000     0x2bbe
 .debug_loc     0x00000000      0x102 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_loc     0x00000102      0x2ed E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_loc     0x000003ef      0x1ba E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_loc     0x000005a9       0xc7 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_loc     0x00000670     0x1537 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_loc     0x00001ba7     0x1017 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)

.debug_aranges  0x00000000       0xd8
 .debug_aranges
                0x00000000       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_aranges
                0x00000020       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_aranges
                0x00000040       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_aranges
                0x00000060       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_aranges
                0x00000080       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_aranges
                0x000000a0       0x20 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_aranges
                0x000000c0       0x18 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_line     0x00000000      0x849
 .debug_line    0x00000000      0x15d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_line    0x0000015d      0x164 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_line    0x000002c1      0x16d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_line    0x0000042e       0xf5 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
 .debug_line    0x00000523      0x15d E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_line    0x00000680      0x163 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
 .debug_line    0x000007e3       0x66 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)

.debug_str      0x00000000      0x4dc
 .debug_str     0x00000000      0x14f E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
                                0x188 (size before relaxing)
 .debug_str     0x0000014f       0x7b E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
                                0x1af (size before relaxing)
 .debug_str     0x000001ca       0x68 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
                                0x19c (size before relaxing)
 .debug_str     0x00000232       0x54 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)
                                0x1a3 (size before relaxing)
 .debug_str     0x00000286      0x1dc E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
                                0x284 (size before relaxing)
 .debug_str     0x00000462        0xa E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
                                0x284 (size before relaxing)
 .debug_str     0x0000046c       0x70 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_clz.o)
                                0x1cb (size before relaxing)

.debug_frame    0x00000000       0xa0
 .debug_frame   0x00000000       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcmp.o)
 .debug_frame   0x00000028       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memcpy.o)
 .debug_frame   0x00000050       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-memset.o)
 .debug_frame   0x00000078       0x28 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\or1k-unknown-elf\lib\no-delay\libc.a(lib_a-strcpy.o)

.debug_ranges   0x00000000      0x320
 .debug_ranges  0x00000000      0x190 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_udivdi3.o)
 .debug_ranges  0x00000190      0x190 E:\develop\day1\hx330x-gcc-elf-newlib-mingw-V4.9.1\lib\gcc\or1k-unknown-elf\4.9.1\no-delay\libgcc.a(_umoddi3.o)
