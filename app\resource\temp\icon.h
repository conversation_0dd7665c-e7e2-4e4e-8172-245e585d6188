
extern R_ICON_T User_Icon_Table[];

enum r_icon_id_e {
    R_ID_ICON_CG_PHOTO_SUCCESS = RES_ID_TYPE_ICON,
    R_ID_ICON_MENUAUDIO,
    R_ID_ICON_MENUBATTERY,
    R_ID_ICON_MENUCLOCK,
    R_ID_ICON_MENUDELALL,
    R_ID_ICON_MENUDELONE,
    R_ID_ICON_MENUEV,
    R_ID_ICON_MENUFORMAT,
    R_ID_ICON_MENUGSENSOR,
    R_ID_ICON_MENUHZ,
    R_ID_ICON_MENUIMAGEQ,
    R_ID_ICON_MENULANGUAGE,
    R_ID_ICON_MENULIGHTNORMAL,
    R_ID_ICON_MENULOCK,
    R_ID_ICON_MENULOOPRECORD,
    R_ID_ICON_MENUMONITOR,
    R_ID_ICON_MENUMOON,
    R_ID_<PERSON>CON_MENUMOTION,
    R_ID_ICON_MENUPOWEROFF,
    R_ID_ICON_MENURESET,
    R_ID_ICON_MENURESOLUTION,
    R_ID_ICON_MENUSCRENNOFF,
    R_ID_ICON_MENUSTRAMP,
    R_ID_ICON_MENUTV,
    R_ID_ICON_MENUUNLOCK,
    R_ID_ICON_MENUVERSION,
    R_ID_ICON_MENUVOLUME,
    R_ID_ICON_MTBACK,
    R_ID_ICON_MTBATTERY0,
    R_ID_ICON_MTBATTERY1,
    R_ID_ICON_MTBATTERY2,
    R_ID_ICON_MTBATTERY3,
    R_ID_ICON_MTBATTERY4,
    R_ID_ICON_MTBATTERY5,
    R_ID_ICON_MTBATTERY6,
    R_ID_ICON_MTFOCUS,
    R_ID_ICON_MTFORWARD,
    R_ID_ICON_MTIROFF,
    R_ID_ICON_MTIRON,
    R_ID_ICON_MTLED1,
    R_ID_ICON_MTLED2,
    R_ID_ICON_MTLED3,
    R_ID_ICON_MTLOCK,
    R_ID_ICON_MTMENU,
    R_ID_ICON_MTMICOFF,
    R_ID_ICON_MTMICON,
    R_ID_ICON_MTMORE,
    R_ID_ICON_MTMOTION,
    R_ID_ICON_MTOFF,
    R_ID_ICON_MTON,
    R_ID_ICON_MTON1,
    R_ID_ICON_MTON2,
    R_ID_ICON_MTPARKOFF,
    R_ID_ICON_MTPARKON,
    R_ID_ICON_MTPAUSE,
    R_ID_ICON_MTPAUSE1,
    R_ID_ICON_MTPHOTO,
    R_ID_ICON_MTPLAY,
    R_ID_ICON_MTPLAY1,
    R_ID_ICON_MTPLAY2,
    R_ID_ICON_MTPPAUSE,
    R_ID_ICON_MTPPLAY,
    R_ID_ICON_MTRECORD,
    R_ID_ICON_MTROTATE,
    R_ID_ICON_MTSDCNORMAL,
    R_ID_ICON_MTSDCNULL,
    R_ICON_MAX
};

