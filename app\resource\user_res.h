/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          : user_res.h
* Author             :
* Version            : v1
* Date               :
* Description        :
*******************************************************************/

#ifndef USER_RES_H
#define USER_RES_H

#define R_ID_MUSIC_TAKE_PHOTO           0
#define R_ID_MUSIC_POWER_ON             1
#define R_ID_MUSIC_POWER_OFF            2
#define R_ID_MUSIC_KEY_SOUND            3
#define R_ID_IMAGE_USB_MODE             4
#define R_ID_IMAGE_SENSOR_PHOTO         5
#define R_ID_IMAGE_POWER_ON             6
#define R_ID_IMAGE_POWER_OFF            7
#define R_ID_IMAGE_PLAY_VIDEO           8
#define R_ID_IMAGE_PCCAM_MODE           9
#define R_ID_IMAGE_MAIN_BACKGROUND      10
#define R_ID_IMAGE_LOGO_VIDEO           11
#define R_ID_IMAGE_LOGO_SETTING         12
#define R_ID_IMAGE_LOGO_PCCAM           13
#define R_ID_IMAGE_LOGO_MASS            14
#define R_ID_IMAGE_FANGDAJING           15
#define R_ID_IMAGE_DELETE_PHOTO         16
#define R_ID_BIN_VERSION                17
#define R_ID_BIN_FONTLIB                18
#define R_ID_BIN_LANGSTR                19
#define R_ID_BIN_ICONLIB                20
#define R_ID_BIN_PALETTE                21

extern R_STRING_T User_String_Table[];

enum r_str_id_e {
    R_ID_STR_LAN_ENGLISH = RES_ID_TYPE_STR,
    R_ID_STR_LAN_SCHINESE,
    R_ID_STR_LAN_TCHINESE,
    R_ID_STR_LAN_JAPANESE,
    R_ID_STR_LAN_KOERA,
    R_ID_STR_LAN_RUSSIAN,
    R_ID_STR_LAN_TURKEY,
    R_ID_STR_LAN_TAI,
    R_ID_STR_LAN_CZECH,
    R_ID_STR_LAN_UKRAINIAN,
    R_ID_STR_LAN_GERMAN,
    R_ID_STR_LAN_FRECH,
    R_ID_STR_LAN_ITALIAN,
    R_ID_STR_LAN_PORTUGUESE,
    R_ID_STR_LAN_DUTCH,
    R_ID_STR_LAN_HEBREW,
    R_ID_STR_LAN_POLISH,
    R_ID_STR_UPDATE_START,
    R_ID_STR_UPDATE_END,
    R_ID_STR_COM_OFF,
    R_ID_STR_COM_ON,
    R_ID_STR_COM_OK,
    R_ID_STR_COM_CANCEL,
    R_ID_STR_COM_YES,
    R_ID_STR_DC_OUT,
    R_ID_STR_COM_NO,
    R_ID_STR_COM_LOW,
    R_ID_STR_COM_MIDDLE,
    R_ID_STR_COM_HIGH,
    R_ID_STR_COM_WAITING,
    R_ID_STR_COM_50HZ,
    R_ID_STR_COM_60HZ,
    R_ID_STR_COM_P2_0,
    R_ID_STR_COM_P1_0,
    R_ID_STR_COM_P0_0,
    R_ID_STR_COM_N1_0,
    R_ID_STR_COM_N2_0,
    R_ID_STR_COM_ALWAYSON,
    R_ID_STR_COM_ECONOMIC,
    R_ID_STR_COM_NORMAL,
    R_ID_STR_COM_FINE,
    R_ID_STR_TIM_1MIN,
    R_ID_STR_TIM_2MIN,
    R_ID_STR_TIM_3MIN,
    R_ID_STR_TIM_5MIN,
    R_ID_STR_TIM_10MIN,
    R_ID_STR_TIM_2SEC,
    R_ID_STR_TIM_5SEC,
    R_ID_STR_TIM_30SEC,
    R_ID_STR_SET_DATETIME,
    R_ID_STR_SET_AUTOOFF,
    R_ID_STR_SET_LANGUAGE,
    R_ID_STR_SET_SCREENOFF,
    R_ID_STR_SET_VIDEOROTATE,
    R_ID_STR_SET_RESET,
    R_ID_STR_SET_FORMAT,
    R_ID_STR_SET_VERSION,
    R_ID_STR_SET_RESOLUTION,
    R_ID_STR_SET_LOOPRECORD,
    R_ID_STR_SET_MOTIONDET,
    R_ID_STR_SET_AUDIOREC,
    R_ID_STR_SET_TIMESTRAMP,
    R_ID_STR_SET_FILLIGHT,
    R_ID_STR_IR_AUTO,
    R_ID_STR_SET_GSENSOR,
    R_ID_STR_SET_PARKMODE,
    R_ID_STR_SET_FASTVIEW,
    R_ID_STR_SET_DELETECUR,
    R_ID_STR_SET_DELETEALL,
    R_ID_STR_SET_LOCKCUR,
    R_ID_STR_SET_LOCKALL,
    R_ID_STR_SET_UNLOCKCUR,
    R_ID_STR_SET_UNLOCKALL,
    R_ID_STR_SET_LOCKED,
    R_ID_STR_SET_LOCK,
    R_ID_STR_SET_DELETE,
    R_ID_STR_SET_PLAYMODE,
    R_ID_STR_SET_REPEATALL,
    R_ID_STR_SET_REPEATRAD,
    R_ID_STR_SET_REPEATSIG,
    R_ID_STR_SET_AUDIOPLAY,
    R_ID_STR_SET_VOLUME,
    R_ID_STR_SET_THUMBNAIL,
    R_ID_STR_SET_SETTING,
    R_ID_STR_SET_VIDEO,
    R_ID_STR_SET_PHOTO,
    R_ID_STR_SET_PLAY,
    R_ID_STR_SET_MUSIC,
    R_ID_STR_SET_AUDIOMODE,
    R_ID_STR_SET_USBMASS,
    R_ID_STR_SET_USBCAM,
    R_ID_STR_SET_BEEPSOUND,
    R_ID_STR_SET_FREQUENCY,
    R_ID_STR_SET_QUALITY,
    R_ID_STR_SET_PROMT,
    R_ID_STR_RES_240P,
    R_ID_STR_RES_480P,
    R_ID_STR_RES_480FHD,
    R_ID_STR_RES_720P,
    R_ID_STR_RES_1024P,
    R_ID_STR_RES_1080P,
    R_ID_STR_RES_1080FHD,
    R_ID_STR_RES_1440P,
    R_ID_STR_RES_3024P,
    R_ID_STR_RES_QVGA,
    R_ID_STR_RES_VGA,
    R_ID_STR_RES_HD,
    R_ID_STR_RES_FHD,
    R_ID_STR_RES_48M,
    R_ID_STR_RES_40M,
    R_ID_STR_RES_24M,
    R_ID_STR_RES_20M,
    R_ID_STR_RES_18M,
    R_ID_STR_RES_16M,
    R_ID_STR_RES_12M,
    R_ID_STR_RES_10M,
    R_ID_STR_RES_8M,
    R_ID_STR_RES_5M,
    R_ID_STR_RES_3M,
    R_ID_STR_RES_2M,
    R_ID_STR_RES_1M,
    R_ID_STR_SDC_NULL,
    R_ID_STR_SDC_NULL1,
    R_ID_STR_SDC_FULL,
    R_ID_STR_SDC_ERROR,
    R_ID_STR_FIL_NULL,
    R_ID_STR_FIL_LOCKED,
    R_ID_STR_FMT_ING,
    R_ID_STR_FMT_SUCCESS,
    R_ID_STR_FMT_FAIL,
    R_ID_STR_FMT_RESET,
    R_ID_STR_FMT_FORMAT,
    R_ID_STR_FMT_DELETE,
    R_ID_STR_PWR_LOW,
    R_ID_STR_PWR_NO,
    R_ID_STR_PWR_BACKLOW,
    R_ID_STR_PWR_CHARGELOW,
    R_ID_STR_ISP_WHITEBL,
    R_ID_STR_ISP_ISO,
    R_ID_STR_ISP_ANTISHANK,
    R_ID_STR_ISP_AUTO,
    R_ID_STR_ISP_SOFT,
    R_ID_STR_ISP_STRONG,
    R_ID_STR_ISP_SUNLIGHT,
    R_ID_STR_ISP_CLOUDY,
    R_ID_STR_ISP_TUNGSTEN,
    R_ID_STR_ISP_FLUORESCENT,
    R_ID_STR_ISP_BLACKWHITE,
    R_ID_STR_ISP_SEPIA,
    R_ID_STR_ISP_ISO100,
    R_ID_STR_ISP_ISO200,
    R_ID_STR_ISP_ISO400,
    R_ID_STR_ISP_WDR,
    R_ID_STR_ISP_EXPOSURE,
    R_ID_STR_COM_SUCCESS,
    R_ID_STR_COM_FAILED,
    R_ID_STR_LAN_MEMORY,
    R_STR_MAX
};

enum r_lan_id_e {
    LAN_ENGLISH,
    LAN_SCHINESE,
    LAN_TCHINESE,
    LAN_JAPANESE,
    LAN_KOERA,
    LAN_RUSSIAN,
    LAN_TURKEY,
    LAN_TAI,
    LAN_CZECH,
    LAN_UKRAINIAN,
    LAN_GERMAN,
    LAN_FRENCH,
    LAN_ITALIAN,
    LAN_PORTUGUESE,
    LAN_DUTCH,
    LAN_HEBREW,
    LAN_POLISH,
    LAN_MAX
};

extern R_ICON_T User_Icon_Table[];

enum r_icon_id_e {
    R_ID_ICON_CG_PHOTO_SUCCESS = RES_ID_TYPE_ICON,
    R_ID_ICON_MENUAUDIO,
    R_ID_ICON_MENUBATTERY,
    R_ID_ICON_MENUCLOCK,
    R_ID_ICON_MENUDELALL,
    R_ID_ICON_MENUDELONE,
    R_ID_ICON_MENUEV,
    R_ID_ICON_MENUFORMAT,
    R_ID_ICON_MENUGSENSOR,
    R_ID_ICON_MENUHZ,
    R_ID_ICON_MENUIMAGEQ,
    R_ID_ICON_MENULANGUAGE,
    R_ID_ICON_MENULIGHTNORMAL,
    R_ID_ICON_MENULOCK,
    R_ID_ICON_MENULOOPRECORD,
    R_ID_ICON_MENUMONITOR,
    R_ID_ICON_MENUMOON,
    R_ID_ICON_MENUMOTION,
    R_ID_ICON_MENUPOWEROFF,
    R_ID_ICON_MENURESET,
    R_ID_ICON_MENURESOLUTION,
    R_ID_ICON_MENUSCRENNOFF,
    R_ID_ICON_MENUSTRAMP,
    R_ID_ICON_MENUTV,
    R_ID_ICON_MENUUNLOCK,
    R_ID_ICON_MENUVERSION,
    R_ID_ICON_MENUVOLUME,
    R_ID_ICON_MTBACK,
    R_ID_ICON_MTBATTERY0,
    R_ID_ICON_MTBATTERY1,
    R_ID_ICON_MTBATTERY2,
    R_ID_ICON_MTBATTERY3,
    R_ID_ICON_MTBATTERY4,
    R_ID_ICON_MTBATTERY5,
    R_ID_ICON_MTBATTERY6,
    R_ID_ICON_MTFOCUS,
    R_ID_ICON_MTFORWARD,
    R_ID_ICON_MTIROFF,
    R_ID_ICON_MTIRON,
    R_ID_ICON_MTLED1,
    R_ID_ICON_MTLED2,
    R_ID_ICON_MTLED3,
    R_ID_ICON_MTLOCK,
    R_ID_ICON_MTMENU,
    R_ID_ICON_MTMICOFF,
    R_ID_ICON_MTMICON,
    R_ID_ICON_MTMORE,
    R_ID_ICON_MTMOTION,
    R_ID_ICON_MTOFF,
    R_ID_ICON_MTON,
    R_ID_ICON_MTON1,
    R_ID_ICON_MTON2,
    R_ID_ICON_MTPARKOFF,
    R_ID_ICON_MTPARKON,
    R_ID_ICON_MTPAUSE,
    R_ID_ICON_MTPAUSE1,
    R_ID_ICON_MTPHOTO,
    R_ID_ICON_MTPLAY,
    R_ID_ICON_MTPLAY1,
    R_ID_ICON_MTPLAY2,
    R_ID_ICON_MTPPAUSE,
    R_ID_ICON_MTPPLAY,
    R_ID_ICON_MTRECORD,
    R_ID_ICON_MTROTATE,
    R_ID_ICON_MTSDCNORMAL,
    R_ID_ICON_MTSDCNULL,
    R_ICON_MAX
};


                                              // ( A  R  G  B)
#define R_ID_PALETTE_Gray_SUB_SEL        0xEF // (FF 84 84 84)
#define R_ID_PALETTE_Gray_SUB_BG         0xF0 // (FF ED ED ED)
#define R_ID_PALETTE_DoderBlue           0xF1 // (FF 00 80 E0)
#define R_ID_PALETTE_DarkGreen           0xF2 // (FF 02 29 59)
#define R_ID_PALETTE_LightGreen          0xF3 // (FF 60 C0 C0)
#define R_ID_PALETTE_Yellow              0xF4 // (FF EB AC 14)
#define R_ID_PALETTE_Blue                0xF5 // (FF 00 00 FF)
#define R_ID_PALETTE_Green               0xF6 // (FF 00 FF 00)
#define R_ID_PALETTE_Red                 0xF7 // (FF FF 00 00)
#define R_ID_PALETTE_DimGray             0xF8 // (FF 30 30 30)
#define R_ID_PALETTE_DarkGray            0xF9 // (FF 50 50 50)
#define R_ID_PALETTE_Gray                0xFA // (FF 75 75 75)
#define R_ID_PALETTE_TransBlack          0xFB // (80 00 00 00)
#define R_ID_PALETTE_White               0xFC // (FF FF FF FF)
#define R_ID_PALETTE_Black               0xFD // (FF 00 00 00)
#define R_ID_PALETTE_Transparent         0xFE // (00 00 00 00)
#define R_ID_PALETTE_Error               0xFF // (00 00 00 00)


#endif
