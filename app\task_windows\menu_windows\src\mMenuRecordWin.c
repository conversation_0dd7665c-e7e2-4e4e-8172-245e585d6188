/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

void menuProcDateTime(winHandle handle,u32 parameNum,u32* parame);
void menuProcFormat(winHandle handle,u32 parameNum,u32* parame);
void menuProcDefault(winHandle handle,u32 parameNum,u32* parame);
void menuProcVersion(winHandle handle,u32 parameNum,u32* parame);

MENU_OPTION_START(videoResolution)//---------video resolution
	
//	MENU_OPTION_STR(R_ID_STR_RES_QVGA)
//	MENU_OPTION_STR(R_ID_STR_RES_VGA)
//	MENU_OPTION_STR(R_ID_STR_RES_HD)
//	MENU_OPTION_STR(R_ID_STR_RES_FHD)
	MENU_OPTION_STR(R_ID_STR_RES_VGA)
	MENU_OPTION_STR(R_ID_STR_RES_720P)
	// MENU_OPTION_STR(R_ID_STR_RES_1080FHD)
MENU_OPTION_END()

MENU_OPTION_START(photoResolution)//--------- photo resolution
	MENU_OPTION_STR(R_ID_STR_RES_2M)
	MENU_OPTION_STR(R_ID_STR_RES_1M)
	MENU_OPTION_STR(R_ID_STR_RES_VGA)
	
//	MENU_OPTION_STR(R_ID_STR_RES_3M)
//	MENU_OPTION_STR(R_ID_STR_RES_5M)
//	MENU_OPTION_STR(R_ID_STR_RES_8M)
//	MENU_OPTION_STR(R_ID_STR_RES_10M)
//	MENU_OPTION_STR(R_ID_STR_RES_12M)
//	MENU_OPTION_STR(R_ID_STR_RES_16M)
//	MENU_OPTION_STR(R_ID_STR_RES_18M)
//	MENU_OPTION_STR(R_ID_STR_RES_20M)
//	MENU_OPTION_STR(R_ID_STR_RES_24M)
//	MENU_OPTION_STR(R_ID_STR_RES_40M)
//	MENU_OPTION_STR(R_ID_STR_RES_48M)

MENU_OPTION_END()

MENU_OPTION_START(loopRecord)//--------loop record
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_TIM_3MIN)
	MENU_OPTION_STR(R_ID_STR_TIM_5MIN)
	MENU_OPTION_STR(R_ID_STR_TIM_10MIN)
MENU_OPTION_END()

MENU_OPTION_START(awb)//---------white balance
	MENU_OPTION_STR(R_ID_STR_ISP_AUTO)
	MENU_OPTION_STR(R_ID_STR_ISP_SUNLIGHT)
	MENU_OPTION_STR(R_ID_STR_ISP_CLOUDY)
	MENU_OPTION_STR(R_ID_STR_ISP_TUNGSTEN)
	MENU_OPTION_STR(R_ID_STR_ISP_FLUORESCENT)
MENU_OPTION_END()

MENU_OPTION_START(ev)//--------EV
	MENU_OPTION_STR(R_ID_STR_COM_P2_0)
	MENU_OPTION_STR(R_ID_STR_COM_P1_0)
	MENU_OPTION_STR(R_ID_STR_COM_P0_0)
	MENU_OPTION_STR(R_ID_STR_COM_N1_0)
	MENU_OPTION_STR(R_ID_STR_COM_N2_0)
MENU_OPTION_END()

MENU_OPTION_START(md)//--------motion decetion
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_COM_ON)
MENU_OPTION_END()

MENU_OPTION_START(audio)//--------movie audio
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_COM_ON)
MENU_OPTION_END()

MENU_OPTION_START(parking)//--------parking mode
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_COM_ON)
MENU_OPTION_END()

MENU_OPTION_START(timeStamp)//--------time stamp
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_COM_ON)
MENU_OPTION_END()

MENU_OPTION_START(gsensor)//--------gsensor
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_COM_LOW)
	MENU_OPTION_STR(R_ID_STR_COM_MIDDLE)
	MENU_OPTION_STR(R_ID_STR_COM_HIGH)
MENU_OPTION_END()

MENU_OPTION_START(keySound)//--------key sound
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_COM_ON)
MENU_OPTION_END()

MENU_OPTION_START(autoPowerOff)//----AUTO POWER OFF
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_TIM_1MIN)
	MENU_OPTION_STR(R_ID_STR_TIM_3MIN)
MENU_OPTION_END()

MENU_OPTION_START(language)//--------LANGUAGE
	MENU_OPTION_STR(R_ID_STR_LAN_ENGLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_SCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_TCHINESE)
	MENU_OPTION_STR(R_ID_STR_LAN_JAPANESE)
	MENU_OPTION_STR(R_ID_STR_LAN_KOERA)
	MENU_OPTION_STR(R_ID_STR_LAN_RUSSIAN)
	MENU_OPTION_STR(R_ID_STR_LAN_FRECH)
	MENU_OPTION_STR(R_ID_STR_LAN_GERMAN)
	MENU_OPTION_STR(R_ID_STR_LAN_CZECH)
	MENU_OPTION_STR(R_ID_STR_LAN_PORTUGUESE)
	MENU_OPTION_STR(R_ID_STR_LAN_POLISH)
	MENU_OPTION_STR(R_ID_STR_LAN_DUTCH)
	MENU_OPTION_STR(R_ID_STR_LAN_TURKEY)
	MENU_OPTION_STR(R_ID_STR_LAN_TAI)
MENU_OPTION_END()

MENU_OPTION_START(frequency)//--------key sound
	MENU_OPTION_STR(R_ID_STR_COM_50HZ)
	MENU_OPTION_STR(R_ID_STR_COM_60HZ)
MENU_OPTION_END()

MENU_OPTION_START(irLed)//--------ir
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_COM_ON)
	MENU_OPTION_STR(R_ID_STR_IR_AUTO)
MENU_OPTION_END()

MENU_OPTION_START(screenSave)//--------screen Save
	MENU_OPTION_STR(R_ID_STR_COM_OFF)
	MENU_OPTION_STR(R_ID_STR_TIM_3MIN)
	MENU_OPTION_STR(R_ID_STR_TIM_5MIN)
	MENU_OPTION_STR(R_ID_STR_TIM_10MIN)
MENU_OPTION_END()

MENU_OPTION_START(version)
	MENU_OPTION_STR(SysCtrl.version_str)
MENU_OPTION_END()
MENU_OPTION_START(memory)
	MENU_OPTION_STR(0)
MENU_OPTION_END()




MENU_ITME_START(setting)
	MENU_ITEM_OPTIONS(videoResolution,	CONFIG_ID_RESOLUTION,	R_ID_ICON_MENURESOLUTION,	R_ID_STR_SET_RESOLUTION)
	MENU_ITEM_OPTIONS(photoResolution,	CONFIG_ID_PRESLUTION,	R_ID_ICON_MENUIMAGEQ,		R_ID_STR_SET_QUALITY)
	MENU_ITEM_OPTIONS(language,			CONFIG_ID_LANGUAGE,		R_ID_ICON_MENULANGUAGE,		R_ID_STR_SET_LANGUAGE)
//	MENU_ITEM_OPTIONS(loopRecord,		CONFIG_ID_LOOPTIME,		R_ID_ICON_MENULOOPRECORD,	R_ID_STR_SET_LOOPRECORD)
//	MENU_ITEM_OPTIONS(awb,				CONFIG_ID_WBLANCE,		R_ID_ICON_MENULIGHTNORMAL,	R_ID_STR_ISP_WHITEBL)
//	MENU_ITEM_OPTIONS(ev,				CONFIG_ID_EV,			R_ID_ICON_MENUEV,			R_ID_STR_ISP_EXPOSURE)
	//MENU_ITEM_OPTIONS(md,				CONFIG_ID_MOTIONDECTION,R_ID_ICON_MENUMOTION,		R_ID_STR_SET_MOTIONDET)
//	MENU_ITEM_OPTIONS(audio,			CONFIG_ID_AUDIOREC,		R_ID_ICON_MENUAUDIO,		R_ID_STR_SET_AUDIOREC)
	//MENU_ITEM_OPTIONS(parking,			CONFIG_ID_PARKMODE,		R_ID_ICON_MENUMONITOR,		R_ID_STR_SET_PARKMODE)
	MENU_ITEM_OPTIONS(timeStamp,		CONFIG_ID_TIMESTAMP,	R_ID_ICON_MENUSTRAMP,		R_ID_STR_SET_TIMESTRAMP)
	//MENU_ITEM_OPTIONS(gsensor,			CONFIG_ID_GSENSOR,		R_ID_ICON_MENUGSENSOR,		R_ID_STR_SET_GSENSOR)
//	MENU_ITEM_OPTIONS(keySound,			CONFIG_ID_KEYSOUND,		R_ID_ICON_MENUAUDIO,		R_ID_STR_SET_BEEPSOUND)
	MENU_ITEM_PROC(menuProcDateTime,	R_ID_ICON_MENUCLOCK,	R_ID_STR_SET_DATETIME)
	// MENU_ITEM_OPTIONS(autoPowerOff,		CONFIG_ID_AUTOOFF,		R_ID_ICON_MENUPOWEROFF,		R_ID_STR_SET_AUTOOFF)
	// MENU_ITEM_OPTIONS(language,			CONFIG_ID_LANGUAGE,		R_ID_ICON_MENULANGUAGE,		R_ID_STR_SET_LANGUAGE)
//	MENU_ITEM_OPTIONS(frequency,		CONFIG_ID_FREQUNCY,R_ID_ICON_MENUHZ,R_ID_STR_SET_FREQUENCY)
//	MENU_ITEM_OPTIONS(irLed,			CONFIG_ID_IR_LED,		R_ID_ICON_MENULIGHTNORMAL,	R_ID_STR_SET_FILLIGHT)
//	MENU_ITEM_OPTIONS(screenSave,		CONFIG_ID_SCREENSAVE,	R_ID_ICON_MENUSCRENNOFF,	R_ID_STR_SET_SCREENOFF)
	//  MENU_ITEM_NO_ID(memory, 			R_ID_ICON_MENUVERSION,	R_ID_STR_LAN_MEMORY)
	MENU_ITEM_PROC(menuProcFormat,		R_ID_ICON_MENUFORMAT,	R_ID_STR_SET_FORMAT)
	MENU_ITEM_PROC(menuProcDefault,		R_ID_ICON_MENURESET,	R_ID_STR_SET_RESET)
#if UI_SHOW_SMALL_PANEL == 0
	MENU_ITEM_NO_ID(version,			R_ID_ICON_MENUVERSION,	R_ID_STR_SET_VERSION)
#else 
	MENU_ITEM_PROC(menuProcVersion,		R_ID_ICON_MENUVERSION,	R_ID_STR_SET_VERSION)
#endif
MENU_ITME_END()

//MENU_ITME_START(multiplePagesTest)
//	MENU_ITEM_OPTIONS(ev,CONFIG_ID_EV,R_ID_ICON_MENUEV,R_ID_STR_ISP_EXPOSURE)
//	MENU_ITEM_OPTIONS(md,CONFIG_ID_MOTIONDECTION,R_ID_ICON_MENUMOTION,R_ID_STR_SET_MOTIONDET)
//MENU_ITME_END()


MENU_PAGE_START(setting)
	MENU_PAGE_ITEMS(setting,R_ID_ICON_MTMENU,R_ID_ICON_MTMENU,R_ID_STR_SET_SETTING)
//MENU_PAGE_ITEMS(multiplePagesTest,R_ID_ICON_MTRECORD,R_ID_ICON_MTRECORD,R_ID_STR_SET_VIDEO)
MENU_PAGE_END()

MENU_DEFINE(setting)





