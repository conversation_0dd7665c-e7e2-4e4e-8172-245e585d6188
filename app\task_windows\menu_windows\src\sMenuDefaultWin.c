/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"

enum
{
	DEFAULT_TIPS_ID =0,
	DEFAULT_SELECT_ID,
	DEFAULT_RECT_ID,
	DEFAULT_TOPRECT_ID,
	DEFAULT_TIPS2_ID,
};
UNUSED ALIGNED(4) const widgetCreateInfor defaultWin[] =
{
#if UI_SHOW_SMALL_PANEL == 0
	#if USER_UI_MENU_ROUNDRECT == 0
		createFrameWin(						Rx(70),	<PERSON>y(42), <PERSON>w(180),<PERSON>h(142),R_ID_PALETTE_DimGray,WIN_ABS_POS),
		createStringIcon(DEFAULT_TIPS_ID,	Rx(0),	Ry(0), 	Rw(180),Rh(100),R_ID_STR_FMT_RESET,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createItemManage(DEFAULT_SELECT_ID,	Rx(0),	Ry(100),Rw(180),Rh(40),	INVALID_COLOR),
	#else
		createFrameRoundRimWin(				Rx(70),	Ry(42), Rw(180),Rh(142),R_ID_PALETTE_Gray_SUB_BG,USER_UI_MENU_RIMCOLOR,WIN_ABS_POS, ROUND_ALL),
		createRect(DEFAULT_TOPRECT_ID, 		Rx(0),	Ry(0), Rw(180),Rh(50), R_ID_PALETTE_DarkGray),
		createStringIcon(DEFAULT_TIPS2_ID,	Rx(0),	Ry(0), Rw(180),Rh(50),R_ID_STR_SET_RESET,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
				

		createStringIcon(DEFAULT_TIPS_ID,	Rx(0),	Ry(50), Rw(180),Rh(40),R_ID_STR_FMT_RESET,ALIGNMENT_CENTER, R_ID_PALETTE_Black,DEFAULT_FONT),
		createRect(DEFAULT_RECT_ID,			Rx(0),	Ry(103), Rw(180),Rh(1),	R_ID_PALETTE_Black),

		createItemManageRoundRim(DEFAULT_SELECT_ID,	Rx(0),	Ry(102),Rw(180),Rh(40), INVALID_COLOR, INVALID_COLOR, ROUND_NONE),
	#endif
#else
	#if USER_UI_MENU_ROUNDRECT == 0
		createFrameWin(						Rx(30),	Ry(50), Rw(260),Rh(140),R_ID_PALETTE_DimGray,WIN_ABS_POS),
		createStringIcon(DEFAULT_TIPS_ID,	Rx(0),	Ry(0), 	Rw(260),Rh(100),R_ID_STR_FMT_RESET,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createItemManage(DEFAULT_SELECT_ID,	Rx(0),	Ry(100),Rw(260),Rh(40),	INVALID_COLOR),
	#else
		createFrameRoundRimWin(				Rx(30),	Ry(50), Rw(260),Rh(140),R_ID_PALETTE_Gray_SUB_BG,USER_UI_MENU_RIMCOLOR,WIN_ABS_POS, ROUND_ALL),
		createStringIcon(DEFAULT_TIPS_ID,	Rx(0),	Ry(0), 	Rw(260),Rh(100),R_ID_STR_FMT_RESET,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createItemManageRoundRim(DEFAULT_SELECT_ID,	Rx(0),	Ry(100),Rw(260),Rh(40), INVALID_COLOR, INVALID_COLOR, ROUND_NONE),
	#endif
#endif
	widgetEnd(),
};



