/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuDelAllWin.c"

/*******************************************************************************
* Function Name  : getdelAllResInfor
* Description    : getdelAllResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getdelAllResInfor(u32 item,u32* image,u32* str)
{
	if(item == 0)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_OK;
	}
	else if(item==1)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_CANCEL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delAllKeyMsgOk
* Description    : delAllKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/

static int delAllKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	int i,cnt;
	int file_type;
	char *name;
	INT32S list;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,DELALL_SELECT_ID));
		if(item==0)
		{
			task_com_tips_show(TIPS_COM_WAITING_5S);
			if(app_taskCurId()  == TASK_PLAY_VIDEO)
			{
				if(SysCtrl.spi_jpg_list >= 0)
				{
					list = SysCtrl.spi_jpg_list;
				}else{
					list = SysCtrl.avi_list;
				}
				
			}else if(app_taskCurId()  == TASK_PLAY_AUDIO)
			{
				list = SysCtrl.wav_list;
			}else
			{
				return 0;
			}
			cnt = filelist_api_CountGet(list);
			//deg_Printf("del:cnt:%d\n",cnt);
			for(i = 0;i < cnt;i++)
			{
				hal_wdtClear();
				name = filelist_GetFileFullNameByIndex(list, i, &file_type);
				if(name && !(file_type & FILELIST_FLAG_LOK))
				{
					//deg_Printf("delete : %s.",name);
					if(file_type & FILELIST_TYPE_SPI)
					{
						if(nv_jpgfile_delete(filelist_GetFileIndexByIndex(list,i)) == NV_OK)
						{
							//deg_Printf("->ok\n");
						}	
						else
						{
							//deg_Printf("->fail\n");		
						}
										
					}else 
					{	
						if(f_unlink(name)==FR_OK)
						{
							//deg_Printf("->ok\n");
						}		
						else
						{
							//deg_Printf("->fail\n");	
						}
							
					}
				}
			}
			if(cnt > 0)
				task_com_tips_show(TIPS_COM_SUCCESS);
			else
				task_com_tips_show(TIPS_NO_FILE);
			if(list != SysCtrl.spi_jpg_list)
			task_com_sdc_freesize_check();
			filelist_listDelAll(list);
			SysCtrl.file_cnt   = filelist_api_CountGet(list);
			SysCtrl.file_index = SysCtrl.file_cnt - 1;
		}
		else
			uiWinDestroy(&handle);
	}
	return 0;
}


// //
// static int delAllKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
// {
// 	u32 keyState = KEY_STATE_INVALID;
// 	u32 item;
// 	int i,cnt;
// 	int file_type;
// 	char *name;
// 	INT32S list;
// 	if(parameNum == 1)
// 		keyState = parame[0];
// 	if(keyState == KEY_PRESSED)
// 	{
// 		item = uiItemManageGetCurrentItem(winItem(handle,DELALL_SELECT_ID));
// 		if(item==0)
// 		{
// 			task_com_tips_show(TIPS_COM_WAITING_5S);
// 			if(app_taskCurId()  == TASK_PLAY_VIDEO)
// 			{
// 				if(SysCtrl.spi_jpg_list >= 0)
// 				{
// 					list = SysCtrl.spi_jpg_list;
// 				}else{
// 					list = SysCtrl.avi_list;
// 				}
				
// 			}else if(app_taskCurId()  == TASK_PLAY_AUDIO)
// 			{
// 				list = SysCtrl.wav_list;
// 			}else
// 			{
// 				return 0;
// 			}
// 			cnt = filelist_api_CountGet(list);
// 			//deg_Printf("del:cnt:%d\n",cnt);
// 			for(i = 0;i < cnt;i++)
// 			{
// 				hal_wdtClear();
// 				name = filelist_GetFileFullNameByIndex(list, i, &file_type);
// 				if(name && !(file_type & FILELIST_FLAG_LOK))
// 				{
// 					//deg_Printf("delete : %s.",name);
// 					if(file_type & FILELIST_TYPE_SPI)
// 					{
// 						if(nv_jpgfile_delete(filelist_GetFileIndexByIndex(list,i)) == NV_OK)
// 						{
// 							//deg_Printf("->ok\n");
// 						}	
// 						else
// 						{
// 							//deg_Printf("->fail\n");		
// 						}
										
// 					}else 
// 					{	
// 						if(f_unlink(name)==FR_OK)
// 						{
// 							//deg_Printf("->ok\n");
// 						}		
// 						else
// 						{
// 							//deg_Printf("->fail\n");	
// 						}
							
// 					}
// 				}
// 			}
// 			if(cnt > 0)
// 				task_com_tips_show(TIPS_COM_SUCCESS);
// 			else
// 				task_com_tips_show(TIPS_NO_FILE);
// 			if(list != SysCtrl.spi_jpg_list)
// 			task_com_sdc_freesize_check();
// 			filelist_listDelAll(list);
// 			SysCtrl.file_cnt   = filelist_api_CountGet(list);
// 			SysCtrl.file_index = SysCtrl.file_cnt - 1;
// 		}
// 		else
// 			uiWinDestroy(&handle);
// 	}
// 	return 0;
// }//无卡拍照
/*******************************************************************************
* Function Name  : delAllKeyMsgUp
* Description    : delAllKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delAllKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,DELALL_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delAllKeyMsgDown
* Description    : delAllKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delAllKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		 uiItemManageNextItem(winItem(handle,DELALL_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delAllKeyMsgMenu
* Description    : delAllKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delAllKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delAllKeyMsgMode
* Description    : delAllKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delAllKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delAllOpenWin
* Description    : delAllOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delAllOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]delAllOpenWin\n");
	uiItemManageSetRowSum(winItem(handle,DELALL_SELECT_ID),1,Rh(40));
#if UI_SHOW_SMALL_PANEL == 0
	uiItemManageSetColumnSumWithGap(winItem(handle,DELALL_SELECT_ID),0,2,Rw(90), Rw(12));
#else
	uiItemManageSetColumnSumWithGap(winItem(handle,DELALL_SELECT_ID),0,2,Rw(100),Rw(6));
#endif 
	uiItemManageCreateItem(		winItem(handle,DELALL_SELECT_ID),uiItemCreateMenuOption,getdelAllResInfor,2);
	uiItemManageSetCharInfor(	winItem(handle,DELALL_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_Black);
	uiItemManageSetSelectColor(	winItem(handle,DELALL_SELECT_ID),R_ID_PALETTE_Gray);
	uiItemManageSetUnselectColor(winItem(handle,DELALL_SELECT_ID),R_ID_PALETTE_Gray_SUB_BG);

	uiItemManageSetCurItem(		winItem(handle,DELALL_SELECT_ID),1);
	
	return 0;
}
/*******************************************************************************
* Function Name  : delAllCloseWin
* Description    : delAllCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delAllCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]delAllCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : delAllWinChildClose
* Description    : delAllWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delAllWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]delAllWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : delAllTouchWin
* Description    : delAllTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delAllTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("delAllTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == DELALL_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : delAllTouchSlideOff
* Description    : delAllTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int delAllTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;

	if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	return 0;
}

ALIGNED(4) msgDealInfor delAllMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	delAllOpenWin},
	{SYS_CLOSE_WINDOW,	delAllCloseWin},
	{SYS_CHILE_COLSE,	delAllWinChildClose},
	{SYS_TOUCH_WINDOW,  delAllTouchWin},
	{SYS_TOUCH_SLIDE_OFF,delAllTouchSlideOff},	
	{KEY_EVENT_PHOTO,		delAllKeyMsgOk},
	{KEY_EVENT_UP,		delAllKeyMsgUp},
	{KEY_EVENT_DOWN,	delAllKeyMsgDown},
	{KEY_EVENT_PLAYVIDEO,	delAllKeyMsgMenu},
	{KEY_EVENT_MODE,	delAllKeyMsgMode},
	{EVENT_MAX,NULL},
};

WINDOW(delAllWindow,delAllMsgDeal,delAllWin)


