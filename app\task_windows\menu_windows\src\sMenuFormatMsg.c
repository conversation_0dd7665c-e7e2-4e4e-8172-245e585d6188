/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuFormatWin.c"

/*******************************************************************************
* Function Name  : getformatResInfor
* Description    : getformatResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : none
*******************************************************************************/
static u32 getformatResInfor(u32 item,u32* image,u32* str)
{
	if(item == 0)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_OK;
	}
	else if(item == 1)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_CANCEL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : formatKeyMsgOk
* Description    : formatKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int formatKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	u8 *mem;
	u8 res;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,FORMAT_SELECT_ID));
		if(item == 0)
		{
			if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
			{
				mem = (INT8U *)hal_sysMemMalloc(32768);
				if(mem == NULL)
				{
					deg_Printf("malloc mem for format fail.\n");
					task_com_tips_show(TIPS_ERROR);
					return 0;
				}
				task_com_tips_show(TIPS_COM_WAITING_5S);
				u32 sd_cap = sd_api_Capacity()/(2*1024L);	// MB
				if(sd_cap < 2*1024L)
				{
					res = f_mkfs("",FM_FAT,32768,(void *)mem,32768);
				}else
				{
					res = f_mkfs("",FM_FAT32,32768,(void *)mem,32768);
				}
				
				//res =f_mkfs("",FM_EXFAT,32768,(void *)mem,32768);
				hal_sysMemFree(mem);
				if(res == FR_OK)
				{				
					//SysCtrl.dev_stat_sdc = SDC_STAT_NULL;  // systemDeamonService will mount 
					filelist_api_nodedestory(SysCtrl.avi_list);
					filelist_api_nodedestory(SysCtrl.avia_list);
					filelist_api_nodedestory(SysCtrl.avib_list);
					filelist_api_nodedestory(SysCtrl.jpg_list);
					filelist_api_nodedestory(SysCtrl.wav_list);
					
					SysCtrl.avi_list  = -1;
					SysCtrl.avia_list = -1;
					SysCtrl.avib_list = -1;
					SysCtrl.jpg_list  = -1;
					SysCtrl.wav_list  = -1;
					task_com_fs_scan();
					task_com_tips_show(TIPS_FMT_SUCCESS);
				}
				else
				{
					deg_Printf ("error : %d\n",res);
					task_com_tips_show(TIPS_FMT_FAIL);
				}
			}else
			{
				task_com_tips_show(TIPS_COM_WAITING_2S);
				nv_jpg_format();
				if(SysCtrl.spi_jpg_list >= 0)
				{
					filelist_api_nodedestory(SysCtrl.spi_jpg_list);
					SysCtrl.spi_jpg_list = -1;
				}				
				SysCtrl.spi_jpg_list = filelist_api_nodecreate(NULL,FILELIST_TYPE_SPI,-1);
				filelist_api_scan(SysCtrl.spi_jpg_list);				
			// #if TASK_SCAN_FILE_EVERY_TIME == 0
				//nv_jpg_init();
				//SysCtrl.spi_jpg_list = filelist_api_nodecreate(NULL,FILELIST_TYPE_SPI,-1);
				// filelist_api_scan(SysCtrl.spi_jpg_list);
			// #else
			// 	if(SysCtrl.spi_jpg_list >= 0)
			// 	{
			// 		filelist_api_nodedestory(SysCtrl.spi_jpg_list);
			// 		SysCtrl.spi_jpg_list = -1;
			// 	}else
			// 	{
			// 		nv_jpg_uinit();
			// 	}
			// #endif
				task_com_tips_show(TIPS_FMT_SUCCESS);
			}

		}
		else
			uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : formatKeyMsgUp
* Description    : formatKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int formatKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,FORMAT_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : formatKeyMsgDown
* Description    : formatKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int formatKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		 uiItemManageNextItem(winItem(handle,FORMAT_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : formatKeyMsgMenu
* Description    : formatKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int formatKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : formatKeyMsgMode
* Description    : formatKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int formatKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : formatOpenWin
* Description    : formatOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int formatOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]formatOpenWin\n");
	uiItemManageSetRowSum(winItem(handle,FORMAT_SELECT_ID),1,Rh(40));
#if UI_SHOW_SMALL_PANEL == 0
	uiItemManageSetColumnSumWithGap(winItem(handle,FORMAT_SELECT_ID),0,2,Rw(90), Rw(0));
#else
	uiItemManageSetColumnSumWithGap(winItem(handle,FORMAT_SELECT_ID),0,2,Rw(100),Rw(6));
#endif
	uiItemManageCreateItem(		winItem(handle,FORMAT_SELECT_ID),uiItemCreateMenuOption,getformatResInfor,2);
	uiItemManageSetCharInfor(	winItem(handle,FORMAT_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_Black);
	uiItemManageSetSelectColor(	winItem(handle,FORMAT_SELECT_ID),R_ID_PALETTE_Gray);
	uiItemManageSetUnselectColor(winItem(handle,FORMAT_SELECT_ID),R_ID_PALETTE_Gray_SUB_BG);

	uiItemManageSetCurItem(		winItem(handle,FORMAT_SELECT_ID),1);
	
	return 0;
}
/*******************************************************************************
* Function Name  : formatCloseWin
* Description    : formatCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int formatCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]formatCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : formatWinChildClose
* Description    : formatWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int formatWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]formatWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : formatTouchWin
* Description    : formatTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int formatTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("formatTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == FORMAT_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : formatTouchSlideOff
* Description    : formatTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int formatTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;

	if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	return 0;
}

ALIGNED(4) msgDealInfor formatMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	formatOpenWin},
	{SYS_CLOSE_WINDOW,	formatCloseWin},
	{SYS_CHILE_COLSE,	formatWinChildClose},
	{SYS_TOUCH_WINDOW,  formatTouchWin},
	{SYS_TOUCH_SLIDE_OFF,formatTouchSlideOff},	

	{KEY_EVENT_PHOTO,		formatKeyMsgOk},
	{KEY_EVENT_UP,		formatKeyMsgUp},
	{KEY_EVENT_DOWN,	formatKeyMsgDown},
	{KEY_EVENT_PLAYVIDEO,	formatKeyMsgMenu},
	{KEY_EVENT_MODE,	formatKeyMsgMode},
	{EVENT_MAX,NULL},
};

WINDOW(formatWindow,formatMsgDeal,formatWin)


