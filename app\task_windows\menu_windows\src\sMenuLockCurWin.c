/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	LOCKCUR_TIPS_ID=0,
	LOCKCUR_SELECT_ID
};
UNUSED ALIGNED(4) const widgetCreateInfor lockCurWin[] =
{
#if UI_SHOW_SMALL_PANEL == 0
	#if USER_UI_MENU_ROUNDRECT == 0
		createFrameWin(						Rx(70),	<PERSON>y(42), <PERSON>w(180),<PERSON>h(142),R_ID_PALETTE_DimGray,WIN_ABS_POS),
		//createStringIcon(LOCKCUR_TIPS_ID,	Rx(0),	Ry(0), 	Rw(180),Rh(100),R_ID_STR_SET_LOCKCUR,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createItemManage(LOCKCUR_SELECT_ID,	Rx(0),	Ry(100),Rw(180),Rh(40),	INVALID_COLOR),
	#else
		createFrameRoundRimWin(				Rx(70),	Ry(42), Rw(180),Rh(142),R_ID_PALETTE_DimGray,USER_UI_MENU_RIMCOLOR,WIN_ABS_POS, ROUND_ALL),
		//createStringIcon(LOCKCUR_TIPS_ID,	Rx(0),	Ry(0), 	Rw(180),Rh(100),R_ID_STR_SET_LOCKCUR,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createItemManageRoundRim(LOCKCUR_SELECT_ID,	Rx(0),	Ry(100),Rw(180),Rh(40), INVALID_COLOR, INVALID_COLOR, ROUND_NONE),
	#endif
#else
	#if USER_UI_MENU_ROUNDRECT == 0
		createFrameWin(						Rx(30),	Ry(50), Rw(260),Rh(140),R_ID_PALETTE_DimGray,WIN_ABS_POS),
		//createStringIcon(LOCKCUR_TIPS_ID,	Rx(0),	Ry(0), 	Rw(260),Rh(100),R_ID_STR_SET_LOCKCUR,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createItemManage(LOCKCUR_SELECT_ID,	Rx(0),	Ry(100),Rw(260),Rh(40),	INVALID_COLOR),
	#else
		createFrameRoundRimWin(				Rx(30),	Ry(50), Rw(260),Rh(140),R_ID_PALETTE_DimGray,USER_UI_MENU_RIMCOLOR,WIN_ABS_POS, ROUND_ALL),
		//createStringIcon(LOCKCUR_TIPS_ID,	Rx(0),	Ry(0), 	Rw(260),Rh(100),R_ID_STR_SET_LOCKCUR,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createItemManageRoundRim(LOCKCUR_SELECT_ID,	Rx(0),	Ry(100),Rw(260),Rh(40), INVALID_COLOR, INVALID_COLOR, ROUND_NONE),
	#endif
#endif
	widgetEnd(),
};



