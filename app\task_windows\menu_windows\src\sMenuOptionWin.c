/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	OPTION_TITLE_ID=0,
	OPTION_RECT_ID,
	OPTION_SELECT_ID,
};
UNUSED ALIGNED(4) const widgetCreateInfor menuOptionWin[] =
{
#if USER_UI_MENU_ROUNDRECT == 0
	createFrameWin(						Rx(70),	Ry(42), <PERSON>w(180),Rh(176),R_ID_PALETT<PERSON>_<PERSON>, W<PERSON>_ABS_POS),
	createStringIcon(OPTION_TITLE_ID,	Rx(0), 	<PERSON><PERSON>(0), 	<PERSON>w(180),Rh(32),	" ",ALIGNMENT_CENTER,  R_ID_PALETTE_Black,DEFAULT_FONT),
	createRect(OPTION_RECT_ID,       	Rx(0),	Ry(32), Rw(180),Rh(2),	R_ID_PALETTE_DarkGreen),
	createItemManage(OPTION_SELECT_ID,	Rx(0),	Ry(34), Rw(180),Rh(142),INVALID_COLOR),
#else
	createFrameRoundRimWin(				Rx(70),	Ry(42), Rw(180),Rh(176),R_ID_PALETTE_Gray_SUB_BG, USER_UI_MENU_RIMCOLOR, WIN_ABS_POS, ROUND_ALL),
	createStringIcon(OPTION_TITLE_ID,	Rx(0), 	Ry(0), 	Rw(180),Rh(32),	" ",ALIGNMENT_CENTER,  R_ID_PALETTE_Black,DEFAULT_FONT),
	//createRectRoundRim(OPTION_RECT_ID,  Rx(0),	Ry(32), Rw(180),Rh(2),	R_ID_PALETTE_DarkGreen, USER_UI_MENU_RIMCOLOR, ROUND_ALL),
	createRect(OPTION_RECT_ID,       	Rx(0),	Ry(32), Rw(180),Rh(1),	R_ID_PALETTE_Black),
	createItemManageRoundRim(OPTION_SELECT_ID,	Rx(0),	Ry(34), Rw(180),Rh(142), INVALID_COLOR, INVALID_COLOR, ROUND_NONE),
#endif

	widgetEnd(),
};



