/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sMenuUnlockAllWin.c"
/*******************************************************************************
* Function Name  : getunlockAllResInfor
* Description    : getunlockAllResInfor
* Input          : u32 item,u32* image,u32* str
* Output         : none
* Return         : u32: 
*******************************************************************************/
static u32 getunlockAllResInfor(u32 item,u32* image,u32* str)
{
	if(item == 0)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_OK;
	}
	else if(item == 1)
	{
		if(image)
			*image = INVALID_RES_ID;
		if(str)
			*str   = R_ID_STR_COM_CANCEL;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllKeyMsgOk
* Description    : unlockAllKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	int i,cnt;
	char *name;
	INT32S list;
	int file_type;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		item = uiItemManageGetCurrentItem(winItem(handle,UNLOCKALL_SELECT_ID));
		if(item == 0)
		{
			if(app_taskCurId()  == TASK_PLAY_VIDEO)
			{
				if(SysCtrl.spi_jpg_list >= 0)
				{
					list = SysCtrl.spi_jpg_list;
				}else{
					list = SysCtrl.avi_list;
				}
			}else if(app_taskCurId()  == TASK_PLAY_AUDIO)
			{
				list = SysCtrl.wav_list;
			}else
			{
				return 0;
			}
			cnt = filelist_api_CountGet(list);
			if(cnt > 0)
			{
				task_com_tips_show(TIPS_COM_WAITING_5S);
				for(i = 0;i < cnt;i++)
				{
					if(filelist_fnameChecklockByIndex(list,i) > 0) // > 0: lock, 0: AVI and unlock, <0: lock invalid
					{
						name = filelist_GetFileFullNameByIndex(list, i, &file_type); 
						hx330x_str_cpy(SysCtrl.file_fullname,name);
						filenode_filefullnameUnlock(name);
						deg_Printf("unlock : %s -> %s.",SysCtrl.file_fullname,name);
						if(file_type & FILELIST_TYPE_SPI)
						{
							if(nv_jpg_change_lock (filelist_GetFileIndexByIndex(list,SysCtrl.file_index),0) == NV_OK)
							{
								deg_Printf("->ok\n");
								filenode_fnameUnlockByIndex(list,SysCtrl.file_index);
							}else
							{
								deg_Printf("->fail\n");
							}
						}else
						{
							if(f_rename(SysCtrl.file_fullname,name)==FR_OK)  // rename in file system
							{
								deg_Printf("->ok\n");
								filenode_fnameUnlockByIndex(list,i);
							}
							else
								deg_Printf("->fail\n");
						}
							
					}
				}
				task_com_tips_show(TIPS_COM_SUCCESS);
			}
			else
				task_com_tips_show(TIPS_NO_FILE);
		}
		else
			uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllKeyMsgUp
* Description    : unlockAllKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		uiItemManagePreItem(winItem(handle,UNLOCKALL_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllKeyMsgDown
* Description    : unlockAllKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		 uiItemManageNextItem(winItem(handle,UNLOCKALL_SELECT_ID));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllKeyMsgMenu
* Description    : unlockAllKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllKeyMsgMode
* Description    : unlockAllKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//app_taskChange();
		uiWinDestroy(&handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllOpenWin
* Description    : unlockAllOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]unlockAllOpenWin\n");
	uiItemManageSetRowSum(winItem(handle,UNLOCKALL_SELECT_ID),1,Rh(32));
#if UI_SHOW_SMALL_PANEL == 0
	uiItemManageSetColumnSumWithGap(winItem(handle,UNLOCKALL_SELECT_ID),0,2,Rw(50), Rw(12));
#else
	uiItemManageSetColumnSumWithGap(winItem(handle,UNLOCKALL_SELECT_ID),0,2,Rw(100),Rw(6));
#endif 
	uiItemManageCreateItem(		winItem(handle,UNLOCKALL_SELECT_ID),uiItemCreateMenuOption,getunlockAllResInfor,2);
	uiItemManageSetCharInfor(	winItem(handle,UNLOCKALL_SELECT_ID),DEFAULT_FONT,ALIGNMENT_CENTER,R_ID_PALETTE_White);
	uiItemManageSetSelectColor(	winItem(handle,UNLOCKALL_SELECT_ID),R_ID_PALETTE_DoderBlue);
	uiItemManageSetUnselectColor(winItem(handle,UNLOCKALL_SELECT_ID),R_ID_PALETTE_Gray);

	uiItemManageSetCurItem(		winItem(handle,UNLOCKALL_SELECT_ID),1);
	
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllCloseWin
* Description    : unlockAllCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]unlockAllCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllCloseWin
* Description    : unlockAllCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int unlockAllWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]unlockAllWinChildClose\n");
	uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllTouchWin
* Description    : unlockAllTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int unlockAllTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=3)
	{
		//deg_Printf("unlockAllTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
		if(parame[0] == UNLOCKALL_SELECT_ID)
			XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	}
	return 0;
}
/*******************************************************************************
* Function Name  : unlockAllTouchSlideOff
* Description    : unlockAllTouchSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int unlockAllTouchSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
		return 0;

	if(parame[0] == TP_DIR_LEFT)
		uiWinDestroy(&handle);
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	return 0;
}

ALIGNED(4) msgDealInfor unlockAllMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	unlockAllOpenWin},
	{SYS_CLOSE_WINDOW,	unlockAllCloseWin},
	{SYS_CHILE_COLSE,	unlockAllWinChildClose},
	{SYS_TOUCH_WINDOW,  unlockAllTouchWin},
	{SYS_TOUCH_SLIDE_OFF,unlockAllTouchSlideOff},
	{KEY_EVENT_OK,		unlockAllKeyMsgOk},
	{KEY_EVENT_UP,		unlockAllKeyMsgUp},
	{KEY_EVENT_DOWN,	unlockAllKeyMsgDown},
	{KEY_EVENT_MENU,	unlockAllKeyMsgMenu},
	{KEY_EVENT_MODE,	unlockAllKeyMsgMode},
	{EVENT_MAX,			NULL},
};

WINDOW(unlockAllWindow,unlockAllMsgDeal,unlockAllWin)


