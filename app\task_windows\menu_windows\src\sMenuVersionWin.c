/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
#if UI_SHOW_SMALL_PANEL > 0
enum
{
	VERSION_TIPS_ID=0,
	VERSION_LOG1_ID,
	VERSION_LOG2_ID,
};
UNUSED ALIGNED(4) const widgetCreateInfor versionWin[] =
{
	#if USER_UI_MENU_ROUNDRECT == 0
		createFrameWin(						Rx(70),	<PERSON>y(50), <PERSON>w(180),<PERSON>h(140),	R_ID_PALETTE_Black,WIN_ABS_POS),
		createStringIcon(VERSION_TIPS_ID,	Rx(0),	<PERSON>y(10), Rw(180),Rh(40),		R_ID_STR_SET_VERSION,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createStringIcon(VERSION_LOG1_ID,	Rx(0),	Ry(50), Rw(180),Rh(40),		"HX330X",	ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createStringIcon(VERSION_LOG2_ID,	Rx(0),	Ry(90), Rw(180),Rh(40),		"20220401",	ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	#else
		createFrameRoundRimWin(					Rx(70),	Ry(50), Rw(180),Rh(140),	R_ID_PALETTE_Black,USER_UI_MENU_RIMCOLOR,WIN_ABS_POS,ROUND_ALL),
		createStringIcon(VERSION_TIPS_ID,	Rx(0),	Ry(10), Rw(180),Rh(40),		R_ID_STR_SET_VERSION,ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createStringIcon(VERSION_LOG1_ID,	Rx(0),	Ry(50), Rw(180),Rh(40),		"HX330X",	ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
		createStringIcon(VERSION_LOG2_ID,	Rx(0),	Ry(90), Rw(180),Rh(40),		"20220401",	ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	#endif
	widgetEnd(),
};
#endif

