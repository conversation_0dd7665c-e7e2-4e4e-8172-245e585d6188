/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sWindowNoFileWin.c"
/*******************************************************************************
* Function Name  : noFileKeyMsgMode
* Description    : noFileKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int noFileKeyMsgAll(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : noFileKeyMsgMode
* Description    : noFileKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int noFileKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		task_com_keysound_play();
		app_taskChange();
	}
	return 0;
}


static int playphotokeyMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		// XOSTimeDly(300);
		app_taskStart(TASK_RECORD_PHOTO,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : noFileSysMsgSD
* Description    : noFileSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int noFileSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		INT32S list;
		taskID curTaskId = app_taskCurId();
		if(curTaskId == TASK_PLAY_VIDEO)
		{
			list = SysCtrl.avi_list;
		}else if(curTaskId == TASK_PLAY_AUDIO)
		{
			list = SysCtrl.wav_list;
		}else
		{
			return 0;
		}
		SysCtrl.file_cnt   = filelist_api_CountGet(list);
		SysCtrl.file_index = SysCtrl.file_cnt - 1;
		if(SysCtrl.file_cnt >= 0)
			uiWinDestroy(&handle);
		
	}
	return 0;
}
static int noFileOpenWin(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("noFile open Win!!!\n");
	if(parameNum==1)
		uiWinSetResid(winItem(handle,NO_FILE_TIP),parame[0]);
	return 0;
}
static int noFileCloseWin(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("noFile Close Win!!!\n");
	return 0;
}
static int noFileWinChildClose(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("noFile WinChild Close!!!\n");
	return 0;
}

static int noFilekeyPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		// XOSTimeDly(300);
		app_taskStart(TASK_MAIN,0);
	}
	return 0;
}

ALIGNED(4) msgDealInfor noFileMsgDeal[]=
{
	{KEY_EVENT_OK,		noFileKeyMsgAll},
	{KEY_EVENT_UP,		noFileKeyMsgAll},
	{KEY_EVENT_DOWN,	noFileKeyMsgAll},
	{KEY_EVENT_MENU,	noFileKeyMsgAll},
	{KEY_EVENT_MODE,	noFileKeyMsgMode},
	{KEY_EVENT_PLAYVIDEO, playphotokeyMode},
	{KEY_EVENT_POWER,		noFilekeyPower},
	{SYS_OPEN_WINDOW,	noFileOpenWin},
	{SYS_CLOSE_WINDOW,	noFileCloseWin},
	{SYS_CHILE_COLSE,	noFileWinChildClose},
	{SYS_EVENT_SDC,		noFileSysMsgSD},
	{EVENT_MAX,NULL},
};

WINDOW(noFileWindow,noFileMsgDeal,noFileWin)



