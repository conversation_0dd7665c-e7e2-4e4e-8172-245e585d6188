/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sWindowTips1Win.c"

ALIGNED(4) static u32 tip1ResId;
ALIGNED(4) static u32 continueTime1 = 0xffffffff;
/*******************************************************************************
* Function Name  : tips1KeyMsgAll
* Description    : tips1KeyMsgAll
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1KeyMsgAll(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//if( tipResId == (u32)TIPS_USENSOR_POWER_LOW||
		//		tipResId == (u32)TIPS_POWER_LOW||
		//		tipResId == (u32)TIPS_NO_POWER||
		//		tipResId == (u32)TIPS_NO_DC_POWEROFF)
		{
			uiWinDestroy(&handle);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : tips1SysMsgSD
* Description    : tips1SysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1SysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	if(tip1ResId == TIPS_NO_DC_POWEROFF)
		return 0;
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		if(	tip1ResId == TIPS_SD_NOT_INSERT||
			tip1ResId == TIPS_SD_FULL||
			tip1ResId == TIPS_SD_ERROR)
		{
			uiWinDestroy(&handle);
			return 0;
		}
			
	}
	uiParentDealMsg(handle,SYS_EVENT_SDC);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsgUSB
* Description    : tipsSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1SysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	{
		if( tip1ResId == (u32)TIPS_USENSOR_POWER_LOW||
			tip1ResId == (u32)TIPS_POWER_LOW||
			tip1ResId == (u32)TIPS_NO_POWER||
			tip1ResId == (u32)TIPS_NO_DC_POWEROFF)
		{
			uiWinDestroy(&handle);
			return 0;
		}
			
	}
	uiParentDealMsg(handle,SYS_EVENT_USBDEV);
	return 0;
}
/*******************************************************************************
* Function Name  : tips1SysMsgUSBHost
* Description    : tips1SysMsgUSBHost
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1SysMsgUSBHost(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_USBHOST);
	return 0;
}
/*******************************************************************************
* Function Name  : tips1SysMsgBattery
* Description    : tips1SysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1SysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(tip1ResId == TIPS_NO_DC_POWEROFF)
		return 0;
	if(SysCtrl.dev_stat_battery == BATTERY_STAT_1)
	{
		tip1ResId =	TIPS_POWER_LOW;
		uiWinSetResid(winItem(handle,TIP1_STRING_ID),tip1ResId);
	}
	else if(SysCtrl.dev_stat_battery == BATTERY_STAT_0)
	{
		tip1ResId =	TIPS_NO_POWER;
		uiWinSetResid(winItem(handle,TIP1_STRING_ID),tip1ResId);
	}
	uiParentDealMsg(handle,SYS_EVENT_BAT);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsg500Ms
* Description    : tipsSysMsg500Ms
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1SysMsg500Ms(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_500MS);
	return 0;
}
/*******************************************************************************
* Function Name  : tips1SysMsg1S
* Description    : tips1SysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1SysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_1S);
	if(continueTime1)
		continueTime1--;
	if(continueTime1 == 0)
		uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsg1S
* Description    : tipsSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1SysMsgTimeUpdate(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_TIME_UPDATE);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsg500Ms
* Description    : tipsSysMsg500Ms
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1SysMsgRecord(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_RECORD);
	return 0;
}
/*******************************************************************************
* Function Name  : tipsSysMsg500Ms
* Description    : tipsSysMsg500Ms
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1SysMsgPlay(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_PLAY);
	return 0;
}
/*******************************************************************************
* Function Name  : tips1OpenWin
* Description    : tips1OpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1OpenWin(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(parameNum < 2)
		return 0;
	tip1ResId 		= parame[0];
	continueTime1	= parame[1];
	uiWinSetResid(winItem(handle,TIP1_STRING_ID),tip1ResId);
	deg_Printf("[WIN]tips1OpenWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : tips1CloseWin
* Description    : tips1CloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1CloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]tips1CloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : tips1WinChildClose
* Description    : tips1WinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int tips1WinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]tips1WinChildClose\n");
	return 0;
}


ALIGNED(4) msgDealInfor tips1MsgDeal[]=
{
	{SYS_OPEN_WINDOW,			tips1OpenWin},
	{SYS_CLOSE_WINDOW,			tips1CloseWin},
	{SYS_CHILE_COLSE,			tips1WinChildClose},
	{KEY_EVENT_OK,				tips1KeyMsgAll},
	{KEY_EVENT_UP,				tips1KeyMsgAll},
	{KEY_EVENT_DOWN,			tips1KeyMsgAll},
	{KEY_EVENT_UVC_FORM,		tips1KeyMsgAll},
	{KEY_EVENT_UVC_FRAME,		tips1KeyMsgAll},
	{KEY_EVENT_ROTATE_ADD,		tips1KeyMsgAll},
	{KEY_EVENT_ROTATE_DEC,		tips1KeyMsgAll},
	{KEY_EVENT_POWER,	  		tips1KeyMsgAll},
	{KEY_EVENT_MENU,			tips1KeyMsgAll},
	{KEY_EVENT_MODE,			tips1KeyMsgAll},

	{SYS_EVENT_SDC,				tips1SysMsgSD},
	{SYS_EVENT_USBDEV,			tips1SysMsgUSB},
	{SYS_EVENT_USBHOST,			tips1SysMsgUSBHost},
	{SYS_EVENT_BAT,				tips1SysMsgBattery},
	{SYS_EVENT_500MS,			tips1SysMsg500Ms},
	{SYS_EVENT_1S,				tips1SysMsg1S},
	{SYS_EVENT_TIME_UPDATE,		tips1SysMsgTimeUpdate},
	{SYS_EVENT_RECORD,			tips1SysMsgRecord},
	{SYS_EVENT_PLAY,			tips1SysMsgPlay},
	{EVENT_MAX,			NULL},
};

MULTIWIN(tips1Window,tips1MsgDeal,tips1Win)



