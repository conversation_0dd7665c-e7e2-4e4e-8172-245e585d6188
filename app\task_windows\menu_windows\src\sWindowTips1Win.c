/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	TIP1_STRING_ID=0,
	TIP1_TIPS_ID,
};
UNUSED ALIGNED(4) const widgetCreateInfor tips1Win[] =
{
#if 1//USER_UI_MENU_ROUNDRECT == 0
	createFrameWin(					Rx(70),	Ry(50), Rw(180),Rh(140),R_ID_PALETTE_Gray_SUB_SEL,		WIN_ABS_POS),
#else
	createFrameRoundRimWin(					Rx(70),	<PERSON>y(50), <PERSON>w(180),<PERSON>h(140),R_ID_PALETTE_TransBlack,R_ID_PALETTE_TransBlack,WIN_ABS_POS, ROUND_ALL),
#endif
	createStringIcon(TIP1_TIPS_ID,	Rx(0),	Ry(0), 	Rw(180),Rh(45),	R_ID_STR_SET_PROMT,	ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	createStringIcon(TIP1_STRING_ID,Rx(0),	Ry(45), Rw(180),Rh(95),	" ",				ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	widgetEnd(),
};



