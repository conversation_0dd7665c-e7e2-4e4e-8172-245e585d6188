/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	TIP_STRING_ID=0,
};
UNUSED ALIGNED(4) const widgetCreateInfor tipsWin[] =
{
#if 1//USER_UI_MENU_ROUNDRECT == 0
	createFrameWin(					Rx(70),	Ry(60), Rw(180), Rh(120),	R_ID_PALETTE_Gray,	WIN_ABS_POS),
#else
	createFrameRoundRimWin(				Rx(70),	<PERSON>y(60), Rw(180), <PERSON>h(120),	R_ID_PALETTE_Blue,	R_ID_PALETTE_Blue, WIN_ABS_POS, ROUND_ALL),
#endif
	
	createStringIcon(TIP_STRING_ID,	Rx(0),	Ry(0), 	Rw(180), Rh(120),	" ",					ALIGNMENT_CENTER, R_ID_PALETTE_White,DEFAULT_FONT),
	widgetEnd(),
};



