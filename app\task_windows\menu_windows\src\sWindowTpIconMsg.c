/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "sWindowTpIconWin.c"

ALIGNED(4) static u32 TpIconResId;
ALIGNED(4) static u32 continueTime = 0xffffffff;

/*******************************************************************************
* Function Name  : TpIconSysMsg500Ms
* Description    : TpIconSysMsg500Ms
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int TpIconSysMsg500Ms(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_500MS);
	return 0;
}
/*******************************************************************************
* Function Name  : TpIcon1SysMsg1S
* Description    : TpIcon1SysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int TpIconSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_1S);
	if(continueTime)
		continueTime--;
	if(continueTime == 0)
		uiWinDestroy(&handle);
	return 0;
}
/*******************************************************************************
* Function Name  : TpIconSysMsg1S
* Description    : TpIconSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int TpIconSysMsgTimeUpdate(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_TIME_UPDATE);
	return 0;
}
/*******************************************************************************
* Function Name  : TpIconSysMsg500Ms
* Description    : TpIconSysMsg500Ms
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int TpIconSysMsgRecord(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_RECORD);
	return 0;
}
/*******************************************************************************
* Function Name  : TpIconSysMsg500Ms
* Description    : TpIconSysMsg500Ms
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int TpIconSysMsgPlay(winHandle handle,u32 parameNum,u32* parame)
{
	uiParentDealMsg(handle,SYS_EVENT_PLAY);
	return 0;
}
/*******************************************************************************
* Function Name  : TpIcon1OpenWin
* Description    : TpIcon1OpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int TpIconOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum < 2)
		return 0;
	TpIconResId 		= parame[0];
	continueTime	= parame[1];
	//uiWinSetResid(winItem(handle,TIP_STRING_ID),TpIconResId);
	deg_Printf("[WIN]TpIconOpenWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : TpIconCloseWin
* Description    : TpIconCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int TpIconCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]TpIconCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : TpIconWinChildClose
* Description    : TpIconWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int: 
*******************************************************************************/
static int TpIconWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]TpIconWinChildClose\n");
	return 0;
}


ALIGNED(4) msgDealInfor TpIconMsgDeal[]=
{
	{SYS_OPEN_WINDOW,			TpIconOpenWin},
	{SYS_CLOSE_WINDOW,			TpIconCloseWin},
	{SYS_CHILE_COLSE,			TpIconWinChildClose},
	/*{KEY_EVENT_OK,				TpIconKeyMsgAll},
	{KEY_EVENT_UP,				TpIconKeyMsgAll},
	{KEY_EVENT_DOWN,			TpIconKeyMsgAll},
	{KEY_EVENT_UVC_FORM,		TpIconKeyMsgAll},
	{KEY_EVENT_UVC_FRAME,		TpIconKeyMsgAll},
	{KEY_EVENT_ROTATE_ADD,		TpIconKeyMsgAll},
	{KEY_EVENT_ROTATE_DEC,		TpIconKeyMsgAll},
	{KEY_EVENT_POWER,	  		TpIconKeyMsgAll},
	{KEY_EVENT_MENU,			TpIconKeyMsgAll},
	{KEY_EVENT_MODE,			TpIconKeyMsgAll},
	{SYS_EVENT_SDC,				TpIconSysMsgSD},
	{SYS_EVENT_USBDEV,			TpIconSysMsgUSB},
	{SYS_EVENT_USBHOST,			TpIconSysMsgUSBHost},
	{SYS_EVENT_BAT,				TpIconSysMsgBattery},*/
	{SYS_EVENT_500MS,			TpIconSysMsg500Ms},
	{SYS_EVENT_1S,				TpIconSysMsg1S},
	{SYS_EVENT_TIME_UPDATE,		TpIconSysMsgTimeUpdate},
	{SYS_EVENT_RECORD,			TpIconSysMsgRecord},
	{SYS_EVENT_PLAY,			TpIconSysMsgPlay},	
	{EVENT_MAX,NULL},
};

MULTIWIN(takephotoiconWindow,TpIconMsgDeal,TpIconWin)



