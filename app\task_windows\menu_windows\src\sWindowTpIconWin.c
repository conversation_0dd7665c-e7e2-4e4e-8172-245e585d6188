/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	TP_ICON_ID=0,
};
UNUSED ALIGNED(4) const widgetCreateInfor TpIconWin[] =
{
#if 1//USER_UI_MENU_ROUNDRECT == 0
	createFrameWin(					Rx(144),	Ry(104), Rw(48), Rh(32),	R_ID_PALETTE_Transparent,	WIN_ABS_POS),
#else
	createFrameRoundRimWin(				Rx(70),	<PERSON>y(60), <PERSON>w(180), <PERSON>h(120),	R_ID_PALETTE_Blue,	R_ID_PALETTE_Blue, WIN_ABS_POS, ROUND_ALL),
#endif
	createImageIcon(TP_ICON_ID, Rx(0), Ry(0), Rw(48),  Rh(32), R_ID_ICON_CG_PHOTO_SUCCESS,ALIGNMENT_CENTER),
	widgetEnd(),
};



