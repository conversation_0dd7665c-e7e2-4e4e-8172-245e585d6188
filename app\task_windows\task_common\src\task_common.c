/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

typedef struct TASK_COMMON_PARA_S{
	u32 sreen_mtime;
	u32 poweroff_mtime;
	u32 ir_led_cnt;
	u32 sdc_check_time;
	u32 sdc_scan_flag;		//BIT(1):scan fs, BIT(0):wait stable
	u32 bat_check_cnt;
	u32 usbhost_check_time;
	u32 tp_check_time;
	u32 tp_check_interval;
	u32 tp_quickResponse;
	u32 tp_moveth;
}TASK_COMMON_PARA_T;

ALIGNED(4) static TASK_COMMON_PARA_T tComPara;
/*******************************************************************************
* Function Name  : task_com_para_init
* Description    : task com para init
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_para_init(void)
{
	tComPara.sreen_mtime 		= XOSTimeGet();
	tComPara.poweroff_mtime 	= XOSTimeGet();
	tComPara.ir_led_cnt			= 100;
	tComPara.sdc_check_time 	= 0;
	tComPara.sdc_scan_flag  	= 0;
	tComPara.bat_check_cnt		= 0;
	tComPara.usbhost_check_time = 0;
	tComPara.tp_check_time      = 0;
	tComPara.tp_check_interval  = 20;
	tComPara.tp_quickResponse   = 0;
	tComPara.tp_moveth          = (tComPara.tp_quickResponse)?100:8;
}
/*******************************************************************************
* Function Name  : task_com_key_check
* Description    : key check value
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_key_check(void)
{
	if(SysCtrl.dev_fd_key < 0)
		return;
	int adcValue;
	if(dev_ioctrl(SysCtrl.dev_fd_key, DEV_KEY_AD_READ, (INT32U )&adcValue)>=0)
	{
		if(adcValue) 
		{
			if(SysCtrl.dev_fd_lcd >= 0 && SysCtrl.dev_stat_lcd == 0 && KEY_EVENT_POWER != getType(adcValue))
			{
				task_com_sreen_check(SREEN_RESET_AUTOOFF);
			}else{
				XMsgQPost(SysCtrl.sysQ,(void*)adcValue);
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_usbhost_check
* Description    : usb host check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_usbhost_set(u32 stat)
{
	int ret;
	if(SysCtrl.dev_husb_stat != stat)
	{
		//SysCtrl.dev_stat_power &= ~POWERON_FLAG_WAIT;
		//SysCtrl.dev_husb_stat = stat;
		switch(stat)
		{
			case USBHOST_STAT_NULL: 
			case USBHOST_STAT_OUT: 
				dev_ioctrl(SysCtrl.dev_fd_husb,DEV_HUSB_POWER_CTRL, HUSB_POWER_OFF);
				SysCtrl.dev_husb_ch	  = USBNONE_CH;
				//SysCtrl.rec_show_time = 0;
				if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW || SysCtrl.dev_husb_stat == USBHOST_STAT_ASTERN)
				{
					XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBHOST,USBHOST_STAT_NULL));
					SysCtrl.rec_show_time = 0;
					deg_Printf("[COM] usbhost out:%d\n",stat);
				}
				break;
			case USBHOST_STAT_SOFT_STOP:
				SysCtrl.dev_husb_stat_bak = SysCtrl.dev_husb_stat;
				husb_api_usensor_stop_fill(1);
				break;
			case USBHOST_STAT_SOFT_RESUME:
				if(SysCtrl.dev_husb_stat == USBHOST_STAT_SOFT_STOP)
				{
					stat = SysCtrl.dev_husb_stat_bak;
					husb_api_usensor_stop_fill(0);			
				}
				break;		
			case USBHOST_STAT_WAIT_STABLE:
				//deg_Printf("[COM] USB HOST WAIT STABLE\n");
				break;
			case USBHOST_STAT_PWR_ON:
				dev_ioctrl(SysCtrl.dev_fd_husb,DEV_HUSB_POWER_CTRL, HUSB_POWER_ON);
				break;
			case USBHOST_STAT_IN:
				ret = dev_ioctrl(SysCtrl.dev_fd_husb,DEV_HUSB_TYPE_CHECK, 0);
				if(ret > 0)
				{
					dev_ioctrl(SysCtrl.dev_fd_husb, DEV_HUSB_INIT, ret);
					stat = USBHOST_STAT_IN;
					SysCtrl.dev_husb_ch = ret;
					deg_Printf("[COM] USBHOST IN:%d\n",SysCtrl.dev_husb_ch);
					XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBHOST,USBHOST_STAT_IN));
					if(SysCtrl.dev_stat_power & POWERON_FLAG_FIRST)
					{
						SysCtrl.dev_stat_power |= POWERON_FLAG_WAIT;
					}
					if(hardware_setup.usb_host_double_bond  && SysCtrl.dev_dusb_stat >= USBDEV_STAT_DEVIN)
					{
						SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
					}

				}else
				{
					dev_ioctrl(SysCtrl.dev_fd_husb, DEV_HUSB_POWER_CTRL, HUSB_POWER_OFF);
					stat = USBHOST_STAT_NULL;
					SysCtrl.dev_husb_ch	  = USBNONE_CH;
				}
				break;
			case USBHOST_STAT_SHOW:
				SysCtrl.rec_show_time = 0;
				deg_Printf("[COM] USBHOST SHOW ON:%d\n",SysCtrl.dev_husb_ch);
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBHOST,USBHOST_STAT_SHOW));
//				dev_ioctrl(SysCtrl.dev_fd_led_pwm, DEV_LED_PWM_ADJUST, LED_LEVEL_3_DUTY);//插摄像头灯PWM不变
				dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,1);
				break;
			case USBHOST_STAT_ASTERN:
				deg_Printf("[COM] USBHOST ASTERN ON:%d\n",SysCtrl.dev_husb_ch);
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBHOST,USBHOST_STAT_ASTERN));
				break;
			case USBHOST_STAT_SHOWOFF:
				deg_Printf("[COM] USBHOST SHOW OFF:%d\n",SysCtrl.dev_husb_ch);
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBHOST,USBHOST_STAT_NULL));
			default:break;
		}
		SysCtrl.dev_husb_stat = stat;

	}
	tComPara.usbhost_check_time = XOSTimeGet();
}
/*******************************************************************************
* Function Name  : task_com_usbhost_check
* Description    : usb host check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_usbhost_check(void)
{
	//static u32 lastTime = 0;
	//if((XOSTimeGet()-lastTime) <= 200)
	//	return ;

	//lastTime = XOSTimeGet();

	u32 temp = 0;
	int ret = dev_ioctrl(SysCtrl.dev_fd_husb, DEV_HUSB_DET_CHECK, (u32)&temp);
	if(ret >= 0)
	{
		//temp = 1;
		//deg_Printf("SysCtrl.dev_husb_stat:%d, temp:%d\n",SysCtrl.dev_husb_stat, temp);
		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SOFT_STOP)
		{
			task_com_usbhost_set(USBHOST_STAT_SOFT_STOP);
		}
		else if((SysCtrl.dev_husb_stat == USBHOST_STAT_OUT) || ((temp == 0)&& (SysCtrl.dev_husb_stat != USBHOST_STAT_NULL)))
		{
			if(SysCtrl.dev_husb_stat == USBHOST_STAT_OUT)
			{
				//SysCtrl.dev_husb_stat = USBHOST_STAT_MAX;
				task_com_usbhost_set(USBHOST_STAT_OUT);
			}else
			{
				//deg_Printf("SysCtrl.dev_husb_stat:%d\n",SysCtrl.dev_husb_stat);
				task_com_usbhost_set(USBHOST_STAT_NULL);
			}
		}else if(temp > 0) //HUSB DET
		{
			if(SysCtrl.dev_husb_stat < USBHOST_STAT_IN)
			{
				if(tComPara.usbhost_check_time == 0)	//power on det
				{
					task_com_usbhost_set(USBHOST_STAT_PWR_ON);
					XOSTimeDly(200);
					task_com_usbhost_set(USBHOST_STAT_IN);
				}else
				{
					switch(SysCtrl.dev_husb_stat)
					{
						case USBHOST_STAT_NULL: task_com_usbhost_set(USBHOST_STAT_WAIT_STABLE); return;
						case USBHOST_STAT_WAIT_STABLE:
							if(tComPara.usbhost_check_time + 100 > XOSTimeGet())
								return;
							task_com_usbhost_set(USBHOST_STAT_PWR_ON); return;
						case USBHOST_STAT_PWR_ON:
							if(tComPara.usbhost_check_time + 100 > XOSTimeGet())
								return;
							task_com_usbhost_set(USBHOST_STAT_IN); return;
					}
				}
			}else
			{
				husb_api_usensor_linkingLcd();	
				u32 dev_sta = husb_api_devicesta(SysCtrl.dev_husb_ch);
				//if(dev_sta)
					//deg_Printf("dev_sta:%x\n",dev_sta);
				if(dev_sta == USB_NONE)
				{
					//deg_Printf("dev_sta:%x\n",dev_sta);
					task_com_usbhost_set(USBHOST_STAT_NULL);
				}else if(dev_sta & USB_UVC_SWITCH)
				{
					task_com_usbhost_set(USBHOST_STAT_SHOWOFF);
					husb_api_devicesta_set(SysCtrl.dev_husb_ch, 0, USB_UVC_SWITCH);
				}if((dev_sta & (USB_UVC_TRAN|USB_ASTERN)) == USB_UVC_TRAN)
				{
					task_com_usbhost_set(USBHOST_STAT_SHOW);
				}else if((dev_sta & (USB_UVC_TRAN|USB_ASTERN)) == (USB_UVC_TRAN|USB_ASTERN))
				{
					task_com_usbhost_set(USBHOST_STAT_ASTERN);
				}
				
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_usbhost_check
* Description    : usb host check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_usbdev_set(u32 stat)
{
	if(SysCtrl.dev_dusb_stat != stat)
	{
		u32 temp = 0;
		u32 dm_status = 0;
		u32 dp_status = 0;
		SysCtrl.dev_dusb_stat = stat;
		switch(stat)
		{
			case USBDEV_STAT_NULL: 
				dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_ONLINE_SET, 0); //set offline
				tComPara.bat_check_cnt 	  = 5;// wait stable
				SysCtrl.dev_stat_battery  	= BATTERY_STAT_MAX;
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_NULL));
				deg_Printf("[COM] usbdev out\n");
				break;
			case USBDEV_STAT_DCIN:
				//deg_Printf("[COM] usbdev %d\n",SysCtrl.dev_husb_stat);
				SysCtrl.dev_stat_battery  	= BATTERY_STAT_MAX;
				deg_Printf("[COM] usbdev in\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_DCIN));
				break;
			case USBDEV_STAT_DEVIN_CHECK:
				if(SysCtrl.dev_dusb_out == 0 && (hardware_setup.usb_host_double_bond == 0 || SysCtrl.dev_husb_stat == USBHOST_STAT_NULL ))
				{
					dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_HW_CON_CHECK, (u32)&temp);
					if(temp)
					{
						SysCtrl.dev_dusb_stat = USBDEV_STAT_DEVIN;
						if(hardware_setup.usb_host_double_bond)
							task_com_usbhost_set(USBHOST_STAT_OUT);
						dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_INIT, USB_DEVTYPE_MSC);
						XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_DEVIN));
					}else
					{
                        SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
					}
				}else
				{
					SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
					if(SysCtrl.start_judge_usb)
					{
						dm_status = hal_gpioRead(hardware_setup.usb_dm_ch, hardware_setup.usb_dm_pin);
						dp_status = hal_gpioRead(hardware_setup.usb_dp_ch, hardware_setup.usb_dp_pin);
						if((!dm_status&&!dp_status)&&(!SysCtrl.user_dev_in))
						{
							deg_Printf("Ready Enter pc\n");
							SysCtrl.user_dev_in = 1;
							task_com_USB_CS_DM_DP_status_select(0);
							dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_HW_CON_CHECK, (u32)&temp);
							deg_Printf("-----temp=%d-------\n",temp);
							if(temp)
							{
								SysCtrl.dev_dusb_stat = USBDEV_STAT_DEVIN;
								if(hardware_setup.usb_host_double_bond)
									task_com_usbhost_set(USBHOST_STAT_OUT);
								dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_INIT, USB_DEVTYPE_MSC);
								XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_DEVIN));
							}						
						}
					}
				}

				break;
			case USBDEV_STAT_PC:
				SysCtrl.dev_dusb_stat = USBDEV_STAT_PC;
				dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_ONLINE_SET, 1); //set online
				deg_Printf("[COM] usbdev PC\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_USBDEV,USBDEV_STAT_PC));
				break;
			default: return;
		}
		//SysCtrl.dev_dusb_stat = stat;
		
	}
}
/*******************************************************************************
* Function Name  : task_com_usbdev_check
* Description    : task_com_usbdev_check and bat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_usbdev_check(void)
{
	int ret, temp;
	//--------------------usb detect------------------------
	ret = dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_PWR_CHECK, (INT32U)&temp);
	if(ret>=0)
    {
		//deg_Printf("[USB DEV] SysCtrl.dev_dusb_stat:%d, %d\n", SysCtrl.dev_dusb_stat, SysCtrl.dev_husb_stat);
		if((temp == 0) && (SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)) // dc out
		{
			task_com_usbdev_set(USBDEV_STAT_NULL);
			if(SysCtrl.user_dev_in)
			{
				SysCtrl.user_dev_in = 0;
				SysCtrl.start_judge_usb = 1;
			}
		}
		else if(temp && (SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)) // dc in
		{
			task_com_usbdev_set(USBDEV_STAT_DCIN);
			task_com_usbdev_set(USBDEV_STAT_DEVIN_CHECK);
		}
		else if(temp && (SysCtrl.dev_dusb_stat == USBDEV_STAT_DCIN)) // dc in
		{
			//deg_Printf("temp:%d,SysCtrl.dev_dusb_stat:%d, %d\n",temp,SysCtrl.dev_dusb_stat, SysCtrl.dev_husb_stat);
			task_com_usbdev_set(USBDEV_STAT_DEVIN_CHECK);
		}
		else 
		{
			temp = 0;
			dev_ioctrl(SysCtrl.dev_fd_dusb, DEV_DUSB_SW_CON_CHECK, (INT32U)&temp);
			if((SysCtrl.dev_dusb_stat == USBDEV_STAT_DEVIN) && temp )
			{	
				task_com_usbdev_set(USBDEV_STAT_PC);
			}
		}
    }
//----------------------battery detect---------------------------------------	
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL) // only dc out check battery
	{
	    ret = dev_ioctrl(SysCtrl.dev_fd_battery,DEV_BATTERY_READ,(INT32U)&temp);  
		if(ret>=0)
		{
			if(SysCtrl.dev_stat_battery != temp && tComPara.bat_check_cnt == 0) // need battery stable
				tComPara.bat_check_cnt = 5;//  3;
			else if(SysCtrl.dev_stat_battery == temp)
			{
				tComPara.bat_check_cnt = 0;
				return ; // no need update
			}
			if(tComPara.bat_check_cnt >0)
				tComPara.bat_check_cnt--;
            
			if(tComPara.bat_check_cnt == 0)
			{
				if(temp == 0)
					SysCtrl.low_power_tips = 1;
//					app_taskStart(TASK_POWER_OFF,0);	
				if(SysCtrl.dev_stat_battery != BATTERY_STAT_MAX)		
				{
					//if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL) //不接DC，电池只下降
					{
						if(SysCtrl.dev_stat_battery <= temp)
						{
							return;
						}
					}
					//else //充电时，电池只上升
					//{
					//	if(SysCtrl.dev_stat_battery >= temp)
					//	{
					//		return;		
					//	}
					//}
				}
				SysCtrl.dev_stat_battery = temp;
								
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_BAT,SysCtrl.dev_stat_battery));
				deg_Printf("[COM]battery = %x\n",SysCtrl.dev_stat_battery);
				deg_Printf("[COM]battery = %x\n",SysCtrl.dev_stat_battery);
				deg_Printf("[COM]battery = %x\n",SysCtrl.dev_stat_battery);
				deg_Printf("[COM]battery = %x\n",SysCtrl.dev_stat_battery);
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_sdc_stat_set
* Description    : sd card stat set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_usb_dev_out(u32 out)
{
	SysCtrl.dev_dusb_out = out;
	if(out)
	{
		if(SysCtrl.dev_dusb_stat > USBDEV_STAT_DCIN)
		{
			SysCtrl.dev_dusb_stat = USBDEV_STAT_DCIN;
		} 
	}
}
/*******************************************************************************
* Function Name  : task_com_spijpg_Init
* Description    : APP LAYER: task_com_spijpg_Init
* Input          : 
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void task_com_spijpg_Init(u32 unit_force)
{
#if TASK_SCAN_FILE_EVERY_TIME
	if(unit_force || SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
	{
		if(SysCtrl.spi_jpg_list >= 0)
		{
			nv_jpg_uinit();
			filelist_api_nodedestory(SysCtrl.spi_jpg_list);
			SysCtrl.spi_jpg_list = -1;
		}
	}else
	{
		if(SysCtrl.spi_jpg_list < 0)
		{
			nv_jpg_init();
			SysCtrl.spi_jpg_list   = filelist_api_nodecreate(NULL,FILELIST_TYPE_SPI,-1);
			filelist_api_scan(SysCtrl.spi_jpg_list);
		}
	}
#endif
}
/*******************************************************************************
* Function Name  : task_com_sdlist_scan
* Description    : APP LAYER: task_com_spijpg_Init
* Input          : u8 unit_force: 1: \
				   u8 type: 0: jpg, 1:avi, 2: jpg + avi
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
void task_com_sdlist_scan(u8 unit_force, u8 type)
{
#if TASK_SCAN_FILE_EVERY_TIME
	u8 create;
	if(unit_force || (SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL && SysCtrl.dev_stat_sdc != SDC_STAT_FULL))
	{
		create = 0;
	}else
	{
		create = 1;
	}
	if(create)
	{
		if(SysCtrl.avi_list < 0) 
		{
			int list;
			if(type == 1)
			{
				list = SysCtrl.avi_list   = filelist_api_nodecreate(FILEDIR_REC,FILELIST_TYPE_AVI,-1);
			}else if(type == 0)
			{
				list = SysCtrl.jpg_list	= filelist_api_nodecreate(FILEDIR_IMG,  FILELIST_TYPE_JPG, -1);
			}else
			{
				list = SysCtrl.jpg_list	= filelist_api_nodecreate(FILEDIR_IMG,  FILELIST_TYPE_JPG,   -1);
				SysCtrl.avi_list   = filelist_api_nodecreate(FILEDIR_REC,FILELIST_TYPE_AVI,SysCtrl.jpg_list);
				
			}
			filelist_api_scan(list);
		}	

	}else
	{
		filelist_api_nodedestory(SysCtrl.avi_list);
		filelist_api_nodedestory(SysCtrl.jpg_list);
		SysCtrl.avi_list = -1;
		SysCtrl.jpg_list = -1;
	}
#endif
}

/*******************************************************************************
* Function Name  : task_com_sdc_stat_set
* Description    : sd card stat set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sdc_stat_set(u32 stat)
{
	if(SysCtrl.dev_stat_sdc != stat) 
	{
		SysCtrl.dev_stat_sdc = stat;	
		tComPara.sdc_check_time = XOSTimeGet();
		switch(stat)
		{
			case SDC_STAT_NULL: 
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_NULL));
				deg_Printf("[COM] : sdc out\n");
				break;
			case SDC_STAT_UNSTABLE:
				deg_Printf("[COM] : sdc wait stable\n");
				break;
			case SDC_STAT_IN:
				deg_Printf("[COM] : sdc in\n");
				break;
			case SDC_STAT_ERROR:
				deg_Printf("[COM] : sdc error\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_ERROR));
				break;
			case SDC_STAT_FULL:
				deg_Printf("[COM] : sdc full\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_FULL));
				break;
			case SDC_STAT_NORMAL:
				deg_Printf("[COM] : sdc normal\n");
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_NORMAL));
				break;
			default: break;
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_fs_scan
* Description    : fs scan
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_fs_scan(void)
{
	deg_Printf("[COM] : fs mount start.%d\n",XOSTimeGet());
	
	char string[16];
	
#if FUN_AUDIO_RECORD_EN
	hx330x_str_cpy(string, FILEDIR_WAV);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);
#endif

	hx330x_str_cpy(string, FILEDIR_REC);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);


	hx330x_str_cpy(string, FILEDIR_IMG);
	string[hx330x_str_len(string)-1] = 0;
	fs_mkdir((char*)string);

	sd_api_Stop();

	SysCtrl.fs_clustsize = fs_getclustersize();
	deg_Printf("[COM] : fs cluster size.%d B\n",SysCtrl.fs_clustsize);
	if(SysCtrl.spi_jpg_list >= 0)
	{
	#if TASK_SCAN_FILE_EVERY_TIME	
		nv_jpg_uinit();
	#endif
		filelist_api_nodedestory(SysCtrl.spi_jpg_list);
		SysCtrl.spi_jpg_list = -1;
	}
#if TASK_SCAN_FILE_EVERY_TIME == 0	
	if(SysCtrl.avi_list < 0) // scan file list
	{
		SysCtrl.avi_list 	= SysCtrl.avia_list = filelist_api_nodecreate(FILEDIR_REC,FILELIST_TYPE_AVI,-1);
		SysCtrl.jpg_list	= filelist_api_nodecreate(FILEDIR_IMG,  FILELIST_TYPE_JPG,                  SysCtrl.avi_list);
		filelist_api_scan(SysCtrl.avi_list);
	}
#endif
#if FUN_AUDIO_RECORD_EN
	if(SysCtrl.wav_list<0)
    {
		SysCtrl.wav_list = filelist_api_nodecreate(FILEDIR_WAV, FILELIST_TYPE_WAV,-1);
		filelist_api_scan(SysCtrl.wav_list);
    }
#endif
	task_com_sdc_stat_set(SDC_STAT_NORMAL);
	task_com_sdc_freesize_check();
	
	
	//task_video_record_caltime();

	sd_api_unlock();
		
}

/*******************************************************************************
* Function Name  : task_com_sdc_check
* Description    : sd card stat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_sdc_check(void)
{
	int temp,res;
    if(SysCtrl.dev_fd_sdc < 0)
		return;
	if(tComPara.sdc_scan_flag & BIT(1))
	{
		task_com_fs_scan();
		tComPara.sdc_scan_flag &= ~BIT(1);
	}
	dev_ioctrl(SysCtrl.dev_fd_sdc, DEV_SDCARD_READ, (INT32U)&temp);  //temp: 1 - sd online or lock, 0 - sd  offline 
	if(SysCtrl.dev_stat_sdc  <= SDC_STAT_UNSTABLE)
	{
		if(sd_api_init(hardware_setup.sdcard_bus_width)>=0)
		{
			if(!(tComPara.sdc_scan_flag & BIT(0)))
			{
				task_com_sdc_stat_set(SDC_STAT_IN);	
			}
			if(SysCtrl.dev_stat_sdc == SDC_STAT_NULL)
			{
				task_com_sdc_stat_set(SDC_STAT_UNSTABLE);
			}
			if(SysCtrl.dev_stat_sdc == SDC_STAT_UNSTABLE)
			{
				if(tComPara.sdc_check_time + 500 > XOSTimeGet())// 500 ms,wait sdc stable
					return;
				task_com_sdc_stat_set(SDC_STAT_IN);	
			}
			if(SysCtrl.dev_stat_sdc == SDC_STAT_IN)
			{
				res = fs_mount();
				if(res!=FR_OK)
				{
					sd_api_CardState_Set(SDC_STATE_NULL);
                    if(hardware_setup.sdcard_bus_width != SD_BUS_WIDTH1)
                    {
                        if(sd_api_init(SD_BUS_WIDTH1)>=0) // sdc intial 1line
                        {
                            res = fs_mount();
                        }
                    }
				}
				if(res<0)
				{
					task_com_sdc_stat_set(SDC_STAT_ERROR);	
				}
				else
				{
					tComPara.sdc_scan_flag |= BIT(1);
				}	
			}
			temp = 1;
		}
	}
	if(temp == 0) // no sdc dectcted
	{
		if(SysCtrl.dev_stat_sdc >= SDC_STAT_ERROR && SysCtrl.dev_stat_sdc <= SDC_STAT_NORMAL)
		{
			fs_nodeinit();  // initial fs node
			filelist_api_nodedestory(SysCtrl.avi_list);
			filelist_api_nodedestory(SysCtrl.avia_list);
			filelist_api_nodedestory(SysCtrl.avib_list);
			filelist_api_nodedestory(SysCtrl.jpg_list);
		#if (FUN_AUDIO_RECORD_EN == 1)
			filelist_api_nodedestory(SysCtrl.wav_list);
		#endif
			SysCtrl.avi_list 	= -1;
			SysCtrl.avia_list 	= -1;
			SysCtrl.avib_list 	= -1;
			SysCtrl.jpg_list 	= -1;
			SysCtrl.wav_list 	= -1;
			SysCtrl.mp3_list	= -1;
			SysCtrl.nes_list	= -1;
		#if TASK_SCAN_FILE_EVERY_TIME == 0
			SysCtrl.spi_jpg_list = filelist_api_nodecreate(NULL,FILELIST_TYPE_SPI,-1);
			filelist_api_scan(SysCtrl.spi_jpg_list);
		#endif
			task_com_sdc_stat_set(SDC_STAT_NULL);
			task_com_sdc_freesize_check();
		}	
			
		tComPara.sdc_scan_flag |= BIT(0);
	}
	
}
/*******************************************************************************
* Function Name  : task_common_gsensor_check
* Description    : gsensor stat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_common_gsensor_check(void) // 10 ms
{
	int temp,ret;
	if((app_taskCurId() != TASK_RECORD_VIDEO))
		return;
	if((SysCtrl.dev_fd_gsensor > 0) && user_configValue2Int(CONFIG_ID_GSENSOR))
	{
		if(videoRecordGetStatus() == MEDIA_STAT_START)	// recording will check Gsensor
		{
			ret = dev_ioctrl(SysCtrl.dev_fd_gsensor,DEV_GSENSOR_LOCK_READ,(INT32U)&temp);
			if((ret >=0) && temp > 0)
			{
				XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_LOCK));
				deg_Printf("[COM].gsensor active\n");
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_lcdbk_set
* Description    : lcd sreen on/off
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_lcdbk_set(u32 on)
{
	if(SysCtrl.dev_fd_lcd < 0)
		return;
	if(SysCtrl.dev_stat_lcd != on)
	{
		dev_ioctrl(SysCtrl.dev_fd_lcd,DEV_LCD_BK_WRITE,on);   // screen on
		SysCtrl.dev_stat_lcd = on;
	}
	tComPara.sreen_mtime = XOSTimeGet();  //save sreen on time
}
/*******************************************************************************
* Function Name  : task_com_sreen_save
* Description    : screen save check
* Input          : u32 on: COM_SREEN_OP 0x00: check auto off, 0x01: reset auto off, 0x02:sreen off, 0x03 sreen on
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sreen_check(u32 on)
{
	if(SysCtrl.dev_fd_lcd < 0 || app_taskCurId() == TASK_POWER_OFF)
		return;
	//deg_Printf("task_com_sreen_check:%d\n", on);
	if(SysCtrl.dev_husb_stat == USBHOST_STAT_ASTERN || (on == SREEN_RESET_AUTOOFF)||(on == SREEN_SET_ON) ) //0x01: reset auto off, 0x03 sreen on
	{
		task_com_lcdbk_set(1);
	}else if(on == SREEN_SET_OFF)   //0x02:sreen off
	{
		task_com_lcdbk_set(0);
	}else //0x00: check auto off SREEN_CHECK_AUTOOFF
	{
		u32 sreensave = user_configValue2Int(CONFIG_ID_SCREENSAVE)*1000;
		if(sreensave != 0)
		{
			if(tComPara.sreen_mtime + sreensave < XOSTimeGet())
			{
				//deg_Printf("tComPara.sreen_mtime:%d,%d,%d\n",tComPara.sreen_mtime,XOSTimeGet(),sreensave);
				task_com_lcdbk_set(0);
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_auto_poweroff
* Description    : system auto power off check
* Input          : int reset : 1:reset auto poweroff, 0:no reset
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_auto_poweroff(int reset)
{
	if(app_taskCurId() == TASK_POWER_OFF)
		return;
	if(reset)
	{
		tComPara.poweroff_mtime = XOSTimeGet();
	}else
	{
		u32 autopowerofftime = user_configValue2Int(CONFIG_ID_AUTOOFF)*1000;
		if(autopowerofftime != 0)
		{
			if(tComPara.poweroff_mtime + autopowerofftime <= XOSTimeGet())
			{
				deg_Printf("[COM]Auto power off\n");
				app_taskStart(TASK_POWER_OFF,0);
				tComPara.poweroff_mtime = XOSTimeGet();
			}
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_keysound_play
* Description    : task_com_keysound_play
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_keysound_play(void)
{
	if( (SysCtrl.dev_stat_keysound == 0) || // key sound off
		(audioPlaybackGetStatus() == MEDIA_STAT_PLAY)||// video/audio playing
		(app_taskCurId()  == TASK_POWER_OFF))
	{
		return;
	}
	res_keysound_play();		
}
/*******************************************************************************
* Function Name  : task_com_sound_wait_end
* Description    : task_com_sound_wait_end
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_sound_wait_end(void)
{
	while(audioPlaybackGetStatus() == MEDIA_STAT_PLAY)
	{
		XOSTimeDly(1);
	}
}
/*******************************************************************************
* Function Name  : task_com_sdc_freesize_check
* Description    : com get fs free size
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
int task_com_sdc_freesize_check(void)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL)
	{
		SysCtrl.sdc_freesize = fs_free_size()>>1;  // kb
		if(SysCtrl.sdc_freesize < 32)
		{
			SysCtrl.sdc_freesize  = 0;
			SysCtrl.dev_stat_sdc = SDC_STAT_FULL; 
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_SDC,SDC_STAT_FULL));
			deg_Printf("[COM] : sdc normal but free space is 0.set sdc error\n");  // user need format sdc
		}
	}
	else
		SysCtrl.sdc_freesize = 0;
	deg_Printf("[COM] : fs free size = %dG%dM%dKB\n",SysCtrl.sdc_freesize>>20,(SysCtrl.sdc_freesize>>10)&0x3ff,(SysCtrl.sdc_freesize)&0x3ff);
	return SysCtrl.sdc_freesize;
}
/*******************************************************************************
* Function Name  : task_com_sdc_freesize_modify
* Description    : com dec size from free size
* Input          : INT8S dec: >=0 add freesize, <0 minus freesize
*                  INT32U size : unit byte
* Output         : None
* Return         : None
*******************************************************************************/
int task_com_sdc_freesize_modify(INT8S dec,INT32U size)
{
	if(size&(SysCtrl.fs_clustsize-1))
	{
		size = (size&(~(SysCtrl.fs_clustsize-1)))+SysCtrl.fs_clustsize;
		//size+=1024;
	}
	size>>=10;
	if(dec<0)
	{
		if(SysCtrl.sdc_freesize>size)
			SysCtrl.sdc_freesize-=size;
		else
			SysCtrl.sdc_freesize = 0;
	}
	else
	{
		SysCtrl.sdc_freesize+=size;
	}
	return SysCtrl.sdc_freesize;
}
/*******************************************************************************
* Function Name  : task_com_ir_set
* Description    : task_com_ir_set
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_ir_set(u32 on)
{
	if(SysCtrl.dev_fd_ir >= 0)
	{
		if(SysCtrl.dev_stat_ir != on)
		{
			dev_ioctrl(SysCtrl.dev_fd_ir,DEV_IR_WRITE,on);
			SysCtrl.dev_stat_ir = on;
		}
	}
}
/*******************************************************************************
* Function Name  : task_com_ir_auto_check
* Description    : gsensor stat check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_ir_auto_check(void)
{
	if(SysCtrl.dev_fd_ir >= 0)
	{
		if(user_config_get(CONFIG_ID_IR_LED) != R_ID_STR_IR_AUTO)
			return;	
		if(hal_isp_br_get() < 0xa)		// need ir
		{
			task_com_ir_set(1);
		}	
		else if(hal_isp_br_get() > 0x30)
		{
			tComPara.ir_led_cnt--;
			if(tComPara.ir_led_cnt == 0)// wait for stable
			{
				task_com_ir_set(0);
				tComPara.ir_led_cnt =100;
			}
		}		
	}
}

/*******************************************************************************
* Function Name  : task_com_md_check
* Description    : motion detect  check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_md_check(void)
{
	if(user_config_get(CONFIG_ID_MOTIONDECTION) == R_ID_STR_COM_ON)
	{
		if(hal_mdCheck())
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_MD,1));
	}
}
/*******************************************************************************
* Function Name  : task_com_tp_check
* Description    : motion detect  check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
static void task_com_tp_check(void)
{

	static u16 lastTpType  = TP_TYPE_NONE;
	TOUCHPANEL_INFO_T *tp_info;
	uiRect	rect;
	if(SysCtrl.dev_fd_tp < 0 || app_taskCurId() == TASK_POWER_OFF)
		return;
	if((XOSTimeGet() - tComPara.tp_check_time) <= tComPara.tp_check_interval)
		return ;
	tComPara.tp_check_time = XOSTimeGet();
	tp_info = (TOUCHPANEL_INFO_T*)dev_ioctrl(SysCtrl.dev_fd_tp, DEV_TOUCHPANEL_READ,(INT32U)tComPara.tp_moveth);
	if(tp_info == NULL)
		return;
	if(tp_info->tp_type == TP_TYPE_PRESS)
	{
		if(tComPara.tp_quickResponse && lastTpType != TP_TYPE_PRESS)
		{
			rect.x0 = (tp_info->x < 2) ? 0 : (tp_info->x - 1);
			rect.y0 = (tp_info->y < 2) ? 0 : (tp_info->y - 1);
			rect.x1 = ((tp_info->x + 1) >= USER_UI_WIDTH) ? (USER_UI_WIDTH - 1) : (tp_info->x + 1);
			rect.y1 = ((tp_info->y + 1) >= USER_UI_HEIGHT) ? (USER_UI_HEIGHT - 1) : (tp_info->y + 1);
			if(uiWinTouchProcess(&rect,TOUCH_PRESS))
			{
				if (!((app_taskCurId() == TASK_PLAY_AUDIO && audioPlaybackGetStatus() != MEDIA_STAT_STOP ) ||
					(app_taskCurId() == TASK_PLAY_VIDEO && videoPlaybackGetStatus() != MEDIA_STAT_STOP)))
				{
					task_com_keysound_play();
				}
			}
		}
		tComPara.tp_check_interval = 60;
	}else if(tp_info->tp_type == TP_TYPE_MOVE)
	{
		if(tp_info->tp_speed > 0 && tComPara.tp_check_interval != 60)
		{
			XMsgQPost(SysCtrl.sysQ,makeMSG(SYS_TOUCH_SLIDE_ON,tp_info->tp_dir));
		}
		if(tComPara.tp_check_interval == 60)
			tComPara.tp_check_interval = 140;
		else
			tComPara.tp_check_interval = 200;
	}else if(tp_info->tp_type == TP_TYPE_NONE)
	{
		if(lastTpType == TP_TYPE_PRESS)
		{
			if(tComPara.tp_quickResponse == 0)
			{
				rect.x0 = (tp_info->x < 2) ? 0 : (tp_info->x - 1);
				rect.y0 = (tp_info->y < 2) ? 0 : (tp_info->y - 1);
				rect.x1 = ((tp_info->x + 1) >= USER_UI_WIDTH) ? (USER_UI_WIDTH - 1) : (tp_info->x + 1);
				rect.y1 = ((tp_info->y + 1) >= USER_UI_HEIGHT) ? (USER_UI_HEIGHT - 1) : (tp_info->y + 1);
				if(uiWinTouchProcess(&rect,TOUCH_PRESS))
				{
					if (!((app_taskCurId() == TASK_PLAY_AUDIO && audioPlaybackGetStatus() != MEDIA_STAT_STOP ) ||
						(app_taskCurId() == TASK_PLAY_VIDEO && videoPlaybackGetStatus() != MEDIA_STAT_STOP)))
					{
						task_com_keysound_play();
					}
				}
				uiWinTouchProcess(NULL,TOUCH_RELEASE);
			}else
			{
				uiWinTouchProcess(NULL,TOUCH_RELEASE);
			}
		}else if(lastTpType == TP_TYPE_MOVE)
		{
			if(tComPara.tp_quickResponse)
			{
				uiWinTouchProcess(NULL,TOUCH_OVER);
			}
			if(tp_info->tp_speed > 0)
			{
				XMsgQPost(SysCtrl.sysQ,makeMSG(SYS_TOUCH_SLIDE_OFF,tp_info->tp_dir));
			}
		}
		if(videoRecordGetStatus() == MEDIA_STAT_START)
		{
			tComPara.tp_check_interval = 60;
		}else
		{
			tComPara.tp_check_interval = 30;
		}
	}
	lastTpType = tp_info->tp_type;
}
/*******************************************************************************
* Function Name  : task_com_md_check
* Description    : motion detect  check
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/

static void task_com_sec(void)
{
	static u8 lastSec		= 255;
	static uint32 lastTime 	= 0;
	HAL_RTC_T* rtcTime = hal_rtcTimeGet();
	if(lastSec != rtcTime->sec)
	{
		lastSec = rtcTime->sec;
		SysCtrl.powerOnTime++;
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_1S,0));
		// task_com_temperature_check();
		res_iconBuffTimeUpdate();
	}
	if((XOSTimeGet()-lastTime)<=500)
		return;
	lastTime = XOSTimeGet();
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_500MS,0));
}


static void task_com_100ms(void)
{
	static uint32 lastTime 	= 0;
	if((XOSTimeGet()-lastTime)<=100)
		return;
	lastTime = XOSTimeGet();
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_100MS,0));
	//  temperature_calc();

}

// static void temperature_calc()
// {
// 	static u8 i = 0;
// 	int temperaue_adc_value;
// 	static int temperaue_sum;
// 		temperaue_adc_value = hal_adcGetChannel(hardware_setup.tmp_adc_pos);
// 		temperaue_sum += Get_Kelvin_Temperaure(ADvalue_change_Rntc(temperaue_adc_value));
// 		///deg_Printf("temperaue_sum=%d\r\n",temperaue_sum);
// 	i++;
// 	if(i>29)
// 	{
// 		i = 0;
// 		SysCtrl.temperaue_value = temperaue_sum/30;
// 		deg_Printf("SysCtrl.temperaue_value00=%d\r\n",SysCtrl.temperaue_value);
// 		temperaue_sum = 0;
// 	}
// };

/*******************************************************************************
* Function Name  : task_com_powerOnTime_str
* Description    : task_com_powerOnTime_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_powerOnTime_str(void)
{
	u32 sec = SysCtrl.powerOnTime;
	static char powerOnTimeStr[]= "00:00";
	hx330x_num2str(powerOnTimeStr, sec/3600, 2);
	powerOnTimeStr[2] = ':';
	hx330x_num2str(&powerOnTimeStr[3], (sec%3600)/60, 2);
	
	return powerOnTimeStr;
}
/*******************************************************************************
* Function Name  : task_com_sensor_res_str
* Description    : task_com_sensor_res_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_sensor_res_str(void)
{
	static char usensor_resolution_str[]= "MJP 1280X720 ";
	
	if(!husb_api_usensor_atech_sta())
	{
		usensor_resolution_str[0] = ' ';
		usensor_resolution_str[1] = 0;
	}else
	{
		u16 width,height, cnt;
		if(husb_api_usensor_res_type_is_mjp())
		{
			hx330x_str_cpy(usensor_resolution_str,(char *)"MJP");
		}
		else
		{
			hx330x_str_cpy(usensor_resolution_str,(char *)"YUV");
		}
		usensor_resolution_str[3] = ' ';
		husb_api_usensor_res_get(&width, &height);
		cnt = hx330x_num2str_cnt(&usensor_resolution_str[4], width, 4);
		usensor_resolution_str[4 + cnt] = 'x';
		hx330x_num2str_cnt(&usensor_resolution_str[4 + cnt + 1], height, 4);
	}
	return usensor_resolution_str;
}
/*******************************************************************************
* Function Name  : task_com_rec_show_time_str
* Description    : task_com_rec_show_time_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_rec_show_time_str(void)
{
	u32 sec = SysCtrl.rec_show_time;
	static char recTimeStr[]="00:00:00";
	hx330x_num2str(recTimeStr, sec/3600, 2); //hour
	recTimeStr[2] = ':';
	hx330x_num2str(&recTimeStr[3], (sec%3600)/60, 2); //min
	recTimeStr[5] = ':';
	hx330x_num2str(&recTimeStr[6], sec%60, 2); //sec
	
	return recTimeStr;
}
/*******************************************************************************
* Function Name  : task_com_rec_show_time_str
* Description    : task_com_rec_show_time_str
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
char * task_com_rec_remain_time_str(void)
{
	static char recRemainTimeStr[]="00:00:00";
	app_taskRecordVideo_caltime();
	u32 sec = SysCtrl.rec_remain_time;
	hx330x_num2str(recRemainTimeStr, sec/3600, 2); //hour
	recRemainTimeStr[2] = ':';
	hx330x_num2str(&recRemainTimeStr[3], (sec%3600)/60, 2); //min
	recRemainTimeStr[5] = ':';
	hx330x_num2str(&recRemainTimeStr[6], sec%60, 2); //sec
	
	
	return recRemainTimeStr;
}
/*******************************************************************************
* Function Name  : task_com_play_time_str
* Description    : task_com_play_time_str
* Input          : int sel: 1: total time, 0: cur play time
* Output         : None
* Return         : None
*******************************************************************************/
// char * task_com_play_time_str(int sel)
// {	
// 	char *str;
// 	static char playCurTimeStr[] = {"00:00:00"};
// 	static char playTotalTimeStr[] = {"00:00:00"};
// 	u32 sec;
// 	u32 playtime  = SysCtrl.play_cur_time/1000;
// 	u32 totaltime = SysCtrl.play_total_time;

// 	if(totaltime%1000 > 500)
// 		totaltime = totaltime/1000 + 1;
// 	else 
// 		totaltime = totaltime/1000;
// 	if(sel)
// 	{
// 		sec = totaltime;
// 		str = playTotalTimeStr;
// 	}
// 	else
// 	{
// 		sec = playtime;
// 		str = playCurTimeStr;
// 	}
// 	if(sec/3600)
// 	{
// 		hx330x_num2str(str, sec/3600, 2); //hour
// 		str[2] = ':';
// 		hx330x_num2str(&str[3], (sec%3600)/60, 2); //min
// 		str[5] = ':';
// 		hx330x_num2str(&str[6], sec%60, 2); //sec		
// 	}else
// 	{
// 		hx330x_num2str(str, (sec%3600)/60, 2); //min
// 		str[2] = ':';
// 		hx330x_num2str(&str[3], sec%60, 2); //sec		
// 	}


// 	return str;
// }

char * task_com_play_time_str(int sel)
{	
	char *str;
	static char playCurTimeStr[] = {"00:00:00"};
	static char playTotalTimeStr[] = {"/00:00:00"};
	u32 sec;
	u32 playtime  = SysCtrl.play_cur_time/1000;
	u32 totaltime = SysCtrl.play_total_time;

	if(totaltime%1000 > 500)
		totaltime = totaltime/1000 + 1;
	else 
		totaltime = totaltime/1000;
	if(sel)
	{
		sec = totaltime;
		str = playTotalTimeStr;
		if(sec/3600)
		{
			str[0] = '/';
			hx330x_num2str(&str[1], sec/3600, 2); //hour
			str[3] = ':';
			hx330x_num2str(&str[4], (sec%3600)/60, 2); //min
			str[6] = ':';
			hx330x_num2str(&str[7], sec%60, 2); //sec		
		}else
		{
			str[0] = '/';
			hx330x_num2str(&str[1], (sec%3600)/60, 2); //min
			str[3] = ':';
			hx330x_num2str(&str[4], sec%60, 2); //sec		
		}		
	}
	else
	{
		sec = playtime;
		str = playCurTimeStr;
		if(sec/3600)
		{
			hx330x_num2str(str, sec/3600, 2); //hour
			str[2] = ':';
			hx330x_num2str(&str[3], (sec%3600)/60, 2); //min
			str[5] = ':';
			hx330x_num2str(&str[6], sec%60, 2); //sec		
		}else
		{
			hx330x_num2str(str, (sec%3600)/60, 2); //min
			str[2] = ':';
			hx330x_num2str(&str[3], sec%60, 2); //sec		
		}		
	}



	return str;
}
/*******************************************************************************
* Function Name  : task_com_service
* Description    : system service in task com
* Input          : *
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_service(u32 scanKey)
{
	static u32 lastTime = 0;
	task_com_sec();
	task_com_100ms();
	hal_ispService();
		
	if((XOSTimeGet()-lastTime)<=10*X_TICK)
		return ;
		
	lastTime = XOSTimeGet();
//--------key check ------------------- 
	if(SysCtrl.dev_stat_power & POWERON_FLAG_KEY) //�ȴ�power key�ͷ�
	{
		u32 adcValue;
		if(dev_ioctrl(SysCtrl.dev_fd_key, DEV_KEY_POWER_READ, (INT32U )&adcValue)>=0)
		{
			if(adcValue == 0)
			{
				SysCtrl.dev_stat_power &=~POWERON_FLAG_KEY;
			}
		}
		scanKey = 0;
	}else
	{
		if(scanKey)
			task_com_key_check();      // system key read
	}

	//deg_Printf("[%d,%d]", XSFR_USB20_CON0,(XSFR_USB20_CON2 >> 5) & 0x03);	
//--------usb host check---------------------
	task_com_usbhost_check();
	//--------usb dev check---------------------
	task_com_usbdev_check();	
    if(app_taskCurId() != TASK_POWER_OFF)
    {
//-------sdc card check-----------------    
	    task_com_sdc_check();  // sdc state check
//--------gsensor check-----------------
	    task_common_gsensor_check(); // gsensor state check
//--------sereen save check ------------	
		task_com_sreen_check(SREEN_CHECK_AUTOOFF); // system check,no event
//--------auto power off check----------
	    task_com_auto_poweroff(0); //auto power off check 
//--------ir auto check-------------
		task_com_ir_auto_check();
//--------motion dection check----------
		task_com_md_check();
		if(scanKey)
			task_com_tp_check();
    }
}

/*******************************************************************************
* Function Name  : task_com_tips_show
* Description    : task_com_tips_show
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void task_com_tips_show(u32 type)
{
	u32 tips_id;
	switch(type)
	{
		case TIPS_TYPE_SD:
		{
			switch(SysCtrl.dev_stat_sdc)
			{
				case SDC_STAT_NULL:  tips_id = TIPS_SD_NOT_INSERT; break;
				case SDC_STAT_FULL:  tips_id = TIPS_SD_FULL;       break;
				case SDC_STAT_ERROR: tips_id = TIPS_SD_ERROR;	   break;
				default: return;
			}
			uiOpenWindow(&tips1Window,0,2,tips_id, 2); //R_ID_PALETTE_TransBlack
			break;
		}
		case TIPS_TYPE_SPI:
		{
			uiOpenWindow(&tipsWindow,0,2,TIPS_SPI_FULL, 2);
			break;
		}
		case TIPS_TYPE_POWER:
		{
			if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
			{
				// if(SysCtrl.dev_stat_battery == BATTERY_STAT_1)
				 if(SysCtrl.dev_stat_battery == BATTERY_STAT_1)

				{
					tips_id = TIPS_POWER_LOW;
				}else if(SysCtrl.dev_stat_battery == BATTERY_STAT_0)
				{
					tips_id = TIPS_NO_POWER;
				}else
				{
                    return;
				}
				uiOpenWindow(&tips1Window,0,2,tips_id,4); //改为提示4秒，2秒感觉有点太快 R_ID_PALETTE_TransBlack
			}
			break;
		}
		case TIPS_COM_WAITING_2S: uiOpenWindow(&tipsWindow,1,2,R_ID_STR_COM_WAITING,2); break; //R_ID_PALETTE_Blue
		case TIPS_COM_WAITING_5S: uiOpenWindow(&tipsWindow,1,2,R_ID_STR_COM_WAITING,5); break; //R_ID_PALETTE_Blue
		case TIPS_COM_SUCCESS: 	uiOpenWindow(&tipsWindow,0,2,R_ID_STR_COM_SUCCESS,2); break;  //R_ID_PALETTE_Blue
		case TIPS_COM_FAIL:		uiOpenWindow(&tipsWindow,0,2,R_ID_STR_COM_FAILED,2);  break;  //R_ID_PALETTE_Blue
		case TIPS_NO_FILE: 		uiOpenWindow(&tipsWindow,0,2,R_ID_STR_FIL_NULL,2);	  break;  //R_ID_PALETTE_Blue
		case TIPS_SET_LOCKED:	uiOpenWindow(&tipsWindow,0,2,R_ID_STR_SET_LOCKED,2);  break;  //R_ID_PALETTE_Blue
		case TIPS_FMT_SUCCESS:	uiOpenWindow(&tipsWindow,0,2,R_ID_STR_FMT_SUCCESS,2); break;  //R_ID_PALETTE_Blue
		case TIPS_FMT_FAIL:		uiOpenWindow(&tipsWindow,0,2,R_ID_STR_FMT_FAIL,2);	  break;
		case TIPS_ERROR:		uiOpenWindow(&tipsWindow,0,2,"ERROR",2);	  break;
		case TIPS_TAKEPHOTO_SUCCESS:uiOpenWindow(&takephotoiconWindow,0,2," ",1);	  break;
		case TIPS_DEF_SUCCESS:	uiOpenWindow(&tipsWindow,1,2,R_ID_STR_FMT_SUCCESS,4);	break;
		
		
		default : break;
	}
}



// void task_com_LedPwm_ctrl(int step)//-1:dec	1:add
// {
// 	static u8 buf[4]={LED_LEVEL_0_DUTY,LED_LEVEL_1_DUTY,LED_LEVEL_2_DUTY,LED_LEVEL_3_DUTY};
// 	#if 1//(BOARD_SELECT == BOARD_G30)
		
// 			SysCtrl.led_pwm_level --;
// 			if(SysCtrl.led_pwm_level<0)
// 				SysCtrl.led_pwm_level = 0;
		
// 			SysCtrl.led_pwm_level ++;
// 			if(SysCtrl.led_pwm_level>3)
// 				SysCtrl.led_pwm_level = 0;
		
	
// 		SysCtrl.led_pwm_level --;
// 		if(SysCtrl.led_pwm_level<0)
// 			SysCtrl.led_pwm_level = 3;
	
// 		deg_Printf("SysCtrl.led_pwm_level():%d\n", SysCtrl.led_pwm_level);
// 		if(SysCtrl.led_pwm_level == 0)
// 			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,0); 
// 		else
// 			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,1); 
// 	if(SysCtrl.led_pwm_level == 3)
// 	{
// 	hal_timerPWMStop(hardware_setup.led_pwm_timer);
// 	hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin ,GPIO_OUTPUT,GPIO_PULL_FLOATING);
// 	//hal_gpioEPullSet(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 1 ? GPIO_PULLE_UP : GPIO_PULLE_DOWN);
// 	hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 1);   
// 	}else if(SysCtrl.led_pwm_level == 0)
// 	{
// 	hal_timerPWMStop(hardware_setup.led_pwm_timer);
// 	hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin ,GPIO_OUTPUT,GPIO_PULL_FLOATING);
// 	//hal_gpioEPullSet(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ? GPIO_PULLE_UP : GPIO_PULLE_DOWN);
// 	hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ); 

// 	}else
// 	{
// 		dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_PWM_ADJUST,buf[SysCtrl.led_pwm_level]); 
// 	}
// }

// void task_com_LedOnOff_ctrl(u8 onoff)//0:off			1:on
// {
// 	static u8 buf[4]={LED_LEVEL_0_DUTY,LED_LEVEL_1_DUTY,LED_LEVEL_2_DUTY,LED_LEVEL_3_DUTY};
// 	if(!onoff)
// 	{
// 		dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,0); 
// 		hal_timerPWMStop(hardware_setup.led_pwm_timer);
// 		hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin ,GPIO_OUTPUT,GPIO_PULL_FLOATING);
// 		//hal_gpioEPullSet(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ? GPIO_PULLE_UP : GPIO_PULLE_DOWN);
// 		hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ); 
// 		return;
// 	}
// 		if(SysCtrl.led_pwm_level == 0)
// 			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,0); 
// 		else
// 			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,1); 
// 	if(SysCtrl.led_pwm_level == 3)
// 	{
// 	hal_timerPWMStop(hardware_setup.led_pwm_timer);
// 	hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin ,GPIO_OUTPUT,GPIO_PULL_FLOATING);
// 	//hal_gpioEPullSet(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 1 ? GPIO_PULLE_UP : GPIO_PULLE_DOWN);
// 	hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 1);   
// 	}else if(SysCtrl.led_pwm_level == 0)
// 	{
// 	hal_timerPWMStop(hardware_setup.led_pwm_timer);
// 	hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin ,GPIO_OUTPUT,GPIO_PULL_FLOATING);
// 	//hal_gpioEPullSet(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ? GPIO_PULLE_UP : GPIO_PULLE_DOWN);
// 	hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ); 

// 	}else
// 	{
// 		dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_PWM_ADJUST,buf[SysCtrl.led_pwm_level]); 
// 	}
// }


void task_com_LedPwm_ctrl(int step)//-1:dec	1:add
{
	static u8 buf[4]={LED_LEVEL_0_DUTY,LED_LEVEL_1_DUTY,LED_LEVEL_2_DUTY,LED_LEVEL_3_DUTY};
		SysCtrl.led_pwm_level --;
		if(SysCtrl.led_pwm_level<0)
			SysCtrl.led_pwm_level = 3;
		if(SysCtrl.led_pwm_level == 0)
			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,0); 
		else
			dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,1); 
	if(SysCtrl.led_pwm_level == 3)
	{
		hal_timerPWMStop(hardware_setup.led_pwm_timer);
		hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin ,GPIO_OUTPUT,GPIO_PULL_FLOATING);
		//hal_gpioEPullSet(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 1 ? GPIO_PULLE_UP : GPIO_PULLE_DOWN);
		hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 1);   
	}else if(SysCtrl.led_pwm_level == 0)
	{
		hal_timerPWMStop(hardware_setup.led_pwm_timer);
		hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin ,GPIO_OUTPUT,GPIO_PULL_FLOATING);
		//hal_gpioEPullSet(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ? GPIO_PULLE_UP : GPIO_PULLE_DOWN);
		hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ); 

	}else
	{
		dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_PWM_ADJUST,buf[SysCtrl.led_pwm_level]); 
	}

	deg_Printf("KEYPWN________________:%d\n",SysCtrl.led_pwm_level);
}

void task_com_LedOnOff_ctrl(u8 onoff)//0:off			1:on
{
	static u8 buf[4]={LED_LEVEL_0_DUTY,LED_LEVEL_1_DUTY,LED_LEVEL_2_DUTY,LED_LEVEL_3_DUTY};
	if(!onoff)
	{
		dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,0); 
		hal_timerPWMStop(hardware_setup.led_pwm_timer);
		hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin ,GPIO_OUTPUT,GPIO_PULL_FLOATING);
		//hal_gpioEPullSet(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ? GPIO_PULLE_UP : GPIO_PULLE_DOWN);
		hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ); 
		return;
	}
	if(SysCtrl.led_pwm_level == 0)
		dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,0); 
	else
		dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_ON_OFF_WRITE,1); 
	if(SysCtrl.led_pwm_level == 3)
	{
		hal_timerPWMStop(hardware_setup.led_pwm_timer);
		hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin ,GPIO_OUTPUT,GPIO_PULL_FLOATING);
		//hal_gpioEPullSet(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 1 ? GPIO_PULLE_UP : GPIO_PULLE_DOWN);
		hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 1);   
	}else if(SysCtrl.led_pwm_level == 0)
	{
		hal_timerPWMStop(hardware_setup.led_pwm_timer);
		hal_gpioInit(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin ,GPIO_OUTPUT,GPIO_PULL_FLOATING);
		//hal_gpioEPullSet(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ? GPIO_PULLE_UP : GPIO_PULLE_DOWN);
		hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin, 0 ); 

	}else
	{
		dev_ioctrl(SysCtrl.dev_fd_led_pwm,DEV_LED_PWM_ADJUST,buf[SysCtrl.led_pwm_level]); 
	}
}

char * task_com_scaler_str(void)
{
	// static char scaler_str[] = {"1.0X"};
	// if(SysCtrl.lcd_scaler_level == 0)
	// {
	// 	scaler_str[0] = '1';
	// 	scaler_str[2] = '0';
	// }else
	// {
	// 	scaler_str[0] = 1+SysCtrl.lcd_scaler_level + '0';
	// 	scaler_str[2] = '0';
	// }
	// return scaler_str;
		static char *scaler_str[] = {"","1.3X","1.5X","2.0X"};
	return scaler_str[SysCtrl.lcd_scaler_level];
}





/*
* status------0:enter usb device	1:enter sensor show
*/
void task_com_USB_CS_DM_DP_status_select(u8 status)
{
	if(status)
	{
		hal_gpioInit(hardware_setup.usb_dp_ch, hardware_setup.usb_dp_pin,GPIO_INPUT,GPIO_PULL_UP);
		hal_gpioInit(hardware_setup.usb_dm_ch, hardware_setup.usb_dm_pin,GPIO_INPUT,GPIO_PULL_UP); 
		hal_gpioWrite(hardware_setup.usb_cs_ch, hardware_setup.usb_cs_pin,GPIO_HIGH);

	}else
	{
		hal_gpioInit(hardware_setup.usb_dp_ch, hardware_setup.usb_dp_pin,GPIO_INPUT,GPIO_PULL_FLOATING);
		hal_gpioInit(hardware_setup.usb_dm_ch, hardware_setup.usb_dm_pin,GPIO_INPUT,GPIO_PULL_FLOATING); 
		hal_gpioWrite(hardware_setup.usb_cs_ch, hardware_setup.usb_cs_pin,GPIO_LOW);
	}
}




