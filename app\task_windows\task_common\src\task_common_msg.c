/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

/*******************************************************************************
* Function Name  : taskComMsgMode
* Description    : taskComMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int taskComMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		deg_Printf("[COM]:deal MSG Mode\n");
		app_taskChange();
	}
	return 0;
}

ALIGNED(4) const msgDealInfor taskComMsgDeal[]=
{
	{KEY_EVENT_MODE,	taskComMsgMode},
	{EVENT_MAX,NULL},
};






