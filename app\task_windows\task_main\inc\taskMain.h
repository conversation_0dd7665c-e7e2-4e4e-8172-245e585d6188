/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef  __TASK_Main_H
#define  __TASK_Main_H

EXTERN_WINDOW(mainWindow);
extern msgDealInfor mainMsgDeal[];
extern sysTask_T taskMain;
typedef struct MAINTASK_OP_S
{
	u32 curId;
	u32 curIdShowBig; 
	u32 winShowToggleInterval;
	u32 winShowToggleCurtime;

	u32 main_id;
	u32 sub_type;
	u32 sub_id;
	u32 sub_size;
	u8* main_yaddr;
	u8* main_uvaddr;
	u8* sub_yaddr;
	u8* sub_uvaddr;
	u16 x, y;
	u16 ratio_w, ratio_h;
	u16 dest_w, dest_h;
	u16 w_step,h_step;
	u32 winChangeInterval;
	u16 main_stride;
	u16 wintype;
}MAINTASK_OP_T;
typedef enum
{
    MAIN_WIN_NONE 	= 0,
	SUB_TO_MAIN_HOR_LEFT,
	SUB_TO_MAIN_HOR_RIGHT,
	SUB_TO_MAIN_VOR_UP,
	SUB_TO_MAIN_VOR_DOWN,
	SUB_TO_MAIN_CENTER,
	MAIN_TO_SUB_HOR_LEFT,
	MAIN_TO_SUB_HOR_RIGHT,
	MAIN_TO_SUB_VOR_UP,
	MAIN_TO_SUB_VOR_DOWN,
	MAIN_TO_SUB_CENTER,
	MAIN_WIN_MAX,
	
}MAIN_WIN_TYPE;

typedef struct{
	u32 resId;
	u32 taskId;
}MAIN_MENU_TAB;
extern const MAIN_MENU_TAB mainMenuTab[];

extern MAINTASK_OP_T  mainTaskOp;



/*******************************************************************************
* Function Name  : taskMainWinInit
* Description    : taskMainWinInit function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
void taskMainWinInit(u32 main_id, u32 sub_type, u32 sub_id, u32 sub_size,u32 win_type);
/*******************************************************************************
* Function Name  : taskMainWinChangeInit
* Description    : taskMainWinChangeInit function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
void taskMainWinChangeInit(void);
/*******************************************************************************
* Function Name  : taskWinChangeProcess
* Description    : taskWinChangeProcess function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
int taskWinChangeProcess(void);
/*******************************************************************************
* Function Name  : taskMainWinShow
* Description    : taskMainWinShow
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
//void taskMainWinShow(void);
#endif
