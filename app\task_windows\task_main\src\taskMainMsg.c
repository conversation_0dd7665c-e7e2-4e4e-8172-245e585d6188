/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskMainWin.c"
extern u8 flash_success;
//static u8 main_choose_count=0


ALIGNED(4) const MAIN_MENU_TAB mainMenuTab[] = {
	{R_ID_IMAGE_FANGDAJING, 		TASK_RECORD_PHOTO},//拍照
	{R_ID_IMAGE_SENSOR_PHOTO,  		TASK_RECORD_VIDEO},//录像
	{R_ID_IMAGE_PLAY_VIDEO,			TASK_PLAY_VIDEO},//相册
	{R_ID_IMAGE_DELETE_PHOTO,		TASK_SETTING},//设置
	// {R_ID_IMAGE_MAIN_SETTINGS,			TASK_SETTING},//设置
//{R_ID_IMAGE_MAIN_PLAY, 			TASK_C_GAME},//游戏
//{R_ID_IMAGE_MAIN_SETTINGS,		TASK_RECORD_PHOTO}//场景拍照


};
#define MAIN_ID_MAX			(sizeof(mainMenuTab)/sizeof(mainMenuTab[0]))


/*******************************************************************************
* Function Name  : taskMainService
* Description    : taskMainService function.
* Input          :
* Output         : none
* Return         : none
*******************************************************************************/
void taskMainCurIdCfg(uint32 taskId)
{
	u32 i;
	for(i = 0; i < MAIN_ID_MAX; i++)
	{
		if(taskId == mainMenuTab[i].taskId)
		{
			mainTaskOp.curId = i;
			break;
		}
	}
}

/*******************************************************************************
* Function Name  : taskMainWinShow
* Description    : taskMainWinShow
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int  taskMainWinShow(winHandle handle)
{

	resID id = mainTaskOp.curIdShowBig ? mainMenuTab[mainTaskOp.curId].resId :R_ID_IMAGE_MAIN_BACKGROUND ;


	// if(SysCtrl.black_id)
	// {
	// 	res_image_show(R_ID_IMAGE_MAIN_BLACK);
	// }else{
	// 	res_image_show(id);

	// }


	mainNameshow(handle,1);
	recordMAINBatteryShow(handle,1);
	res_image_show1(id);


}
/*******************************************************************************
* Function Name  : mainKeyMsgOk
* Description    : mainKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		//mainNameshow(handle,0);
		if(mainTaskOp.curId == 0)//拍照
		{



			app_taskStart(TASK_RECORD_PHOTO,0);
			//XOSTimeDly(1000);
			// if(!hardware_setup.cmos_sensor_sel==1)//
			// {
			//  	//app_lcdCsiVideoShowStop();
			// 	dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_UINIT, hardware_setup.cmos_sensor_sel);
			// 	hardware_setup.cmos_sensor_sel ^=1;
			// 	//XOSTimeDly(1000);
			// 	dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_INIT, hardware_setup.cmos_sensor_sel);
			// 	//app_lcdCsiVideoShowStart();
			// 	//app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
			// }

		}else if(mainTaskOp.curId == 1)//录像
		{






			app_taskStart(TASK_RECORD_VIDEO,0);
			// //XOSTimeDly(1000);
			// if(!hardware_setup.cmos_sensor_sel==0)
			// {
				//app_lcdCsiVideoShowStop();
				// SysCtrl.dev_fd_led_pwm_sensor=1;




				// dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_UINIT, hardware_setup.cmos_sensor_sel);
				// hardware_setup.cmos_sensor_sel ^=1;
				// //XOSTimeDly(100);
				// dev_ioctrl(SysCtrl.dev_fd_sensor, DEV_SENSOR_INIT, hardware_setup.cmos_sensor_sel);




				//app_lcdCsiVideoShowStart();
				//app_lcdShowWinModeCfg(SysCtrl.lcdshow_win_mode);
			//}

		}
		else if(mainTaskOp.curId == 2)//相册
		{
			app_taskStart(TASK_RECORD_VIDEO,0);
		}
		else if(mainTaskOp.curId == 3)//设置
		{
			
			app_taskStart(TASK_SETTING,0);
		}
		else
		{
			app_taskStart(mainMenuTab[mainTaskOp.curId%MAIN_ID_MAX].taskId,0);
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : mainKeyMsgUp
* Description    : mainKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		mainTaskOp.curIdShowBig = 0;
			// taskMainWinShow(handle);
			switch (mainTaskOp.curId) {//四宫格
			case 0:
				mainTaskOp.curId = 1;
				break;
			 case 1:
			 	mainTaskOp.curId = 0;
			 	break;
			// case 3:
			// 	mainTaskOp.curId = 1;
			// 	break;
			// case 2:
			// 	mainTaskOp.curId = 0;
			// 	break;
			}
		mainTaskOp.curIdShowBig = 1;
			taskMainWinShow(handle);
		mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : mainKeyMsgDown
* Description    : mainKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		mainTaskOp.curIdShowBig = 0;
			taskMainWinShow(handle);
			switch (mainTaskOp.curId) {//四宫格
			case 0:
				mainTaskOp.curId = 1;
				break;
			 case 1:
			 	mainTaskOp.curId = 0;
			 	break;
			// case 3:
			// 	mainTaskOp.curId = 1;
			// 	break;
			// case 2:
			// 	mainTaskOp.curId = 0;
			// 	break;
			}
		mainTaskOp.curIdShowBig = 1;
			taskMainWinShow(handle);
		mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : mainKeyMsgLeft
* Description    : mainKeyMsgLeft
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainKeyMsgLeft(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		mainTaskOp.curIdShowBig = 0;
			// taskMainWinShow(handle);
		if(mainTaskOp.curId <= 0)
			mainTaskOp.curId = MAIN_ID_MAX -1;
		else
			mainTaskOp.curId--;
		mainTaskOp.curIdShowBig = 1;
			taskMainWinShow(handle);
		mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	}
	return 0;
}
/*******************************************************************************
* Function Name  : mainKeyMsgRight
* Description    : mainKeyMsgRight
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainKeyMsgRight(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		mainTaskOp.curIdShowBig = 0;
			// taskMainWinShow(handle);
		mainTaskOp.curId++;
		if(mainTaskOp.curId >=  MAIN_ID_MAX)
			mainTaskOp.curId = 0;
		mainTaskOp.curIdShowBig = 1;
			taskMainWinShow(handle);
		mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	}
	return 0;
}

/*******************************************************************************
* Function Name  : mainSysMsgUSBDEV
* Description    : mainSysMsgUSBDEV
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainSysMsgUSBDEV(winHandle handle,uint32 parameNum,uint32* parame)
{
	//if(SysCtrl.dev_dusb_stat == USBDEV_STAT_PC)
	//	app_taskStart(TASK_USB_DEVICE,0);


	//if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		recordMAINBatteryShow(handle,1);
	return 0;
	return 0;
}

static int recordMAINSysMsgBattery(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		recordMAINBatteryShow(handle,1);
	return 0;
}
/*******************************************************************************
* Function Name  : mainSysMsg1sec
* Description    : mainSysMsg1sec
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainSysMsgTimeUpdata(winHandle handle,uint32 parameNum,uint32* parame)
{
	mainTaskOp.curIdShowBig ^= 1;
	//mainNameshow(handle,1);
	//recordMAINBatteryShow(handle,1);
		taskMainWinShow(handle);
	return 0;
}

/*******************************************************************************
* Function Name  : mainOpenWin
* Description    : mainOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	//task_com_sound_wait_end();
	//res_music_end();
	deg_Printf("[WIN]mainOpenWin\n");
	//recordMAINBatteryShow(handle,0);
	//mainNameshow(handle,0);
	// if(SysCtrl.item_ui_name)
	// {
	// 	mainNameshow(handle,1);
	// 	recordMAINBatteryShow(handle,1);




	// }
	res_image_show(R_ID_IMAGE_FANGDAJING);
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL&&!flash_success){//wxn-��flash error
		task_com_tips_show(TIPS_ERROR);
		app_taskStart(TASK_POWER_OFF,0);
	}

	return 0;
}
/*******************************************************************************
* Function Name  : mainCloseWin
* Description    : mainCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]mainCloseWin\n");

// XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_TRIGLE,0));//wxn--不直接hal_lcdUiEnable(UI_LAYER0,1);的原因是
// 														//直接显示UI的话，切到拍照模式时会有USB的菜单显示出来
	return 0;
}
/*******************************************************************************
* Function Name  : mainWinChildClose
* Description    : mainWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int mainWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	mainTaskOp.curIdShowBig = 1;
	//app_draw_Service(1);
	//mainNameshow(handle,1);

	//app_draw_Service(1);
	//recordMAINBatteryShow(handle,1);
	taskMainWinShow	(handle);
	mainTaskOp.winShowToggleCurtime = XOSTimeGet();
	deg_Printf("[WIN]mainWinChildClose\n");
	return 0;
}
static int mainSysMsg500MS(winHandle handle, u32 parameNum, u32 *parame)
{
	//mainNameshow(handle,1);
	//recordMAINBatteryShow(handle);
	//if(hx330x_gpioDataGet(GPIO_PE,GPIO_PIN1))
	//{
		recordMAINBatteryShow(handle,1);
	//}
	return 0;
}
static int recordmaintrigle(winHandle handle,uint32 parameNum,uint32* parame)
{
hal_lcdUiEnable(UI_LAYER0,1);
	return 0;
}

static int mainKeyMsgOkLong(winHandle handle, u32 parameNum, u32 *parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		app_taskStart(TASK_RECORD_PHOTO,0);
	}
	return 0;
}

static int mainKeyMsgMode(winHandle handle, u32 parameNum, u32 *parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		app_taskStart(TASK_PLAY_VIDEO,0);
	}
	return 0;
}


ALIGNED(4) msgDealInfor mainMsgDeal[]=
{
	{SYS_OPEN_WINDOW,	mainOpenWin},
	{SYS_CLOSE_WINDOW,	mainCloseWin},
	{SYS_CHILE_COLSE,	mainWinChildClose},
	{KEY_EVENT_PHOTO,		mainKeyMsgOk},
	{KEY_EVENT_PHOTO_LONG, mainKeyMsgOkLong},
	//{KEY_EVENT_UP,		mainKeyMsgUp},//
	//{KEY_EVENT_DOWN,	mainKeyMsgDown},//
	{KEY_EVENT_UP,	mainKeyMsgLeft},//
	{KEY_EVENT_DOWN,	mainKeyMsgRight},//
	//{SYS_EVENT_1S,	mainSysMsg500MS},
	//{SYS_EVENT_1S,	mainSysMsg1000MS},
	//{SYS_EVENT_USBDEV,	mainSysMsgUSBDEV},
	//{SYS_EVENT_TIME_UPDATE,mainSysMsgTimeUpdata},
//	{SYS_EVENT_BAT,		recordMAINSysMsgBattery},
	{KEY_EVENT_TRIGLE,		recordmaintrigle},
	{KEY_EVENT_UVC_FORM,		mainKeyMsgMode},

	{EVENT_MAX,NULL},
};

WINDOW(mainWindow,mainMsgDeal,mainWin)
