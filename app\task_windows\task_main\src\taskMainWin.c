/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	TASKPHOTO,
	TASKVIDEO,
	TASKALBUM,
	TASKMUSIC,
	TASKGAME,
	TASKSRETTING,
	PHOTO_MODE_ID,
	PHOTO_SD_ID,
	MAIN_BATERRY_ID,
};
UNUSED ALIGNED(4) const widgetCreateInfor mainWin[] =
{
	createFrameWin(							Rx(0),   <PERSON>y(0),   <PERSON>w(320), <PERSON>h(240),	R_ID_PALETTE_Transparent,		WIN_ABS_POS),
	
	//createStringIcon(TASKPHOTO,     		Rx(40),  <PERSON>y(93),   Rw(106),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_Black,DEFAULT_FONT),
	//createStringIcon(TASKVIDEO,     		Rx(175), Ry(93),   Rw(106),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_Black,DEFAULT_FONT),
//	createStringIcon(TASKMUSIC,     		Rx(208), Ry(96),   Rw(106),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_Black,DEFAULT_FONT),
	//createStringIcon(TASKALBUM,     		Rx(40),  Ry(212),   Rw(106),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_Black,DEFAULT_FONT),
//	createStringIcon(TASKGAME,     			Rx(107), Ry(203),   Rw(106),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_Black,DEFAULT_FONT),
	//createStringIcon(TASKSRETTING,     		Rx(173), Ry(212),   Rw(110),  Rh(24),	RAM_ID_MAKE(" "),		ALIGNMENT_CENTER, 	R_ID_PALETTE_Black,DEFAULT_FONT),
	//createImageIcon(MAIN_BATERRY_ID,    	Rx(265), Ry(0), Rw(50),  Rh(28), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),
	widgetEnd(),
};


UNUSED static void mainNameshow(winHandle handle, u8 visable)
{
return;
	// if(visable)
	// {
	// 	uiWinSetResid(winItem(handle, TASKPHOTO), R_ID_STR_TIPS_TITLE_MICROSCOPE);
	// 	uiWinSetResid(winItem(handle, TASKVIDEO), R_ID_STR_TIPS_TITLE_CAMERA);
	// 	uiWinSetResid(winItem(handle, TASKALBUM), R_ID_STR_TIPS_TITLE_ALBUM);
	// 	//uiWinSetResid(winItem(handle, TASKMUSIC), R_ID_STR_TIPS_TITLE_MP3);
	// 	//uiWinSetResid(winItem(handle, TASKGAME), R_ID_STR_TIPS_TITLE_GAME);
    //     uiWinSetResid(winItem(handle, TASKSRETTING), R_ID_STR_TIPS_TITLE_SETTING);
	// 	uiWinSetVisible(winItem(handle, TASKPHOTO), 1);
	// 	uiWinSetVisible(winItem(handle, TASKVIDEO), 1);
	// 	uiWinSetVisible(winItem(handle, TASKALBUM), 1);
	// 	uiWinSetVisible(winItem(handle, TASKMUSIC), 1);
	// 	uiWinSetVisible(winItem(handle, TASKGAME), 1);
	// 	uiWinSetVisible(winItem(handle, TASKSRETTING), 1);
	// 	uiWinSetVisible(winItem(handle, MAIN_BATERRY_ID), 1);
	// }else{
	// 	uiWinSetVisible(winItem(handle, TASKPHOTO), 0);
	// 	uiWinSetVisible(winItem(handle, TASKVIDEO), 0);
	// 	uiWinSetVisible(winItem(handle, TASKALBUM), 0);
	// 	uiWinSetVisible(winItem(handle, TASKMUSIC), 0);
	// 	uiWinSetVisible(winItem(handle, TASKGAME), 0);
	// 	uiWinSetVisible(winItem(handle, TASKSRETTING), 0);
	// 	uiWinSetVisible(winItem(handle, MAIN_BATERRY_ID), 0);
	// }
	// SysCtrl.item_ui_name=0;
}



UNUSED static void recordMAINBatteryShow(winHandle handle, u8 visable)
{
    return;
	// resID batid;
	// if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	// 	batid = R_ID_ICON_MTBATTERYCHARGE;
	// else{
	// 	switch(SysCtrl.dev_stat_battery)
	// 	{
	// 		case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
	// 		case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
	// 		case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
	// 		case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
	// 		//case BATTERY_STAT_4:
	// 		//case BATTERY_STAT_5:
	// 		default:
	// 							 batid = R_ID_ICON_MTBATTERY4; break;
	// 	}
	// }
	// uiWinSetVisible(winItem(handle,MAIN_BATERRY_ID),visable);
	// uiWinSetResid(winItem(handle,MAIN_BATERRY_ID),batid);
	// if(hx330x_gpioDataGet(GPIO_PE,GPIO_PIN1))
	// {
	// 	uiWinSetResid(winItem(handle,MAIN_BATERRY_ID),R_ID_ICON_MTBATTERY4);
	// }

}



UNUSED static void recordPhotoSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		uiWinSetResid(winItem(handle,PHOTO_SD_ID),R_ID_ICON_MTSDCNORMAL);
	else
		uiWinSetResid(winItem(handle,PHOTO_SD_ID),R_ID_ICON_MTSDCNULL);
}