/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
//#include "../../../app_common/inc/app_api.h"
enum
{
	PLAYAUDIO_MODE_ID   = 0,
	PLAYAUDIO_PLAY_TIME_ID,
	PLAYAUDIO_SD_ID,
	PLAYAUDIO_BATERRY_ID,
	PLAYAUDIO_SELECT_ID =0,
};

UNUSED ALIGNED(4) const widgetCreateInfor playAudioWin[] =
{
	createFrameWin( 						Rx(0),   <PERSON>y(0),   <PERSON>w(320), <PERSON>h(240), R_ID_PALETTE_Black,WIN_ABS_POS),
	createImageIcon(PLAYAUDIO_MODE_ID,      Rx(5),   Ry(0),   Rw(25),  Rh(25),  R_ID_ICON_MTPLAY2, 	ALIGNMENT_LEFT),
	createStringIcon(PLAYAUDIO_PLAY_TIME_ID,Rx(30),  Ry(0),   Rw(110), Rh(25),	RAM_ID_MAKE(" "),	ALIGNMENT_CENTER, 	R_ID_PALETTE_White,DEFAULT_FONT),
	createImageIcon(PLAYAUDIO_SD_ID,        Rx(270), Ry(0),   Rw(25),  Rh(25), 	R_ID_ICON_MTSDCNORMAL,	ALIGNMENT_CENTER),
	createImageIcon(PLAYAUDIO_BATERRY_ID,   Rx(290), Ry(0),   Rw(32),  Rh(30), 	R_ID_ICON_MTBATTERY3,	ALIGNMENT_RIGHT),
	createItemManage(PLAYAUDIO_SELECT_ID,	Rx(5),   Ry(25),  Rw(310), Rh(215), INVALID_COLOR),
	widgetEnd(),
};

/*******************************************************************************
* Function Name  : playAudioPlayTimeShow
* Description    : playAudioPlayTimeShow
* Input          : winHandle handle,u32 playTime,u32 totalTime
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioPlayTimeShow(winHandle handle)
{
    uiWinSetVisible(winItem(handle,PLAYAUDIO_PLAY_TIME_ID),1);
	if(audioPlaybackGetStatus() == MEDIA_STAT_PLAY)
	{
		uiWinSetStrInfor(winItem(handle,PLAYAUDIO_PLAY_TIME_ID), DEFAULT_FONT, ALIGNMENT_CENTER, R_ID_PALETTE_Red);
		uiWinSetResid(winItem(handle,PLAYAUDIO_PLAY_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(0)));
	}
	else
	{
		uiWinSetStrInfor(winItem(handle,PLAYAUDIO_PLAY_TIME_ID), DEFAULT_FONT, ALIGNMENT_CENTER, R_ID_PALETTE_White);
		uiWinSetResid(winItem(handle,PLAYAUDIO_PLAY_TIME_ID),RAM_ID_MAKE(task_com_play_time_str(1)));
	}


}
/*******************************************************************************
* Function Name  : playAudioSDShow
* Description    : playAudioSDShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioSDShow(winHandle handle)
{
	if(SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL || SysCtrl.dev_stat_sdc == SDC_STAT_FULL)
		uiWinSetResid(winItem(handle,PLAYAUDIO_SD_ID),R_ID_ICON_MTSDCNORMAL);
	else
		uiWinSetResid(winItem(handle,PLAYAUDIO_SD_ID),R_ID_ICON_MTSDCNULL);
}
/*******************************************************************************
* Function Name  : playAudioBaterryShow
* Description    : playAudioBaterryShow
* Input          : winHandle handle
* Output         : none
* Return         : none
*******************************************************************************/
static void playAudioBaterryShow(winHandle handle)
{
	resID batid;
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
		batid = R_ID_ICON_MTBATTERY5;
	else{
		switch(SysCtrl.dev_stat_battery)
		{
			case BATTERY_STAT_0: batid = R_ID_ICON_MTBATTERY0; break;
			case BATTERY_STAT_1: batid = R_ID_ICON_MTBATTERY1; break;
			case BATTERY_STAT_2: batid = R_ID_ICON_MTBATTERY2; break;
			case BATTERY_STAT_3: batid = R_ID_ICON_MTBATTERY3; break;
			//case BATTERY_STAT_4:
			//case BATTERY_STAT_5:
			default:
								 batid = R_ID_ICON_MTBATTERY4; break;
		}
	}
	uiWinSetVisible(winItem(handle,PLAYAUDIO_BATERRY_ID),1);
	uiWinSetResid(winItem(handle,PLAYAUDIO_BATERRY_ID),batid);
}
