/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"


ALIGNED(4) PLAYVIDEO_OP_T  playVideoOp;
/*******************************************************************************
* Function Name  : taskPlayVideoDecodeToImage
* Description    : taskPlayVideoDecodeToImage
* Input          : int index: filelist index
*                  u8 *buff:  image buff
*                  u16 tar_w: tar image width
*                  u16 tar_h: tar image height
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
static int taskPlayVideoDecodeToImage(int index,u8 *buff,u16 tar_w,u16 tar_h)
{
	char *name;
	int type;
	name = filelist_GetFileFullNameByIndex(SysCtrl.avi_list, index, &type);
	if(name == NULL)
		return -1;
	SysCtrl.file_index 	= index;
    SysCtrl.file_type	= type;
	//deg_Printf("ThumbNail:%s\n",name);
	if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_JPG)
	{
		JPG_DEC_ARG arg;
		arg.type 		= MEDIA_FILE_JPG;
		arg.wait 		= 1;
		arg.fd 			= (int)fs_open(name,FA_READ);
		arg.src_type	= MEDIA_SRC_FS;
		arg.dst_width	= tar_w;
		arg.dst_height	= tar_h;
		arg.yout  		= buff;
		arg.uvout 		= buff+((tar_w+0x1f)&(~0x1f))*tar_h;
		arg.step_yout 	= NULL;
		arg.p_lcd_buffer = NULL;
		if(imageDecodeStart(&arg)<0)
		{
			fs_close(arg.fd);
			return -1;
		}
		fs_close(arg.fd);
	}
	else
	{
		VIDEO_PARG_T arg;
		arg.avi_arg.src_type	= MEDIA_SRC_FS;
		arg.avi_arg.fd			= (int)fs_open(name,FA_READ);
		if((int)arg.avi_arg.fd < 0)
			return -1;
		arg.tar_width 	= tar_w;
		arg.tar_height 	= tar_h;
		arg.dest_width  = tar_w;
		arg.dest_height = tar_h;
		arg.yout  		= buff;
        arg.uvout 		= buff+((tar_w+0x1f)&(~0x1f))*tar_h;
		fs_seek(arg.avi_arg.fd,0,FA_CREATE_LINKMAP);

		if(videoDecodeFirstFrame(&arg)<0)
		{
			deg_Printf("avi decode fail<%s>\n",name);
			fs_close(arg.avi_arg.fd);
			return -1;
		}
		fs_close(arg.avi_arg.fd);

	 }
	 return 0;
}
/*******************************************************************************
* Function Name  : taskPlayVideoThumbnallDrawImage
* Description    : taskPlayVideoThumbnallDrawImage
* Input          : lcdshow_frame_t* p_frame,int index,uiRect* tar_rect
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskPlayVideoThumbnallDrawImage(lcdshow_frame_t* p_frame,int index,uiRect* tar_rect)
{
	u16 tar_main_w 	= p_frame->w;
	u16 tar_main_h 	= p_frame->h;
	u16 tar_thumb_w	= tar_rect->x1 - tar_rect->x0 ;
	u16 tar_thumb_h = tar_rect->y1 - tar_rect->y0;
	u16 tar_thumb_x = tar_rect->x0;
	u16 tar_thumb_y = tar_rect->y0;
	u16 tar_thumb_stride = (tar_thumb_w + 0x1f)&~0x1f;
	u32 thumbSize;
	u8 *thumbBuf;
	if(tar_thumb_x + tar_thumb_w > tar_main_w)
	{
		deg_Printf("[PLAY VIDEO THUMB] w over \n");
		tar_thumb_w = (tar_main_w - tar_thumb_x)&~0x1;
	}
	if(tar_thumb_y + tar_thumb_h > tar_main_h)
	{
		deg_Printf("[PLAY VIDEO THUMB] h over \n");
		tar_thumb_h = (tar_main_h - tar_thumb_y)&~0x01;
	}
	thumbSize	= tar_thumb_stride*tar_thumb_h*3/2;
	tar_main_w  = (tar_main_w + 0x1f)&~0x1f;

	//==decode jpeg==
	thumbBuf = hal_sysMemMalloc(thumbSize);
	if(NULL == thumbBuf)
	{
		deg_Printf("[PLAY VIDEO THUMB] buf malloc fail :%d\n",thumbSize);
		return -1;
	}
	if(taskPlayVideoDecodeToImage(index,thumbBuf,tar_thumb_w,tar_thumb_h)<0)
	{
		deg_Printf("[PLAY VIDEO THUMB] image decode fail\n");
		hal_sysMemFree(thumbBuf);
		return -1;
	}
	u8* tar_main_buf  = p_frame->y_addr + tar_thumb_y*tar_main_w + tar_thumb_x;
	u8* src_thumb_buf = thumbBuf;
	u16 i;
	//Y
	for(i = 0; i < tar_thumb_h; i++)
	{
		//deg_Printf("memcpy %x, %x, %d\n",tar_main_buf,src_thumb_buf,tar_thumb_w);
		hx330x_mcpy0_sdram2gram((void *)tar_main_buf, (void*)src_thumb_buf, tar_thumb_w);
		tar_main_buf  += tar_main_w;
		src_thumb_buf += tar_thumb_stride;
	}
	//UV
	tar_main_buf = p_frame->uv_addr + tar_thumb_y*tar_main_w/2 + tar_thumb_x;
	for(i = 0; i < tar_thumb_h/2; i++)
	{
		hx330x_mcpy0_sdram2gram((void *)tar_main_buf, (void*)src_thumb_buf, tar_thumb_w);
		tar_main_buf  += tar_main_w;
		src_thumb_buf += tar_thumb_stride;
	}
	hal_sysMemFree(thumbBuf);

	return 0;
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideStyleProcess
* Description    : taskPlayVideoSlideStyleProcess
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoSlideStyleProcess(void)
{
	playVideoOp.slide.step_cur++;
	if(playVideoOp.slide.step_cur == playVideoOp.slide.step_max)
	{
		playVideoOp.slide.rect_type = SLIDE_RECT_SLIDE;
		playVideoOp.slide.slide_rect.xs	= 0;
		playVideoOp.slide.slide_rect.ys	= 0;
		playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
		playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
	}else
	{
		u16 slide_w = playVideoOp.slide.main_rect.xe / playVideoOp.slide.step_max * playVideoOp.slide.step_cur;
		u16 slide_h = playVideoOp.slide.main_rect.ye / playVideoOp.slide.step_max * playVideoOp.slide.step_cur;
		if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_AROUND_OPEN)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= playVideoOp.slide.main_rect.xe/2 - slide_w/2;
			playVideoOp.slide.slide_rect.ys	= playVideoOp.slide.main_rect.ye/2 - slide_h/2;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe/2 + slide_w/2;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye/2 + slide_h/2;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_AROUND_CLOSE)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_MAIN;
			playVideoOp.slide.slide_rect.xs	= slide_w/2;
			playVideoOp.slide.slide_rect.ys	= slide_h/2;
			playVideoOp.slide.slide_rect.xe = playVideoOp.slide.main_rect.xe - slide_w/2;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye - slide_h/2;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_HOR_OPEN)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= playVideoOp.slide.main_rect.xe/2 - slide_w/2;
			playVideoOp.slide.slide_rect.ys	= 0;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe/2 + slide_w/2;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
		}
		else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_HOR_CLOSE)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_MAIN;
			playVideoOp.slide.slide_rect.xs	= slide_w/2;
			playVideoOp.slide.slide_rect.ys	= 0;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe - slide_w/2;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
		}
		else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_VER_OPEN)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= 0;
			playVideoOp.slide.slide_rect.ys	= playVideoOp.slide.main_rect.ye/2 - slide_h/2;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye/2 + slide_h/2;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_VER_CLOSE)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_MAIN;
			playVideoOp.slide.slide_rect.xs	= 0;
			playVideoOp.slide.slide_rect.ys	= slide_h/2;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye - slide_h/2;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_LEFT)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= 0;
			playVideoOp.slide.slide_rect.ys	= 0;
			playVideoOp.slide.slide_rect.xe	= slide_w;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_RIGHT)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= playVideoOp.slide.main_rect.xe - slide_w;
			playVideoOp.slide.slide_rect.ys	= 0;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_UP)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= 0;
			playVideoOp.slide.slide_rect.ys	= 0;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
			playVideoOp.slide.slide_rect.ye	= slide_h;
		}else if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_DOWN)
		{
			playVideoOp.slide.rect_type   	= SLIDE_RECT_SLIDE;
			playVideoOp.slide.slide_rect.xs	= 0;
			playVideoOp.slide.slide_rect.ys	= playVideoOp.slide.main_rect.ye - slide_h;
			playVideoOp.slide.slide_rect.xe	= playVideoOp.slide.main_rect.xe;
			playVideoOp.slide.slide_rect.ye	= playVideoOp.slide.main_rect.ye;
		}
	}
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideRectDraw
* Description    : taskPlayVideoSlideRectDraw
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoSlideRectDraw(lcdshow_frame_t* displaybuf)
{
	u16 i;
	u8* tar_buf 	= displaybuf->y_addr;
	u8* main_buf 	= playVideoOp.slide.mainBuf;
	u8* slide_buf 	= playVideoOp.slide.slideBuf;
	u16 stride 		= (playVideoOp.slide.main_rect.xe + 0x1f) & ~0x1f;
	playVideoOp.slide.slide_rect.xs = playVideoOp.slide.slide_rect.xs&~3;
	playVideoOp.slide.slide_rect.ys = playVideoOp.slide.slide_rect.ys&~3;
	playVideoOp.slide.slide_rect.xe = playVideoOp.slide.slide_rect.xe&~3;
	playVideoOp.slide.slide_rect.ye = playVideoOp.slide.slide_rect.ye&~3;

	for(i = 0; i < playVideoOp.slide.main_rect.ye; i++) //y addr
	{
		if(i >= playVideoOp.slide.slide_rect.ys && i < playVideoOp.slide.slide_rect.ye)
		{
			if(playVideoOp.slide.rect_type == SLIDE_RECT_SLIDE) //叠加mainbuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
											(void*)main_buf,
											playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xs,
											(void*)slide_buf + playVideoOp.slide.slide_rect.xs,
											playVideoOp.slide.slide_rect.xe - playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xe,
											(void*)main_buf + playVideoOp.slide.slide_rect.xe,
											 playVideoOp.slide.main_rect.xe - playVideoOp.slide.slide_rect.xe);
			}else
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
											(void*)slide_buf,
											playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xs,
											(void*)main_buf + playVideoOp.slide.slide_rect.xs,
											playVideoOp.slide.slide_rect.xe - playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xe,
											(void*)slide_buf + playVideoOp.slide.slide_rect.xe,
											 playVideoOp.slide.main_rect.xe - playVideoOp.slide.slide_rect.xe);
			}
		}else //rect 外区域
		{
			if(playVideoOp.slide.rect_type == SLIDE_RECT_SLIDE) //叠加mainbuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
												(void*)main_buf,
												playVideoOp.slide.main_rect.xe);
			}else //叠加slidebuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
												(void*)slide_buf,
												playVideoOp.slide.main_rect.xe);
			}
		}
		tar_buf 	+= stride;
		main_buf 	+= stride;
		slide_buf 	+= stride;
	}
	tar_buf = displaybuf->uv_addr;
	for(i = 0; i < playVideoOp.slide.main_rect.ye/2; i++) //uv addr
	{
		if(i >= playVideoOp.slide.slide_rect.ys/2 && i < playVideoOp.slide.slide_rect.ye/2)
		{
			if(playVideoOp.slide.rect_type == SLIDE_RECT_SLIDE) //叠加mainbuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
											(void*)main_buf,
											playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xs,
											(void*)slide_buf + playVideoOp.slide.slide_rect.xs,
											playVideoOp.slide.slide_rect.xe - playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xe,
											(void*)main_buf + playVideoOp.slide.slide_rect.xe,
											 playVideoOp.slide.main_rect.xe - playVideoOp.slide.slide_rect.xe);
			}else
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
											(void*)slide_buf,
											playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xs,
											(void*)main_buf + playVideoOp.slide.slide_rect.xs,
											playVideoOp.slide.slide_rect.xe - playVideoOp.slide.slide_rect.xs);
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf + playVideoOp.slide.slide_rect.xe,
											(void*)slide_buf + playVideoOp.slide.slide_rect.xe,
											 playVideoOp.slide.main_rect.xe - playVideoOp.slide.slide_rect.xe);
			}
		}else //rect 外区域
		{
			if(playVideoOp.slide.rect_type == SLIDE_RECT_SLIDE) //叠加mainbuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
												(void*)main_buf,
												playVideoOp.slide.main_rect.xe);
			}else //叠加slidebuf
			{
				hx330x_mcpy0_sdram2gram_nocache((void *)tar_buf,
												(void*)slide_buf,
												playVideoOp.slide.main_rect.xe);
			}
		}
		tar_buf 	+= stride;
		main_buf 	+= stride;
		slide_buf 	+= stride;
	}
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideOpen
* Description    : taskPlayVideoSlideOpen
* Input          : none
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
int taskPlayVideoSlideOpen(void)
{
	if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
		videoPlaybackStop();
	if(SysCtrl.file_cnt < 2)
		return -1;
	app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
#if 1 // 按最大VIDEO SIZE 显示
	hal_lcdGetVideoPos(&playVideoOp.slide.main_rect.xs,&playVideoOp.slide.main_rect.ys);
	hal_lcdGetVideoResolution(&playVideoOp.slide.main_rect.xe,&playVideoOp.slide.main_rect.ye);
#else //按当前ratio显示
	hal_lcdGetVideoRatioPos(&playVideoOp.slide.main_rect.xs,&playVideoOp.slide.main_rect.ys);
	hal_lcdGetVideoRatioResolution(&playVideoOp.slide.main_rect.xe,&playVideoOp.slide.main_rect.ye);
#endif
	playVideoOp.slide.bufsize 	= ((playVideoOp.slide.main_rect.xe + 0x1f)&~0x1f) * playVideoOp.slide.main_rect.ye*3/2;
	playVideoOp.slide.mainBuf    = hal_sysMemMalloc(playVideoOp.slide.bufsize);
	playVideoOp.slide.slideBuf   = hal_sysMemMalloc(playVideoOp.slide.bufsize);
	if(playVideoOp.slide.mainBuf == NULL || playVideoOp.slide.slideBuf == NULL)
	{
		deg_Printf("[SLIDE OPEN] malloc fail\n");
		return -1;
	}
	deg_Printf("[SLIDE OPEN]\n");
	playVideoOp.playMode 		= PLAYVIDEO_SLIDE;
	playVideoOp.slide.playstat	= PLAYVIDEO_SLIDE_STOP;
	playVideoOp.slide.rect_type = 0;
	playVideoOp.slide.step_cur  = 0;
	playVideoOp.slide.step_max  = 10;
	playVideoOp.slide.style     = PLAYVIDEO_SLIDE_AROUND_OPEN;
	playVideoOp.slide.frame_interval  = 2000; //2sec播放一个step
	playVideoOp.slide.playtime  = 0;
	playVideoOp.slide.file_index = SysCtrl.file_index;
	return 0;
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideClose
* Description    : taskPlayVideoSlideClose
* Input          : none
* Output         : none
* Return         : int 0: success, <0 fail
*******************************************************************************/
void taskPlayVideoSlideClose(void)
{
	playVideoOp.playMode 		= PLAYVIDEO_MAIN;
	playVideoOp.slide.playstat	= PLAYVIDEO_SLIDE_STOP;
	hal_sysMemFree(playVideoOp.slide.mainBuf);
	hal_sysMemFree(playVideoOp.slide.slideBuf);
	playVideoOp.slide.mainBuf = playVideoOp.slide.slideBuf = NULL;
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlidePause
* Description    : taskPlayVideoSlidePause
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayVideoSlidePause(void)
{
	if(playVideoOp.slide.playstat == PLAYVIDEO_SLIDE_START)
		playVideoOp.slide.playstat = PLAYVIDEO_SLIDE_PAUSE;
	else if(playVideoOp.slide.playstat == PLAYVIDEO_SLIDE_PAUSE)
		playVideoOp.slide.playstat = PLAYVIDEO_SLIDE_START;
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideStart
* Description    : taskPlayVideoSlideStart
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayVideoSlideStart(void)
{
	lcdshow_frame_t* p_lcd_buffer;
	if(playVideoOp.playMode != PLAYVIDEO_SLIDE)
		return;
	deg_Printf("taskPlayVideoSlideStart:%d\n",playVideoOp.slide.playstat);
	if(playVideoOp.slide.playstat == PLAYVIDEO_SLIDE_STOP)
	{
		//mainBuf:  SysCtrl.file_index
		//slidebuf: playVideoOp.slide.file_index
		deg_Printf("taskPlayVideoSlideStart\n");
		if(taskPlayVideoDecodeToImage(SysCtrl.file_index,playVideoOp.slide.slideBuf,playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye) < 0)
		{
			hal_lcdSetBufYUV_2(playVideoOp.slide.mainBuf,playVideoOp.slide.bufsize,0,0x80);
		}
		do{
			p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
		}while(p_lcd_buffer==NULL);
		hal_lcdVideoFrameFlush(p_lcd_buffer,
								playVideoOp.slide.main_rect.xs,playVideoOp.slide.main_rect.ys,
								playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye,
								playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye);

		hx330x_mcpy0_sdram2gram_nocache((void *)p_lcd_buffer->y_addr, (void*)playVideoOp.slide.slideBuf, playVideoOp.slide.bufsize);
		hal_lcdVideoSetFrame((void *)p_lcd_buffer);

		playVideoOp.slide.file_index = SysCtrl.file_index + 1;
		if(playVideoOp.slide.file_index >= SysCtrl.file_cnt)
		{
			playVideoOp.slide.file_index = 0;
		}
		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,MSG_PLAY_START));
		playVideoOp.slide.playtime = XOSTimeGet();
		playVideoOp.slide.playstat = PLAYVIDEO_SLIDE_START;
		playVideoOp.slide.step_cur = 0;
		playVideoOp.slide.style    = PLAYVIDEO_SLIDE_AROUND_OPEN;
		playVideoOp.slide.step_interval = playVideoOp.slide.frame_interval;

	}
}
/*******************************************************************************
* Function Name  : taskPlayVideoSlideStyleProcess
* Description    : taskPlayVideoSlideStyleProcess
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoSlideService(void)
{
    lcdshow_frame_t* p_lcd_buffer;
	if(playVideoOp.playMode != PLAYVIDEO_SLIDE)
		return;
	if(playVideoOp.slide.playstat != PLAYVIDEO_SLIDE_START)
		return;
	if(XOSTimeGet() - playVideoOp.slide.playtime < playVideoOp.slide.step_interval)
		return;

	if(playVideoOp.slide.file_index != SysCtrl.file_index)
	{
		u8 *tempbuf = playVideoOp.slide.mainBuf;
		playVideoOp.slide.mainBuf = playVideoOp.slide.slideBuf;
		playVideoOp.slide.slideBuf = tempbuf;
		if(taskPlayVideoDecodeToImage(playVideoOp.slide.file_index,playVideoOp.slide.slideBuf,playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye) < 0)
		{
			hal_lcdSetBufYUV_2(playVideoOp.slide.slideBuf,playVideoOp.slide.bufsize,0,0x80);
		}
		SysCtrl.file_index = playVideoOp.slide.file_index;
	}
	do{
		p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
	}while(p_lcd_buffer==NULL);
	hal_lcdVideoFrameFlush(p_lcd_buffer,
						playVideoOp.slide.main_rect.xs,playVideoOp.slide.main_rect.ys,
						playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye,
						playVideoOp.slide.main_rect.xe,playVideoOp.slide.main_rect.ye);
	taskPlayVideoSlideStyleProcess();
	taskPlayVideoSlideRectDraw(p_lcd_buffer);
	if(playVideoOp.slide.step_cur == playVideoOp.slide.step_max)
	{
		playVideoOp.slide.step_cur = 0;
		playVideoOp.slide.style++;
		if(playVideoOp.slide.style == PLAYVIDEO_SLIDE_TYPE_MAX)
			playVideoOp.slide.style = PLAYVIDEO_SLIDE_AROUND_OPEN;

		playVideoOp.slide.step_interval = playVideoOp.slide.frame_interval;
		//deg_Printf("next\n");
		playVideoOp.slide.file_index++;
		if(playVideoOp.slide.file_index >= SysCtrl.file_cnt)
		{
			playVideoOp.slide.file_index = 0;
		}

		XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,MSG_PLAY_START));
	}else
	{
		playVideoOp.slide.step_interval = 1000/playVideoOp.slide.step_max;
	}
	hal_lcdVideoSetFrame((void *)p_lcd_buffer);
	playVideoOp.slide.playtime = XOSTimeGet();
}
/*******************************************************************************
* Function Name  : taskPlayVideoMainStart
* Description    : taskPlayVideoMainStart
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
int taskPlayVideoMainStart(int index)
{
	char * name;
	int  type;

	playVideoOp.playErrIndex = -1;

	//playVideoOp.playTotalTime = 0;
	//playVideoOp.playCurTime   = 0;
	//playVideoOp.playLastTime  = 0;
	if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
		videoPlaybackStop();
	if(SysCtrl.file_cnt < 1)
		goto PLAY_ERROR;
	deg_Printf("[PLAY VIDEO] start: index = %d ",index);
	name = filelist_GetFileFullNameByIndex(playVideoOp.list,index,&type);
	if(name == NULL)
	{
		goto PLAY_ERROR;
	}
	SysCtrl.play_total_time = 0;
	SysCtrl.play_cur_time   = 0;
	SysCtrl.play_last_time  = 0;
    SysCtrl.file_index = index;
    SysCtrl.file_type  = type;
	deg_Printf(": %s\n",name);
	u16 x, y, width, height, dest_w, dest_h;
	if(playVideoOp.playIndex != index) //切换文件，重新设置scaler
	{
		app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
	}
#if 0 //按最大video size显示
	hal_lcdGetVideoPos(&x,&y);
	hal_lcdGetVideoResolution(&width,&height);
	hal_lcdGetVideoResolution(&dest_w,&dest_h);
#else //按实际ratio后的size显示
	hal_lcdGetVideoRatioPos(&x,&y);
	hal_lcdGetVideoRatioResolution(&width,&height);
	hal_lcdGetVideoResolution(&dest_w,&dest_h);
#endif
	if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_JPG)
	{
		lcdshow_frame_t *p_lcd_buffer;
		JPG_DEC_ARG jpg_dec_arg;

		do{
			p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
		}while(p_lcd_buffer==NULL);

		hal_lcdVideoFrameFlush(p_lcd_buffer,x,y,width,height,dest_w,dest_h);
		jpg_dec_arg.type 		= MEDIA_FILE_JPG;
		jpg_dec_arg.wait 		= 1;
		jpg_dec_arg.fd 			= fs_open(name,FA_READ);
		deg_Printf("jpg_dec_arg.fd:%d\n",jpg_dec_arg.fd);
		jpg_dec_arg.src_type	= MEDIA_SRC_FS;
		jpg_dec_arg.dst_width	= width;
		jpg_dec_arg.dst_height  = height;
        jpg_dec_arg.yout  		= p_lcd_buffer->y_addr;
        jpg_dec_arg.uvout 		= p_lcd_buffer->uv_addr;
		jpg_dec_arg.step_yout 	= NULL;
		jpg_dec_arg.p_lcd_buffer = p_lcd_buffer;
	    if(imageDecodeStart(&jpg_dec_arg)<0)
		{
			fs_close(jpg_dec_arg.fd);
			hal_dispframeFree((lcdshow_frame_t *) p_lcd_buffer);

			goto PLAY_ERROR;
		}
		if(playVideoOp.playIndex != index) //切换文件，重新设置scaler
		{
			app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_CENTER); //重新计算scaler
		}
		hal_lcdVideoSetFrame((void *)p_lcd_buffer);


		fs_close(jpg_dec_arg.fd);

		deg_Printf("[PLAY VIDEO]: JPG[%d:%d]\n",jpg_dec_arg.src_width,jpg_dec_arg.src_height);
	}
	else if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_AVI)
	{
		VIDEO_PARG_T video_dec_arg;
		video_dec_arg.avi_arg.src_type = MEDIA_SRC_FS;
		video_dec_arg.avi_arg.fd = (int)fs_open(name,FA_READ);
		if((int)video_dec_arg.avi_arg.fd < 0)
			goto PLAY_ERROR;
		video_dec_arg.pos_x			= x;
		video_dec_arg.pos_y			= y;
		video_dec_arg.tar_width 	= width;
		video_dec_arg.tar_height 	= height;
		video_dec_arg.dest_width    = dest_w;
		video_dec_arg.dest_height   = dest_h;
		video_dec_arg.rotate 		= 0;
		video_dec_arg.firstframe 	= TASK_PLAYVIDEO_STOPFRAME; // pause at first frame waiting user key
		fs_seek(video_dec_arg.avi_arg.fd,0,FA_CREATE_LINKMAP);// enable fast seek for this file
		task_com_sound_wait_end();  // wait key sound end
		if(videoPlaybackStart(&video_dec_arg)<0)
		{
			deg_Printf("[PLAY VIDEO] avi decode fail<0x%x>\n",(int)video_dec_arg.avi_arg.fd);
			fs_close(video_dec_arg.avi_arg.fd);
			goto PLAY_ERROR;
		}
		app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_CENTER); //重新计算scaler
		if(video_dec_arg.firstframe)
		{
			videoPlaybackService();
		}

		//deg_Printf("[%d,%d]\n",width,height);
		videoPlaybackGetTime(&SysCtrl.play_total_time,&SysCtrl.play_cur_time);
		SysCtrl.play_last_time = SysCtrl.play_total_time;
		deg_Printf("[PLAY VIDEO] : AVI[%d:%d],total time = %dms\n",video_dec_arg.avi_arg.width,video_dec_arg.avi_arg.height,SysCtrl.play_total_time);
	}else if((type & FILELIST_TYPE_MASK) == FILELIST_TYPE_SPI)
	{
		lcdshow_frame_t *p_lcd_buffer;
		JPG_DEC_ARG jpg_dec_arg;
		do{
			p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
		}while(p_lcd_buffer==NULL);

		hal_lcdVideoFrameFlush(p_lcd_buffer,x,y,width,height,dest_w,dest_h);
		jpg_dec_arg.type 		= MEDIA_FILE_JPG;
		jpg_dec_arg.wait 		= 1;
		jpg_dec_arg.fd 			= (nv_jpg_open(filelist_GetFileIndexByIndex(playVideoOp.list,index),NVFA_READ) == NV_OK) ? 0 : -1;
		deg_Printf("jpg_dec_arg.fd:%d\n",jpg_dec_arg.fd);
		jpg_dec_arg.src_type	= MEDIA_SRC_NVJPG;
		jpg_dec_arg.dst_width	= width;
		jpg_dec_arg.dst_height  = height;
		jpg_dec_arg.jpgsize		= 0;
        jpg_dec_arg.yout  		= p_lcd_buffer->y_addr;
        jpg_dec_arg.uvout 		= p_lcd_buffer->uv_addr;
		jpg_dec_arg.step_yout 	= NULL;
		jpg_dec_arg.p_lcd_buffer = p_lcd_buffer;
	    if(imageDecodeSpiStart(&jpg_dec_arg)<0)
		{
			nv_jpg_close();
			hal_dispframeFree((lcdshow_frame_t *) p_lcd_buffer);

			goto PLAY_ERROR;
		}
		hal_lcdVideoSetFrame((void *)p_lcd_buffer);


		nv_jpg_close();

		deg_Printf("[PLAY VIDEO]: JPG[%d:%d]\n",jpg_dec_arg.src_width,jpg_dec_arg.src_height);
	}else
	{
		goto PLAY_ERROR;
	}
	playVideoOp.playIndex = index;
	//XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
	//deg_Printf("[PLAY VIDEO] start.<%s>\n",name);
	return type;
PLAY_ERROR:
	playVideoOp.playErrIndex = SysCtrl.file_index;
	XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_PLAY,MSG_PLAY_ERROR));
	return -1;
}



/*******************************************************************************
* Function Name  : taskPlayVideoMainScalerCfg
* Description    : taskPlayVideoMainScalerCfg
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
void taskPlayVideoMainScalerCfg(int step, u8 scaler_type)
{
	if((SysCtrl.file_type & FILELIST_TYPE_MASK) == FILELIST_TYPE_JPG)
	{
		app_lcdVideoShowScaler_cfg(step, scaler_type); //切换文件，重新设置scaler
		taskPlayVideoMainStart(SysCtrl.file_index);
	}else
	{
		app_lcdVideoShowScaler_cfg(step, scaler_type); //切换文件，重新设置scaler
	}

}
/*******************************************************************************
* Function Name  : taskPlayVideoMainOpen
* Description    : taskPlayVideoMainOpen
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoMainOpen(u32 arg)
{
//	u16 x, y, w, h, dest_w, dest_h;
	SysCtrl.play_total_time = 0;
	SysCtrl.play_cur_time   = 0;
	SysCtrl.play_last_time  = 0;
	playVideoOp.playMode = PLAYVIDEO_MAIN;
	playVideoOp.slide.playstat	= PLAYVIDEO_SLIDE_STOP;
	playVideoOp.playErrIndex = -1;
	playVideoOp.playIndex    = -1;
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 2);
	//playVideoOp.playTotalTime = 0;
	//playVideoOp.playCurTime   = 0;
	//playVideoOp.playLastTime  = 0;
	if(SysCtrl.spi_jpg_list >= 0)
	{
		deg_Printf("[PLAY SPI PHOTO] count = %d\n",filenode_api_CountGet(SysCtrl.spi_jpg_list));
		playVideoOp.list = SysCtrl.spi_jpg_list;
	}else
	{
	deg_Printf("[PLAY VIDEOA] count = %d\n",filenode_api_CountGet(SysCtrl.avia_list));
	deg_Printf("[PLAY VIDEOB] count = %d\n",filenode_api_CountGet(SysCtrl.avib_list));
	deg_Printf("[PLAY PHOTO] count = %d\n",filenode_api_CountGet(SysCtrl.jpg_list));
		playVideoOp.list = SysCtrl.avi_list;
	}

	app_lcdShowWinModeCfg(LCDSHOW_WIN_DISABLE);
	//lcdshow_frame_t* p_lcd_buffer;
	//do{
	//	p_lcd_buffer = (lcdshow_frame_t *)hal_lcdVideoIdleFrameMalloc();
	//}while(p_lcd_buffer==NULL);
	//hal_lcdGetVideoRatioPos(&x,&y);
	//hal_lcdGetVideoRatioResolution(&w,&h);
	//hal_lcdGetVideoResolution(&dest_w,&dest_h);

	//hal_lcdVideoFrameFlush(p_lcd_buffer,x,y,w,h,dest_w,dest_h);

	//hal_lcdSetBufYUV(p_lcd_buffer,0,0x80,0x80);
	//hal_lcdVideoSetFrame((void *)p_lcd_buffer);
	task_com_sound_wait_end();  // wait key sound end
	//task_com_usbhost_set(USBHOST_STAT_OUT);
	//husb_api_usensor_detech();
 	task_com_usbhost_set(USBHOST_STAT_SOFT_STOP);

	videoPlaybackInit();
	videoPlaybackSetVolume(100);
	SysCtrl.file_cnt = filelist_api_CountGet(playVideoOp.list);
	if(SysCtrl.file_cnt > 0)
	{
		SysCtrl.file_index = SysCtrl.file_cnt - 1;	
	}
	
	if(SysCtrl.file_cnt > 0)
	{
		taskPlayVideoMainStart(SysCtrl.file_index);
	}
	uiOpenWindow(&playVideoMainWindow, 0, 0);

}
/*******************************************************************************
* Function Name  : taskPlayVideoMainService
* Description    : taskPlayVideoMainService
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoMainService(u32 arg)
{
	if(menuWinIsOpen())
	{
		return;
	}
	if(SysCtrl.file_cnt <= 0)
	{
		return;
	}
	switch(playVideoOp.playMode)
	{
		case PLAYVIDEO_THUMBNALL: 	break;
		case PLAYVIDEO_SLIDE:		taskPlayVideoSlideService(); break;
		case PLAYVIDEO_MAIN:
#if TASK_PLAYVIDEO_AUTOPLAY > 0
		if((videoPlaybackGetStatus() == MEDIA_STAT_STOP) && (SysCtrl.file_type & (FILELIST_TYPE_AVI))&&(SysCtrl.dev_husb_stat != USBHOST_STAT_ASTERN))
		{
	#if (TASK_PLAYVIDEO_AUTOPLAY > 1)
			SysCtrl.file_index++;
			if(SysCtrl.file_index >= SysCtrl.file_cnt)
				SysCtrl.file_index = 0;
	#endif
			if(playVideoOp.playErrIndex != SysCtrl.file_index)
				taskPlayVideoMainStart(SysCtrl.file_index);
		}
#endif
		videoPlaybackGetTime(NULL,&SysCtrl.play_cur_time);
		if((SysCtrl.play_last_time/1000 != SysCtrl.play_cur_time/1000)&&(SysCtrl.play_cur_time!=0))
		{
			SysCtrl.play_last_time = SysCtrl.play_cur_time;
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_TIME_UPDATE,0));
		}
		videoPlaybackService();
		break;
	}
}
/*******************************************************************************
* Function Name  : taskPlayVideoMainService
* Description    : taskPlayVideoMainService
* Input          : int index
* Output         : none
* Return         : none
*******************************************************************************/
static void taskPlayVideoMainClose(u32 arg)
{
	if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
		videoPlaybackStop();
	
	task_com_sound_wait_end(); // wait key sound end
	videoPlaybackUninit();
	task_com_usbhost_set(USBHOST_STAT_SOFT_RESUME);
}



ALIGNED(4) sysTask_T taskPlayVideo =
{
	"Play Video",
	0,
	taskPlayVideoMainOpen,
	taskPlayVideoMainClose,
	taskPlayVideoMainService,
};


