/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskPlayVideoMainWin.c"


/*******************************************************************************
* Function Name  : playVideoMainKeyMsgOk
* Description    : playVideoMainKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(SysCtrl.file_cnt > 0 && playVideoOp.playErrIndex != SysCtrl.file_index)
		{
			if(videoPlaybackGetStatus()== MEDIA_STAT_START)
			{
				deg_Printf("[playvideoOK]--MEDIA_STAT_START\n");
				videoPlaybackPause();
				playVideoSpeedShow(handle);
				playVideoMainPlayPauseShow(handle,1);
				while(videoPlaybackGetStatus() == MEDIA_STAT_START);
				task_com_keysound_play();
			}
			else if(videoPlaybackGetStatus() == MEDIA_STAT_PAUSE)
			{
				deg_Printf("[playvideoOK]--MEDIA_STAT_PAUSE\n");
				task_com_keysound_play();
				task_com_sound_wait_end();
				videoPlaybackResume();
				playVideoSpeedShow(handle);
				playVideoMainPlayPauseShow(handle,0);
			}
			else
			{
				deg_Printf("[playvideoOK]--MEDIA_STAT_ELSE\n");
				task_com_keysound_play();
				task_com_sound_wait_end();
				taskPlayVideoMainStart(SysCtrl.file_index);
			}
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgUp
* Description    : playVideoMainKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
	#if FUN_VIDEO_PLAY_SPEED
		if(videoPlaybackGetStatus() == MEDIA_STAT_START)
		{
			videoPlaybackFastBackward();//videoPlaybackFastForward();
			playVideoSpeedShow(handle);
		}else
	#endif
		if(SysCtrl.file_cnt > 0)
		{
			if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
			{
				videoPlaybackStop();
			}
			task_com_keysound_play();
			task_com_sound_wait_end();
	// #if TASK_PLAYVIDEO_SUB_SET == TASK_PLAYVIDEO_SUB_NONE	
			SysCtrl.file_index--;
			if(SysCtrl.file_index < 0)
				SysCtrl.file_index = SysCtrl.file_cnt - 1;
			taskPlayVideoMainStart(SysCtrl.file_index);
			playVideoMainResolutionShow(handle);
			playVideoMainFileNameShow(handle);
			playVideoMainLockShow(handle);
			playVideoMainPlayTimeShow(handle);
	// #elif TASK_PLAYVIDEO_SUB_SET == TASK_PLAYVIDEO_SUB_SLIDE
	// 		uiOpenWindow(&playVideoSlideWindow, 0, 0);
	// #elif TASK_PLAYVIDEO_SUB_SET == TASK_PLAYVIDEO_SUB_THUMBNALL
	// 		uiOpenWindow(&playVideoThumbnallWindow, 0, 0);
	// #endif
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgDown
* Description    : playVideoMainKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
	#if FUN_VIDEO_PLAY_SPEED
		if(videoPlaybackGetStatus() == MEDIA_STAT_START)
		{
			videoPlaybackFastForward();// videoPlaybackFastBackward();
			playVideoSpeedShow(handle);
		}else
	#endif
		if(SysCtrl.file_cnt > 0)
		{
			if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
			{
                videoPlaybackStop();
			}
			task_com_keysound_play();
			task_com_sound_wait_end();
			SysCtrl.file_index++;
			if(SysCtrl.file_index >= SysCtrl.file_cnt)
				SysCtrl.file_index = 0;
			taskPlayVideoMainStart(SysCtrl.file_index);
			playVideoMainResolutionShow(handle);
			playVideoMainFileNameShow(handle);
			playVideoMainLockShow(handle);
			playVideoMainPlayTimeShow(handle);
		}


	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgUp
* Description    : playVideoMainKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgScalerUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
	#if (1 == LCDSHOW_SCALE_EN)
		if(SysCtrl.file_cnt > 0)
		{	
			if(videoPlaybackGetStatus() == MEDIA_STAT_STOP)
			{
				task_com_keysound_play();
				task_com_sound_wait_end();
			}
			taskPlayVideoMainScalerCfg(1, VIDEO_SCALER_CENTER);
			
		}
	#endif
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgDown
* Description    : playVideoMainKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgScalerDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
	#if (1 == LCDSHOW_SCALE_EN)
		if(SysCtrl.file_cnt > 0)
		{	
			if(videoPlaybackGetStatus() == MEDIA_STAT_STOP)
			{
				task_com_keysound_play();
				task_com_sound_wait_end();
			}
			taskPlayVideoMainScalerCfg(-1, VIDEO_SCALER_CENTER);
			
		}
	#endif
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgMenu
* Description    : playVideoMainKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if((videoPlaybackGetStatus() != MEDIA_STAT_START)&&(SysCtrl.file_cnt>0))
		{
			videoPlaybackStop();
			task_com_keysound_play();
			task_com_sound_wait_end();
            uiOpenWindow(&menuItemWindow, 0, 1,(u32)&MENU(playBack));
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainKeyMsgMode
* Description    : playVideoMainKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoPlaybackGetStatus() != MEDIA_STAT_PLAY)
		{
			 app_taskChange();
			 task_com_keysound_play();
			 task_com_sound_wait_end();
		}
	}
	return 0;
}


static int totaskphotoMode(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{	
		app_taskStart(TASK_RECORD_PHOTO,0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgRotateAdd
* Description    : videoKeyMsgRotateAdd
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgRotateAdd(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(SysCtrl.file_cnt > 0)
		{
			if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
			{
                videoPlaybackStop();
			}
			task_com_keysound_play();
			task_com_sound_wait_end();
			app_lcdVideoShowRotate_cfg(1);
			taskPlayVideoMainStart(SysCtrl.file_index);
			playVideoMainResolutionShow(handle);
			playVideoMainFileNameShow(handle);
			playVideoMainLockShow(handle);
			playVideoMainPlayTimeShow(handle);
		}
		
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgRotateDec
* Description    : videoKeyMsgRotateDec
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainKeyMsgRotateDec(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	u32 item;
	char *name;
	int file_type;
	INT32S list;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		// if(SysCtrl.file_cnt > 0)
		// {
		// 	if(videoPlaybackGetStatus() != MEDIA_STAT_STOP)
		// 	{
        //         videoPlaybackStop();
		// 	}
		// 	task_com_keysound_play();
		// 	task_com_sound_wait_end();
		// 	app_lcdVideoShowRotate_cfg(-1);
		// 	taskPlayVideoMainStart(SysCtrl.file_index);
		// 	playVideoMainResolutionShow(handle);
		// 	playVideoMainFileNameShow(handle);
		// 	playVideoMainLockShow(handle);
		// 	playVideoMainPlayTimeShow(handle);
		// }
			if(app_taskCurId()  == TASK_PLAY_VIDEO)
			{
				if(SysCtrl.spi_jpg_list >= 0)
				{
					list = SysCtrl.spi_jpg_list;
				}else{
					list = SysCtrl.avi_list;
				}
			}else if(app_taskCurId()  == TASK_PLAY_AUDIO)
			{
				list = SysCtrl.wav_list;
			}else
			{
				return 0;
			}
			if(filelist_api_CountGet(list) <= 0)
			{
				task_com_tips_show(TIPS_NO_FILE);
				return -1;
			}
			
			if(filelist_fnameChecklockByIndex(list,SysCtrl.file_index) <= 0) // > 0: lock, 0: AVI and unlock, <0: lock invalid
			{
				name = filelist_GetFileFullNameByIndex(list, SysCtrl.file_index, &file_type);
				deg_Printf("delete : %s.",name);
				if(file_type & FILELIST_TYPE_SPI)
				{
					if(nv_jpgfile_delete(filelist_GetFileIndexByIndex(list,SysCtrl.file_index)) == NV_OK)
					{
						deg_Printf("->ok\n");
						filelist_delFileByIndex(list,SysCtrl.file_index);
						SysCtrl.file_cnt   = filelist_api_CountGet(list);
						SysCtrl.file_index = SysCtrl.file_cnt - 1;
						task_com_tips_show(TIPS_COM_SUCCESS);
					}else
					{
						deg_Printf("->fail\n");
						task_com_tips_show(TIPS_COM_FAIL);
					}
				}else
				{
					if(f_unlink(name)==FR_OK)
					{
						deg_Printf("->ok\n");
						filelist_delFileByIndex(list,SysCtrl.file_index);
						SysCtrl.file_cnt   = filelist_api_CountGet(list);
						SysCtrl.file_index = SysCtrl.file_cnt - 1;

						task_com_sdc_freesize_check();
						task_com_tips_show(TIPS_COM_SUCCESS);
					}
					else
					{
						deg_Printf("->fail\n");
						task_com_tips_show(TIPS_COM_FAIL);
					}
				}
			}
			else
				task_com_tips_show(TIPS_SET_LOCKED);
	}
	return 0;
}
// //无卡拍照
/*******************************************************************************
* Function Name  : playVideoMainSysMsgErr
* Description    : playVideoMainSysMsgErr
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsgPlay(winHandle handle,u32 parameNum,u32* parame)
{
	u32 playState = MSG_PLAY_MAX;
	if(parameNum == 1)
		playState = parame[0];
	if(playState == MSG_PLAY_ERROR)
	{
		hal_lcdSetBufYUV(hal_lcdVideoShowFrameGet(),0,0x80,0x80);
		uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_ERROR_ID),1);
	}

	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSysMsgSD
* Description    : playVideoMainSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[Play Video] : sdc stat ->%d\n",SysCtrl.dev_stat_sdc);

		if(videoPlaybackGetStatus() == MEDIA_STAT_PLAY)
		    videoPlaybackStop();
	SysCtrl.file_cnt = 0;
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 2);
	if(SysCtrl.spi_jpg_list >= 0)
	{
		playVideoOp.list = SysCtrl.spi_jpg_list;
	}else
	{
		playVideoOp.list = SysCtrl.avi_list;
	}
	SysCtrl.file_cnt = filelist_api_CountGet(playVideoOp.list);
	SysCtrl.file_index = SysCtrl.file_cnt - 1;
	if(SysCtrl.file_cnt > 0)
	{
			taskPlayVideoMainStart(SysCtrl.file_index);
			playVideoMainResolutionShow(handle);
			playVideoMainFileNameShow(handle);
			playVideoMainLockShow(handle);
			playVideoMainPlayTimeShow(handle);
	}else
	{
			uiOpenWindow(&noFileWindow, 0, 0);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSysMsgUSB
* Description    : playVideoMainSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	playVideoMainBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSysMsgBattery
* Description    : playVideoMainSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		playVideoMainBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSysMsgTimeUpdate
* Description    : playVideoMainSysMsgTimeUpdate
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsgTimeUpdate(winHandle handle,u32 parameNum,u32* parame)
{
	playVideoMainPlayTimeShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSysMsg1S
* Description    : playVideoMainSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
//	playVideoMainPoweOnTimeShow(handle);
//	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
//	{
//		if(uiWinIsVisible(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID)))
//			uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),0);
//		else
//		{
//			uiWinSetVisible(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),1);
//			uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_BATERRY_ID),R_ID_ICON_MTBATTERY5);
//		}
//	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainOpenWin
* Description    : playVideoMainOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	
	deg_Printf("[WIN]playVideoMainOpenWin\n");
	hal_lcdUiEnable(UI_LAYER0,1);//显示UI
	playVideoOp.playMode = PLAYVIDEO_MAIN;
	uiWinSetResid(winItem(handle,PLAYVIDEOMAIN_MODE_ID), R_ID_ICON_MTPLAY2);
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 2);
	if(SysCtrl.spi_jpg_list >= 0)
	{
		playVideoOp.list = SysCtrl.spi_jpg_list;
	}else
	{
		playVideoOp.list = SysCtrl.avi_list;
	}

	// if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
	// 	{
				
	// 		if(SysCtrl.dev_stat_battery == BATTERY_STAT_1)
	// 		{
	// 				// tips_id = -1;
	// 		}
				
	// 	}

	SysCtrl.file_cnt = filelist_api_CountGet(playVideoOp.list);
	SysCtrl.file_index = SysCtrl.file_cnt - 1;
	playVideoMainPoweOnTimeShow(handle);
	playVideoMainResolutionShow(handle);
	playVideoMainFileNameShow(handle);
	playVideoMainLockShow(handle);
	//playVideoMainMonitorShow(handle);
	//playVideoMainIrLedShow(handle);
	playVideoMainSDShow(handle);
	playVideoMainBaterryShow(handle);
	playVideoMainPlayTimeShow(handle);
	if(SysCtrl.file_cnt <= 0)
	{
		uiOpenWindow(&noFileWindow, 0, 0);
	}


	#if TASK_PLAYVIDEO_SUB_SET == TASK_PLAYVIDEO_SUB_NONE	
			SysCtrl.file_index--;
			if(SysCtrl.file_index < 0)
				SysCtrl.file_index = filelist_api_CountGet(SysCtrl.avi_list) - 1;
				SysCtrl.file_index_next=SysCtrl.file_index;
				SysCtrl.file_index_next_mode=1;
			taskPlayVideoMainStart(SysCtrl.file_index);
			playVideoMainResolutionShow(handle);
			playVideoMainFileNameShow(handle);
			playVideoMainLockShow(handle);
			playVideoMainPlayTimeShow(handle);
	#elif TASK_PLAYVIDEO_SUB_SET == TASK_PLAYVIDEO_SUB_SLIDE
			uiOpenWindow(&playVideoSlideWindow, 0, 0);
	#elif TASK_PLAYVIDEO_SUB_SET == TASK_PLAYVIDEO_SUB_THUMBNALL
			// uiOpenWindow(&playVideoThumbnallWindow, 0, 0);
			if(filelist_api_CountGet(SysCtrl.avi_list) <= 0)
			{
				uiOpenWindow(&noFileWindow, 0, 0);
			}
			else
			{
				uiOpenWindow(&playVideoThumbnallWindow, 0, 0);
			}

	#endif





	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainCloseWin
* Description    : playVideoMainCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playVideoMainCloseWin\n");
	XOSTimeDly(100);
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainWinChildClose
* Description    : playVideoMainWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]playVideoMainWinChildClose\n");
	playVideoOp.playMode = PLAYVIDEO_MAIN;
	task_com_spijpg_Init(0);
	task_com_sdlist_scan(0, 2);
	if(SysCtrl.spi_jpg_list >= 0)
	{
		playVideoOp.list = SysCtrl.spi_jpg_list;
	}else
	{
		playVideoOp.list = SysCtrl.avi_list;
	}
	SysCtrl.file_cnt = filelist_api_CountGet(playVideoOp.list);
	SysCtrl.file_index = SysCtrl.file_cnt - 1;
	if(SysCtrl.file_cnt <= 0)
	{
		uiOpenWindow(&noFileWindow, 0, 0);
		return 0;
	}
	else
	{
		taskPlayVideoMainStart(SysCtrl.file_index);
	}
	playVideoMainPoweOnTimeShow(handle);
	playVideoMainResolutionShow(handle);
	playVideoMainFileNameShow(handle);
	playVideoMainLockShow(handle);
	//playVideoMainMonitorShow(handle);
	//playVideoMainIrLedShow(handle);
	playVideoMainSDShow(handle);
	playVideoMainBaterryShow(handle);
	playVideoMainPlayTimeShow(handle);
	return 0;
}

/*******************************************************************************
* Function Name  : playVideoMainTouchWin
* Description    : playVideoMainTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainTouchWin(winHandle handle,u32 parameNum,u32* parame)
{
/*
parame[0]: widget id;
parame[1]: selected item id(for createItemManage widget)
parame[2]: touch state
*/
	if(parameNum!=3)
	{
		//deg_Printf("playBackTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	//deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
	}
	return 0;
}
/*******************************************************************************
* Function Name  : playVideoMainSlideOff
* Description    : playVideoMainSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int playVideoMainSlideOff(winHandle handle,u32 parameNum,u32* parame)
{
	if(parameNum!=1)
	{
		//deg_Printf("playBackSlidRelease, parame num error %d\n",parameNum);
		return 0;
	}
	if(parame[0] == TP_DIR_UP)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_DOWN,KEY_PRESSED));
	else if(parame[0] == TP_DIR_DOWN)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MENU,KEY_PRESSED));	
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	else if(parame[0] == TP_DIR_LEFT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	return 0;
}
ALIGNED(4) msgDealInfor playVideoMainMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		playVideoMainOpenWin},
	{SYS_CLOSE_WINDOW,		playVideoMainCloseWin},
	{SYS_CHILE_COLSE,		playVideoMainWinChildClose},
	{SYS_TOUCH_WINDOW,      playVideoMainTouchWin},
	{SYS_TOUCH_SLIDE_OFF,   playVideoMainSlideOff},

	{KEY_EVENT_OK,			playVideoMainKeyMsgOk},
	{KEY_EVENT_PHOTO,		playVideoMainKeyMsgOk},

	{KEY_EVENT_POWER,		playVideoMainKeyMsgMenu},
	{KEY_EVENT_UP,			playVideoMainKeyMsgUp},
	{KEY_EVENT_DOWN,		playVideoMainKeyMsgDown},

	{KEY_EVENT_PLAYVIDEO,		playVideoMainKeyMsgMenu},
	{KEY_EVENT_MODE,		playVideoMainKeyMsgMode},
	{KEY_EVENT_PLAYVIDEO,	totaskphotoMode},
	{KEY_EVENT_UVC_FRAME,   playVideoMainKeyMsgScalerUp},
	{KEY_EVENT_UVC_FORM,    playVideoMainKeyMsgScalerDown},
	{KEY_EVENT_ROTATE_ADD,  playVideoMainKeyMsgRotateAdd},
	{KEY_EVENT_ROTATE_DEC,  playVideoMainKeyMsgRotateDec},
	{SYS_EVENT_PLAY,		playVideoMainSysMsgPlay},
	{SYS_EVENT_SDC,			playVideoMainSysMsgSD},
	{SYS_EVENT_USBDEV,		playVideoMainSysMsgUSB},
	{SYS_EVENT_BAT,			playVideoMainSysMsgBattery},
	{SYS_EVENT_1S,			playVideoMainSysMsg1S},
	{SYS_EVENT_TIME_UPDATE,	playVideoMainSysMsgTimeUpdate},
	{EVENT_MAX,NULL},
};

WINDOW(playVideoMainWindow,playVideoMainMsgDeal,playVideoMainWin)


