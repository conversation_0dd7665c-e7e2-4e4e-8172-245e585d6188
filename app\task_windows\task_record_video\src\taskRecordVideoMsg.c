/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskRecordVideoWin.c"


static int videoKeyMsgled(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
	#if 0
		u8 uvcunitsel,request;
		u16 val;
		for(uvcunitsel = PU_BACKLIGHT_COMPENSATION_CONTROL; uvcunitsel < PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL; uvcunitsel++)
		{
			for(request = GET_CUR; request <= GET_RES; request++)
			{
				val = husb_api_usensor_uvcunit_get(uvcunitsel, request);
				deg_Printf("uvcunitsel:%x, request:%x,val:%x\n", uvcunitsel,request,val);
				if(request == GET_CUR)
					husb_api_usensor_uvcunit_set(uvcunitsel, val);
			}
		}
		

	#else
		if(husb_api_usensor_tran_sta())
		{	
			task_com_LedPwm_ctrl(1);
			//recordPhotoLedShow(handle);
		}
	#endif
	}
	return 0;
}

static int videoKeyMsgphoto(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() == MEDIA_STAT_START)
			app_taskRecordVideo_stop();
		// if(SysCtrl.dev_stat_sdc == SDC_STAT_NULL)
		// 	{
		// 		app_taskRecordVideo_stop();
		// 		deg_Printf("[COM] : sdc out--------------\n");
		// 	}
		app_task_rec_Change();


//		if(videoRecordGetStatus() == MEDIA_STAT_START)
//			app_taskRecordVideo_stop();
//		else
//			app_taskRecordVideo_start();
	}
	return 0;
}
static int videoKeyMsgpvideo(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() == MEDIA_STAT_START)
			app_taskRecordVideo_stop();
		app_task_rec_Change();


		// if(SysCtrl.dev_stat_sdc == (SDC_STAT_NULL||SDC_STAT_FULL))
		// {
		// app_taskStart(TASK_RECORD_PHOTO,0);
		// }
//		if(videoRecordGetStatus() == MEDIA_STAT_START)
//			app_taskRecordVideo_stop();
//		else
//			app_taskRecordVideo_start();
	}
	return 0;
}



/*******************************************************************************
* Function Name  : videoKeyMsgOk
* Description    : videoKeyMsgOk
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgOk(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
//		if(videoRecordGetStatus() == MEDIA_STAT_START)
//			app_taskRecordVideo_stop();
//		app_task_rec_Change();
//		if(videoRecordGetStatus() == MEDIA_STAT_START)
//			app_taskRecordVideo_stop();
//		else
//			app_taskRecordVideo_start();
	}
	return 0;
}


static int videoKeyMsgOk_1(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() == MEDIA_STAT_START)
			app_taskRecordVideo_stop();
		// app_task_rec_Change();
		// if(videoRecordGetStatus() == MEDIA_STAT_START)
		// 	app_taskRecordVideo_stop();
		else
			app_taskRecordVideo_start();
	}
	return 0;
}


/*******************************************************************************
* Function Name  : videoKeyMsgUp
* Description    : videoKeyMsgUp
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgUp(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
	// #if 0
	// 	u8 uvcunitsel,request;
	// 	u16 val;
	// 	for(uvcunitsel = PU_BACKLIGHT_COMPENSATION_CONTROL; uvcunitsel < PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL; uvcunitsel++)
	// 	{
	// 		for(request = GET_CUR; request <= GET_RES; request++)
	// 		{
	// 			val = husb_api_usensor_uvcunit_get(uvcunitsel, request);
	// 			deg_Printf("uvcunitsel:%x, request:%x,val:%x\n", uvcunitsel,request,val);
	// 			if(request == GET_CUR)
	// 				husb_api_usensor_uvcunit_set(uvcunitsel, val);
	// 		}
	// 	}
		

	// #else
	// 	app_lcdVideoShowRotate_cfg(1);
	// #endif


	#if (1 == LCDSHOW_SCALE_EN)
		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
		{
			// if(SysCtrl.lcd_scaler_level == 100/LCDSHOW_SCALER_MIN)
			// 	app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
			// else
			// 	app_lcdVideoShowScaler_cfg(1, VIDEO_SCALER_CENTER);
			app_lcdVideoShowScaler_cfg(-1, VIDEO_SCALER_CENTER);
			videoScalerShow(handle);
		}
#endif



	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgDown
* Description    : videoKeyMsgDown
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgDown(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
#if (1 == LCDSHOW_SCALE_EN)
		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
		{
			// if(SysCtrl.lcd_scaler_level == 100/LCDSHOW_SCALER_MIN)
			// 	app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
			// else
			// 	app_lcdVideoShowScaler_cfg(1, VIDEO_SCALER_CENTER);
			app_lcdVideoShowScaler_cfg(1, VIDEO_SCALER_CENTER);
			videoScalerShow(handle);
		}
#endif
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgMenu
* Description    : videoKeyMsgMenu
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgMenu(winHandle handle,u32 parameNum,u32* parame)
{
	return 0;
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() != MEDIA_STAT_START)
		{
			uiOpenWindow(&menuItemWindow,0,1,&MENU(setting));
		}
		else
		{	
		#if FUN_VIDEO_PREVIEW_PAUSE_EN
			if(hal_lcd_pause_sta_get())
			{
				hal_lcd_pause_set(0);
			}else
			{
				hal_lcd_pause_set(1);
			}
		#else
			app_taskRecordVideo_Capture(CMD_PHOTO_RECORD_START);
		#endif
		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgMode
* Description    : videoKeyMsgMode
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgMode(winHandle handle,u32 parameNum,u32* parame)
{
	// return 0;
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() != MEDIA_STAT_START)
			app_taskChange();
		// else  // lock this file
		// {
		// 	if(SysCtrl.dev_stat_gsensorlock)
		// 		SysCtrl.dev_stat_gsensorlock = 0;
		// 	else
		// 		SysCtrl.dev_stat_gsensorlock = 1;
		// 	videoLockShow(handle);
		// }
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgUvcForm
* Description    : videoKeyMsgUvcForm
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgUvcForm(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		if(videoRecordGetStatus() == MEDIA_STAT_START)
			app_taskRecordVideo_stop();
		app_task_rec_Change();
		//if(videoRecordGetStatus() == MEDIA_STAT_START)
		//	app_taskRecordVideo_stop();
//		if(SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
//		{
//			husb_api_usensor_switch_res_kick(1, 0);
//			task_com_tips_show(TIPS_COM_WAITING_2S);
//		}
			
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgUvcForm
* Description    : videoKeyMsgUvcForm
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
extern int lcd_color_flag;
static int videoKeyMsgUvcFrame(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
#if FUN_VIDEO_NOCOLOR_EN
		if (lcd_color_flag == 0) {
			lcd_color_flag = 1;
			dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_NOCOLOR_CHANGE, 0); 
		} else {
			lcd_color_flag = 0;
			dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_NOCOLOR_CHANGE, 1); 
		}
#endif
			
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgRotateAdd
* Description    : videoKeyMsgRotateAdd
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgRotateAdd(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_lcdVideoShowRotate_cfg(1);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoKeyMsgRotateDec
* Description    : videoKeyMsgRotateDec
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoKeyMsgRotateDec(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		app_lcdVideoShowRotate_cfg(-1);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgSD
* Description    : videoSysMsgSD
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgSD(winHandle handle,u32 parameNum,u32* parame)
{
	u32 tips_id;
	if(SysCtrl.dev_stat_sdc != SDC_STAT_NORMAL && videoRecordGetStatus() == MEDIA_STAT_START) // sdc out when recording
	{
		app_taskRecordVideo_stop();
	}
	videoRemainTimeShow(handle);
	videoSDShow(handle);

	task_com_tips_show(TIPS_TYPE_SD);

	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgUSB
* Description    : videoSysMsgUSB
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgUSB(winHandle handle,u32 parameNum,u32* parame)
{
	videoBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : taskComMsgUSBHost
* Description    : taskComMsgUSBHost
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : int <0 fail
*******************************************************************************/
static int videoSysMsgUSBHOST(winHandle handle,u32 parameNum,u32* parame)
{
	videoSensorResShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgBattery
* Description    : videoSysMsgBattery
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgBattery(winHandle handle,u32 parameNum,u32* parame)
{
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_NULL)
		videoBaterryShow(handle);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgRecord
* Description    : videoSysMsgRecord
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgRecord(winHandle handle,u32 parameNum,u32* parame)
{
	u32 type = MSG_RECORD_MAX;
	if(parameNum == 1)
		type = parame[0];
	if(type == MSG_RECORD_MAX)
	{
		return 0;
	}
	else if(type == MSG_RECORD_START)
	{
		SysCtrl.rec_show_time = 0;
		//SysCtrl.dev_stat_gsensorlock = 0;
		videoRecTimeShow(handle);
	}else if(type == MSG_RECORD_STOP || type == MSG_RECORD_ERROR)
	{
		SysCtrl.dev_stat_gsensorlock = 0;
		videoRemainTimeShow(handle);
	}else if(type == MSG_RECORD_RESTART)
	{
//		if(videoRecordGetStatus() != MEDIA_STAT_START)
//			app_taskRecordVideo_start();
	}else if(type == MSG_RECORD_LOCK)
	{
		SysCtrl.dev_stat_gsensorlock = 1;
	}
	videoLockShow(handle);
	if(type == MSG_RECORD_START)
		app_draw_Service(1);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsg1S
* Description    : videoSysMsg1S
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsg1S(winHandle handle,u32 parameNum,u32* parame)
{
	static u32 flag = 0;
	// if(SysCtrl.dev_stat_power & POWERON_FLAG_FIRST)
	// {
	//     if(!(SysCtrl.dev_stat_power & POWERON_FLAG_WAIT))
    //     {
    //         if(SysCtrl.dev_dusb_stat < USBDEV_STAT_DEVIN && SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL && SysCtrl.dev_stat_battery > BATTERY_STAT_2)
    //         {
    //             deg_Printf("power on,start record\n");
    //             XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_RESTART));
    //         }
    //         SysCtrl.dev_stat_power &= ~(POWERON_FLAG_FIRST|POWERON_FLAG_WAIT);
    //     }


	// }
	videoSysTimeShow(handle);
	videoPoweOnTimeShow(handle);
	videoIrLedShow(handle);

	// uiWinSetResid(winItem(handle,VIDEO_CHACK_SD_ID),RAM_ID_MAKE(task_com_sdcCap_str()));
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_NULL)
	{
		videoBaterryShow(handle);
//		if(uiWinIsVisible(winItem(handle,VIDEO_BATERRY_ID)))
//			uiWinSetVisible(winItem(handle,VIDEO_BATERRY_ID),0);
//		else
//		{
//            uiWinSetVisible(winItem(handle,VIDEO_BATERRY_ID),1);
//			uiWinSetResid(winItem(handle,VIDEO_BATERRY_ID),R_ID_ICON_MTBATTERY5);
//		}
	}

//	if(videoRecordGetStatus() == MEDIA_STAT_START)
//	{
//		if(flag&1)
//			dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 1);
//		else
//			dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 0);
//		flag ^= 1;
//	}
	// if(videoRecordGetStatus() != MEDIA_STAT_START){
	// 	app_taskRecordVideo_start();
	// }
	if(SysCtrl.lcd_scaler_level == 0){
		SysCtrl.scaler_0_hide_str_count++;
		if(SysCtrl.scaler_0_hide_str_count>3)
			videoScalerHide(handle);
	}else
	{
		SysCtrl.scaler_0_hide_str_count = 0;
		videoScalerShow(handle);
	}	
	//app_draw_Service(1);
	return 0;
}
/*******************************************************************************
* Function Name  : videoSysMsgTimeUpdate
* Description    : videoSysMsgTimeUpdate
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSysMsgTimeUpdate(winHandle handle,u32 parameNum,u32* parame)
{
	if(videoRecordGetStatus() == MEDIA_STAT_START)
	{
		videoRecTimeShow(handle);
	}else
	{
		videoRemainTimeShow(handle);
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoOpenWin
* Description    : videoOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoOpenWin\n");
	hal_lcdUiEnable(UI_LAYER0,1);//显示UI
	uiWinSetResid(winItem(handle,VIDEO_MODE_ID),R_ID_ICON_MTRECORD);
	videoRemainTimeShow(handle);
	videoPoweOnTimeShow(handle);
	videoResolutionShow(handle);
	videoSensorResShow(handle);
	videoLedShow(handle);
	videoIrLedShow(handle);
	videoLockShow(handle);
	videoSDShow(handle);
	videoMicShow(handle);
	recordRotateShow(handle);
	uiWinSetVisible(winItem(handle,VIDEO_FOCUS_ID),0);
	videoBaterryShow(handle);
	if(SysCtrl.dev_dusb_stat != USBDEV_STAT_PC && SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL && SysCtrl.dev_stat_battery > BATTERY_STAT_2)
	{
		// deg_Printf("power on,start record\n");
		// XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_PHOTO,KEY_PRESSED));
	}

	
	// XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_PHOTO,KEY_PRESSED));

/*	if(SysCtrl.dev_stat_power & POWERON_FLAG_FIRST)
	{
		if(SysCtrl.dev_dusb_stat != USBDEV_STAT_PC && SysCtrl.dev_stat_sdc == SDC_STAT_NORMAL && SysCtrl.dev_stat_battery > BATTERY_STAT_2)
		{
			deg_Printf("power on,start record\n");
			XMsgQPost(SysCtrl.sysQ, makeMSG(SYS_EVENT_RECORD,MSG_RECORD_RESTART));
		}
		SysCtrl.dev_stat_power &= ~POWERON_FLAG_FIRST;

	}*/

	return 0;
}
/*******************************************************************************
* Function Name  : videoCloseWin
* Description    : videoCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoCloseWin\n");
	return 0;
}
/*******************************************************************************
* Function Name  : videoWinChildClose
* Description    : videoWinChildClose
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoWinChildClose(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]videoWinChildClose\n");
	if(videoRecordGetStatus() != MEDIA_STAT_START)
		videoRemainTimeShow(handle);
	videoPoweOnTimeShow(handle);
	videoResolutionShow(handle);
	videoSensorResShow(handle);

	
	
	videoIrLedShow(handle);
	videoLockShow(handle);
	videoSDShow(handle);
	videoMicShow(handle);
	videoBaterryShow(handle);

	return 0;
}
/*******************************************************************************
* Function Name  : videoWinChildOpen
* Description    : videoWinChildOpen
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoWinChildOpen(winHandle handle,uint32 parameNum,uint32* parame)
{
	deg_Printf("[WIN]videoWinChildOpen\n");
	task_com_LedOnOff_ctrl(0);

	//  XOSTimeDly(500);
	//app_taskStart(TASK_RECORD_PHOTO,0);
	//deg_Printf("[WIN]taskoff···························\n");

	return 0;
}



#define SHORT_PRESS_COUNT_THRESHOLD  2
#define LONG_PRESS_COUNT_THRESHOLD   5
static int lcd_rotate_flag ;  // lcd画面旋转标志位
static int key_press_count = 0;  // 按键按下计数器
static int key_is_pressed = 0;   // 按键是否按下标志位
static int short_press_executed = 0; // 短按操作是否已经执行的标志位
static int long_press_triggered = 0; // 长按操作是否已经触发的标志位
static int lcd_zoom_level = 0; // 记录当前 LCD 画面放大档位，共 3 档，范围 0 - 2

static int recordPhotoKeyMsgled(winHandle handle,u32 parameNum,u32* parame)
{
    KEY_STATE_T keyState = KEY_STATE_INVALID;
    int ret;
    if (parameNum == 1)
        keyState = (KEY_STATE_T)parame[0];

    if (keyState == KEY_PRESSED)
    {
        if (!key_is_pressed)
        {
            key_is_pressed = 1;
            key_press_count = 0;
            short_press_executed = 0; // 重置短按执行标志位
            long_press_triggered = 0; // 重置长按触发标志位
        }
        else
        {
            key_press_count++;
        }
    }
	 else if (keyState == KEY_RELEASE)
    {
        if (key_is_pressed)
        {
            key_is_pressed = 0;
            if (key_press_count < SHORT_PRESS_COUNT_THRESHOLD)
            {
               
                // #if FUN_VIDEO_PREVIEW_PAUSE_EN
                //     if (hal_lcd_pause_sta_get())
                //     {
                //         hal_lcd_pause_set(0);
                //         return 0;
                //     }
                // #endif

                // task_com_reshowUI_and_RotateRestore();

						// if (SysCtrl.enter_zoominout_mode)
						// {
                    // #if (1 == LCDSHOW_SCALE_EN)
                    //     if (SysCtrl.dev_husb_stat == USBHOST_STAT_SHOW)
                    //     {
                    //         if (SysCtrl.lcd_scaler_level == 100/LCDSHOW_SCALER_MIN)
                    //         {
                    //             // SysCtrl.enter_zoominout_mode = 0;
                    //             app_lcdVideoShowScaler_cfg(0, VIDEO_SCALER_NONE);
                    //         }
                    //         else
                    //             app_lcdVideoShowScaler_cfg(1, VIDEO_SCALER_CENTER);
                    //         recordPhotoScalerShow(handle);
                    //     }
                    // #endif
               				 // }
              
                    if (husb_api_usensor_tran_sta())
                    {
                        task_com_LedPwm_ctrl(1);
                        videoLedShow(handle);
                        short_press_executed = 1; // 标记短按操作已执行
                    }
                
            }
            key_press_count = 0;
            short_press_executed = 0; // 重置短按执行标志位
            long_press_triggered = 0; // 重置长按触发标志位
        }
    }
    else if (keyState == KEY_CONTINUE)
    {
        if (key_is_pressed)
        {
            key_press_count++;
            if (key_press_count >= LONG_PRESS_COUNT_THRESHOLD && !long_press_triggered)
            {
                // task_com_reshowUI_and_RotateRestore();

             
		if(SysCtrl.mirro_status)
		{
			SysCtrl.mirro_status = 0;
			// app_lcdVideoShowMirro_cfg(1);
			//recordPhotoMirroShow(handle);
			hal_lcdUiEnable(UI_LAYER0,1);
			return 0;
		}		
	SysCtrl.rotate_status^=1;
	if(SysCtrl.rotate_status)
	{
		//hal_lcdUiEnable(UI_LAYER0,0);
		app_lcdVideoShowRotate_cfg(1);
		// lcd_rotate_flag = 1;
		// deg_Printf("in 180++++++++++=%d\n",lcd_rotate_flag); 			
	}else
	{
		app_lcdVideoShowRotate_cfg(1);
		//hal_lcdUiEnable(UI_LAYER0,1);
		// lcd_rotate_flag = 0;
		// deg_Printf("tui 180----------=%d\n",lcd_rotate_flag); 		
	
	}
	 recordRotateShow(handle);
	//  is_screen_flipped = SysCtrl.rotate_status;
	
	deg_Printf("Switch_Photo_lcd_rotate: %d\n", SysCtrl.rotate_status);
               

                key_press_count = 0; // 处理完长按后重置计数器
                long_press_triggered = 1; // 标记长按操作已触发
            }
        }
    }

    recordPhotoOp.upkeystate = keyState;
    return 0;
}



static int videoSysMsg100ms(winHandle handle,uint32 parameNum,uint32* parame)
{	
	u8 uvcunitsel,request;
	//u16 val;
	static u32 pre_val,val,i;
	if(husb_api_usensor_atech_sta())
	{
		pre_val = val;
		val = husb_api_usensor_uvcunit_get(PU_SATURATION_CONTROL, GET_CUR);
		
		//deg_Printf("val[%d],pre_val[%d]\n",val,pre_val);
		// if(pre_val == val)
		// {
		// 	uiWinSetVisible(winItem(handle,VIDEO_FOCUS_ID),0);
		// 	i = 0;
		// }else
		// {
		// 	i++;
		// 	if(i>2)
		// 	uiWinSetVisible(winItem(handle,VIDEO_FOCUS_ID),1);
		// }
	}

	return 0;
}
/*******************************************************************************
* Function Name  : videoTouchWin
* Description    : videoTouchWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoTouchWin(winHandle handle,uint32 parameNum,uint32* parame)
{
/*
parame[0]: widget id;
parame[1]: selected item id(for createItemManage widget)
parame[2]: touch state
*/
	if(parameNum!=3)
	{
		deg_Printf("videoRecordTouchWin, parame num error %d\n",parameNum);
		return 0;
	}
	deg_Printf("ID:%d, item:%d, state:%d\n",parame[0],parame[1],parame[2]);
	if(parame[2] == TOUCH_RELEASE)
	{
//		if(parame[0]== VIDEO_MIC_ID)
//		{
//			if(user_config_get(CONFIG_ID_AUDIOREC) == R_ID_STR_COM_ON)
//			{
//				user_config_set(CONFIG_ID_AUDIOREC,R_ID_STR_COM_OFF);
//				videoRecordCmdSet(CMD_COM_AUDIOEN,0);
//				hal_adc_volume_set(0);
//				hal_adc_volume_setB(0);
//			}
//       	else
//       	{
//				user_config_set(CONFIG_ID_AUDIOREC,R_ID_STR_COM_ON);
//				videoRecordCmdSet(CMD_COM_AUDIOEN,1);
//				hal_adc_volume_set(100);
//				hal_adc_volume_setB(100);
//       	}
//			videoMicShow(handle);
//		}
	}
	return 0;
}
/*******************************************************************************
* Function Name  : videoSlideOff
* Description    : videoSlideOff
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int videoSlideOff(winHandle handle,uint32 parameNum,uint32* parame)
{
	if(parameNum!=1)
	{
		deg_Printf("videoRecordSlidRelease, parame num error %d\n",parameNum);
		return 0;
	}
	//if(parame[0] == TP_DIR_UP)
	//	XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_DOWN,KEY_PRESSED));
	//else 
	if(parame[0] == TP_DIR_DOWN)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MENU,KEY_PRESSED));	
	else if(parame[0] == TP_DIR_RIGHT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_MODE,KEY_PRESSED));
	else if(parame[0] == TP_DIR_LEFT)
		XMsgQPost(SysCtrl.sysQ, makeMSG(KEY_EVENT_OK,KEY_PRESSED));
	return 0;
}

static int videoKeyMsgOkLong(winHandle handle, u32 parameNum, u32 *parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED || keyState == KEY_CONTINUE)
	{
		
	}
	return 0;
}

static int videoKeyMsgPower(winHandle handle,u32 parameNum,u32* parame)
{
	u32 keyState = KEY_STATE_INVALID;
	if(parameNum == 1)
		keyState = parame[0];
	if(keyState == KEY_PRESSED)
	{
		 app_taskStart(TASK_MAIN,0);		// uiOpenWindow(&menuItemWindow,0,1,&MENU(record));
	}
	return 0;
}

ALIGNED(4) msgDealInfor recordVideoMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		videoOpenWin},
	{SYS_CLOSE_WINDOW,		videoCloseWin},
	{SYS_CHILE_COLSE,		videoWinChildClose},
	{SYS_CHILE_OPEN,		videoWinChildOpen},
	{SYS_TOUCH_WINDOW,      videoTouchWin},
	{SYS_TOUCH_SLIDE_OFF,   videoSlideOff},
	{KEY_EVENT_OK,			videoKeyMsgOk},
	{KEY_EVENT_UP,			videoKeyMsgDown},//
	{KEY_EVENT_DOWN,		videoKeyMsgUp},//recordPhotoKeyMsgled
	{KEY_EVENT_MENU,		videoKeyMsgMenu},
	{KEY_EVENT_MODE,		videoKeyMsgMode},
	{KEY_EVENT_UVC_FORM,	videoKeyMsgUvcForm},
	{KEY_EVENT_UVC_FRAME,	videoKeyMsgUvcFrame},
	{KEY_EVENT_ROTATE_ADD,  videoKeyMsgRotateAdd},
	{KEY_EVENT_ROTATE_DEC,  videoKeyMsgRotateDec},
	{KEY_EVENT_PHOTO,  videoKeyMsgOk_1},
	{KEY_EVENT_VIDEO,  videoKeyMsgpvideo},
	{KEY_EVENT_LED,		videoKeyMsgled},
	{KEY_EVENT_PHOTO_LONG, videoKeyMsgOkLong},
	{KEY_EVENT_POWER,		videoKeyMsgPower},
	
	{SYS_EVENT_SDC,			videoSysMsgSD},
	{SYS_EVENT_USBDEV,		videoSysMsgUSB},
	{SYS_EVENT_USBHOST,		videoSysMsgUSBHOST},
	{SYS_EVENT_BAT,			videoSysMsgBattery},
	{SYS_EVENT_RECORD,		videoSysMsgRecord},
	{SYS_EVENT_1S,			videoSysMsg1S},
	{SYS_EVENT_100MS,		videoSysMsg100ms},
	{SYS_EVENT_TIME_UPDATE, videoSysMsgTimeUpdate},
	{EVENT_MAX,				NULL},
};

WINDOW(recordVideoWindow,recordVideoMsgDeal,recordVideoWin)


