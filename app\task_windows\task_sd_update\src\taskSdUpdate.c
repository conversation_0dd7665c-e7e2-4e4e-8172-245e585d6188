/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

ALIGNED(4)  SDUpdate_OP sd_update_op;

/*******************************************************************************
* Function Name  : taskSdUpdateSameCheck
* Description    : taskSdUpdateSameCheck
* Input          :
* Output         : none
* Return         : int :  > 0:  need to updata
						  <= 0 :no need to update
*******************************************************************************/
static int taskSdCheckNeedUpdata(int fd)
{
#if SD_UPDATE_CHECK_SAME
	INT32U mem = 0, bin_mem = 0;
	INT32U cur_checksum, cur_time;
	INT32U bin_checksum, bin_time;
    nv_port_read(4,(INT32U)&mem,4);
	if(mem != 0x52444c42) //'BLDR'
	{
		return 1; //need to updata
	}
	mem = 0;
	nv_port_read(9,(INT32U)&mem,1); //flash para per line(1line = 16bytes)	
	deg_Printf("mem:%x\n", mem);
	mem = mem<<4;
	nv_port_read(mem+0x00,(INT32U)&cur_checksum,4);
	nv_port_read(mem+0x04,(INT32U)&cur_time,4);
	deg_Printf("cur_checksum:%x,cur_time:%x\n", cur_checksum, cur_time);
	if(cur_checksum == 0x33323130 && cur_time == 0x37363534) //default val
	{
		return 2; //need to updata
	}
	if(fs_seek(fd, 4, 0) < 0 || fs_read(fd, (void*)&bin_mem, 4) != 4)
	{
		return -1;
	}
	if(bin_mem != 0x52444c42) //'BLDR'
	{
		return -2;
	}
	bin_mem = 0;
	if(fs_seek(fd, 9, 0) < 0 || fs_read(fd, (void*)&bin_mem, 1) != 1)
	{
		return -3;
	}
	deg_Printf("bin_mem:%x\n", bin_mem);
	bin_mem = bin_mem<<4;
	if(bin_mem != mem)
	{
		return 3; //need to updata
	}
	if(fs_seek(fd, bin_mem, 0) < 0 || fs_read(fd, (void*)&bin_checksum, 4) != 4 || fs_read(fd, (void*)&bin_time, 4) != 4)
	{
		return -4;
	}
	if(fs_seek(fd, 0, 0) < 0)
	{
		return -5;
	}
	deg_Printf("bin_checksum:%x,bin_time:%x\n", bin_checksum, bin_time);
	if(bin_checksum == 0x33323130 && bin_time == 0x37363534)
	{
		return 4; //need to updata
	}
	if(cur_time == bin_time && cur_checksum == bin_checksum) //У������ֵ
	{
		return 0;  // no need to update
	}else
	{
		return 5; //need to updata
	}
#else
	return 1;
#endif
}
/*******************************************************************************
* Function Name  : upgrade
* Description    : upgrade
* Input          :
* Output         : none
* Return         : int : 0: ->upgrade fail
                            -1:->no upgrade file
                           <-1:->upgrade fail,fireware maybe error
*******************************************************************************/
static int taskSdUpdateCheck(void)
{
	int res;

    if(SysCtrl.dev_stat_sdc < SDC_STAT_IN)
    {
		deg_Printf("[SD UPDATE]: no sdc\n");
		return -1;
    }
	deg_Printf("[SD UPDATE]: find update file<%s> -> \n", SD_UPDATE_PATH_NAME);
//-------find update file
	res = fs_open((const char *)SD_UPDATE_PATH_NAME,FA_READ);
	if(res<0)
	{
		deg_Printf("fail\n");
		return -1;
	}
	int check = taskSdCheckNeedUpdata(res);
	deg_Printf("CheckNeedUpdata :%d\n", check);
	//if(taskSdCheckNeedUpdata(res) <= 0)
	if(check <= 0)
	{
		fs_close(res);
		deg_Printf("check same ,no to update\n");
		return -2;
	}
	fs_close(res);
	sd_api_CardState_Set(SDC_STATE_NULL);
	if(sd_api_init(SD_BUS_WIDTH1)>=0) // sdc intial 1line
    {
        res = fs_mount();
		if(res < 0)
		{
			sd_api_CardState_Set(SDC_STATE_NULL);
			return -3;
		}
    }
	res = fs_open((const char *)SD_UPDATE_PATH_NAME,FA_READ);
	if(res<0)
	{
		deg_Printf("fail\n");
		sd_api_CardState_Set(SDC_STATE_NULL);
		return -4;
	}	
#if SD_UPDATE_TYPE
	if(fs_seek(res,0,FA_CREATE_LINKMAP) < 0)// enable fast seek for this file
	{
		deg_Printf("fs create linkmap fail\n");
		sd_api_CardState_Set(SDC_STATE_NULL);
		return -5;	
	}
#endif
    deg_Printf("ok\n");
	
	return res;
}
/*******************************************************************************
* Function Name  : taskSdUpdateProgramPrepare
* Description    : taskSdUpdateProgramPrepare 
* Input          : INT16U idx resource id
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
static void taskSdUpdateProgramEndPrepare(void)
{
	int addr;
	
	JPG_DEC_ARG img_arg;
	WAV_PARA_T	music_arg;
	HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
	

	img_arg.type 		= MEDIA_FILE_JPG;
	img_arg.wait 		= 1;
	img_arg.fd 			= R_ID_IMAGE_POWER_OFF;
	img_arg.src_type	= MEDIA_SRC_NVFS;
	img_arg.dst_width	= sd_update_op.video_w;
	img_arg.dst_height	= sd_update_op.video_h;
	img_arg.yout  		= &sd_update_op.videoBuff[0];
	img_arg.uvout 		= &sd_update_op.videoBuff[sd_update_op.videoYSize];
	img_arg.step_yout 	= NULL;
	img_arg.p_lcd_buffer = NULL;
	//hx330x_sysDcacheWback(sd_update_op.videoBuff,sd_update_op.videoYSize*3/2);
	if(imageDecodeStart(&img_arg)<0)
	{
		//deg_Printf("logo : image decode fail.\n");
		//HAL_CRITICAL_EXIT();
		//return -1;
		sd_update_op.videoBuff = NULL;
	}	
	hx330x_sysDcacheInvalid((u32)sd_update_op.videoBuff,sd_update_op.videoYSize*3/2);
	//hx330x_sysDcacheFlush(sd_update_op.videoBuff,sd_update_op.videoYSize*3/2);
	#if 0
	addr = nv_open(R_ID_MUSIC_POWER_OFF);
	if(addr >= 0)
	{
		music_arg.cachelen = nv_size(R_ID_MUSIC_POWER_OFF);
		music_arg.cachebuf = (u8*)hal_sysMemMalloc(music_arg.cachelen);
		if(music_arg.cachebuf)
		{
			nv_read(addr,(void *)music_arg.cachebuf,music_arg.cachelen);
			music_arg.ch_out	= 1;
			music_arg.type 		= MEDIA_WAV;
			music_arg.src_type 	= MEDIA_SRC_RAM;
			music_arg.volume	= FUN_KEYSOUND_VOLUME;
			music_arg.fd		= R_ID_MUSIC_POWER_OFF;
			if(audioPlaybackParse(&music_arg) == STATUS_OK)
			{
				hal_dacPlayInit(music_arg.samplerate);
				hal_dacSetVolume(music_arg.volume);
				sd_update_op.music_buff 	= music_arg.cachebuf;
				sd_update_op.music_size 	= music_arg.datasize;
				sd_update_op.music_offset	= music_arg.dataoffset;
				//hx330x_sysDcacheWback(sd_update_op.music_buff,sd_update_op.music_size);
			}
		}
	}
	#endif
	HAL_CRITICAL_EXIT();
	
}
/*******************************************************************************
* Function Name  : taskSdUpdateProgramPrepare
* Description    : taskSdUpdateProgramPrepare 
* Input          : INT16U idx resource id
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
static int taskSdUpdateProgramPrepare(void)
{
	sd_update_op.fd = taskSdUpdateCheck();
	if(sd_update_op.fd < 0)
		return -1;
	task_com_sound_wait_end();
	res_music_end();	
	task_com_usbhost_set(USBHOST_STAT_OUT);
	husb_api_usensor_detech();
	app_lcdCsiVideoShowStop();
	hal_csiEnable(0);

	//taskSdUpdate_uiInit();
	hx330x_lcdShowWaitDone();
	//hal_sysMemPrint();
//--free sdram malloc
	hal_dispframeUinit();
//--free key sound buf

	res_keysound_stop();
//
	res_iconBuffInit();
//--free filelist malloc
	filelist_api_nodedestory(SysCtrl.avi_list);
	//filelist_api_nodedestory(SysCtrl.avia_list);
	//filelist_api_nodedestory(SysCtrl.avib_list);
	filelist_api_nodedestory(SysCtrl.jpg_list);
#if (FUN_AUDIO_RECORD_EN == 1)
	filelist_api_nodedestory(SysCtrl.wav_list);
#endif

	SysCtrl.avi_list 	= -1;
	//SysCtrl.avia_list 	= -1;
	//SysCtrl.avib_list 	= -1;
	SysCtrl.jpg_list 	= -1;
	SysCtrl.wav_list 	= -1;
	SysCtrl.mp3_list	= -1;	

//--mem malloc
	//hal_sysMemPrint();
	hal_lcdGetVideoResolution(&sd_update_op.video_w, &sd_update_op.video_h);
	hal_lcdGetUiResolution(&sd_update_op.ui_w,&sd_update_op.ui_h);
	sd_update_op.video_stride = (sd_update_op.video_w + 0x1f)&~0x1f;
	sd_update_op.ofs			= 0;
	sd_update_op.clustsize      = SD_UPDATE_TYPE ? fs_getclusize(sd_update_op.fd) : 0;
	sd_update_op.clbtbl			= SD_UPDATE_TYPE ? fs_getcltbl(sd_update_op.fd) : NULL;
#if SD_UPDATE_TYPE	
	if(sd_update_op.clbtbl == NULL || *sd_update_op.clbtbl == 0)
	{
		deg_Printf("[SD update] clbtal err\n");
		return -3;
	}
#endif
	
	sd_update_op.binsize	 	= fs_size(sd_update_op.fd);
	sd_update_op.binsize_align	= (sd_update_op.binsize + 0x1000 + 0x0fff)&~0xfff;
#if SD_UPDATE_END_SAVE
	sd_update_op.binbuf		 	= hal_sysMemMalloc(SD_UPDATE_TYPE? (SF_SECTOR_SIZE*4):(sd_update_op.binsize + 512));
#else
	sd_update_op.binbuf		 	= hal_sysMemMalloc(SD_UPDATE_TYPE? (SF_SECTOR_SIZE*4):(sd_update_op.binsize));
#endif
	sd_update_op.binverifysize	= SF_SECTOR_SIZE;
	sd_update_op.binverifybuf	= hal_sysMemMalloc(sd_update_op.binverifysize);
	sd_update_op.video_scan_mode= hal_lcdVideoScanModeGet();
	sd_update_op.ui_scan_mode   = hal_lcdUiScanModeGet();
	sd_update_op.videoYSize 	= sd_update_op.video_stride*sd_update_op.video_h;
	sd_update_op.videoBuff 		= hal_sysMemMalloc(sd_update_op.videoYSize*3/2);
	sd_update_op.uiDrawSize		= sd_update_op.ui_w*sd_update_op.ui_h;
	sd_update_op.uiDrawBuff 	= hal_sysMemMalloc(sd_update_op.uiDrawSize);
	
	if(sd_update_op.binbuf == NULL || sd_update_op.binverifybuf == NULL|| sd_update_op.videoBuff == NULL || sd_update_op.uiDrawBuff == NULL)
	{
		hal_sysMemFree(sd_update_op.binbuf);
		hal_sysMemFree(sd_update_op.binverifybuf);
		hal_sysMemFree(sd_update_op.videoBuff);
		hal_sysMemFree(sd_update_op.uiDrawBuff);
		sd_update_op.binbuf = sd_update_op.binverifybuf = sd_update_op.videoBuff = sd_update_op.uiDrawBuff = NULL;
		deg_Printf("[SD UPDATE] malloc buf err\n");
		return -2;
	}
	u32 y_ofs,uv_ofs;
	switch(sd_update_op.video_scan_mode)
	{
		default:
		case LCD_DISPLAY_ROTATE_0://nomal left-top
			y_ofs = uv_ofs = 0;
			break;
			
		case LCD_DISPLAY_ROTATE_270://LCD_DISPLAY_ROTATE_270://LCD_DISPLAY_ROTATE_270://rotate 270
		case LCD_DISPLAY_H_MIRROR://vertical mirror left-down
			y_ofs = (sd_update_op.video_h - 1) * sd_update_op.video_stride;
			uv_ofs = (sd_update_op.video_h / 2 - 1) * sd_update_op.video_stride;
			break;
		
		case LCD_DISPLAY_ROTATE_90://rotate 90
		case LCD_DISPLAY_V_MIRROR://horizontal mirror right-top

			y_ofs = uv_ofs = sd_update_op.video_w - 1;
			break;
			
		case LCD_DISPLAY_ROTATE_180://rotate 180 right-down
			y_ofs  = (sd_update_op.video_h - 1) * sd_update_op.video_stride + sd_update_op.video_w - 1;
			uv_ofs = (sd_update_op.video_h / 2 - 1) * sd_update_op.video_stride + sd_update_op.video_w - 1;
			break;
	}
	sd_update_op.videoYAddr     = &sd_update_op.videoBuff[y_ofs];
	sd_update_op.videoUVAddr 	= &sd_update_op.videoBuff[sd_update_op.videoYSize + uv_ofs];
	//hal_sysMemPrint();
	//hx330x_bytes_memset(sd_update_op.osdBuff,R_ID_PALETTE_DarkGray,sd_update_op.osdSize);
	taskSdUpdate_uiInit();
	taskSdUpdate_uiProgress(0);

	deg_Printf("sd_update scan_mode:%d, %d\n", sd_update_op.video_scan_mode, sd_update_op.ui_scan_mode);
	deg_Printf("sd_update clustsize:%d\n", sd_update_op.clustsize);
	deg_Printf("sd_update w h:%d,%d\n", sd_update_op.video_w,sd_update_op.video_h );
	//if(fs_read_link(sd_update_op.fd,(void*)sd_update_op.binbuf,SF_SECTOR_SIZE) < 0)
	//{
	//	deg_Printf("update: read file fail\n");
	//	return -2;
	//}
#if SD_UPDATE_TYPE == 0
	if(fs_read(sd_update_op.fd ,(void*)sd_update_op.binbuf,sd_update_op.binsize) < 0)
	{
		deg_Printf("update: read file fail\n");
		return -2;
	}
	#if SD_UPDATE_END_SAVE
	hx330x_bytes_memcpy((u8*)(sd_update_op.binbuf + sd_update_op.binsize), (u8*)(sd_update_op.binbuf), 512);
	hx330x_bytes_memset((u8*)(sd_update_op.binbuf),0,512);
	hx330x_sysDcacheWback((u32)sd_update_op.binbuf, sd_update_op.binsize + 512);
	#endif
#else
#if SD_UPDATE_DELETE
	f_unlink(SD_UPDATE_PATH_NAME);
#endif
#endif
//---power off image and music prepare
	taskSdUpdateProgramEndPrepare();
	return 0;
}
/*******************************************************************************
* Function Name  : taskSdUpdateProgram
* Description    : taskSdUpdateProgram
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
SDRAM_TEXT_SECTION
int taskSdGetClst(u32 ofs)
{
	u32 cl, ncl, *tbl;
	tbl = sd_update_op.clbtbl; //[n*2+2]  [ncl] [startcl]........
	if(tbl == NULL|| *tbl++ == 0)
	{
		return 0;
	}
	cl = (ofs / 512 / sd_update_op.clustsize);
	for(;;)
	{
		ncl = *tbl++;			/* Number of cluters in the fragment */
		if (ncl == 0) return 0;	/* End of table? (error) */
		if (cl < ncl) break;	/* In this fragment? */
		cl -= ncl; tbl++;		/* Next fragment */
	}
	return cl + *tbl;	/* Return the cluster number */
}
/*******************************************************************************
* Function Name  : taskSdUpdateProgram
* Description    : taskSdUpdateProgram
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
SDRAM_TEXT_SECTION
int taskSdReadBuf(u8 * buf, u32 len)
{
	u32 remain = sd_update_op.binsize - sd_update_op.ofs;
	
	if (len > remain) 
	{
		hx330x_bytes_memset(buf, 0, len);
		len = remain;		/* Truncate btr by remaining bytes */
	}
	u32 cur_sect, cur_clst, sect_cnt,cur_clst_sect, r_cnt;
	while(len)
	{
		hal_wdtClear();
		cur_clst = taskSdGetClst(sd_update_op.ofs);
		if(cur_clst < 2 || cur_clst == 0xFFFFFFFF)
		{
			return -1;
		}
		cur_sect = fs_getClustStartSector(sd_update_op.fd, cur_clst);
		if(cur_sect == 0)
		{
			return -2;
		}
		cur_clst_sect = (sd_update_op.ofs/512) &(sd_update_op.clustsize - 1);/* Sector offset in the cluster */
		cur_sect += cur_clst_sect;
		sect_cnt = len/512;
		if(sect_cnt == 0)
		{
			return -3;
		}
		if(cur_clst_sect + sect_cnt > sd_update_op.clustsize)
		{
			sect_cnt = sd_update_op.clustsize - cur_clst_sect;
		}
		if(sd_api_Read((void*)buf, cur_sect, sect_cnt) < 0)
		{
			return -4;
		}
		r_cnt = sect_cnt*512;
		len -= r_cnt;
		buf += r_cnt;
		sd_update_op.ofs += r_cnt;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : taskSdUpdataLedShow
* Description    : taskSdUpdataLedShow
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
#if SD_UPDATE_TYPE
SDRAM_TEXT_SECTION
static int taskSdUpdataLedShow(u8 state)
{
	static u8 sta = 0;
	if(hardware_setup.boot_uart_en == 0 && hardware_setup.led_en)
	{
		if(state == 0)
		{
			sta = 0;
		}else if(state == 1)
		{
			sta = 1;
		}else
		{
			sta ^= 1;
		}
		if(hardware_setup.led_valid == 0)
		{
			hal_gpioWrite(hardware_setup.led_ch, hardware_setup.led_pin,sta ? GPIO_LOW:GPIO_HIGH);
		}else
		{
			hal_gpioWrite(hardware_setup.led_ch, hardware_setup.led_pin,sta ? GPIO_HIGH:GPIO_LOW);
		}	
	}	
}
#endif
/*******************************************************************************
* Function Name  : taskSdUpdateProgram
* Description    : taskSdUpdateProgram
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
SDRAM_TEXT_SECTION
static int taskSdUpdateProgram(void)
{
	u32 i, addr, processRate;
#if SD_UPDATE_END_SAVE && SD_UPDATE_TYPE
	u8  updata_end = 0;
#endif
	__LGIE_DIS__();
	__HGIE_DIS__();
#if SD_UPDATE_TYPE
	if(hardware_setup.boot_uart_en == 0)
		dev_ioctrl(SysCtrl.dev_fd_led, DEV_LED_WRITE, 1);
	taskSdUpdataLedShow(1);
#else
	hal_spiUpdata_led_show_init(200);
#endif
	
	
	hal_spiModeSwitch(0, 0);
#if SD_UPDATE_TYPE == 0
	if(hal_spiFlashEraseChip() == false)
	{
		//boot_putchar('A'); //ERASE CHIP FAIL
		addr = 0;
		for(i = 0; (i*SF_SECTOR_SIZE) < sd_update_op.binsize; i++)
		{
			if(hal_spiFlashEraseSector(addr,0) == false)
			{
				boot_putchar('B'); //ERASE SECTOR FAIL
			}
			addr += SF_SECTOR_SIZE;
		}
	}
//-------write
	boot_putchar('W');	
#endif
	addr = 0;
	processRate = 0;
#if SD_UPDATE_TYPE
	i = 0;
	while(1)
	{
		if(i > 3)
		{
			taskSdUpdataLedShow(2);
			i = 0;
		}else
		{
			i++;
		}
		hal_wdtClear();
		//--------read data
		if(taskSdReadBuf(sd_update_op.binbuf,SF_SECTOR_SIZE) < 0)
		{
			boot_putchar('R'); 
			return -1;
		}	
	#if SD_UPDATE_END_SAVE
		if(addr == 0 && updata_end == 0)
		{
			hx330x_bytes_memset(sd_update_op.binbuf, 0, 512);
		}
	#endif
		//hx330x_sysDcacheWback((u32)sd_update_op.binbuf,SF_SECTOR_SIZE);
		//hx330x_sysDcacheFlush((u32)sd_update_op.binbuf,SF_SECTOR_SIZE);
		//-------erase
		if(hal_spiFlashEraseSector(addr,0) == false)
		{
			boot_putchar('E'); //ERASE SECTOR FAIL
			return -2;
		} 
		//-------write
		if(hal_spiFlashWrite(addr,(u32)sd_update_op.binbuf,SF_SECTOR_SIZE, 0) == false)
		{
			boot_putchar('W'); //ERASE SECTOR FAIL
			return -3;
		}	
		
#if SD_UPDATE_VERIFY
		hal_spiFlashReadManual(addr,(u32)(sd_update_op.binverifybuf),SF_SECTOR_SIZE);
		if(hx330x_bytes_cmp(sd_update_op.binverifybuf,sd_update_op.binbuf,SF_SECTOR_SIZE) != 0)
		{
			boot_putchar('V'); //ERASE SECTOR FAIL
			return -4;
		}		
#endif
#if SD_UPDATE_END_SAVE == 0
		if((addr * 100)/sd_update_op.binsize != processRate)
		{
			processRate = (addr * 100)/sd_update_op.binsize;

			boot_putchar('+');
			taskSdUpdate_uiProgress(processRate);
			//boot_putchar('R');
		}
		addr += SF_SECTOR_SIZE;	
		if(addr >= sd_update_op.binsize)
			break;
#else
		if(updata_end == 0)
		{
			if((addr * 100)/sd_update_op.binsize != processRate)
			{
				processRate = (addr * 100)/sd_update_op.binsize;

				boot_putchar('+');
				taskSdUpdate_uiProgress(processRate);
				//boot_putchar('R');
			}
			addr += SF_SECTOR_SIZE;	
		}else
		{
			break;
		}
		if(addr >= sd_update_op.binsize)
		{
			updata_end = 1;
			addr = 0;
			sd_update_op.ofs = 0;
		}
#endif
	}

	taskSdUpdataLedShow(0);
#else
	while(1)
	{
		if(hal_spiFlashWrite(addr,(u32)(&sd_update_op.binbuf[addr]),SF_SECTOR_SIZE, 0) == false)
		{
			boot_putchar('W');
			return -1;
		}
		addr += SF_SECTOR_SIZE;
	#if SD_UPDATE_VERIFY
		if((addr * 50)/sd_update_op.binsize != processRate)
		{
			processRate = (addr * 50)/sd_update_op.binsize;
	#else
		if((addr * 100)/sd_update_op.binsize != processRate)

		{
			processRate = (addr * 100)/sd_update_op.binsize;
	#endif
			boot_putchar('+');
			taskSdUpdate_uiProgress(processRate);
			//boot_putchar('R');
		}
		if(addr >= sd_update_op.binsize)
			break;
	}
//-----verify
	#if SD_UPDATE_VERIFY
		//boot_putchar('V');
		addr = 0;
		processRate = 0;
		hal_spiAutoModeInit();
		while(1)
		{
			hal_spiFlashRead(addr,(u32)(sd_update_op.binverifybuf),sd_update_op.binverifysize);
			hal_spiUpdata_led_show(2);
			if(hx330x_bytes_cmp(sd_update_op.binverifybuf,&sd_update_op.binbuf[addr],sd_update_op.binverifysize) != 0)
			{
				boot_putchar('V');
				return -2;
			}
			addr += sd_update_op.binverifysize;
			if((addr * 50)/sd_update_op.binsize != processRate)
			{
				boot_putchar('-');
				processRate = (addr * 50)/sd_update_op.binsize;
				taskSdUpdate_uiProgress(50+processRate);
			}
			if(addr >= sd_update_op.binsize)
				break;
		}
	#endif
	#if SD_UPDATE_END_SAVE
		hx330x_bytes_memcpy((u8*)(sd_update_op.binbuf), (u8*)(sd_update_op.binbuf + sd_update_op.binsize), 512);
		hx330x_sysDcacheWback((u32)sd_update_op.binbuf, 512);
		hal_spiModeSwitch(0, 0);
		if(hal_spiFlashEraseSector(0,0) == false)
		{
			boot_putchar('E'); //ERASE SECTOR FAIL
			return -3;
		}
		if(hal_spiFlashWrite(0,(u32)(&sd_update_op.binbuf[0]),SF_SECTOR_SIZE, 0) == false)
		{
			boot_putchar('W');
			return -4;
		}	
		#if SD_UPDATE_VERIFY
			hal_spiAutoModeInit();
			hal_spiFlashRead(0,(u32)(sd_update_op.binverifybuf),sd_update_op.binverifysize);
			if(hx330x_bytes_cmp(sd_update_op.binverifybuf,&sd_update_op.binbuf[0],sd_update_op.binverifysize) != 0)
			{
				boot_putchar('V');
				return -5;
			}
		#endif

	#endif
#if SD_UPDATE_DELETE
	f_unlink(SD_UPDATE_PATH_NAME);
#endif
	hal_spiUpdata_led_show_uinit();
#endif

	
	boot_putchar('O');
	boot_putchar('K');
	return 0;

}

/*******************************************************************************
* Function Name  : taskSdUpdateProgramEnd
* Description    : taskSdUpdateProgramEnd
* Input          : 
* Output         : none                                            
* Return         : 
*******************************************************************************/
SDRAM_TEXT_SECTION
static void taskSdUpdateProgramEnd(void)
{
	hx330x_sysCpuMsDelay(10);
	hx330x_lcdShowWaitDone();
	
	//hal_lcdUiEnable(UI_LAYER0,0);
	//if(LCD_IF_IS_MCU())	
    //	hx330x_lcdKick();
	////hx330x_lcdShowKick();
	//hx330x_sysCpuMsDelay(10);
	//hx330x_lcdShowWaitDone();
//-- power off img and music show
	if(sd_update_op.videoBuff)
	{
		hx330x_lcdVideoSetStride(sd_update_op.video_stride ,sd_update_op.video_stride );
		hx330x_lcdVideoSetScanMode(sd_update_op.video_scan_mode);
		hx330x_lcdvideoSetPosition(0,0);
		hx330x_lcdVideoUpScalerSoftRotate_cfg(sd_update_op.video_w,sd_update_op.video_h,sd_update_op.video_w,sd_update_op.video_h,sd_update_op.video_scan_mode);
		hx330x_lcdVideoSetAddr((u32)sd_update_op.videoYAddr, (u32)sd_update_op.videoUVAddr);
		hx330x_lcdShowKick();
		if(LCD_IF_IS_MCU())	
    		hx330x_lcdKick();
		hx330x_sysCpuMsDelay(10);
		hx330x_lcdShowWaitDone();
	}
	hal_lcdUiEnable(UI_LAYER0,0);
	if(LCD_IF_IS_MCU())	
    	hx330x_lcdKick();
	if(sd_update_op.music_buff)
	{
		hx330x_dacStart(sd_update_op.music_offset,sd_update_op.music_size);
		hx330x_check_dacstop();
	}
	hx330x_sysCpuMsDelay(200);
	hx330x_lcdShowWaitDone();


//  power off
	exception_lowpower_io_cfg();
	hal_wkiWakeupTriger(1); //wki wakeup rising trigger
	hal_wkiCleanPending();
	hal_vddWKOEnable(0);

	hx330x_sysCpuMsDelay(200);


	while(1);
	
}
/*******************************************************************************
* Function Name  : upgrade
* Description    : upgrade
* Input          : 
* Output         : none                                            
* Return         : int : 0: ->upgrade fail
                            -1:->no upgrade file
                           <-1:->upgrade fail,fireware maybe error
*******************************************************************************/
SDRAM_TEXT_SECTION
int taskSdUpdateProcess(void)
{
	if(taskSdUpdateProgramPrepare() < 0)
		return -1;
	taskSdUpdateProgram();
	taskSdUpdateProgramEnd();
	return 0;
}

static void taskSDUpdateOpen(u32 arg)
{
	taskSdUpdateProcess();
}

ALIGNED(4) sysTask_T taskSDUpdate =
{
	"SD Update",
	0,
	taskSDUpdateOpen,
	NULL,
	NULL,
};







