/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"

/*******************************************************************************
* Function Name  : taskRecordWinChangeProcess
* Description    : taskRecordWinChangeProcess function.
* Input          : 
* Output         : none                                            
* Return         : int fd : file handle
*******************************************************************************/
static void taskSettingWinChangeProcess(u8 enter)
{
	// if(SysCtrl.winChangeEnable == 0)
	// 	return;
	// if(enter)
	// {
	// 	taskMainWinInit(0,MEDIA_SRC_NVFS, R_ID_IMAGE_MAIN_BACKGROUND, 0,MAIN_TO_SUB_VOR_UP);
	// 	if(taskWinChangeProcess() < 0)
	// 	{
	// 		res_image_show(R_ID_IMAGE_MAIN_BACKGROUND, 0);
	// 	}
	// }else
	// {
	// 	taskMainWinInit(0,MEDIA_SRC_NVFS, R_ID_IMAGE_MAIN_BACKGROUND, 0,SUB_TO_MAIN_VOR_DOWN);
	// }
}
/*******************************************************************************
* Function Name  : taskSettingOpen
* Description    : taskSettingOpen function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskSettingOpen(u32 arg)
{
	deg_Printf("[SETTING] taskSettingOpen called\n");
	app_lcdCsiVideoShowStop();
	taskSettingWinChangeProcess(1);
	deg_Printf("[SETTING] opening setting menu\n");
	uiOpenWindow(&menuItemWindow,0,1,&MENU(setting));
}
/*******************************************************************************
* Function Name  : taskSettingClose
* Description    : taskSettingClose function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskSettingClose(uint32 arg)
{
	taskSettingWinChangeProcess(0);
}
/*******************************************************************************
* Function Name  : taskSettingService
* Description    : taskSettingService function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskSettingService(uint32 arg)
{


}

ALIGNED(4) sysTask_T taskSetting =
{
	"Setting",
	0,
	taskSettingOpen,
	taskSettingClose,
	taskSettingService,
};


