/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
#include "taskShowLogoWin.c"





/*******************************************************************************
* Function Name  : ShowLogoOpenWin
* Description    : ShowLogoOpenWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int ShowLogoOpenWin(winHandle handle,u32 parameNum,u32* parame)
{
	uiRect rect;
	task_com_lcdbk_set(1);
	taskSdUpdateProcess();

	
	app_logo_show(0,0,1);

	return 0;
}
/*******************************************************************************
* Function Name  : ShowLogoCloseWin
* Description    : ShowLogoCloseWin
* Input          : winHandle handle,u32 parameNum,u32* parame
* Output         : none
* Return         : none
*******************************************************************************/
static int ShowLogoCloseWin(winHandle handle,u32 parameNum,u32* parame)
{
	deg_Printf("[WIN]ShowLogoCloseWin\n");
	return 0;
}


static int ShowLogoSysMsg100MS(winHandle handle,uint32 parameNum,uint32* parame)
{
 	static u8 i;
	u32 sta;	
	husb_api_usensor_linkingLcd();	
	sta = husb_api_devicesta(SysCtrl.dev_husb_ch);
	if(SysCtrl.dev_dusb_stat == USBDEV_STAT_DEVIN && i == 25)
	{
		app_taskStart(TASK_USB_DEVICE,0);
		return 0;
	}
	if(sta & USB_UVC_TRAN)
	{
		task_com_service(0);
		//taskSdUpdateProcess();
		task_com_USB_CS_DM_DP_status_select(1);	
		app_taskStart(TASK_RECORD_PHOTO,0);		
		return 0;		
	}
	if(i++ > 30) //ms 大于3S还不出图那也进拍照模式
	{
		//taskSdUpdateProcess();
		task_com_USB_CS_DM_DP_status_select(1);	
		app_taskStart(TASK_RECORD_PHOTO,0);
	}
	return 0;
}

ALIGNED(4) msgDealInfor ShowLogoMsgDeal[]=
{
	{SYS_OPEN_WINDOW,		ShowLogoOpenWin},
	{SYS_CLOSE_WINDOW,		ShowLogoCloseWin},
	// {SYS_CHILE_COLSE,		ShowLogoWinChildClose},
	//{KEY_EVENT_POWEROFF,    ShowLogoKeyMsgPowerOff},
	//{KEY_EVENT_OK,			ShowLogoKeyMsgOK},
	//{KEY_EVENT_UP,			ShowLogoKeyMsgCommon},
	//{KEY_EVENT_DOWN,		ShowLogoKeyMsgCommon},
	//{KEY_EVENT_LEFT,		ShowLogoKeyMsgCommon},
	//{KEY_EVENT_RIGHT,		ShowLogoKeyMsgCommon},
	//{SYS_EVENT_USBDEV,		ShowLogoSysMsgUSB},
//	{SYS_EVENT_BAT,			ShowLogoSysMsgBattery},
	//{SYS_EVENT_TIME_UPDATE,	ShowLogoSysMsgTimeUpdate},
	{SYS_EVENT_100MS,		ShowLogoSysMsg100MS},
	{EVENT_MAX,NULL},
};

WINDOW(ShowLogoWindow,ShowLogoMsgDeal,ShowLogoWin)


