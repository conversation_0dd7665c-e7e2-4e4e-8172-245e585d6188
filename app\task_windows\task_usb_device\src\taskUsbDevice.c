/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../app_common/inc/app_api.h"
ALIGNED(4) USBDEVICE_OP_T  usbDeviceOp;
/*******************************************************************************
* Function Name  : taskUsbDeviceOpen
* Description    : taskUsbDeviceOpen function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskUsbDeviceOpen(u32 arg)
{
	deg_Printf("taskUsbDeviceOpen>>>>>>>>>>>>>>>>\n\n\n\n");
	u32 usb_mode;
    SysCtrl.dev_stat_power &= ~(POWERON_FLAG_FIRST|POWERON_FLAG_WAIT);
	usbDeviceOp.cur_sel_index 		= 0;
	usbDeviceOp.usb_process_flag 	= 0;
	usbDeviceOp.cur_page_num		= 0;
	task_com_usbhost_set(USBHOST_STAT_OUT);
	husb_api_usensor_detech();
	app_lcdShowWinModeCfg(LCDSHOW_WIN_DISABLE);
	res_image_show(R_ID_IMAGE_USB_MODE);
	usb_mode = USB_DEVTYPE_MSC;
	task_com_sound_wait_end();;

	dusb_api_Init(usb_mode);
	usbDeviceOp.usb_process_flag = 1;

	uiOpenWindow(&usbDeviceWindow,0,0);
}
/*******************************************************************************
* Function Name  : taskUsbDeviceClose
* Description    : taskUsbDeviceClose function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskUsbDeviceClose(uint32 arg)
{
	task_com_usbhost_set(USBHOST_STAT_NULL);
	hal_lcdUiEnable(UI_LAYER0,1);
	deg_Printf("taskUsbDeviceClose\n");
	filelist_api_nodedestory(SysCtrl.avi_list);
	filelist_api_nodedestory(SysCtrl.avia_list);
	filelist_api_nodedestory(SysCtrl.avib_list);
	filelist_api_nodedestory(SysCtrl.jpg_list);
	filelist_api_nodedestory(SysCtrl.wav_list);
	
	SysCtrl.avi_list  = -1;
	SysCtrl.avia_list = -1;
	SysCtrl.avib_list = -1;
	SysCtrl.jpg_list  = -1;
	SysCtrl.wav_list  = -1;
	task_com_fs_scan();	
}
/*******************************************************************************
* Function Name  : taskUsbDeviceService
* Description    : taskUsbDeviceService function.
* Input          : 
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void taskUsbDeviceService(uint32 arg)
{
	if(usbDeviceOp.usb_process_flag)
	{
		if(false == dusb_api_Process())
		{
			deg_Printf("usb update\n");
			SysCtrl.dev_stat_lcd = 0;
			dev_ioctrl(SysCtrl.dev_fd_lcd, DEV_LCD_BK_WRITE, 0); // back light off
			//dusb_api_Uninit();
			app_taskStart(TASK_USB_UPDATE,0);
			//app_taskStart(TASK_POWER_OFF,0);
		}		
	}

}

ALIGNED(4) sysTask_T taskUSBDevice =
{
	"usb device",
	0,
	taskUsbDeviceOpen,
	taskUsbDeviceClose,
	taskUsbDeviceService,
};


