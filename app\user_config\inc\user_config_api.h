/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  USER_CONFIG_API_H
    #define  USER_CONFIG_API_H
	
#include "user_config_typedef.h"

//用户需要根据自己的板子的硬件接口来include对应的 “user_hardware_cfg_xxx.h” 文件
#if CURRENT_CHIP == FPGA
	#include "user_hardware_cfg_fpga.h"
#else
	#include "user_hardware_cfg_hx3303_demo.h"
	//#include "user_hardware_cfg_hx3308_demo.h"
#endif

//---------file dir CFG--------------------------------------------------------------------------------------------------
#define  FILEDIR_RECA 			    "RECA/"
#define  FILEDIR_RECB 			    "RECB/"
#define  FILEDIR_REC 				"REC/"
#define  FILEDIR_IMG  			    "IMG/"
#define  FILEDIR_WAV  			    "WAV/"
//--------------RESOURCE LOAD CFG-----------------------------------------------------------------------------------------
#define  VERSION_LOAD_AUTO     		0   				  	//定义软件版本是否自动从资源获取
#define  SYSTEM_VERSION     	 	"YMX-MSO2_20250714"  	//当定义 VERSION_LOAD_AUTO为0时，软件版本由此宏定义

#define  KEYSOUND_LOAD_AUTO    		1  						// 定义是否从资源load按键音

#define  DATETIME_LOAD_AUTO    		1  						// 定义是否从资源获取默认的系统时间


//----------------------SYS FUNCTION CFG----------------------------------------------------------------------------------
//-------lcd  show cfg-----
#define  UI_SHOW_SMALL_PANEL		0//当屏幕尺寸小于320*240时，去除MENU显示右侧子项
 //1: 支持LCD屏显画面放大缩小; 0: 不支持LCD缩放
#define  LCDSHOW_SCALE_EN      		1
#define  LCDSHOW_SCALER_STEP		20//缩放比例百分比
#define  LCDSHOW_SCALER_MIN			50//最大缩放尺寸，为屏幕分辨率的百分比
//可配置在原有屏的基础上VIDEO层旋转：(暂不支持动态旋转)
//LCD_DISPLAY_ROTATE_NONE / LCD_DISPLAY_ROTATE_0 ：保持不变
//LCD_DISPLAY_ROTATE_90 ：逆时针旋转90度
//LCD_DISPLAY_ROTATE_180 ：逆时针旋转180度
//LCD_DISPLAY_ROTATE_270 ：逆时针旋转270度
//LCD_DISPLAY_MIRROR_NONE : 保持不变
//LCD_DISPLAY_V_MIRROR ： 水平镜像
//LCD_DISPLAY_H_MIRROR ：垂直镜像
#define  LCDSHOW_VIDEO_ROTATE_MORE_MODE		(LCD_DISPLAY_ROTATE_NONE |LCD_DISPLAY_MIRROR_NONE) 

//可配置在原有屏的基础上UI层旋转：(注意客户需要自行考虑UI)
//LCD_DISPLAY_ROTATE_NONE / LCD_DISPLAY_ROTATE_0 ：保持不变
//LCD_DISPLAY_ROTATE_90 ：逆时针旋转90度
//LCD_DISPLAY_ROTATE_180 ：逆时针旋转180度
//LCD_DISPLAY_ROTATE_270 ：逆时针旋转270度
#define  LCDSHOW_UI_ROTATE_MORE_MODE		(LCD_DISPLAY_ROTATE_NONE) 

//配置缩放比例，配置为0是表示最大比例缩放，即填满全屏 (注意客户需要自行考虑UI)
#define  LCDSHOW_RATIO_CFG			LCD_RATIO_MAKE(0, 0) 

//-------FUN CFG------------
#define  FUN_AUTOPOWEROFF_TIME		0 // 定义电池供电情况下，自动关机时间，单位：秒

#define  FUN_MOTION_DEC_TIME    	20 // 定义动态侦测启动录像时，录像时间长度，单位：秒

#define  FUN_KEYSOUND_VOLUME    	100 // 定义按键音量，范围： [0-100]

#define  FUN_AUDIO_RECORD_EN       	0	//1: 打开录音和播放录音任务

#define  FUN_VIDEO_SYNC_WRITE		0   //1: 定义录像文件定时回写，用于录像过程意外掉电时，录像文件仍旧能正常播放

#define  FUN_VIDEO_PLAY_SPEED		1   //1: 支持文件快进或快退播放

#define  FUN_VIDEO_PREVIEW_PAUSE_EN	1 // 1: 支持预览时暂停LCD画面		
#define  TASK_SCAN_FILE_EVERY_TIME  0

//-------------DEV CFG(battery, gsensor,ir,key,lcd,led,sd,sensor,usb...)---------------------------------------------------
//-------dev enable and dev io cfg, please check and modify file"dev/dev_api.h"

//配置USB HOST相关的一些配置

//配置没有接USB设备时LCD显示的图案
//可选： CSI_TEST_WHITE , CSI_TEST_YELLOW , CSI_TEST_CYAN , CSI_TEST_GREEN ,  CSI_TEST_MAGENTA, 
//      CSI_TEST_RED,   CSI_TEST_BLUE, CSI_TEST_BLACK, CSI_TEST_COLORBARS,  CSI_TEST_SQUARE
#define USB_NONE_TEST_PATTERN		CSI_TEST_BLACK

//配置接USB设备时，优先识别的 UVC格式和分辨率(如果满足，则会优先显示该格式和分辨率)
#define USB_PRIOR_FRAME				UVC_FORMAT_MJP	//UVC_FORMAT_MJP ,  UVC_FORMAT_YUV
#if HAL_MJP_SAVE_CARD_DIRECT == 0
#define USB_PRIOR_RESOLUTION		UVC_FRAME_VGA	//UVC_FRAME_1080P, UVC_FRAME_720P, UVC_FRAME_VGA, UVC_FRAME_QVGA
#else
#define USB_PRIOR_RESOLUTION		UVC_FRAME_720P	//UVC_FRAME_1080P, UVC_FRAME_720P, UVC_FRAME_VGA, UVC_FRAME_QVGA
#endif

#define  DEV_SDC_TIMEOUT			2000//定义SD卡读写出错超时时间，单位ms。在录像过程中，拔卡响应时间，建议配置1000ms

#define USER_UI_MENU_ROUNDRECT		1
#define USER_UI_MENU_RIMCOLOR		R_ID_PALETTE_White

#define FUN_VIDEO_NOCOLOR_EN		0 // 1: 支持按键切换彩色和黑白


#define FUN_TEMPERATURE_SHOW				1
#define T_70								70
#define T_75								75
#define T_85								85	


#define	ENCRYPT_FUNC_SWITCH					1	//加密功能开关 0：关 		1：开


#define LED_LEVEL_3_DUTY							70	
#define LED_LEVEL_2_DUTY							40//50
#define LED_LEVEL_1_DUTY							10
#define LED_LEVEL_0_DUTY							100


#define CLOSE_POWERONOFF_SOUND 1

#endif




