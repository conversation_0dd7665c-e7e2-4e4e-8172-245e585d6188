/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  USER_CONFIG_TYPEDEF_H
    #define  USER_CONFIG_TYPEDEF_H

typedef struct USER_CFG_TAB_S{
	u32 cfg_id;
	u32 cfg_value;
}USER_CFG_TAB_T;

typedef struct SYSTEM_FLAG_S
{
	INT32U flag[127];	// must  CFG_ID_MAX < 127 
	INT32U CheckSum;
}SYSTEM_FLAG_T;		// size must 256 aligned 

extern const USER_CFG_TAB_T user_cfg_tab[];
typedef enum
{
	CONFIG_ID_YEAR=0,
	CONFIG_ID_MONTH,
	CONFIG_ID_MDAY,
	CONFIG_ID_WDAY,
	CONFIG_ID_HOUR,
	CONFIG_ID_MIN,
	CONFIG_ID_SEC,
    CONFIG_ID_LANGUAGE,
    CONFIG_ID_AUTOOFF,    
    CONFIG_ID_SCREENSAVE,	// 10
    CONFIG_ID_FREQUNCY,
    CONFIG_ID_ROTATE,
    CONFIG_ID_FILLIGHT,
	CONFIG_ID_RESOLUTION,
	CONFIG_ID_TIMESTAMP,
	CONFIG_ID_MOTIONDECTION,
	CONFIG_ID_PARKMODE,
	CONFIG_ID_GSENSOR,
	CONFIG_ID_KEYSOUND,
	CONFIG_ID_IR_LED,		// 20
    CONFIG_ID_LOOPTIME,  
    CONFIG_ID_AUDIOREC,  
    CONFIG_ID_EV,
    CONFIG_ID_WBLANCE,
	CONFIG_ID_PRESLUTION,
	CONFIG_ID_PFASTVIEW,  
	CONFIG_ID_PTIMESTRAMP,
	CONFIG_ID_PEV,
    CONFIG_ID_VOLUME,
    CONFIG_ID_THUMBNAIL,	// 30
    CONFIG_ID_GSENSORMODE,
    CONFIG_ID_MAX
}CONFIG_ID_E;

/*******************************************************************************
* Function Name  : user_config_set
* Description    : set configure value
* Input          : u32 configId : configure id
*                  u32 value    : configure value
* Output         : None
* Return         : none
*******************************************************************************/
void user_config_set(u32 configId,u32 value);
/*******************************************************************************
* Function Name  : userCofigGetValue
* Description    : get configure value in configure table
* Input          : u8_t configId : configure id
* Output         : None
* Return         : u32_t : configure value
*******************************************************************************/
u32 user_config_get(u32 configId);
/*******************************************************************************
* Function Name  : user_config_save
* Description    : save user configure value to spi flash
* Input          : none
* Output         : None
* Return         : s32_t 
*                    0 : always
*******************************************************************************/
void user_config_save(void);
/*******************************************************************************
* Function Name  : userConfigReset
* Description    : reset or default user configure value
* Input          : none
* Output         : None
* Return         : s32_t 
*                    0 : always
*******************************************************************************/
void userConfig_Reset(void);
/*******************************************************************************
* Function Name  : userConfig_Init
* Description    : initial user configure value
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void userConfig_Init(void);
/*******************************************************************************
* Function Name  : userConfig_Init
* Description    : initial user configure value
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
u32 userConfigInitial(void);
/*******************************************************************************
* Function Name  : user_configValue2Int
* Description    : change user config value to int value
* Input          : none
* Output         : None
* Return         : u32 value
*******************************************************************************/
u32 user_configValue2Int(u32 config_id);
/*******************************************************************************
* Function Name  : user_config_Language
* Description    : config user language
* Input          : none
* Output         : None
* Return         : u32 value
*******************************************************************************/
void user_config_Language(void);
/*******************************************************************************
* Function Name  : user_config_Language
* Description    : config user language
* Input          : none
* Output         : None
* Return         : u32 value
*******************************************************************************/
void user_config_cfgSys(u32 configId);
/*******************************************************************************
* Function Name  : user_config_cfgSysAll
* Description    : user config change to system ctrl
* Input          : none
* Output         : None
* Return         : u32 value
*******************************************************************************/
void user_config_cfgSysAll(void);
#endif




