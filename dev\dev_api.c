/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../hal/inc/hal.h"

typedef struct DEV_NODE_API_S{
	u32			cnt;
	DEV_NODE_T  *node;
}DEV_NODE_API_T;
 
ALIGNED(4)  DEV_NODE_T dev_node[] = 
{
	{
		.name	= DEV_NAME_BATTERY,
		.init	= dev_battery_init,
		.ioctrl	= dev_battery_ioctrl,
		.prit	= 0,		
	},
	{
		.name	= DEV_NAME_GSENSOR,
		.init	= dev_gSensor_Init,
		.ioctrl	= dev_gSensor_ioctrl,
		.prit	= 0,		
	},
	{
		.name	= DEV_NAME_IR,
		.init	= dev_ir_init,
		.ioctrl	= dev_ir_ioctrl,
		.prit	= 0,		
	},
	{
		.name	= DEV_NAME_KEY,
		.init	= dev_key_init,
		.ioctrl	= dev_key_ioctrl,
		.prit	= 0,		
	},
	{
		.name	= DEV_NAME_LCD,
		.init	= dev_lcd_init,
		.ioctrl	= dev_lcd_ioctrl,
		.prit	= 0,		
	},
	{
		.name	= DEV_NAME_LED,
		.init	= dev_led_init,
		.ioctrl	= dev_led_ioctrl,
		.prit	= 0,		
	},
	{
		.name	= DEV_NAME_SENSOR,
		.init	= dev_sensor_init,
		.ioctrl	= dev_sensor_ioctrl,
		.prit	= 0,		
	},
	{
		.name	= DEV_NAME_SDCARD,
		.init	= dev_sdc_init,
		.ioctrl	= dev_sdc_ioctrl,
		.prit	= 0,		
	},
	{
		.name	= DEV_NAME_DUSB,
		.init	= dev_dusb_init,
		.ioctrl	= dev_dusb_ioctrl,
		.prit	= 0,		
	},
	{
		.name	= DEV_NAME_HUSB,
		.init	= dev_husb_init,
		.ioctrl	= dev_husb_ioctrl,
		.prit	= 0,		
	},
	{
		.name	= DEV_NAME_TP,
		.init	= dev_touchpanel_Init,
		.ioctrl	= dev_touchpanel_ioctrl,
		.prit	= 0,
	},
	{
		.name	= DEV_NAME_LED_PWM,
		.init	= dev_led_pwm_init,
		.ioctrl	= dev_led_pwm_ioctrl,
		.prit	= 0,		
	},
};
static ALIGNED(4) DEV_NODE_API_T	dev_node_api;

/*******************************************************************************
* Function Name  : dev_api_node_init
* Description    : dev_api_node_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void dev_api_node_init(void)
{
	int i;

	
	//----------nv fs for read only resource
	nv_init();	 
	
	dev_node_api.node = (DEV_NODE_T  *)&dev_node[0];
	dev_node_api.cnt  = sizeof(dev_node)/sizeof(DEV_NODE_T);
	for(i = 0; i< dev_node_api.cnt; i++)
	{
		if(dev_node_api.node->name[0] == 0)
			break;
		deg_Printf("[DEV] init: %s..",dev_node_api.node->name);
		if(dev_node_api.node->init)
		{
			if(dev_node_api.node->init() < 0)
			{
				deg_Printf("->fail.\n");
				dev_node_api.node->prit = 0;
			}else
			{
				deg_Printf("->OK.\n");
				dev_node_api.node->prit = 1;
			}
		}else
		{
			deg_Printf("->no init\n");
			dev_node_api.node->prit = 1;
		}
		dev_node_api.node++;
	}
	dev_node_api.cnt = i;
	dev_node_api.node = (DEV_NODE_T  *)&dev_node[0];
	deg_Printf("[DEV] init finish. total:%d\n",dev_node_api.cnt);
}
/*******************************************************************************
* Function Name  : dev_open
* Description    : dev_open
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int dev_open(char *name)
{
	DEV_NODE_T * dev_node_temp = dev_node_api.node;
	int i;
	if(dev_node_api.cnt == 0 || dev_node_api.node == NULL)
		return -1;
	for(i = 0; i < dev_node_api.cnt; i++, dev_node_temp++)
	{
		if(hx330x_str_cmp(name,dev_node_temp->name) == 0)
		{
			if(dev_node_temp->prit == 1)
			{
				return i;
			}else
				return -1;
		}
	}
	return -1;
}
/*******************************************************************************
* Function Name  : dev_open
* Description    : dev_open
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
int dev_ioctrl(int fd, u32 op, u32 para)
{
	DEV_NODE_T * dev_node_temp;

	if(fd < 0 || dev_node_api.cnt == 0 || dev_node_api.node == NULL)
		return -1;
	dev_node_temp = dev_node_api.node + fd;
	if(dev_node_temp->ioctrl)
	{
		return dev_node_temp->ioctrl(op, para);
	}
	else
	{
		return -1;
	}
 	
}
BOOT_TEXT_KEPT_SECTION
void exception_lowpower_io_cfg(void)
{
	if(hardware_setup.usb_host_en)
    {
		if(hardware_setup.usb_host_pwr_io != IO1_CH_NONE)
		{
			hal_io1d1_softstart_clr(1);//1 :enable softstart function
		}
        
    }


    if (LCD_TAG_SELECT != LCD_NONE)
    {
        hal_gpioEPullSet(hardware_setup.lcd_backlight_ch, hardware_setup.lcd_backlight_pin, GPIO_PULLE_DOWN);
        hal_gpioWrite(hardware_setup.lcd_backlight_ch, hardware_setup.lcd_backlight_pin, GPIO_LOW);
    }

}
