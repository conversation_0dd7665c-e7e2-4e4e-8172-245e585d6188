﻿/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT  == LCD_MCU_NT35510HSD

#define WriteComm(x)    LCD_CMD_MCU_CMD8(x)
#define WriteData(x)    LCD_CMD_MCU_DAT8(x)
#define Delay(m)        LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
//NT35510-HSD3.97 2012.10.20
#if 1
//#LV2 Page 1 enable
WriteComm(0xF000),WriteData(0x55),
WriteComm(0xF001),WriteData(0xAA),
WriteComm(0xF002),WriteData(0x52),
WriteComm(0xF003),WriteData(0x08),
WriteComm(0xF004),WriteData(0x01),

//#AVDD Set AVDD 5.2V
WriteComm(0xB000),WriteData(0x0D),
WriteComm(0xB001),WriteData(0x0D),
WriteComm(0xB002),WriteData(0x0D),

//#AVDD ratio
WriteComm(0xB600),WriteData(0x34),
WriteComm(0xB601),WriteData(0x34),
WriteComm(0xB602),WriteData(0x34),
 
//#AVEE  -5.2V
WriteComm(0xB100),WriteData(0x0D),
WriteComm(0xB101),WriteData(0x0D),
WriteComm(0xB102),WriteData(0x0D),
//#AVEE ratio
WriteComm(0xB700),WriteData(0x34),
WriteComm(0xB701),WriteData(0x34),
WriteComm(0xB702),WriteData(0x34),

//#VCL  -2.5V
WriteComm(0xB200),WriteData(0x00),
WriteComm(0xB201),WriteData(0x00),
WriteComm(0xB202),WriteData(0x00),

//#VCL ratio
WriteComm(0xB800),WriteData(0x24),
WriteComm(0xB801),WriteData(0x24),
WriteComm(0xB802),WriteData(0x24),


//#VGH 15V  (Free pump)
WriteComm(0xBF00),WriteData(0x01),
WriteComm(0xB300),WriteData(0x0F),
WriteComm(0xB301),WriteData(0x0F),
WriteComm(0xB302),WriteData(0x0F),

//#VGH ratio
WriteComm(0xB900),WriteData(0x34),
WriteComm(0xB901),WriteData(0x34),
WriteComm(0xB902),WriteData(0x34),

//#VGL_REG -10V
WriteComm(0xB500),WriteData(0x08),
WriteComm(0xB501),WriteData(0x08),
WriteComm(0xB502),WriteData(0x08),
WriteComm(0xC200),WriteData(0x03),

//#VGLX ratio
WriteComm(0xBA00),WriteData(0x24),
WriteComm(0xBA01),WriteData(0x24),
WriteComm(0xBA02),WriteData(0x24),

//#VGMP/VGSP 4.5V/0V
WriteComm(0xBC00),WriteData(0x00),
WriteComm(0xBC01),WriteData(0x78),
WriteComm(0xBC02),WriteData(0x00),

//#VGMN/VGSN -4.5V/0V
WriteComm(0xBD00),WriteData(0x00),
WriteComm(0xBD01),WriteData(0x78),
WriteComm(0xBD02),WriteData(0x00),

//#VCOM -1.325V 
WriteComm(0xBE00),WriteData(0x00),
WriteComm(0xBE01),WriteData(0x82),

//#Gamma Setting
WriteComm(0xD100),WriteData(0x00),
WriteComm(0xD101),WriteData(0x06),
WriteComm(0xD102),WriteData(0x00),
WriteComm(0xD103),WriteData(0x07),
WriteComm(0xD104),WriteData(0x00),
WriteComm(0xD105),WriteData(0x0E),
WriteComm(0xD106),WriteData(0x00),
WriteComm(0xD107),WriteData(0x22),
WriteComm(0xD108),WriteData(0x00),
WriteComm(0xD109),WriteData(0x3B),
WriteComm(0xD10A),WriteData(0x00),
WriteComm(0xD10B),WriteData(0x71),
WriteComm(0xD10C),WriteData(0x00),
WriteComm(0xD10D),WriteData(0x9F),
WriteComm(0xD10E),WriteData(0x00),
WriteComm(0xD10F),WriteData(0xE2),
WriteComm(0xD110),WriteData(0x01),
WriteComm(0xD111),WriteData(0x12),
WriteComm(0xD112),WriteData(0x01),
WriteComm(0xD113),WriteData(0x57),
WriteComm(0xD114),WriteData(0x01),
WriteComm(0xD115),WriteData(0x88),
WriteComm(0xD116),WriteData(0x01),
WriteComm(0xD117),WriteData(0xCE),
WriteComm(0xD118),WriteData(0x02),
WriteComm(0xD119),WriteData(0x07),
WriteComm(0xD11A),WriteData(0x02),
WriteComm(0xD11B),WriteData(0x08),
WriteComm(0xD11C),WriteData(0x02),
WriteComm(0xD11D),WriteData(0x39),
WriteComm(0xD11E),WriteData(0x02),
WriteComm(0xD11F),WriteData(0x6C),
WriteComm(0xD120),WriteData(0x02),
WriteComm(0xD121),WriteData(0x87),
WriteComm(0xD122),WriteData(0x02),
WriteComm(0xD123),WriteData(0xA6),
WriteComm(0xD124),WriteData(0x02),
WriteComm(0xD125),WriteData(0xBA),
WriteComm(0xD126),WriteData(0x02),
WriteComm(0xD127),WriteData(0xD2),
WriteComm(0xD128),WriteData(0x02),
WriteComm(0xD129),WriteData(0xE2),
WriteComm(0xD12A),WriteData(0x02),
WriteComm(0xD12B),WriteData(0xF7),
WriteComm(0xD12C),WriteData(0x03),
WriteComm(0xD12D),WriteData(0x06),
WriteComm(0xD12E),WriteData(0x03),
WriteComm(0xD12F),WriteData(0x1E),
WriteComm(0xD130),WriteData(0x03),
WriteComm(0xD131),WriteData(0x55),
WriteComm(0xD132),WriteData(0x03),
WriteComm(0xD133),WriteData(0xFF),

WriteComm(0xD200),WriteData(0x00),
WriteComm(0xD201),WriteData(0x06),
WriteComm(0xD202),WriteData(0x00),
WriteComm(0xD203),WriteData(0x07),
WriteComm(0xD204),WriteData(0x00),
WriteComm(0xD205),WriteData(0x0E),
WriteComm(0xD206),WriteData(0x00),
WriteComm(0xD207),WriteData(0x22),
WriteComm(0xD208),WriteData(0x00),
WriteComm(0xD209),WriteData(0x3B),
WriteComm(0xD20A),WriteData(0x00),
WriteComm(0xD20B),WriteData(0x71),
WriteComm(0xD20C),WriteData(0x00),
WriteComm(0xD20D),WriteData(0x9F),
WriteComm(0xD20E),WriteData(0x00),
WriteComm(0xD20F),WriteData(0xE2),
WriteComm(0xD210),WriteData(0x01),
WriteComm(0xD211),WriteData(0x12),
WriteComm(0xD212),WriteData(0x01),
WriteComm(0xD213),WriteData(0x57),
WriteComm(0xD214),WriteData(0x01),
WriteComm(0xD215),WriteData(0x88),
WriteComm(0xD216),WriteData(0x01),
WriteComm(0xD217),WriteData(0xCE),
WriteComm(0xD218),WriteData(0x02),
WriteComm(0xD219),WriteData(0x07),
WriteComm(0xD21A),WriteData(0x02),
WriteComm(0xD21B),WriteData(0x08),
WriteComm(0xD21C),WriteData(0x02),
WriteComm(0xD21D),WriteData(0x39),
WriteComm(0xD21E),WriteData(0x02),
WriteComm(0xD21F),WriteData(0x6C),
WriteComm(0xD220),WriteData(0x02),
WriteComm(0xD221),WriteData(0x87),
WriteComm(0xD222),WriteData(0x02),
WriteComm(0xD223),WriteData(0xA6),
WriteComm(0xD224),WriteData(0x02),
WriteComm(0xD225),WriteData(0xBA),
WriteComm(0xD226),WriteData(0x02),
WriteComm(0xD227),WriteData(0xD2),
WriteComm(0xD228),WriteData(0x02),
WriteComm(0xD229),WriteData(0xE2),
WriteComm(0xD22A),WriteData(0x02),
WriteComm(0xD22B),WriteData(0xF7),
WriteComm(0xD22C),WriteData(0x03),
WriteComm(0xD22D),WriteData(0x06),
WriteComm(0xD22E),WriteData(0x03),
WriteComm(0xD22F),WriteData(0x1E),
WriteComm(0xD230),WriteData(0x03),
WriteComm(0xD231),WriteData(0x55),
WriteComm(0xD232),WriteData(0x03),
WriteComm(0xD233),WriteData(0xFF),

WriteComm(0xD300),WriteData(0x00),
WriteComm(0xD301),WriteData(0x06),
WriteComm(0xD302),WriteData(0x00),
WriteComm(0xD303),WriteData(0x07),
WriteComm(0xD304),WriteData(0x00),
WriteComm(0xD305),WriteData(0x0E),
WriteComm(0xD306),WriteData(0x00),
WriteComm(0xD307),WriteData(0x22),
WriteComm(0xD308),WriteData(0x00),
WriteComm(0xD309),WriteData(0x3B),
WriteComm(0xD30A),WriteData(0x00),
WriteComm(0xD30B),WriteData(0x71),
WriteComm(0xD30C),WriteData(0x00),
WriteComm(0xD30D),WriteData(0x9F),
WriteComm(0xD30E),WriteData(0x00),
WriteComm(0xD30F),WriteData(0xE2),
WriteComm(0xD310),WriteData(0x01),
WriteComm(0xD311),WriteData(0x12),
WriteComm(0xD312),WriteData(0x01),
WriteComm(0xD313),WriteData(0x57),
WriteComm(0xD314),WriteData(0x01),
WriteComm(0xD315),WriteData(0x88),
WriteComm(0xD316),WriteData(0x01),
WriteComm(0xD317),WriteData(0xCE),
WriteComm(0xD318),WriteData(0x02),
WriteComm(0xD319),WriteData(0x07),
WriteComm(0xD31A),WriteData(0x02),
WriteComm(0xD31B),WriteData(0x08),
WriteComm(0xD31C),WriteData(0x02),
WriteComm(0xD31D),WriteData(0x39),
WriteComm(0xD31E),WriteData(0x02),
WriteComm(0xD31F),WriteData(0x6C),
WriteComm(0xD320),WriteData(0x02),
WriteComm(0xD321),WriteData(0x87),
WriteComm(0xD322),WriteData(0x02),
WriteComm(0xD323),WriteData(0xA6),
WriteComm(0xD324),WriteData(0x02),
WriteComm(0xD325),WriteData(0xBA),
WriteComm(0xD326),WriteData(0x02),
WriteComm(0xD327),WriteData(0xD2),
WriteComm(0xD328),WriteData(0x02),
WriteComm(0xD329),WriteData(0xE2),
WriteComm(0xD32A),WriteData(0x02),
WriteComm(0xD32B),WriteData(0xF7),
WriteComm(0xD32C),WriteData(0x03),
WriteComm(0xD32D),WriteData(0x06),
WriteComm(0xD32E),WriteData(0x03),
WriteComm(0xD32F),WriteData(0x1E),
WriteComm(0xD330),WriteData(0x03),
WriteComm(0xD331),WriteData(0x55),
WriteComm(0xD332),WriteData(0x03),
WriteComm(0xD333),WriteData(0xFF),

WriteComm(0xD400),WriteData(0x00),
WriteComm(0xD401),WriteData(0x06),
WriteComm(0xD402),WriteData(0x00),
WriteComm(0xD403),WriteData(0x07),
WriteComm(0xD404),WriteData(0x00),
WriteComm(0xD405),WriteData(0x0E),
WriteComm(0xD406),WriteData(0x00),
WriteComm(0xD407),WriteData(0x22),
WriteComm(0xD408),WriteData(0x00),
WriteComm(0xD409),WriteData(0x3B),
WriteComm(0xD40A),WriteData(0x00),
WriteComm(0xD40B),WriteData(0x71),
WriteComm(0xD40C),WriteData(0x00),
WriteComm(0xD40D),WriteData(0x9F),
WriteComm(0xD40E),WriteData(0x00),
WriteComm(0xD40F),WriteData(0xE2),
WriteComm(0xD410),WriteData(0x01),
WriteComm(0xD411),WriteData(0x12),
WriteComm(0xD412),WriteData(0x01),
WriteComm(0xD413),WriteData(0x57),
WriteComm(0xD414),WriteData(0x01),
WriteComm(0xD415),WriteData(0x88),
WriteComm(0xD416),WriteData(0x01),
WriteComm(0xD417),WriteData(0xCE),
WriteComm(0xD418),WriteData(0x02),
WriteComm(0xD419),WriteData(0x07),
WriteComm(0xD41A),WriteData(0x02),
WriteComm(0xD41B),WriteData(0x08),
WriteComm(0xD41C),WriteData(0x02),
WriteComm(0xD41D),WriteData(0x39),
WriteComm(0xD41E),WriteData(0x02),
WriteComm(0xD41F),WriteData(0x6C),
WriteComm(0xD420),WriteData(0x02),
WriteComm(0xD421),WriteData(0x87),
WriteComm(0xD422),WriteData(0x02),
WriteComm(0xD423),WriteData(0xA6),
WriteComm(0xD424),WriteData(0x02),
WriteComm(0xD425),WriteData(0xBA),
WriteComm(0xD426),WriteData(0x02),
WriteComm(0xD427),WriteData(0xD2),
WriteComm(0xD428),WriteData(0x02),
WriteComm(0xD429),WriteData(0xE2),
WriteComm(0xD42A),WriteData(0x02),
WriteComm(0xD42B),WriteData(0xF7),
WriteComm(0xD42C),WriteData(0x03),
WriteComm(0xD42D),WriteData(0x06),
WriteComm(0xD42E),WriteData(0x03),
WriteComm(0xD42F),WriteData(0x1E),
WriteComm(0xD430),WriteData(0x03),
WriteComm(0xD431),WriteData(0x55),
WriteComm(0xD432),WriteData(0x03),
WriteComm(0xD433),WriteData(0xFF),

WriteComm(0xD500),WriteData(0x00),
WriteComm(0xD501),WriteData(0x06),
WriteComm(0xD502),WriteData(0x00),
WriteComm(0xD503),WriteData(0x07),
WriteComm(0xD504),WriteData(0x00),
WriteComm(0xD505),WriteData(0x0E),
WriteComm(0xD506),WriteData(0x00),
WriteComm(0xD507),WriteData(0x22),
WriteComm(0xD508),WriteData(0x00),
WriteComm(0xD509),WriteData(0x3B),
WriteComm(0xD50A),WriteData(0x00),
WriteComm(0xD50B),WriteData(0x71),
WriteComm(0xD50C),WriteData(0x00),
WriteComm(0xD50D),WriteData(0x9F),
WriteComm(0xD50E),WriteData(0x00),
WriteComm(0xD50F),WriteData(0xE2),
WriteComm(0xD510),WriteData(0x01),
WriteComm(0xD511),WriteData(0x12),
WriteComm(0xD512),WriteData(0x01),
WriteComm(0xD513),WriteData(0x57),
WriteComm(0xD514),WriteData(0x01),
WriteComm(0xD515),WriteData(0x88),
WriteComm(0xD516),WriteData(0x01),
WriteComm(0xD517),WriteData(0xCE),
WriteComm(0xD518),WriteData(0x02),
WriteComm(0xD519),WriteData(0x07),
WriteComm(0xD51A),WriteData(0x02),
WriteComm(0xD51B),WriteData(0x08),
WriteComm(0xD51C),WriteData(0x02),
WriteComm(0xD51D),WriteData(0x39),
WriteComm(0xD51E),WriteData(0x02),
WriteComm(0xD51F),WriteData(0x6C),
WriteComm(0xD520),WriteData(0x02),
WriteComm(0xD521),WriteData(0x87),
WriteComm(0xD522),WriteData(0x02),
WriteComm(0xD523),WriteData(0xA6),
WriteComm(0xD524),WriteData(0x02),
WriteComm(0xD525),WriteData(0xBA),
WriteComm(0xD526),WriteData(0x02),
WriteComm(0xD527),WriteData(0xD2),
WriteComm(0xD528),WriteData(0x02),
WriteComm(0xD529),WriteData(0xE2),
WriteComm(0xD52A),WriteData(0x02),
WriteComm(0xD52B),WriteData(0xF7),
WriteComm(0xD52C),WriteData(0x03),
WriteComm(0xD52D),WriteData(0x06),
WriteComm(0xD52E),WriteData(0x03),
WriteComm(0xD52F),WriteData(0x1E),
WriteComm(0xD530),WriteData(0x03),
WriteComm(0xD531),WriteData(0x55),
WriteComm(0xD532),WriteData(0x03),
WriteComm(0xD533),WriteData(0xFF),

WriteComm(0xD600),WriteData(0x00),
WriteComm(0xD601),WriteData(0x06),
WriteComm(0xD602),WriteData(0x00),
WriteComm(0xD603),WriteData(0x07),
WriteComm(0xD604),WriteData(0x00),
WriteComm(0xD605),WriteData(0x0E),
WriteComm(0xD606),WriteData(0x00),
WriteComm(0xD607),WriteData(0x22),
WriteComm(0xD608),WriteData(0x00),
WriteComm(0xD609),WriteData(0x3B),
WriteComm(0xD60A),WriteData(0x00),
WriteComm(0xD60B),WriteData(0x71),
WriteComm(0xD60C),WriteData(0x00),
WriteComm(0xD60D),WriteData(0x9F),
WriteComm(0xD60E),WriteData(0x00),
WriteComm(0xD60F),WriteData(0xE2),
WriteComm(0xD610),WriteData(0x01),
WriteComm(0xD611),WriteData(0x12),
WriteComm(0xD612),WriteData(0x01),
WriteComm(0xD613),WriteData(0x57),
WriteComm(0xD614),WriteData(0x01),
WriteComm(0xD615),WriteData(0x88),
WriteComm(0xD616),WriteData(0x01),
WriteComm(0xD617),WriteData(0xCE),
WriteComm(0xD618),WriteData(0x02),
WriteComm(0xD619),WriteData(0x07),
WriteComm(0xD61A),WriteData(0x02),
WriteComm(0xD61B),WriteData(0x08),
WriteComm(0xD61C),WriteData(0x02),
WriteComm(0xD61D),WriteData(0x39),
WriteComm(0xD61E),WriteData(0x02),
WriteComm(0xD61F),WriteData(0x6C),
WriteComm(0xD620),WriteData(0x02),
WriteComm(0xD621),WriteData(0x87),
WriteComm(0xD622),WriteData(0x02),
WriteComm(0xD623),WriteData(0xA6),
WriteComm(0xD624),WriteData(0x02),
WriteComm(0xD625),WriteData(0xBA),
WriteComm(0xD626),WriteData(0x02),
WriteComm(0xD627),WriteData(0xD2),
WriteComm(0xD628),WriteData(0x02),
WriteComm(0xD629),WriteData(0xE2),
WriteComm(0xD62A),WriteData(0x02),
WriteComm(0xD62B),WriteData(0xF7),
WriteComm(0xD62C),WriteData(0x03),
WriteComm(0xD62D),WriteData(0x06),
WriteComm(0xD62E),WriteData(0x03),
WriteComm(0xD62F),WriteData(0x1E),
WriteComm(0xD630),WriteData(0x03),
WriteComm(0xD631),WriteData(0x55),
WriteComm(0xD632),WriteData(0x03),
WriteComm(0xD633),WriteData(0xFF),
#endif
//#LV2 Page 0 enable
WriteComm(0xF000),WriteData(0x55),
WriteComm(0xF001),WriteData(0xAA),
WriteComm(0xF002),WriteData(0x52),
WriteComm(0xF003),WriteData(0x08),
WriteComm(0xF004),WriteData(0x00),
#if 1
//#Display control
WriteComm(0xB100),WriteData( 0xCC),
WriteComm(0xB101),WriteData( 0x00),

//#Source hold time
WriteComm(0xB600),WriteData(0x05),

//#Gate EQ control
WriteComm(0xB700),WriteData(0x70),
WriteComm(0xB701),WriteData(0x70),

//#Source EQ control (Mode 2)
WriteComm(0xB800),WriteData(0x01),
WriteComm(0xB801),WriteData(0x03),
WriteComm(0xB802),WriteData(0x03),
WriteComm(0xB803),WriteData(0x03),


//#Inversion mode  (2-dot)
WriteComm(0xBC00),WriteData(0x02),
WriteComm(0xBC01),WriteData(0x00),
WriteComm(0xBC02),WriteData(0x00),

//#Timing control 4H w/ 4-delay 
WriteComm(0xC900),WriteData(0xD0),
WriteComm(0xC901),WriteData(0x02),
WriteComm(0xC902),WriteData(0x50),
WriteComm(0xC903),WriteData(0x50),
WriteComm(0xC904),WriteData(0x50),

// 50fps
WriteComm(0xBD00),WriteData(0x01),
WriteComm(0xBD01),WriteData(0xC4),
WriteComm(0xBD02),WriteData(0x2D),
WriteComm(0xBD03),WriteData(0x2D),
WriteComm(0xBD04),WriteData(0x00),

//WriteComm(0x3500),WriteData(0x00),
WriteComm(0x3A00),WriteData(0x55),
WriteComm(0x3600),WriteData(0x00),
WriteComm(0x3500),WriteData(0x00),
// WriteComm(0x4400),WriteData((16 >> 8) & 0xFF),
// WriteComm(0x4401),WriteData(16 & 0xFF),
//High),WriteData(以下代码为增加的增艳显示效果
WriteComm(0xF000),WriteData(0x55),
WriteComm(0xF001),WriteData(0xAA),
WriteComm(0xF002),WriteData(0x52),
WriteComm(0xF003),WriteData(0x08),
WriteComm(0xF004),WriteData(0x00),

WriteComm(0xB400),WriteData(0x10),

WriteComm(0xFF00),WriteData(0xAA),
WriteComm(0xFF01),WriteData(0x55),
WriteComm(0xFF02),WriteData(0x25),
WriteComm(0xFF03),WriteData(0x01),

WriteComm(0XF900),WriteData(0x14),
WriteComm(0XF901),WriteData(0x00),
WriteComm(0XF902),WriteData(0x0D),
WriteComm(0XF903),WriteData(0x1A),
WriteComm(0XF904),WriteData(0x26),
WriteComm(0XF905),WriteData(0x33),
WriteComm(0XF906),WriteData(0x40),
WriteComm(0XF907),WriteData(0x4D),
WriteComm(0XF908),WriteData(0x5A),
WriteComm(0XF909),WriteData(0x66),
WriteComm(0XF90A),WriteData(0x73),
#endif
WriteComm(0x1100),
Delay (50),
WriteComm(0x2900),
WriteComm(0x2C00),
LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    .name           = "MCU_NT35510HSD",
    .lcd_bus_type   = LCD_IF_GET(),
    .scan_mode      = LCD_DISPLAY_ROTATE_90,
    .te_mode        = LCD_MCU_TE_ENABLE,
    .dma_prio       = 15,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_16,

    .pclk_div       = LCD_PCLK_DIV(480*800*50),
    .clk_per_pixel  = 1,
    .even_order     = LCD_RGB,
    .odd_order      = LCD_RGB,

    .data_mode = LCD_DATA_MODE0_16BIT_RGB565,

    .screen_w       = 480,
    .screen_h       = 800,

    .video_w        = 800,
    .video_h        = 480,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness     = -16,

    .saturation     = LCD_SATURATION_130,

    .contra_index   = 9,

    .gamma_index    = {0, 0, 0},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()

#endif




























