/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT == LCD_RGB_JLT28060B

#define CMD(x)    LCD_CMD_RGB_DAT(x)
#define DAT(x)    LCD_CMD_RGB_DAT((x)|(1<<8))
#define DLY(m)    LCD_CMD_DELAY_MS(m)

LCD_INIT_TAB_BEGIN()
    CMD(0xFF), DAT(0x77), DAT(0x01), DAT(0x00), DAT(0x00), DAT(0x13),
    CMD(0xEF), DAT(0x08),
    CMD(0xFF), DAT(0x77), DAT(0x01), DAT(0x00), DAT(0x00), DAT(0x10),
    CMD(0xC0), DAT(0x4F), DAT(0x00),
    CMD(0xC1), DAT(0x10), DAT(0x02),
    CMD(0xC2), DAT(0x07), DAT(0x02),
    CMD(0xCC), DAT(0x10),

    CMD(0xB0), DAT(0x00), DAT(0x10), DAT(0x17), DAT(0x0D), DAT(0x11), DAT(0x06), DAT(0x05), DAT(0x08),
               DAT(0x07), DAT(0x1F), DAT(0x04), DAT(0x11), DAT(0x0E), DAT(0x29), DAT(0x30), DAT(0x1F),
    CMD(0xB1), DAT(0x00), DAT(0x0D), DAT(0x14), DAT(0x0E), DAT(0x11), DAT(0x06), DAT(0x04), DAT(0x08),
               DAT(0x08), DAT(0x20), DAT(0x05), DAT(0x13), DAT(0x13), DAT(0x26), DAT(0x30), DAT(0x1F),
    CMD(0xFF), DAT(0x77), DAT(0x01), DAT(0x00), DAT(0x00), DAT(0x11),
    CMD(0xB0), DAT(0x65),
    CMD(0xB1), DAT(0x71),
    CMD(0xB2), DAT(0x87),
    CMD(0xB3), DAT(0x80),
    CMD(0xB5), DAT(0x4D),
    CMD(0xB7), DAT(0x85),
    CMD(0xB8), DAT(0x20),
    CMD(0xC1), DAT(0x78),
    CMD(0xC2), DAT(0x78),
    CMD(0xD0), DAT(0x88),
    CMD(0xEE), DAT(0x42),
    CMD(0xE0), DAT(0x00), DAT(0x00), DAT(0x02),
    CMD(0xE1), DAT(0x04), DAT(0xA0), DAT(0x06), DAT(0xA0), DAT(0x05), DAT(0xA0), DAT(0x07), DAT(0xA0),
               DAT(0x00), DAT(0x44), DAT(0x44),
    CMD(0xE2), DAT(0x00), DAT(0x00), DAT(0x00), DAT(0x00), DAT(0x00), DAT(0x00), DAT(0x00), DAT(0x00),
               DAT(0x00), DAT(0x00), DAT(0x00), DAT(0x00),
    CMD(0xE3), DAT(0x00), DAT(0x00), DAT(0x22), DAT(0x22),
    CMD(0xE4), DAT(0x44), DAT(0x44),
    CMD(0xE5), DAT(0x0C), DAT(0x90), DAT(0xA0), DAT(0xA0), DAT(0x0E), DAT(0x92), DAT(0xA0), DAT(0xA0),
               DAT(0x08), DAT(0x8C), DAT(0xA0), DAT(0xA0), DAT(0x0A), DAT(0x8E), DAT(0xA0), DAT(0xA0),
    CMD(0xE6), DAT(0x00), DAT(0x00), DAT(0x22), DAT(0x22),
    CMD(0xE7), DAT(0x44), DAT(0x44),
    CMD(0xE8), DAT(0x0D), DAT(0x91), DAT(0xA0), DAT(0xA0), DAT(0x0F), DAT(0x93), DAT(0xA0), DAT(0xA0),
               DAT(0x09), DAT(0x8D), DAT(0xA0), DAT(0xA0), DAT(0x0B), DAT(0x8F), DAT(0xA0), DAT(0xA0),
    CMD(0xEB), DAT(0x00), DAT(0x00), DAT(0xE4), DAT(0xE4), DAT(0x44), DAT(0x00), DAT(0x40),
    CMD(0xED), DAT(0xFF), DAT(0xF5), DAT(0x47), DAT(0x6F), DAT(0x0B), DAT(0xA1), DAT(0xAB), DAT(0xFF),
               DAT(0xFF), DAT(0xBA), DAT(0x1A), DAT(0xB0), DAT(0xF6), DAT(0x74), DAT(0x5F), DAT(0xFF),
    CMD(0xEF), DAT(0x08), DAT(0x08), DAT(0x08), DAT(0x45), DAT(0x3F), DAT(0x54),
    CMD(0xFF), DAT(0x77), DAT(0x01), DAT(0x00), DAT(0x00), DAT(0x00),
    CMD(0xFF), DAT(0x77), DAT(0x01), DAT(0x00), DAT(0x00), DAT(0x13),
    CMD(0xE6), DAT(0x16),
    CMD(0xE8), DAT(0x00), DAT(0x0E),
    CMD(0xFF), DAT(0x77), DAT(0x01), DAT(0x00), DAT(0x00), DAT(0x00),
    CMD(0x11),
    DLY(120),
    CMD(0xFF), DAT(0x77), DAT(0x01), DAT(0x00), DAT(0x00), DAT(0x13),
    CMD(0xE8), DAT(0x00), DAT(0x0C),
    DLY(10),
    CMD(0xE8), DAT(0x00), DAT(0x00),
    CMD(0xFF), DAT(0x77), DAT(0x01), DAT(0x00), DAT(0x00), DAT(0x00),
    CMD(0x29),
    CMD(0x3A), DAT(0x77),
    CMD(0x29),
    CMD(0x36), DAT(0x08),

LCD_INIT_TAB_END()

LCD_UNINIT_TAB_BEGIN()  
    CMD(0x28),
    DLY(10),
LCD_UNINIT_TA_ENDB()

//无白色边框的屏
LCD_DESC_BEGIN()
    .name 			= "RGB_LCD_JLT28060B",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_24,

    .pclk_div 		= LCD_PCLK_DIV(12000000),
    .clk_per_pixel 	= 1,
    .even_order 	= LCD_BGR,
    .odd_order 		= LCD_BGR,

    .pclk_edge      = LCD_PCLK_EDGE_FALLING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw 			= 1,	//vsync false
    .vbp 			= 12,	//Vertical Sync Pulse Start Position = VPS lines
    .vfp 			= 20,

    .hlw 			= 2,
    .hbp 			= 1,	//Horizontal Sync Pulse Start Position = (HPS + 1) pixels
    .hfp 			= 1,

    LCD_SPI_DEFAULT(9),

    .data_mode 	= LCD_DATA_MODE0_24BIT_RGB888,

    .screen_w 		= 480,
    .screen_h 		= 640,

    .video_w  		= 640,
    .video_h 	 	= 480,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
    UNINIT_TAB_INIT
LCD_DESC_END()


#endif








