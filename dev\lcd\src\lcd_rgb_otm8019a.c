/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../hal/inc/hal.h"

#if LCD_TAG_SELECT == LCD_RGB_OTM8019A


#define CMD(x)  LCD_CMD_RGB_DAT((0x20<<8)|(((x)>>8)&0xff)),LCD_CMD_RGB_DAT((0x00<<8)|((x)&0xff))
#define DAT(x)  LCD_CMD_RGB_DAT((0x40<<8)|((x)&0xff))
#define DLY(m)  LCD_CMD_DELAY_MS(m)


LCD_INIT_TAB_BEGIN()

CMD(0xFF00), DAT(0x80),
CMD(0xFF01), DAT(0x19),
CMD(0xFF02), DAT(0x01),

CMD(0xFF80), DAT(0x80),
CMD(0xFF81), DAT(0x19),

CMD(0xC48A), DAT(0x40),

CMD(0xB3A6), DAT(0x20),
CMD(0xB3A7), DAT(0x01),

CMD(0xC090), DAT(0x00),
CMD(0xC091), DAT(0x15),
CMD(0xC092), DAT(0x00),
CMD(0xC093), DAT(0x00),
CMD(0xC094), DAT(0x00),
CMD(0xC095), DAT(0x03),

CMD(0xC0B4), DAT(0x70),

CMD(0xC181), DAT(0x33),

CMD(0xC481), DAT(0x81),

CMD(0xC487), DAT(0x00),

CMD(0xC489), DAT(0x00),

CMD(0xC582), DAT(0xB0),

CMD(0xC590), DAT(0x4E),
CMD(0xC591), DAT(0x34),
CMD(0xC592), DAT(0x06),
CMD(0xC593), DAT(0x91),
CMD(0xC594), DAT(0x33),
CMD(0xC595), DAT(0x34),
CMD(0xC596), DAT(0x23),

CMD(0xC5B1), DAT(0xA8),

CMD(0xD800), DAT(0x6F),
CMD(0xD801), DAT(0x6F),

CMD(0xD900), DAT(0x4B),

CMD(0xCE80), DAT(0x86),
CMD(0xCE81), DAT(0x01),
CMD(0xCE82), DAT(0x00),
CMD(0xCE83), DAT(0x85),
CMD(0xCE84), DAT(0x01),
CMD(0xCE85), DAT(0x00),
CMD(0xCE86), DAT(0x00),
CMD(0xCE87), DAT(0x00),
CMD(0xCE88), DAT(0x00),
CMD(0xCE89), DAT(0x00),
CMD(0xCE8A), DAT(0x00),
CMD(0xCE8B), DAT(0x00),

CMD(0xCEA0), DAT(0x18),
CMD(0xCEA1), DAT(0x05),
CMD(0xCEA2), DAT(0x83),
CMD(0xCEA3), DAT(0x39),
CMD(0xCEA4), DAT(0x00),
CMD(0xCEA5), DAT(0x00),
CMD(0xCEA6), DAT(0x00),
CMD(0xCEA7), DAT(0x18),
CMD(0xCEA8), DAT(0x04),
CMD(0xCEA9), DAT(0x83),
CMD(0xCEAA), DAT(0x3A),
CMD(0xCEAB), DAT(0x00),
CMD(0xCEAC), DAT(0x00),
CMD(0xCEAD), DAT(0x00),

CMD(0xCEB0), DAT(0x18),
CMD(0xCEB1), DAT(0x03),
CMD(0xCEB2), DAT(0x83),
CMD(0xCEB3), DAT(0x3B),
CMD(0xCEB4), DAT(0x86),
CMD(0xCEB5), DAT(0x00),
CMD(0xCEB6), DAT(0x00),
CMD(0xCEB7), DAT(0x18),
CMD(0xCEB8), DAT(0x02),
CMD(0xCEB9), DAT(0x83),
CMD(0xCEBA), DAT(0x3C),
CMD(0xCEBB), DAT(0x88),
CMD(0xCEBC), DAT(0x00),
CMD(0xCEBD), DAT(0x00),

CMD(0xCFC0), DAT(0x01),
CMD(0xCFC1), DAT(0x01),
CMD(0xCFC2), DAT(0x20),
CMD(0xCFC3), DAT(0x20),
CMD(0xCFC4), DAT(0x00),
CMD(0xCFC5), DAT(0x00),
CMD(0xCFC6), DAT(0x01),
CMD(0xCFC7), DAT(0x02),
CMD(0xCFC8), DAT(0x00),
CMD(0xCFC9), DAT(0x00),

CMD(0xCFD0), DAT(0x00),

CMD(0xCBC0), DAT(0x00),
CMD(0xCBC1), DAT(0x01),
CMD(0xCBC2), DAT(0x01),
CMD(0xCBC3), DAT(0x01),
CMD(0xCBC4), DAT(0x01),
CMD(0xCBC5), DAT(0x01),
CMD(0xCBC6), DAT(0x00),
CMD(0xCBC7), DAT(0x00),
CMD(0xCBC8), DAT(0x00),
CMD(0xCBC9), DAT(0x00),
CMD(0xCBCA), DAT(0x00),
CMD(0xCBCB), DAT(0x00),
CMD(0xCBCC), DAT(0x00),
CMD(0xCBCD), DAT(0x00),
CMD(0xCBCE), DAT(0x00),

CMD(0xCBD0), DAT(0x00),

CMD(0xCBD5), DAT(0x00),
CMD(0xCBD6), DAT(0x00),
CMD(0xCBD7), DAT(0x00),
CMD(0xCBD8), DAT(0x00),
CMD(0xCBD9), DAT(0x00),
CMD(0xCBDA), DAT(0x00),
CMD(0xCBDB), DAT(0x00),
CMD(0xCBDC), DAT(0x00),
CMD(0xCBDD), DAT(0x00),
CMD(0xCBDE), DAT(0x00),

CMD(0xCBE0), DAT(0x01),
CMD(0xCBE1), DAT(0x01),
CMD(0xCBE2), DAT(0x01),
CMD(0xCBE3), DAT(0x01),
CMD(0xCBE4), DAT(0x01),
CMD(0xCBE5), DAT(0x00),

CMD(0xCC80), DAT(0x00),
CMD(0xCC81), DAT(0x26),
CMD(0xCC82), DAT(0x09),
CMD(0xCC83), DAT(0x0B),
CMD(0xCC84), DAT(0x01),
CMD(0xCC85), DAT(0x25),
CMD(0xCC86), DAT(0x00),
CMD(0xCC87), DAT(0x00),
CMD(0xCC88), DAT(0x00),
CMD(0xCC89), DAT(0x00),

CMD(0xCC90), DAT(0x00),
CMD(0xCC91), DAT(0x00),
CMD(0xCC92), DAT(0x00),
CMD(0xCC93), DAT(0x00),
CMD(0xCC94), DAT(0x00),
CMD(0xCC95), DAT(0x00),

CMD(0xCC9A), DAT(0x00),
CMD(0xCC9B), DAT(0x00),
CMD(0xCC9C), DAT(0x00),
CMD(0xCC9D), DAT(0x00),
CMD(0xCC9E), DAT(0x00),

CMD(0xCCA0), DAT(0x00),
CMD(0xCCA1), DAT(0x00),
CMD(0xCCA2), DAT(0x00),
CMD(0xCCA3), DAT(0x00),
CMD(0xCCA4), DAT(0x00),
CMD(0xCCA5), DAT(0x25),
CMD(0xCCA6), DAT(0x02),
CMD(0xCCA7), DAT(0x0C),
CMD(0xCCA8), DAT(0x0A),
CMD(0xCCA9), DAT(0x26),
CMD(0xCCAA), DAT(0x00),

CMD(0xCCB0), DAT(0x00),
CMD(0xCCB1), DAT(0x25),
CMD(0xCCB2), DAT(0x0C),
CMD(0xCCB3), DAT(0x0A),
CMD(0xCCB4), DAT(0x02),
CMD(0xCCB5), DAT(0x26),
CMD(0xCCB6), DAT(0x00),
CMD(0xCCB7), DAT(0x00),
CMD(0xCCB8), DAT(0x00),
CMD(0xCCB9), DAT(0x00),

CMD(0xCCC0), DAT(0x00),
CMD(0xCCC1), DAT(0x00),
CMD(0xCCC2), DAT(0x00),
CMD(0xCCC3), DAT(0x00),
CMD(0xCCC4), DAT(0x00),
CMD(0xCCC5), DAT(0x00),

CMD(0xCCCA), DAT(0x00),
CMD(0xCCCB), DAT(0x00),
CMD(0xCCCC), DAT(0x00),
CMD(0xCCCD), DAT(0x00),
CMD(0xCCCE), DAT(0x00),

CMD(0xCCD0), DAT(0x00),
CMD(0xCCD1), DAT(0x00),
CMD(0xCCD2), DAT(0x00),
CMD(0xCCD3), DAT(0x00),
CMD(0xCCD4), DAT(0x00),
CMD(0xCCD5), DAT(0x26),
CMD(0xCCD6), DAT(0x01),
CMD(0xCCD7), DAT(0x09),
CMD(0xCCD8), DAT(0x0B),
CMD(0xCCD9), DAT(0x25),
CMD(0xCCDA), DAT(0x00),

CMD(0xE100), DAT(0x00),
CMD(0xE101), DAT(0x07),
CMD(0xE102), DAT(0x10),
CMD(0xE103), DAT(0x22),
CMD(0xE104), DAT(0x40),
CMD(0xE105), DAT(0x56),
CMD(0xE106), DAT(0x66),
CMD(0xE107), DAT(0x99),
CMD(0xE108), DAT(0x88),
CMD(0xE109), DAT(0x9F),
CMD(0xE10A), DAT(0x6A),
CMD(0xE10B), DAT(0x5A),
CMD(0xE10C), DAT(0x74),
CMD(0xE10D), DAT(0x61),
CMD(0xE10E), DAT(0x68),
CMD(0xE10F), DAT(0x61),
CMD(0xE110), DAT(0x5B),
CMD(0xE111), DAT(0x4E),
CMD(0xE112), DAT(0x44),
CMD(0xE113), DAT(0x00),

CMD(0xE200), DAT(0x00),
CMD(0xE201), DAT(0x07),
CMD(0xE202), DAT(0x10),
CMD(0xE203), DAT(0x22),
CMD(0xE204), DAT(0x40),
CMD(0xE205), DAT(0x56),
CMD(0xE206), DAT(0x66),
CMD(0xE207), DAT(0x99),
CMD(0xE208), DAT(0x88),
CMD(0xE209), DAT(0x9F),
CMD(0xE20A), DAT(0x6A),
CMD(0xE20B), DAT(0x5A),
CMD(0xE20C), DAT(0x74),
CMD(0xE20D), DAT(0x61),
CMD(0xE20E), DAT(0x68),
CMD(0xE20F), DAT(0x61),
CMD(0xE210), DAT(0x5B),
CMD(0xE211), DAT(0x4E),
CMD(0xE212), DAT(0x44),
CMD(0xE213), DAT(0x00),

CMD(0xC480), DAT(0x30),

CMD(0xC098), DAT(0x00),

CMD(0xC0A9), DAT(0x0A),

CMD(0xC1B0), DAT(0x20),
CMD(0xC1B1), DAT(0x00),
CMD(0xC1B2), DAT(0x00),

CMD(0xC0E1), DAT(0x40),
CMD(0xC0E2), DAT(0x30),

CMD(0xC180), DAT(0x03),
CMD(0xC181), DAT(0x33),

CMD(0xC1A0), DAT(0xE8),

CMD(0xB690), DAT(0xB4),
DLY(10),

CMD(0xFB00), DAT(0x01),

CMD(0xFF00), DAT(0xFF),
CMD(0xFF01), DAT(0xFF),
CMD(0xFF02), DAT(0xFF),

CMD(0x3a00), DAT(0x77),

CMD(0x1100), DAT(0x00),
DLY(120),

CMD(0x2900), DAT(0x00),
DLY(50),

LCD_INIT_TAB_END()

LCD_DESC_BEGIN()
    // LHYC4006A-V8
    .name = "OTM8019A",
    .lcd_bus_type 	= LCD_IF_GET(),
    .scan_mode 		= LCD_DISPLAY_ROTATE_90,

    .io_data_pin    = LCD_DPIN_EN_DEFAULT_24,

    .pclk_div 		= LCD_PCLK_DIV(24000000),
    .clk_per_pixel 	= 1,
    .even_order 	= LCD_RGB,
    .odd_order 		= LCD_RGB,

    .pclk_edge      = LCD_PCLK_EDGE_FALLING,
    .de_level 		= LCD_SIG_ACT_LEVEL_HIGH,
    .hs_level 		= LCD_SIG_ACT_LEVEL_LOW,
    .vs_level 		= LCD_SIG_ACT_LEVEL_LOW,

    .vlw            = 2,
    .vbp            = 16,
    .vfp            = 20,
    .hlw            = 3,
    .hbp            = 20,
    .hfp            = 20,

    LCD_SPI_DEFAULT(16),

    .data_mode 	= LCD_DATA_MODE0_24BIT_RGB888,

    .screen_w 		= 480,
    .screen_h 		= 800,

    .video_w  		= 800,
    .video_h 	 	= 480,

    //支持配置VIDEO放大，如果配置，UI的SIZE跟随 video_scaler，否则UI的size跟随sreen的size
    .video_scaler_w = 0,    //配置为0，则按video_w显示；不为0，则将video_w放大到video_scaler_w显示。(video_w <= video_scaler_w)
    .video_scaler_h = 0,    //配置为0，则按video_h显示；不为0，则将video_h放大到video_scaler_w显示。(video_h <= video_scaler_h)
    
    .contrast       = LCD_CONTRAST_DEFAULT,

    .brightness 	= -12,

    .saturation     = LCD_SATURATION_DEFAULT,

    .contra_index 	= 8,

    .gamma_index 	= {3, 3, 3},

    .asawtooth_index = {5, 5},

    .lcd_ccm         = LCD_CCM_DEFAULT,
    .lcd_saj         = LCD_SAJ_DEFAULT,

    INIT_TAB_INIT
LCD_DESC_END()
#endif

