/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : dev_led_pwm_init
* Description    : dev_led_pwm_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_led_pwm_init(void)
{
	if(hardware_setup.led_pwm_en)
	{
	
	}
	return 0;
}

/*******************************************************************************
* Function Name  : dev_key_ioctrl
* Description    : dev_key_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_led_pwm_ioctrl(u32 op, u32 para)
{
	if(hardware_setup.led_pwm_en)
	{
		static u32 dev_led_pwm_state = 0;
		switch(op)
		{
			case DEV_LED_ON_OFF_READ:
				if(para)
					*(u32*)para = dev_led_pwm_state;
				break;
			case DEV_LED_ON_OFF_WRITE:
				dev_led_pwm_state = para;
				/*if(hardware_setup.led_pwm_valid == 0)
				{
					para = (para^1)&1;
				}
				if(para)
					hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin,GPIO_HIGH);
				else
					hal_gpioWrite(hardware_setup.led_pwm_ch, hardware_setup.led_pwm_pin,GPIO_LOW);*/
				if(para)
					hal_gpioWrite(hardware_setup.led_on_off_ch, hardware_setup.led_on_off_pin,GPIO_HIGH);
				else
					hal_gpioWrite(hardware_setup.led_on_off_ch, hardware_setup.led_on_off_pin,GPIO_LOW);					
				break;
			case DEV_LED_PWM_ADJUST:
				deg_Printf("DEV_LED_PWM_ADJUST>>>>>>>>>\n");
				hal_timerPWMStart(hardware_setup.led_pwm_timer,hardware_setup.led_pwm_timer_ch,10000, para);
			break;
		}
	}
	return 0;
}

