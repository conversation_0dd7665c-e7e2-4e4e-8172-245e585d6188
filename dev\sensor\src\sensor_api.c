/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"


ALIGNED(4) static SENSOR_API_T sensor_api;
ALIGNED(4) const s8 user_ee_sharp_tab[25] = {
	-1,-2,-2,-2,-1,
	-2, 1, 3, 1,-2,
	-2, 3, 20,3,-2,
	-2, 1, 3, 1,-2,
	-1,-2,-2,-2,-1
};
ALIGNED(4) const s8 user_ee_dn_tab[25] = {
	0,0,1,0,0,
	0,2,4,2,0,
	1,4,24,4,1,
	0,2,4,2,0,
	0,0,1,0,0,//*/
};
ALIGNED(4) const s8 user_ccf_dn_tab[9] = {
	21,10,21,
	10,4,10,
	21,10,21//sum must be 128
};
/*******************************************************************************
* Function Name  : sensor_iic_write
* Description    : senor iic  write
* Input          : INT8U *data : data & addr
* Output         : none                                            
* Return         : none
*******************************************************************************/
void sensor_iic_write(INT8U *data)
{	
    if(sensor_api.sensorIdent.addr_num == 2)
    {   
		hal_iic016bitAddrWrite(sensor_api.sensorIdent.w_cmd,(data[0] << 8) | data[1],&data[2],sensor_api.sensorIdent.data_num);
    }
	else
	{
		hal_iic08bitAddrWrite(sensor_api.sensorIdent.w_cmd,data[0],&data[1],sensor_api.sensorIdent.data_num);
	}
}
/*******************************************************************************
* Function Name  : sensor_iic_read
* Description    : senor iic  read
* Input          : INT8U *data : data & addr
* Output         : none                                            
* Return         : none
*******************************************************************************/
INT32U sensor_iic_read(INT8U *data)
{
     INT32U temp = 0;
     
    if(sensor_api.sensorIdent.addr_num==2)
    {

		hal_iic016bitAddrRead(sensor_api.sensorIdent.w_cmd,(data[0] << 8) | data[1],(u8 *)&temp,sensor_api.sensorIdent.data_num);
    }
	else
	{
		hal_iic08bitAddrRead(sensor_api.sensorIdent.w_cmd,data[0],(u8 *)&temp,sensor_api.sensorIdent.data_num);
	}
    return temp;	
}
/*******************************************************************************
* Function Name  : sensorCheckId
* Description    : senor check id
* Input          : sensor_adpt *p_sensor_cmd : sensor op
* Output         : none                                            
* Return         : >0 : check ok, return id
                      -1: check fail
*******************************************************************************/
static s32 sensorCheckId(void)
{
	INT8U u8Buf[3];
	INT32U id;
	
	u8Buf[0] = sensor_api.sensorIdent.id_reg;
	if(sensor_api.sensorIdent.addr_num == 2)
	{
		u8Buf[0] = sensor_api.sensorIdent.id_reg>>8;
		u8Buf[1] = sensor_api.sensorIdent.id_reg;
	}
	//hal_sysDelayMS(100);
	
	id = sensor_iic_read(u8Buf);
	deg_Printf("[Sensor ID] %x, %x, %x, %x\n", id, sensor_api.sensorIdent.id, sensor_api.sensorIdent.w_cmd, sensor_api.sensorIdent.r_cmd);

	if(id == sensor_api.sensorIdent.id)
		return id;
	else
		return -1;
}
/*******************************************************************************
* Function Name  : sensorCheckId
* Description    : senor check id
* Input          : sensor_adpt *p_sensor_cmd : sensor op
* Output         : none                                            
* Return         : >0 : check ok, return id
                      -1: check fail
*******************************************************************************/
static s32 sensor_res_tab_load(void)
{
	
	if(sensor_api.sensor_index < 0)
		return 0;
	if(sensor_api.sensorIdent.sensor_struct_addr)
	{
		hal_spiFlashRead(SEN_RES_FLASH_ADDR(sensor_api.sensorIdent.sensor_struct_addr),(u32)&sensor_api.sensorAdpt,sensor_api.sensorIdent.sensor_struct_size);
		//sensor_api.sensorIdent.sensor_struct_addr = addr;
		if(sensor_api.sensorIdent.sensor_init_tab_adr)
		{
			sensor_api.psensor_init_tab = (u8*)hal_sysMemMalloc(sensor_api.sensorIdent.sensor_init_tab_size);
			if(sensor_api.psensor_init_tab == NULL)
			{
				//hal_sysMemFree(sensor_api.sensorIdent.sensor_struct_addr);
				return -1;
			}
			hal_spiFlashRead(SEN_RES_FLASH_ADDR(sensor_api.sensorIdent.sensor_init_tab_adr),(u32)sensor_api.psensor_init_tab,sensor_api.sensorIdent.sensor_init_tab_size);
		}
		return 0;
	}
	return -1;
}
/*******************************************************************************
* Function Name  : sensorAutoCheck
* Description    : auto check,find support sensor in initial table
* Input          : none
* Output         : none                                            
* Return         : sensor_adpt *p_sensor_cmd : support sensor
*******************************************************************************/
static void  sensorAutoCheck(void)
{
	int i,sensor_num;
	//sensor_api.sensor_index 	= -1;
	if(hardware_setup.cmos_sensor_en)
	{
		hal_spiFlashRead(SEN_RES_FLASH_ADDR(&RES_SensorHeader),(u32)&sensor_api.sensorHead,sizeof(Sensor_Header_T));
		sensor_num = sensor_api.sensorHead.header_items_total_size/ sensor_api.sensorHead.header_item_size;
		deg_Printf("[Sensor] res tab:[addr: %x, num:%d]\n",SEN_RES_FLASH_ADDR(sensor_api.sensorHead.header_items_addr), sensor_num);
		
		for(i = 0; i< sensor_num; i++)
		{
			hal_spiFlashRead(SEN_RES_FLASH_ADDR(sensor_api.sensorHead.header_items_addr + i*sensor_api.sensorHead.header_item_size),
							(u32)&sensor_api.sensorIdent,sensor_api.sensorHead.header_item_size);
			if(sensorCheckId() > 0)
			{
				sensor_api.sensor_index = sensor_num;
				break;
			}
		}

		if(sensor_res_tab_load() < 0)
		{
			deg_Printf("Sensor res tab load fail\n");
			sensor_api.sensor_index = -1;
		}
	}else
	{
		sensor_api.sensor_index = -1;
	}
	if(sensor_api.sensor_index < 0)
	{
		deg_Printf("Sensor ColorBar\n");
		memcpy((void*)&sensor_api.sensorIdent, (void*)&test_img_init,sizeof(Sensor_Ident_T));	
		memcpy((void*)&sensor_api.sensorAdpt, (void*)sensor_api.sensorIdent.sensor_struct_addr,sizeof(Sensor_Adpt_T));
		u16 lcd_w, lcd_h;
		if(hal_lcdGetVideoResolution(&lcd_w,&lcd_h) >= 0)
		{
			//lcd_w = hx330x_max(lcd_w, lcd_h);	
			sensor_api.sensorAdpt.senPixelw = sensor_api.sensorAdpt.senCropW_Ed = sensor_api.sensorAdpt.pixelw = lcd_w;
			sensor_api.sensorAdpt.senPixelh = sensor_api.sensorAdpt.senCropH_Ed = sensor_api.sensorAdpt.pixelh = lcd_h;
		}	
		
	}else
	{
		deg_Printf("SENSOR[id: %d] online\n",sensor_api.sensorIdent.id);
	}
	//return sensor_api.id;
}
/*******************************************************************************
* Function Name  : sensorAutoCheck
* Description    : auto check,find support sensor in initial table
* Input          : none
* Output         : none                                            
* Return         : sensor_adpt *p_sensor_cmd : support sensor
*******************************************************************************/
static void  sensorTabInit(u8 * init_tab)
{
	int i;
	if(init_tab == NULL || sensor_api.sensor_index < 0)
		return;
	for(i = 0; ;i+= sensor_api.sensorIdent.addr_num + sensor_api.sensorIdent.data_num)
	{
		if((init_tab[i] == 0xff)&&(init_tab[i+1] == 0xff))
			break;
		sensor_iic_write(&init_tab[i]);
		if(i == 0)
			hal_sysDelayMS(10);
	}
}

/*******************************************************************************
* Function Name  : sensor_rgbgamma_tab_load
* Description    : u32 rgbgma_num
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void sensor_rgbgamma_tab_load(u32 rgbgma_num0, u32 rgbgamma_num1)
{
	//if(rgbgma_num0 >= SENSOR_RGBGAMMA_CLASSES)
	//{
	//	rgbgma_num0 = 3;
	//}
	//deg_Printf("rgb gamma:%d,%d\n",rgbgma_num0,rgbgamma_num1);
	hal_spiFlashRead(SEN_RES_FLASH_ADDR(sensor_rgb_gamma[rgbgma_num0]),(u32)sensor_api.user_rgb_gamma_tab,_RGB_GAMA_STEPS_);	
	hal_spiFlashRead(SEN_RES_FLASH_ADDR(sensor_rgb_gamma[rgbgamma_num1]),(u32)&sensor_api.user_rgb_gamma_tab[_RGB_GAMA_STEPS_],_RGB_GAMA_STEPS_);	
	if(!(hx330x_data_check(sensor_api.user_rgb_gamma_tab[0],0,64)&&hx330x_data_check(sensor_api.user_rgb_gamma_tab[_RGB_GAMA_STEPS_*2-1],_RGB_GAMA_MAX_VAL_-32,_RGB_GAMA_MAX_VAL_)))
	{
		deg_Printf("user rgbgamma resource_load err\n");
		//while(1);
	}
}
/*******************************************************************************
* Function Name  : sensor_ygamma_tab_load
* Description    : u32 ygamma_num
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void sensor_ygamma_tab_load(u32 ygamma_num0,u32 ygamma_num1)
{
	//if(ygamma_num >= SENSOR_YGAMMA_CLASSES)
	//{
	//	ygamma_num = 16;
	//}
	//deg_Printf("ygamma:%d,%d\n",ygamma_num0,ygamma_num1);
	hal_spiFlashRead(SEN_RES_FLASH_ADDR(sensor_ygamma_tab[ygamma_num0]),(u32)sensor_api.user_ygamma_tab,_YGAMA_STEPS_*2);	
	hal_spiFlashRead(SEN_RES_FLASH_ADDR(sensor_ygamma_tab[ygamma_num1]),(u32)&sensor_api.user_ygamma_tab[_YGAMA_STEPS_],_YGAMA_STEPS_*2);	
	if(!(hx330x_data_check(sensor_api.user_ygamma_tab[0],0,64)&&hx330x_data_check(sensor_api.user_ygamma_tab[_YGAMA_STEPS_*2-1],_YGAMA_MAX_VAL_-32,_YGAMA_MAX_VAL_)))
	{
		deg_Printf("user ygamma resource_load err\n");
		//while(1);
	}
}
/*******************************************************************************
* Function Name  : sensor_lsc_tab_load
* Description    : none
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void sensor_lsc_tab_load(void)
{
	if(sensor_api.sensorIdent.lsc_tab_adr)
	{
		hal_spiFlashRead(SEN_RES_FLASH_ADDR(sensor_api.sensorIdent.lsc_tab_adr),(u32)sensor_api.user_lsc_tab,sensor_api.sensorIdent.lsc_tab_size);	
	}
}

/*******************************************************************************
* Function Name  : SensorGetName
* Description    : get Sensor name
* Input          :
* Output         : none                                            
* Return         : char *
*******************************************************************************/
char *SensorGetName(void)
{
	return (char *)sensor_api.sensorIdent.sensor_name;
}

/*******************************************************************************
* Function Name  : sensorInit
* Description    : initial cmos sensor
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void sensorInit(void)
{
	sensor_api.sensor_index 	= -1;
	sensor_api.psensor_init_tab	= NULL;
	//hx330x_csiMclkSet(16000000,MCLK_SRC_SYSPLL); // default mclk for id check
	//hx330x_sysLDOSet(SYS_LDO_VDDSENCORE,SYS_VOL_V1_8,1);		
	//hx330x_sysCpuMsDelay(1);
	//hx330x_sysLDOSet(SYS_LDO_VDDSENIO,SYS_VOL_V3_0,1);	
	//hx330x_sysCpuMsDelay(1);
	//sensor_iic_enable();
	//hal_sysDelayMS(100);
	sensorAutoCheck();

	/*hx330x_sysLDOSet(SYS_LDO_VDDSENCORE,sensor_api.sensorAdpt.sensorCore,1);		
	hx330x_sysCpuMsDelay(1);
	hx330x_sysLDOSet(SYS_LDO_VDDSENIO,sensor_api.sensorAdpt.sensorIo,1);	
	hx330x_sysCpuMsDelay(1);*/
	//deg_Printf("[sensor_api]--sensorInit-sensor_api.sensorAdpt.sensorIo=%d\r\n",sensor_api.sensorAdpt.sensorIo);

	//hx330x_csiMclkSet(sensor_api.sensorAdpt.mclk, sensor_api.sensorAdpt.mclk_src);
	if(sensor_api.sensorAdpt.typ & CSI_TYPE_MIPI)
	{
#if (CURRENT_CHIP == FPGA)
		XSFR_CLK_CON2 |= BIT(16);                 //RC10M en 
		hx330x_sysCpuNopDelay(1000);        //delay atleast 100us
#endif	
		hx330x_MipiCSIInit(&sensor_api.sensorAdpt.mipi_adapt);
	}
	sensorTabInit((u8*)sensor_api.psensor_init_tab);
	if(sensor_api.psensor_init_tab)
		hal_sysMemFree(sensor_api.psensor_init_tab);
	sensor_api.psensor_init_tab = NULL;
	hal_SensorRegister(&sensor_api);

	hal_sensor_fps_adpt(0,sensor_api.sensorAdpt.hvb_adapt.fps);
	
	hx330x_csiInit(sensor_api.sensorAdpt.typ);
#if 0 //USER ee & cfg denoise tab cfg
	hx330x_isp_mask_tab_cfg(user_ee_sharp_tab,user_ee_dn_tab, user_ccf_dn_tab);
#else
	hx330x_isp_mask_tab_cfg(NULL,NULL, NULL);
#endif
	hal_csi_init();
	
}
/*******************************************************************************
* Function Name  : sensorInit
* Description    : initial cmos sensor
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void sensorUinit(void)
{
	sensor_api.sensor_index 	= -1;
	sensor_api.psensor_init_tab	= NULL;
	hal_SensorRegister(NULL);
	sensor_iic_disable();
	if(sensor_api.sensorAdpt.typ & CSI_TYPE_MIPI)
	{
		hx330x_MipiCSIUinit();
	}
	
}
/*******************************************************************************
* Function Name  : dev_sensor_init
* Description    : initial cmos sensor
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_sensor_init(void)
{
	/*if(hardware_setup.cmos_sensor_en)
	{
		hx330x_csiMclkSet(16000000,MCLK_SRC_SYSPLL); // default mclk for id check
		hx330x_sysLDOSet(SYS_LDO_VDDSENCORE,SYS_VOL_V1_8,1);		
		hx330x_sysCpuMsDelay(1);
		hx330x_sysLDOSet(SYS_LDO_VDDSENIO,SYS_VOL_V3_0,1);	
		hx330x_sysCpuMsDelay(1);
	}*/
	
	return 0;
}
/*******************************************************************************
* Function Name  : dev_sensor_ioctrl
* Description    : dev_sensor_ioctrl
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_sensor_ioctrl(INT32U op,INT32U para)
{
	if(op == DEV_SENSOR_INIT)
	{
	/*	hx330x_csiMclkSet(16000000,MCLK_SRC_SYSPLL); // default mclk for id check
		hx330x_sysLDOSet(SYS_LDO_VDDSENCORE,SYS_VOL_V1_8,1);		
		hx330x_sysCpuMsDelay(1);
		hx330x_sysLDOSet(SYS_LDO_VDDSENIO,SYS_VOL_V3_0,1);	
		hx330x_sysCpuMsDelay(1);*/
		sensorInit();
		hal_mjpA_EncodeInit();
		hx330x_csi_common_int_set();
		hal_csiEnable(1);
		return 0;
	}else if(op == DEV_SENSOR_UINIT)
	{
		hal_csiEnable(0);
		hal_mjpA_EncodeUninit();
		sensorUinit();
		return 0;
	}else if(op == DEV_SENSOR_CHECK)
	{
		if(sensor_api.sensor_index < 0)
			return SENSOR_STAT_NULL;
		else
			return SENSOR_STAT_NORMAL;
	}
	return -1;
}
#if HAL_CFG_PQTOOL_EN
/*******************************************************************************
* Function Name  : get_sensor_api
* Description    : get_sensor_api
* Input          : none
* Output         : none
* Return         : SENSOR_API_T*
*******************************************************************************/
SENSOR_API_T* get_sensor_api(void)
{
	return &sensor_api;
}
#endif
