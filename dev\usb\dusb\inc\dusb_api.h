
#ifndef __USB_API_H__
#define __USB_API_H__

#include "dusb_enum.h"
#include "dusb_msc.h"
#include "dusb_uac.h"
#include "dusb_uvc.h"
#include "dusb_tool_api.h"

#define USB_DEVTYPE_MSC					0
#define USB_DEVTYPE_COMBINE				1			

//USB Vender ID & Product ID
 
#define DEV_VID                        	0x349C 
#define DEV_PID                        	0x3301


#define DEV_TXEP_UVC        			USB20_EP2  
#define DEV_RXEP_UVC        			USB20_EP2 
#define DEV_TXEP_UAC        			USB20_EP3  
#define DEV_RXEP_UAC        			USB20_EP3
#define DEV_TXEP_KEY        			USB20_EP4//USB20_EP4
#define DEV_RXEP_KEY        			USB20_EP4//USB20_EP4 
#define DEV_TXEP_MASS       			USB20_EP1//USB20_EP1   //DEV IN 端点，DEV发送
#define DEV_RXEP_MASS       			USB20_EP1//USB20_EP1   //DEV OUT端点，DEV接收




//UVC定义了5种分辨率
//1280,720
#define SOL1							1
#define SOL1_W							1280L
#define SOL1_H							720L
//640,480
#define SOL2							2
#define SOL2_W							640L
#define SOL2_H							480L
//1920,1080
#define SOL3							3
#define SOL3_W							1920L
#define SOL3_H							1080L
//1280,960
#define SOL4							4
#define SOL4_W							1280L
#define SOL4_H							960L
//320,240
#define SOL5							5
#define SOL5_W							320L
#define SOL5_H							240L

#define DEFAULT_SOL						SOL1
#define MAX_SOL							5

#define SOL1_MIN_BITRATE				(SOL1_W*SOL1_H*2*15L)
#define SOL1_MAX_BITRATE				(SOL1_W*SOL1_H*2*30L)
#define SOL1_VIDEO_FRAME_SIZE 			(SOL1_W*SOL1_H*2)

#define SOL2_MIN_BITRATE				(SOL2_W*SOL2_H*2*15L)
#define SOL2_MAX_BITRATE				(SOL2_W*SOL2_H*2*30L)
#define SOL2_VIDEO_FRAME_SIZE 			(SOL2_W*SOL2_H*2)

#define SOL3_MIN_BITRATE				(SOL3_W*SOL3_H*2*15L)
#define SOL3_MAX_BITRATE				(SOL3_W*SOL3_H*2*30L)
#define SOL3_VIDEO_FRAME_SIZE 			(SOL3_W*SOL3_H*2)

#define SOL4_MIN_BITRATE				(SOL4_W*SOL4_H*2*15L)
#define SOL4_MAX_BITRATE				(SOL4_W*SOL4_H*2*30L)
#define SOL4_VIDEO_FRAME_SIZE 			(SOL4_W*SOL4_H*2)

#define SOL5_MIN_BITRATE				(SOL5_W*SOL5_H*2*15L)
#define SOL5_MAX_BITRATE				(SOL5_W*SOL5_H*2*30L)
#define SOL5_VIDEO_FRAME_SIZE 			(SOL5_W*SOL5_H*2)

#define USB_MIC_SAMPLE					32000L	
#define USB_MIC_ISO_MAXSIZE				(USB_MIC_SAMPLE*2/1000+4)
#define USB_UVC_ISO_MAXSIZE				(944)

// Startup Command Request Packet
typedef struct
{
    u8 	bmRequestType;
    u8 	bRequest;
    u16 wValue;
    u16 wIndex;
    u16 wLength;
}EP0_REQ;
typedef struct {
//	unsigned long		Signature;
	u32		CbwTag;
	u32		CbwTrxLength;
	u32		CbwFlag;
	u32		CbwLun;
//	unsigned char		CbwCbLen;

	u32		OpCode;
	u32		SubOpCode;
	u32		Address;
	u32		SubEx;
	u32		Length;
	u32		Residue;
    u32		SubEx1;
    u32		SubEx2;
    u32		SubEx3;
    u32     Func1;
    u32     DataAddr;
    u32     Func2;
    u32     Param;
	//unsigned long		TrxLen;
//	unsigned char		Page;
} MSC_CMD;

typedef struct
{
	u8 pc_move;  //false:卡在线
	u8 epxin;
	u8 epxout;
	u8 bstall;
	u8 sense;
	u8 cswsta;
	
	//u8 *pcsw;
	//u8 *pcbw;
	u8 *ptxbuf;
	u8 *prxbuf;
	s32 (*disk_rd_func)(void *pDataBuf, u32 dwLBA, u32 dwLBANum);
	s32 (*disk_wr_func)(void *pDataBuf, u32 dwLBA, u32 dwLBANum);	
	bool (*disk_online_func)(void);
	u32 (*disk_cap_func)();
	void (*disk_free_func)(void);
	void (*disk_busy_func)(void);
	bool (*disk_stop_func)(void);
}SCSI;

typedef struct VC_Probe_Commit_t {
	u16  wHint; //D0: dwFrameInterval,D1: wKeyFrameRate,D2: wPFrameRate,D3: wCompQuality,D4: wCompWindowSize
	u8   bFormatIndex;
	u8   bFrameIndex;
	u32  dwFrameInterval;
	u16  wKeyFrameRate;
	u16  wPFrameRate;
	u16  wCompQuality;
	u16  wCompWindowSize;
    u16  wDelay;
    u32  dwMaxVideoFrameSize;
	u32  dwMaxPayloadTransferSize;
	u16  wAlign;
} __attribute__((packed)) VC_Probe_Commit, *pVC_Probe_Commit;

typedef struct VC_STILL_Probe_Commit_t {
	u8   bFormatIndex;
	u8   bFrameIndex;
	u8   bCompressionIndex;
	u32  dwMaxVideoFrameSize;
	u32  dwMaxPayloadTransferSize;
	u8   bAlign;
}__attribute__((packed)) VC_STILL_Probe_Commit, *pVC_STILL_Probe_Commit;

typedef struct VS_Probe_RES_t {
	u16  wWidth;
	u16  wHeight;
} VS_Probe_RES, *pVS_Probe_RES;
typedef bool (*SETREQ_FUNC) (uint8_t *);
typedef struct {
	EP0_REQ request;
	u8  *ep0_fifo_buf;  //usb ep0 fifo地址
	u8  *pdev;
	u8  *pcfg;
	u16 devlen;
	u16 cfglen;	
	
	u8 	connect;
    u8 	set_addr;                    //设地址
    u8 	cfg_value;                   //Config Value, 用于Set Config与Get Config
    u8 	cfg_state;                   //USB当前状态机
    u8 	ep0_state;                   //EP0的状态机
    u8  ep0_pkt;                     //EP0包大小
	u16 ep0_len;                    //当前发送剩余
	
    u8 *ep0_ptr;                    //当前发送指针
	
    SETREQ_FUNC pSetReqCallback;
    u8  uvc_tx_interface;           //ISOC，MIC发送的接口
	u8  uac_tx_interface;           //ISOC，MIC发送的接口
	volatile u8  uvc_on_flag;				
    volatile u8  uac_on_flag;                //ISOC使能
	
	volatile u8 returnmaskcombo;			 
    volatile u8 msc_tx_stall;       //ep1 tx stall标志
    volatile u8 msc_rx_stall;       //ep1 rx stall标志
    volatile u8 msc_pipe_stall;     //ep1 Stall的标志
	MSC_CMD  MscCmd;
	SCSI 	 scsi; 
	
	//uac
	u32      uac_vol_cur;
	u32      uac_vol_max;
	u8*		 uac_frame_buf;
	u32		 uac_frame_len;
	u32      uac_iso_len;
	u8       uac_samplerate[4];
	u8       uac_vol_buf[3];
	u8       uac_mute;	
	
	//uvc
	u32      uvc_ptsync;
	u16*     pselector;         //指向当前的UVC UNIT参数，动态配置
	u8*		 uvc_frame_buf;
	u32		 uvc_frame_len;
	volatile u32 uvc_st;
	VS_Probe_RES 			uvc_res;
	VC_Probe_Commit 		vc_probe_commit_value;
	VC_STILL_Probe_Commit 	vc_still_probe_commit_value;
	u8 		 uvc_unitsel[0x14*8*2];
	SETREQ_FUNC unit_callback[0x14];
	//uvc 拍照
	u32      uvc_picsta;
	u8       uvc_picbuf[4];
	volatile u32  key_flag;
	//工具使用
	u32      addr32;
	
} USB_DEV_CTL;

extern USB_DEV_CTL usb_dev_ctl;


/*******************************************************************************
* Function Name  : dusb_api_online
* Description    : dusb_api_online
* Input          : None
* Output         : None
* Return         : u8 : >0 usb dev is connect
*******************************************************************************/
u8 dusb_api_online(void);

/*******************************************************************************
* Function Name  : dusb_api_offline
* Description    : dusb_api_offline
* Input          : None
* Output         : None
* Return         : none
*******************************************************************************/
void dusb_api_offline(void);


/*******************************************************************************
* Function Name  : dusb_api_Init
* Description    : dusb_api_Init
* Input          : None
* Output         : None
* Return         :
*******************************************************************************/
void dusb_api_Init(u8 mod);

/*******************************************************************************
* Function Name  : dusb_api_Uninit
* Description    : dusb_api_Uninit
* Input          : None
* Output         : None
* Return         :
*******************************************************************************/
void dusb_api_Uninit(void);

/*******************************************************************************
* Function Name  : dusb_api_Process
* Description    : dev layer.usb device process 
* Input          :  
* Output         : None
* Return         : none
*******************************************************************************/
bool dusb_api_Process(void);


/*******************************************************************************
* Function Name  : dev_dusb_init
* Description    : dev_dusb_init
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_dusb_init(void);
/*******************************************************************************
* Function Name  : dev_dusb_ioctrl
* Description    : dev_dusb_ioctrl
* Input          : NONE
* Output         : none                                            
* Return         : none
*******************************************************************************/
int dev_dusb_ioctrl(INT32U op,INT32U para);

#endif