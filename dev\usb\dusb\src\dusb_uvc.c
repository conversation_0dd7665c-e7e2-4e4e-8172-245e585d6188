/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../../hal/inc/hal.h"


ALIGNED(4) const VS_Probe_RES uvc_res_tab[] = {
	{SOL1_W, SOL1_H},
	{SOL2_W, SOL2_H},
	{SOL3_W, SOL3_H},
	{SOL4_W, SOL4_H},
	{SOL5_W, SOL5_H},
};

ALIGNED(4) const VC_Probe_Commit vc_probe_commit_desc = {
	.wHint                       = 0,
	.bFormatIndex                = 1,
	.bFrameIndex                 = 1,
	.dwFrameInterval             = UVC_30FPS,
	.wKeyFrameRate               = 0, //(not supported)
	.wPFrameRate                 = 0, //(not supported)
	.wCompQuality                = 0, //(not supported)
	.wCompWindowSize             = 0, //(not supported)
    .wDelay                      = 0, //(not supported)
    .dwMaxVideoFrameSize         = 1280L*720L*2,//1920L*1080L*2,
    .dwMaxPayloadTransferSize    = ISO_MAX_SIZE_HS

};
ALIGNED(4) const VC_STILL_Probe_Commit vc_still_probe_commit_desc = {
	.bFormatIndex                = 1,
	.bFrameIndex                 = 1,
	.bCompressionIndex           = 0,
	.dwMaxVideoFrameSize         = 1280L*720L*2,//1920L*1080L*2,
	.dwMaxPayloadTransferSize    = ISO_MAX_SIZE_HS

};
ALIGNED(4) const SETREQ_FUNC unit_callback[0x14] = {
	unitsel_set,			//0x00	
    unitsel_set,			//0x01
    unitsel_set,            //0x02
    unitsel_set,	        //0x03
    unitsel_set,			//0x04
    unitsel_set,			//0x05
    unitsel_set,		    //0x06
    unitsel_set,            //0x07
    unitsel_set,			//0x08
    unitsel_set,			//0x09
    unitsel_set,			//0x0a
    unitsel_set,			//0x0b
    unitsel_set,			//0x0c
    unitsel_set,			//0x0d
    unitsel_set,			//0x0e
    unitsel_set,			//0x0f
    unitsel_set,			//0x10
    unitsel_set,			//0x11
    unitsel_set,			//0x12
    unitsel_set,			//0x13
};
ALIGNED(4) const u16 uvc_ctl_tab[0x14][8]=
{
    //小端
	//type ,UVC_GET_CUR, UVC_GET_MIN, UVC_GET_MAX, UVC_GET_RES(分辨率), UVC_GET_LEN, UVC_GET_INFO, UVC_GET_DEF(光标位置)
    //0x00 PU_CONTROL_UNDEFINED	
    {0x0000, 0x0080, 	 0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0080},
    //0x01 PU_BACKLIGHT_COMPENSATION_CONTROL
    {0x0001, 0x0080,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0080},
    //0x02 PU_BRIGHTNESS_CONTROL 亮度B
    {0x0002, 0x0080,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0080},
    //0x03 PU_CONTRAST_CONTROL 对比度C
    {0x0003, 0x0080,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0080},
    //0x04 PU_GAIN_CONTROL
    {0x0004, 0x0080, 	 0x0000, 	  0x00ff,      0x0001,              0x0001,      0x0003,       0x0080},
    //0x05 PU_POWER_LINE_FREQUENCY_CONTROL
    {0x0005, 0x0001,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0001},
    //0x06 PU_HUE_CONTROL 色调H
    {0x0006, 0x0080,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0080},
    //0x07 PU_SATURATION_CONTROL 饱和度S
    {0x0007, 0x0040,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0040},
    //0x08 PU_SHARPNESS_CONTROL 清晰度P
    {0x0008, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000},
    //0x09 PU_GAMMA_CONTROL 伽马G
    {0x0009, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000},
    //0x0a PU_WHITE_BALANCE_TEMPERATURE_CONTROL
    {0x000A, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000},
    //0x0b PU_WHITE_BALANCE_TEMPERATURE_AUTO_CONTROL
    {0x000B, 0x0001,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0001},
    //0x0c PU_WHITE_BALANCE_COMPONENT_CONTROL
    {0x000C, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000},
    //0x0d PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL
    {0x000D, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000},
    //0x0e PU_DIGITAL_MULTIPLIER_CONTROL
    {0x000E, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000},
    //0x0f PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL
    {0x000F, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000},
    //0x10 PU_HUE_AUTO_CONTROL
    {0x0010, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000},
    //0x11 PU_ANALOG_VIDEO_STANDARD_CONTROL
    {0x0011, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000},
    //0x12 PU_ANALOG_LOCK_STATUS_CONTROL
    {0x0012, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000},
    //0x13 PU_CONTRAST_AUTO_CONTROL
    {0x0013, 0x0000,     0x0000,      0x00ff,      0x0001,              0x0001,      0x0003,       0x0000}
};

/*******************************************************************************
* Function Name  : uvc_epx_cfg
* Description    : uvc_epx_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_epx_cfg(void)
{
	u32 index_temp  = XSFR_USB20_SIE_EPS;
	XSFR_USB20_SIE_EPS 	= DEV_TXEP_UVC;		
	XSFR_USB20_SIE_TXPKGMAXL  	= (u8)(ISO_MAX_SIZE_FS & 0xff);//0xe4;
	XSFR_USB20_SIE_TXPKGMAXH  	= (u8)(ISO_MAX_SIZE_FS >> 8) | BIT(4)| BIT(3);//0x1b;
	XSFR_USB20_SIE_EPTX_CTRL0 	= DUSB_EPTX_CLRDATATOG | DUSB_EPTX_FLUSHFIFO;
	XSFR_USB20_SIE_EPTX_CTRL1 	= DUSB_EPTX_SETASTX|DUSB_EPTX_ISOMODE;
	XSFR_USB20_SIE_EPS     = DEV_TXEP_KEY;
	XSFR_USB20_SIE_TXPKGMAXL  	= (u8)(1024 & 0xff);//0xe4;
	XSFR_USB20_SIE_TXPKGMAXH  	= (u8)(1024 >> 8);//0x1b;
	XSFR_USB20_SIE_EPTX_CTRL0 	= DUSB_EPTX_CLRDATATOG | DUSB_EPTX_FLUSHFIFO;
	XSFR_USB20_SIE_EPTX_CTRL1 	= DUSB_EPTX_SETASTX;
	//XSFR_USB20_SIE_POWER   &= ~BIT(7);
	XSFR_USB20_SIE_POWER   |= BIT(7);
	(&XSFR_USB20_EP1_TXADDR)[DEV_TXEP_UVC*2-2]  = (u32)_USB20_UVC_FIFO_;
	(&XSFR_USB20_EP1_TXADDR)[DEV_TXEP_KEY*2-2]  = (u32)_USB20_UVCKEY_FIFO_;
	XSFR_USB20_SIE_EPS 	= index_temp;

	
	memcpy((u8 *)&usb_dev_ctl.vc_probe_commit_value,(u8 *)&vc_probe_commit_desc, sizeof(vc_probe_commit_desc));
	memcpy((u8 *)&usb_dev_ctl.vc_still_probe_commit_value,(uint8_t *)&vc_still_probe_commit_desc, sizeof(vc_still_probe_commit_desc));
	memcpy((u8 *)usb_dev_ctl.uvc_unitsel,(uint8_t *)uvc_ctl_tab, sizeof(uvc_ctl_tab));
	memcpy((u8 *)usb_dev_ctl.unit_callback,(uint8_t *)unit_callback, sizeof(unit_callback));
	//debgbuf((u8 *)&vc_probe_commit_desc,sizeof(vc_probe_commit_desc));
	//debgbuf((u8 *)&usb_dev_ctl.vc_probe_commit_value,sizeof(vc_probe_commit_desc));
	if(hx330x_usb20_HighSpeed() == true)
	{
		usb_dev_ctl.vc_probe_commit_value.dwMaxPayloadTransferSize  		= ISO_MAX_SIZE_HS;
		usb_dev_ctl.vc_still_probe_commit_value.dwMaxPayloadTransferSize 	= ISO_MAX_SIZE_HS;
	}else
	{
		usb_dev_ctl.vc_probe_commit_value.dwMaxPayloadTransferSize  		= ISO_MAX_SIZE_FS;
		usb_dev_ctl.vc_still_probe_commit_value.dwMaxPayloadTransferSize 	= ISO_MAX_SIZE_FS;		
	}
}
/*******************************************************************************
* Function Name  : uac_stop
* Description    : uac_stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool unitsel_set(u8* rxbuf)
{
	//debgbuf(rxbuf, 4);
	memcpy((u8*)&usb_dev_ctl.pselector[1],(u8*)rxbuf, usb_dev_ctl.request.wLength);
	return true;
}

/*******************************************************************************
* Function Name  : uvc_unit_ctl_hal
* Description    : uvc_unit_ctl_hal
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uvc_unit_ctl_hal(u8 val, u8 rqu, u8 len)
{	
	u16 (*punitsel)[8];
	if(val > PU_CONTRAST_AUTO_CONTROL)
		return false;
	punitsel = (u16(*)[8])usb_dev_ctl.uvc_unitsel;
    if(val == punitsel[val][0]){ //匹配unit类型
        //get
        if((rqu & 0xf0)==0x80){
			return dusb_ep0_tx((u8 *)&punitsel[val][rqu & 0x7f], len);
		}
		else if((rqu & 0x91)==0x01){
		//set
			usb_dev_ctl.pselector =  punitsel[val];
			dusb_ep0_recieve_set(usb_dev_ctl.unit_callback[val]);

			return true;				
		}					
    }
	return false;
}
/*******************************************************************************
* Function Name  : uvc_video_probe_control_callback
* Description    : uvc_video_probe_control_callback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uvc_video_probe_control_callback(u8* rxbuf)
{
	memcpy((u8 *)&usb_dev_ctl.vc_probe_commit_value,(u8 *)rxbuf, 22);
	//debg("set26:");debgbuf(rxbuf,26);
	return true;
}
/*******************************************************************************
* Function Name  : uvc_video_probe_control
* Description    : uvc_video_probe_control
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_video_probe_control(u8 request,u8 len)
{

    if (request == SET_CUR){
        dusb_ep0_recieve_set(uvc_video_probe_control_callback);
    }
    else{
		dusb_ep0_tx((uint8_t* )&usb_dev_ctl.vc_probe_commit_value,len);
    }
}
/*******************************************************************************
* Function Name  : uvc_still_probe_control_callback
* Description    : uvc_still_probe_control_callback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uvc_still_probe_control_callback(u8* rxbuf)
{
    memcpy((u8 *)&usb_dev_ctl.vc_still_probe_commit_value,rxbuf,3);
    return true;
}
/*******************************************************************************
* Function Name  : uvc_still_probe_control
* Description    : uvc_still_probe_control
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_still_probe_control(u8 request,u8 len)
{
    if (request == SET_CUR){
        dusb_ep0_recieve_set(uvc_still_probe_control_callback);
    }
    else{
		dusb_ep0_tx((uint8_t* )&usb_dev_ctl.vc_still_probe_commit_value,len);
    }
}
/*******************************************************************************
* Function Name  : uvc_pic_callback
* Description    : uvc_pic_callback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uvc_pic_callback(u8 * rxbuf)
{
	usb_dev_ctl.uvc_picsta = UVC_PICSTA_PUSH;
    return true;
}	
/*******************************************************************************
* Function Name  : uvc_still_trigger_control
* Description    : uvc_still_trigger_control
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_still_trigger_control(u8 request,u8 len)
{
    if(!(request & 0x80)){	
        dusb_ep0_recieve_set(uvc_pic_callback);
    }
}
/*******************************************************************************
* Function Name  : uvc_probe_ctl_hal
* Description    : uvc_probe_ctl_hal
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool uvc_probe_ctl_hal(u8 val, u8 rqu, u8 len)
{
    switch(val)
    {
		case /*1*/VS_PROBE_CONTROL:	
		case /*2*/VS_COMMIT_CONTROL:
			//debg("UVC_VS_PROBE_CONTROL\n");
			uvc_video_probe_control(rqu,len);
			return true;
		case /*3*/VS_STILL_PROBE_CONTROL:
		case /*4*/VS_STILL_COMMIT_CONTROL:
			//debg("UVC_VS_STILL_PROBE_CONTROL\n");
			uvc_still_probe_control(rqu,len);
			return true;
		case /*5*/VS_STILL_IMAGE_TRIGGER_CONTROL:
			//debg("UVC_VS_STILL_IMAGE_TRIGGER_CONTROL\n");
			uvc_still_trigger_control(rqu,len);
			return true;
    }	
    return false;
}
/*******************************************************************************
* Function Name  : uvc_start
* Description    : uvc_start
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_start(void)
{
	u8 winid = usb_dev_ctl.vc_probe_commit_value.bFrameIndex - 1;
	if(winid < MAX_SOL){
		if(hal_mjpA_EncVideo_Start(MJPEG_TYPE_UVC, uvc_res_tab[winid].wWidth, uvc_res_tab[winid].wHeight, JPEG_Q_50, 0) != true)
			return;
		hx330x_usb20_eptx_register(1, DEV_TXEP_UVC,uvc_isr_process);
		usb_dev_ctl.uvc_frame_buf 	= NULL;
		usb_dev_ctl.uvc_frame_len 	= 0;
		usb_dev_ctl.uvc_ptsync 		= XOSTimeGet();
		usb_dev_ctl.uvc_st 			= 1;
		usb_dev_ctl.uvc_on_flag 	= 1;
		
		//set_wave();
	}
	debg("-UVC START:[%d, %d]\n", uvc_res_tab[winid].wWidth, uvc_res_tab[winid].wHeight);

}
/*******************************************************************************
* Function Name  : uvc_stop
* Description    : uvc_stop
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_stop(void)
{	
	if(usb_dev_ctl.uvc_on_flag == 0)
		return;
	usb_dev_ctl.uvc_on_flag = 0;
	usb_dev_ctl.uvc_st = 0;
	//hal_mjpA_EncodeUninit();
	hal_mjpA_EncodeUninit();
    //hal_csiEnable(0);
	hx330x_USB20_EPTX_Flush(DEV_TXEP_UVC);
	hx330x_usb20_eptx_register(0, DEV_TXEP_UVC,NULL);
    //hal_csiEnable(0);
}
/*******************************************************************************
* Function Name  : uvc_is_start
* Description    : uvc_is_start
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u8 uvc_is_start(void)
{
	return usb_dev_ctl.uvc_on_flag;
}
/*******************************************************************************
* Function Name  : uvc_pic_sanp
* Description    : uvc_pic_sanp
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_pic_sanp(void)
{
	if(usb_dev_ctl.uvc_on_flag == 0)
	{
		return;
	}
#if 0
	//key sanp : P21
    XSFR_P2_FMAP &=~ BIT(1);
    XSFR_P2_PU   |= BIT(1);
    XSFR_P2_PD   &= ~BIT(1);
    XSFR_P2_DIR  |= BIT(1);
    //IO = 0 && picsta = 0
    if(!((bool)(XSFR_P2 & BIT(1))) && (usb_dev_ctl.key_flag == 0)){
		usb_dev_ctl.key_flag = 1;
		usb_dev_ctl.uvc_picbuf
		usb_dev_ctl.uvc_picbuf[0] = 0x02;
		usb_dev_ctl.uvc_picbuf[1] = 0x01;
		usb_dev_ctl.uvc_picbuf[2] = 0x00;
		usb_dev_ctl.uvc_picbuf[3] = 0x01;
		hx330x_iso20_tx(DEV_TXEP_KEY,usb_dev_ctl.uvc_picbuf, 4);
	//debg("-d\n");
    }
    //keypress && IO 高
    if((usb_dev_ctl.key_flag == 1) && ((BOOL)(XSFR_P2 & BIT(1)))){
		usb_dev_ctl.uvc_picbuf[3] = 0x00;
		hx330x_iso20_tx(DEV_TXEP_KEY,usb_dev_ctl.uvc_picbuf, 4);

		usb_dev_ctl.key_flag = 0;
    }
#endif
}
/*******************************************************************************
* Function Name  : uvc_header_fill
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_header_fill(u8 *buf, u8 flg)
{
	u32 ptime = XOSTimeGet();//0x01c9c380;//0x80, 0xC3, 0xC9, 0x01
	u16 softcnt = get_u16softcnt();
	hx330x_sysDcacheFlush((u32)buf,12);
#if 0
	buf[0] = 0x0c;	
	buf[1] = 0x80 | (flg & 0x01);// | BIT(2) | BIT(3);;
#else
	
			
	buf[0] = 0x0c;	
	buf[1] = 0x80 | BIT(3)| BIT(2) |flg;//| endframe |still;
	if(usb_dev_ctl.uvc_picsta == UVC_PICSTA_POP){
	    buf[1] |= 0x20;
    }
	buf[2] = (usb_dev_ctl.uvc_ptsync >> 0) & 0xff;
	buf[3] = (usb_dev_ctl.uvc_ptsync >> 8) & 0xff;
	buf[4] = (usb_dev_ctl.uvc_ptsync >> 16) & 0xff;
	buf[5] = (usb_dev_ctl.uvc_ptsync >> 24) & 0xff;
	
	buf[6] = (ptime >> 0) & 0xff;
	buf[7] = (ptime >> 8) & 0xff;
	buf[8] = (ptime >> 16) & 0xff;
	buf[9] = (ptime >> 24) & 0xff;

	buf[10] = (softcnt >> 0) & 0xff;
	buf[11] = (softcnt >> 8) & 0xff;
#endif 
	hx330x_sysDcacheWback((u32)buf, 12);
}

/*******************************************************************************
* Function Name  : uvc_process
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_process(void)
{
	#define UVC_TGL				(0<<1)
	#define UVC_END				(1<<1)
	static u8 uvc_toggle = 0;
//	u32 addr;
	//uac_process();
	if(usb_dev_ctl.uvc_on_flag == 0)
	{
		return;
	}
	u8 *buf;
	u32 len;
	u32 tlen;
	u8* uvcfifo = (u8*)_USB20_UVC_FIFO_;
	buf = hal_mjpA_RawBufferGet(&len,NULL,NULL);
	if(buf)
	{
		uvc_toggle &=~UVC_END;
		uvc_toggle ^= UVC_TGL;
		while(len)
		{
			hal_wdtClear();
			tlen = (len > (USB_UVC_ISO_MAXSIZE-12))?(USB_UVC_ISO_MAXSIZE-12):len;
			if(len > (USB_UVC_ISO_MAXSIZE-12))
			{
				tlen = USB_UVC_ISO_MAXSIZE-12;
			}else
			{
				uvc_toggle |= UVC_END;
				tlen = len;
			}
			uvc_header_fill((u8*)uvcfifo,uvc_toggle);
			hx330x_mcpy0_sdram2gram((void *)&uvcfifo[12], (void *)buf, tlen);
			hx330x_iso20_tx(DEV_TXEP_UVC, (u32)uvcfifo, tlen);
			buf += tlen;
			len -= tlen;
			//uac_process();
		}
		usb_dev_ctl.uvc_ptsync = XOSTimeGet();
		//hal_mjpA_RawBufferfree();
		hal_mjpA_RawBufferfree();
	}
	#undef UVC_TGL
	#undef UVC_END
}
/*******************************************************************************
* Function Name  : uvc_process
* Description    : 
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void uvc_isr_process(void)
{
	#define UVC_TGL				(1<<0)
	#define UVC_END				(1<<1)
	static u8 uvc_toggle = 0;
//	u32 addr;
	if(usb_dev_ctl.uvc_on_flag == 0)
	{
		return;
	}
	hx330x_intCriticalInit();
	hx330x_intCriticalEnter();
	

	if((usb_dev_ctl.uvc_frame_buf == NULL)||(usb_dev_ctl.uvc_frame_len == 0))
	{
		usb_dev_ctl.uvc_frame_buf = hal_mjpA_RawBufferGet(&usb_dev_ctl.uvc_frame_len,NULL, NULL);
	}
	if(usb_dev_ctl.uvc_frame_buf)
	{
		u8* uvcfifo = (u8*)_USB20_UVC_FIFO_;
		u32 len;
		usb_dev_ctl.uvc_st = 0;
		
		if(usb_dev_ctl.uvc_frame_len > (USB_UVC_ISO_MAXSIZE-12))
		{
			len = USB_UVC_ISO_MAXSIZE-12;
			
		}else
		{
			len = usb_dev_ctl.uvc_frame_len;
			uvc_toggle |= UVC_END;
		}
		uvc_header_fill((u8*)uvcfifo,uvc_toggle);
		hx330x_mcpy1_sdram2gram((void *)&uvcfifo[12], (void *)usb_dev_ctl.uvc_frame_buf, len);
		hx330x_iso20_tx_kick(DEV_TXEP_UVC, (u32)uvcfifo, len+12);
		usb_dev_ctl.uvc_frame_len -= len;
		if(uvc_toggle & UVC_END)
		{
			hal_mjpA_RawBufferfree();
			usb_dev_ctl.uvc_frame_buf = NULL;
			uvc_toggle &=~UVC_END;
			uvc_toggle ^= UVC_TGL;
			usb_dev_ctl.uvc_ptsync = XOSTimeGet();
		}else
		{
			usb_dev_ctl.uvc_frame_buf += len;
		}
	}else
	{
		usb_dev_ctl.uvc_st = 1;
	}

	hx330x_intCriticalExit();
	#undef UVC_TGL
	#undef UVC_END
}