/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef USB_HOST_USENSOR_H_
#define USB_HOST_USENSOR_H_


/*******************************************************************************
* Function Name  : husb_api_handle_reg
* Description    : husb_api_handle_reg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_handle_reg(void *handle);

/*******************************************************************************
* Function Name  : husb_api_usensor_tran_sta
* Description    : husb_api_usensor_tran_sta
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_usensor_tran_sta(void);
/*******************************************************************************
* Function Name  : husb_api_usensor_tran_sta
* Description    : husb_api_usensor_tran_sta
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_usensor_atech_sta(void);
/*******************************************************************************
* Function Name  : husb_api_usensor_res_get
* Description    : husb_api_usensor_res_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
s32 husb_api_usensor_res_get(u16* width, u16* height);
/*******************************************************************************
* Function Name  : husb_api_usensor_res_get
* Description    : husb_api_usensor_res_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u32 husb_api_usensor_res_type_is_mjp(void);
/*******************************************************************************
* Function Name  : husb_api_usensor_res_get
* Description    : husb_api_usensor_res_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u32 husb_api_usensor_res_type_is_yuv(void);
/*******************************************************************************
* Function Name  : husb_api_astern_set
* Description    : husb_api_astern_set
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_astern_set(bool sta);

/*******************************************************************************
* Function Name  : husb_api_astern_get
* Description    : husb_api_astern_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_astern_get(void);

/*******************************************************************************
* Function Name  : husb_api_detech_check
* Description    : husb_api_detech_check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_detech_check(void);

/*******************************************************************************
* Function Name  : husb_api_usensor_linkingLcd
* Description    : husb_api_usensor_linkingLcd
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_linkingLcd(void);

/*******************************************************************************
* Function Name  : husb_api_usensor_relinkLcd_reg
* Description    : husb_api_usensor_relinkLcd_reg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_relinkLcd_reg(void);

/*******************************************************************************
* Function Name  : husb_api_usensor_dcdown
* Description    : husb_api_usensor_dcdown
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_dcdown(u8 err);

/*******************************************************************************
* Function Name  : husb_api_usensor_detech
* Description    : husb_api_usensor_detech: for software remove usb
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_detech(void);

/*******************************************************************************
* Function Name  : husb_api_usensor_asterncheck
* Description    : husb_api_usensor_asterncheck: for usensor astern check kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_asterncheck(void);
/*******************************************************************************
* Function Name  : husb_api_usensor_asterncheck
* Description    : husb_api_usensor_asterncheck: for usensor astern check kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_asternset(u32 astern);
/*******************************************************************************
* Function Name  : husb_api_usensor_frame_read
* Description    : husb_api_usensor_frame_read
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u8 *husb_api_usensor_frame_read(u32 *len);
/*******************************************************************************
* Function Name  : husb_api_usensor_csi_kick
* Description    : husb_api_usensor_csi_kick: kick usensor yuv input to csi
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_csi_kick(void);
/*******************************************************************************
* Function Name  : husb_api_usensor_switch_res_kick
* Description    : husb_api_usensor_switch_res_kick: kick usensor to change format or frame
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_usensor_switch_res_kick(u8 switch_format, u8 switch_frame);
/*******************************************************************************
* Function Name  : husb_api_usensor_notran_check
* Description    : husb_api_usensor_notran_check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_notran_check(void);
/*******************************************************************************
* Function Name  : husb_api_usensor_stop_fill
* Description    : husb_api_usensor_stop_fill
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_usensor_stop_fill(u32 stop);

/*******************************************************************************
* Function Name  : husb_api_usensor_uvcunit_get
* Description    : husb_api_usensor_uvcunit_get
* Input          : u8 uvcunitsel: PU_BACKLIGHT_COMPENSATION_CONTROL ~ PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL
				   u8 request:GET_CUR/GET_MIN/GET_MAX/GET_RES
* Output         : None
* Return         : NULL: FAIL
*******************************************************************************/
u16 husb_api_usensor_uvcunit_get(u8 uvcunitsel, u8 request);
/*******************************************************************************
* Function Name  : husb_api_usensor_uvcunit_set
* Description    : husb_api_usensor_uvcunit_set
* Input          : u8 uvcunitsel: PU_BACKLIGHT_COMPENSATION_CONTROL ~ PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL
				   u16 val
* Output         : None
* Return         : NULL: FAIL
*******************************************************************************/
bool husb_api_usensor_uvcunit_set(u8 uvcunitsel, u16 val);

#endif /* USB_HOST_USENSOR_H_ */

