/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef USB_HOST_UVC_H_
#define USB_HOST_UVC_H_

#define _UVC_HEADER_			12	
#if  SDRAM_SIZE == SDRAM_SIZE_2M 
#if HAL_MJP_SAVE_CARD_DIRECT
#define _UVC_JPG_CACHE_SIZE		(100*1024L)
#else
#define _UVC_JPG_CACHE_SIZE		(80*1024L)
#endif
#else
#define _UVC_JPG_CACHE_SIZE		(300*1024L)
#endif
//FLOW
enum 
{
	NULL_FRAME = 0, //can use for filling jpg
	NEW_FRAME, 		//filling jpg 
	RDY_FRAME, 		//fill jpg ready
	DCD_FRAME, 		//jpg decodding 	
	BAK_FRAME, 		//use for insert frame
}UVC_CACHE_STA;	

enum
{
	UVC_ASYN = 0,
	UVC_SYNF,		//BULK传输下UVC HEADER END
	UVC_SYNC,		//BULK传输下UVC DATA
	UVC_SYNC_START,  //BULK传输下UVC HEADER START + DATA

}UVC_FSTACK_STA;
/*******************************************************************************
* Function Name  : huvc_cache_dcd_down
* Description    : huvc_cache_dcd_down
* Input          : u8 err: 1 usb recieve err
* Output         : None
* Return         : u8 sta: RDY_FRAME/NULL_FRAME
*******************************************************************************/
void huvc_cache_dcd_down(void *handle, u8 err);

/*******************************************************************************
* Function Name  : husb_uvc_frame_read
* Description    : husb_uvc_frame_read
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_uvc_frame_read(void *handle,u8**p, u32 *len);

/*******************************************************************************
* Function Name  : husb_uvc_atech_codec
* Description    : husb_uvc_atech_codec
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_uvc_atech_codec(void *handle);

/*******************************************************************************
* Function Name  : husb_uvc_init
* Description    : husb_uvc_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_uvc_init(void *handle);

/*******************************************************************************
* Function Name  : husb_uvc_linking
* Description    : husb_uvc_linking
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_uvc_linking(void* handle);

/*******************************************************************************
* Function Name  : husb_uvc_relink_register
* Description    : husb_uvc_relink_register
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_uvc_relink_register(void* handle);

/*******************************************************************************
* Function Name  : husb_uvc_detech
* Description    : husb_uvc_detech
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_uvc_detech(void* handle);
/*******************************************************************************
* Function Name  : husb_uvc_stop_fill
* Description    : husb_uvc_stop_fill
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_uvc_stop_fill(void *handle, u32 stop);


#endif /* USB_HOST_TPBULK_H_ */

