/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../../hal/inc/hal.h"
#include "../../../../app/app_common/inc/app_api.h"


//#define husb_debgbuf(a,b)	debgbuf(a,b)
#define husb_debgbuf(a,b)



/*******************************************************************************
* Function Name  : husb20_ep0_cfg
* Description    : husb20_ep0_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb20_ep0_cfg(void)
{
	XSFR_USB20_EP0_ADDR  = (u32)_USB20_EP0_FIFO_;
}
/*******************************************************************************
* Function Name  : husb11_ep0_cfg
* Description    : husb11_ep0_cfg
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb11_ep0_cfg(void)
{
	XSFR_USB11_EP0_ADDR  = (u32)_USB11_EP0_FIFO_;
}
/*******************************************************************************
* Function Name  : husb20_pidsetup
* Description    : husb20_pidsetup
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb20_pidsetup(u8 *setup)
{
	u8 t_epx = XSFR_USB20_SIE_EPS;
	XSFR_USB20_SIE_EPS = 0;
	hx330x_bytes_memcpy((u8*)_USB20_EP0_FIFO_,(u8*)setup,8);
	XSFR_USB20_EPINTLEN = 8;
	XSFR_USB20_EPINT    = BIT(0);
	husb_debgbuf((u8*)_USB20_EP0_FIFO_, 8);
	XSFR_USB20_SIE_EP0_CTRL0 	= HUSB_EP0_SETUPPKT|HUSB_EP0_TXPKTRDY;
	XSFR_USB20_SIE_EPS = t_epx;
}
/*******************************************************************************
* Function Name  : husb20_pidin_kick
* Description    : husb20_pidin_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb20_pidin_kick(void)
{
	u8 t_epx = XSFR_USB20_SIE_EPS;
	XSFR_USB20_SIE_EPS = 0;
	XSFR_USB20_SIE_EP0_CTRL0 = HUSB_EP0_STATUSPKT | HUSB_EP0_REQPKT;
	XSFR_USB20_SIE_EPS = t_epx;
}
/*******************************************************************************
* Function Name  : husb20_pidin_next
* Description    : husb20_pidin_next
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb20_pidin_next(void)
{
	u8 t_epx = XSFR_USB20_SIE_EPS;
	XSFR_USB20_SIE_EPS = 0;
	XSFR_USB20_SIE_EP0_CTRL0 &= ~HUSB_EP0_RXPKTRDY; // Clear INPktRdy bit
	XSFR_USB20_SIE_EP0_CTRL0 = HUSB_EP0_REQPKT;
	XSFR_USB20_SIE_EPS = t_epx;
}
/*******************************************************************************
* Function Name  : husb20_pidout
* Description    : husb20_pidout
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb20_pidout(u8 *pout, u8 tlen)
{
	u8 t_epx = XSFR_USB20_SIE_EPS;
	XSFR_USB20_SIE_EPS = 0;
	if((tlen !=0)&&(pout)){
		hx330x_bytes_memcpy((u8*)_USB20_EP0_FIFO_, (u8*)pout,tlen);
		XSFR_USB20_EPINTLEN = tlen;//XSFR_USB20_EPINTLEN  = len;
		XSFR_USB20_EPINT    = BIT(0);//XSFR_USB20_EPINT     = BIT(ep);
		husb_debgbuf((u8*)pout, tlen);
		XSFR_USB20_SIE_EP0_CTRL0 = HUSB_EP0_TXPKTRDY;
	}
	else
	{
		//debg("out zero\n");
		XSFR_USB20_SIE_EP0_CTRL0 = HUSB_EP0_STATUSPKT | HUSB_EP0_TXPKTRDY;

	}
	XSFR_USB20_SIE_EPS = t_epx;
}
/*******************************************************************************
* Function Name  : husb20_pidin_get
* Description    : husb20_pidin_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool husb20_pidin_get(u8 *pin, u8 *len)
{
	*len = 0;
	u8 t_epx = XSFR_USB20_SIE_EPS;
	XSFR_USB20_SIE_EPS = 0;
	U8 intr = XSFR_USB20_SIE_EP0_CTRL0;
	//send stall
	if (intr & HUSB_EP0_ERROR){
		deg_Printf("-HUSB20 EP0 ERR\n");
		XSFR_USB20_SIE_EPS = t_epx;
		return false;
	}

	// STALL detected
	if (intr & HUSB_EP0_RXSTALL){
		deg_Printf("-HUSB20 EP0 RXSTALL\n");
		XSFR_USB20_SIE_EP0_CTRL0 &= ~HUSB_EP0_RXSTALL;
		XSFR_USB20_SIE_EPS = t_epx;
		return false;                                // for unsupported request.
	}

	//in
	if (intr & HUSB_EP0_RXPKTRDY){

		u32 rlen = XSFR_USB20_SIE_EP0RXCNT;
		if(pin)
		{
			//debg("rlen:%d,pin:%x\n",rlen,pin);
			hx330x_bytes_memcpy((u8*)pin,(u8*)_USB20_EP0_FIFO_,rlen);
			husb_debgbuf((u8*)pin,rlen);
		}
		XSFR_USB20_SIE_EP0_CTRL1 = BIT(0); //FLUSH FIFO
		*len = rlen;
		XSFR_USB20_SIE_EP0_CTRL0 &= ~HUSB_EP0_RXPKTRDY;
		XSFR_USB20_SIE_EPS = t_epx;
		return true;
	}
	if(!(intr & HUSB_EP0_TXPKTRDY)){
		//deg("out end\n");
		XSFR_USB20_SIE_EPS = t_epx;
		return true;
	}
	deg_Printf("?");
	return 	false;
}
/*******************************************************************************
* Function Name  : husb11_pidsetup
* Description    : husb11_pidsetup
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb11_pidsetup(u8 *setup)
{
	u8 t_epx        = XSFR_USB11_SIE_EPS;
	XSFR_USB11_SIE_EPS     = 0;//USB_Index = 0;
	hx330x_bytes_memcpy((u8*)_USB11_EP0_FIFO_,(u8*)setup, 8);
	XSFR_USB11_EPINTLEN = 8;
	XSFR_USB11_EPINT    = BIT(0);
	husb_debgbuf((u8*)_USB11_EP0_FIFO_, 8);
	XSFR_USB11_SIE_EP0_CTRL0     = HUSB_EP0_SETUPPKT|HUSB_EP0_TXPKTRDY;
	//debg("XSFR_USB11_SIE_EP0_CTRL0:%x\n",XSFR_USB11_SIE_EP0_CTRL0);
	//debg("-XSFR_USB11_SIE_EP0_CTRL0:%x\n",XSFR_USB11_SIE_EP0_CTRL0);
	//debg("-XSFR_USB11_EP0_ADDR:%x\n",XSFR_USB11_EP0_ADDR);
	XSFR_USB11_SIE_EPS    = t_epx;

}
/*******************************************************************************
* Function Name  : husb11_pidin_kick
* Description    : husb11_pidin_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb11_pidin_kick(void)
{
	u8 t_epx        = XSFR_USB11_SIE_EPS;//u8 t_epx = USB_Index;
	XSFR_USB11_SIE_EPS     = 0;
	XSFR_USB11_SIE_EP0_CTRL0      = HUSB_EP0_STATUSPKT|HUSB_EP0_REQPKT;
	XSFR_USB11_SIE_EPS     = t_epx;//USB_Index = t_epx;
}
/*******************************************************************************
* Function Name  : husb11_pidin_next
* Description    : husb11_pidin_next
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb11_pidin_next(void)
{
	u8 t_epx        = XSFR_USB11_SIE_EPS;//u8 t_epx = USB_Index;
	XSFR_USB11_SIE_EP0_CTRL0     &= ~HUSB_EP0_RXPKTRDY; // Clear INPktRdy bit
	XSFR_USB11_SIE_EPS     = 0;
	XSFR_USB11_SIE_EP0_CTRL0      = HUSB_EP0_REQPKT;
	XSFR_USB11_SIE_EPS     = t_epx;
}
/*******************************************************************************
* Function Name  : husb11_pidout
* Description    : husb11_pidout
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void husb11_pidout(u8 *pout, u8 tlen)
{
	u8 t_epx        = XSFR_USB11_SIE_EPS;
	XSFR_USB11_SIE_EPS     = 0;
	if((tlen != 0)&&(pout)){
		hx330x_bytes_memcpy((u8*)_USB11_EP0_FIFO_,(u8*)pout,tlen);
		XSFR_USB11_EPINTLEN = tlen;
		XSFR_USB11_EPINT    = BIT(0);
		XSFR_USB11_SIE_EP0_CTRL0     = HUSB_EP0_TXPKTRDY;
		husb_debgbuf((u8*)pout, tlen);
	}
	else
	{
		XSFR_USB11_SIE_EP0_CTRL0     = HUSB_EP0_STATUSPKT|HUSB_EP0_TXPKTRDY;
	}
	XSFR_USB11_SIE_EPS     = t_epx;

}
/*******************************************************************************
* Function Name  : husb11_pidin_get
* Description    : husb11_pidin_get
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static bool husb11_pidin_get(u8 *pin, u8 *len)
{
	*len = 0;
	u8 t_epx        = XSFR_USB11_SIE_EPS;//u8 t_epx = USB_Index;
	XSFR_USB11_SIE_EPS     = 0;
	u8 intr         =  XSFR_USB11_SIE_EP0_CTRL0;
	//debg("CSR0:%x\n",intr);
	//send stall
	if (intr & HUSB_EP0_ERROR){
		deg_Printf("-HUSB11 EP0 ERR\n");
		XSFR_USB11_SIE_EPS     = t_epx;
		return false;
	}

	// STALL detected
	if (intr & HUSB_EP0_RXSTALL){
		deg_Printf("-HUSB11 EP0 RXSTALL\n");
		XSFR_USB11_SIE_EP0_CTRL0      &= ~HUSB_EP0_RXSTALL;
		XSFR_USB11_SIE_EPS     = t_epx;
		return false;                                // for unsupported request.
	}

	//in
	if (intr & HUSB_EP0_RXPKTRDY){
		//u32 i;
		u32 rlen = XSFR_USB11_SIE_EP0RXCNT;
		if(rlen && pin)
		{
			hx330x_bytes_memcpy((u8*)pin,(u8*)_USB11_EP0_FIFO_, rlen);
		}
		*len = rlen;

		XSFR_USB11_SIE_EP0_CTRL0 &= ~HUSB_EP0_RXPKTRDY;
		XSFR_USB11_SIE_EPS = t_epx;
		return true;
	}
	if(!(intr & HUSB_EP0_TXPKTRDY)){
		//debg("out end\n");
		XSFR_USB11_SIE_EPS = t_epx;
		return true;
	}
	deg_Printf("?");
	return 	false;

}
/*******************************************************************************
* Function Name  : husb_pid_func
* Description    : husb_pid_func
* Input          : None
* Output         : None
* Return         : PID_PHASE_NEXT, ENUM_PHASE_NEXT, ENUM_FAIL
*******************************************************************************/
static u8 husb_pid_func(HUSB_HANDLE *pHusb_handle)
{
	//send usb cmd
	//GET DESC: PID_SETUP_PHASE -> PID_KICK_PHASE ->PID_IN_PHASE(DESC) 'N + PID_OUT_PHASE->SETUP_END_PHASE
	//SET DAT:  PID_SETUP_PHASE -> PID_OUT_PHASE(DAT)  ->PID_KICK_PHASE->PID_IN_PHASE(NULL)
	//SET DESC: PID_SETUP_PHASE -> PID_KICK_PHASE ->PID_IN_PHASE(NULL)
	//deg_Printf("pid_phase:%d\n",pHusb_handle->usbsta.pid_phase);
	switch(pHusb_handle->usbsta.pid_phase)
	{
		case PID_SETUP_PHASE:
		{
			if(pHusb_handle->usbsta.ch == USB20_CH)
				husb20_pidsetup((u8*)&pHusb_handle->setup);
			else
				husb11_pidsetup((u8*)&pHusb_handle->setup);
			pHusb_handle->usbsta.pid_dir   	= (bool)(pHusb_handle->setup.mrequest &0x80); //1:get, 0:set
			pHusb_handle->usbsta.pid_phase 	= PID_KICK_PHASE;
			pHusb_handle->usbsta.inlen		= pHusb_handle->setup.length;
			if((!pHusb_handle->usbsta.pid_dir)&&(pHusb_handle->usbsta.inlen != 0))//SET DAT
			{
				pHusb_handle->usbsta.pid_phase = PID_OUT_PHASE;
			}
			return PID_PHASE_NEXT;
		}
		case PID_KICK_PHASE:
		{
			if(pHusb_handle->usbsta.ch == USB20_CH)
				husb20_pidin_kick();
			else
				husb11_pidin_kick();
			pHusb_handle->usbsta.pid_phase = PID_IN_PHASE;
			return PID_PHASE_NEXT;
		}
		case PID_IN_PHASE:
		{
			bool (*Pidin_get)(u8 *, u8 *);
			u8 getlen;
			if(pHusb_handle->usbsta.ch == USB20_CH)
			{
				Pidin_get = husb20_pidin_get;
			}else
			{
				Pidin_get = husb11_pidin_get;
			}
			if( false == Pidin_get(pHusb_handle->usbsta.dsc_buf,&getlen)){
				pHusb_handle->usbsta.pid_phase = PID_SETUP_PHASE;
				return ENUM_FAIL;
			}
			if(!pHusb_handle->usbsta.pid_dir) //SET
			{
				pHusb_handle->usbsta.pid_phase = PID_SETUP_PHASE;
				return ENUM_PHASE_NEXT;
			}else{
				//debg("inlen:%x,%x\n",usbsta.inlen,getlen);
				if(pHusb_handle->usbsta.inlen > getlen){
					pHusb_handle->usbsta.inlen -= getlen;
					pHusb_handle->usbsta.dsc_buf += getlen;
					if(pHusb_handle->usbsta.ch == USB20_CH)
						husb20_pidin_next();
					else
						husb11_pidin_next();
					//pHusb_handle->usbsta.pid_phase = PID_IN_PHASE;

				}else
				{
					pHusb_handle->usbsta.inlen = 0;
					if(pHusb_handle->usbsta.ch == USB20_CH)
						husb20_pidout(NULL,0);
					else
						husb11_pidout(NULL,0);
					pHusb_handle->usbsta.pid_phase = SETUP_END_PHASE;

				}
				return PID_PHASE_NEXT;
			}
		}
		case PID_OUT_PHASE:
		{
			if(pHusb_handle->usbsta.ch == USB20_CH)
				husb20_pidout(pHusb_handle->usbsta.dsc_buf,pHusb_handle->usbsta.inlen);
			else
				husb11_pidout(pHusb_handle->usbsta.dsc_buf,pHusb_handle->usbsta.inlen);
			if(!pHusb_handle->usbsta.pid_dir) //SET
			{
				pHusb_handle->usbsta.pid_phase = PID_KICK_PHASE;
			}else{
				pHusb_handle->usbsta.pid_phase = SETUP_END_PHASE;
			}
			return PID_PHASE_NEXT;
		}
		case SETUP_END_PHASE:
		{
			pHusb_handle->usbsta.pid_phase = PID_SETUP_PHASE;
			return ENUM_PHASE_NEXT;
		}
	}
	pHusb_handle->usbsta.pid_phase = PID_SETUP_PHASE;
	return ENUM_FAIL;
}
/*******************************************************************************
* Function Name  : husb_cfgdsc_dc
* Description    : husb_cfgdsc_dc
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_cfgdsc_dc(u8 *buf, HUSB_HANDLE *pHusb_handle)
{
	u32 desclen = pHusb_handle->cfg.t_length;
	u32 stplen;
	u8 	bInterfaceClass = 0;
	u8  bInterfaceSubClass = 0;
	pHusb_handle->usbsta.infs_num 	= 0;
	SINTFEP_DESC* pinfsep_dsc 		= &pHusb_handle->infsep_dsc[0];
	SEP_DESC* pendpoint_dsc 		= pinfsep_dsc->endpoint_desc;
	VS_FORMAT_FRAME* pvs_format_c 	= &pHusb_handle->vs_format_c[0];
	VS_FRAME		*pvs_frame    	= pvs_format_c->vs_frame_s;

	//deg_Printf("husb_cfgdsc_dc\n");

	while(desclen)
	{
		hal_wdtClear();
		stplen = buf[0];
		//debgbuf(buf,stplen);
		if(stplen == 0)
		{
			return false;
		}
		switch(buf[1])
		{
			case CONFIGURATION_DESCRIPTOR:  //0x02
			{
				//pHusb_handle->cfg.num_intf 	= buf[4];              		// Number of interface
				//pHusb_handle->cfg.cv			= buf[5];                  	// bConfigurationValue
				break;
			}
			case INTERFACE_DESCRIPTOR:  //0x04
			{

				hx330x_bytes_memcpy((u8*)&pinfsep_dsc->intfs_desc,(u8*)buf,stplen);
				pendpoint_dsc = pinfsep_dsc->endpoint_desc;
				bInterfaceClass 	= pinfsep_dsc->intfs_desc.iclass;
				bInterfaceSubClass 	= pinfsep_dsc->intfs_desc.sub;
				pinfsep_dsc++;
				pHusb_handle->usbsta.infs_num++;
				break;
			}
			case ENDPOINT_DESCRIPTOR: //0x05
			{
				hx330x_bytes_memcpy((u8*)pendpoint_dsc,(u8*)buf,stplen);
				pendpoint_dsc++;
				break;
			}
			case CS_INTERFACE:  //0x24
			{
				if((bInterfaceClass == CC_VIDEO) && (bInterfaceSubClass == SC_VIDEOCONTROL))
				{
					if(buf[2] == VC_PROCESSING_UNIT)
					{
						pHusb_handle->usbsta.uvc_pu_id 				= buf[3];
						pHusb_handle->usbsta.uvc_pu_bControlSize	= buf[7];
						pHusb_handle->usbsta.uvc_pu_Control			= buf[8] | (buf[9] << 8);
					}
				}
				else if((bInterfaceClass == CC_VIDEO) && (bInterfaceSubClass == SC_VIDEOSTREAMING))
				{
					switch(buf[2])
					{
						case VS_INPUT_HEADER:
						{
							hx330x_bytes_memcpy((u8*)&pHusb_handle->vs_head_c,(u8*)buf,sizeof(VS_HEAD));
							break;
						}

						case VS_FORMAT_UNCOMPRESSED:
						case VS_FORMAT_MJPEG:
						{
							hx330x_bytes_memcpy((u8*)&pvs_format_c->vs_format_s,(u8*)buf,sizeof(VS_FORMAT)-1);
							if(buf[2] == VS_FORMAT_UNCOMPRESSED)
							{
								pvs_format_c->vs_format_s.bDefaultFrameIndex = buf[22];
							}else
							{
								pvs_format_c->vs_format_s.bDefaultFrameIndex = buf[6];
							}
							if(pvs_format_c->vs_format_s.bNumFrameDescriptors > 16)
							{
								deg_Printf("VS bNumFrameDescriptors OUT OF RANGE:%d\n",pvs_format_c->vs_format_s.bNumFrameDescriptors);
								return false;
							}
							pvs_frame = pvs_format_c->vs_frame_s;
							pvs_format_c++;
							break;
						}
						case VS_FRAME_UNCOMPRESSED:
						case VS_FRAME_MJPEG:
						{
							hx330x_bytes_memcpy((u8*)&pvs_frame[buf[3]-1],(u8*)buf,sizeof(VS_FRAME)-1);

							if(buf[2] == VS_FRAME_UNCOMPRESSED)
							{
								pvs_frame[buf[3]-1].bFrameSupport = UVC_YUV_FORMAT_SUPPORT;
								deg_Printf("YUV:%d,%d,%d,%d\n",pvs_frame[buf[3]-1].wWidth,pvs_frame[buf[3]-1].wHeight,((UVC_YUV_MAX_FRAME>>0)&0xffff), ((UVC_YUV_MAX_FRAME>>16)&0xffff));
								if(pvs_frame[buf[3]-1].wWidth > ((UVC_YUV_MAX_FRAME>>0)&0xffff) || pvs_frame[buf[3]-1].wHeight > ((UVC_YUV_MAX_FRAME>>16)&0xffff))
								{
									pvs_frame[buf[3]-1].bFrameSupport = 0;
								}
							}else if(buf[2] == VS_FRAME_MJPEG)
							{
								pvs_frame[buf[3]-1].bFrameSupport = UVC_MJP_FORMAT_SUPPORT;
								deg_Printf("MJP:%d,%d,%d,%d\n",pvs_frame[buf[3]-1].wWidth,pvs_frame[buf[3]-1].wHeight,((UVC_MJP_MAX_FRAME>>0)&0xffff), ((UVC_MJP_MAX_FRAME>>16)&0xffff));
								if(pvs_frame[buf[3]-1].wWidth > ((UVC_MJP_MAX_FRAME>>0)&0xffff) || pvs_frame[buf[3]-1].wHeight > ((UVC_MJP_MAX_FRAME>>16)&0xffff))
								{
									pvs_frame[buf[3]-1].bFrameSupport = 0;
								}
							}
							break;
						}
						default:break;
					}
				}
				break;
			}
			default: break;
		}
		if(desclen > stplen)
		{
			desclen -= stplen;
		}else
		{
			desclen = 0;
		}
		buf		+= stplen;
	}
#if 0
	u32 i,j;
	pinfsep_dsc 	= pHusb_handle->infsep_dsc;
	for(i = 0;i<pHusb_handle->usbsta.infs_num;i++)
	{
		deg_Printf("interface:\n");
		debgbuf(&pinfsep_dsc->intfs_desc,9);
		deg_Printf("endpoint:\n");
		pendpoint_dsc = pinfsep_dsc->endpoint_desc;
		for(j = 0; j < (pinfsep_dsc->intfs_desc.end_points);j++)
		{
			debgbuf(pendpoint_dsc,7);
			pendpoint_dsc++;
		}
		pinfsep_dsc++;
	}
	deg_Printf("VS HEAD:\n");
	debgbuf((u8*)&pHusb_handle->vs_head_c,sizeof(VS_HEAD));
	pvs_format_c = pHusb_handle->vs_format_c;
	for(i = 0; i < pHusb_handle->vs_head_c.bNumFormats;i++)
	{
		deg_Printf("VS FORMAT:\n");
		debgbuf((u8*)&pHusb_handle->vs_format_c[i].vs_format_s,sizeof(VS_FORMAT));
		for(j = 0;j<pHusb_handle->vs_format_c[i].vs_format_s.bFormatsIndex;j++)
		{
			deg_Printf("VS FRAME:\n");
			debgbuf((u8*)&pHusb_handle->vs_format_c[i].vs_frame_s[j],sizeof(VS_FRAME));
		}

	}
#endif
	return true;
}
/*******************************************************************************
* Function Name  : husb_infseps_dc
* Description    : husb_infseps_dc
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_infseps_dc(HUSB_HANDLE *pHusb_handle)
{
	u32 i;
	u32 j;
	SINTFEP_DESC* pinfsep_dsc 	= pHusb_handle->infsep_dsc;
	SEP_DESC * pedpt;
	pHusb_handle->usbsta.device_type = DEVICE_TYPE_NONE;
	u32 uvc_strm_max_payload = 0;
	for(i = 0;i<pHusb_handle->usbsta.infs_num;i++)
	{
		switch(pinfsep_dsc->intfs_desc.iclass)
		{
			case CLASS_MASSSTORAGE:
			{
				if((pinfsep_dsc->intfs_desc.sub == SUBCLASS_SCSI)&&(pinfsep_dsc->intfs_desc.proto == PROTOCOL_BULKONLY))
				{
					pHusb_handle->usbsta.device_type |= DEVICE_TYPE_MSC;
					pHusb_handle->msc.ctyp 	= CLASS_MASSSTORAGE;
					pHusb_handle->msc.intfs = pinfsep_dsc->intfs_desc.num;
					pHusb_handle->msc.altset = pinfsep_dsc->intfs_desc.alt_tring;
					pHusb_handle->msc.endpts = pinfsep_dsc->intfs_desc.end_points;
					pedpt = pinfsep_dsc->endpoint_desc;
					for(j = 0; j < (pinfsep_dsc->intfs_desc.end_points);j++)
					{
						if(pedpt->ep_addr & 0x80)
						{
							pHusb_handle->msc.epin 	 	= pedpt->ep_addr;
							pHusb_handle->msc.attrin 	= pedpt->attr;
							pHusb_handle->msc.inpload 	= pedpt->pay_load;
						}else
						{
							pHusb_handle->msc.epout 	= pedpt->ep_addr;
							pHusb_handle->msc.attrout 	= pedpt->attr;
							pHusb_handle->msc.outpload 	= pedpt->pay_load;
						}
						pedpt++;
					}
				}
				break;
			}
			case CC_VIDEO:
			{
				pHusb_handle->usbsta.device_type |= DEVICE_TYPE_UVC;
				pHusb_handle->uvc.ctyp = CC_VIDEO;
				pedpt = pinfsep_dsc->endpoint_desc;
				if(pinfsep_dsc->intfs_desc.sub  == SC_VIDEOCONTROL)
				{
					pHusb_handle->uvc.ctl_intfs 	= pinfsep_dsc->intfs_desc.num;
					pHusb_handle->uvc.ctl_altset 	= pinfsep_dsc->intfs_desc.alt_tring;
					for(j = 0; j < (pinfsep_dsc->intfs_desc.end_points);j++)  //实际只有0或1个enpoint
					{
						if(pedpt->ep_addr & 0x80)
						{
							pHusb_handle->uvc.ctl_ep 		= pedpt->ep_addr;
							pHusb_handle->uvc.ctl_attr 		= pedpt->attr;
							pHusb_handle->uvc.ctl_pload 	= pedpt->pay_load;
							pHusb_handle->uvc.ctl_interval 	= pedpt->interval;
						}
						pedpt++;
					}

				}else if (pinfsep_dsc->intfs_desc.sub  == SC_VIDEOSTREAMING)
				{
					for(j = 0; j < (pinfsep_dsc->intfs_desc.end_points);j++)
					{
						if(pedpt->ep_addr & 0x80)
						{
							if(uvc_strm_max_payload < pedpt->pay_load) //只保存最大payload的intfs和endpoint
							{
								uvc_strm_max_payload = pedpt->pay_load;
								pHusb_handle->uvc.strm_intfs 	= pinfsep_dsc->intfs_desc.num;
								pHusb_handle->uvc.strm_altset 	= pinfsep_dsc->intfs_desc.alt_tring;
								pHusb_handle->uvc.strm_ep 		= pedpt->ep_addr;
								pHusb_handle->uvc.strm_attr 	= pedpt->attr & 0x03;
								pHusb_handle->uvc.strm_pload 	= pedpt->pay_load;
								pHusb_handle->uvc.strm_interval = pedpt->interval;
							}
						}
						pedpt++;
					}

				}
				break;
			}
			case CC_AUDIO:
			{
				pHusb_handle->usbsta.device_type |= DEVICE_TYPE_UAC;

				break;
			}
			default:break;
		}
		pinfsep_dsc++;
	}
}
/*******************************************************************************
* Function Name  : husb_cfgdsc_printf
* Description    : husb_cfgdsc_printf
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : None
*******************************************************************************/
static void husb_cfgdsc_printf(HUSB_HANDLE *pHusb_handle)
{
	if(pHusb_handle->usbsta.ch == USB20_CH)
	{
		deg_Printf("=========HUSB20 Connect=========\n");
	}else
	{
		deg_Printf("=========HUSB11 Connect=========\n");
	}
	deg_Printf("DEV ID:[%x, %x]\n",pHusb_handle->dev.id_vendor,pHusb_handle->dev.id_product);

	if(pHusb_handle->msc.ctyp 	== CLASS_MASSSTORAGE)
	{
		deg_Printf("MSC:   [infs:%d, altset:%d, enpts:%d]\n",pHusb_handle->msc.intfs,pHusb_handle->msc.altset,pHusb_handle->msc.endpts);
		deg_Printf("MSC:IN [ep:%x, attr:%x, pload:%x]\n",pHusb_handle->msc.epin,pHusb_handle->msc.attrin,pHusb_handle->msc.inpload);
		deg_Printf("MSC:OUT[ep:%x, attr:%x, pload:%x]\n",pHusb_handle->msc.epout,pHusb_handle->msc.attrout,pHusb_handle->msc.outpload);
	}
	if(pHusb_handle->uvc.ctyp 	== CC_VIDEO)
	{
		u32 i,j;
		deg_Printf("UVC: CTRL [infs:%d, altset:%d]\n", 	pHusb_handle->uvc.ctl_intfs,pHusb_handle->uvc.ctl_altset);
		deg_Printf("UVC: CTRL [ep:%x, attr:%x, pload:%x, interval:%x]\n",	pHusb_handle->uvc.ctl_ep,pHusb_handle->uvc.ctl_attr,pHusb_handle->uvc.ctl_pload,pHusb_handle->uvc.ctl_interval);
		deg_Printf("UVC: STRM [infs:%d, altset:%d]\n",	pHusb_handle->uvc.strm_intfs,pHusb_handle->uvc.strm_altset);
		deg_Printf("UVC: STRM [ep:%x, attr:%x, pload:%x, interval:%x]\n",	pHusb_handle->uvc.strm_ep,pHusb_handle->uvc.strm_attr,pHusb_handle->uvc.strm_pload,pHusb_handle->uvc.strm_interval);
		deg_Printf("UVC: PU   [id:%x, bControlSize:%x, Control:%x\n", pHusb_handle->usbsta.uvc_pu_id,pHusb_handle->usbsta.uvc_pu_bControlSize, pHusb_handle->usbsta.uvc_pu_Control);
		deg_Printf("=========UVC VS_HEADER=========\n");
		deg_Printf("bNumFormats:            %d\n",pHusb_handle->vs_head_c.bNumFormats );
		//for(i = 0; i < pHusb_handle->vs_head_c.bNumFormats;i++)
		SysCtrl.sensor_change_en = 0;
		for(i = 0; i < pHusb_handle->vs_head_c.bNumFormats;i++)
		{
			VS_FORMAT* pvs_format = &pHusb_handle->vs_format_c[i].vs_format_s;
			VS_FRAME* pvs_frame   = &pHusb_handle->vs_format_c[i].vs_frame_s[0];
			deg_Printf("\n=========UVC VS_FORMAT=========\n");
			deg_Printf("bDescriptorSubtype:     %x\n",pvs_format->bDescriptorSubtype);
			deg_Printf("bFormatsIndex:          %x\n",pvs_format->bFormatsIndex);
			deg_Printf("bNumFrameDescriptors:   %x\n",pvs_format->bNumFrameDescriptors);
			deg_Printf("bDefaultFrameIndex:     %x\n",pvs_format->bDefaultFrameIndex);
			for(j = 0;j<pvs_format->bNumFrameDescriptors;j++)
			{
				deg_Printf("---------UVC VS_FRAME---------\n");
				deg_Printf("bDescriptorSubtype:     %x\n",pvs_frame->bDescriptorSubtype);
				deg_Printf("bFrameIndex:            %x\n",pvs_frame->bFrameIndex);
				deg_Printf("wWidth:                 %d\n",pvs_frame->wWidth);
				deg_Printf("wHeight:                %d\n",pvs_frame->wHeight);
				deg_Printf("wDefaultFrameInterval:  %x\n",pvs_frame->wDefaultFrameInterval);
				deg_Printf("wMaxFrameInterval:      %x\n",pvs_frame->wMaxFrameInterval);
				deg_Printf("wMinFrameInterval:      %x\n",pvs_frame->wMinFrameInterval);
				deg_Printf("bFrameSupport:          %x\n",pvs_frame->bFrameSupport);
				pvs_frame++;
				if(pvs_frame->bDescriptorSubtype == VS_FRAME_MJPEG){
					//deg_Printf("00000000000000000000000000000000````judge is double sensor\n");
					if((pvs_frame->wWidth == 1280&&pvs_frame->wHeight == 712)||(pvs_frame->wWidth == 640&&pvs_frame->wHeight == 472)){
						deg_Printf("1111111111111111111111111111````judge is double sensor\n");
						SysCtrl.sensor_change_en = 1;
						
					}
				}
			}
			deg_Printf("===============================\n");
		}
	}
}
/*******************************************************************************
* Function Name  : husb_cfgdsc_analysis
* Description    : husb_cfgdsc_analysis
* Input          : u8 *buf: desc buf
* 				   HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_cfgdsc_analysis(u8 *buf,HUSB_HANDLE *pHusb_handle)
{
	if(husb_cfgdsc_dc(buf,pHusb_handle) == false)
	{
		return false;
	}
	husb_infseps_dc(pHusb_handle);
	husb_cfgdsc_printf(pHusb_handle);
	return true;
}
/*******************************************************************************
* Function Name  : usensor_resolution_default_cfg
* Description    : usensor_resolution_default_cfg
* Input          : USENSOR_PROBE_RES *res
* Output         : None
* Return         : true/false
*******************************************************************************/
static void usensor_resolution_default_cfg(USENSOR_PROBE_RES *res)
{
	res->width  = hardware_setup.usb_host_prior_resolution&0xffff;
	res->height = (hardware_setup.usb_host_prior_resolution>>16)&0xffff;

}
/*******************************************************************************
* Function Name  : usensor_resolution_getByWH
* Description    : usensor_resolution_getByWH
* Input          : HUSB_HANDLE *pHusb_handle,USENSOR_PROBE_RES *res
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool usensor_resolution_getByWH(HUSB_HANDLE *pHusb_handle,USENSOR_PROBE_RES *res)
{
	u32 i,j;
	//for(i = 0; i<pHusb_handle->vs_head_c.bNumFormats;i++)
	for(i = 0; i<pHusb_handle->vs_head_c.bNumFormats;i++)
	{
		if(pHusb_handle->vs_format_c[i].vs_format_s.bDescriptorSubtype == res->type)
		{
			//match bDescriptorSubtype
			res->format_num = pHusb_handle->vs_format_c[i].vs_format_s.bFormatsIndex;
			res->frame_max  = pHusb_handle->vs_format_c[i].vs_format_s.bNumFrameDescriptors;
			for(j = 0;j<pHusb_handle->vs_format_c[i].vs_format_s.bNumFrameDescriptors;j++)
			{
				if(pHusb_handle->vs_format_c[i].vs_frame_s[j].bFrameSupport &&
				   (res->width == pHusb_handle->vs_format_c[i].vs_frame_s[j].wWidth)
				   && (res->height == pHusb_handle->vs_format_c[i].vs_frame_s[j].wHeight))
				{
					res->frame_num  = pHusb_handle->vs_format_c[i].vs_frame_s[j].bFrameIndex;
					res->wMaxVideoFrameBufSzie = pHusb_handle->vs_format_c[i].vs_frame_s[j].wMaxVideoFrameBufSzie;
					res->wDefaultFrameInterval = pHusb_handle->vs_format_c[i].vs_frame_s[j].wDefaultFrameInterval;
					return true;
				}
			}
			//not match width and height，select default frame
			res->frame_num  = pHusb_handle->vs_format_c[i].vs_format_s.bDefaultFrameIndex;
			u8 frame_num_temp = res->frame_num;
		NEXT:
			if(pHusb_handle->vs_format_c[i].vs_frame_s[frame_num_temp-1].bFrameSupport == 0)
			{
				if(frame_num_temp == res->frame_max)
				{
					frame_num_temp = 1;
				}else
				{
					frame_num_temp = frame_num_temp + 1;
				}
				if(frame_num_temp != res->frame_num)
					goto NEXT;
				else{
					return false;
				}
			}
			res->frame_num = frame_num_temp;
			res->width		= pHusb_handle->vs_format_c[i].vs_frame_s[res->frame_num-1].wWidth;
			res->height     = pHusb_handle->vs_format_c[i].vs_frame_s[res->frame_num-1].wHeight;
			res->wMaxVideoFrameBufSzie = pHusb_handle->vs_format_c[i].vs_frame_s[res->frame_num-1].wMaxVideoFrameBufSzie;
			res->wDefaultFrameInterval = pHusb_handle->vs_format_c[i].vs_frame_s[res->frame_num-1].wDefaultFrameInterval;
			return true;
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : usensor_resolution_getByIndx
* Description    : usensor_resolution_getByIndx
* Input          : HUSB_HANDLE *pHusb_handle,USENSOR_PROBE_RES *res
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool usensor_resolution_getByIndx(HUSB_HANDLE *pHusb_handle,USENSOR_PROBE_RES *res)
{
	u32 i;
	//for(i = 0; i<pHusb_handle->vs_head_c.bNumFormats;i++)
	for(i = 0; i<pHusb_handle->vs_head_c.bNumFormats;i++)
	{
		if(pHusb_handle->vs_format_c[i].vs_format_s.bDescriptorSubtype == res->type)
		{
			//match bDescriptorSubtype
			res->format_num = pHusb_handle->vs_format_c[i].vs_format_s.bFormatsIndex;
			res->frame_max  = pHusb_handle->vs_format_c[i].vs_format_s.bNumFrameDescriptors;
			u8 frame_num_temp = res->frame_num;
		NEXT:
			if(pHusb_handle->vs_format_c[i].vs_frame_s[frame_num_temp-1].bFrameSupport == 0)
			{
				if(frame_num_temp == res->frame_max)
				{
					frame_num_temp = 1;
				}else
				{
					frame_num_temp = frame_num_temp + 1;
				}
				if(frame_num_temp != res->frame_num)
					goto NEXT;
				else{
					return false;
				}
			}
			res->frame_num = frame_num_temp;
			res->width		= pHusb_handle->vs_format_c[i].vs_frame_s[res->frame_num-1].wWidth;
			res->height     = pHusb_handle->vs_format_c[i].vs_frame_s[res->frame_num-1].wHeight;
			res->wMaxVideoFrameBufSzie = pHusb_handle->vs_format_c[i].vs_frame_s[res->frame_num-1].wMaxVideoFrameBufSzie;
			res->wDefaultFrameInterval = pHusb_handle->vs_format_c[i].vs_frame_s[res->frame_num-1].wDefaultFrameInterval;
			return true;
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : usensor_resolution_select
* Description    : usensor_resolution_select
* Input          : HUSB_HANDLE *pHusb_handle
*                  u8 format:UVC_FORMAT_MJP/UVC_FORMAT_YUV
				   u8 frame_switch: 0 - default frame，1 - 切換frame
* Output         : None
* Return         : true：选择分辨率成功，保存分辨率信息到pHusb_handle->usbsta.usensor_res
*******************************************************************************/
bool usensor_resolution_select(void *handle, u8 format,u8 frame_switch)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	USENSOR_PROBE_RES target_res;
	if(pHusb_handle == NULL)
		return false;
	memset((u8*)&target_res, 0, sizeof(USENSOR_PROBE_RES));
	target_res.type = format;
	if(pHusb_handle->usbsta.usensor_res.format_num == 0)
	{
		//usensor_res:第一次匹配
		usensor_resolution_default_cfg(&target_res);
		if(usensor_resolution_getByWH(pHusb_handle,&target_res) == false) //false: 默认的格式和分辨率不匹配时
		{
			//切换另一种格式进行匹配
			if(target_res.type == UVC_FORMAT_MJP)
			{
				target_res.type = UVC_FORMAT_YUV;
			}else
			{
				target_res.type = UVC_FORMAT_MJP;
			}
			usensor_resolution_default_cfg(&target_res);
			if(usensor_resolution_getByWH(pHusb_handle,&target_res) == false)
			{
				deg_Printf("Usensor Format Not Support YUV or MJP\n");
				return false;
			}
		}
	}else
	{

		if(pHusb_handle->usbsta.usensor_res.type == format)
		{
			//格式相同，根据frame_switch匹配
			if(frame_switch) //切换frame
			{
				if(pHusb_handle->usbsta.usensor_res.frame_max == 1) //只支持一种frame
				{
					deg_Printf("Format:%x Support Only One Frame,Not switch\n",format);
					return false;
				}
				if(pHusb_handle->usbsta.usensor_res.frame_max == pHusb_handle->usbsta.usensor_res.frame_num)
				{
					target_res.frame_num = 1;
				}else
				{
					target_res.frame_num = pHusb_handle->usbsta.usensor_res.frame_num + 1;
				}

			}else
			{
				deg_Printf("fomart [%x]type not need to switch\n",format);
				return false;
			}

			usensor_resolution_getByIndx(pHusb_handle,&target_res);
			if(pHusb_handle->usbsta.usensor_res.frame_num == target_res.frame_num) //相同的frame，不需要切换
			{
				return false;
			}
		}else
		{
			usensor_resolution_default_cfg(&target_res);
			if(usensor_resolution_getByWH(pHusb_handle,&target_res) == false)
			{
				deg_Printf("Usensor Format Not Support Switch\n");
				return false;
			}
		}
	}
#if 0//UVC_FORMAT_FIX_MJP
	if(target_res.type != UVC_FORMAT_MJP)
	{
		deg_Printf("Usensor Fix MJP But not Support\n");
		return false;
	}
#endif
	deg_Printf("[Usensor Select]:format:%d, frame:%d, type:%d, width:%d, height:%d\n",target_res.format_num,target_res.frame_num, target_res.type,target_res.width,target_res.height);
	deg_Printf("[Usensor Select]:wMaxVideoFrameBufSzie:%x, wDefaultFrameInterval:%x\n",target_res.wMaxVideoFrameBufSzie,target_res.wDefaultFrameInterval);
	memcpy((u8*)&pHusb_handle->usbsta.usensor_res, (u8*)&target_res,sizeof(USENSOR_PROBE_RES));
	return true;
}
/*******************************************************************************
* Function Name  : husb_pcommit_cfg
* Description    : husb_pcommit_cfg
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static void husb_pcommit_cfg(HUSB_HANDLE *pHusb_handle,u8 *pcommit)
{
	pcommit[2] = pHusb_handle->usbsta.usensor_res.format_num;
	pcommit[3] = pHusb_handle->usbsta.usensor_res.frame_num;
	pcommit[4] = (pHusb_handle->usbsta.usensor_res.wDefaultFrameInterval)&0xff;
	pcommit[5] = (pHusb_handle->usbsta.usensor_res.wDefaultFrameInterval>>8)&0xff;
	pcommit[6] = (pHusb_handle->usbsta.usensor_res.wDefaultFrameInterval>>16)&0xff;
	pcommit[7] = (pHusb_handle->usbsta.usensor_res.wDefaultFrameInterval>>24)&0xff;
}
/*******************************************************************************
* Function Name  : husb_1st_get_devdsc_probe
* Description    : husb_1st_get_devdsc_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_1st_get_devdsc_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	pHusb_handle->usbsta.dsc_buf = (u8 *)&(pHusb_handle->dev);
	return true;
}
/*******************************************************************************
* Function Name  : husb_set_addr_probe
* Description    : husb_set_addr_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_set_addr_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	pHusb_handle->usbsta.dsc_buf = NULL;
	return true;
}
static u8 husb_set_addr_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(pHusb_handle->usbsta.ch == USB20_CH)
	{
		XSFR_USB20_SIE_DEVADDR = pHusb_handle->setup.value;//__DEV_ADDR__;
	}else
	{
		XSFR_USB11_SIE_DEVADDR = pHusb_handle->setup.value;//__DEV_ADDR__;
	}

	return ENUM_PHASE_NEXT;
}
/*******************************************************************************
* Function Name  : husb_all_get_devdsc_probe
* Description    : husb_all_get_devdsc_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_all_get_devdsc_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	pHusb_handle->usbsta.dsc_buf = (u8 *)&(pHusb_handle->dev);
	pHusb_handle->setup.length = pHusb_handle->dev.length;
	return true;
}
static u8 husb_all_get_devdsc_ack(void *handle)
{
	//HUSB_HANDLE *pHusb_handle = handle;
	//预留 ,可能根据PID VID做一些限制什么的
	//pHusb_handle->dev.device_class
	//pHusb_handle->dev.id_vendor
	//pHusb_handle->dev.id_product
	return ENUM_PHASE_NEXT;
}
/*******************************************************************************
* Function Name  : husb_1st_get_cfg_desc_probe
* Description    : husb_1st_get_cfg_desc_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_1st_get_cfg_desc_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	pHusb_handle->usbsta.dsc_buf = (u8 *)&(pHusb_handle->cfg);
	return true;
}
/*******************************************************************************
* Function Name  : husb_all_get_cfg_desc_probe
* Description    : husb_all_get_cfg_desc_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_all_get_cfg_desc_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//预留
	pHusb_handle->usbsta.dsc_buf = (u8 *)(pHusb_handle->tempbuf);
	pHusb_handle->setup.length = pHusb_handle->cfg.t_length;
	return true;
}
/*******************************************************************************
* Function Name  : husb_cfgdsc_analysis
* Description    : husb_cfgdsc_analysis
* Input          : u8 *buf: desc buf
* 				   HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_analysis(u8 *buf,HUSB_HANDLE *pHusb_handle)
{
	if(pHusb_handle->dev.device_class == USB_DEV_HUB_CLASS)
	{
		u32 desclen = pHusb_handle->cfg.t_length;
		u32 stplen;
		u32 hub_class = 0;
		u32 hub_ep_num = 0;
		pHusb_handle->usbsta.hub_sta = 0;
		while(desclen)
		{
			hal_wdtClear();
			stplen = buf[0];
			if(stplen == 0)
			{
				return false;
			}
			switch(buf[1])
			{
				case INTERFACE_DESCRIPTOR:
				{
					hub_ep_num = buf[4];
					hub_class = buf[5];
					break;
				}
				case ENDPOINT_DESCRIPTOR:
				{
					if(hub_class == USB_DEV_HUB_CLASS && hub_ep_num)
					{
						if(buf[2] & 0x80)
						{
							pHusb_handle->usbsta.hub_ep = buf[2];
							pHusb_handle->usbsta.hub_attr = buf[3]&0x03;
							pHusb_handle->usbsta.hub_sta = 1;
						}

					}
					break;
				}
			}
			if(desclen > stplen)
			{
				desclen -= stplen;
			}else
			{
				desclen = 0;
			}
			buf		+= stplen;
		}
		deg_Printf("hub_sta:%d, hub_ep:%x,hub_attr:%x\n", pHusb_handle->usbsta.hub_sta,pHusb_handle->usbsta.hub_ep, pHusb_handle->usbsta.hub_attr);
	}


	return true;
}
u8 husb_all_get_cfg_desc_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
#if HOST_HUB_SUPPORT
	if(pHusb_handle->dev.device_class == USB_DEV_HUB_CLASS){
		husb_hub_analysis(pHusb_handle->tempbuf,pHusb_handle);
		return ENUM_PHASE_NEXT;
	}
#endif
	//cfg desc analysis
	if(husb_cfgdsc_analysis(pHusb_handle->tempbuf,pHusb_handle) == false)
	{
		return ENUM_END;  //不再重新枚举，除非插拔
	}
	if(pHusb_handle->usbsta.device_type & DEVICE_TYPE_UVC)
	{

		if(usensor_resolution_select(pHusb_handle, hardware_setup.usb_host_prior_frame,0)==false)
			return ENUM_END;  //不再重新枚举，除非插拔

	}else
	{
		return ENUM_FAIL;  //不再重新枚举，除非插拔
	}
	return ENUM_PHASE_NEXT;

}
/*******************************************************************************
* Function Name  : husb_set_cfg_probe
* Description    : husb_set_cfg_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_set_cfg_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//预留
	pHusb_handle->usbsta.dsc_buf = NULL;

	return true;
}
#if HOST_HUB_SUPPORT
/*******************************************************************************
* Function Name  : husb_hub_inf_probe
* Description    : husb_hub_inf_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_inf_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//预留
	if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	{

		pHusb_handle->usbsta.phcdtrb    += (12-1); //如果不是hub，跳过hub的枚举，直接到set_intfs_msc_0
		return false;
	}
	//deg_Printf("husb_hub_inf_probe\n");
	pHusb_handle->usbsta.dsc_buf 		= (u8 *)(pHusb_handle->tempbuf);
	pHusb_handle->usbsta.hub_ports 		= 0;
	pHusb_handle->usbsta.hub_portstas 	= 0;

	return true;
}
u8 husb_hub_inf_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//预留
	if(pHusb_handle->tempbuf[1] == 0x29)
	{
		pHusb_handle->usbsta.hub_ports 		= pHusb_handle->tempbuf[2];
		pHusb_handle->usbsta.hub_portstas 	= pHusb_handle->tempbuf[2];
		pHusb_handle->usbsta.hub_portcur	= 1;
	}

	return ENUM_PHASE_NEXT;
}

/*******************************************************************************
* Function Name  : husb_hub_status_probe
* Description    : husb_hub_status_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_status_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//预留
	//if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	//	return false;
	//deg_Printf("husb_hub_status_probe\n");
	pHusb_handle->usbsta.dsc_buf 		= (u8 *)(pHusb_handle->tempbuf);
	return true;
}
/*******************************************************************************
* Function Name  : husb_setport_feature_probe
* Description    : husb_setport_feature_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_setport_feature_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	//	return false;
	if(pHusb_handle->usbsta.hub_ports == 0)
	{
		return false;
	}
	//deg_Printf("husb_setport_feature_probe\n");
	pHusb_handle->setup.index = pHusb_handle->usbsta.hub_portcur;//pHusb_handle->usbsta.hub_ports;
	pHusb_handle->usbsta.dsc_buf = NULL;
	return true;
}
u8 husb_setport_feature_ack(void *handle)
{
	//HUSB_HANDLE *pHusb_handle = handle;
	//HUB连接到设备或上电时，向主机报告，主机通过发送Set_port_feature请求让hub复位新插入的设备。

	//设备复位操作是驱动数据线到复位SE0（D+,D-都为低电平），并持续至少10MS。

	hx330x_sysCpuMsDelay(50);
	return ENUM_PHASE_NEXT;
}
/*******************************************************************************
* Function Name  : husb_hub_port_reset_probe
* Description    : husb_hub_port_reset_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_port_reset_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	//	return false;
	if(pHusb_handle->usbsta.hub_ports == 0)
	{
		return false;
	}
	//deg_Printf("husb_hub_port_reset_probe\n");
	pHusb_handle->setup.index = pHusb_handle->usbsta.hub_portcur;//pHusb_handle->usbsta.hub_ports;
	pHusb_handle->usbsta.dsc_buf = NULL;
	return true;
}
u8 husb_hub_port_reset_ack(void *handle)
{
	//HUSB_HANDLE *pHusb_handle = handle;
	hx330x_sysCpuMsDelay(20);
	return ENUM_PHASE_NEXT;
}

/*******************************************************************************
* Function Name  : husb_hub_getport_status_probe
* Description    : husb_hub_getport_status_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_getport_status_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	//	return false;
	if(pHusb_handle->usbsta.hub_portstas == 0)
	{
		return false;
	}
	//deg_Printf("husb_hub_getport_status_probe\n");
	pHusb_handle->setup.index = pHusb_handle->usbsta.hub_portcur;//pHusb_handle->usbsta.hub_ports;
	pHusb_handle->usbsta.dsc_buf = (u8 *)(pHusb_handle->tempbuf);
	return true;
}
u8 husb_hub_getport_status_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	husb_debgbuf((u8 *)(pHusb_handle->tempbuf),4);
	//hx330x_sysCpuMsDelay(100);
	if((pHusb_handle->tempbuf[0]&BIT(1)) && (pHusb_handle->tempbuf[2]&BIT(4)))
	{
		husb_hub_uinit(handle);
		deg_Printf("hub check ok\n");
		return ENUM_PHASE_NEXT;
	}

	//pHusb_handle->usbsta.hub_ports--;
	if(pHusb_handle->usbsta.hub_portcur < pHusb_handle->usbsta.hub_ports)
	{
		//pHusb_handle->usbsta.enum_phase -= 4;//husb_hub_setport_feature->husb_hub_port_reset->husb_hub_c_port_reset->hub_hub_getport_status
		pHusb_handle->usbsta.phcdtrb    -= 4;
	}else
	{
		husb_hub_init(handle);
		return ENUM_END;
		//return ENUM_FAIL;//ENUM_END;//hub san fail 不再进行枚举，除非插拔
	}
	pHusb_handle->usbsta.hub_portcur++;
	return ENUM_PHASE_NEXT;
}
/*******************************************************************************
* Function Name  : husb_hub_clrport_feature_probe
* Description    : husb_hub_clrport_feature_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_clrport_feature_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	//	return false;
	if(pHusb_handle->usbsta.hub_ports == 0)
	{
		return false;
	}
	//deg_Printf("husb_hub_clrport_feature_probe\n");
	pHusb_handle->setup.index = pHusb_handle->usbsta.hub_portcur;//pHusb_handle->usbsta.hub_ports;
	pHusb_handle->usbsta.dsc_buf = NULL;
	return true;
}
u8 husb_hub_clrport_feature_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//hx330x_sysCpuMsDelay(100);
	if(pHusb_handle->usbsta.ch == USB20_CH)
	{
		XSFR_USB20_SIE_DEVADDR = 0;
	}else
	{
		XSFR_USB11_SIE_DEVADDR = 0;
	}
	return ENUM_PHASE_NEXT;
}

/*******************************************************************************
* Function Name  : husb_hub_set_port_devaddr_probe
* Description    : husb_hub_set_port_devaddr_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_set_port_devaddr_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	//	return false;
	pHusb_handle->usbsta.dsc_buf = NULL;
	return true;
}
u8 husb_hub_set_port_devaddr_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(pHusb_handle->usbsta.ch == USB20_CH)
	{

		XSFR_USB20_SIE_DEVADDR = pHusb_handle->setup.value;//__USB_HUB_ADDR_;
	}else
	{
		XSFR_USB11_SIE_DEVADDR = pHusb_handle->setup.value;//__USB_HUB_ADDR_;
	}
	return ENUM_PHASE_NEXT;
}
/*******************************************************************************
* Function Name  : husb_hub_all_get_devdsc_probe
* Description    : husb_hub_all_get_devdsc_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_all_get_devdsc_probe(void *handle)
{
	//HUSB_HANDLE *pHusb_handle = handle;
	//if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	//	return false;
	return husb_all_get_devdsc_probe(handle);

}
/*******************************************************************************
* Function Name  : husb_hub_1st_get_cfg_desc_probe
* Description    : husb_hub_1st_get_cfg_desc_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_1st_get_cfg_desc_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	//	return false;
	pHusb_handle->usbsta.dsc_buf = (u8 *)&(pHusb_handle->cfg);
	return true;
}
/*******************************************************************************
* Function Name  : husb_hub_all_get_cfg_desc_probe
* Description    : husb_hub_all_get_cfg_desc_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_all_get_cfg_desc_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//预留
	//if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	//	return false;
	pHusb_handle->usbsta.dsc_buf = (u8 *)(pHusb_handle->tempbuf);
	pHusb_handle->setup.length = pHusb_handle->cfg.t_length;
	return true;
}
/*******************************************************************************
* Function Name  : husb_hub_set_cfg_probe
* Description    : husb_hub_set_cfg_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_hub_set_cfg_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//预留
	//if(pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS)
	//	return false;
	pHusb_handle->usbsta.dsc_buf = NULL;

	return true;
}
#endif
/*******************************************************************************
* Function Name  : husb_set_intfs_msc_0_probe
* Description    : husb_set_intfs_msc_0_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_set_intfs_msc_0_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(pHusb_handle->usbsta.device_type & DEVICE_TYPE_MSC)
	{
		pHusb_handle->setup.index = pHusb_handle->msc.intfs;
	}else
	{
		return false;
	}

	pHusb_handle->usbsta.dsc_buf = NULL;
	return true;
}
/*******************************************************************************
* Function Name  : husb_get_max_lun_probe
* Description    : husb_get_max_lun_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_get_max_lun_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(!(pHusb_handle->usbsta.device_type & DEVICE_TYPE_MSC))
	{
		return false;
	}
	pHusb_handle->setup.index = pHusb_handle->msc.intfs;
	pHusb_handle->usbsta.dsc_buf = (u8 *)(pHusb_handle->tempbuf);
	return true;
}
u8 husb_get_max_lun_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	pHusb_handle->usbsta.max_lun = pHusb_handle->tempbuf[0];
	husb_msc_init(pHusb_handle);
	return ENUM_PHASE_NEXT;
}
/*******************************************************************************
* Function Name  : husb_set_intfs_uvc_0_probe
* Description    : husb_set_intfs_uvc_0_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_set_intfs_uvc_0_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(pHusb_handle->usbsta.device_type & DEVICE_TYPE_UVC)
	{
		if(pHusb_handle->uvc.strm_attr == EP_TYPE_BULK)
		{
			return false;
		}
		pHusb_handle->setup.index = pHusb_handle->uvc.strm_intfs;
	}else
	{
		return false;
	}

	pHusb_handle->usbsta.dsc_buf = NULL;
	return true;
}
/*******************************************************************************
* Function Name  : husb_clear_feature_probe
* Description    : husb_clear_feature_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_clear_feature_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(pHusb_handle->usbsta.device_type & DEVICE_TYPE_UVC)
	{
		if(pHusb_handle->uvc.strm_attr == EP_TYPE_BULK)
		{
			pHusb_handle->setup.index = pHusb_handle->uvc.strm_ep;
			return true;
		}
	}
	return false;
}
/*******************************************************************************
* Function Name  : husb_get_pcommit_cs100_probe
* Description    : husb_get_pcommit_cs100_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_get_pcommit_cs100_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(!(pHusb_handle->usbsta.device_type & DEVICE_TYPE_UVC))
	{
		return false;
	}
	pHusb_handle->setup.index = pHusb_handle->uvc.strm_intfs;
	pHusb_handle->usbsta.dsc_buf = (u8 *)(pHusb_handle->tempbuf);
	return true;
}
u8 husb_get_pcommit_cs100_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//select FRAME
	husb_pcommit_cfg(pHusb_handle,(u8 *)pHusb_handle->tempbuf);
	return ENUM_PHASE_NEXT;
}
/*******************************************************************************
* Function Name  : husb_set_pcommit_cs100_probe
* Description    : husb_set_pcommit_cs100_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_set_pcommit_cs100_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(!(pHusb_handle->usbsta.device_type & DEVICE_TYPE_UVC))
	{
		return false;
	}
	pHusb_handle->setup.index = pHusb_handle->uvc.strm_intfs;
	pHusb_handle->usbsta.dsc_buf = (u8 *)(pHusb_handle->tempbuf);
	return true;
}
/*******************************************************************************
* Function Name  : husb_set_pcommit_cs200_probe
* Description    : husb_set_pcommit_cs200_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_set_pcommit_cs200_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(!(pHusb_handle->usbsta.device_type & DEVICE_TYPE_UVC))
	{
		return false;
	}
	pHusb_handle->setup.index = pHusb_handle->uvc.strm_intfs;
	pHusb_handle->usbsta.dsc_buf = (u8 *)(pHusb_handle->tempbuf);
	return true;
}
u8 husb_set_pcommit_cs200_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(pHusb_handle->uvc.strm_attr == EP_TYPE_BULK)
	{
		husb_uvc_init(pHusb_handle);
		deg_Printf("usensor atech\n");

	}
	return ENUM_PHASE_NEXT;
}
/*******************************************************************************
* Function Name  : husb_set_intfs_uvc_1_probe
* Description    : husb_set_intfs_uvc_1_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_set_intfs_uvc_1_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(!(pHusb_handle->usbsta.device_type & DEVICE_TYPE_UVC))
	{
		return false;
	}
	if(pHusb_handle->uvc.strm_attr == EP_TYPE_BULK)
	{
		return false;
	}
	pHusb_handle->setup.value = pHusb_handle->uvc.strm_altset;
	pHusb_handle->setup.index = pHusb_handle->uvc.strm_intfs;
	pHusb_handle->usbsta.dsc_buf = NULL;
	//husb_uvc_init(pHusb_handle);
	return true;
}
u8 husb_set_intfs_uvc_1_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	//husb_uvc_init
	//if(pHusb_handle->dev.id_vendor != 0x349c)
	//{
	//	return ENUM_END;
	//}
	husb_uvc_init(pHusb_handle);
	//uvc_actech_as_usensor
	//usensor_actech
	//deg_Printf("usensor atech\n");
	return ENUM_PHASE_NEXT;
}
/*******************************************************************************
* Function Name  : husb_astern_probe
* Description    : husb_astern_probe
* Input          : HUSB_HANDLE *pHusb_handle
* Output         : None
* Return         : true/false
*******************************************************************************/
static bool husb_astern_probe(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(!(pHusb_handle->usbsta.device_type & DEVICE_TYPE_UVC))
	{
		return false;
	}
	if((pHusb_handle->usbsta.device_sta & (USB_UVC_TRAN|USB_ASTERN_CK_LOCK)) != USB_UVC_TRAN)
	{
		return false;
	}
	pHusb_handle->usbsta.device_sta |= USB_ASTERN_CK_LOCK;
	if(pHusb_handle->dev.id_vendor == 0x1902)
	{
		pHusb_handle->setup.index 	= 0x0300;
		pHusb_handle->setup.length 	= 4;
	}
	else
	{
		pHusb_handle->setup.index 	= 0x0200;
		pHusb_handle->setup.length 	= 2;
	}
	pHusb_handle->usbsta.dsc_buf = pHusb_handle->backstabuf;
	return true;
}
bool husb_astern_check(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	if(pHusb_handle->dev.id_vendor == 0x1902)
	{
		if(pHusb_handle->backstabuf[2]	== 0xfc)
			return true;
		else
			return false;
	}else
	{
		if( (pHusb_handle->backstabuf[0] == 0x00) && (pHusb_handle->backstabuf[1] == 0x01) )
			return true;
		else if( (pHusb_handle->backstabuf[0] == 0x79) && (pHusb_handle->backstabuf[1] == 0x00) )
			return false;
		else
			return false;
	}
}
u8 husb_astern_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;

	if(husb_astern_check(pHusb_handle))
	{
		pHusb_handle->usbsta.device_sta |= USB_ASTERN;
	}else
	{
		pHusb_handle->usbsta.device_sta &= ~USB_ASTERN;
	}

	pHusb_handle->usbsta.device_sta &= ~USB_ASTERN_CK_LOCK;
	return ENUM_PHASE_NEXT;
}
ALIGNED(4) const HUSB_HCDTRB husb_enum_hcdtrb[] =
{
	//SETUP_PKG   setup;											probe	pack
	//1st_get_devdsc
	{{0x80,	SC_GET_DESCRIPTOR,	DEVICE_DESCRIPTOR<<8, 			0x0000, 0x0008}, 	husb_1st_get_devdsc_probe,		NULL},
	//set_devaddr
	{{0x00, SC_SET_ADDRESS,		__DEV_ADDR__, 					0x0000, 0x0000}, 	husb_set_addr_probe,			husb_set_addr_ack},
	//all_get_devdsc
	{{0x80, SC_GET_DESCRIPTOR,	DEVICE_DESCRIPTOR<<8, 	   		0x0000, 0x0008}, 	husb_all_get_devdsc_probe,		husb_all_get_devdsc_ack},
	//1st_get_cfg_desc
	{{0x80, SC_GET_DESCRIPTOR,	CONFIGURATION_DESCRIPTOR<<8,	0x0000, 0x0009}, 	husb_1st_get_cfg_desc_probe,	NULL},
	//all_get_cfg_desc
	{{0x80, SC_GET_DESCRIPTOR,	CONFIGURATION_DESCRIPTOR<<8,	0x0000, 0x0009}, 	husb_all_get_cfg_desc_probe,	husb_all_get_cfg_desc_ack},
	//set_cfg
	{{0x00, SC_SET_CONFIGURATION,0x0001, 						0x0000, 0x0000}, 	husb_set_cfg_probe,				NULL},
#if HOST_HUB_SUPPORT
	//hub_inf
	{{0xa0, SC_GET_DESCRIPTOR,	0x0000, 						0x0000, 0x0008}, 	husb_hub_inf_probe,				husb_hub_inf_ack},
	//hub_status
	{{0xa0, SC_GET_STATUS,		0, 								0x0000, 0x0004}, 	husb_hub_status_probe,			NULL},
	//hub_setport_feature
	{{0x23, SC_SET_FEATURE,		8, 								0x0000, 0x0000}, 	husb_setport_feature_probe,		husb_setport_feature_ack},
	//hub_hub_port_reset
	{{0x23, SC_CLEAR_FEATURE,	0x0010, 						0x0000, 0x0000}, 	husb_hub_port_reset_probe,		husb_hub_port_reset_ack},
	//hub_hub_c_port_reset
	{{0x23, SC_SET_FEATURE,		0x0004, 						0x0000, 0x0000}, 	husb_hub_port_reset_probe,		husb_hub_port_reset_ack},
	//hub_hub_getport_status
	{{0xa3, SC_GET_STATUS,		0x00, 							0x0000, 0x0004}, 	husb_hub_getport_status_probe,	husb_hub_getport_status_ack},
	//hub_clrport_feature
	{{0x23, SC_CLEAR_FEATURE,	0x0014, 						0x0000, 0x0000}, 	husb_hub_clrport_feature_probe,	husb_hub_clrport_feature_ack},
	//hub_set_port_devaddr
	{{0x00, SC_SET_ADDRESS,		__USB_HUB_ADDR_, 				0x0000, 0x0000}, 	husb_hub_set_port_devaddr_probe,husb_hub_set_port_devaddr_ack},
	//all_get_devdsc
	{{0x80, SC_GET_DESCRIPTOR, 	DEVICE_DESCRIPTOR<<8, 	   		0x0000, 0x0008}, 	husb_hub_all_get_devdsc_probe,	husb_all_get_devdsc_ack},
	//1st_get_cfg_desc
	{{0x80, SC_GET_DESCRIPTOR, 	CONFIGURATION_DESCRIPTOR<<8,	0x0000, 0x0009}, 	husb_hub_1st_get_cfg_desc_probe,NULL},
	//all_get_cfg_desc
	{{0x80, SC_GET_DESCRIPTOR, 	CONFIGURATION_DESCRIPTOR<<8,	0x0000, 0x0009}, 	husb_hub_all_get_cfg_desc_probe,husb_all_get_cfg_desc_ack},
	//set_cfg
	{{0x00, SC_SET_CONFIGURATION,0x0001, 						0x0000, 0x0000}, 	husb_hub_set_cfg_probe,			NULL},
#endif
	//set_intfs_msc_0 //for msc or uvc
	{{0x01, SC_SET_INTERFACE,	0x0000, 						0x0000, 0x0000}, 	husb_set_intfs_msc_0_probe,		NULL},
	//get_max_lun //for msc
	{{0xa1, 0xfe,				0x0000, 						0x0000, 0x0001}, 	husb_get_max_lun_probe,			husb_get_max_lun_ack},
	//set_intfs_uvc_0 //for msc
	{{0x01, SC_SET_INTERFACE,	0x0000, 						0x0000, 0x0000}, 	husb_set_intfs_uvc_0_probe,		NULL},
	//get_pcommit_cs100 //for uvc
	{{0xa1, GET_CUR,			VS_PROBE_CONTROL<<8, 			0x0001, 0x001a}, 	husb_get_pcommit_cs100_probe,	husb_get_pcommit_cs100_ack},
	//set_pcommit_cs100 //for uvc
	{{0x21, SET_CUR,			VS_PROBE_CONTROL<<8,			0x0001, 0x001a},	husb_set_pcommit_cs100_probe,	NULL},
	//get_pcommit_cs100 //for uvc
	{{0xa1, GET_CUR,			VS_PROBE_CONTROL<<8,			0x0001, 0x001a}, 	husb_get_pcommit_cs100_probe,	husb_get_pcommit_cs100_ack},
	//set_pcommit_cs200 //for uvc
	{{0x21, SET_CUR,			VS_COMMIT_CONTROL<<8, 			0x0001, 0x001a}, 	husb_set_pcommit_cs200_probe,	husb_set_pcommit_cs200_ack},
	//set_intfs_0 //for msc or uvc
	{{0x01, SC_SET_INTERFACE,	0x0001, 						0x0000, 0x0000}, 	husb_set_intfs_uvc_1_probe,		husb_set_intfs_uvc_1_ack},
	//0XFF:TAB END FLAG
	{{0xff, 0x00,				0x0000, 						0x0000, 0x0000}, 	NULL,							NULL},
};

ALIGNED(4) const HUSB_HCDTRB husb_uvc_switch_hcdtrb[] =
{
	//set_intfs_uvc_0 //for msc
	{{0x01, SC_SET_INTERFACE,	0x0000, 						0x0000, 0x0000}, 	husb_set_intfs_uvc_0_probe,		NULL},
	//clear_endpoint_feature, for uvc bulk transfer
	{{0x02, SC_CLEAR_FEATURE,	0x0000, 						0x0000, 0x0000}, 	husb_clear_feature_probe,		NULL},
	//get_pcommit_cs100 //for uvc
	{{0xa1, GET_CUR,			VS_PROBE_CONTROL<<8, 			0x0001, 0x001a}, 	husb_get_pcommit_cs100_probe,	husb_get_pcommit_cs100_ack},
	//set_pcommit_cs100 //for uvc
	{{0x21, SET_CUR,			VS_PROBE_CONTROL<<8,			0x0001, 0x001a}, 	husb_set_pcommit_cs100_probe,	NULL},
	//get_pcommit_cs100 //for uvc
	{{0xa1, GET_CUR,			VS_PROBE_CONTROL<<8,			0x0001, 0x001a}, 	husb_get_pcommit_cs100_probe,	husb_get_pcommit_cs100_ack},
	//set_pcommit_cs200 //for uvc
	{{0x21, SET_CUR,			VS_COMMIT_CONTROL<<8, 			0x0001, 0x001a}, 	husb_set_pcommit_cs200_probe,	husb_set_pcommit_cs200_ack},
	//set_intfs_0 //for msc or uvc
	{{0x01, SC_SET_INTERFACE,	0x0001, 						0x0000, 0x0000}, 	husb_set_intfs_uvc_1_probe,		husb_set_intfs_uvc_1_ack},
	//0XFF:TAB END FLAG
	{{0xff, 0x00,				0x0000, 						0x0000, 0x0000}, 	NULL,							NULL},
};
ALIGNED(4) const HUSB_HCDTRB husb_astern_hcdtrb[] =
{
	//husb_backsta_check
	{{0xa1, GET_CUR,			0x0100, 						0x0000, 0x0000}, 	husb_astern_probe,				husb_astern_ack},
	//0XFF:TAB END FLAG
	{{0xff, 0x00,				0x0000, 						0x0000, 0x0000}, 	NULL,							NULL},
};

u8 husb_uvcunit_ack(void *handle)
{
	HUSB_HANDLE *pHusb_handle = handle;
	pHusb_handle->usbsta.device_sta |= USB_UVCUNIT_DONE;
	return ENUM_PHASE_NEXT;
}
ALIGNED(4) const HUSB_HCDTRB husb_uvcunit_get_hcdtrb[] =
{
	//husb_backsta_check
	{{0xa1, GET_CUR,			0x0000, 						0x0000, 0x0002}, 	NULL,				husb_uvcunit_ack},
	//0XFF:TAB END FLAG
	{{0xff, 0x00,				0x0000, 						0x0000, 0x0000}, 	NULL,				NULL},
};
ALIGNED(4) const HUSB_HCDTRB husb_uvcunit_set_hcdtrb[] =
{
	//husb_backsta_check
	{{0x21, SET_CUR,			0x0000, 						0x0000, 0x0002}, 	NULL,				husb_uvcunit_ack},
	//0XFF:TAB END FLAG
	{{0xff, 0x00,				0x0000, 						0x0000, 0x0000}, 	NULL,				NULL},
};
/*******************************************************************************
* Function Name  : husb_api_ep0_kick
* Description    : husb_api_ep0_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_ep0_kick(void *handle)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	//pHusb_handle->usbsta.enum_phase = 0;
	pHusb_handle->usbsta.pid_phase  = PID_SETUP_PHASE;
	//deg_Printf("hal_husb_ep0_kick:\n");
	while(1)
	{
		hal_wdtClear();
		//pHusb_handle->usbsta.phcdtrb    = &(pHusb_handle->usbsta.phcdtrb[pHusb_handle->usbsta.enum_phase]);
		memcpy((u8*)&(pHusb_handle->setup),(u8*)&(pHusb_handle->usbsta.phcdtrb->setup),8);
		if(pHusb_handle->setup.mrequest == 0xff)
		{
			return;
		}
		if(pHusb_handle->usbsta.phcdtrb->probe != NULL)
		{
			if((*(pHusb_handle->usbsta.phcdtrb->probe))((void*)pHusb_handle) == false)
			{
				//pHusb_handle->usbsta.enum_phase++;
				pHusb_handle->usbsta.phcdtrb++;
				continue;
			}
		}
		break;
	}
	HAL_HE_CRITICAL_INIT();
	HAL_HE_CRITICAL_ENTER();
	husb_pid_func(pHusb_handle);
	HAL_HE_CRITICAL_EXIT();
}
/*******************************************************************************
* Function Name  : husb_api_ep0_process
* Description    : husb_api_ep0_process
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_ep0_process(void *handle)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	u8 ret;

REPROCESS:
	//deg_Printf("husb_api_ep0_process:%x\n",pHusb_handle->usbsta.pid_phase);
	hal_wdtClear();
	if(pHusb_handle->usbsta.pid_phase  == PID_SETUP_PHASE)
	{
		//pHusb_handle->usbsta.phcdtrb    = &(pHusb_handle->usbsta.phcdtrb[pHusb_handle->usbsta.enum_phase]);
		memcpy((u8*)&(pHusb_handle->setup),(u8*)&(pHusb_handle->usbsta.phcdtrb->setup),8);
		if(pHusb_handle->setup.mrequest == 0xff)
		{
			return true;
		}
		if(pHusb_handle->usbsta.phcdtrb->probe != NULL)
		{
			if((*(pHusb_handle->usbsta.phcdtrb->probe))((void*)pHusb_handle) == false)
			{
				//pHusb_handle->usbsta.enum_phase++;
				pHusb_handle->usbsta.phcdtrb++;
				goto REPROCESS;
			}
		}
	}
	ret = husb_pid_func(pHusb_handle);
	if(ret == ENUM_PHASE_NEXT)
	{
		if(pHusb_handle->usbsta.phcdtrb->pack != NULL)
		{
			ret = (*(pHusb_handle->usbsta.phcdtrb->pack))((void*)pHusb_handle);
		}
	}

	if(ret == ENUM_PHASE_NEXT)
	{
		pHusb_handle->usbsta.pid_phase  = PID_SETUP_PHASE;
		//pHusb_handle->usbsta.enum_phase++;
		pHusb_handle->usbsta.phcdtrb++;
		goto REPROCESS;
	}else if(ret == ENUM_FAIL)
	{
		if((pHusb_handle->usbsta.device_sta & USB_UVCUNIT_KICK))
		{
			pHusb_handle->usbsta.dsc_buf[0] = pHusb_handle->usbsta.dsc_buf[1] = 0x00;
			pHusb_handle->usbsta.device_sta |= USB_UVCUNIT_DONE;
			return true;
		}else
		{
			return false;
		}

	}
	return true;
}
/*******************************************************************************
* Function Name  : husb_api_ep0_uvc_switch_kick
* Description    : husb_api_ep0_uvc_switch_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_ep0_uvc_switch_kick(void* handle)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)handle;
	if(pHusb_handle)
	{
		//if((pHusb_handle->usbsta.device_sta & (USB_UVC_TRAN|USB_ASTERN_CK_LOCK)) == USB_UVC_TRAN)
		{
			pHusb_handle->usbsta.phcdtrb    = (HUSB_HCDTRB *)husb_uvc_switch_hcdtrb;
			husb_api_ep0_kick(pHusb_handle);
		}
	}

}
/*******************************************************************************
* Function Name  : husb_api_ep0_asterncheck_kick
* Description    : husb_api_ep0_asterncheck_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_api_ep0_asterncheck_kick(void* handle)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)handle;
	if(pHusb_handle)
	{
		if((pHusb_handle->usbsta.device_sta & (USB_UVC_TRAN|USB_ASTERN_CK_LOCK)) == USB_UVC_TRAN)
		{
			pHusb_handle->usbsta.phcdtrb    = (HUSB_HCDTRB *)husb_astern_hcdtrb;
			husb_api_ep0_kick(pHusb_handle);
		}
	}

}
/*******************************************************************************
* Function Name  : husb_api_uvcunit_get_kick
* Description    : husb_api_uvcunit_get_kick
* Input          : void* handle
				   u8 uvcunitsel: PU_BACKLIGHT_COMPENSATION_CONTROL ~ PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL
				   u8 request:GET_CUR/GET_MIN/GET_MAX/GET_RES
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_uvcunit_get_kick(void* handle, u8 uvcunitsel, u8 request)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)handle;
	if(pHusb_handle)
	{
	//pHusb_handle->usbsta.uvc_pu_id 				= buf[3];
	//pHusb_handle->usbsta.uvc_pu_bControlSize		= buf[7];
	//pHusb_handle->usbsta.uvc_pu_Control			= buf[8] | (buf[9] << 8);
		if(!(pHusb_handle->usbsta.device_type & DEVICE_TYPE_UVC))
		{
			return false;
		}
		if(pHusb_handle->usbsta.uvc_pu_bControlSize == 0 || uvcunitsel > 16)
		{
			return false;
		}
		if(uvcunitsel > 8 && pHusb_handle->usbsta.uvc_pu_bControlSize < 2)
		{
			return false;
		}
		if(!(pHusb_handle->usbsta.uvc_pu_Control & BIT(uvcunitsel-1)))
		{
			return false;
		}
		if((pHusb_handle->usbsta.device_sta & (USB_UVC_TRAN|USB_UVCUNIT_KICK)) == USB_UVC_TRAN)
		{
			pHusb_handle->usbsta.device_sta |= USB_UVCUNIT_KICK;
			pHusb_handle->usbsta.phcdtrb    = (HUSB_HCDTRB *)husb_uvcunit_get_hcdtrb;
			pHusb_handle->usbsta.pid_phase  = PID_SETUP_PHASE;
			hx330x_bytes_memcpy((u8*)&(pHusb_handle->setup),(u8*)&(pHusb_handle->usbsta.phcdtrb->setup),8);
			pHusb_handle->setup.request = request;
			pHusb_handle->setup.value	= ((u16)uvcunitsel << 8);
			pHusb_handle->setup.index   = ((u16)pHusb_handle->usbsta.uvc_pu_id << 8)| pHusb_handle->uvc.ctl_intfs;
			pHusb_handle->usbsta.dsc_buf = pHusb_handle->tempbuf;

			HAL_HE_CRITICAL_INIT();
			HAL_HE_CRITICAL_ENTER();
			husb_pid_func(pHusb_handle);
			//deg_Printf("husb_api_uvcunit_get_kick:%x\n", pHusb_handle->usbsta.pid_phase);
			HAL_HE_CRITICAL_EXIT();


			return true;
		}
	}
	return false;

}
/*******************************************************************************
* Function Name  : husb_api_uvcunit_get_done
* Description    : husb_api_uvcunit_get_done
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
u16 husb_api_uvcunit_get_done(void* handle)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)handle;
	u16 val = 0;
	hx330x_timerTickStart();
	if(pHusb_handle)
	{
		while(1)
		{
			hal_wdtClear();
			if(hx330x_timerTickCount() >= hardware_setup.sys_clk)
			{
				deg_Printf("husb_api_uvcunit_get_done timeout\n");
				break;
			}
			if((pHusb_handle->usbsta.device_sta & USB_UVCUNIT_DONE))
			{
				val = pHusb_handle->tempbuf[0] | ((u16)pHusb_handle->tempbuf[1] << 8);
				pHusb_handle->usbsta.device_sta &=~(USB_UVCUNIT_DONE|USB_UVCUNIT_KICK);
				break;
			}
		}
	}
	hx330x_timerTickStop();
	return val;
}
/*******************************************************************************
* Function Name  : husb_api_uvcunit_get_kick
* Description    : husb_api_uvcunit_get_kick
* Input          : void* handle
				   u8 uvcunitsel: PU_BACKLIGHT_COMPENSATION_CONTROL ~ PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL
				   u16 val
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_uvcunit_set_kick(void* handle, u8 uvcunitsel, u16 val)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)handle;
	if(pHusb_handle)
	{
	//pHusb_handle->usbsta.uvc_pu_id 				= buf[3];
	//pHusb_handle->usbsta.uvc_pu_bControlSize		= buf[7];
	//pHusb_handle->usbsta.uvc_pu_Control			= buf[8] | (buf[9] << 8);
		if(!(pHusb_handle->usbsta.device_type & DEVICE_TYPE_UVC))
		{
			return false;
		}
		if(pHusb_handle->usbsta.uvc_pu_bControlSize == 0 || uvcunitsel > 16)
		{
			return false;
		}
		if(uvcunitsel > 8 && pHusb_handle->usbsta.uvc_pu_bControlSize < 2)
		{
			return false;
		}
		if(!(pHusb_handle->usbsta.uvc_pu_Control & BIT(uvcunitsel-1)))
		{
			return false;
		}
		if((pHusb_handle->usbsta.device_sta & (USB_UVC_TRAN|USB_UVCUNIT_KICK)) == USB_UVC_TRAN)
		{
			pHusb_handle->usbsta.device_sta |= USB_UVCUNIT_KICK;
			pHusb_handle->usbsta.phcdtrb    = (HUSB_HCDTRB *)husb_uvcunit_set_hcdtrb;
			pHusb_handle->usbsta.pid_phase  = PID_SETUP_PHASE;
			hx330x_bytes_memcpy((u8*)&(pHusb_handle->setup),(u8*)&(pHusb_handle->usbsta.phcdtrb->setup),8);
			pHusb_handle->setup.value	= ((u16)uvcunitsel << 8);
			pHusb_handle->setup.index   = ((u16)pHusb_handle->usbsta.uvc_pu_id << 8)| pHusb_handle->uvc.ctl_intfs;
			pHusb_handle->tempbuf [0] = val & 0xff;
			pHusb_handle->tempbuf [1] = val >> 8;
			pHusb_handle->usbsta.dsc_buf = pHusb_handle->tempbuf;

			HAL_HE_CRITICAL_INIT();
			HAL_HE_CRITICAL_ENTER();
			husb_pid_func(pHusb_handle);
			//deg_Printf("husb_api_uvcunit_get_kick:%x\n", pHusb_handle->usbsta.pid_phase);
			HAL_HE_CRITICAL_EXIT();


			return true;
		}
	}
	return false;

}
/*******************************************************************************
* Function Name  : husb_api_uvcunit_set_done
* Description    : husb_api_uvcunit_set_done
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_uvcunit_set_done(void* handle)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)handle;
	bool ret = false;
	hx330x_timerTickStart();
	if(pHusb_handle)
	{
		while(1)
		{
			hal_wdtClear();
			if(hx330x_timerTickCount() >= hardware_setup.sys_clk)
			{
				deg_Printf("husb_api_uvcunit_set_done timeout\n");
				break;
			}
			if((pHusb_handle->usbsta.device_sta & USB_UVCUNIT_DONE))
			{
				pHusb_handle->usbsta.device_sta &=~(USB_UVCUNIT_DONE|USB_UVCUNIT_KICK);
				ret = true;
				break;
			}
		}
	}
	hx330x_timerTickStop();
	return ret;
}

/*******************************************************************************
* Function Name  : husb_api_ep0_asterncheck_kick
* Description    : husb_api_ep0_asterncheck_kick
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool husb_api_hub_check_kick(void* handle)
{
	HUSB_HANDLE* pHusb_handle = (HUSB_HANDLE*)handle;
	if(pHusb_handle)
	{
		if((pHusb_handle->usbsta.device_sta & (USB_HUB_ATECH|USB_HUB_CHECK)) == (USB_HUB_ATECH|USB_HUB_CHECK))
		{
			pHusb_handle->usbsta.device_sta &=~ (USB_HUB_ATECH|USB_HUB_CHECK);
			pHusb_handle->usbsta.hub_portcur	= 1;
			pHusb_handle->usbsta.phcdtrb    = (HUSB_HCDTRB *)&husb_enum_hcdtrb[8];
			husb_api_ep0_kick(pHusb_handle);
		}
	}
}
