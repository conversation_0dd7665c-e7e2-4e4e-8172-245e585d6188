/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../../../../hal/inc/hal.h"

#if  HOST_HUB_SUPPORT
ALIGNED(4) HUSB_HANDLE* uhub_handle;
/*******************************************************************************
* Function Name  : husb20_isopayload_recieve
* Description    : husb20_isopayload_recieve
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
SDRAM_TEXT_SECTION
static u8* husb20_hub_recieve(u32 *rlen)
{
	u8* buf  = NULL;
	u32 len;
	u32 index_temp = XSFR_USB20_SIE_EPS;
	XSFR_USB20_SIE_EPS	= HUSB20_RXEP_HUB;
	while(1)
	{
		u32 rxcsr1 = XSFR_USB20_SIE_EPRX_CTRL0;
		//deg_Printf("rxcsr1:%x\n",rxcsr1);
		if(rxcsr1 & HUSB_EPRX_ERROR)
		{
			XSFR_USB20_SIE_EPRX_CTRL0 = HUSB_EPRX_ERROR;
			deg_Printf("HUSB20 HUB RX err\n");
			break;
		}
		if(rxcsr1 & HUSB_EPRX_SENTSTALL)
		{
			XSFR_USB20_SIE_EPRX_CTRL0 = HUSB_EPRX_SENTSTALL;
			deg_Printf("HUSB20 HUB RX stall\n");
			break;
		}
		if(rxcsr1 & HUSB_EPRX_RXPKTRDY)
		{
			len = (XSFR_USB20_SIE_EPXRXCNTH<<8)+XSFR_USB20_SIE_EPXRXCNTL;
			deg_Printf("len:%x\n", len);
			if(len)
			{
				buf = (u8 *)((&XSFR_USB20_EP1_RXADDR)[HUSB20_RXEP_HUB*2-2]);
			}
			break;
		}else
		{
			deg_Printf("HUSB20 HUB rxcsr1:%x\n",rxcsr1);
			break;
		}
	}
	if(rlen)
		*rlen = len;	
	if(buf)
	{
		XSFR_USB20_SIE_EPRX_CTRL0 = HUSB_EPRX_FLUSHFIFO;
	}else
	{
		XSFR_USB20_SIE_EPRX_CTRL0 = HUSB_EPRX_FLUSHFIFO|HUSB_EPRX_REQPKT;
	}
	
	XSFR_USB20_SIE_EPS   = index_temp;
	return buf;
}

/*******************************************************************************
* Function Name  : husb20_isopayload_recieve
* Description    : husb20_isopayload_recieve
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
SDRAM_TEXT_SECTION
static u8 *husb11_hub_recieve(u32 *rlen)
{
	u8* buf  = NULL;
	u32 len = 0;
	u32 index_temp = XSFR_USB11_SIE_EPS;
	XSFR_USB11_SIE_EPS	= HUSB11_RXEP_HUB;
	while(1)
	{
		u32 rxcsr1 = XSFR_USB11_SIE_EPRX_CTRL0;
		if(rxcsr1 & HUSB_EPRX_ERROR)
		{
			XSFR_USB11_SIE_EPRX_CTRL0 = HUSB_EPRX_ERROR;
			deg_Printf("HUSB11 HUB RX err\n");
			break;
		}
		if(rxcsr1 & HUSB_EPRX_SENTSTALL)
		{
			XSFR_USB11_SIE_EPRX_CTRL0 = HUSB_EPRX_SENTSTALL;
			deg_Printf("HUSB11 HUB RX stall\n");
			break;
		}
		if(rxcsr1 & HUSB_EPRX_RXPKTRDY)
		{
			len = (XSFR_USB11_SIE_EPXRXCNTH<<8)+XSFR_USB11_SIE_EP0RXCNTL;
			if(len)
			{
				buf = (u8 *)((&XSFR_USB11_EP1_RXADDR)[HUSB11_RXEP_HUB*2-2]);
			}
			break;
		}else
		{
			deg_Printf("HUSB11 HUB rxcsr1:%x\n",rxcsr1);
			break;
		}
	}
	if(rlen)
		*rlen = len;
	if(buf)
	{
		XSFR_USB11_SIE_EPRX_CTRL0 = HUSB_EPRX_FLUSHFIFO;
	}else
	{
		XSFR_USB11_SIE_EPRX_CTRL0 = HUSB_EPRX_FLUSHFIFO|HUSB_EPRX_REQPKT;
	}
	XSFR_USB11_SIE_EPS  = index_temp;
	return buf;
}
/*******************************************************************************
* Function Name  : husb_uvc20_eprx_isr
* Description    : husb_uvc20_eprx_isr
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
SDRAM_TEXT_SECTION
static void husb_hub_eprx_isr(void)
{
	u8* buf = NULL;
	u32 len = 0;
	
	if(uhub_handle)
	{
		if(uhub_handle->usbsta.ch == USB20_CH)
		{
			buf = husb20_hub_recieve(&len);
		}else
		{
			buf = husb11_hub_recieve(&len);
		}
		if(buf && len)
		{
			//debgbuf(buf, len);
			uhub_handle->usbsta.device_sta 	|= USB_HUB_ATECH;
			husb_api_hub_check_kick((void*)uhub_handle);
		}else
		{
			if(uhub_handle->usbsta.ch == USB20_CH)
				husb_api_u20_remove();
			else
				husb_api_u11_remove();
		}
	}
}
/*******************************************************************************
* Function Name  : husb_hub_init
* Description    : husb_hub_init
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_hub_init(void *handle)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	if(pHusb_handle == NULL || pHusb_handle->dev.device_class != USB_DEV_HUB_CLASS || pHusb_handle->usbsta.hub_sta == 0)
		return ;
	uhub_handle = pHusb_handle;
	pHusb_handle->usbsta.device_sta 	|= USB_HUB_CHECK;
	
	if(pHusb_handle->usbsta.ch == USB20_CH)
	{
		deg_Printf("HUSB20 HUB INIT.\n");
		u32 index_temp	= XSFR_USB20_SIE_EPS;

		XSFR_USB20_SIE_EPS			= HUSB20_RXEP_HUB;
		XSFR_USB20_SIE_EPRX_CTRL0	= HUSB_EPRX_CLRDATATOG|HUSB_EPRX_FLUSHFIFO;
		XSFR_USB20_SIE_EPRX_CTRL1	= 0;
		XSFR_USB20_SIE_RXPKGMAXL	= (u8)(1024 & 0xff);
		XSFR_USB20_SIE_RXPKGMAXH	= (u8)(1024 >> 8) | BIT(4)| BIT(3);
		//XSFR_USB20_SIE_RXTYPE 	= (TT_ISOCHRONOUS|(pHusb_handle->uvc.strm_ep&0x7f));//SET BULK AND ENDPIONT

		//(&XSFR_USB20_EP1_TXADDR)[HUSB20_TXEP_UVC*2-2]  = (u32)_USB20_MSC_TXFIFO_;
		(&XSFR_USB20_EP1_RXADDR)[HUSB20_RXEP_HUB*2-2]  = (u32)_USB20_MSC_RXFIFO_; //pHusb_handle->usbsta.uvc_fifo_tgl = 0;
		XSFR_USB20_SDR_DMA_EN &= ~BIT(HUSB20_RXEP_HUB);  //使用sram通道
		XSFR_USB20_SIE_RXTYPE 	= ((pHusb_handle->usbsta.hub_attr << 4)|(pHusb_handle->usbsta.hub_ep&0x7f));//SET BULK AND ENDPIONT	
		hx330x_usb20_eprx_register(1, HUSB20_RXEP_HUB,husb_hub_eprx_isr);
		XSFR_USB20_SIE_EPRX_CTRL0 |= HUSB_EPRX_REQPKT;//request pkt,send a in packet
		//deg_Printf("XSFR_USB20_SIE_EPRX_CTRL0:%x\n",XSFR_USB20_SIE_EPRX_CTRL0);
		//deg_Printf("XSFR_USB20_SIE_EPRX_CTRL1:%x\n",XSFR_USB20_SIE_EPRX_CTRL1);
		//deg_Printf("XSFR_USB20_SIE_RXPKGMAXL:%x\n",XSFR_USB20_SIE_RXPKGMAXL);
		//deg_Printf("XSFR_USB20_SIE_RXPKGMAXH:%x\n",XSFR_USB20_SIE_RXPKGMAXH);
		//deg_Printf("XSFR_USB20_EP1_RXADDR:%x\n",XSFR_USB20_EP1_RXADDR);
		//deg_Printf("XSFR_USB20_SIE_RXTYPE:%x\n",XSFR_USB20_SIE_RXTYPE);
		XSFR_USB20_SIE_EPS		= index_temp;
	}else
	{
		deg_Printf("HUSB11 UVC INIT.\n");
		u32 index_temp				= XSFR_USB11_SIE_EPS;
		XSFR_USB11_SIE_EPS			= HUSB11_RXEP_HUB;
		XSFR_USB11_SIE_EPRX_CTRL0	= HUSB_EPRX_CLRDATATOG|HUSB_EPRX_FLUSHFIFO;
		XSFR_USB11_SIE_EPRX_CTRL1	= 0;
		XSFR_USB11_SIE_RXPKGMAX		= 250;
		(&XSFR_USB11_EP1_RXADDR)[HUSB11_RXEP_HUB*2-2]  = (u32)_USB11_MSC_RXFIFO_;
		XSFR_USB11_SIE_RXTYPE 	= ((pHusb_handle->usbsta.hub_attr << 4)|(pHusb_handle->usbsta.hub_ep&0x7f));//SET BULK AND ENDPIONT	
		hx330x_usb11_host_eprx_register(1, HUSB11_RXEP_HUB,husb_hub_eprx_isr);
		XSFR_USB11_SIE_EPRX_CTRL0 |= HUSB_EPRX_REQPKT;//request pkt,send a in packet
		XSFR_USB11_SIE_EPS		= index_temp;
	}
	return;
}
/*******************************************************************************
* Function Name  : husb_hub_uinit
* Description    : husb_hub_uinit
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void husb_hub_uinit(void *handle)
{
	HUSB_HANDLE *pHusb_handle = (HUSB_HANDLE *)handle;
	if(pHusb_handle == NULL || !(pHusb_handle->usbsta.device_sta & USB_HUB_CHECK))
		return ;
	
	pHusb_handle->usbsta.device_sta 	&= ~(USB_HUB_CHECK|USB_HUB_ATECH);
	if(pHusb_handle->usbsta.ch == USB20_CH)
	{
		hx330x_usb20_eprx_register(0, HUSB20_RXEP_HUB,NULL);
	}else
	{
		hx330x_usb11_host_eprx_register(0, HUSB11_RXEP_UVC,NULL);
	}
	uhub_handle = NULL;
}
#endif

