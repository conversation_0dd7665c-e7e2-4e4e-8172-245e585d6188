/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HAL_LCDMEM_H
#define HAL_LCDMEM_H

enum frame_type_u {
    FTYPE_VIDEO_ROTATE, // 0
                        //video rotate
                        //malloc from CSI中断/回放等非中断
                        //free from rotate中断
    FTYPE_VIDEO_DISPLAY,// 1
                        //video display
                        //malloc from CSI中断/回放等
                        //free from rotate中断，当de有新buffer时，free旧的的buffer
    FTYPE_UI_ROTATE,   // 2
                        //UI rotate
                        //malloc from UI画图
                        //free from 1.rotate中断，2.当de有新buffer时，free旧的的buffer
    FTYPE_UI_LZO,       // 3
                        //UI LZO

    FTYPE_UI_DISPLAY,  // 4
                        //UI dispaly
                        //malloc from rotate中断
                        //free from 1.uilzo中断，2.当de有新buffer时，free旧的的buffer
};

typedef struct frame_s {
    volatile u8  buf_sta;
    volatile u8  frame_type;
    volatile u8  scan_mode;
    
    volatile u8  win_sta;
    

    u8 * y_addr,* uv_addr;
    u8 * _y_addr,* _uv_addr;

    u32 buf_size; //y & uv total size
    u32 data_size;//for ui
    u16 w,h;
    u16 ratio_w,ratio_h;
	u16 destw,desth;
	u16 posX,posY;
    u16 stride;
    u16 id;


} lcdshow_frame_t;

typedef struct frame_ctl_t
{
    u8 frame_total;
    u8 frame_num[5];
    u16 reserve;
    lcdshow_frame_t frame[10];
}lcdshow_frame_ctrl_t;


/*******************************************************************************
* Function Name  : hal_lcdAddrCalculate
* Description    : hardware layer ,calculate y/uv offset addr
* Input          : u32 sx, u32 sy, lcdshow_frame_t *p_frame
* Output         : none
* Return         : none
*******************************************************************************/
void hal_lcdAddrCalculate(u32 sx, u32 sy, lcdshow_frame_t *p_frame);

/*******************************************************************************
* Function Name  : hal_lcdframes_vr_init
* Description    : hardware layer ,lcd video rotate frames init
* Input          : lcdshow_frame_t *p_frame
* Output         : None
* Return         : none
*******************************************************************************/
void hal_lcdframes_vr_init(lcdshow_frame_t *p_frame);
/*******************************************************************************
* Function Name  : hal_lcdframes_vd_init
* Description    : hardware layer ,lcd video display frames init
* Input          : lcdshow_frame_t *p_frame
* Output         : None
* Return         : none
*******************************************************************************/
void hal_lcdframes_vd_init(lcdshow_frame_t *p_frame);

/*******************************************************************************
* Function Name  : hal_dispframeInit
* Description    : init all display frames
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_dispframeInit(void);
/*******************************************************************************
* Function Name  : hal_dispframeUinit
* Description    : uinit all display frames
* Input          : none
* Output         : None
* Return         : None
*******************************************************************************/
void hal_dispframeUinit(void);
/*******************************************************************************
* Function Name  : hal_dispframeMalloc
* Description    : malloc a idle frame
* Input          : u8 frame_type : enum frame_type_u
* Output         : None
* Return         : NULL is fail
*******************************************************************************/
lcdshow_frame_t * hal_dispframeMalloc(u8 frame_type);
/*******************************************************************************
* Function Name  : hal_dispframeFree
* Description    : free a frame
* Input          : lcdshow_frame_t * p_frame
* Output         : None
* Return         : None
*******************************************************************************/
void hal_dispframeFree(lcdshow_frame_t * p_frame);


/*******************************************************************************
* Function Name  : hal_lcdVideoFrameFlush
* Description    : config video layer position and size
* Input          : p_frame: video frame
posX+width do not large than lcd width
posY+height do not large than lcd height
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdVideoFrameFlush(lcdshow_frame_t* p_frame, u16 x, u16 y, u16 w, u16 h, u16 dest_w, u16 dest_h);

/*******************************************************************************
* Function Name  : hal_dispframeInit
* Description    : init all display frames
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_dispframePrintf(void);
#endif
