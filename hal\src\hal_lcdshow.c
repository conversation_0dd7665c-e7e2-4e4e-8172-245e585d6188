/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             :
* Version            : v1
* Date               : 09/01/2021
* Description        :
***************************************************************************/
#include "../inc/hal.h"

/*******************************************************************************
* Function Name  : hal_lcdSetCsiCrop
* Description    : set csi LDMA crop
* Input          : u16 sx,u16 sy,u16 ex,u16 ey
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetCsiScaler(u16 sx,u16 sy,u16 ex,u16 ey)
{
    if(lcd_show_ctrl.crop_sx == sx && lcd_show_ctrl.crop_sy == sy && lcd_show_ctrl.crop_ex == ex && lcd_show_ctrl.crop_ey == ey)
        return;
    //deg_Printf("hal_lcdSetCsiScaler:%d,%d,%d,%d\n",sx,sy,ex,ey);
    lcd_show_ctrl.video_config_scaler &= ~VIDEO_SCALER_STAT_CSI;
    lcd_show_ctrl.crop_sx = sx;
    lcd_show_ctrl.crop_sy = sy;
    lcd_show_ctrl.crop_ex = ex;
    lcd_show_ctrl.crop_ey = ey;
    lcd_show_ctrl.video_config_scaler |= VIDEO_SCALER_STAT_CSI;
    hal_lcdSetWINAB(LCDWIN_A,-1,-1,-1,-1,-1,-1);
    hal_lcdSetWINAB(LCDWIN_B,-1,-1,-1,-1,-1,-1);

}
/*******************************************************************************
* Function Name  : hal_lcdSetVideoScaler
* Description    : set video scaler
* Input          : u16 sx,u16 sy,u16 ex,u16 ey
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetVideoScaler(u16 sx,u16 sy,u16 ex,u16 ey)
{
    lcd_show_ctrl.video_config_scaler &= ~VIDEO_SCALER_STAT_VIDEO;
    //deg_Printf("hal_lcdSetVideoScaler:%d,%d,%d,%d\n",sx,sy,ex,ey);
    if(lcd_show_ctrl.video_scan_mode & LCD_DISPLAY_ROTATE_EN)
    {
        lcd_show_ctrl.scaler_sx = sy;
        lcd_show_ctrl.scaler_sy = sx;
        lcd_show_ctrl.scaler_ex = ey;
        lcd_show_ctrl.scaler_ey = ex;
    }else
    {
        lcd_show_ctrl.scaler_sx = sx;
        lcd_show_ctrl.scaler_sy = sy;
        lcd_show_ctrl.scaler_ex = ex;
        lcd_show_ctrl.scaler_ey = ey;
    }

    lcd_show_ctrl.video_config_scaler |= VIDEO_SCALER_STAT_VIDEO;
}
/*******************************************************************************
* Function Name  : hal_lcd_scaler_done_check
* Description    : hal_lcd_scaler_done_check
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_lcd_scaler_done_check(void)
{
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
    u8 scaler = lcd_show_ctrl.video_config_scaler;
    HAL_CRITICAL_EXIT();
    if(scaler == VIDEO_SCALER_STAT_NONE)
        return true;
    else
        return false;
}
/*******************************************************************************
* Function Name  : hal_lcdVideoScalerTypeAdj
* Description    : hal_lcdVideoScalerTypeAdj
* Input          : u8 scaler_type
* Output         : None
* Return         : None
*******************************************************************************/
u8 hal_lcdVideoScalerTypeAdj(u8 scaler_type)
{
    u8 scan_mode = hal_lcdVideoScanModeGet();
    if(scan_mode & LCD_DISPLAY_ROTATE_180)
    {
        switch(scaler_type)
        {
            case VIDEO_SCALER_LEFT:  return VIDEO_SCALER_RIGHT;
            case VIDEO_SCALER_RIGHT: return VIDEO_SCALER_LEFT;
            case VIDEO_SCALER_UP:    return VIDEO_SCALER_DOWN;
            case VIDEO_SCALER_DOWN:  return VIDEO_SCALER_UP;
            default: break;
        }
    }else if(scan_mode & LCD_DISPLAY_H_MIRROR)
    {
        switch(scaler_type)
        {
            case VIDEO_SCALER_LEFT:  return VIDEO_SCALER_RIGHT;
            case VIDEO_SCALER_RIGHT: return VIDEO_SCALER_LEFT;
            default: break;
        }
    }else if(scan_mode & LCD_DISPLAY_V_MIRROR)
    {
        switch(scaler_type)
        {
            case VIDEO_SCALER_UP:    return VIDEO_SCALER_DOWN;
            case VIDEO_SCALER_DOWN:  return VIDEO_SCALER_UP;
            default: break;
        }
    }
    return scaler_type;

}
/*******************************************************************************
* Function Name  : hal_lcdSetRotate
* Description    : hardware layer ,set lcd rotate degree
* Input          : u8 rotate : rotate
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,

    LCD_DISPLAY_MIRROR_NONE = 0x00,
    LCD_DISPLAY_V_MIRROR    = 0x10,
    LCD_DISPLAY_H_MIRROR    = 0x30,
* Output         : None
* Return         :
*******************************************************************************/
s32 hal_lcdVideoSetRotate(u8 rotate)
{
    u8 target = lcd_show_ctrl.lcd_scan_mode + rotate;
    u8 rotate_mode;
    u8 mirror_mode = LCD_DISPLAY_ROTATE_0;
    switch(target & (LCD_DISPLAY_ROTATE_MASK|LCD_DISPLAY_MIRROR_MASK))
    {
        case (LCD_DISPLAY_V_MIRROR + LCD_DISPLAY_H_MIRROR) : target += LCD_DISPLAY_ROTATE_180; break;
        case (LCD_DISPLAY_V_MIRROR + LCD_DISPLAY_V_MIRROR) :
        case (LCD_DISPLAY_H_MIRROR + LCD_DISPLAY_H_MIRROR) : target += LCD_DISPLAY_ROTATE_0;   break;
        default: mirror_mode = target & LCD_DISPLAY_MIRROR_MASK;
    }
    rotate_mode = (target & LCD_DISPLAY_ROTATE_MASK);
    if((rotate_mode >= LCD_DISPLAY_ROTATE_180 ) && (mirror_mode != LCD_DISPLAY_ROTATE_0))
    {
        rotate_mode -= LCD_DISPLAY_ROTATE_180;
        if(mirror_mode == LCD_DISPLAY_V_MIRROR)
        {
            mirror_mode = LCD_DISPLAY_H_MIRROR;
        }else
        {
            mirror_mode = LCD_DISPLAY_V_MIRROR;
        }
    }
    //deg_Printf("[LCD SET ROTATE] rotate:%x, mirror:%x\n",rotate_mode, mirror_mode );
    if( mirror_mode != LCD_DISPLAY_MIRROR_NONE && rotate_mode != LCD_DISPLAY_ROTATE_0 &&  rotate_mode!= LCD_DISPLAY_ROTATE_90)
    {
        return -1;
    }
    if((rotate_mode + mirror_mode) == lcd_show_ctrl.video_scan_mode)
    {
        return -1;
    }
    lcd_show_ctrl.video_config_rotate    &= ~BIT(0);
    lcd_show_ctrl.video_scan_mode_preset = rotate_mode + mirror_mode;
    lcd_show_ctrl.video_config_rotate    |= BIT(0);
    deg_Printf("[LCD VIDEO ROTATE]%x\n", lcd_show_ctrl.video_scan_mode_preset);
	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdUiSetRotate
* Description    : hardware layer ,set lcd ui rotate degree
* Input          : u8 rotate : rotate
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,
* Output         : None
* Return         :
*******************************************************************************/
s32 hal_lcdUiSetRotate(u8 rotate)
{
    u8 src_mode = lcd_show_ctrl.p_lcddev->scan_mode & LCD_DISPLAY_ROTATE_MASK;
    rotate = rotate & LCD_DISPLAY_ROTATE_MASK;
    u8 target_mode = (src_mode + rotate) & LCD_DISPLAY_ROTATE_MASK;
    lcd_show_ctrl.video_config_rotate &= ~BIT(1);
    lcd_show_ctrl.ui_scan_mode_preset = target_mode;
    lcd_show_ctrl.video_config_rotate |= BIT(1);
    deg_Printf("[LCD UI ROTATE]%x\n", lcd_show_ctrl.ui_scan_mode);
	return 0;
}

/*******************************************************************************
* Function Name  : hal_lcdRatioResCal
* Description    : calculate resolution of ratio
* Input          : u8 ratio_w,u8 ratio_h
                   u16 *width : width
                   u16 *height : height
* Output         : none
* Return         :0
*******************************************************************************/
static void hal_lcdRatioResCal(u8 ratio_w,u8 ratio_h,u16 *width,u16 *height)
{
    int ratw,rath;
 	u16 tar_width, tar_height;
    if(width == NULL || height == NULL)
		return ;

	ratw = *width/ratio_w;
	rath = *height/ratio_h;
	if(ratw == rath)
		return;

	tar_height = ratw*ratio_h; //120*3 = 360
	if(tar_height > *height)
	{
		tar_width = rath*ratio_w; //91*4 = 364
		if(tar_width < *width)
			*width = tar_width;
	}
	else
		*height = tar_height;
    return;
}

/*******************************************************************************
* Function Name  : hal_lcdSetRatio
* Description    : hal_lcdSetRatio
* Input          : u16 ratio : LCD_RATIO_MAKE(w, h)
* Output         : None
* Return         :
*******************************************************************************/
void hal_lcdSetRatio(u16 ratio, u8 sensor_adj)
{
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
    u8 ratio_w = LCD_RATIO_GET_W(ratio);
    u8 ratio_h = LCD_RATIO_GET_H(ratio);
    lcd_show_ctrl.cur_ratio = ratio;
    if(sensor_adj)
    {
        u16 csi_w,csi_h;
        if(husb_api_usensor_tran_sta() && husb_api_usensor_res_type_is_yuv())
        {
            husb_api_usensor_res_get(&csi_w,&csi_h);
        }else{

            if(hal_SensorResolutionGet(&csi_w,&csi_h) < 0)
            {
                csi_w = lcd_show_ctrl.video_w;
                csi_h = lcd_show_ctrl.video_h;
            }
        }

        lcd_show_ctrl.ratio_w = hx330x_min(csi_w,lcd_show_ctrl.video_w);
        lcd_show_ctrl.ratio_h = hx330x_min(csi_h,lcd_show_ctrl.video_h);
    }else
    {
        lcd_show_ctrl.ratio_w = lcd_show_ctrl.video_w;
        lcd_show_ctrl.ratio_h = lcd_show_ctrl.video_h;
    }


    lcd_show_ctrl.ratio_dest_w = lcd_show_ctrl.video_scaler_w;
    lcd_show_ctrl.ratio_dest_h = lcd_show_ctrl.video_scaler_h;
    lcd_show_ctrl.ratio_x  = lcd_show_ctrl.video_x;
    lcd_show_ctrl.ratio_y  = lcd_show_ctrl.video_y;
    if(ratio_w && ratio_h )
    {
        hal_lcdRatioResCal(ratio_w,ratio_h, &lcd_show_ctrl.ratio_dest_w, &lcd_show_ctrl.ratio_dest_h);
        if(lcd_show_ctrl.ratio_dest_w < lcd_show_ctrl.ratio_w )
        {
           lcd_show_ctrl.ratio_w =  lcd_show_ctrl.ratio_dest_w;
        }
        if(lcd_show_ctrl.ratio_dest_h < lcd_show_ctrl.ratio_h )
        {
           lcd_show_ctrl.ratio_h =  lcd_show_ctrl.ratio_dest_h;
        }
        lcd_show_ctrl.ratio_x +=  (lcd_show_ctrl.video_scaler_w - lcd_show_ctrl.ratio_dest_w)/2;
        lcd_show_ctrl.ratio_y +=  (lcd_show_ctrl.video_scaler_h - lcd_show_ctrl.ratio_dest_h)/2;
    }
    if(lcd_show_ctrl.ratio_w < lcd_show_ctrl.video_w || lcd_show_ctrl.ratio_h < lcd_show_ctrl.video_h)
    {
        lcd_show_ctrl.ratio_cfg = 1;
    }else
    {
        lcd_show_ctrl.ratio_cfg = 0;
    }
    //lcd_show_ctrl.lcdwins[LCDWIN_A]->w = lcd_show_ctrl.ratio_w;
    //lcd_show_ctrl.lcdwins[LCDWIN_A]->h = lcd_show_ctrl.ratio_h;
    //lcd_show_ctrl.lcdwins[LCDWIN_A]->bak->w = lcd_show_ctrl.ratio_w;
    //lcd_show_ctrl.lcdwins[LCDWIN_A]->bak->h = lcd_show_ctrl.ratio_h;
    //deg_Printf("[LCD ratio][%d:%d] pos[%d,%d], res[%d,%d], dest[%d,%d]\n",
    //ratio_w, ratio_h, lcd_show_ctrl.ratio_x, lcd_show_ctrl.ratio_y,
    //lcd_show_ctrl.ratio_w, lcd_show_ctrl.ratio_h, lcd_show_ctrl.ratio_dest_w, lcd_show_ctrl.ratio_dest_h);
    HAL_CRITICAL_EXIT();
}
/*******************************************************************************
* Function Name  : hal_lcdSetBufYUV
* Description    : memset buffer color,but U must equ V
* Input          : buffer: lcd buffer pointer
                   y:
                   u: must u = v
                   v: must u = v
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetBufYUV(lcdshow_frame_t * buffer,u8 y,u8 u,u8 v)
{
    if(buffer)
    {
        u32 y_size  = buffer->ratio_h * buffer->stride;
        u32 uv_size = y_size / 2;
        memset(buffer->y_addr,y,y_size);
        hx330x_sysDcacheWback((u32)buffer->y_addr,y_size);
        memset(buffer->uv_addr,u,uv_size);
        hx330x_sysDcacheWback((u32)buffer->uv_addr,uv_size);
    }
}
/*******************************************************************************
* Function Name  : hal_lcdSetBufYUV
* Description    : memset buffer color,but U must equ V
* Input          : buffer: lcd buffer pointer
                   y:
                   u: must u = v
                   v: must u = v
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetBufYUV_2(u8 * buffer,u32 buf_size,u8 y,u8 uv)
{
    if(buffer)
    {
		u32 y_size = buf_size*2/3;
        memset(buffer,y,y_size);
		memset(buffer+y_size,uv,y_size/2);
        hx330x_sysDcacheWback((u32)buffer,buf_size);
    }
}
/*******************************************************************************
* Function Name  : hal_lcdSetWINAB
* Description    : set lcd video channles
* Input          : u8 src:video channle source,enum {LCDWIN_B,LCDWIN_A}
*                  u8 layer:video channle layer,enum {LCDWIN_TOP_LAYER,LCDWIN_BOT_LAYER}
*                  u16 x:if x == -1,means don't change this parameter
*                  u16 y:
*                  u16 w:
*                  u16 h:
*                  u8 win_en:channle enable,enum {WINAB_EN,WINAB_DIS},if win_en == -1,means don't change
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetWINAB(u8 src,u8 layer,
                  u16 x,u16 y,u16 w,u16 h,
                  u8 win_en)
{

    lcd_win_t * p_lcd_vch = lcd_show_ctrl.lcdwins[src]->bak;
    //deg_Printf("[%d:%d] pos[%d,%d], res[%d,%d]\n", src, layer, x, y,w, h);
    if(p_lcd_vch)
    {
        p_lcd_vch->config_enable = 0;
        if(x != 0xffff)    p_lcd_vch->x = x & ~7;
        if(y != 0xffff)    p_lcd_vch->y = y & ~1;
        if(w != 0xffff)    p_lcd_vch->w = w;
        if(h != 0xffff)    p_lcd_vch->h = h;
        if(layer != 0xff)  p_lcd_vch->layer = layer;
        if(win_en != 0xff) p_lcd_vch->status = win_en;
        p_lcd_vch->config_enable = 1;
    }


}
/*******************************************************************************
* Function Name  : hal_lcdWinEnablePreSet
* Description    : prepare set lcd win enable/disbale,
*                  take effect when next csi frame done
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdWinEnablePreSet(u8 enable)
{
    lcd_show_ctrl.lcd_winAB_en = enable;
}
/*******************************************************************************
* Function Name  : hal_lcdSetWinEnable
* Description    : set lcd win enable/disbale,take effect immediately
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetWinEnable(u8 enable)
{
    lcd_show_ctrl.lcd_winAB_en = enable;
    hx330x_lcdWinABEnable(enable);
}
/*******************************************************************************
* Function Name  : lcd_struct_get
* Description    : lcd_struct_get
* Input          :
* Output         : lcddev_t * p_lcd_struct
* Return         : none
*******************************************************************************/
lcddev_t * lcd_struct_get(void)
{
	return lcd_show_ctrl.p_lcddev;
}
/*******************************************************************************
* Function Name  : hal_lcdLCMPowerOff
* Description    : lcd module power off sequence
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdLCMPowerOff(void)
{
    if(lcd_show_ctrl.p_lcddev && lcd_show_ctrl.p_lcddev->uninit_table)
    {
        if(lcd_show_ctrl.p_lcddev->lcd_bus_type == LCD_BUS_MCU)
        {
            hx330x_lcdEnable(0);
            hx330x_lcdTeMode(0, 0);
            hx330x_lcdEnable(1);
        }
        lcd_initTab_config(lcd_show_ctrl.p_lcddev->uninit_table);
    }
}


/*******************************************************************************
 * WINAB PROCESS有三种
 *  WINAB_PROCESS_TYPE_DECWINSRC_ENCSRC：UVC 同时解码到 WIN和SRC，然后用SRC的YUV进行ENC,经历以下STEP：
 * (1) WINAB_PROCESS_STEP_END:
 * -读UVC FRAME，成功
 *      -如果不需要ENC，则进入 WINAB_PROCESS_STEP_0_DECWIN ，只需要DEC到WIN,  KICK LCD DMA
 *      -如果需要ENC，则进入 WINAB_PROCESS_STEP_0_DECSRC ，同时DEC到WIN和SRC, KICK LCD DMA
 * -读UVC FRAME，失败， WINAB_PROCESS_STEP_END, KICK LCD DMA
 * (2) WINAB_PROCESS_STEP_0_DECWIN：等待DEC WIN解码完成，结束。
 * - 解码成功：LB_READY1， FREE UVC FRAME，WINAB_PROCESS_STEP_END
 * - 解码失败：FREE UVC FRAME（ERROR），WINAB_PROCESS_STEP_END
 * (3) WINAB_PROCESS_STEP_0_DECSRC: 等待DEC SRC解码完成
 * - 解码成功：LB_READY1， FREE UVC FRAME，判断外部是否停止ENC,
 *      - 没有停止 ENC: kick enc SRC (不需要配linebuf)
 *      - 停止 ENC : WINAB_PROCESS_STEP_END
 * - 解码失败：FREE UVC FRAME（ERROR）， WINAB_PROCESS_STEP_END
 * (4) ENC_DONE: WINAB_PROCESS_STEP_END
 * *******************************************************************************/

/*******************************************************************************
* Function Name  : hal_lcd_getJpgIdleBuf
* Description    : hal_lcd_getJpgIdleBuf
* Input          : none
* Output         : None
* Return         : bool: true: no kic lcd dma, false: kick lcd dma
*******************************************************************************/
static void hal_lcd_win_dec_kick(void)
{
    //lcd_show_ctrl.jpg_dcd_buf = NULL;
	if(husb_api_usensor_tran_sta())
    {
        //deg_Printf("E");
        if(lcd_show_ctrl.winAB_process_step != WINAB_PROCESS_STEP_END)
        {
            return ;
        }
        //deg_Printf("F");
        lcd_show_ctrl.jpg_dcd_buf = husb_api_usensor_frame_read(&lcd_show_ctrl.jpg_dcd_len);
        if(lcd_show_ctrl.jpg_dcd_buf && lcd_show_ctrl.jpg_dcd_len)
		{
            lcd_show_ctrl.jpg_frame_sta = 1;
            lcd_show_ctrl.jpg_dcd_len += 64;
			hx330x_mjpB_reset();
            if(hx330x_mjpA_Encode_StartFunc_Check() == false)
            {
                hal_mjpB_DecodeODMA1En(0);
                lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_0_DECWIN;
                lcd_show_ctrl.lcd_dma_sta = 0;

            }else
            {
            #if HAL_MJP_SAVE_CARD_DIRECT == 0
                u16 sensor_w, sensor_h;
                u16 enc_src_w, enc_src_h;
                husb_api_usensor_res_get(&sensor_w, &sensor_h);
                hal_mjpA_src_res_get(&enc_src_w,&enc_src_h);
                if(hal_mjpDecodeIsYUV422() == 0 || sensor_w > enc_src_w || sensor_h > enc_src_h)
                //if(1)
                {
                    hal_mjpB_DecodeODMA1En(0);
                    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_1_DECWIN;
                    lcd_show_ctrl.lcd_dma_sta = 1;
                }else
                {

                    hal_mjpB_DecodeODMA1En(1);
                    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_0_DECSRC;
                    lcd_show_ctrl.lcd_dma_sta = 0; //will kick lcd dma
                }
            #else
                hal_mjpB_DecodeODMA1En(0);
                lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_2_DECWINSAVE;
                lcd_show_ctrl.lcd_dma_sta = 0;
            #endif



            }
            lcd_win_t * p_WINB = lcd_show_ctrl.lcdwins[LCDWIN_B];
            u8 * p_y = &lcd_show_ctrl.win_using->y_addr[p_WINB->x +
                       (p_WINB->y * lcd_show_ctrl.win_using->stride)];
            u8 * p_uv= &lcd_show_ctrl.win_using->uv_addr[p_WINB->x +
                       (p_WINB->y / 2 * lcd_show_ctrl.win_using->stride)];
            //deg_Printf("JPG:[%x][%d,%d][%d,%d,%d]\n",p_uv-p_y,p_WINB->x, p_WINB->y,  p_WINB->w,p_WINB->h,lcd_show_ctrl.win_using->stride);
            hal_mjpDecodeOneFrame_Fast(lcd_show_ctrl.jpg_dcd_buf,lcd_show_ctrl.jpg_dcd_buf + lcd_show_ctrl.jpg_dcd_len,
                                        p_y,p_uv,
                                        p_WINB->w,p_WINB->h,
                                        lcd_show_ctrl.win_using->stride);
            //如果JPG没有DRI，可能导致JPG丢包但解码仍然成功，所以指定JPIEND地址，会产生DC TIME OUT 中断
            //XSFR_JPEG1_IN_EADDR = ((u32)lcd_show_ctrl.jpg_dcd_buf + lcd_show_ctrl.jpg_dcd_len + 7)&~0x07;
            //if(lcd_show_ctrl.winAB_process_step == WINAB_PROCESS_STEP_0_DECSRC)
            //{
            //     XSFR_JPEG1_INTCON   = (XSFR_JPEG1_INTCON &~(1<<6))|(1<<2); //only enable yuv422 src img decode int
            //}
            //deg_Printf("G");
        }else{
            lcd_show_ctrl.jpg_frame_sta = 0;
            lcd_show_ctrl.lcd_dma_sta = 0;
			lcd_show_ctrl.jpg_dcd_buf = NULL;
        }
        //deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);

	}else
	{
		lcd_show_ctrl.jpg_dcd_buf = NULL;
	}
}
/*******************************************************************************
* Function Name  : hal_lcd_wintotal_dec_kick
* Description    : hal_lcd_wintotal_dec_kick
* Input          : none
* Output         : None
* Return         : bool: true: no kic lcd dma, false: kick lcd dma
*******************************************************************************/
static bool hal_lcd_wintotal_dec_kick(void)
{
	if(lcd_show_ctrl.jpg_dcd_buf)
	{
        u32 linebuf_y, linebuf_uv;
        u16 enc_src_w, enc_src_h;
	    hal_mjpA_LineBuf_get(&linebuf_y, &linebuf_uv);
        hal_mjpA_src_res_get(&enc_src_w,&enc_src_h);

        hx330x_lcdWinABEnable(0);
        lcd_show_ctrl.win_reset_en = 1;
        //hal_mjpDecodeParse(lcd_show_ctrl.jpg_dcd_buf,sensor_w,sensor_h);
        hx330x_mjpB_reset();
        hal_mjpB_DecodeODMA1En(0);
		hal_mjpDecodeOneFrame_Fast(lcd_show_ctrl.jpg_dcd_buf,lcd_show_ctrl.jpg_dcd_buf + lcd_show_ctrl.jpg_dcd_len,
                                    (u8*)linebuf_y,(u8*)linebuf_uv,
                                         enc_src_w,enc_src_h,
                                         (enc_src_w + 0x1f)&~0x1f);
		//如果JPG没有DRI，可能导致JPG丢包但解码仍然成功，所以指定JPIEND地址，会产生DC TIME OUT 中断
		//XSFR_JPEG1_IN_EADDR = ((u32)lcd_show_ctrl.jpg_dcd_buf + lcd_show_ctrl.jpg_dcd_len + 7)&~0x07;
        return true;

	}
    return false;
}
/*******************************************************************************
* Function Name  : hal_lcdMJPB_Decode_Done
* Description    : hardware layer ,lcd process for mjpB decode done
* Input          : int flag: mjpB decode flag
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_decwin_done(int flag)
{
    u8 kick_enc = 0;
    u8 usensor_frame_free  = 0;
    u8 dec_sta = 0;
    u8 kick_lcddma = 0;
    if(!lcd_show_ctrl.win_using) return;

    //deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);


	if(flag & (BIT(1)|BIT(8)|BIT(5)|BIT(12)|BIT(13)))//error,不管处于哪个step，都要结束，同时释放usensor_frame
    {
        //deg_Printf("err:%x\n", flag);
        goto LCD_DECWIN_FAIL;
	}else if(flag & (BIT(2)|BIT(6)))
    {
        //if(!(flag & BIT(1)))
        //{
        //    deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);
        //    deg_Printf("err:%x\n", flag);
        //    goto LCD_DECWIN_FAIL;
        //}else  //decode win done
        {
            if(flag & BIT(6))
            {
                switch(lcd_show_ctrl.winAB_process_step)
                {
                    case WINAB_PROCESS_STEP_0_DECWIN:   //不用enc，直接结束
                        usensor_frame_free = 1;
                        lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
                        lcd_show_ctrl.win_using->win_sta |= LB_READY1;
                        break;
                    case WINAB_PROCESS_STEP_0_DECSRC:   //要等decode src done再启动encode
                        lcd_show_ctrl.win_using->win_sta |= LB_READY1;
                        //usensor_frame_free = 1;
                        break;
                    case WINAB_PROCESS_STEP_1_DECWIN:   //再启动一次DEC
                        lcd_show_ctrl.win_using->win_sta |= LB_READY1;
                        if(hx330x_mjpA_Encode_StartFunc_Check() == false || hal_lcd_wintotal_dec_kick() == false)
                        {
                            //deg_Printf("wintotal_dec_kick fail\n");
                            lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
                            kick_lcddma = 1;
                            usensor_frame_free = 1;
                        }else
                        {
                            lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_1_DECWINTOTAL;
                        }
                        break;
                    case WINAB_PROCESS_STEP_1_DECWINTOTAL: //配置buf，启动ENC，释放 uvc frame, 重配win并kick lcd dma,注意需要在ENC DONE释放jpg_using
                        if(lcd_show_ctrl.win_reset_en == 1)
                        {
                            hx330x_lcdWinABEnable(1);
                            lcd_show_ctrl.win_reset_en = 0;
                        }
                        kick_lcddma = 1;
                        usensor_frame_free = 1;
                        if(hx330x_mjpA_Encode_StartFunc_Check() == true)
                        {
                            kick_enc = 1;
                        }else
                        {
                            lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
                        }
                        break;
                    case WINAB_PROCESS_STEP_2_DECWINSAVE:
                        //usensor_frame_free = 1;
                        //lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
                        lcd_show_ctrl.win_using->win_sta |= LB_READY1;   
                        kick_lcddma = 1; 
                        if(hx330x_mjpA_Encode_StartFunc_Check() == true)
                        {
                            kick_enc = 1;    
                        }else
                        {
                            lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;   
                            usensor_frame_free = 1;
                        }
                        break;
                }
            }
            if(lcd_show_ctrl.winAB_process_step == WINAB_PROCESS_STEP_0_DECSRC) //启动src encode，linebuf固定
            {
                lcd_show_ctrl.win_using->win_sta |= LB_READY1;
                usensor_frame_free = 1;
                if((flag & BIT(2)) && (hx330x_mjpA_Encode_StartFunc_Check() == true))
                {
                    kick_enc = 1;
                }else
                {
                    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
                }
            }
            goto LCD_DECWIN_OK;

        }
    }else
    {
        deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);
        deg_Printf("err:%x\n", flag);
    }
LCD_DECWIN_FAIL:
    usensor_frame_free = 1;
	dec_sta = 1;
    kick_lcddma = 1;
    if(lcd_show_ctrl.win_reset_en == 1)
    {
        hx330x_lcdWinABEnable(1);
        lcd_show_ctrl.win_reset_en = 0;
    }else
    {
        hx330x_lcdWinReset(WIN_SRC_B);
    }
    //deg_Printf("[%d]\n",lcd_show_ctrl.winAB_process_step);
    //deg_Printf("err:%x\n", flag);
    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
LCD_DECWIN_OK:

    //deg_Printf("1[%d]\n",lcd_show_ctrl.winAB_process_step);
    if(usensor_frame_free && lcd_show_ctrl.jpg_frame_sta)
    {
        husb_api_usensor_dcdown(dec_sta);
        lcd_show_ctrl.jpg_frame_sta = 0;
        //deg_Printf("A");
    }
    if(kick_lcddma && lcd_show_ctrl.lcd_dma_sta)
    {
        hx330x_csiLCDDmaKick();
        lcd_show_ctrl.lcd_dma_sta = 0;
    //    deg_Printf("B");
    }
    if(kick_enc)
    {
    #if HAL_MJP_SAVE_CARD_DIRECT == 0    
		hx330x_mjpA_Encode_StartFunc_call(); //call hal_mjpB_manualstart
    #else
        hal_mjpSaveDirect(lcd_show_ctrl.jpg_dcd_buf, lcd_show_ctrl.jpg_dcd_len);
    #endif
        //deg_Printf("C");
    }
    //deg_Printf("flag:%x\n", flag);

}
/*******************************************************************************
* Function Name  : hal_lcdMJPB_Decode_Done
* Description    : hardware layer ,lcd process for mjpB decode done
* Input          : int flag: mjpB decode flag
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_encwin_done(void)
{
    if(!lcd_show_ctrl.win_using) return;
    if(lcd_show_ctrl.win_reset_en == 1)
    {
        hx330x_lcdWinABEnable(1);
        lcd_show_ctrl.win_reset_en = 0;
    }
    lcd_show_ctrl.winAB_process_step = WINAB_PROCESS_STEP_END;
    if(lcd_show_ctrl.jpg_frame_sta && lcd_show_ctrl.jpg_dcd_buf)
    {
        husb_api_usensor_dcdown(0);
        lcd_show_ctrl.jpg_frame_sta = 0;
    }
    if( lcd_show_ctrl.lcd_dma_sta)
    {
        hx330x_csiLCDDmaKick();
        lcd_show_ctrl.lcd_dma_sta = 0;
    //    deg_Printf("B");
    }
    //deg_Printf("P");
}
ALIGNED(4) static void (*hal_lcd_frame_enc_func)(u32 y_addr, u32 uv_addr);
void hal_lcd_frame_enc_func_register(void (*fp_fun)(u32 y_addr, u32 uv_addr))
{
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
	hal_lcd_frame_enc_func = fp_fun;
    HAL_CRITICAL_EXIT();
}


/*******************************************************************************
* Function Name  : hal_CSI_lcdFrameEndCallback
* Description    : hardware layer ,switch frame when csi frame end
* Input          : none
* Output         : None
* Return         : none
*******************************************************************************/
static u32 lcd_fps;
void hal_CSI_lcdFrameEndCallback(void)
{
    lcdshow_frame_t * ready = lcd_show_ctrl.win_using;
    lcdshow_frame_t * next = NULL;

    if(!ready)
    {
        //never run here
        deg_Printf("[LCD Err] csi using null\n");
        return;
    }

    //debgchar('-');
    hal_lcdWinUpdata();

    lcd_win_t * p_WINA = lcd_show_ctrl.lcdwins[LCDWIN_A];
    lcd_win_t * p_WINB = lcd_show_ctrl.lcdwins[LCDWIN_B];
    lcd_win_t * p_WIN_top = lcd_show_ctrl.lcdwins[LCDWIN_TOP_LAYER];

    u8 not_skip = 1;
    lcd_show_ctrl.lcd_dma_sta = 0;
    lcd_show_ctrl.win_reset_en = 0;
    //config lcdwinAB,必须在csi没有DMA，且JPGB也没有DMA的情况下修改lcdwinAB配置
    if(p_WIN_top->config_enable && (p_WIN_top->status == WINAB_EN))
    {
        //hx330x_csiLCDDmaEnable(0);
        hx330x_lcdWinABConfig(p_WIN_top->src,
                         p_WIN_top->x,p_WIN_top->y,
                         p_WIN_top->w,p_WIN_top->h,
                         lcd_show_ctrl.lcd_winAB_en);
        hx330x_lcdVideoSetScaleLine(p_WIN_top->y + p_WIN_top->h - 1,1);
        //deg_Printf("[WIN%d]%x, %x, %x,%x,%x\n",p_WIN_top->src, XSFR_LCDPIP_CON,XSFR_LCDPIP_POS,XSFR_LCDPIP_BSIZE, XSFR_LCDC_SHOW_CTRL );
        //deg_Printf("pip:%d, [%d,%d],[%d,%d]%d\n",p_WIN_top->src,
        //                 p_WIN_top->x,p_WIN_top->y,
        //                p_WIN_top->w,p_WIN_top->h, lcd_show_ctrl.lcd_winAB_en);
        //deg_Printf("JPG:[%d,%d][%d,%d,%d]\n",p_WINB->x, p_WINB->y,
        //p_WINB->w,p_WINB->h,lcd_show_ctrl.win_using->stride);
        ///hx330x_csiLCDDmaEnable(1);
    }
    //config linescaler
    if(p_WINA->config_enable)
    {
        not_skip = 0;//skip this frame

		hx330x_csiLCDScaler(p_WINA->w,p_WINA->h,
                             lcd_show_ctrl.crop_sx,lcd_show_ctrl.crop_sy,lcd_show_ctrl.crop_ex,lcd_show_ctrl.crop_ey,
							ready->stride);
        lcd_show_ctrl.video_config_scaler &= ~VIDEO_SCALER_STAT_CSI;
        //deg_Printf("CSI LCD Scaler [%d, %d]- [%d,%d,%d,%d]\n",p_WINA->w,p_WINA->h,
        //                     lcd_show_ctrl.crop_sx,lcd_show_ctrl.crop_sy,lcd_show_ctrl.crop_ex,lcd_show_ctrl.crop_ey);
    }

    ready->win_sta |= LB_READY0;
    u8  buf_ready = LB_READY0;
    if (p_WINB->status == WINAB_EN)
    {
        buf_ready |= LB_READY1;
    }

    if(((ready->win_sta & buf_ready) == buf_ready) && not_skip)
    {
        next = hal_lcdVideoIdleFrameMalloc();
        if(next)
        {
            //deg_Printf("+");
            //lcd_show_ctrl.video_config_scaler &= ~VIDEO_SCALER_STAT_VALID;
            //update display buffer
            lcd_fps++;
            if(hal_lcd_frame_enc_func)
            {
                hal_lcd_frame_enc_func((u32)ready->y_addr, (u32)ready->uv_addr);
            }
            hal_lcdVideoSetFrame(ready);
            //config next frame
			u16 csi_w,csi_h;
			if(husb_api_usensor_tran_sta() && husb_api_usensor_res_type_is_yuv())
			{
				husb_api_usensor_res_get(&csi_w,&csi_h);
			}else{
				hal_SensorResolutionGet(&csi_w,&csi_h);
			}
            lcd_show_ctrl.ratio_w = hx330x_min(csi_w,lcd_show_ctrl.ratio_w);
		    lcd_show_ctrl.ratio_h = hx330x_min(csi_h,lcd_show_ctrl.ratio_h);
            //deg_Printf("next:pos[%d,%d],src[%d,%d],dst[%d,%d]\n",lcd_show_ctrl.ratio_x, lcd_show_ctrl.ratio_y,
            //                       lcd_show_ctrl.ratio_w, lcd_show_ctrl.ratio_h,
            //                       lcd_show_ctrl.ratio_dest_w, lcd_show_ctrl.ratio_dest_h);
            hal_lcdVideoFrameFlush(next,
                                   lcd_show_ctrl.ratio_x, lcd_show_ctrl.ratio_y,
                                   lcd_show_ctrl.ratio_w, lcd_show_ctrl.ratio_h,
                                   lcd_show_ctrl.ratio_dest_w, lcd_show_ctrl.ratio_dest_h);
            u32 csi_y_ofs  = p_WINA->x + (p_WINA->y * next->stride);
            u32 csi_uv_ofs = p_WINA->x + (p_WINA->y / 2 * next->stride);

            hx330x_csiLCDFrameSet((u32)&next->y_addr[csi_y_ofs],
                                  (u32)&next->uv_addr[csi_uv_ofs]);
            //updata using
            lcd_show_ctrl.win_using = next;
        }
    }

    if(!next)
    {
        u32 csi_y_ofs  = p_WINA->x + (p_WINA->y * ready->stride);
        u32 csi_uv_ofs = p_WINA->x + (p_WINA->y / 2 * ready->stride);
        hx330x_csiLCDFrameSet((u32)&ready->y_addr[csi_y_ofs],
                              (u32)&ready->uv_addr[csi_uv_ofs]);
        ready->win_sta = LB_IDLE;
    }
    p_WINA->config_enable =
    p_WINB->config_enable = 0;

    if((p_WINB->status == WINAB_EN) &&
        (hal_BackRecDecodeStatusCheck()) &&
        hx330x_csiLCDScalerDoneCheck())
    {
        hal_lcd_win_dec_kick();
    }
    if(lcd_show_ctrl.lcd_dma_sta == 0)
        hx330x_csiLCDDmaKick();

}
void hal_lcd_fps_debg(void)
{
    u32 temp = lcd_fps;
    lcd_fps = 0;
    deg_Printf("LCD FPS %d\n", temp);
}
/*******************************************************************************
* Function Name  : hal_lcdGetSreenResolution
* Description    : hardware layer ,get lcd resolution,after rotate
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetSreenResolution(u16 *width,u16 *height)
{
	if(lcd_show_ctrl.p_lcddev == NULL)
		return -1;
	if(lcd_show_ctrl.lcd_scan_mode & LCD_DISPLAY_ROTATE_EN)
	{
		if(width)
			*width  = lcd_show_ctrl.p_lcddev->screen_h;
		if(height)
			*height = lcd_show_ctrl.p_lcddev->screen_w;
		return 0;
	}
	if(width)
		*width  = lcd_show_ctrl.p_lcddev->screen_w;
	if(height)
		*height = lcd_show_ctrl.p_lcddev->screen_h;

	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdGetUiResolution
* Description    : hardware layer ,get lcd ui showing resolution
* Input          : u16 *width : width
                   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetUiResolution(u16 *width,u16 *height)
{

	if(lcd_show_ctrl.p_lcddev == NULL)
		return -1;
	if(width)
		*width = lcd_show_ctrl.ui_w;
	if(height)
		*height = lcd_show_ctrl.ui_h;

	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdGetUiPosition
* Description    : hardware layer ,get lcd ui showing position
* Input          : u16 *x : y
                  u16 *x : y
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetUiPosition(u16 *x,u16 *y)
{

	if(lcd_show_ctrl.p_lcddev == NULL)
		return -1;
	if(x)
		*x = lcd_show_ctrl.ui_x;
	if(y)
		*y = lcd_show_ctrl.ui_y;

	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdUiScanModeGet
* Description    : hardware layer ,get lcd ui showing rotate degree
* Input          : none
* Output         : None
* Return         : int :
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,

                          <0: fail
*******************************************************************************/
s32 hal_lcdUiScanModeGet(void)
{
	if(lcd_show_ctrl.p_lcddev == NULL)
		return -1;

	return lcd_show_ctrl.ui_scan_mode;
}
/*******************************************************************************
* Function Name  : hal_lcdGetVideoRatioResolution
* Description    : hardware layer ,get lcd showing ratio resolution
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetVideoRatioResolution(u16 *width,u16 *height)
{
	if(lcd_show_ctrl.p_lcddev == NULL)
		return -1;
	if(width)
		*width = lcd_show_ctrl.ratio_w;
	if(height)
		*height = lcd_show_ctrl.ratio_h;

	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdSetVideoRatioResolution
* Description    : hardware layer ,set lcd showing ratio resolution
* Input          : u16 width : width
				   u16 height : height
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcdSetVideoRatioResolution(u16 width,u16 height)
{
	if(lcd_show_ctrl.p_lcddev == NULL)
		return;
    lcd_show_ctrl.ratio_w = width;
    lcd_show_ctrl.ratio_h = height;
}
/*******************************************************************************
* Function Name  : hal_lcdGetVideoRatioPos
* Description    : hardware layer ,get lcd showing position
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetVideoRatioPos(u16 *x,u16 *y)
{

	if(lcd_show_ctrl.p_lcddev == NULL)
		return -1;
	if(x)
		*x = lcd_show_ctrl.ratio_x;
	if(y)
		*y = lcd_show_ctrl.ratio_y;

	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdGetVideoResolution
* Description    : hardware layer ,get lcd showing resolution
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetVideoResolution(u16 *width,u16 *height)
{

	if(lcd_show_ctrl.p_lcddev == NULL)
		return -1;
	if(width)
		*width = lcd_show_ctrl.video_w;
	if(height)
		*height = lcd_show_ctrl.video_h;

	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdGetVideoPos
* Description    : hardware layer ,get lcd showing position
* Input          : u16 *width : width
				   u16 *height : height
* Output         : None
* Return         : int 0: success
                          <0: fail
*******************************************************************************/
s32 hal_lcdGetVideoPos(u16 *x,u16 *y)
{

	if(lcd_show_ctrl.p_lcddev == NULL)
		return -1;
	if(x)
		*x = lcd_show_ctrl.video_x;
	if(y)
		*y = lcd_show_ctrl.video_y;

	return 0;
}
/*******************************************************************************
* Function Name  : hal_lcdUiScanModeGet
* Description    : hardware layer ,get lcd ui showing rotate degree
* Input          : none
* Output         : None
* Return         : int :
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,

                          <0: fail
*******************************************************************************/
s32 hal_lcdVideoScanModeGet(void)
{
	if(lcd_show_ctrl.p_lcddev == NULL)
		return -1;

	return lcd_show_ctrl.video_scan_mode;
}
/*******************************************************************************
* Function Name  : hal_lcdEncodeDoneManual
* Description    : hal layer .mjpeg manual encode done callback
* Input          : None
* Output         : None
* Return         : None
*******************************************************************************/
static void hal_lcdEncodeDoneManual(int flag)
{
	u32 len;
	//deg_Printf("D");
	if(flag&((1<<MJPEG_IRQ_OUTPAUSE)|(1<<MJPEG_IRQ_FRAMEEND)))
	{
		len = hx330x_mjpA_EncodeLoadAddrGet()- lcd_show_ctrl.lcd_mjpbuf;
		lcd_show_ctrl.lcd_mjpoutsize = (len+0x1ff)&(~0x1ff);

		if(hal_mjpA_EncState(flag))
		{
			lcd_show_ctrl.lcd_enc_stat = VIDEOSHOW_ENC_DONE;
			hx330x_mjpA_EncodeEnable(0);
            hx330x_mjpA_EncodeISRRegister(NULL);
            return;
		}
	}
    //运行到这里表示enc有错误，重新启动enc
    lcd_show_ctrl.lcd_enc_stat = VIDEOSHOW_ENC_START;
    return;

}
/*******************************************************************************
* Function Name  : hal_lcd_enc_kick
* Description    : hal_lcd_enc_kick
* Input          : lcdshow_frame_t * frame
* Output         : None
* Return         :
*******************************************************************************/
SDRAM_TEXT_SECTION
int hal_lcd_enc_kick(lcdshow_frame_t * frame)
{
    //没有启动抓屏,或者ENCODE结束，可以更新下一帧
    if(lcd_show_ctrl.lcd_enc_stat == VIDEOSHOW_ENC_NONE || lcd_show_ctrl.lcd_enc_stat == VIDEOSHOW_ENC_DONE)
        return 0;
    else if(lcd_show_ctrl.lcd_enc_stat == VIDEOSHOW_ENC_START) //启动抓屏,同时更新屏显
    {
        lcd_show_ctrl.lcd_mjp_width = frame->destw;
        lcd_show_ctrl.lcd_mjp_height = frame->desth;
        hx330x_mjpA_EncodeInit(1,0);
        hx330x_mjpA_Encode_inlinebuf_init((u32)frame->_y_addr,(u32)frame->_uv_addr);
        hx330x_mjpA_EncodeSizeSet(frame->ratio_w,frame->ratio_h, lcd_show_ctrl.lcd_mjp_width, lcd_show_ctrl.lcd_mjp_height);
        hx330x_mjpA_EncodeQuilitySet(lcd_show_ctrl.lcd_enc_qulity);
        hx330x_mjpA_EncodeInfoSet(0);
        hal_jpg_watermarkStart(frame->destw, frame->desth,lcd_show_ctrl.lcd_enc_timestamp);
        hx330x_mjpA_EncodeBufferSet(lcd_show_ctrl.lcd_mjpbuf,lcd_show_ctrl.lcd_mjpbuf+lcd_show_ctrl.lcd_mjpsize);
		hx330x_mjpA_Encode_manual_on();
        hx330x_mjpA_EncodeISRRegister(hal_lcdEncodeDoneManual);
		hx330x_intEnable(IRQ_JPGA,1); // enable jpegirq
        lcd_show_ctrl.lcd_enc_stat = VIDEOSHOW_ENC_PROCESS;
        return 0;
    }else if(lcd_show_ctrl.lcd_enc_stat == VIDEOSHOW_ENC_PROCESS)
    {
        deg_Printf("[LCD ENC] drop show frame\n");
        return -1;
    }
    return 0;

}
/*******************************************************************************
* Function Name  : hal_lcd_enc_start
* Description    : hal_lcd_enc_start
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
int hal_lcd_enc_start(void)
{
    if(lcd_show_ctrl.lcd_enc_stat != VIDEOSHOW_ENC_NONE)
        return -1;
    lcd_show_ctrl.lcd_mjpsize = 100*1024L;
    lcd_show_ctrl.lcd_mjpbuf = (u32)hal_sysMemMalloc(lcd_show_ctrl.lcd_mjpsize);
    if(lcd_show_ctrl.lcd_mjpbuf == 0)
    {
        deg_Printf("[LCD ENC] malloc buf fail\n");
        return -2;
    }
    lcd_show_ctrl.lcd_mjpoutsize = 0;
    lcd_show_ctrl.lcd_enc_type = 0;
    lcd_show_ctrl.lcd_enc_qulity = JPEG_Q_75;
    lcd_show_ctrl.lcd_enc_timestamp = 0;
    lcd_show_ctrl.lcd_enc_stat = VIDEOSHOW_ENC_START;
    return 0;
}
/*******************************************************************************
* Function Name  : hal_lcd_enc_stop
* Description    : hal_lcd_enc_stop
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_enc_stop(void)
{
  	lcd_show_ctrl.lcd_enc_stat = VIDEOSHOW_ENC_NONE;
	hx330x_mjpA_EncodeEnable(0);
    hx330x_mjpA_EncodeISRRegister(NULL);
    hal_jpg_watermarkStart(0,0,0);
    if(lcd_show_ctrl.lcd_mjpbuf)
    {
        hal_sysMemFree((void*)lcd_show_ctrl.lcd_mjpbuf);
        lcd_show_ctrl.lcd_mjpbuf = 0;
    }
}
/*******************************************************************************
* Function Name  : hal_lcd_enc_checkdone
* Description    : hal_lcd_enc_checkdone
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
bool hal_lcd_enc_checkdone(void)
{
    if(lcd_show_ctrl.lcd_enc_stat == VIDEOSHOW_ENC_DONE)
        return true;
    else
        return false;
}
/*******************************************************************************
* Function Name  : hal_lcd_enc_frame_get
* Description    : hal_lcd_enc_frame_get
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u8* hal_lcd_enc_frame_get(u32 *len)
{
    if(lcd_show_ctrl.lcd_enc_stat != VIDEOSHOW_ENC_DONE)
    {
        return NULL;
    }
    if(len)
        *len = lcd_show_ctrl.lcd_mjpoutsize;
    return (u8*)lcd_show_ctrl.lcd_mjpbuf;
}
/*******************************************************************************
* Function Name  : hal_lcd_enc_frame_res_get
* Description    : hal_lcd_enc_frame_res_get
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_enc_frame_res_get(u16 *w, u16 * h)
{
    if(w)
        *w = lcd_show_ctrl.lcd_mjp_width;
    if(h)
        *h = lcd_show_ctrl.lcd_mjp_height;
}
/*******************************************************************************
* Function Name  : hal_lcd_pause_set
* Description    : hal_lcd_pause_set
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
void hal_lcd_pause_set(u8 en)
{
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
    lcd_show_ctrl.lcd_pause_en = en;
    HAL_CRITICAL_EXIT();
}
/*******************************************************************************
* Function Name  : hal_lcd_pause_sta_get
* Description    : hal_lcd_pause_sta_get
* Input          :
* Output         : None
* Return         : None
*******************************************************************************/
u8 hal_lcd_pause_sta_get(void)
{
    u8 sta;
    HAL_CRITICAL_INIT();
    HAL_CRITICAL_ENTER();
    sta = lcd_show_ctrl.lcd_pause_en;
    HAL_CRITICAL_EXIT();
    return sta;
}
/*******************************************************************************
* Function Name  : hal_lcdSetGamma
* Description    :
* Input          : lcddev_t *p_lcddev : lcd op node
* Output         : none
* Return         : none
*******************************************************************************/
extern int _lcd_res_size;
extern int _lcd_res_lma;
#define LCD_RES_FLASH_ADDR(x)       ((u32)(x) + ((u32)&_lcd_res_lma))
extern u32 lcd_gamma[12][128];
extern u8 lcd_contra_tab[13][256];
void hal_lcdSetGamma(lcddev_t * desc)
{
	const u32 contra_len = sizeof(lcd_contra_tab[0]);
	const u32 gamma_len = sizeof(lcd_gamma[0]);

    u32 i = 0;
	u8 * buffer = hal_sysMemMalloc(contra_len + 3 * gamma_len);
	u8 * contra = buffer;
	u8 * gamma = contra + contra_len;

    // load contra tab
    if (desc->contra_index < ARRAY_NUM(lcd_contra_tab))
    {
        hal_spiFlashRead(LCD_RES_FLASH_ADDR(&lcd_contra_tab) + desc->contra_index * contra_len,
                        (u32)contra,
                        contra_len);
    }

    // load RGB gamma tab
    for (i = 0; i < 3; i++)
    {
        if (desc->gamma_index[i] < ARRAY_NUM(lcd_gamma))
        {
            hal_spiFlashRead(LCD_RES_FLASH_ADDR(&lcd_gamma) + desc->gamma_index[i] * gamma_len,
                            (u32)(gamma + i * gamma_len),
                            gamma_len);
        }
    }
	for (i = 0; i < gamma_len * 3; i++)
	{
		s32 temp;
		temp = gamma[i];
		temp = temp + (-(s32)desc->brightness) * (temp + 1) / 256;
		temp = hx330x_clip(temp, 0, 255);
		gamma[i] = contra[temp];
	}

    hx330x_lcdVideoSetGAMA((u32)&gamma[0], (u32)&gamma[gamma_len], (u32)&gamma[2 * gamma_len]);
    hal_sysMemFree(buffer);
}
