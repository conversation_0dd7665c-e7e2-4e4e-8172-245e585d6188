MEMORY
        {
            boot     : ORIGIN = 0x01FFFC00, LENGTH = 0x0000200
            ram_boot : ORIGIN = 0x00000000, LENGTH = 0x06c00
            ram_user : ORIGIN = 0x00000000, LENGTH = 28K
            usb_ram  : ORIGIN = 0x00008000, LENGTH = 32K
	        line_ram : ORIGIN = 0x00200000, LENGTH = 64K
			mp3_text  : ORIGIN = 0x00008000, LENGTH = 32K
			mp3_ram	:  ORIGIN = 0x00200000, LENGTH = 64K
			nes_text  : ORIGIN = 0x00008000, LENGTH = 32K
			nes_ram	:  ORIGIN = 0x00200000, LENGTH = 64K
            sdram    : ORIGIN = 0x02000000, LENGTH = 8M
			flash    : ORIGIN = 0x06000000, LENGTH = 8M
            exsdram  : ORIGIN = 0x00000000, LENGTH = 8M
        }


SECTIONS
{
    __sdram_size = boot_sdram_size == 1 ? (8*1024*1024) : (2*1024*1024);
        /* bootsec */
        .bootsec : AT(0)
        {
            KEEP(*(.bootsec))
        } > boot

        .boot_code : AT((LOADADDR(.bootsec) + SIZEOF(.bootsec) + 0x1FF) & 0xfffffe00)
        {
_boot_vma = .;
            *(.vector)
			*(.vector.kepttext)
			*(.vector.keptdata)
			. = ALIGN (4);
_boot_kept_vma = .;
            *(.vector.text)
            *(.vector.data)
        } > ram_boot

        /* RAM AREA2: mask variable use + code use + buf use*/
        .ram (NOLOAD):
        {
			. = _boot_kept_vma;
__sram_start = .;
            /* buff if need run in sram */
			*(.sram_usb11fifo)
            *(.sram_comm)
        } > ram_user
__sram_end = .;

        .usb_ram (NOLOAD):
        {
__ufifo_start = .;
            *(.uram_usb20fifo)
            *(.uram_comm)
        } > usb_ram
__ufifo_end = .;

        .line_ram (NOLOAD): 
        {
__line_start = .;
            *(.lram_comm)
        } > line_ram
__line_end = .;

		.on_sdram : AT((LOADADDR(.boot_code) + SIZEOF(.boot_code) + 0x1FF) & 0xfffffe00)
        {
_onsdram_start = .;
            *(.sdram_text)
			*(.sdram_code)
            *(.sdram_data)
               			
            *(.data*)
        } > sdram
		
        /* bss in sdram */
        .bss ALIGN(4) (NOLOAD):  
        {
__bss_start = .;
            *(.bss*)
            *(COMMON)
            *(.big_buffer*)
            *(._sdram_buf_)
			
        } > sdram	
_sdram_remian_addr = ALIGN(64) + 0;		
		
        /* user code in flash */
		_text_lma = ((LOADADDR(.on_sdram) + SIZEOF(.on_sdram) + 0x1FF) & 0xfffffe00);
		_text_vma = ORIGIN(flash) + _text_lma;
        .text _text_vma : AT(_text_lma)
        {
            *(.text*)
            *(.rodata*) 
        } > flash


        .lcd_resource 0x00000000 : AT((LOADADDR(.text) + SIZEOF(.text) + 0x1FF) & 0xfffffe00)
		{
			*(.lcd_res.struct)
			*(.lcd_res*)
		} > exsdram
		.sensor_resource 0x00000000 : AT(LOADADDR(.lcd_resource) + SIZEOF(.lcd_resource)){
			*(.sensor_res.header)

			_res_sensor_header_item_start = .;
			*(.sensor_res.header.items)
			_res_sensor_header_item_end = .;

			*(.sensor_res.isp_tab)

			*(.sensor_res.struct)

			*(.sensor_res.init_tab)

		}> exsdram
		.nes_resource 0x00000000 : AT((LOADADDR(.sensor_resource) + SIZEOF(.sensor_resource) + 0x1FF) & 0xfffffe00){
			*(.nes_games_tab)
		}> exsdram       

        .eh_frame (NOLOAD):
        {
            *(.eh_frame)
        } > exsdram
		/* user code and ram overlay in line_ram */
		.mp3_text : AT((LOADADDR(.nes_resource) + SIZEOF(.nes_resource) + 0x1FF) & 0xfffffe00)
        {
__mp3_text_start = .;
            *(.mp3_text)
			. = ALIGN (4);
        } > mp3_text		
        .mp3_code : AT((LOADADDR(.mp3_text) + SIZEOF(.mp3_text) + 0x1FF) & 0xfffffe00)
        {
__mp3_code_start = .;
            *(.mp3_code)
			. = ALIGN (4);
        } > mp3_ram	
        .mp3_data (NOLOAD): 
        {
            *(.mp3_data)
        } > mp3_ram
	
		
        .nes_code : AT((LOADADDR(.mp3_code) + SIZEOF(.mp3_code) + 0x1FF) & 0xfffffe00)
        {
__nes_code_start = .;
            *(.nes_code)
			. = ALIGN (4);
        } > nes_ram	
        .nes_data (NOLOAD): 
        {
__nes_data_start = .;
            *(.nes_data)
__nes_data_end = .;
        } > nes_ram	
		.nes_com_text : AT((LOADADDR(.nes_code) + SIZEOF(.nes_code) + 0x1FF) & 0xfffffe00)
        {
__nes_text_start = .;
            *(.nes_com_text)
			. = ALIGN (4);
        } > nes_text
		/* user code and ram overlay in usb_ram */

        
        _boot_code_len = ((SIZEOF(.boot_code) + 511) / 512);
        _boot_code_sec = (LOADADDR(.boot_code) / 512);
        
        /*start lba address for boot_loader upload in SPI-FLASH */    
		_text_start = _onsdram_start;
        _text_sec = (LOADADDR(.on_sdram) / 512);
        
        /*code lba length */
        _text_len = ((SIZEOF(.on_sdram) + 0x1ff) / 512);
        

        ASSERT( ((_sdram_remian_addr -  ORIGIN(sdram))  <  __sdram_size),"No memroy for sdram")
        __sdram_remain_size = ( (ORIGIN(sdram) + __sdram_size) - _sdram_remian_addr); /*- __stack_size;*/

        __stack_size     = 4096 + 1024;
        ASSERT(((ORIGIN(ram_user) + LENGTH(ram_user) - __sram_end) >= __stack_size),"No memroy for stack")

        __bss_end = _sdram_remian_addr + __sdram_remain_size;

		__mp3_text_len = (SIZEOF(.mp3_text));
		__mp3_text_addr = (LOADADDR(.mp3_text));
		
		__mp3_code_len = (SIZEOF(.mp3_code));
		__mp3_code_addr = (LOADADDR(.mp3_code));

		__nes_text_len = (SIZEOF(.nes_com_text));
		__nes_text_addr = (LOADADDR(.nes_com_text));
		
		__nes_code_len = (SIZEOF(.nes_code));
		__nes_code_addr = (LOADADDR(.nes_code));

		/*addr of resource in spiflash,uint:byte*/
		_lcd_res_lma = LOADADDR(.lcd_resource);
		_lcd_res_size = SIZEOF(.lcd_resource);


		_sensor_resource_start_addr = LOADADDR(.sensor_resource);
		_res_sensor_header_len = _res_sensor_header_item_end - _res_sensor_header_item_start;

        _nes_res_lma = LOADADDR(.nes_resource);
        

}
    /*PROVIDE(__stack = 0xfd000 - 4); if d-cache is not use*/
    /*PROVIDE(__stack = 0x2000 - 4);if d-cache use*/
    /*PROVIDE(__stack = ORIGIN(sdram) + LENGTH(sdram) - 4) stack in sdram */;
    PROVIDE(__stack = ORIGIN(ram_user) + LENGTH(ram_user) - 4)/*stack in sram */;
