/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_DMA_H
    #define HX330X_DMA_H
	
	
typedef enum
{
    DMA_DMAUART = 0,    //0
    DMA_JPGB_DRI,      //1
    DMA_JPGA_UVC_DRI,  //2
    DMA_ISP_WR_GRAM,   //3
    DMA_AUDAC,         //4
    DMA_AUADC,         //5
    DMA_MEMCPY0,       //6
    DMA_MEMCPY1,       //7
    DMA_MEMCPY2,       //8
    DMA_MEMCPY3,       //9
    DMA_SPI0,          //10
    DMA_SPI1,          //11
    DMA_SD0,           //12
    DMA_SD1,           //13
    DMA_JPGA_TIMEINFO, //14
    DMA_JPGB_TIMEINFO, //15
    DMA_ICACHE,        //16
    DMA_DCACHE,        //17
    DMA_ISP_RD_DRAM,   //18
    DMA_ISP2JPEG_WIFI, //19
    DMA_JPGA_ENC_WR,   //20
    DMA_JPGA_RD,       //21
    DMA_ISP_JDMA,      //22
    DMA_JPGB_ENC_WR,   //23
    DMA_JPGB_DEC_WR,   //24
    DMA_JPGB_RD,       //25
    DMA_ISP_LDMA,      //26
    DMA_DE,            //27
    DMA_UILZO_WR,     //28
    DMA_UILZO_RD,     //29
    DMA_ROTATE_WR,     //30
    DMA_ROTATE_RD,     //31
	DMA_USB20_RW,     //32
	DMA_USB11_RW,     //33
	
}DMA_CH_E;
#if 1
typedef struct noc_cfg_s {
//channel 12 DMA_SD0	
u32  NOC_SDC0_QOS_STEP;
u32  NOC_SDC0_LATENCY;
//channel 13 DMA_SD1
u32  NOC_SDC1_QOS_STEP;
u32  NOC_SDC1_LATENCY;
//channel 14 DMA_JPGA_TIMEINFO
u32  NOC_JPGA_TIMEINFO_QOS_STEP;
u32  NOC_JPGA_TIMEINFO_LATENCY;
//channel 15 DMA_JPGB_TIMEINFO
u32  NOC_JPGB_TIMEINFO_QOS_STEP;
u32  NOC_JPGB_TIMEINFO_LATENCY;
//channel 16 DMA_ICACHE	
u32  NOC_ICACHE_QOS_STEP;
u32  NOC_ICACHE_LATENCY;
//channel 17 DMA_DCACHE	
u32  NOC_DCACHE_QOS_STEP;
u32  NOC_DCACHE_LATENCY;
//channel 18 DMA_ISP_RD_DRAM	
u32  NOC_ISP_RD_DRAM_QOS_STEP;
u32  NOC_ISP_RD_DRAM_LATENCY;
//channel 19 DMA_ISP2JPEG_WIFI
u32  NOC_ISP_WR2WIFI_QOS_STEP;
u32  NOC_ISP_WR2WIFI_LATENCY;
//channel 20 DMA_JPGA_ENC_WR
u32  NOC_PRE_CODEC_WR2CARD_QOS_STEP;
u32  NOC_PRE_CODEC_WR2CARD_LATENCY;
//channel 21 DMA_JPGA_RD
u32  NOC_PRE_CODEC_RD_QOS_STEP;
u32  NOC_PRE_CODEC_RD_LATENCY;
//channel 22 DMA_ISP_JDMA
u32  NOC_ISP_WR2CARD_QOS_STEP;
u32  NOC_ISP_WR2CARD_LATENCY;
//channel 23 DMA_JPGB_ENC_WR
u32  NOC_BACK_CODEC_WR2CARD_QOS_STEP;
u32  NOC_BACK_CODEC_WR2CARD_LATENCY;
//channel 24 DMA_JPGB_DEC_WR
u32  NOC_BACK_CODEC_WR2LCD_QOS_STEP;
u32  NOC_BACK_CODEC_WR2LCD_LATENCY;
//channel 25 DMA_JPGB_RD
u32  NOC_BACK_CODEC_RD_QOS_STEP;
u32  NOC_BACK_CODEC_RD_LATENCY;
//channel 26 DMA_ISP_LDMA
u32  NOC_ISP_WR2LCD_QOS_STEP;
u32  NOC_ISP_WR2LCD_LATENCY;
//channel 27 DMA_DE
u32  NOC_DE_QOS_STEP;
u32  NOC_DE_LATENCY;
//channel 28 DMA_UILZO_WR
u32  NOC_UILZO_WR_QOS_STEP;
u32  NOC_UILZO_WR_LATENCY;
//channel 29 DMA_UILZO_RD
u32  NOC_UILZO_RD_QOS_STEP;
u32  NOC_UILZO_RD_LATENCY;
//channel 30 DMA_ROTATE_WR
u32  NOC_ROTATE_WR_QOS_STEP;
u32  NOC_ROTATE_WR_LATENCY;
//channel 31 DMA_ROTATE_RD
u32  NOC_ROTATE_RD_QOS_STEP;
u32  NOC_ROTATE_RD_LATENCY;
//channel 32 DMA_USB20_RW
u32  NOC_USB20_WR_DRAM_QOS_STEP;
u32  NOC_USB20_WR_DRAM_LATENCY;
//channel 33 DMA_USB11_RW	
////USB11_RW_DRAM
u32  NOC_USB11_WR_DRAM_QOS_STEP;
u32  NOC_USB11_WR_DRAM_LATENCY;
} noc_cfg_t;
#else
typedef struct noc_cfg_s {
////SDC0
u32  NOC_SDC0_QOS_STEP;
u32  NOC_SDC0_LATENCY;
////DE
u32  NOC_DE_QOS_STEP;
u32  NOC_DE_LATENCY;
////back jpeg codec write to card
u32  NOC_BACK_CODEC_WR2CARD_QOS_STEP;
u32  NOC_BACK_CODEC_WR2CARD_LATENCY;
////back jpeg codec read
u32  NOC_BACK_CODEC_RD_QOS_STEP;
u32  NOC_BACK_CODEC_RD_LATENCY;
////back jpeg codec write to lcd
u32  NOC_BACK_CODEC_WR2LCD_QOS_STEP;
u32  NOC_BACK_CODEC_WR2LCD_LATENCY;
////pre jpeg codec write to card
u32  NOC_PRE_CODEC_WR2CARD_QOS_STEP;
u32  NOC_PRE_CODEC_WR2CARD_LATENCY;
////pre jpeg codec read
u32  NOC_PRE_CODEC_RD_QOS_STEP;
u32  NOC_PRE_CODEC_RD_LATENCY;
////ISP write to card
u32  NOC_ISP_WR2CARD_QOS_STEP;
u32  NOC_ISP_WR2CARD_LATENCY;
////ISP write to WIFI
u32  NOC_ISP_WR2WIFI_QOS_STEP;
u32  NOC_ISP_WR2WIFI_LATENCY;
////ISP write to LCD
u32  NOC_ISP_WR2LCD_QOS_STEP;
u32  NOC_ISP_WR2LCD_LATENCY;
////DCACHE
u32  NOC_DCACHE_QOS_STEP;
u32  NOC_DCACHE_LATENCY;
////ICACHE
u32  NOC_ICACHE_QOS_STEP;
u32  NOC_ICACHE_LATENCY;
} noc_cfg_t;
#endif

/*******************************************************************************
* Function Name  : hx330x_dmaChannelEnable
* Description    : dma channel enable
* Input          : u8 channel : dma channel 
*                  u8 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_dmaNocWinA(void);
/*******************************************************************************
* Function Name  : hx330x_dmaChannelEnable
* Description    : dma channel enable
* Input          : u8 channel : dma channel 
*                  u8 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_dmaNocWinB(void);
/*******************************************************************************
* Function Name  : hx330x_dmaChannelEnable
* Description    : dma channel enable
* Input          : u8 channel : dma channel 
*                  u8 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_dmaNocWinDis(void);
/*******************************************************************************
* Function Name  : hx330x_dmaNocCfg
* Description    : 
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_dmaNocCfg(noc_cfg_t * p_cfg);
/*******************************************************************************
* Function Name  : hx330x_dmaChannelEnable
* Description    : dma channel enable
* Input          : u8 channel : dma channel 
*                  u8 en : 1-enable,0-disable
* Output         : none
* Return         : none
*******************************************************************************/
void hx330x_dmaChannelEnable(u8 channel,u8 en);


#endif
