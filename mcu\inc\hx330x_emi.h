#ifndef HX330X_EMI_H
#define HX330X_EMI_H

enum {
    EMI_MASTER = 0,
    EMI_SLAVE  = 1,
};


/*******************************************************************************
* Function Name  : hx330x_emiPinConfig
* Description    : emi init
* Input          : u8 mode : select MASTER/SLAVE
*                  u32 baud: baud rate
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_emiInit(u8 mode,u32 baud);
/*******************************************************************************
* Function Name  : hx330x_emiISRRegister
* Description    : register irq service
* Input          : void (*isr)(u8 header)
*                  header for rx
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_emiISRRegister(void (*isr)(u32 header));
/*******************************************************************************
* Function Name  : hx330x_emiIRQHandler
* Description    : emi irs
* Input          : None
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_emiIRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_emiKick
* Description    : if emi is master,this will kick tx
*                  if emi is slave,this will kick rx
* Input          : u8 header : only for tx,4bit header will send to slave
*                  u8 * buffer : src buffer for tx,dest buffer for rx
*                  u32 cnt : for tx,means how many bytes to tx
*                            for rx,means how many bytes to rx
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_emiKick(u32 header,u8 * buffer,u32 cnt);
/*******************************************************************************
* Function Name  : hx330x_emiCheckBusy
* Description    : check emi is busy or not
* Input          : None
* Output         : None
* Return         : true: busy, false: idle
*******************************************************************************/
bool hx330x_emiCheckBusy(void);
/*******************************************************************************
* Function Name  : hx330x_emiCheckRXError
* Description    : check emi rx is error or not
* Input          : None
* Output         : None
* Return         : true : rx error, false: rx ok
*******************************************************************************/
bool hx330x_emiCheckRXError(void);
#endif
