/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  HX330X_GPIO_H
   #define HX330X_GPIO_H

typedef enum
{
    GPIO_PA=0,
    GPIO_PB,
    GPIO_PD,
    GPIO_PE,
    GPIO_PF,
    GPIO_PG,
    GPIO_PH,
    GPIO_PJ,
    GPIO_CH_MAX
}GPIO_CH_E;

enum pin_num_e
{
    GPIO_PIN0  = 0x00000001,
    GPIO_PIN1  = 0x00000002,
    GPIO_PIN2  = 0x00000004,
    GPIO_PIN3  = 0x00000008,
    GPIO_PIN4  = 0x00000010,
    GPIO_PIN5  = 0x00000020,
    GPIO_PIN6  = 0x00000040,
    GPIO_PIN7  = 0x00000080,
    GPIO_PIN8  = 0x00000100,
    GPIO_PIN9  = 0x00000200,
    GPIO_PIN10 = 0x00000400,
    GPIO_PIN11 = 0x00000800,
    GPIO_PIN12 = 0x00001000,
    GPIO_PIN13 = 0x00002000,
    GPIO_PIN14 = 0x00004000,
    GPIO_PIN15 = 0x00008000,
};

enum pin_e
{
	GPIO_PA0 = 0x00, GPIO_PA1, GPIO_PA2, GPIO_PA3, GPIO_PA4, GPIO_PA5, GPIO_PA6, GPIO_PA7,
	GPIO_PA8, GPIO_PA9, GPIO_PA10, GPIO_PA11, GPIO_PA12, GPIO_PA13, GPIO_PA14, GPIO_PA15,

	GPIO_PD0 = 0x20, GPIO_PD1, GPIO_PD2, GPIO_PD3, GPIO_PD4, GPIO_PD5, GPIO_PD6, GPIO_PD7,
	GPIO_PD8, GPIO_PD9, GPIO_PD10, GPIO_PD11, GPIO_PD12, GPIO_PD13, GPIO_PD14, GPIO_PD15,

	GPIO_PE0 = 0x30, GPIO_PE1, GPIO_PE2, GPIO_PE3, GPIO_PE4, GPIO_PE5, GPIO_PE6, GPIO_PE7,
	GPIO_PE8, GPIO_PE9, GPIO_PE10, GPIO_PE11, GPIO_PE12, GPIO_PE13, GPIO_PE14, GPIO_PE15,

	GPIO_PF0 = 0x40, GPIO_PF1, GPIO_PF2, GPIO_PF3, GPIO_PF4, GPIO_PF5, GPIO_PF6, GPIO_PF7,
	GPIO_PF8, GPIO_PF9, GPIO_PF10, GPIO_PF11, GPIO_PF12, GPIO_PF13, GPIO_PF14, GPIO_PF15,
};

typedef enum
{
    GPIO_OUTPUT=0,
    GPIO_INPUT  
}GPIO_DIR_E;

typedef enum
{
    GPIO_PULL_FLOATING=0,
    GPIO_PULL_UP,
    GPIO_PULL_DOWN,
    GPIO_PULL_UPDOWN,

    GPIO_PULL_MAX
}GPIO_PULL_E;

typedef enum
{
	GPIO_PULLE_FLOATING=0,
	GPIO_PULLE_UP,
	GPIO_PULLE_DOWN,
	GPIO_PULLE_UPDOWN
}GPIO_PULLE_E;

typedef enum
{
	GPIO_LOW=0,
	GPIO_HIGH
}GPIO_DATA_E;

typedef enum
{
	GPIO_DRV_NORMAL=0,
	GPIO_DRV_HIGH
}GPIO_DRV_E;

typedef enum
{
	GPIO_FUNC_GPIO=0,
	GPIO_FUNC_SFR
}GPIO_FUNC_E;

typedef enum
{
	GPIO_DIGITAL_DIS=0,
	GPIO_DIGITAL_EN
}GPIO_DIGITAL_E;

typedef enum
{
	GPIO_MAP_UARTTX1=0,
	GPIO_MAP_UARTRX1,
	GPIO_MAP_UARTTX0,
	GPIO_MAP_UARTRX0,
	GPIO_MAP_SPI1,
	GPIO_MAP_SPI0,
	GPIO_MAP_SD1,
	GPIO_MAP_SD0,
	GPIO_MAP_TMR3,
	GPIO_MAP_TMR2,
	GPIO_MAP_TMR1,
	GPIO_MAP_TMR0,
	GPIO_MAP_DLL,
	GPIO_MAP_XOSC32K,
	GPIO_MAP_EMI,
	GPIO_MAP_CSI,
	GPIO_MAP_LCD,
	GPIO_MAP_IIC1,
	GPIO_MAP_IIC0
}GPIO_MAP_E;

typedef enum
{
	GPIO_LED0_PA2 = 0,
	GPIO_LED1_PA3,
	GPIO_LED2_PA5,
	GPIO_LED3_PA9,
	GPIO_LED4_PA15,
	GPIO_LED5_PF10,
	GPIO_LED6_PF11,
	GPIO_LED7_PD0,
	GPIO_LED8_PD2,
	GPIO_LED9_PD3,
	GPIO_LED10_PD10,
	GPIO_LED11_PF0,
	GPIO_LED12_PF1,
	GPIO_LED13_PF2,
	GPIO_LED14_PF7,
	GPIO_LED15_PF8,
	GPIO_LED16_PF9,
	GPIO_LED17_PA13,
}GPIO_LED_E;

typedef enum
{
	GPIO_INT0_PA2 = 0,
	GPIO_INT1_PA3,
	GPIO_INT2_PA4,
	GPIO_INT3_PA5,
	GPIO_INT4_PA7,
	GPIO_INT5_PA9,
	GPIO_INT6_PA14,
	GPIO_INT6_PA15,
	GPIO_INT8_PF14,
	GPIO_INT9_PF10,
	GPIO_INT10_PF11,
	GPIO_INT11_PD3,
	GPIO_INT12_PD4,
	GPIO_INT13_PD8,
	GPIO_INT14_PD10,
	GPIO_INT15_PD12,
	GPIO_INT16_PD13,
	GPIO_INT17_PF0,
	GPIO_INT18_PF4,
	GPIO_INT19_PF8,
    GPIO_INT20_PF9,
    GPIO_INT21_PA12,
    GPIO_INT22_PA13,
    GPIO_INT23_PE0,
    GPIO_INT24_PE2,
    GPIO_INT25_PE4,
    GPIO_INT26_PE15,
    GPIO_INT_MAX
}GPIO_INT_E;

typedef enum
{
	UART0_POS_NONE = 0,
	UART0_POS_PA0,  //1
	UART0_POS_PA1,  //2
	UART0_POS_PA4,  //3
	UART0_POS_PA5,  //4
	UART0_POS_PA6,  //5
	UART0_POS_PA7,  //6
	UART0_POS_PA9,  //7
	//UART0_POS_PA9,  //8
	UART0_POS_PA14 = 9, //9
	UART0_POS_PA15,  //10
	UART0_POS_PF10,  //11
	UART0_POS_PF11,  //12
	UART0_POS_PD1,  //13
	UART0_POS_PD2,  //14
	UART0_POS_PD3,  //15
	UART0_POS_PD7,  //16
	UART0_POS_PD10,  //17
	UART0_POS_PF0, //18
	UART0_POS_PF2,  //19
	UART0_POS_PF8,  //20
	UART0_POS_PA12,  //21
	UART0_POS_PA13, //22
	UART0_POS_PE0, //23
	UART0_POS_PE1,  //24
	UART0_POS_PE2,  //25
	UART0_POS_PE3,  //26
	UART0_POS_MAX
}UART0_POS_E;

typedef enum  //GROUP7 空
{
	UART1_POS_NONE = 0,
	UART1_POS_PA4,  //1
	UART1_POS_PA5,  //2
	UART1_POS_PA6,  //3
	UART1_POS_PA7,  //4
	UART1_POS_PA9,  //5
	UART1_POS_PA10, //6  
	UART1_POS_PA14 = 8, //8
	UART1_POS_PF14,  //9
	UART1_POS_PF10,  //10
	UART1_POS_PF11,  //11
	UART1_POS_PD0,  //12
	UART1_POS_PD8,  //13
	UART1_POS_PD9,  //14
	UART1_POS_PD10, //15
	UART1_POS_PF0,  //16
	UART1_POS_PF1,  //17
	UART1_POS_PF8,  //18
	UART1_POS_PA12, //19
	UART1_POS_PA13, //20
	UART1_POS_PE0,  //21
	UART1_POS_PE1,  //22
	UART1_POS_PE2,  //23
	UART1_POS_PE3,  //24
	UART1_POS_PE15, //25
	UART1_POS_MAX
}UART1_POS_E;

typedef enum
{
	TRIGGER_LEVEL_HIGH=0,
	TRIGGER_LEVEL_LOW,
	TRIGGER_EDGE_RISING,
	TRIGGER_EDGE_FALLING
}EXT_INT_TRIGGER_E;

enum iic0_group_e
{
    IIC0_POS_NONE,
    IIC0_POS_PE2PE13,
    IIC0_POS_PE1PE0,
    IIC0_POS_PE13PE15,
    IIC0_POS_PE14PE13,
	IIC0_POS_MAX,
};

enum iic1_group_e
{
    IIC1_POS_NONE,
    IIC1_POS_PA6PA7,
    IIC1_POS_PF10PF11,
    IIC1_POS_PD0PD1,
    IIC1_POS_PD3PD4,
    IIC1_POS_PD5PD6,
    IIC1_POS_PD7PD8,
    IIC1_POS_PE0PE1,
	IIC1_POS_MAX,
};

enum timer0_group_e
{
    TMR0_PWM_POS_NONE,
	TMR0_PWM_POS_PA1, //0
	TMR0_PWM_POS_PA4, //1
	TMR0_PWM_POS_PA14, //2
	TMR0_PWM_POS_PA15, //3
	TMR0_PWM_POS_PF10, // 4
	TMR0_PWM_POS_PF11, // 5
	TMR0_PWM_POS_PD2,  //6
	TMR0_PWM_POS_PF2, //7
	TMR0_PWM_POS_PA13, //8
	TMR0_PWM_POS_PE15, //9
};

enum timer1_group_e
{
    TMR1_PWM_POS_NONE,
	TMR1_PWM_POS_PA1, //0
	TMR1_PWM_POS_PA5, //1
	TMR1_PWM_POS_PF14,//2
	TMR1_PWM_POS_PD3, //3
	TMR1_PWM_POS_PD10,//4
	TMR1_PWM_POS_PF7, //5
	TMR1_PWM_POS_PE0, //6
};

enum timer2_group_e
{
    TMR2_PWM_POS_NONE,
	TMR2_PWM_POS_PA2, //0
	TMR2_PWM_POS_PA6, //1
	TMR2_PWM_POS_PD0, //2
	TMR2_PWM_POS_PF0, //3
	TMR2_PWM_POS_PF8,  //4
	TMR2_PWM_POS_PE1, //5
};

enum timer3_group_e
{
    TMR3_PWM_POS_NONE,
	TMR3_PWM_POS_PA3, //0
	TMR3_PWM_POS_PA9, //1
	TMR3_PWM_POS_PF1, //2
	TMR3_PWM_POS_PF9, //3
	TMR3_PWM_POS_PE2, //4
};

//               CLK     FCS        DATx    D1   
//   SPI1_POS0   PA2     PA3    ---	PA1		PA0	  ---   ---     
// -----------------------------------------------------------
//   SPI1_POS1   PD2     PD4    ---	PD1		PD3	  ---   ---     
// -----------------------------------------------------------
//   SPI1_POS2   PD8     PD10   ---	PD7		PD9	  ---   ---   
// -----------------------------------------------------------
//   SPI1_POS3   PF2     PF4    ---	PF1		PF3	  ---   ---   
enum spi1_group_e
{
    SPI1_POS_NONE = 0,
    SPI1_POS0,
    SPI1_POS1,
    SPI1_POS2,
    SPI1_POS3,
};
enum spi1_group_line
{
	SPI1_3LINE = 0,// clk/DAT_IN/DAT_OUT
    SPI1_2LINE,    // clk/DATX
};
enum spi0_group_e
{
    SPI0_POS_NONE = 0,
    SPI0_1_LINE, // clk/DATX
    SPI0_2_LINE0,// clk/DAT0/DAT1
    SPI0_2_LINE1,// clk/DAT_IN/DAT_OUT
    SPI0_4_LINE, // clk/DAT0/DAT1/DAT2/DAT3
};

enum sdc0_group_e
{
    SD0_POS_NONE,
    SD0_POS0,
};

enum sdc1_group_e
{
    SD1_POS_NONE,
    SD1_POS0,
    SD1_POS1,
	SD1_POS2,
	SD1_POS3,  //1 bus
	SD1_POS4,  //1 bus
};

enum lcdc_group_e
{
    LCD_POS_NONE,
    LCD_POS0,
    LCD_POS1,
    LCD_POS2,
    LCD_POS3,
    LCD_POS4,
    LCD_POS5,
    LCD_POS6,
    LCD_POS7,
    LCD_POS8,
    LCD_POS9,
    LCD_POS10,
    LCD_POS11,
    LCD_POS12,
    LCD_POS13,
    LCD_POS14,
};

enum csi_group_e
{
    CSI_POS_NONE,
    CSI_POS0,
    CSI_POS1,
	CSI_POS2, //可复用MIPI
	CSI_POS3, //可复用MIPI
	CSI_POS4, //可复用MIPI
};

enum emi_group_e
{
    EMI_POS_NONE,
    EMI_POS0,
    EMI_POS1,
    EMI_POS2,
    EMI_POS3,
    EMI_POS4,
    EMI_POS5,
    EMI_POS6,
};
enum io1_chanel
{
    IO1_CH_PD1,
    IO1_CH_PA7,
	//IO1_CH_PA9,
	//IO1_CH_PF14,
    IO1_CH_NONE,
};
enum io1_softtime
{
    IO1_SOFTTIME_7MS  = 0,
    IO1_SOFTTIME_14MS = 1,

};
typedef struct{
	u32 port;
	u32 pin;
}IO_STRUCT;
/*******************************************************************************
* Function Name  : hx330x_gpioSFRSet
* Description    : set gpio sfr function
* Input          : u8 type : function table,see : GPIO_MAP_E
				   u8 group: function group
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioSFRSet(u8 type,u8 group);
/*******************************************************************************
* Function Name  : hx330x_gpioSFRGet
* Description    : get gpio sfr function
* Input          : u8 type : function table,see : GPIO_MAP_E
* Output         : None
* Return         : u32 group: function group
*******************************************************************************/
u32 hx330x_gpioSFRGet(u8 type);
/*******************************************************************************
* Function Name  : hx330x_gpioDirSet
* Description    : set gpio direction
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
                   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
				   u8 dir : dir. ->GPIO_OUTPUT,GPIO_INPUT
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioDirSet(u8 ch,u32 pin,u8 dir);
/*******************************************************************************
* Function Name  : hx330x_gpioPullSet
* Description    : set gpio pull for 10k register
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
                   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
                   u8 dir : pull. ->GPIO_PULL_FLOATING,GPIO_PULL_UP,GPIO_PULL_DOWN,GPIO_PULL_UPDOWN
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioPullSet(u8 ch,u32 pin,u8 pull);
/*******************************************************************************
* Function Name  : hx330x_gpioPinPollSet
* Description    : set gpio pull for 10k register
* Input          : enum pin_e pin
                   u8 pull ->GPIO_PULL_FLOATING,GPIO_PULL_UP,GPIO_PULL_DOWN,GPIO_PULL_UPDOWN
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioPinPollSet(enum pin_e pin, u8 pull);
/*******************************************************************************
* Function Name  : hx330x_gpioDrvSet
* Description    : set gpio drving
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
                   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
                   u8 dir : drv. ->GPIO_DRV_NORMAL,GPIO_DRV_HIGH
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioDrvSet(u8 ch,u32 pin,u8 drv);
/*******************************************************************************
* Function Name  : hx330x_gpioDataSet
* Description    : set gpio output data set
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
                   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
                   u8 dir : data. ->GPIO_LOW,GPIO_HIGH
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioDataSet(u8 ch,u32 pin,u8 data);
/*******************************************************************************
* Function Name  : hx330x_gpioDataSet
* Description    : set gpio output data set
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
                   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
                   u8 dir : data. ->GPIO_LOW,GPIO_HIGH
* Output         : None
* Return         : none
*******************************************************************************/
void exception_gpioDataSet(u8 ch,u32 pin,u8 data);
/*******************************************************************************
* Function Name  : hx330x_gpioDataGet
* Description    : set gpio input data get
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
                   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
* Output         : None
* Return         : ->GPIO_LOW,GPIO_HIGH
*******************************************************************************/
u8 hx330x_gpioDataGet(u8 ch,u32 pin);
/*******************************************************************************
* Function Name  : hx330x_gpioMapSet
* Description    : set gpio map set
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
                   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
                   u8 dir : map. ->GPIO_FUNC_GPIO,GPIO_FUNC_SFR
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioMapSet(u8 ch,u32 pin,u8 map);
/*******************************************************************************
* Function Name  : hx330x_gpioDigitalSet
* Description    : set gpio digital set
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
                   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
                   u8 dir : digital. ->GPIO_DIGITAL_DIS,GPIO_DIGITAL_EN
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioDigitalSet(u8 ch,u32 pin,u8 digital);
/*******************************************************************************
* Function Name  : hx330x_gpioHystersisSet
* Description    : set gpio hystersis set
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
				   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
                   u8 dir : hystersis. 0->disable,1->enable
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioHystersisSet(u8 ch,u32 pin,u8 hystersis);
/*******************************************************************************
* Function Name  : hx330x_GPIO_FUNC
* Description    : GPIO FUNC CFG
* Input          : u8 ch, u8 pin, u8 dir
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_GPIO_FUNC(u8 ch,u32 pin, u8 dir);
/*******************************************************************************
* Function Name  : hx330x_gpioCommonConfig
* Description    : GPIO FUNC CFG
* Input          : enum pin_e pin, u8 dir, u8 map
* Output         : None
* Return         : None
*******************************************************************************/ 
void hx330x_gpioCommonConfig(enum pin_e pin, u8 dir, u8 map);
/*******************************************************************************
* Function Name  : hx330x_gpioCommonDataGet
* Description    : GPIO FUNC CFG
* Input          : enum pin_e pin
* Output         : None
* Return         : None
*******************************************************************************/
u8 hx330x_gpioCommonDataGet(enum pin_e pin);
/*******************************************************************************
* Function Name  : hx330x_gpioPinDataSet
* Description    : set gpio output data set
* Input          : enum pin_e pin
                   u8 dir : data. ->GPIO_LOW,GPIO_HIGH
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioPinDataSet(enum pin_e pin, u8 data);
/*******************************************************************************
* Function Name  : hx330x_gpioLedPull
* Description    : set gpio pull for 300R led set
* Input          : u8 ch : gpio channel->GPIO_PA,GPIO_PB,GPIO_PC,GPIO_PD,GPIO_PE,GPIO_PF,GPIO_PG,GPIO_PF
                   u8 pin : gpio pin.->GPIO_PIN0~GPIO_PIN15
                   u8 dir : pull. ->GPIO_PULLE_FLOATING,GPIO_PULLE_UP,GPIO_PULLE_DOWN,GPIO_PULLE_UPDOWN
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioLedPull(u8 ch,u32 pin,u8 pull);
/*******************************************************************************
* Function Name  : hx330x_gpioLedInit
* Description    : set gpio hardware led
* Input          : u8 ch : gpio channel->GPIO_LED0_PA8~GPIO_LED11_PF14
                   u8 dir : pull. ->GPIO_PULLE_FLOATING,GPIO_PULLE_UP,GPIO_PULLE_DOWN,GPIO_PULLE_UPDOWN
                   u8 soft : soft->0:soft control,1->hardware
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioLedInit(u8 led,u8 pull,u8 soft);
/*******************************************************************************
* Function Name  : hx330x_gpioINTCheck
* Description    : set gpio ext irq
* Input          : u8 int_no : GPIO_INT0_PA2~GPIO_INT26_PE15
* Output         : None
* Return         : u8 : 1->int occured,0->nothing
*******************************************************************************/
u8 hx330x_gpioINTCheck(u8 int_no);
/*******************************************************************************
* Function Name  : hx330x_gpioINTClear
* Description    : clear gpio ext irq
* Input          : u8 int_no : GPIO_INT0_PA2~GPIO_INT26_PE15
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioINTClear(u8 int_no);
/*******************************************************************************
* Function Name  : hx330x_gpioINTInit
* Description    : gpio ext int initial
*                  need set dir of pin as INPUT
* Input          : u8 int_no : GPIO_INT0_PA2~GPIO_INT26_PE15
                   u8 trigger :TRIGGER_LEVEL,TRIGGER_EDGE_RISING,TRIGGER_EDGE_FALLING
                   void (*isr)(void) : call back
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioINTInit(u8 int_no,u8 trigger,void (*isr)(void));
/*******************************************************************************
* Function Name  : hx330x_gpioIRQHandler
* Description    : gpio ext irq handler
* Input          : 
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_gpioIRQHandler(void);

/*******************************************************************************
* Function Name  : hx330x_io1d1_softstart
* Description    : io1d softstart cfg
* Input          : u8 chanel: IO1_CH_PD1/ IO1_CH_PA7/IO1_CH_PA9/IO1_CH_PF14
				   u8 soft_en: 1 - enable soft slow pd, 0 - high io status
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_io1d1_softstart(u8 chanel, u8 soft_en);

/*******************************************************************************
* Function Name  : hx330x_io1d1_softstart_clr
* Description    : hx330x_io1d1_softstart_clr
* Input          : 
* 				   clr_en: 0: enable io1d1 io pull down to low
* 						   1: io1d1 io set to high
* Output         : None
* Return         : none
*******************************************************************************/
void exception_io1d1_softstart_clr(u8 clr_en);

/*******************************************************************************
* Function Name  : hx330x_io1d1_pd_enable
* Description    : hx330x_io1d1_pd_enable
* Input          :
* 				   en: 1: enable pd200k, 0: disable pd200k
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_io1d1_pd_enable(u8 en);

#endif