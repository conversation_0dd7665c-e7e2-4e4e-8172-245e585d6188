/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_IIC_H
    #define HX330X_IIC_H


#define   IIC0   0
#define   IIC1   1




/*******************************************************************************
* Function Name  : hx330x_iic0Init
* Description    : iic 0 initial
* Input          : u32 baudrate : iic0 baudrate
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic0Init(u32 baudrate);
/*******************************************************************************
* Function Name  : hx330x_iic0Uninit
* Description    : iic 0 uninitial
* Input          :  none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic0Uninit(void);
/*******************************************************************************
* Function Name  : hx330x_iic0Start
* Description    : iic 0 send start singal
* Input          :  none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic0Start(void);
/*******************************************************************************
* Function Name  : hx330x_iic0Stop
* Description    : iic 0 send stop singal
* Input          :  none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic0Stop(void);
/*******************************************************************************
* Function Name  : hx330x_iic0Stop
* Description    : iic 0 recv ack singal
* Input          :  none
* Output         : None
* Return         : 0->ack,1->nack
*******************************************************************************/
u8 hx330x_iic0RecvACK(void);
/*******************************************************************************
* Function Name  : hx330x_iic0SendACK
* Description    : iic 0 send ack singal
* Input          :  none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic0SendACK(void);
/*******************************************************************************
* Function Name  : hx330x_iic0SendByte
* Description    : iic 0 send one byte
* Input          :  u8 byte : byte
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic0SendByte(u8 byte);
/*******************************************************************************
* Function Name  : hx330x_iic0RecvByte
* Description    : iic 0 recv one byte
* Input          :  none
* Output         : None
* Return         : u8 byte : byte
*******************************************************************************/
u8 hx330x_iic0RecvByte(void);
/*******************************************************************************
* Function Name  : hx330x_iic1Init
* Description    : iic 0 initial
* Input          : u32 baudrate : iic1 baudrate
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic1Init(u32 baudrate);
/*******************************************************************************
* Function Name  : hx330x_iic1Uninit
* Description    : iic 1 uninitial
* Input          :  none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic1Uninit(void);
/*******************************************************************************
* Function Name  : hx330x_iic1Start
* Description    : iic 1 send start singal
* Input          :  none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic1Start(void);
/*******************************************************************************
* Function Name  : hx330x_iic1Stop
* Description    : iic 1 send stop singal
* Input          :  none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic1Stop(void);
/*******************************************************************************
* Function Name  : hx330x_iic1RecvACK
* Description    : iic 1 recv ack singal
* Input          :  none
* Output         : None
* Return         : 0->ack,1->nack
*******************************************************************************/
u8 hx330x_iic1RecvACK(void);
/*******************************************************************************
* Function Name  : hx330x_iic1SendACK
* Description    : iic 1 send ack singal
* Input          :  none
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic1SendACK(void);
/*******************************************************************************
* Function Name  : hx330x_iic1SendByte
* Description    : iic 1 send one byte
* Input          :  u8 byte : byte
* Output         : None
* Return         : none
*******************************************************************************/
void hx330x_iic1SendByte(u8 byte);
/*******************************************************************************
* Function Name  : hx330x_iic1RecvByte
* Description    : iic 1 recv one byte
* Input          :  none
* Output         : None
* Return         : u8 byte : byte
*******************************************************************************/
u8 hx330x_iic1RecvByte(void);







#endif
