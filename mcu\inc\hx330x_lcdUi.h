/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_LCDUI_H
    #define HX330X_LCDUI_H


enum LCD_DISPLAY_MODE{
    LCD_DISPLAY_ROTATE_NONE = 0x00,
    LCD_DISPLAY_ROTATE_0    = 0x00,
    LCD_DISPLAY_ROTATE_90   = 0x01,
    LCD_DISPLAY_ROTATE_180  = 0x02,
    LCD_DISPLAY_ROTATE_270  = 0x03,

    LCD_DISPLAY_MIRROR_NONE = 0x00,
    LCD_DISPLAY_V_MIRROR    = 0x10,
    LCD_DISPLAY_H_MIRROR    = 0x30,

    LCD_DISPLAY_ROTATE_EN   = 0x01,
    LCD_DISPLAY_ROTATE_MASK = 0x03,
    LCD_DISPLAY_MIRROR_MASK = 0x30,

};
#define  LCD_ROTATE_0            0
#define  LCD_ROTATE_V_MIRROR	 1
#define  LCD_ROTATE_H_MIRROR	 2
#define  LCD_ROTATE_180          3
#define  LCD_ROTATE_90           4
#define  LCD_ROTATE_270          5
#define  LCD_ROTATE_MASK         7



//-------alpha type

enum
{
	ALPHA_NORMAL=0,
	ALPHA_GLOBAL
};


//--------UI type
enum
{
	UI_LAYER0=0,
	UI_LAYER1,

	UI_LAYER_MAX
};


/*******************************************************************************
* Function Name  : hx330x_lcdShowWaitDone
* Description    : wait lcdshow finish
* Input          :  none
* Output         : None
* Return         : 
*******************************************************************************/
//SDRAM_TEXT_SECTION
void hx330x_lcdShowWaitDone(void);
/*******************************************************************************
* Function Name  : hx330x_lcdShowKick
* Description    : kick lcd show
* Input          :  none
* Output         : None
* Return         : 
*******************************************************************************/
//SDRAM_TEXT_SECTION
void hx330x_lcdShowKick(void);
/*******************************************************************************
* Function Name  : hx330x_checkLcdShowStatus
* Description    : check lcd show kick or not
* Input          :  none
* Output         : bit 1 status
* Return         : 
*******************************************************************************/
u8 hx330x_checkLcdShowStatus(void);
/*******************************************************************************
* Function Name  : hx330x_lcdShowIRQHandler
* Description    : initial lcdshow irq handler
* Input          :  none
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdShowIRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_lcdShowISRRegister
* Description    : register lcd show isr 
* Input          :  void (*isr)(void) : call back
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdShowISRRegister(void (*isr)(void));
/*******************************************************************************
* Function Name  : hx330x_lcdshowInit
* Description    : initial LCD SHOW for video/ui0/ui1 layers
* Input          : u16 width : display width
				   u16 height: display height
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdshowInit(u16 width,u16 height);
/*******************************************************************************
* Function Name  : hx330x_lcdshowSetCritical
* Description    : set dma critical level
* Input          : u32 critical
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdshowSetCritical(u8 critical);
/*******************************************************************************
* Function Name  : hx330x_lcdSetVideoBgColor
* Description    : set video layer background color
* Input          : u8 red : red value 
				   u8 green:green value 
				   u8 blue  : blue value 
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdSetVideoBgColor(u8 red,u8 green,u8 blue);
/*******************************************************************************
* Function Name  : hx330x_lcdSetVideoBSC
* Description    : set video layer csc effect
* Input          : u16 brightness : brightness
				   u16 saturation:saturation
				   u16 contrast  : contrast
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdSetVideoBSC(u16 brightness,u16 saturation,u16 contrast);
/*******************************************************************************
* Function Name  : hx330x_lcdSetVideoBrightness
* Description    : set video layer csc effect
* Input          : u16 brightness : brightness

* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdSetVideoBrightness(s8 brightness);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoSetRgbWidth
* Description    : set video layer color dither width
* Input          : u8 red : red color width
				   u8 green:green color width
				   u8 blue  : blue color width
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdVideoSetRgbWidth(u32 datamode0);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoSetScalePara
* Description    : set video layer scale
* Input          : u8 tap0 : tap0
				   u8 tap1 : tap1
				   u8 tap2 : tap2
				   u8 tap3 : tap3
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdVideoSetScalePara(u8 tap3,u8 tap2,u8 tap1,u8 tap0);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoSetScaleLine
* Description    : set video layer scale specific line
* Input          : u16 line:line number
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdVideoSetScaleLine(u16 line,u8 enable);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoSetScanMode
* Description    : set video layer scan mode.scan mode is rotate
* Input          :  u8 scan_mode : scan mode 
* Output         : None
* Return         : 
*******************************************************************************/
//SDRAM_TEXT_SECTION
void hx330x_lcdVideoSetScanMode(u8 scan_mode);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoGetYAddr
* Description    : set video layer current y buffer addr
* Input          :  
* Output         : None
* Return         : buffer addr
*******************************************************************************/
u32 hx330x_lcdVideoGetYAddr(void);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoGetUVAddr
* Description    : set video layer current uv buffer addr
* Input          :  
* Output         : None
* Return         : buffer addr
*******************************************************************************/
u32 hx330x_lcdVideoGetUVAddr(void);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoSetAddr
* Description    : set video layer buffer addr
* Input          : u32 y_addr : ystride 64byte align
				   u32 uv_addr : uv_addr   64byte align
* Output         : None
* Return         : None
*******************************************************************************/
//SDRAM_TEXT_SECTION
void hx330x_lcdVideoSetAddr(u32 y_addr,u32 uv_addr);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoSetStride
* Description    : set video layer stride
* Input          : u32 ystride : ystride
				   u32 uvstride : uvstride
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdVideoSetStride(u32 ystride,u32 uvstride);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoSetSize
* Description    : set video layer size
* Input          : u16 width : width
                   u16 height : height
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdVideoSetSize(u16 width,u16 height);
/*******************************************************************************
* Function Name  : hx330x_lcdvideoSetPosition
* Description    : set video position
* Input          : u16 x : x
				   u16 y : y
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdvideoSetPosition(u16 x,u16 y);
/*******************************************************************************
* Function Name  : hx330x_lcdvideoMemcpy
* Description    : set video copy function,src size must be 320x240
* Input          : u16 dst_w : dest width,range (320,640]
*                  u16 dst_h : dest height,range (320,640]
*                  bool enable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdvideoMemcpy(u16 dst_w,u16 dst_h,bool enable);
/*******************************************************************************
* Function Name  : hx330x_lcdvideoEnable
* Description    : video enable
* Input          : u8 en: 0-disable,1-enable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdvideoEnable(u8 en);

/*******************************************************************************
* Function Name  : hx330x_lcdVideoSetGAMA
* Description    : set GAMA table by index
* Input          : u8 gamma_index :gamma table index
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdVideoSetGAMA(u32 gred,u32 ggreen,u32 gblue);
/*******************************************************************************
* Function Name  : hx330x_lcdVideo_CCM_cfg
* Description    : set lcd_ccm
* Input          : u32 *p_ccm:pointer of ccm matrix 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdVideo_CCM_cfg(u32 *p_ccm);

/*******************************************************************************
* Function Name  : hx330x_lcdVideo_SAJ_cfg
* Description    : set lcd_saj
* Input          : u32 *p_saj:pointer of saj 
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdVideo_SAJ_cfg(u8 *p_saj);
/*******************************************************************************
* Function Name  : hx330x_lcdvideoGammaEnable
* Description    : enable lcd  video gamma table
* Input          :  u8 en : 1-enable,0-disable
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdvideoGammaEnable(u8 en);

/*******************************************************************************
* Function Name  : hx330x_lcdUiEnable
* Description    : lcd ui enable
* Input          : layer : ui layer,UI_LAYER0,UI_LAYER1
				   u8 en: 0-disable,1-enable
* Output         : None
* Return         : None
*******************************************************************************/
//SDRAM_TEXT_SECTION
void hx330x_lcdUiEnable(u8 layer,u8 en);
/*******************************************************************************
* Function Name  : hx330x_lcdUiSetSize
* Description    : lcd ui set size
* Input          : layer : ui layer,UI_LAYER0,UI_LAYER1
				   u16 width : width
				   u16 height: height
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdUiSetSize(u8 layer,u16 width,u16 height);
/*******************************************************************************
* Function Name  : hx330x_lcdUiSetPosition
* Description    : lcd ui set position
* Input          : layer : ui layer,UI_LAYER0,UI_LAYER1
				   u16 x : x
				   u16 y: y
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdUiSetPosition(u8 layer,u16 x,u16 y);
/*******************************************************************************
* Function Name  : hx330x_lcdUiSetPalette
* Description    : lcd ui set palette
* Input          : layer : ui layer,UI_LAYER0,UI_LAYER1
                   u32 addr : addr
* Output         : None
* Return         : None
*******************************************************************************/
void hx330x_lcdUiSetPalette(u8 layer,u32 addr);
/*******************************************************************************
* Function Name  : hx330x_lcdUiSetAddr
* Description    : lcd ui set  addr
* Input          : layer : ui layer,UI_LAYER0,UI_LAYER1
                   u32 addr : addr
                   u32 data_len : 
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdUiSetAddr(u8 layer,u32 addr,u32 data_len);
/*******************************************************************************
* Function Name  : hx330x_UiGetAddr
* Description    : get UI layer current buffer addr
* Input          : layer : ui layer (UI_LAYER0,UI_LAYER1)
* Output         : None
* Return         : buffer addr
*******************************************************************************/
u32 hx330x_UiGetAddr(u8 layer);
/*******************************************************************************
* Function Name  : hx330x_lcdUiSetAlpha
* Description    : lcd ui set set alpha type
* Input          : layer : ui layer (UI_LAYER0,UI_LAYER1)
				   u8 type : ALPHA_NORMAL,ALPHA_GLOBAL
				   u8 value : gblobal alpha value
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdUiSetAlpha(u8 layer,u8 type,u8 value);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoUpScaler_cfg
* Description    : hx330x_lcdVideoUpScaler_cfg
* Input          : 
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdVideoUpScaler_cfg(u16 src_w,u16 src_h,u16 dst_w,u16 dst_h,u32 * args);
/*******************************************************************************
* Function Name  : hx330x_lcdVideoUpScaler_cfg
* Description    : hx330x_lcdVideoUpScaler_cfg
* Input          : 
* Output         : None
* Return         : 
*******************************************************************************/
void hx330x_lcdVideoUpScalerSoftRotate_cfg(u16 src_w,u16 src_h,u16 dst_w,u16 dst_h,u32 scan_mode);
/*******************************************************************************
* Function Name  : hx330x_lcdUiLzoSoftCreate
* Description    : create a uilzo code
* Input          : u8 data : 
                   u32 cnt : need encode 
                   u32 *used : used cnt
* Output         : encoded code
* Return         : none
*******************************************************************************/
u32 hx330x_lcdUiLzoSoftCreate(u8 data,u32 cnt,u32 *used);


#endif
