/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef HX330X_RTC_H
    #define HX330X_RTC_H


#define  WKI_0      0
#define  WKI_1      1

/*******************************************************************************
* Function Name  : hx330x_rtcWriteByte
* Description	 : rtc write byte
* Input 		 : u8 byte : byte value
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtcWriteByte(u8 byte);
/*******************************************************************************
* Function Name  : hx330x_rtcReadByte
* Description	 : rtc read byte
* Input 		 : 
* Output		 : None
* Return		 : u8
*******************************************************************************/
u8 hx330x_rtcReadByte(void);
/*******************************************************************************
* Function Name  : hx330x_rtcRamRead
* Description	 : rtc read ram
* Input 		 : u8 cmd : ram addr
                    u8 *buffer : buffer
                    u8 len : length
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtcRamRead(u8 addr,u8 *buffer,u8 len);
/*******************************************************************************
* Function Name  : hx330x_rtcRamWrite
* Description	 : rtc write ram
* Input 		 : u8 cmd : ram addr
				   u8 *buffer : buffer
				   u8 len : length
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtcRamWrite(u8 addr,u8 *buffer,u8 len);
/*******************************************************************************
* Function Name  : hx330x_rtcDataRead
* Description	 : rtc read data
* Input 		 : u8 cmd : cmd
                    u8 *buffer : buffer
                    u8 len : length
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtcDataRead(u8 cmd,u8 *buffer,u8 len);
/*******************************************************************************
* Function Name  : hx330x_rtcDataWrite
* Description	 : rtc write data
* Input 		 : u8 cmd : cmd
				   u8 *buffer : buffer
				   u8 len : length
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtcDataWrite(u8 cmd,u8 *buffer,u8 len);
/*******************************************************************************
* Function Name  : hx330x_rtcInit
* Description	 : rtc initial
* Input 		 : 
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtcInit(void);
/*******************************************************************************
* Function Name  : hx330x_rtc128K_trim
* Description	 : 
* Input 		 : 
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtc128K_trim(void);
/*******************************************************************************
* Function Name  : hx330x_rtcIRQHandler
* Description	 : rtc irq handler
* Input 		 : 
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtcIRQHandler(void);
/*******************************************************************************
* Function Name  : hx330x_rtcSencodEnable
* Description	 : rtc enable second irq
* Input 		 :      u8 en : 1-enable
                        void (*callback)(void) : call back
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtcSencodEnable(u8 en,void (*callback)(void));
/*******************************************************************************
* Function Name  : hx330x_rtcAlamEnable
* Description	 : rtc enable alam irq
* Input 		 :      u8 en : 1-enable
                        void (*callback)(void) : call back
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtcAlamEnable(u8 en,void (*callback)(void));
/*******************************************************************************
* Function Name  : hx330x_rtcAlamSet
* Description	 : rtc enable alam time
* Input 		 : u32 value alam time in second
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_rtcAlamSet(u32 value);
/*******************************************************************************
* Function Name  : hx330x_rtc_alarm_weakup_reset
* Description	 : rtc enable alam time
* Input 		 : second:     poweron after second
* Output		 : None
* Return		 : none
*******************************************************************************/
//void hx330x_rtc_alarm_weakup_reset(u32 second) ;
/*******************************************************************************
* Function Name  : hx330x_rtcGet
* Description	 : rtc get second value
* Input 		 :      
* Output		 : None
* Return		 : u32 : second
*******************************************************************************/
u32 hx330x_rtcGet(void);
/*******************************************************************************
* Function Name  : hx330x_rtcSet
* Description	 : rtc set value
* Input 		 : u32 : second
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_rtcSet(u32 value);
/*******************************************************************************
* Function Name  : hx330x_WKOEnable
* Description	 : wko enable
* Input 		 : u8 en : 0->disable,1->enable
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_WKOEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_VDDGSENEnable
* Description	 : VDD_GSEN enable
* Input 		 : u8 en : 0->disable,1->enable
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_VDDGSENEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_WKI0InputEnable
* Description	 : WKI input enable 
* Input 		 : u8 en : 0->disable,1->enable
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_WKI0InputEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_WKIEnable
* Description	 : WKI input enable 
* Input 		 : u8 en : 0->disable,1->enable
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_WKI1InputEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_WKI1Read
* Description	 : WKI 1 read
* Input 		 :      
* Output		 : None
* Return		 : 0-low,1-high
*******************************************************************************/
u8 hx330x_WKI1Read(void);
/*******************************************************************************
* Function Name  : hx330x_WKI0Read
* Description	 : WKI 0 read
* Input 		 :      
* Output		 : None
* Return		 : 0-low,1-high
*******************************************************************************/
u8 hx330x_WKI0Read(void);
/*******************************************************************************
* Function Name  : hx330x_WKI1WakeupEnable
* Description	 : WKI 1 wakeup enable
* Input 		 : u8 en : 0->disable,1->enable
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_WKI1WakeupEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_WKI0WakeupEnable
* Description	 : WKI 1 wakeup enable
* Input 		 : u8 en : 0->disable,1->enable
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_WKI0WakeupEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_WKI1WakeupTriger
* Description	 : WKI 1 wakeup enable
* Input 		 : u8 en : 0->wki1 level triger,1->wki1 wakeup rising edge triger
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_WKI1WakeupTriger(u8 en); //1: rising triger, 0: level triger
/*******************************************************************************
* Function Name  : hx330x_WKI0WakeupTriger
* Description	 : WKI 1 wakeup enable
* Input 		 : u8 en : 0->wki1 level triger,1->wki1 wakeup rising edge triger
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_WKI0WakeupTriger(u8 en);
/*******************************************************************************
* Function Name  : hx330x_WakeUpCleanPending
* Description	 : WKI 1 clean wakeup pending
* Input 		 : None
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_WakeUpCleanPending(void);
/*******************************************************************************
* Function Name  : hx330x_rtcBatDecEnable
* Description	 : rtc BATTERY dectection enable
* Input 		 : u8 en : 0->disable,1->enable
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_rtcBatDecEnable(u8 en);
/*******************************************************************************
* Function Name  : hx330x_bandwidthmon_sec
* Description	 : sdram bandwidth monitor
* Input 		 : none
* Output		 : None
* Return		 : none
*******************************************************************************/
void hx330x_bandwidthmon_sec(void);
/*******************************************************************************
* Function Name  : hx330x_rtcSenHVEnable
* Description	 : hx330x_rtcSenHVEnable
* Input 		 : u8 en : 0->disable,1->enable
* Output		 : None
* Return		 : 
*******************************************************************************/
void hx330x_rtcSenHVEnable(u8 en);

/*******************************************************************************
* Function Name  : hx330x_rtcAlarmWakeUpFlag
* Description	 : hx330x_rtcAlarmWakeUpFlag
* Input 		 : 
* Output		 : None
* Return		 : 
*******************************************************************************/
u32 hx330x_rtcAlarmWakeUpFlag(void);



#endif

