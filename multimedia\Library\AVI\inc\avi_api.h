/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  AVI_API_H
#define  AVI_API_H

#include "avi_typedef.h"

#define	 AVI_STANDARD_EN				1
#define  AVI_OPENDML_EN					1


typedef struct  AVI_ENC_ARG_S{
	int  media_ch;
	int  fd[2];
	INT32U*	avi_cache;
	INT32U	avi_cache_len;	
	u16  width;
	u16  height;
	u32  fps;
	u32  audio_en;
	u32  samplerate;
	u32  sync_wr;
}AVI_ENC_ARG;

typedef struct  AVI_DEC_ARG_S{
	int     media_ch;
	int  	fd;
	INT8U*	avi_cache;
	INT32U	avi_cache_len;	
	INT32U	src_type;   //MEDIA_SRC_TYPE
	
	u32  	width;
	u32  	height;	
	u32  	samplerate;
	s32  	frame_step;
	u32 	fps;  // us	
	u32 	framecnt;
	u32  	stream_num;
}AVI_DEC_ARG;



//---------AVI ALIGN CTL--------------------------------------------------------
#define  AVI_ALIGNLEN					(32*1024L)
//---------AVI FILE CFG---------------------------------------------------------
#define  AVISTD_CFG_FILE_MAX		    FIL_SIZE_2G         //size of one file
#define  AVIDML_CFG_FILE_MAX		    FIL_SIZE_4G         //size of one file

//---------AVI FRAME CFG--------------------------------------------------------
#define  AVI_CFG_FRAME_SIZE      		(40*1024L)			// Probably size every frame(720p)
#define  AVI_CFG_REC_TIME_MAX			(60*60L)			//record time 20min

//---------AVI STD CFG---------------------------------------------------------
#define  AVISTD_IDX_TABLEN				AVI_ALIGNLEN		//AVI STD INDX TABLE BUFF LEN
#define  AVISTD_CFG_MAXFRAME_BYSIZE		(AVISTD_CFG_FILE_MAX/AVI_CFG_FRAME_SIZE) //frame max according to size
#define  AVISTD_CFG_MAXFRAME_BYTIME		(AVI_CFG_REC_TIME_MAX*30) //frame max according to time

//---------AVI OPENDML CFG-----------------------------------------------------
#define  AVIDML_CFG_AVIX_LEN         	(1024L*1024L*900L)	//ONE AVIX MAX LEN 900Mbytes
#define  AVIDML_CFG_AVIX_MAX         	(AVIDML_CFG_FILE_MAX/AVIDML_CFG_AVIX_LEN)					// AVIX NUM  
#define  AVIDML_CFG_SYNC_TIME 			2	 				// second , this value should let ODML_CFG_INDX_NUM%ODML_CFG_INDX_SYNC == 0
#define  AVIDML_CFG_INDX_SYNC        	(AVIDML_CFG_SYNC_TIME/2)
#define  AVIDML_CFG_MAXFRAME_BYSIZE		(AVIDML_CFG_FILE_MAX/AVI_CFG_FRAME_SIZE) //frame max according to size
#define  AVIDML_CFG_MAXFRAME_BYTIME		(AVI_CFG_REC_TIME_MAX*30) //frame max according to time

#define  AVIDML_SUPERIDX_LEN          	(AVIDML_CFG_MAXFRAME_BYTIME/AVI_ODML_IX_NUM) //736+ AVIDML_SUPERINDX_NUM*16*2
//#define  AVIDML_SUPERIDX_LEN          	(AVIDML_CFG_MAXFRAME_BYSIZE/AVI_ODML_IX_NUM + 32*4) //使用size可能会出现不够的情况，多加一些
#define  AVIDML_SUPERINDX_NUM           (((AVIDML_SUPERIDX_LEN +31)&~31) + 9)	  //buff len   



#endif
