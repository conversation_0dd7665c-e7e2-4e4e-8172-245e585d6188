/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#ifndef  IMAGE_ENCODE_H
    #define  IMAGE_ENCODE_H





/*******************************************************************************
* Function Name  : imageEncodeInit
* Description	 : image encode initial
* Input 		 : none
* Output		 : none 										   
* Return		 : int 0
*******************************************************************************/
int imageEncodeInit(void);
/*******************************************************************************
* Function Name  : imageEncodeUninit
* Description	 : image encode uninitial
* Input 		 : none
* Output		 : none 										   
* Return		 : int 0 success
*******************************************************************************/
int imageEncodeUninit(void);
/*******************************************************************************
* Function Name  : imageEncodeStart
* Description	 : take a photo
* Input 		 : JPG_ENC_ARG *arg
* Output		 : none 										   
* Return		 : int : 0 success
                                -1 fail
*******************************************************************************/
int imageEncodeStart(JPG_ENC_ARG *arg);




/*******************************************************************************
* Function Name  : imageLcdEncodeStart
* Description	 : take a photo from lcd
* Input 		 : JPG_ENC_ARG *arg
* Output		 : none 										   
* Return		 : int : 0 success
                                -1 fail
*******************************************************************************/
int imageLcdEncodeStart(JPG_ENC_ARG *arg);

#endif
