/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"
/*******************************************************************************
* Function Name  : filelist_NameCheckSufType
* Description    : check  file type by filename suffix
* Input          : char *name : file name
* Output         : none                                            
* Return         : type
*******************************************************************************/
static int filelist_NameCheckSufType(char *name, u32 *name_len)
{
	char *str,*point;
	u32 len = 0;
    str = name;
	point = NULL;
	while(*str)
	{
		if(*str == '.')
			point = str;
		str++;
		len++;
	}	
	if(name_len)
		*name_len = len;
	if(point == NULL)
		return FILELIST_TYPE_DIR;
	else if(hx330x_str_cmp(point,SUFFIX_AVI)==0)
		return FILELIST_TYPE_AVI;
	else if(hx330x_str_cmp(point,SUFFIX_WAV)==0)
		return FILELIST_TYPE_WAV;
	else if(hx330x_str_cmp(point,SUFFIX_JPG)==0)
		return FILELIST_TYPE_JPG;
	else if(hx330x_str_cmp(point,SUFFIX_MP3)==0)
	{
		return FILELIST_TYPE_MP3;	
		
	}else if(hx330x_str_cmp(point,SUFFIX_NES)==0)
	{
		return FILELIST_TYPE_NES;	
		
	}
	return FILELIST_FLAG_IVL;
}
/*******************************************************************************
* Function Name  : filelist_NameCheckPreType
* Description    : check  file type by filename PREFIX
* Input          : char *name : file name
* Output         : none                                            
* Return         : type
*******************************************************************************/
static int filelist_NameCheckPreType(char *name)
{
	if(hx330x_str_ncmp(name,PREFIX_AVI,PREFIX_LEN) == 0)
		return FILELIST_TYPE_AVI;
	else if(hx330x_str_ncmp(name,PREFIX_WAV,PREFIX_LEN) == 0)
		return FILELIST_TYPE_WAV;
	else if(hx330x_str_ncmp(name,PREFIX_JPG,PREFIX_LEN) == 0)
		return FILELIST_TYPE_JPG;
	else if(hx330x_str_ncmp(name,PREFIX_LOK_AVI,PREFIX_LEN) == 0)
	{
		return FILELIST_TYPE_AVI|FILELIST_FLAG_LOK;
	}
	else if(hx330x_str_ncmp(name,PREFIX_LOK_JPG,PREFIX_LEN) == 0)
	{
		return FILELIST_TYPE_JPG|FILELIST_FLAG_LOK;
	}
	else if(hx330x_str_ncmp(name,PREFIX_LOK_SPI,PREFIX_LEN) == 0)
	{
		return FILELIST_TYPE_JPG|FILELIST_FLAG_LOK;
	}
	return FILELIST_FLAG_IVL;
}
/*******************************************************************************
* Function Name  : filenode_fname_createByString
* Description    : filenode_fname_createByString
* Input          : FILELIST_NODE_T* filenode: handle filenode
* 				   FILELIST_NAME_T *fname: file_list name ptr (fname)
*                  char *string: file name string
*                  u32 datetime: file fat time, if string has datetime info, not use this datetime    
* Output         : None
* Return         : int : <0 fail, 0 sucess
*******************************************************************************/
static int filenode_fname_createByString(FILELIST_NODE_T* filenode,FILELIST_NAME_T *fname,char *string,u32 datetime)
{
    INT32U value;	
	if(filenode == NULL || filenode->valid == 0)
		return -1;
	int    filetype = filelist_NameCheckPreType(string);
	if(( filetype & filenode->node_type) != (filetype & FILENODE_TYPE_MASK))
	//if(!(filetype &(filenode->node_type & FILELIST_TYPE_MASK)))
	{
		return -1;
	}
	string += PREFIX_LEN;
	fname->index 	= filetype | (filenode->node_type&~FILELIST_TYPE_MASK);
	fname->datatime = 0;
	if(*string > '0')
	{
		fname->index |= FILELIST_FLAG_TIME;
		value = hx330x_str2num(string,4);
		if(value < 0 || value > 127)
			return -1;
		fname->datatime |= (value - 1980) << 25; //year: 1980~2107
		value = hx330x_str2num(string+4,2);
		if(value <= 0 || value > 12)
			return -1;
		fname->datatime |= value << 21; //month: [24:21]:1~12
		value = hx330x_str2num(string+6,2);
		if(value < 0 || value > 31)
			return -1;	
		fname->datatime |= value << 16; //day: [20:16]: 0~31
		value = hx330x_str2num(string+8,2);
		if(value < 0 || value > 23)
			return -1;	
		fname->datatime |= value << 11; //hour: [15:11]: 0~23
		value = hx330x_str2num(string+10,2);
		if(value < 0 || value > 59)
			return -1;	
		fname->datatime |= value << 5; //min: [10:5]: 0~59
		value = hx330x_str2num(string+12,2);
		if(value < 0 || value > 59)
			return -1;	
		fname->datatime |= value >> 1; //sec: [4:0]: 0~29
		string += 14;
		
	}else
	{
		fname->datatime = datetime;
		value = hx330x_str2num(string+1,4);
		if(value < 0)
			return -1;
		fname->index |= value;
	}
	
	return 0;
}
/*******************************************************************************
* Function Name  : filelist_NameChangeSufType
* Description    : change  file type by filename suffix
* Input          : char *name : file name
* Output         : none                                            
* Return         : type
*******************************************************************************/
int filelist_NameChangeSufType(char *name, char *dst_sufType)
{
	char *str,*point;
    str = name;
	point = NULL;
	while(*str)
	{
		if(*str == '.')
			point = str;
		str++;
	}	
	if(point == NULL)
		return -1;
	hx330x_str_cpy(point,dst_sufType);
	return 0;
}
/*******************************************************************************
* Function Name  : filenode_fname_createByIndex
* Description    : filenode_fname_createByIndex, create datetime info
* Input          : FILELIST_NODE_T* filenode: handle filenode
*                  FILELIST_NAME_T *fname: file_list name ptr (fname)
*                  int index: fname index
* Output         : None
* Return         : int : <0 fail, 0 sucess
*******************************************************************************/
int filenode_fname_check(FILELIST_NODE_T* filenode,FILELIST_NAME_T *fname)
{
	if(( fname->index & filenode->node_type) == (fname->index & FILENODE_TYPE_MASK))
	//if(fname->index & (filenode->node_type & FILENODE_TYPE_MASK))
		return 0;
	else
		return -1;
}
/*******************************************************************************
* Function Name  : filenode_fname_createByIndex
* Description    : filenode_fname_createByIndex, create datetime info
* Input          : FILELIST_NODE_T* filenode: handle filenode
*                  FILELIST_NAME_T *fname: file_list name ptr (fname)
*                  int index: fname index
* Output         : None
* Return         : int : <0 fail, 0 sucess
*******************************************************************************/
static void filenode_fname_createByIndex(FILELIST_NODE_T* filenode,FILELIST_NAME_T *fname,int index)
{
	DATE_TIME_T 	date_time;
	hal_rtcTimeGetExt(&date_time);
	fname->index  	= filenode->node_type | index; 
	fname->datatime 	= ((date_time.year - 1980) << 25) | (date_time.month << 21) | (date_time.day << 16)
					 |(date_time.hour << 11) | (date_time.min << 5) | (date_time.sec >> 1);
}

/*******************************************************************************
* Function Name  : filenode_fname_createByIndex
* Description    : filenode_fname_createByIndex, create datetime info
* Input          : FILELIST_NODE_T* filenode: handle filenode
*                  FILELIST_NAME_T *fname: file_list name ptr (fname)
*                  int index: fname index
* Output         : None
* Return         : int : <0 fail, 0 sucess
*******************************************************************************/
int filenode_fname_createNew(FILELIST_NODE_T* filenode,FILELIST_NAME_T *fname)
{
	if(filenode == NULL || filenode->valid == 0)
	{
		fname->index = FILELIST_FLAG_IVL;
		return -1;
	}
	FILELIST_CTRL_T * filelist = (FILELIST_CTRL_T*)filenode->plist;
	int i;
	int index = FILE_INDEX_MIN;
	if(filelist->count >= filelist->list_index_max)
		return -1;
	//deg_Printf("filenode_fname_createNew:%d\n",filelist->count);
	if(filelist->count > 0)
	{
		for(i = filelist->count - 1; i >= 0;i--)
		{
			hal_wdtClear();
			//deg_Printf("index 0:%x\n",filelist->name[i].index);
			if(filenode_fname_check(filenode,&filelist->name[i]) >=0 )
			{
				//deg_Printf("index:%x\n",filelist->name[i].index);
				index = (filelist->name[i].index & FILELIST_INDEX_MASK) + 1;
				
				if(index > (filelist->list_index_max + FILE_INDEX_MIN))
					index = FILE_INDEX_MIN;
				//deg_Printf("new index:%d\n",index);
				break;
			}
		}	
	}
	filenode_fname_createByIndex(filenode,fname,index);
	return 0;
}
/*******************************************************************************
* Function Name  : filelist_filename_CreateByFname
* Description    : filelist_filename_CreateByFname, if not have datetime info, create datetime
* Input          : char *string: file name string buf
*                  FILELIST_NAME_T *fname: file_list name ptr (fname)
* Output         : None
* Return         : None
*******************************************************************************/
char* filenode_filename_CreateByFname(FILELIST_NODE_T* filenode,FILELIST_NAME_T *fname)
{
    INT32U  file_type;	
	if(fname->index & FILELIST_FLAG_IVL)
		return NULL;
	if(filenode == NULL || filenode->valid == 0)
	{
		return NULL;
	}
	FILELIST_CTRL_T * filelist = (FILELIST_CTRL_T*)filenode->plist;
	if(filelist->count > filelist->list_index_max)
		return NULL;
	char *string = filenode->file;

	file_type = fname->index & (FILELIST_FLAG_LOK|FILELIST_TYPE_MASK);
	switch(file_type & (FILELIST_TYPE_MASK|FILELIST_FLAG_LOK))
	{
		case FILELIST_TYPE_JPG: hx330x_str_ncpy(string,PREFIX_JPG,PREFIX_LEN); break;
		case FILELIST_TYPE_WAV: hx330x_str_ncpy(string,PREFIX_WAV,PREFIX_LEN); break;
		case FILELIST_TYPE_AVI: hx330x_str_ncpy(string,PREFIX_AVI,PREFIX_LEN); break;
		case FILELIST_TYPE_AVI|FILELIST_FLAG_LOK: hx330x_str_ncpy(string,PREFIX_LOK_AVI,PREFIX_LEN); break;
		case FILELIST_TYPE_JPG|FILELIST_FLAG_LOK: hx330x_str_ncpy(string,PREFIX_LOK_JPG,PREFIX_LEN); break;
		case FILELIST_TYPE_SPI: hx330x_str_ncpy(string,PREFIX_SPI,PREFIX_LEN); break;
		case FILELIST_TYPE_SPI|FILELIST_FLAG_LOK: hx330x_str_ncpy(string,PREFIX_LOK_SPI,PREFIX_LEN); break;
		default: string[0] = 0; return NULL;
	}
	string += PREFIX_LEN;
	if(fname->datatime == 0)
	{
		DATE_TIME_T 	date_time;
		hal_rtcTimeGetExt(&date_time);
		fname->datatime 	= ((date_time.year - 1980) << 25) | (date_time.month << 21) | (date_time.day << 16)
								|(date_time.hour << 11) | (date_time.min << 5) | (date_time.sec >> 1);	
	}

	if(fname->index & FILELIST_FLAG_TIME)
	{
		hx330x_num2str(string, (fname->datatime >> 25) + 1980, 4);	//year: 1980~2107
		hx330x_num2str(string + 4, (fname->datatime >> 21)&0x0f, 2);//month: [24:21]:1~12
		hx330x_num2str(string + 6, (fname->datatime >> 16)&0x1f, 2);//day: [20:16]: 0~31
		hx330x_num2str(string + 8, (fname->datatime >> 11)&0x1f, 2);//hour: [15:11]: 0~23
		hx330x_num2str(string + 10, (fname->datatime >> 5)&0x1f, 2);//min: [10:5]: 0~59
		hx330x_num2str(string + 12, (fname->datatime << 1)&0x3f, 2);//sec: [4:0]: 0~29
		string += 14;
	}else
	{
		*string++ = '0';
	}
	if((file_type & FILELIST_TYPE_MASK) == FILELIST_TYPE_SPI)
	{
		hx330x_num2str(string, fname->index & FILELIST_INDEX_MASK, 3);//INDEX
		string += 3;
	}else
	{
		hx330x_num2str(string, fname->index & FILELIST_INDEX_MASK, 4);//sec: [4:0]: 0~29
		string += 4;
	}

	switch(file_type & FILELIST_TYPE_MASK)
	{
		case FILELIST_TYPE_JPG: hx330x_str_ncpy(string,SUFFIX_JPG,4); break;
		case FILELIST_TYPE_WAV: hx330x_str_ncpy(string,SUFFIX_WAV,4); break;
		case FILELIST_TYPE_AVI: hx330x_str_ncpy(string,SUFFIX_AVI,4); break;
		case FILELIST_TYPE_SPI: hx330x_str_ncpy(string,SUFFIX_JPG,4); break;
		default: return NULL;
	}
	string[4] = 0; //end string
	//debgbuf(filenode->path, 32);
	//debgbuf(filenode->file, 32);
	//debgbuf(filelist->file_fullname, 32);
    return filenode->file;	
}
/*******************************************************************************
* Function Name  : filenode_filefullname_CreateByFname
* Description    : filenode_filefullname_CreateByFname, if not have datetime info, create datetime
* Input          : char *string: file name string buf
*                  FILELIST_NAME_T *fname: file_list name ptr (fname)
* Output         : None
* Return         : None
*******************************************************************************/
char* filenode_filefullname_CreateByFname(FILELIST_NODE_T* filenode,FILELIST_NAME_T *fname)
{
    INT32U  file_type;	
	FILELIST_CTRL_T * filelist = (FILELIST_CTRL_T*)filenode->plist;
	if(filenode_filename_CreateByFname(filenode, fname) == NULL)
	{
		deg_Printf("filelist_createNewFileFullName 333333\n");
		return NULL;
	}
	hx330x_str_cpy(filelist->file_fullname,filenode->path);
	hx330x_str_cat(filelist->file_fullname,filenode->file);	
	//debgbuf(filenode->path, 32);
	//debgbuf(filenode->file, 32);
	//debgbuf(filelist->file_fullname, 32);
    return filelist->file_fullname;	
}
/*******************************************************************************
* Function Name  : filenode_filename_CreateNewByIdx
* Description    : filenode_filename_CreateNewByIdx
* Input          : FILELIST_NODE_T* filenode
*                  u32 index
* Output         : None
* Return         : None
*******************************************************************************/
/*char* filenode_filename_CreateNewByIdx(FILELIST_NODE_T* filenode,u32 index)
{
	FILELIST_NAME_T fname;
	filenode_fname_createByIndex(filenode,&fname,index);
	
	if(filelist_filename_CreateByFname(filenode->file,&fname) < 0)
	{
		filenode->file[0] = 0;
		return NULL;
	}
	return filenode->file;

}*/




/*******************************************************************************
* Function Name  : filenode_AddFileByFname
* Description    : add a file to filenode by Fname, if have not datetime info, create it
* Input          : int exp : handle
* Output         : none                                            
* Return         : int
*******************************************************************************/
int filenode_AddFileByFname(FILELIST_NODE_T *filenode,FILELIST_NAME_T *fname, char *name)
{
	FILELIST_CTRL_T * filelist;
	if(fname->index & FILELIST_FLAG_IVL)
		return 0;
	if(filenode == NULL || filenode->valid == 0)
		return -2;
	if(( fname->index & filenode->node_type) != (fname->index & FILENODE_TYPE_MASK))
	//if(!(filenode->node_type &(fname->index & FILENODE_TYPE_MASK)))
		return -3;
	filelist = (FILELIST_CTRL_T *)filenode->plist;
	//deg_Printf("filenode_AddFileByFname:%x,%d,%x\n",filelist,filelist->count,filelist->dirty);
	if(filelist->count >= filelist->list_index_max)
		return -4;
	filelist_listFlush(filelist);
	filelist->name[filelist->count].index 		= fname->index;
	filelist->name[filelist->count].datatime 	= fname->datatime;
	if(filelist->name[filelist->count].datatime == 0)
	{
		DATE_TIME_T 	date_time;
		hal_rtcTimeGetExt(&date_time);
		filelist->name[filelist->count].datatime = ((date_time.year - 1980) << 25) | (date_time.month << 21) | (date_time.day << 16)
													|(date_time.hour << 11) | (date_time.min << 5) | (date_time.sec >> 1);	
	}

	if(filelist->plist_name && name)
	{
		hx330x_str_cpy(&filelist->plist_name->name[filelist->count][0],name);
		if(fname->index & FILELIST_TYPE_DIR)
			hx330x_str_cat(&filelist->plist_name->name[filelist->count][0], "/");
	}
	filelist->count++;

	filenode->count++;
	//deg_Printf("file add :%d\n",filelist->count);
	return 0;
}
/*******************************************************************************
* Function Name  : filenode_Scan
* Description    : scan file node by dir, and add file to both filelist and filenode
* Input          : FILELIST_NODE_T *filenode
* Output         : none                                            
* Return         : int ,file count
*******************************************************************************/
int filenode_Scan(FILELIST_NODE_T *filenode)
{

	FILELIST_NAME_T fname;
	int 	type, path_i, cnt;
	u32     name_len;
	
	if(filenode == NULL || filenode->valid == 0)
		return -1;
	if(!(filenode->node_type & FILELIST_TYPE_SPI))
	{
		FRESULT res;
		FILINFO finfo;
		DIR 	drent;
		deg_Printf("[FileNode] scan : dir:%s\n",filenode->path);
		for(path_i = 0; filenode->path[path_i] != 0; path_i++)
		{
			if(filenode->path[path_i] == '/' && filenode->path[path_i+1] == 0)
				break;
		}
		filenode->path[path_i] = 0; //f_findfirst strip '/'
		if(filenode->node_type & FILELIST_TYPE_DIR)
			res = f_findfirst(&drent,&finfo,filenode->path,"*");
		else
			res = f_findfirst(&drent,&finfo,filenode->path,"*.*");
		cnt = filenode->count;
		while(1)
		{
			hal_wdtClear();
			if(res != FR_OK || finfo.fname[0] == 0)
				break;	
			if(finfo.fname[0] != '.')
			{
				type = filelist_NameCheckSufType(finfo.fname, &name_len);
				if((name_len < FILE_NAME_LEN)&&(type & (filenode->node_type & FILELIST_TYPE_MASK)))
				{
					if(type == FILELIST_TYPE_MP3 || type == FILELIST_TYPE_DIR || type == FILELIST_TYPE_NES)
					{
						fname.index 	= filenode->count | type;
						if(type == FILELIST_TYPE_DIR)
							fname.datatime = finfo.ftime;
						else
							fname.datatime 	= (finfo.fdate<<16)|(finfo.ftime);
					}else
					{
						if(filenode_fname_createByString(filenode,&fname,finfo.fname, (finfo.fdate<<16)|(finfo.ftime)) < 0)
						{
							fname.index = FILELIST_FLAG_IVL; 
						}
					}
					if(fname.index & FILELIST_FLAG_TIME)
						filenode->node_type |= FILELIST_FLAG_TIME;
						
					if(filenode_AddFileByFname(filenode,&fname,finfo.fname) < 0)
						break;
				}
			}	
			res = f_findnext(&drent,&finfo);
		}
		f_closedir(&drent);
		filenode->path[path_i] = '/';		
	}
	else
	{
		deg_Printf("[FileNode] scan : SPI\n");
		NV_DIR dp;
		filenode->path[0] = 0;
		NVRESULT res = nv_dir_readfirst(&dp);
		cnt = filenode->count;
		while(1)
		{
			hal_wdtClear();
			if(res != NV_OK)
				break;	
			fname.index 	= dp.index | FILELIST_TYPE_SPI;
			if(dp.lok_flag)
				fname.index |= FILELIST_FLAG_LOK;
			fname.datatime  = dp.ftime;
			if(filenode_AddFileByFname(filenode,&fname,NULL) < 0)
				break;
			res = nv_dir_readnext(&dp);
		}	
	}


	deg_Printf("[FileNode] scan : total %d\n",filenode->count - cnt);
	
	return filenode->count;	
}

/*******************************************************************************
* Function Name  : filelist_api_CountGet
* Description    : get a filelist's file count
* Input          : int list_num
* Output         : none                                            
* Return         : int : filelist count
*******************************************************************************/
int filenode_api_findfirst(FILELIST_NODE_T *filenode, FILELIST_NAME_T *fname)
{
	int i;
	//FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filenode == NULL || filenode->valid == 0)
	{
		fname->index = FILELIST_FLAG_IVL;
		return -1;
	}
	FILELIST_CTRL_T * filelist = (FILELIST_CTRL_T *)filenode->plist;	
	for(i = 0; i < filelist->count; i++)
	{
		hal_wdtClear();
		if(filelist->name[i].index & (FILELIST_FLAG_LOK|FILELIST_FLAG_IVL)) //not find lock file
			continue;
		if(filenode_fname_check(filenode,&filelist->name[i]) >= 0 )
		{
			fname->index 	= filelist->name[i].index;
			fname->datatime = filelist->name[i].datatime;
			return i;
		}
	}
	fname->index = FILELIST_FLAG_IVL;
	return -1;
}
/*******************************************************************************
* Function Name  : filelist_api_CountGet
* Description    : get a filelist's file count
* Input          : int list_num
* Output         : none                                            
* Return         : int : filelist count
*******************************************************************************/
int filenode_api_findByFname(FILELIST_NODE_T *filenode, FILELIST_NAME_T *fname)
{
	int i;
	//FILELIST_CTRL_T * filelist = filelist_op_check(list);
	if(filenode == NULL || filenode->valid == 0 || (fname->index & FILELIST_FLAG_IVL))
		return -1;
	FILELIST_CTRL_T * filelist = (FILELIST_CTRL_T *)filenode->plist;	
	for(i = 0; i < filelist->count; i++)
	{
		hal_wdtClear();
		if(filelist->name[i].index & (FILELIST_FLAG_LOK|FILELIST_FLAG_IVL)) //not find lock file
			continue;
		if((filelist->name[i].index & (FILENODE_TYPE_MASK|FILELIST_INDEX_MASK)) == (fname->index &(FILENODE_TYPE_MASK|FILELIST_INDEX_MASK)))
		{
			return i;
		}
	}
	return -1;
}


