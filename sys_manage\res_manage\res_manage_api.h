/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef RES_MANAGE_API_H
#define RES_MANAGE_API_H


#include "res_ascii/inc/res_ascii_api.h"
#include "res_font/inc/res_font_api.h"
#include "res_icon/inc/res_icon_api.h"
#include "res_image/inc/res_image_api.h"
#include "res_music/inc/res_music_api.h"
//--------------id type---------------------------
#define  INVALID_RES_ID  					0x7fffffff
#define  RES_ID_MASK                 		0x00ffffff
#define  RES_ID_CHECK               	 	0xff000000
#define  RES_ID_TYPE_ICON            		0x80000000
#define  RES_ID_TYPE_STR             		0x81000000
#define  RES_ID_TYPE_ASCII           		0x82000000
#define  RES_ID_TYPE_LAYOUT          		0x83000000
#define  RES_ID_TYPE_PROGRESSBAR     		0x84000000
#define  RES_ID_TYPE_VIEW            		0x85000000
#define  RES_ID_TYPE_DIALOG          		0x86000000
#define  RES_ID_TYPE_MENU            		0x87000000

#define  RES_IDNUM_GET(id)					((id)&RES_ID_MASK)
#define  RES_IDTYPE_GET(id)					((id)&RES_ID_CHECK)

#define	 ICON_ID_MAKE(id)					((id)|RES_ID_TYPE_ICON)
#define  STR_ID_MAKE(id)					((id)|RES_ID_TYPE_STR)
#define  ASCII_ID_MAKE(id)					((id)|RES_ID_TYPE_ASCII)
#define  LAYOUT_ID_MAKE(id)					((id)|RES_ID_TYPE_LAYOUT)
#define  PROGRESSBAR_ID_MAKE(id)			((id)|RES_ID_TYPE_PROGRESSBAR)
#define  VIEW_ID_MAKE(id)					((id)|RES_ID_TYPE_VIEW)
#define  DIALOG_ID_MAKE(id)					((id)|RES_ID_TYPE_DIALOG)
#define  MENU_ID_MAKE(id)					((id)|RES_ID_TYPE_MENU)
#define  RAM_ID_MAKE(addr)					((u32)addr)
#define  RAM_ID_TO_ADDR(id)					((u8*)id)

#define  RES_ID_IS_ICON(id)					(((id)&RES_ID_CHECK) == RES_ID_TYPE_ICON)
#define  RES_ID_IS_STR(id)					(((id)&RES_ID_CHECK) == RES_ID_TYPE_STR)
#define  RES_ID_IS_ASCII(id)				(((id)&RES_ID_CHECK) == RES_ID_TYPE_ASCII)
#define  RES_ID_IS_LAYOUT(id)				(((id)&RES_ID_CHECK) == RES_ID_TYPE_LAYOUT)
#define  RES_ID_IS_PROGRESSBAR(id)			(((id)&RES_ID_CHECK) == RES_ID_TYPE_PROGRESSBAR)
#define  RES_ID_IS_VIEW(id)					(((id)&RES_ID_CHECK) == RES_ID_TYPE_VIEW)
#define  RES_ID_IS_DIALOG(id)				(((id)&RES_ID_CHECK) == RES_ID_TYPE_DIALOG)
#define  RES_ID_IS_MENU(id)					(((id)&RES_ID_CHECK) == RES_ID_TYPE_MENU)

#define  RES_ID_IS_RAM(id)					(((id)&0x80000000) == 0)		//SDRAM or RAM


//RES ASCII FONT 
#define  RES_FONT_NUM1         				0x00	 
#define  RES_FONT_NUM2         				0x01  
#define  RES_FONT_NUM3         				0x02
#define  RES_FONT_NUM4         				0x03  
#define  RES_FONT_NUM_MAX	   				RES_FONT_NUM4
//#define  RES_FONT_NUM_DEFAULT				RES_FONT_NUM1
	
#define  CHAR_INTER_VAL      				0	
#define  RES_FONT_PAN         				0x10   // font with transfer panel
#define  RES_FONT_BRD         				0x20   // font with border


/*******************************************************************************
* Function Name : res_GetStringInfor
* Description   :       
* Input         : u32 id,u16* width,u16* height,u8 font  
* Output        : 
* Return        : strnum  
*******************************************************************************/
u32 res_GetStringInfor(u32 id,u16* width,u16* height,u8 font);

/*******************************************************************************
* Function Name : res_GetCharInfor
* Description   :       
* Input         : u32 id,u8 num,u16* width,u16* height,u8 font,u8* special 
* Output        : 
* Return        : strnum  
*******************************************************************************/
u8* res_GetCharInfor(u32 id,u8 num,u16* width,u16* height,u8 font,u8* special);


#endif
