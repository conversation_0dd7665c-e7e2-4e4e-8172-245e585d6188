/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef RES_MUSIC_API_H
#define RES_MUSIC_API_H

extern const u8 res_key_music[1820];

/*******************************************************************************
* Function Name  : res_music_end
* Description    : res_music_end: music end
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void res_music_end(void);
/*******************************************************************************
* Function Name  : res_music_start
* Description    : res_music_start: start music
* Input          : INT16U idx : resource id
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
int res_music_start(INT16U idx, INT8U wait, INT8U volume);
/*******************************************************************************
* Function Name  : res_keysound_init
* Description    : res_keysound_init: keysound start
* Input          : INT8U load_auto,INT16U idx,INT8U volume
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void res_keysound_init(INT8U load_auto,INT16U idx,INT8U volume);
/*******************************************************************************
* Function Name  : res_keysound_play
* Description    : res_keysound_play: keysound start
* Input          : none
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void res_keysound_play(void);
/*******************************************************************************
* Function Name  : res_keysound_stop
* Description    : res_keysound_stop: keysound start
* Input          : none
* Output         : none                                            
* Return         : int 0;
*******************************************************************************/
void res_keysound_stop(void);
#endif
