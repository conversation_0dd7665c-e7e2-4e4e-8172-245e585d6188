/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/

#ifndef UI_WIN_ITEM_MENU_EX_H
#define UI_WIN_ITEM_MENU_EX_H

typedef struct
{
	uiWidgetObj widget;
	ICON_DRAW_T	image;
	ICON_DRAW_T	imageSelect;
	winHandle   hImage;
	winHandle   hStr;
	winHandle   hImageSel;
	winHandle   hStrSel;
	u16 		select;
	uiColor 	color;
	uiColor 	selectColor;	
}uiItemMenuExObj;
/*******************************************************************************
* Function Name  : uiItemMenuExProc
* Description    : uiItemMenuExProc
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiItemCreateMenuItemEx(s16 x0,s16 y0,u16 width,u16 height,u16 style, uiColor rimColor, u8 round_type);

#endif
