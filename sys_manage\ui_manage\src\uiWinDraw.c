/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

ALIGNED(4) UIDRAW_CTRL_T ui_draw_ctrl;
/*******************************************************************************
* Function Name  : uiWinDrawInit
* Description    : uiWinDrawInit
* Input          : lcdshow_frame_t * drawframe
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawInit(lcdshow_frame_t * drawframe)
{
    if(drawframe)
	{
		drawframe->data_size	= drawframe->buf_size;
		ui_draw_ctrl.drawframe  = drawframe;
		ui_draw_ctrl.width  	= drawframe->w;
		ui_draw_ctrl.height 	= drawframe->h;
		ui_draw_ctrl.bufStart   = drawframe->y_addr;
		ui_draw_ctrl.bufEnd     = drawframe->y_addr + drawframe->buf_size;
	}
   
}
/*******************************************************************************
* Function Name  : app_taskRegister
* Description    : app_taskRegister
* Input          : taskID id,sysTask* task
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawUpdate(void)
{	
	if(ui_draw_ctrl.drawframe)
	{
		hx330x_sysDcacheWback((u32)ui_draw_ctrl.bufStart,ui_draw_ctrl.drawframe->buf_size);
		hal_lcdUiKickWait((lcdshow_frame_t *)ui_draw_ctrl.drawframe);
		hal_lcdUiSetBufferWaitDone(UI_LAYER0);
		hal_lcdUiKick((lcdshow_frame_t *)ui_draw_ctrl.drawframe);
	}
}
/*******************************************************************************
* Function Name  : uidraw_Service
* Description    : uidraw_Service
* Input          : taskID id,sysTask* task
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static u16 uiWinDraw_calPhoneticPos(u8* srcAddr,u16 charW,u16 charH)
{
	u8 findBlack = 0;
	u16 i,j;
	u8* curAddr;
	charW = (charW+7)>>3;
	for(i = charH-3;i>0;i--)
	{
		curAddr = srcAddr + i*charW;
		for(j=0;j<charW;j++)
		{
			findBlack = *curAddr++;
			if(findBlack)
				break;
		}
		if(findBlack)
			break;
	}
	if(i>4)
		i=i-4;
	return i;
}
/*******************************************************************************
* Function Name  : uiWinDrawStartAddrCal
* Description    : uiWinDrawStartAddrCal
* Input          : s32 scan_mode, s16 sx, s16 sy
* Output         : none                                            
* Return         : none 
*******************************************************************************/

#define uiWinDrawStartAddrCal(scan_mode, sx, sy)	(ui_draw_ctrl.bufStart + (sy)*ui_draw_ctrl.width + (sx))
#define uiWinDrawAddrReCal(scan_mode,baseAddr,x,y)	(baseAddr + (y)*ui_draw_ctrl.width + (x))

/*******************************************************************************
* Function Name  : uiWinDrawLine
* Description    : uiWinDrawLine
* Input          : LINE_DRAW_T *line
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawLine(LINE_DRAW_T *line)
{
	u16 i,j;

	uiColor *dest;
	uiColor *dest_start;      		//start point of each block
	u16 block_width,block_height;  //the size of block which is needed to move
	s32 hdeta,wdeta,intg,cnt;
	if(ui_draw_ctrl.bufStart == NULL)
		return;

	if(!(line->style & WIN_NOT_ZOOM))
	{
		line->sx 	= USER_Rx(line->sx);
		line->sy 	= USER_Ry(line->sy);
		line->ex 	= USER_Rx(line->ex);
		line->ey 	= USER_Ry(line->ey);
		line->width = USER_Rw(line->width);
	}
	if(line->sx >= USER_UI_WIDTH)
		line->sx = USER_UI_WIDTH - 1;
	if(line->sy >= USER_UI_HEIGHT)
		line->sy = USER_UI_HEIGHT - 1;
	if(line->ex >= USER_UI_WIDTH)
		line->ex = USER_UI_WIDTH - 1;	
	if(line->ey >= USER_UI_HEIGHT)
		line->ey = USER_UI_HEIGHT - 1;	

	block_width 	= line->width;
	block_height	= line->ey - line->sy;
	hdeta = 0;
	wdeta = 0;
	intg = 0;
	if(block_height == 0)
	{
		block_height = line->width;
		block_width  = line->ex - line->sx;
	}
    else
    {
		cnt = intg = line->ex - line->sx;
		if(intg < 0)
			cnt = 0 - cnt;
		if(cnt)
		{
			wdeta =  cnt/block_height;
			if(wdeta == 0)
				hdeta = (block_height*10)/cnt;
			else
				wdeta =  (cnt*10)/block_height;
		}
    }
	//deg_Printf("line:[%d, %d, %d, %d, %d]\n", line->sx, line->sy, line->ex, line->ey, line->width);
	//deg_Printf("line:[%d, %d] wdeta:%d, hdeta:%d, intg:%d\n",block_width,block_height,wdeta, hdeta, intg);
	dest_start =  uiWinDrawStartAddrCal(lcdRotate, line->sx, line->sy);
	if(dest_start == NULL)
		return;
    cnt = 0;
	for(i=0;i<block_height;i++)
	{
		dest = dest_start;
		for(j=0;j < block_width;j++)
		{
			if(dest >= ui_draw_ctrl.bufStart && dest < ui_draw_ctrl.bufEnd)
				*dest = (uiColor)line->fill_color;
			else
				deg_err("memeory operation out of bounds!\n");
			dest = uiWinDrawAddrReCal(lcdRotate, dest, 1, 0);
		}
		dest_start = uiWinDrawAddrReCal(lcdRotate, dest_start, 0, 1);
		if(hdeta || wdeta)
		{
			cnt += 10;
			if(hdeta)
			{
				if(intg<0)
				{
					dest_start = uiWinDrawAddrReCal(lcdRotate, dest_start, -cnt/hdeta, 0);
				}
				else
				{
					dest_start = uiWinDrawAddrReCal(lcdRotate, dest_start, cnt/hdeta, 0);
				}
				if(cnt >= hdeta)
					cnt -=hdeta;
			}
			else
			{
				if(intg < 0)
				{
					dest_start = uiWinDrawAddrReCal(lcdRotate, dest_start, -cnt/wdeta, 0);
				}
				else
				{
					dest_start = uiWinDrawAddrReCal(lcdRotate, dest_start, cnt/wdeta, 0);	
				}
				if(cnt >= wdeta)
					cnt -= wdeta;
			}
		}
	}
}
/*******************************************************************************
* Function Name  : res_draw_Rect
* Description    : res_draw_Rect
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawRect(uiRect* rect,uiColor color)
{
	s16 x,y;
	uiColor* dest;
	uiColor* destStart;
	if(ui_draw_ctrl.bufStart == NULL)
		return;

	if(rect->x0 >= USER_UI_WIDTH)
		rect->x0 = USER_UI_WIDTH - 1;
	if(rect->y0 >= USER_UI_HEIGHT)
		rect->y0 = USER_UI_HEIGHT - 1;
	if(rect->x1 >= USER_UI_WIDTH)
		rect->x1 = USER_UI_WIDTH - 1;
	if(rect->y1 >= USER_UI_HEIGHT)
		rect->y1 = USER_UI_HEIGHT - 1;
	destStart = uiWinDrawStartAddrCal(lcdRotate,rect->x0, rect->y0);
	if(destStart == NULL)
		return;
	
	for(y = rect->y0; y <= rect->y1; y++)
	{
		dest = destStart;
		for(x = rect->x0; x <= rect->x1; x++)
		{
			*dest = color;
			dest = uiWinDrawAddrReCal(lcdRotate, dest,1, 0);		
		}
		destStart = uiWinDrawAddrReCal(lcdRotate, destStart, 0, 1);
	}
}
/*******************************************************************************
* Function Name  : uiWinRoundWidthCal
* Description    : uiWinRoundWidthCal
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static int uiWinRoundWidthCal(s16 r, s16 y, s16 * skip_width, s16 * rim_width)
{
	s16 x_min, x_max;
	int a,b;
	int di;
	x_min =  r;
	x_max =  0;
	a  = 0;
	b  = r;
	di = 3 -  r * 2;
	//deg_Printf("bgcolor:%x, foncolor:%x, x0:%d, y0:%d, b:%d, di:%d\n", bgcolor,fontcolor, x0, y0, b, di);
	while(a<=b)
	{
		if(y == (r-b))
		{
			if(x_min > (r-a))
			{
				x_min = r - a;
			}
			if(x_max < (r-a))
			{
				x_max = r - a;
			}
			//deg_Printf("11 y = %d, x = %d, x_min = %d, x_max = %d\n", y, r-a, x_min, x_max);
		}
		if(y == (r-a))
		{
			if(x_min > (r-b))
			{
				x_min = r - b;
			}	
			if(x_max < (r-b))
			{
				x_max = r - b;
			}	
			//deg_Printf(" 22 y = %d, x = %d, x_min = %d, x_max = %d\n", y, r-b, x_min, x_max);		
		}		
		a++;
		// 计算下一个点的参数值
		if(di<0)di +=4*a+6;
		else
		{
			di+=10+4*(a-b);
			b--;
		}
	}
	if(skip_width)
		*skip_width = x_min;
	if(rim_width)
		*rim_width = x_max - x_min + 1;
	//deg_Printf("[y = %d, x = %d]\n", y, x);
	return 0;
}
/*******************************************************************************
* Function Name  : uiWinRoundWidthCal
* Description    : uiWinRoundWidthCal
* Input          : uiRect* rect,uiColor color
				   round_side: 0 top, 1: bot
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiWinDrawRound(uiRect* rect,uiRect* round_rect, uiColor font_color, u8 round_side)
{
	s16 skip_width, rim_width;
	s16 skip_width_l, rim_width_l;
	s16 skip_width_r, rim_width_r;
	s16 x, y;
	s16 y0, y1;
	uiColor* dest, *destStart; 
	uiColor fill_color, rim_color, round_rim_color;
	u8      round_side_flag = 0;
	u8      rim_flag;
	rim_color = (rect->rimColor == INVALID_COLOR) ? font_color : rect->rimColor ;
	round_rim_color = (round_rect->rimColor == INVALID_COLOR) ? font_color : round_rect->rimColor ;
	//deg_Printf("uiWinDrawRound:%d\n", round_side);
	if(round_side == 0) //top
	{
		y0 = rect->y0;
		y1 = hx330x_min(rect->y1, round_rect->y0 + round_rect->radius);
		if(round_rect->round_type & ROUND_LEFTTOP)
		{
			round_side_flag |= BIT(0); //left
		}
		if(round_rect->round_type & ROUND_RIGHTTOP)
		{
			round_side_flag |= BIT(1); //right
		}
	}else //BOT
	{
		y0 = hx330x_max(rect->y0, round_rect->y1 - round_rect->radius);
		y1 = rect->y1;
		if(round_rect->round_type & ROUND_LEFTBOT)
		{
			round_side_flag |= BIT(0); //left
		}
		if(round_rect->round_type & ROUND_RIGHTBOT)
		{
			round_side_flag |= BIT(1); //right
		}
	}
	destStart = uiWinDrawStartAddrCal(lcdRotate,rect->x0, y0);	
	if(destStart == NULL)
	{
		return;
	}
	for(y = y0; y <= y1; y++)
	{
		dest = destStart;
		if(round_side == 0)
			uiWinRoundWidthCal(round_rect->radius, y - round_rect->y0, &skip_width, &rim_width);
		else
			uiWinRoundWidthCal(round_rect->radius, round_rect->y1 - y, &skip_width, &rim_width);
		rim_flag = 0;
		if( round_side == 0)
		{
			if(y < (y0 + RECT_RIM_WITH))
				rim_flag |= BIT(0);
			if(y < (round_rect->y0 + RECT_RIM_WITH) && y >= round_rect->y0)
			{
				rim_flag |= BIT(1);
			}

		}else
		{
			if(y > (y1 - RECT_RIM_WITH))
			{
				rim_flag |= BIT(0);
			}
			if(y > (round_rect->y1 - RECT_RIM_WITH) && y <= round_rect->y1)
			{
				rim_flag |= BIT(1);
			}
		}

		if(round_side_flag & BIT(0))
		{
			skip_width_l = skip_width;
			rim_width_l	 = rim_width;
		}else
		{
			skip_width_l = 0;
			rim_width_l	 = RECT_RIM_WITH;		
		}
		if(round_side_flag & BIT(1))
		{
			skip_width_r = skip_width;
			rim_width_r	 = rim_width;
		}else
		{
			skip_width_r = 0;
			rim_width_r	 = RECT_RIM_WITH;
		}
		//deg_Printf("uiWinDrawRound[%d][%d,%d,  %d, %d]\n", y, skip_width_l, rim_width_l,skip_width_r,rim_width_r );
		//deg_Printf("RECT[%d, %d] ROUND[%d, %d]\n", rect->x0, rect->x1, round_rect->x0, round_rect->x1);
		for(x = rect->x0; x <= rect->x1; x++)
		{
			if(x < (round_rect->x0 + skip_width_l) || x > (round_rect->x1 - skip_width_r))
			{
				fill_color = INVALID_COLOR;
			}else if(x < (round_rect->x0 + skip_width_l + rim_width_l) || x > (round_rect->x1 - skip_width_r - rim_width_r))
			{
				fill_color = round_rim_color;
			}else
			{
				if(rim_flag & BIT(1))
				{
					fill_color = round_rim_color;
				}else if(rim_flag & BIT(0))
				{
					fill_color = rim_color;
				}else
				{
					fill_color = font_color;
				}	

			}
			if(fill_color != INVALID_COLOR)
				*dest = fill_color;
			dest = uiWinDrawAddrReCal(lcdRotate, dest,1, 0);
		}
		
		destStart = uiWinDrawAddrReCal(lcdRotate, destStart, 0, 1);	
	}	
	
}
/*******************************************************************************
* Function Name  : res_draw_Rect
* Description    : res_draw_Rect
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawRoundRectWithRim(uiRect* rect,uiRect* round_rect,uiColor font_color)
{
	s16 x,y;
	uiColor* dest;
	uiColor* destStart;
	uiColor fill_color;
	uiColor rim_color;
	uiColor round_rim_color;
	u8      rim_flag, rim_side_flag;
	uiRect  draw_rect;
	if(ui_draw_ctrl.bufStart == NULL)
		return;
	if(rect == NULL || round_rect == NULL)
		return;
	if(rect->rimColor == INVALID_COLOR /*&& round_rect->rimColor == INVALID_COLOR*/ &&  font_color == INVALID_COLOR)
	{
		return;
	}
	if(rect->rimColor == INVALID_COLOR)
	{
		rim_color = font_color;
	}else
	{
		rim_color = rect->rimColor;
	}
	if(round_rect->rimColor == INVALID_COLOR)
	{
		round_rim_color = font_color;
	}else
	{
		round_rim_color = round_rect->rimColor;
	}
	if(rect->x0 >= USER_UI_WIDTH)
		rect->x0 = USER_UI_WIDTH - 1;
	if(rect->y0 >= USER_UI_HEIGHT)
		rect->y0 = USER_UI_HEIGHT - 1;
	if(rect->x1 >= USER_UI_WIDTH)
		rect->x1 = USER_UI_WIDTH - 1;
	if(rect->y1 >= USER_UI_HEIGHT)
		rect->y1 = USER_UI_HEIGHT - 1;
	draw_rect.x0 = rect->x0;
	draw_rect.x1 = rect->x1;
	draw_rect.y0 = rect->y0;
	draw_rect.y1 = rect->y1;
	rim_side_flag = 0x03;
	//deg_Printf("ROUND[%d,%d,%d,%d],[%d,%x,%x]\n", round_rect->x0, round_rect->x1, round_rect->y0, round_rect->y1,round_rect->radius,round_rect->round_type, round_rect->rimColor);
	//deg_Printf("RECT[%d,%d,%d,%d],[%x,%x]\n", rect->x0, rect->x1, rect->y0, rect->y1,rect->rimColor, font_color);
	if(round_rect->radius != 0) //处理round部分
	{
		destStart = uiWinDrawStartAddrCal(lcdRotate,rect->x0, rect->y0);
		if(destStart == NULL)
			return;
		if( rect->y0 <= (round_rect->y0 + round_rect->radius) &&  rect->y0 >= round_rect->y0)
		{
			uiWinDrawRound(rect, round_rect, font_color, 0);
			draw_rect.y0 = hx330x_min(rect->y1, round_rect->y0 + round_rect->radius) + 1;
			rim_side_flag &=~0x01;
		}
		if( rect->y1 >= (round_rect->y1 - round_rect->radius) && rect->y1 <= round_rect->y1)
		{
			uiWinDrawRound(rect, round_rect, font_color, 1);
			draw_rect.y1 = hx330x_max(rect->y0, round_rect->y1 - round_rect->radius) - 1;
			rim_side_flag &=~0x02;
		}	
	}
	destStart = uiWinDrawStartAddrCal(lcdRotate,draw_rect.x0, draw_rect.y0);
	if(destStart == NULL)
		return;
	fill_color = font_color;
	for(y = draw_rect.y0; y <= draw_rect.y1; y++)
	{
		dest = destStart;
		rim_flag = 0;
		if((rim_side_flag & 0x01))
		{
			if(y >= round_rect->y0 && y < (round_rect->y0 + RECT_RIM_WITH))
			{
				fill_color = round_rim_color;
				rim_flag = 1;
			}
			else if(y < (draw_rect.y0 + RECT_RIM_WITH))
			{
				fill_color = rim_color;
				rim_flag = 1;
			}
		}
		if((rim_side_flag & 0x02))
		{
			if(y > (round_rect->y1 - RECT_RIM_WITH)  && y <= (round_rect->y1))
			{
				fill_color = round_rim_color;
				rim_flag = 1;
			}
			else if(y > (draw_rect.y1 - RECT_RIM_WITH))
			{
				fill_color = rim_color;
				rim_flag = 1;
			}		
		}
				
		//if(rim_flag)
		//{
		//	deg_Printf("fill_color:%x,%x,%x,%x\n", fill_color, font_color,rim_color,rect->rimColor );
		//}
		for(x = draw_rect.x0; x <= draw_rect.x1; x++)
		{
			if(rim_flag == 0)
			{
				if(x < (rect->x0 + RECT_RIM_WITH) || x > (rect->x1 - RECT_RIM_WITH))
				{
					fill_color = rim_color;
				}else
				{
					fill_color = font_color;
				}
			}	
			if((round_rect->rimColor != INVALID_COLOR) && (x == round_rect->x0 || x == round_rect->x1))
			{
				*dest = round_rect->rimColor;	
			}else if(fill_color != INVALID_COLOR)
				*dest = fill_color;
			dest = uiWinDrawAddrReCal(lcdRotate, dest,1, 0);		
		}
		destStart = uiWinDrawAddrReCal(lcdRotate, destStart, 0, 1);	
	}
}
/*******************************************************************************
* Function Name  : uiWinDrawPoint
* Description    : uiWinDrawPoint
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawPoint(uiRect* rect,int x,int y,int cnt, uiColor color)
{
	//deg_Printf("11 [%d,%d,%d]\n", x,y,cnt);
	if(y < rect->y0 || y > rect->y1)
	{
		return;
	}
	if(x > rect->x1)
	{
		return;
	}
	if(x < rect->x0)
	{
		x = rect->x0;
	}
	

	if(cnt == 0)
	{
		cnt = 1;
	}
	if((rect->x1 - x + 1) < cnt)
		cnt = (rect->x1 - x + 1);
	//deg_Printf("[%d,%d,%d]\n", x,y,cnt);
	uiColor* dest = uiWinDrawStartAddrCal(lcdRotate,x, y);	
	while(cnt--)
	{
		*dest = color;
		dest = uiWinDrawAddrReCal(lcdRotate, dest, 1, 0);
	}

	
}
/*******************************************************************************
* Function Name  : uiWinDrawCircle
* Description    : uiWinDrawCircle
* Input          : uiRect* rect,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawCircle(uiCycle *cycle,uiColor bgcolor, uiColor fontcolor)
{
	int a,b;
	int di;
	int x0, y0;
	//uiColor* dest;
	//uiColor* destStart;
	if(ui_draw_ctrl.bufStart == NULL)
		return;
	if(cycle->radius <= 0)
	{
		return;
	}

	if(cycle->x_center > cycle->rect.x1)
	{
		cycle->x_center = cycle->rect.x1;
	}
	if(cycle->y_center > cycle->rect.y1)
	{
		cycle->y_center = cycle->rect.y1;
	}	
	if(bgcolor != INVALID_COLOR)
		uiWinDrawRect(&cycle->rect,bgcolor);
	if(fontcolor == INVALID_COLOR)
	{
		return;
	}
	x0 = cycle->x_center;
	y0 = cycle->y_center;
	a  = 0;
	b  = cycle->radius;
	di = 3 -  cycle->radius * 2;
	//deg_Printf("bgcolor:%x, foncolor:%x, x0:%d, y0:%d, b:%d, di:%d\n", bgcolor,fontcolor, x0, y0, b, di);
	while(a<=b)
	{
		uiWinDrawPoint(&cycle->rect,x0-a,y0-b,2*a,fontcolor);             //2
		//uiWinDrawPoint(&cycle->rect,x0+a,y0-b,fontcolor);             //5

		uiWinDrawPoint(&cycle->rect,x0-a,y0+b,2*a, fontcolor);             //1
		//uiWinDrawPoint(&cycle->rect,x0+a,y0+b,fontcolor);             //6
		
		
		uiWinDrawPoint(&cycle->rect,x0-b,y0-a,2*b, fontcolor);             //7
 		//uiWinDrawPoint(&cycle->rect,x0+b,y0-a, fontcolor);             //0

		uiWinDrawPoint(&cycle->rect,x0-b,y0+a,2*b,fontcolor);
		//uiWinDrawPoint(&cycle->rect,x0+b,y0+a,fontcolor);             //4
		

 		
  		
		a++;
		// 计算下一个点的参数值
		if(di<0)di +=4*a+6;
		else
		{
			di+=10+4*(a-b);
			b--;
		}
	}
}
/*******************************************************************************
* Function Name  : res_draw_RectNotInter
* Description    : res_draw_RectNotInter
* Input          : uiRect* father,uiRect* child,uiColor color
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiWinDrawRectNotInter(uiRect* father,uiRect* child,uiColor color)
{
	uiRect invalidRect;
	if(father->x0 < child->x0)
	{
		invalidRect.x0 = father->x0;
		invalidRect.x1 = child->x0-1;
		invalidRect.y0 = father->y0;
		invalidRect.y1 = father->y1;
		uiWinDrawRect(&invalidRect,color);
	}
	if(father->y0 < child->y0)
	{
		invalidRect.x0 = hx330x_max(father->x0,child->x0);
		invalidRect.x1 = father->x1;
		invalidRect.y0 = father->y0;
		invalidRect.y1 = child->y0-1;
		uiWinDrawRect(&invalidRect,color);
	}
	if(father->x1 > child->x1)
	{
		invalidRect.x0 = child->x1+1;
		invalidRect.x1 = father->x1;
		invalidRect.y0 = hx330x_max(father->y0,child->y0);
		invalidRect.y1 = father->y1;
		uiWinDrawRect(&invalidRect,color);
	}
	if(father->y1 > child->y1)
	{
		invalidRect.x0 = hx330x_max(father->x0,child->x0);
		invalidRect.x1 = hx330x_min(father->x1,child->x1);
		invalidRect.y0 = child->y1+1;
		invalidRect.y1 = father->y1;
		uiWinDrawRect(&invalidRect,color);
	}
}
/*******************************************************************************
* Function Name  : uiWinDrawIcon
* Description    : res_draw_Icon
* Input          : uiRect* winRect,uiRect* drawRect,resID id,u8 alignment,uiColor bgColor
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawIcon(uiRect* winRect,uiRect* drawRect,ICON_DRAW_T *icon)
{
	u16 iconWidth  	= 0;
	u16 iconHeight 	= 0;
	uiColor* resAddr;
	uiColor *dest;
	uiColor *dest_start;      		//start point of each block
	uiColor* resData;
	u16 i,j,width,height;
	uiRect resRect;
	s16 resX,resY;
	uiColor transfer_Color;

	if(ui_draw_ctrl.bufStart == NULL)
		return;	
	resAddr = (uiColor*)res_icon_GetAddrAndSize(icon->id,&iconWidth,&iconHeight);
	if(resAddr == NULL)
		return;

	if(icon->iconAlign & ALIGNMENT_LEFT)
	{
		resX = resRect.x0 = winRect->x0;
		resRect.x1 = resRect.x0 + iconWidth - 1;
	}
	else if(icon->iconAlign & ALIGNMENT_RIGHT)
	{
		resRect.x1 = winRect->x1;
		resX = resRect.x0 = winRect->x1 - iconWidth + 1;
	}
	else
	{
		resX = resRect.x0 = (winRect->x0 + winRect->x1 - iconWidth)/2;
		resRect.x1 = resRect.x0 + iconWidth - 1;
	}
	resY = resRect.y0 = (winRect->y0 + winRect->y1 - iconHeight)/2;
	resRect.y1 = resRect.y0 + iconHeight - 1;
	
	if(uiWinOverlapCmp(&resRect,drawRect) < 0)
	{
		if(icon->bgColor != INVALID_COLOR)
			uiWinDrawRect(drawRect,icon->bgColor);
		return;
	}
	if(icon->bgColor != INVALID_COLOR)
		uiWinDrawRectNotInter(drawRect,&resRect,icon->bgColor);
	uiWinInterSection(drawRect,drawRect,&resRect);
	resRect.x0 = drawRect->x0 - resX;  //
	resRect.y0 = drawRect->y0 - resY;
	resRect.x1 = drawRect->x1 - resX;
	resRect.y1 = drawRect->y1 - resY;
	dest_start = uiWinDrawStartAddrCal(lcdRotate,drawRect->x0, drawRect->y0);
	if(dest_start == NULL)
		return;

	width  = resRect.x1 - resRect.x0 + 1;
	height = resRect.y1 - resRect.y0 + 1;
	resAddr += iconWidth*resRect.y0 + resRect.x0*sizeof(uiColor);
	transfer_Color = (uiColor)res_icon_GetTColor(icon->id);
	//deg_Printf("id:%x, bgColor:%x, transparentColor:%x\n", icon->id,icon->bgColor,transfer_Color);
	for(i=0;i<height;i++)
	{	
   		resData = res_iconGetData(icon->id,(u32)resAddr,width);
		resAddr += iconWidth;
    	if(resData == NULL)
			break;
		dest = dest_start;
		for(j=0;j<width;j++)
		{
			if(*resData != transfer_Color)
			{
				if(icon->iconColor != INVALID_COLOR)
				{
					*dest = icon->iconColor;
				}else
				{
					*dest = *resData;
				}	

			}
					
			else
			{
				if(icon->bgColor != INVALID_COLOR)
					*dest = icon->bgColor;
			}
			dest = uiWinDrawAddrReCal(lcdRotate, dest,1, 0);		
			resData++;
		}
		dest_start = uiWinDrawAddrReCal(lcdRotate, dest_start,0, 1);	
	}
}
/*******************************************************************************
* Function Name  : uiWinDrawChar
* Description    : uiWinDrawChar
* Input          : u8* destAddr,u8* srcAddr,uiRect* srcRect,u16 charW,u16 charH,uiColor fontColor,uiColor bgColor,uiColor rimColor
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiWinDrawChar(u8* destAddr,u8* srcAddr,CHAR_DRAW_T *char_draw)
{
	u8 temp,mask,lastDraw = 2;
	u16 i,j,drawLine;
	u8* drawAddr,*upPointAddr,*charAddr,*temAddr;
	u16 destW, destH;
	charAddr = srcAddr;
	destW = (char_draw->charW + 7)>>3; 
	srcAddr +=  char_draw->charRect.y0 * destW;
	destH = char_draw->charRect.y1 - char_draw->charRect.y0 + 1;
	//for(i = 0;i < char_draw->charH; i++)
	for(i = 0;i < destH; i++)
	{
		drawLine = 0;
		drawAddr = destAddr;
		//for(j=0;j < char_draw->charW; j++)
		for(j=0;j < destW; j++)
		{
			if(srcAddr - destW >= charAddr)
				upPointAddr = srcAddr - destW;
			else
				upPointAddr = NULL;
			if(i < char_draw->charH)
			{
				temp = *srcAddr++;
			}else
			{
				temp = 0;
			}
			
			for(mask=0; mask < 8; mask++)
			{
				if(drawLine >= char_draw->charRect.x0 && drawLine <= char_draw->charRect.x1)
				{
					if(temp&(0x80>>mask))
					{
						if(char_draw->rimColor != INVALID_COLOR)
						{
							if(lastDraw == 0)
							{
								temAddr = uiWinDrawAddrReCal(lcdRotate, destAddr,-1, 0);
								//if(temAddr>=res_draw_ctrl.bufStart && temAddr<res_draw_ctrl.bufEnd)
									*temAddr = char_draw->rimColor;
							}
							if(upPointAddr)
							{
								if((*upPointAddr&(0x80>>mask)) == 0)
								{
									temAddr = uiWinDrawAddrReCal(lcdRotate, destAddr, 0, -1);
									//if(temAddr>=res_draw_ctrl.bufStart && temAddr<res_draw_ctrl.bufEnd)
										*temAddr = char_draw->rimColor;									
								}
							}
						}
					 	*destAddr = char_draw->fontColor;
						lastDraw = 1;
					}
					else
					{
						if(char_draw->rimColor != INVALID_COLOR)
						{
							if(lastDraw == 1)
								*destAddr = char_draw->rimColor;
							else if(upPointAddr&&(*upPointAddr&(0x80>>mask)))
								*destAddr = char_draw->rimColor;
							else
							{
								if(char_draw->bgColor != INVALID_COLOR)
									*destAddr = char_draw->bgColor;
							}
						}
						else
						{
							if(char_draw->bgColor != INVALID_COLOR)
								*destAddr = char_draw->bgColor;
						}
						lastDraw = 0;
					}
					destAddr = uiWinDrawAddrReCal(lcdRotate, destAddr, 1, 0);
				}
				drawLine++;
			}
		}
		destAddr = uiWinDrawAddrReCal(lcdRotate, drawAddr, 0, 1);
	}
}
/*******************************************************************************
* Function Name  : uiWinDrawString
* Description    : res_draw_Char
* Input          : uiRect* winRect,uiRect* invalidRect,STRING_DRAW_T *string_draw
* Output         : none                                            
* Return         : none 
*******************************************************************************/
void uiWinDrawString(uiRect* winRect,uiRect* invalidRect,STRING_DRAW_T *string_draw)
{
	u32 strNum;	
	s16 resX,resY;
	u16 i,width;
	u8* destAddr;
	u8* lastAddr;
	u8* secSpecialAddr;
	u8* lastSecSpecialAddr;
	u8* lowPhoneticAddr;
	u8* lastLowPhoneticAddr;
	u8* charBuff;
	uiRect drawRect, resRect;
	CHAR_DRAW_T char_draw;

	u8 special,lastSpecial=0;
	
	strNum = res_GetStringInfor(string_draw->id,&char_draw.charW,&char_draw.charH,string_draw->font);
	if(strNum==0)
	{
		//deg_Printf("find string error!!!,0x%x\n",string_draw->id);
		return;
	}

	drawRect.x0 = invalidRect->x0;
	drawRect.y0 = invalidRect->y0;
	drawRect.x1 = invalidRect->x1;
	drawRect.y1 = invalidRect->y1;
	if(string_draw->strAlign & ALIGNMENT_LEFT)
	{
		resX = winRect->x0;
		//resRect.x1 = resRect.x0 + char_draw.charW - 1;
	}
	else if(string_draw->strAlign & ALIGNMENT_RIGHT)
	{
		//resRect.x1 = winRect->x1;
		resX = winRect->x1 - char_draw.charW + 1;
	}
	else
	{
		resX = (winRect->x0 + winRect->x1 - char_draw.charW)>>1;
		//resRect.x1 = resRect.x0 + char_draw.charW - 1;
	}
	if(resX < winRect->x0)
	{
		resX = winRect->x0;
	}
	resRect.x0 = resX;
	resRect.x1 = resX + char_draw.charW - 1;
	
	resY = resRect.y0 = (winRect->y0 + winRect->y1 - char_draw.charH)>>1;
	if(!RES_ID_IS_RAM(string_draw->id))
		resY = resRect.y0 = resRect.y0 + 3;
	resRect.y1 = resRect.y0 + char_draw.charH - 1;
	if(uiWinOverlapCmp(&resRect,&drawRect) < 0)
	{
		if(string_draw->bgColor!=INVALID_COLOR)
			uiWinDrawRect(&drawRect,string_draw->bgColor);
		return;
	}
	if(string_draw->bgColor != INVALID_COLOR)
		uiWinDrawRectNotInter(&drawRect, &resRect,string_draw->bgColor);
	uiWinInterSection(&drawRect,&drawRect,&resRect);
	resRect.x0 = drawRect.x0 - resX;
	resRect.y0 = drawRect.y0 - resY;
	resRect.x1 = drawRect.x1 - resX;
	resRect.y1 = drawRect.y1 - resY;
	destAddr = uiWinDrawStartAddrCal(lcdRotate,drawRect.x0, drawRect.y0);
	if(destAddr == NULL)
		return;
	secSpecialAddr = NULL;
	if(resY > winRect->y0)
	{
		if(resY - winRect->y0 > 5)
			secSpecialAddr = uiWinDrawStartAddrCal(lcdRotate,drawRect.x0, (resY-5));
		else
			secSpecialAddr = uiWinDrawStartAddrCal(lcdRotate,drawRect.x0, winRect->y0);
	}
	lastSecSpecialAddr = secSpecialAddr;
	lowPhoneticAddr = NULL;
	if(resY + (s16)char_draw.charH-7 <= winRect->y1)
	{
		if(resY + (s16)char_draw.charH >= 11)
			lowPhoneticAddr = uiWinDrawStartAddrCal(lcdRotate, drawRect.x0, (resY+(int16)char_draw.charH-11));
	}
	lastLowPhoneticAddr = lowPhoneticAddr;
	width = 0;
	lastAddr = destAddr;
	
	char_draw.fontColor = string_draw->fontColor;
	char_draw.bgColor   = string_draw->bgColor;
	char_draw.rimColor	= string_draw->rimColor;
	for(i=0;i<strNum;i++)
	{
		charBuff = res_GetCharInfor(string_draw->id, i, &char_draw.charW, &char_draw.charH,string_draw->font,&special);
		if(charBuff == NULL)
			break;
		if(special == 0) //限制了字符串起始必须是标准字体
			width += char_draw.charW;
		if(width > resRect.x0)
		{
			if(special)
			{
				if(lastSpecial == special)
				{
					char_draw.charRect.y0 = 0;
					char_draw.charRect.y1 = char_draw.charH-1;
				}
				else if(special==2)
				{
					char_draw.charRect.y0 = uiWinDraw_calPhoneticPos(charBuff,char_draw.charW,char_draw.charH);//resHeight*4;
					char_draw.charRect.y1 = char_draw.charRect.y0 + 4;
				}
				else
				{
					if(resRect.y0 >= char_draw.charH)
						continue;
					char_draw.charRect.y0 = resRect.y0;
					char_draw.charRect.y1 = char_draw.charH - 1;
				}
			}
			else
			{
				if(width - resRect.x0 < char_draw.charW)
					char_draw.charRect.x0 = char_draw.charW - (width - resRect.x0);
				else
					char_draw.charRect.x0 = 0;
				if(width > resRect.x1+1)
					char_draw.charRect.x1 = (char_draw.charW-(width-(resRect.x1+1))-char_draw.charRect.x0)-1;
				else
					char_draw.charRect.x1 = char_draw.charW-1;
					//charRect.x1=char_draw.charW-charRect.x0-1;
				char_draw.charRect.y0 = resRect.y0;
				char_draw.charRect.y1 = resRect.y1;
			}
			if(special)
			{
				if(lastSpecial == special)
				{
					if(lastSecSpecialAddr)
						uiWinDrawChar(lastSecSpecialAddr,charBuff,&char_draw);
						
				}
				else
				{
					if(special == 1)
						uiWinDrawChar(lastAddr,charBuff,&char_draw);
					else if(special==2 && lastLowPhoneticAddr)
						uiWinDrawChar(lastLowPhoneticAddr,charBuff,&char_draw);
				}
			}
			else
				uiWinDrawChar(destAddr,charBuff,&char_draw);
			lastSpecial = special;
			if(special == 0)
			{
				lastAddr  = destAddr;

				destAddr = uiWinDrawAddrReCal(lcdRotate, destAddr, (char_draw.charRect.x1 - char_draw.charRect.x0 + 1), 0);
				if(secSpecialAddr)
				{
					lastSecSpecialAddr 	= secSpecialAddr;
					secSpecialAddr 		= uiWinDrawAddrReCal(lcdRotate, secSpecialAddr, (char_draw.charRect.x1 - char_draw.charRect.x0 + 1), 0);
				}
				if(lowPhoneticAddr)
				{
					lastLowPhoneticAddr = lowPhoneticAddr;
					lowPhoneticAddr 	= uiWinDrawAddrReCal(lcdRotate, lowPhoneticAddr, (char_draw.charRect.x1 - char_draw.charRect.x0 + 1), 0);
				}
			}
			if(width > resRect.x1)
			{
				if(i < strNum-1)
				{
					res_GetCharInfor(string_draw->id, i+1, NULL, NULL,string_draw->font,&special);
					if(special)
						continue;
				}
				break;
			}
		}
	}
}