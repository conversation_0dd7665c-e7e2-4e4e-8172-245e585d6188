/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiFrameWinCB
* Description    : uiFrameWinCB
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
static void uiFrameWinCB(uiWinMsg* msg)
{
	winHandle 		hWin 		= msg->curWin;
	uiFrameWinObj	*pFrameWin	= (uiFrameWinObj*)uiHandleToPtr(hWin);
	uiWinObj		*pWin		= &(pFrameWin->win);
	uiWinObj		*pChild;
	u16   			chileId;
	if(pFrameWin->cb)
		pFrameWin->cb(msg);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			//deg_msg("frame win create\n");
			return;
		case MSG_WIN_PAINT:
			//deg_Printf("uiFrameWinCB\n");
			uiWinDrawRoundRectWithRim((uiRect*)(msg->para.p),(uiRect*)&pWin->round_rect,pWin->bgColor);
			//if(pWin->bgColor != INVALID_COLOR)
			//{
			//	uiWinDrawRect((uiRect*)(msg->para.p), pWin->bgColor);
			//}
				
			//deg_msg("paint frame win[]:[%d %d %d %d]\n",pWin->invalidRect.x0,pWin->invalidRect.y0,pWin->invalidRect.x1,pWin->invalidRect.y1);
			return;
		case MSG_WIN_TOUCH:
			return;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		case MSG_WIN_ADD_WIDGET:
			pChild = (uiWinObj*)uiHandleToPtr(msg->childWin);
			if(pChild != NULL && (pChild->style & WIN_WIDGET))
			{
				chileId = uiWidgetGetId(msg->childWin);
				if(chileId != INVALID_WIDGET_ID && chileId < WIGET_HANDLE_MAX_NUM)
					pFrameWin->widgetHandle[chileId] = msg->childWin;
			}
			return;
		case MSG_WIN_GET_WIDGET:
			chileId = msg->para.v;
			if(chileId != INVALID_WIDGET_ID && chileId < WIGET_HANDLE_MAX_NUM)
				msg->childWin = pFrameWin->widgetHandle[chileId];
			else
				msg->childWin = INVALID_HANDLE;
			return;
		case MSG_WIN_WIDGET_DESTROY:
			pChild = (uiWinObj*)uiHandleToPtr(msg->childWin);
			if(pChild != NULL && (pChild->style & WIN_WIDGET))
			{
				chileId = uiWidgetGetId(msg->childWin);
				if(chileId != INVALID_WIDGET_ID && chileId < WIGET_HANDLE_MAX_NUM)
					pFrameWin->widgetHandle[chileId] = INVALID_HANDLE;
			}
			return;
		case MSG_WIN_CHANGE_ROUNDRECT_RADIUS:
			//deg_Printf("uiFrameWinCB ROUNDRECT_RADIUS: %d, %d\n", pWin->round_rect.radius, msg->para.v);
			if(pWin->round_rect.radius != msg->para.v)
			{
				pWin->round_rect.radius =  msg->para.v;
			}
			return;
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiFrameWinCreate
* Description    : uiFrameWinCreate
* Input          : 
* Output         : none                                            
* Return         : none 
*******************************************************************************/
winHandle uiFrameWinCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle 		hFrameWin;
	uiFrameWinObj	*pFrameWin;
	uiWinObj		*pWin;
	hFrameWin = uiWinCreate(infor->x0,infor->y0,infor->width,infor->height, parent,uiFrameWinCB,sizeof(uiFrameWinObj),infor->style|WIN_FRAME);
	if(hFrameWin != INVALID_HANDLE)
	{
		pFrameWin 			= (uiFrameWinObj*)uiHandleToPtr(hFrameWin);
		pWin				= &(pFrameWin->win);
		pFrameWin->cb		= cb;
		pFrameWin->prvate	= infor->prvate; //pointer WINDOWS_T
		//if(infor->style & WIN_RECT_RIM)
		{
			pWin->rect.rimColor = pWin->round_rect.rimColor = infor->rimColor;
		}
		if(infor->style & WIN_ROUND_RECT)
		{
			pWin->round_rect.round_type = infor->imageAlign;
		}
		if(infor->image != INVALID_RES_ID && infor->image != 0)
		{
			if((infor->style & WIN_NOT_ZOOM) == 0)
			{
				pWin->round_rect.radius = USER_Rw(infor->image);
			}else
			{
				pWin->round_rect.radius = infor->image;
			}
		}	
		//deg_Printf("uiFrameWinCreate[%d,%d,%d,%d][%x,%d,%x]\n",pWin->round_rect.x0, pWin->round_rect.x1, pWin->round_rect.y0, pWin->round_rect.y1,pWin->round_rect.round_type, pWin->round_rect.radius,pWin->round_rect.rimColor);
		//if(infor->bgColor != INVALID_COLOR)
			uiWinSetbgColor(hFrameWin, infor->bgColor);
	}

	return hFrameWin;
}