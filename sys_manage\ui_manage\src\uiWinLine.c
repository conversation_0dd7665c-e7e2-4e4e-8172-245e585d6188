/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

/*******************************************************************************
* Function Name  : uiLineProc
* Description    : uiLineProc
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void uiLineProc(uiWinMsg* msg)
{
	winHandle hWin;
	uiLineObj* pLine;
//	uiWinObj* pWin;
	if(uiWidgetProc(msg))
		return;
	hWin 	= msg->curWin;
	pLine	= (uiLineObj*)uiHandleToPtr(hWin);
//	pWin	= &(pLine->widget.win);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			//deg_msg("line win create\n");
			return;
		case MSG_WIN_PAINT:
			uiWinDrawLine(&pLine->line);
			//deg_msg("paint button [%d]:[%d %d %d %d]\n",pButton->widget.id,pWin->invalidRect.x0,pWin->invalidRect.y0,pWin->invalidRect.x1,pWin->invalidRect.y1);
			return;
		case MSG_WIN_TOUCH:
			break;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		case MSG_WIN_CHANGE_BG_COLOR:
			if(pLine->line.fill_color != msg->para.v )
			{
				pLine->line.fill_color = msg->para.v;
				//uiWinParentRedraw(hWin);	
				uiWinDrawLine(&pLine->line);
				//uiWinUpdateInvalid(hWin);
			}
			break;	
		default:
			break;
	}
	uiWinDefaultProc(msg);
}
/*******************************************************************************
* Function Name  : uiLineCreate
* Description    : uiLineCreate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiLineCreate(widgetCreateInfor* infor,winHandle parent,uiWinCB cb)
{
	winHandle 	hLine;
	uiLineObj   *pLine;
	s16 sx, sy;
	u16 width,height;
	if(!(infor->style & WIN_NOT_ZOOM))
	{
		infor->x0 = USER_Rx(infor->x0);
		infor->y0 = USER_Ry(infor->y0);
		infor->width = USER_Rw(infor->width);
		infor->height = USER_Rh(infor->height);
		infor->image = USER_Rw(infor->image);
	}
	sx = hx330x_min(infor->x0, infor->width);
	sy = hx330x_min(infor->y0, infor->height);
	width = hx330x_abs(infor->x0 - infor->width);
	height = hx330x_abs(infor->y0 - infor->height);
	//deg_Printf("sx:%d,sy:%d,width:%d, height:%d\n",sx, sy, width, height);
	hLine =  uiWinCreate(sx, sy, width, height,parent,uiLineProc,sizeof(uiLineObj),WIN_WIDGET|WIN_TOUCH_SUPPORT|WIN_NOT_ZOOM);
	if(hLine != INVALID_HANDLE)
	{
		pLine = (uiLineObj*)uiHandleToPtr(hLine);
		pLine->line.sx 			= infor->x0;
		pLine->line.sy 			= infor->y0;
		pLine->line.ex 			= infor->width;
		pLine->line.ey 			= infor->height;
		pLine->line.width		= infor->image;
		pLine->line.fill_color	= infor->bgColor;
		pLine->line.style		= WIN_NOT_ZOOM;
		uiWidgetSetId(hLine,infor->id);
		uiWinSetbgColor(hLine, pLine->line.fill_color);
	}
	return hLine;
}