/******************** (C) COPYRIGHT 2021 HX ************************
* File Name          :
* Author             : 
* Version            : v1
* Date               : 09/01/2021
* Description        : 
***************************************************************************/
#include "../../../hal/inc/hal.h"

#define WIN_HEAP_SIZE  		(1024L*20)
typedef struct uiWinManage_S
{
	u32 			winSum;
	u32 			invalidWinSum;
	u32 			destroyWin;
	u32 			mempError;
	winHandle 		deskTopWin;
	winHandle 		curWin;
	uiRect 			errTipRect;
	STRING_DRAW_T	errString;
	memPool_Ctl 	rectMemp;
	u8 				winHeap[WIN_HEAP_SIZE];
}uiWinManage_T;

ALIGNED(4) static uiWinManage_T uiWinManage;


/*******************************************************************************
* Function Name  : uiWinErrShow
* Description    : uiWinErrShow
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void uiWinErrShow(resID id)
{
	uiWinManage.errString.id = id;
	uiWinDrawString(&uiWinManage.errTipRect,&uiWinManage.errTipRect,&uiWinManage.errString);
}
/*******************************************************************************
* Function Name  : uiWinSendMsg
* Description    : uiWinSendMsg ： send message to a window
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSendMsg(winHandle handle,uiWinMsg* msg)
{
	uiWinObj* pWin;
	uiWinLock();
	if(handle != INVALID_HANDLE)
	{	
		pWin = (uiWinObj*)uiHandleToPtr(handle);
		if(pWin == NULL || pWin->cb == NULL)
		{
			uiWinUnlock();
			return;
		}
		msg->curWin = handle;
		(*pWin->cb)(msg);
	}
	uiWinUnlock();
}
/*******************************************************************************
* Function Name  : uiWinSendMsgId
* Description    : uiWinSendMsgId ： send message id to a window
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSendMsgId(winHandle handle,u32 msg_id)
{
	uiWinMsg msg;
	msg.id = msg_id;
	uiWinSendMsg(handle,&msg);
}
/*******************************************************************************
* Function Name  : uiWinSendMsgToParent
* Description    : uiWinSendMsgToParent ： send message to cur win's parent window
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSendMsgToParent(winHandle handle,uiWinMsg* msg)
{
	uiWinObj* pWin;
	//uiWinLock();
	if(handle!=INVALID_HANDLE)
	{
		msg->childWin 	= handle;
		pWin	  		= (uiWinObj*)uiHandleToPtr(handle);
		uiWinSendMsg(pWin->parent,msg);
	}
	//uiWinUnlock();
}
/*******************************************************************************
* Function Name  : uiWinSendMsgToParent
* Description    : uiWinSendMsgToParent ： send message to cur win's parent window
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSendMsgIdToParent(winHandle handle,u32 msg_id)
{
	uiWinMsg msg;
	msg.id = msg_id;
	uiWinSendMsgToParent(handle,&msg);
}
/*******************************************************************************
* Function Name  : uiWinOverlapCmp
* Description    : uiWinOverlapCmp ： determine whether the two windows overlap
* Input          : uiRect* rect1,uiRect* rect2
* Output         : none                                            
* Return         : int : 0: not overlap, <0: overlap
*******************************************************************************/
int uiWinOverlapCmp(uiRect* rect1,uiRect* rect2)
{
	if( rect1->y1 < rect2->y0 ||
	    rect1->x1 < rect2->x0 ||
	    rect1->x0 > rect2->x1 ||
	    rect1->y0 > rect2->y1)
	    return -1;
	return 0;
}
/*******************************************************************************
* Function Name  : uiWinInsideCmp
* Description    : uiWinInsideCmp ： determine if child rect is in parent rect
* Input          : uiRect* parent,uiRect* child
* Output         : none                                            
* Return         : int : 0: child is inside parent, <0: child is not inside parent
*******************************************************************************/
int uiWinInsideCmp(uiRect* parent,uiRect* child)
{
	if( parent->x0 <= child->x0 &&
	    parent->y0 <= child->y0 &&
	    parent->x1 >= child->x1 &&
	    parent->y1 >= child->y1)
	    return 0;
	return -1;
}
/*******************************************************************************
* Function Name  : uiWinStringExRowCal
* Description    : uiWinStringExRowCal
* Input          : char *str
* Output         : none                                            
* Return         : u32 rows
*******************************************************************************/
u32 uiWinStringExRowCal(char *str)
{
	u32 cnt = 0;
	u32 not_end_line = 0;
	if(str == NULL)
		return 0;
	while(*str)
	{
		if(*str >= 0xA0)
		{
			str += 2;
			continue;
		}
		if(str[0] == 0x0D && str[1] == 0x0A)
		{
			cnt++;
			str += 2;
			not_end_line = 0;
		}else
		{
			not_end_line = 1;
			str++;
		}
	}
	return (cnt + not_end_line);
}
/*******************************************************************************
* Function Name  : uiWinStringExGetByRow
* Description    : uiWinStringExGetByRow
* Input          : char *str, u32 row
* Output         : none                                            
* Return         : char*
*******************************************************************************/
char* uiWinStringExGetByRow(char *str, u32 row)
{
	u32 cnt = 0;
	if(str == NULL)
		return NULL;
	while(*str)
	{
		if(cnt == row)
			return str;
		if(*str >= 0xA0)
		{
			str += 2;
			continue;
		}
		if(str[0] == 0x0D && str[1] == 0x0A)
		{
			cnt++;
			str += 2;
		}else
		{
			str++;
		}
		
	}
	return NULL;
}
/*******************************************************************************
* Function Name  : uiWinStringExGetNext
* Description    : uiWinStringExGetNext
* Input          : char *str, u32 row
* Output         : none                                            
* Return         : char*
*******************************************************************************/
char* uiWinStringExGetNext(char *str)
{
	if(str == NULL)
		return NULL;
	while(*str)
	{
		if(*str >= 0xA0)
		{
			str += 2;
			continue;
		}
		if(str[0] == 0x0D && str[1] == 0x0A)
		{
			str += 2;
			if(*str)
				return str;
			else
				return NULL;
		}else
		{
			str++;
		}
		
	}
	return NULL;
}
/*******************************************************************************
* Function Name  : uiWinSetName
* Description    : uiWinSetName: set win's name
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinInterSection(uiRect* out,uiRect* win1,uiRect* win2)
{
	out->x0 = hx330x_max(win1->x0,win2->x0);
	out->x1 = hx330x_min(win1->x1,win2->x1);
	out->y0 = hx330x_max(win1->y0,win2->y0);
	out->y1 = hx330x_min(win1->y1,win2->y1);
}
/*******************************************************************************
* Function Name  : uiWinHasInvalidRect
* Description    : uiWinHasInvalidRect
* Input          : uiRect* win,uiRect* invalidRect
* Output         : none                                            
* Return         : int : 1: win has invalidRect, 0: win has not invalidRect
*******************************************************************************/
int uiWinHasInvalidRect(uiRect* win,uiRect* invalidRect)
{
	while(win)
	{
		if(uiWinInsideCmp(win,invalidRect) == 0)
		{
			return 1;
		}
		else if(uiWinInsideCmp(invalidRect,win) == 0)
		{
			win->x0 = invalidRect->x0;
			win->x1 = invalidRect->x1;
			win->y0 = invalidRect->y0;
			win->y1 = invalidRect->y1;
			return 1;
		}
		win = win->next;
	}
	return 0;
}
/*******************************************************************************
* Function Name  : uiWinFreeInvalidRect
* Description    : uiWinFreeInvalidRect
* Input          : uiWinObj* pWin
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinFreeInvalidRect(uiWinObj* pWin)
{
	uiRect* rect = pWin->invalidRect.next;
	while(rect)
	{
		uiMemPoolPut(&uiWinManage.rectMemp,(void*)rect);
		rect = rect->next;
	}
	pWin->invalidRect.next = NULL;
}
/*******************************************************************************
* Function Name  : uiWinSetbgColor
* Description    : uiWinSetbgColor: set background color
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetbgColor(winHandle hWin,uiColor bgColor)
{
	uiWinMsg msg;
	msg.id 		= MSG_WIN_CHANGE_BG_COLOR;
	msg.para.v	= bgColor;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinSetCycleRadius
* Description    : uiWinSetCycleRadius:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetCycleRadius(winHandle hWin,s16 radius)
{
	uiWinMsg msg;
	msg.id 		= MSG_WIN_CHANGE_CYCLE_RADIUS;
	msg.para.v	= radius;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinSetRoundRectRadius
* Description    : uiWinSetRoundRectRadius:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetRoundRectRadius(winHandle hWin,s16 radius)
{
	uiWinMsg msg;
	msg.id 		= MSG_WIN_CHANGE_ROUNDRECT_RADIUS;
	msg.para.v	= radius;
	uiWinSendMsg(hWin,&msg);
}

/*******************************************************************************
* Function Name  : uiWinSetfgColor
* Description    : uiWinSetfgColor: set font background color
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetfgColor(winHandle hWin,uiColor bgColor)
{
	uiWinMsg msg;
	msg.id		= MSG_WIN_CHANGE_FG_COLOR;
	msg.para.v	= bgColor;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinSetVisible
* Description    : uiWinSetVisible: show or hide a widget
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetVisible(winHandle hWin,u32 visible)
{
	uiWinMsg msg;
	msg.id	= MSG_WIN_VISIBLE_GET;
	uiWinSendMsg(hWin,&msg);
	if(visible)
	{
		if(msg.para.v)
			return;
		msg.para.v=1;
	}
	else
	{
		if(msg.para.v==0)
			return;
		msg.para.v=0;		
	}
	msg.id = MSG_WIN_VISIBLE_SET;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinIsVisible
* Description    : uiWinIsVisible: return 1 if a widget is visible 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
u32 uiWinIsVisible(winHandle hWin)
{
	uiWinMsg msg;
	msg.id = MSG_WIN_VISIBLE_GET;
	uiWinSendMsg(hWin,&msg);
	if(msg.para.v)
		return 1;
	return 0;
}
/*******************************************************************************
* Function Name  : uiWinSetResid
* Description    : uiWinSetResid: set res id of a widget 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetResid(winHandle hWin,resID id)
{
	uiWinMsg msg;
	msg.id		= MSG_WIN_CHANGE_RESID;
	msg.para.v	= id;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinSetItemSelResid
* Description    : uiWinSetItemSelResid: set res id of a sel item widget 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetItemSelResid(winHandle hWin,resID id)
{
	uiWinMsg msg;
	msg.id		= MSG_WIN_CHG_ITEM_SEL_RESID;
	msg.para.v	= id;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinSetItemSelResid
* Description    : uiWinSetItemSelResid: set res id of a sel item widget 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinUpdateResId(winHandle hWin)
{
	uiWinSendMsgId(hWin,MSG_WIN_UPDATE_RESID);
}
/*******************************************************************************
* Function Name  : uiWinSetItemSelResid
* Description    : uiWinSetItemSelResid: set res id of a sel item widget 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinUpdateAllResId(void)
{
	winHandle hWin,hChild;
	uiWinObj* pWin;
	uiWinLock();
	hWin = uiWinManage.deskTopWin;
	while(hWin != INVALID_HANDLE)
	{
		pWin 	= (uiWinObj*)uiHandleToPtr(hWin);
		hChild	= pWin->child;
		while(hWin!=INVALID_HANDLE)
		{
			uiWinSendMsgId(hWin,MSG_WIN_UPDATE_RESID);
			hWin = pWin->next;
			pWin = (uiWinObj*)uiHandleToPtr(hWin);
		}
		hWin = hChild;
	}
	uiWinUnlock();
}
/*******************************************************************************
* Function Name  : uiWinSetStrInfor
* Description    : uiWinSetStrInfor: set str info of a widget
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetStrInfor(winHandle hWin,charFont font,u8 strAlign,uiColor fontColor)
{
	uiStrInfo strInfor;
	uiWinMsg msg;
	strInfor.font 		= font;
	strInfor.strAlign	= strAlign;
	strInfor.fontColor	= fontColor;
	strInfor.bgColor	= INVALID_COLOR;
	strInfor.rimColor	= INVALID_COLOR;
	msg.id				= MSG_WIN_CHANGE_STRINFOR;
	msg.para.p			= &strInfor;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiResInforInit
* Description    : uiResInforInit: uiResInfo* resInfo
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiResInforInit(uiResInfo* resInfo)
{
	resInfo->image		= INVALID_RES_ID;
	resInfo->color		= INVALID_COLOR;
	resInfo->font		= 0;
	resInfo->strAlign	= ALIGNMENT_CENTER;
	resInfo->fontColor	= INVALID_COLOR;
	resInfo->bgColor	= INVALID_COLOR;
	resInfo->rimColor	= INVALID_COLOR;
}
/*******************************************************************************
* Function Name  : uiWinSetSelectInfor
* Description    : uiWinSetSelectInfor:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetSelectInfor(winHandle hWin,uiResInfo* res_info)
{
	uiWinMsg msg;
	msg.id		= MSG_WIN_SELECT_INFOR_EX;
	msg.para.p	= res_info;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinSetUnselectInfor
* Description    : uiWinSetUnselectInfor:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetUnselectInfor(winHandle hWin,uiResInfo* res_info)
{
	uiWinMsg msg;
	msg.id		= MSG_WIN_UNSELECT_INFOR_EX;
	msg.para.p	= res_info;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinGetResSum
* Description    : uiWinGetResSum:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
u32 uiWinGetResSum(winHandle hWin)
{
	uiWinMsg msg;
	msg.id		= MSG_WIDGET_RES_SUM_GET;
	msg.para.v	= 0;
	uiWinSendMsg(hWin,&msg);
	return msg.para.v;
}
/*******************************************************************************
* Function Name  : uiWinSetResSum
* Description    : uiWinSetResSum:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetResSum(winHandle hWin, u32 sum)
{
	uiWinMsg msg;
	msg.id		= MSG_WIDGET_RES_SUM_SET;
	msg.para.v	= sum;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinSetResidByNum
* Description    : uiWinSetResidByNum:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetResidByNum(winHandle hWin,resID id,u32 num,u32 select)
{
	uiWinMsg msg;
	uiStringExInfo infor;
	infor.str		= id;
	infor.num		= (u16)num;
	infor.select	= (u16)select;
	msg.id			= MSG_WIDGET_SET_RESID_BY_NUM;
	msg.para.p 		= (void*)&infor;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinSetPorgressRate
* Description    : uiWinSetPorgressRate:
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetPorgressRate(winHandle hWin,u32 rate)
{
	uiWinMsg msg;
	msg.id		= MSG_WIN_PROGRESS_RATE;
	msg.para.v	= rate;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinParentRedraw
* Description    : uiWinParentRedraw: send msg to parent widget to redraw win
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinParentRedraw(winHandle hWin)
{
	uiWinObj* pWin;
	uiRect invalidRect;
	uiWinMsg msg;
	uiWinLock();
	if(hWin == INVALID_HANDLE)
	{
		uiWinUnlock();
		return;
	}
	pWin = (uiWinObj*)uiHandleToPtr(hWin);
	invalidRect.x0 	= pWin->rect.x0;
	invalidRect.x1 	= pWin->rect.x1;
	invalidRect.y0 	= pWin->rect.y0;
	invalidRect.y1 	= pWin->rect.y1;
	invalidRect.rimColor = INVALID_COLOR;
	msg.id			= MSG_WIN_INVALID;
	msg.para.p		= (void*)&invalidRect;
	uiWinSendMsg(pWin->parent,&msg);
	uiWinUnlock();
}
/*******************************************************************************
* Function Name  : uiWinGetRelativePos
* Description    : uiWinGetRelativePos: get win relative position of win's parent
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinGetRelativePos(winHandle hWin,uiRect* pos)
{
	uiWinObj* pWin;
	uiWinObj* pParenWin;
	if(hWin == INVALID_HANDLE)
	{
		return;
	}
	uiWinLock();
	pWin = (uiWinObj*)uiHandleToPtr(hWin);
	if(pWin->parent == INVALID_HANDLE)
	{
		pos->x0 = pWin->rect.x0;
		pos->x1 = pWin->rect.x1;
		pos->y0 = pWin->rect.y0;
		pos->y1 = pWin->rect.y1;
		uiWinUnlock();
		return;
	}
	pParenWin = (uiWinObj*)uiHandleToPtr(pWin->parent);
	pos->x0	= pWin->rect.x0 - pParenWin->rect.x0;
	pos->x1	= pWin->rect.x1 - pParenWin->rect.x0;
	pos->y0	= pWin->rect.y0 - pParenWin->rect.y0;
	pos->y1	= pWin->rect.y1 - pParenWin->rect.y0;
	uiWinUnlock();
}
/*******************************************************************************
* Function Name  : uiWinGetPos
* Description    : uiWinGetPos: get win's position
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinGetPos(winHandle hWin,uiRect* pos)
{
	uiWinObj* pWin;
	if(hWin==INVALID_HANDLE)
	{
		return;
	}
	uiWinLock();
	pWin = (uiWinObj*)uiHandleToPtr(hWin);
	pos->x0 = pWin->rect.x0;
	pos->x1 = pWin->rect.x1;
	pos->y0 = pWin->rect.y0;
	pos->y1 = pWin->rect.y1;
	uiWinUnlock();
}
/*******************************************************************************
* Function Name  : uiWinUpdateInvalid
* Description    : uiWinUpdateInvalid: update rect to invalid rect
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinUpdateInvalid(winHandle hWin)
{
	uiWinObj* pWin;
	if(hWin == INVALID_HANDLE)
	{
		return;
	}
	uiWinLock();
	pWin = (uiWinObj*)uiHandleToPtr(hWin);
	if((pWin->style & WIN_INVALID) == 0)
		uiWinManage.invalidWinSum++;
	pWin->style |= WIN_INVALID;
	pWin->invalidRect.x0 = pWin->rect.x0;
	pWin->invalidRect.x1 = pWin->rect.x1;
	pWin->invalidRect.y0 = pWin->rect.y0;
	pWin->invalidRect.y1 = pWin->rect.y1;
	pWin->invalidRect.rimColor = pWin->rect.rimColor;
	uiWinUnlock();
}
/*******************************************************************************
* Function Name  : uiWinSetProgressRate
* Description    : uiWinSetProgressRate: set progress rate
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetProgressRate(winHandle hWin,u32 rate)
{
	uiWinMsg msg;
	msg.id		= MSG_WIN_PROGRESS_RATE;
	msg.para.v	= rate;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinSetName
* Description    : uiWinSetName: set win's name
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinSetName(winHandle hWin,char* name)
{
	uiWinObj* pWin;
	if(hWin == INVALID_HANDLE)
	{
		return;
	}
	uiWinLock();
	pWin = (uiWinObj*)uiHandleToPtr(hWin);
	pWin->name = name;
	uiWinUnlock();
}
/*******************************************************************************
* Function Name  : uiWinGetCurrent
* Description    : uiWinGetCurrent: get cur win handle
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiWinGetCurrent(void)
{
	return uiWinManage.curWin;
}
/*******************************************************************************
* Function Name  : uiWinSetInvalid
* Description    : uiWinSetInvalid:set invalid area of window, winPaint() function will redraw this invalid area
* Input          : winHandle hWin,uiRect* invalidRect
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void uiWinSetInvalid(winHandle hWin,uiRect* invalidRect)
{
	uiWinObj* pWin;
	uiRect curInvalidRect;
	uiRect* rect;
	if(hWin == INVALID_HANDLE)
		return;
	pWin = (uiWinObj*)uiHandleToPtr(hWin);
	curInvalidRect.x0 = hx330x_max(invalidRect->x0,pWin->rect.x0);
	curInvalidRect.x1 = hx330x_min(invalidRect->x1,pWin->rect.x1);
	curInvalidRect.y0 = hx330x_max(invalidRect->y0,pWin->rect.y0);
	curInvalidRect.y1 = hx330x_min(invalidRect->y1,pWin->rect.y1);
	curInvalidRect.rimColor = INVALID_COLOR;
	if(pWin->style & WIN_INVALID)
	{
		if(uiWinHasInvalidRect(&(pWin->invalidRect),&curInvalidRect))
			return;
		rect = (uiRect*)uiMemPoolGet(&uiWinManage.rectMemp);
		if(rect == NULL)
			return;
		rect->x0	= curInvalidRect.x0;
		rect->x1	= curInvalidRect.x1;
		rect->y0	= curInvalidRect.y0;
		rect->y1	= curInvalidRect.y1;
		rect->rimColor = curInvalidRect.rimColor;
		rect->next	= pWin->invalidRect.next;
		pWin->invalidRect.next = rect;
		return;
	}
	pWin->style |= WIN_INVALID;
	pWin->invalidRect.x0 	= curInvalidRect.x0;
	pWin->invalidRect.x1 	= curInvalidRect.x1;
	pWin->invalidRect.y0 	= curInvalidRect.y0;
	pWin->invalidRect.y1 	= curInvalidRect.y1;
	pWin->invalidRect.rimColor 	= curInvalidRect.rimColor;
	pWin->invalidRect.next  = NULL;
	uiWinManage.invalidWinSum++;
}
/*******************************************************************************
* Function Name  : uiWinDefaultProc
* Description    : uiWinDefaultProc:all window's callback function must call this function, to handle basic messages 
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinDefaultProc(uiWinMsg* msg) 
{
	uiRect		*invalidRect;
	winHandle 	winTemp;
	winHandle 	curWin 	= msg->curWin;	
	uiWinObj 	*pWin 	= (uiWinObj*)uiHandleToPtr(curWin);
	
	switch(msg->id)
	{
		case MSG_WIN_DESTROY:
			uiWinSendMsg(pWin->child,msg);
			if(pWin->parent != INVALID_HANDLE)
				((uiWinObj*)uiHandleToPtr(pWin->parent))->child = pWin->child;
			invalidRect = (uiRect*)msg->para.p;
			invalidRect->x0 = hx330x_min(invalidRect->x0, pWin->rect.x0);
			invalidRect->y0 = hx330x_min(invalidRect->y0, pWin->rect.y0);
			invalidRect->x1 = hx330x_max(invalidRect->x1, pWin->rect.x1);
			invalidRect->y1 = hx330x_max(invalidRect->y1, pWin->rect.y1);
			uiWinManage.curWin = curWin;
			//uiWinSendMsgIdToParent(curWin,MSG_WIN_CHILE_DESTROY);
			uiWinSendMsgId(curWin,MSG_DIALOG_CLOSE);
			while(curWin != INVALID_HANDLE)
			{
				uiWinManage.winSum--;
				pWin	= (uiWinObj*)uiHandleToPtr(curWin);
				winTemp	= pWin->next;
				//if(pWin->name)
				//	deg_msg("destroyed [%s] %d,style:0x%x\n",pWin->name,state.winSum,pWin->style);
				//else
				//deg_msg("destroyed %d,style:0x%x\n",state.winSum,pWin->style);
				uiWinFreeInvalidRect(pWin);
				uiWinHeapFree(curWin);
				curWin = winTemp;
			}
			break;
		case MSG_WIN_INVALID:
			invalidRect = (uiRect*)msg->para.p;
			winTemp 	= curWin;
			if(uiWinOverlapCmp(&(pWin->rect), invalidRect) == 0) //not overlap
			{
				while(curWin != INVALID_HANDLE)
				{
					pWin = (uiWinObj*) uiHandleToPtr(curWin);
					if(pWin->style & WIN_VISIBLE)
					{
						if(uiWinOverlapCmp(&(pWin->rect), invalidRect) == 0) //not overlap
							uiWinSetInvalid(curWin, invalidRect);
					}
					curWin = pWin->next;
				}
				pWin = (uiWinObj*)uiHandleToPtr(winTemp);
				if(pWin->bgColor != INVALID_COLOR)
				{
					if(uiWinInsideCmp(&(pWin->rect),invalidRect) == 0)
						break;
				}
				uiWinSendMsg(pWin->parent,msg);
				break;
			}
			uiWinSendMsg(pWin->parent,msg);
			break;
		case MSG_WIN_VISIBLE_SET:
			if(msg->para.v)
				pWin->style |= WIN_VISIBLE;
			else
				pWin->style &= (~WIN_VISIBLE);
			uiWinParentRedraw(curWin);
			break;
		case MSG_WIN_VISIBLE_GET:
			if(pWin->style & WIN_VISIBLE)
				msg->para.v = 1;
			else
				msg->para.v = 0;
			break;
		case MSG_WIN_CHANGE_BG_COLOR:
			pWin->bgColor = msg->para.v;
			break;
		case MSG_WIN_TOUCH:
			uiWinSendMsgToParent(curWin,msg);
			break;
		default:
			break;
			//deg_msg("msg [%d] was ignored!!!\n",msg->id);
	}
}
/*******************************************************************************
* Function Name  : uiWinCreate
* Description    : uiWinCreate: create window or widget
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
winHandle uiWinCreate(s16 x0, s16 y0, u16 width,u16 height, winHandle parent,uiWinCB cb,u32 size,u16 style)
{
	winHandle hWin;
	uiWinObj  *pWin = NULL;
	uiWinObj  *pParentWin = NULL;
	uiWinObj  *pTemp;
	if(uiWinManage.destroyWin)
	{
		while(1)
		{
			deg_msg("creating a window is not allowed when window is being destroyed!!!\n\n");
			deg_msg("do not create window in message [SYS_CLOSE_WINDOW]\n");
		}
	}
	if((style & WIN_NOT_ZOOM) == 0) //ZOOM到USER SIZE
	{
		x0		= USER_Rx(x0); 
		y0		= USER_Ry(y0); 
	    width	= USER_Rw(width);
		height	= USER_Rh(height);
	}
	uiWinLock();
	if(parent == INVALID_HANDLE)
		parent = uiWinManage.curWin;
	if(parent != INVALID_HANDLE)
	{
		pParentWin = (uiWinObj*)uiHandleToPtr(parent);
		if((style  & WIN_ABS_POS) == 0)
		{
			x0 += pParentWin->rect.x0;
			y0 += pParentWin->rect.y0;
		}
		if(width == 0)
			width = pParentWin->rect.x1 - pParentWin->rect.x0 + 1;
		if(height == 0)
			height = pParentWin->rect.y1 - pParentWin->rect.y0 + 1;
		
	}
	hWin = uiWinHeapMalloc(size);
	if(hWin == INVALID_HANDLE)
	{
		uiWinUnlock();
		deg_err("uiAlloc no memery!!!\n");
		return INVALID_HANDLE;
	}
	//deg_msg("create window:%d\n",state.winSum);
	uiWinManage.winSum++;
	pWin = (uiWinObj*)uiHandleToPtr(hWin);
	pWin->invalidRect.x0   	= pWin->rect.x0	= hx330x_clip(x0,0,USER_UI_WIDTH-1);
	pWin->invalidRect.x1   	= pWin->rect.x1	= hx330x_clip(x0+width-1,0,USER_UI_WIDTH-1);
	pWin->invalidRect.y0   	= pWin->rect.y0	= hx330x_clip(y0,0,USER_UI_HEIGHT-1);
	pWin->invalidRect.y1   	= pWin->rect.y1 = hx330x_clip(y0+height-1,0,USER_UI_HEIGHT-1);
	pWin->invalidRect.rimColor  = pWin->rect.rimColor = INVALID_COLOR;
	pWin->invalidRect.round_type  = pWin->rect.round_type = 0;
	pWin->invalidRect.radius  = pWin->rect.radius = 0;

	pWin->invalidRect.next 	= NULL;
	pWin->bgColor		   	= INVALID_COLOR;
	pWin->align		   	   	= 0;
	pWin->style 		   	= DEFAULT_STYLE|style;
	pWin->name				= NULL;
	pWin->cb				= cb;	
	pWin->parent 			= parent;
	pWin->child				= INVALID_HANDLE;
	pWin->next				= INVALID_HANDLE;
	if(pParentWin)
	{
		if(style & WIN_WIDGET)
		{
			pTemp = pParentWin;
			while(pTemp->next != INVALID_HANDLE)	
				pTemp = (uiWinObj*)uiHandleToPtr(pTemp->next);
			pTemp->next = hWin;	//link to last  widget window 

			pWin->invalidRect.x0 = pWin->rect.x0 = hx330x_clip(x0,pParentWin->rect.x0,pParentWin->rect.x1);
			pWin->invalidRect.x1 = pWin->rect.x1 = hx330x_clip(x0+width-1,pParentWin->rect.x0,pParentWin->rect.x1);
			pWin->invalidRect.y0 = pWin->rect.y0 = hx330x_clip(y0,pParentWin->rect.y0,pParentWin->rect.y1);
			pWin->invalidRect.y1 = pWin->rect.y1 = hx330x_clip(y0+height-1,pParentWin->rect.y0,pParentWin->rect.y1);
			pWin->invalidRect.rimColor = pWin->rect.rimColor = INVALID_COLOR;
			pWin->invalidRect.round_type = pWin->rect.round_type = 0;
			pWin->invalidRect.radius = pWin->rect.radius = 0;
			
			if(style & WIN_ROUND_RECT)
			{
				pWin->round_rect.x0 = pWin->rect.x0;
				pWin->round_rect.x1 = pWin->rect.x1;
				pWin->round_rect.y0 = pWin->rect.y0;
				pWin->round_rect.y1 = pWin->rect.y1;
				pWin->round_rect.rimColor = INVALID_COLOR;
				pWin->round_rect.round_type = 0;
				pWin->round_rect.radius = hx330x_min(pWin->rect.x1 - pWin->rect.x0 + 1,pWin->rect.y1 - pWin->rect.y0 + 1) / 8;
			}else
			{
				pWin->round_rect.x0 = pParentWin->round_rect.x0;
				pWin->round_rect.x1 = pParentWin->round_rect.x1;
				pWin->round_rect.y0 = pParentWin->round_rect.y0;
				pWin->round_rect.y1 = pParentWin->round_rect.y1;
				pWin->round_rect.rimColor = pParentWin->round_rect.rimColor;
				pWin->round_rect.round_type = pParentWin->round_rect.round_type;
				pWin->round_rect.radius = pParentWin->round_rect.radius;
			}
			pWin->style   |= (pParentWin->style & WIN_ROUND_RECT);
			pWin->bgColor = pParentWin->bgColor;
		}
		else
		{
			pWin->round_rect.x0 = pWin->rect.x0;
			pWin->round_rect.x1 = pWin->rect.x1;
			pWin->round_rect.y0 = pWin->rect.y0;
			pWin->round_rect.y1 = pWin->rect.y1;
			pWin->round_rect.rimColor = INVALID_COLOR;
			pWin->round_rect.round_type = 0;
			pWin->round_rect.radius = hx330x_min(pWin->round_rect.x1 - pWin->round_rect.x0 + 1,pWin->round_rect.y1 - pWin->round_rect.y0 + 1) / 8;
			pWin->child = pParentWin->child;
			pParentWin->child = hWin;
			//不需要处理parent吗？
			
			
		}
	}
	if((style & WIN_WIDGET) == 0)
		uiWinManage.curWin = hWin;
	uiWinManage.invalidWinSum++;
	uiWinSendMsgId(hWin,MSG_WIN_CREATE);
	uiWinUnlock();
	return hWin;
}
/*******************************************************************************
* Function Name  : uiWinDestroy
* Description    : uiWinDestroy: destroy window or widget
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinDestroy(winHandle* hWin)
{
	uiWinMsg 	msg;
	uiRect 		invalidRect;
	uiWinObj	*pWin;
	winHandle 	hParent;
	if(uiWinManage.destroyWin)
	{
		while(1)
		{
			deg_msg("destroy a window is not allowed when window is being destroyed!!!\n\n");
			deg_msg("do not destroy window in message [SYS_CLOSE_WINDOW]\n");
		}
	}
	uiWinLock();
	if(*hWin == INVALID_HANDLE)
	{
		uiWinUnlock();
		return;
	}
	pWin = (uiWinObj*)uiHandleToPtr(*hWin);
	invalidRect.x0 = pWin->rect.x0;
	invalidRect.x1 = pWin->rect.x1;
	invalidRect.y0 = pWin->rect.y0;
	invalidRect.y1 = pWin->rect.y1;
	hParent = pWin->parent;  
	if(pWin->style & WIN_WIDGET)
	{
		pWin = (uiWinObj*)uiHandleToPtr(pWin->parent);
		while(pWin != NULL)
		{
			if(pWin->next == *hWin)
				break;
			pWin = (uiWinObj*)uiHandleToPtr(pWin->next);
		}
		if(pWin == NULL)
		{
			deg_err("can not find this widget\n");
			uiWinUnlock();
			return;
		}
		pWin->next = ((uiWinObj*)uiHandleToPtr(*hWin))->next;
		uiWinManage.winSum--;
		//deg_msg("destroyed %d\n",uiWinManage.winSum);
		uiWinSendMsgIdToParent(*hWin,MSG_WIN_WIDGET_DESTROY);
		uiWinFreeInvalidRect((uiWinObj*)uiHandleToPtr(*hWin));
		uiWinHeapFree(*hWin);	
	}
	else
	{
		msg.id 		= MSG_WIN_DESTROY;
		msg.para.p 	= (void*)&invalidRect;
		uiWinManage.destroyWin = 1;
		uiWinSendMsg(*hWin,&msg);
		uiWinManage.curWin = hParent;
		uiWinManage.destroyWin = 0;
	
		msg.id 		= MSG_WIN_INVALID;
		msg.para.p	= (void*)&invalidRect;
		uiWinSendMsg(hParent,&msg);
		*hWin = INVALID_HANDLE;
		
		uiWinSendMsgId(hParent,MSG_WIN_CHILE_DESTROY);
		uiWinUnlock();
		return;
	}
	msg.id 		= MSG_WIN_INVALID;
	msg.para.p	= (void*)&invalidRect;
	uiWinSendMsg(hParent,&msg);
	*hWin = INVALID_HANDLE;
	uiWinUnlock();
}
/*******************************************************************************
* Function Name  : uiWinGetTouchInfor
* Description    : uiWinGetTouchInfor:
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void uiWinGetTouchInfor(winHandle hWin,touchInfor *infor)
{
	uiWinMsg msg;
	msg.id     = MSG_WIN_TOUCH_GET_INFOR;
	msg.para.p = infor;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinGetTouchInfor
* Description    : uiWinGetTouchInfor:
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
void uiWinSetTouchInfor(winHandle hWin,touchInfor *infor)
{
	uiWinMsg msg;
	msg.id     = MSG_WIN_TOUCH;
	msg.para.p = infor;
	uiWinSendMsg(hWin,&msg);
}
/*******************************************************************************
* Function Name  : uiWinTouchProcess
* Description    : uiWinTouchProcess: find which window the rectangular area belongs to,and send MSG_WIN_TOUCH
* Input          : none
* Output         : none
* Return         : none
*******************************************************************************/
int uiWinTouchProcess(uiRect* rect,uint32 touchState)
{
	static touchInfor lastInfor={{0,0,0,0},INVALID_HANDLE,INVALID_HANDLE,0,0,TOUCH_NONE};
	winHandle hWin;
	uiWinObj* pWin;
	touchInfor infor;
	if(rect == NULL || uiWinManage.curWin == INVALID_HANDLE)
	{
		if(uiWinManage.curWin != lastInfor.touchWin || lastInfor.touchWin == INVALID_HANDLE || touchState == TOUCH_OVER)
		{
			lastInfor.touchHandle = lastInfor.touchWin = INVALID_HANDLE;
			return 0;
		}
		lastInfor.touchState = TOUCH_RELEASE;
		uiWinSetTouchInfor(lastInfor.touchHandle, &lastInfor);
		lastInfor.touchHandle = lastInfor.touchWin = INVALID_HANDLE;
		return 1;
	}
	pWin=(uiWinObj*)uiHandleToPtr(uiWinManage.curWin);
	if(uiWinInsideCmp(&(pWin->rect),rect) < 0)
	{
		lastInfor.touchHandle = lastInfor.touchWin = INVALID_HANDLE;
		return 0;
	}
	infor.touchArea.x0 = rect->x0;
	infor.touchArea.x1 = rect->x1;
	infor.touchArea.y0 = rect->y0;
	infor.touchArea.y1 = rect->y1;
	infor.touchWin     = INVALID_HANDLE;
	hWin = pWin->next;
	while(hWin!=INVALID_HANDLE)
	{
		pWin=(uiWinObj*)uiHandleToPtr(hWin);
		if(pWin->style & WIN_TOUCH_SUPPORT)
			if(uiWinInsideCmp(&(pWin->rect),rect) >=0 )
				uiWinGetTouchInfor(hWin,&infor);
		hWin = pWin->next;
	}
	if(infor.touchWin == INVALID_HANDLE)
	{
		lastInfor.touchHandle = lastInfor.touchWin = INVALID_HANDLE;
		return 0;
	}
	if(infor.touchWin == lastInfor.touchWin
		&&infor.touchID == lastInfor.touchID
		&&infor.touchItem == lastInfor.touchItem)
	{
		return 1;
	}
	lastInfor.touchState = TOUCH_OVER;
	uiWinSetTouchInfor(lastInfor.touchHandle, &lastInfor);

	infor.touchState=TOUCH_PRESS;
	uiWinSetTouchInfor(infor.touchHandle, &infor);

	lastInfor.touchWin=infor.touchWin;
	lastInfor.touchHandle=infor.touchHandle;
	lastInfor.touchID=infor.touchID;
	lastInfor.touchItem=infor.touchItem;
	lastInfor.touchState=infor.touchState;
	return 1;
}


/*******************************************************************************
* Function Name  : uiWinDrawInvalid
* Description    : uiWinDrawInvalid: draw hwin's all invalid rect
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void uiWinDrawInvalid(winHandle hWin,uiRect* childRect)
{
	uiWinMsg 	msg;
	uiWinObj	*pWin;
	uiRect 		invalidRect;
	uiRect		*rect;
	msg.id = MSG_WIN_PAINT;
	invalidRect.rimColor = INVALID_COLOR;
	while(uiWinManage.invalidWinSum)
	{
		pWin = (uiWinObj*)uiHandleToPtr(hWin);
		if(pWin == NULL)
			break;
		if(pWin->style & WIN_INVALID)
		{
			uiWinManage.invalidWinSum--;
			if(pWin->style & WIN_VISIBLE)
			{
				rect = &(pWin->invalidRect);
				if(childRect)	//if childRect is not null
				{
					while(rect)	//draw all invalid rect of win
					{
						if(uiWinInsideCmp(childRect,rect) < 0)	 //rect is not inside childRect
						{
							if((pWin->style & WIN_WIDGET) == 0 && (uiWinOverlapCmp(rect, childRect) == 0)) //if win is frame and rect is not overlap
							{
								msg.para.p = &invalidRect;
								if(rect->x0 < childRect->x0)	
								{
									invalidRect.x0	= rect->x0;
									invalidRect.x1	= childRect->x0-1;
									invalidRect.y0	= rect->y0;
									invalidRect.y1	= rect->y1;
									uiWinSendMsg(hWin,&msg);
								}
								if(rect->y0 < childRect->y0)
								{
									invalidRect.x0 = hx330x_max(rect->x0,childRect->x0);
									invalidRect.x1 = rect->x1;
									invalidRect.y0 = rect->y0;
									invalidRect.y1 = childRect->y0-1;
									uiWinSendMsg(hWin,&msg);
								}
								if(rect->x1 > childRect->x1)
								{
									invalidRect.x0 = childRect->x1+1;
									invalidRect.x1 = rect->x1;
									invalidRect.y0 = hx330x_max(rect->y0,childRect->y0);
									invalidRect.y1 = rect->y1;
									uiWinSendMsg(hWin,&msg);
								}
								if(rect->y1 > childRect->y1)
								{
									invalidRect.x0 = hx330x_max(rect->x0,childRect->x0);
									invalidRect.x1 = hx330x_min(rect->x1,childRect->x1);
									invalidRect.y0 = childRect->y1+1;
									invalidRect.y1 = rect->y1;
									uiWinSendMsg(hWin,&msg);
								}
							}
							else	//if win is widget or rect is  overlap
							{
								msg.para.p = rect;
								uiWinSendMsg(hWin,&msg);
							}
						}
						rect = rect->next;
					}
				}
				else	//if childRect is null
				{
					while(rect)
					{
						msg.para.p = rect;
						uiWinSendMsg(hWin,&msg);
						rect = rect->next;
					}
				}
			}
			uiWinFreeInvalidRect(pWin);
			pWin->style &= (~WIN_INVALID);
		}
		hWin = pWin->next;
	}
}
/*******************************************************************************
* Function Name  : uiWinDrawProcess
* Description    : uiWinDrawProcess: draw all win
* Input          : none
* Output         : none                                            
* Return         : int : < 0 : no need to draw, 0: updata draw success
*******************************************************************************/
int uiWinDrawProcess(void)
{
	winHandle hWin;
	uiWinObj  *pWin;
	uiWinObj  *pChild;
	if(uiWinManage.invalidWinSum == 0)
		return -1;
	hWin = uiWinManage.deskTopWin;
	uiWinLock();
	while(uiWinManage.invalidWinSum)
	{
		pWin = (uiWinObj*)uiHandleToPtr(hWin);
		if(pWin->child == INVALID_HANDLE)
		{
			uiWinDrawInvalid(hWin,NULL);
			break;
		}
		else
		{

			pChild = (uiWinObj*)uiHandleToPtr(pWin->child);
			if( (pChild->style & WIN_VISIBLE) && pChild->bgColor != INVALID_COLOR)	//if child widget visable and has bgcolor
				uiWinDrawInvalid(hWin,&(pChild->rect));
			else
				uiWinDrawInvalid(hWin,NULL);
		}
		hWin = pWin->child;
	}
	if(uiWinManage.rectMemp.freeBlks != uiWinManage.rectMemp.maxBlks)
	{
		uiWinManage.mempError = 1;
	}
	if(uiWinManage.mempError)
	{
		uiWinErrShow(RAM_ID_MAKE("mempool err"));
		deg_msg("memPool error :\n");
		uiMemPoolInfo(&uiWinManage.rectMemp);
	}
	uiWinManage.invalidWinSum = 0;
	uiWinUnlock();
	return 0;
}
/*******************************************************************************
* Function Name  : uiDeskTopWinCB
* Description    : uiDeskTopWinCB:desktop window's callback function
* Input          : uiWinMsg* msg
* Output         : none                                            
* Return         : none
*******************************************************************************/
static void uiDeskTopWinCB(uiWinMsg* msg)
{
	winHandle curWin = msg->curWin;
	uiWinObj  *pWin = (uiWinObj*)uiHandleToPtr(curWin);
	switch(msg->id)
	{
		case MSG_WIN_CREATE:
			break;
		case MSG_WIN_PAINT:
			//uiWinDrawRect((uiRect*)(msg->parameter.p), pWin->bgColor);
			deg_msg("paint [deskTop win]:[%d %d %d %d]\n",pWin->invalidRect.x0,pWin->invalidRect.y0,pWin->invalidRect.x1,pWin->invalidRect.y1);
			break;
		case MSG_WIN_TOUCH:
			return;
		case MSG_WIN_TOUCH_GET_INFOR:
			return;
		default:
			uiWinDefaultProc(msg);
			break;
	}
}
/*******************************************************************************
* Function Name  : uiWinDestroyDeskTopChildWin
* Description    : uiWinDestroyDeskTopChildWin: 
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinDestroyDeskTopChildWin(void)
{
	uiWinObj* pWin;
	winHandle hWin,next;
	hWin = uiWinManage.deskTopWin;
	pWin = (uiWinObj*)uiHandleToPtr(hWin);
	if(pWin == NULL)
	{
		return;
	}
	uiWinLock();
	if(pWin->child != INVALID_HANDLE)
		uiWinDestroy(&(pWin->child));
	hWin = pWin->next;
	while(hWin != INVALID_HANDLE)
	{
		pWin = (uiWinObj*)uiHandleToPtr(hWin);
		next = pWin->next;
		uiWinDestroy(&hWin);
		hWin = next;
	}
	uiWinUnlock();
}
/*******************************************************************************
* Function Name  : uiWinInit
* Description    : uiWinInit
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinInit(void)
{
	uiWinHeapInit((u32)uiWinManage.winHeap,WIN_HEAP_SIZE);
	res_iconBuffInit();
	uiMemPoolCreate(&uiWinManage.rectMemp, (memPool_T*)uiWinHeapMalloc(sizeof(uiRect)*512), sizeof(uiRect)*512, sizeof(uiRect));
	uiWinManage.winSum			= 0;
	uiWinManage.invalidWinSum	= 0;
	uiWinManage.destroyWin		= 0;
	uiWinManage.mempError		= 0;
	//uiWinManage.deskTopWin 		= INVALID_HANDLE;
	uiWinManage.curWin			= INVALID_HANDLE;
	uiWinManage.errTipRect.x0	= 0;
	uiWinManage.errTipRect.y0	= (USER_UI_HEIGHT-60)/2;
	uiWinManage.errTipRect.x1	= USER_UI_WIDTH;
	uiWinManage.errTipRect.y1	= (USER_UI_HEIGHT-60)/2+60;
	uiWinManage.errString.id	= INVALID_RES_ID;
	uiWinManage.errString.style = DEFAULT_STYLE;
	uiWinManage.errString.strAlign = ALIGNMENT_CENTER;
	uiWinManage.errString.font	= TIPS_FONTNUM;
	uiWinManage.errString.fontColor = TIPS_FONTCOLOR;
	uiWinManage.errString.bgColor = TIPS_BGCOLOR;
	uiWinManage.errString.rimColor = INVALID_COLOR;
	uiWinManage.deskTopWin		= uiWinCreate(0,0,USER_UI_WIDTH,USER_UI_HEIGHT,INVALID_HANDLE,uiDeskTopWinCB,WINOBJ_SIZE,WIN_NOT_ZOOM);
	uiWinSetbgColor(uiWinManage.deskTopWin,DEFAULT_COLOR);

}
/*******************************************************************************
* Function Name  : uiWinUninit
* Description    : uiWinUninit
* Input          : none
* Output         : none                                            
* Return         : none
*******************************************************************************/
void uiWinUninit(void)
{
	if(uiWinManage.deskTopWin != INVALID_HANDLE)
		uiWinDestroy(&uiWinManage.deskTopWin);
	res_iconBuffInit();
	
	uiWinManage.winSum			= 0;
	uiWinManage.invalidWinSum	= 0;
	uiWinManage.destroyWin		= 0;
	uiWinManage.curWin			= INVALID_HANDLE;
	if(uiWinManage.mempError)
		deg_Printf("memPool error information:\n");
	uiMemPoolInfo(&uiWinManage.rectMemp);
}
